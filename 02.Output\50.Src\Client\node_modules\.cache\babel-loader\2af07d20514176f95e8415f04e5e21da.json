{"ast": null, "code": "export default function depsAreSame(oldDeps, deps) {\n  if (oldDeps === deps) return true;\n\n  for (var i = 0; i < oldDeps.length; i++) {\n    if (!Object.is(oldDeps[i], deps[i])) return false;\n  }\n\n  return true;\n}", "map": {"version": 3, "names": ["depsAreSame", "oldDeps", "deps", "i", "length", "Object", "is"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/depsAreSame.js"], "sourcesContent": ["export default function depsAreSame(oldDeps, deps) {\n  if (oldDeps === deps) return true;\n  for (var i = 0; i < oldDeps.length; i++) {\n    if (!Object.is(oldDeps[i], deps[i])) return false;\n  }\n  return true;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAT,CAAqBC,OAArB,EAA8BC,IAA9B,EAAoC;EACjD,IAAID,OAAO,KAAKC,IAAhB,EAAsB,OAAO,IAAP;;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,OAAO,CAACG,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;IACvC,IAAI,CAACE,MAAM,CAACC,EAAP,CAAUL,OAAO,CAACE,CAAD,CAAjB,EAAsBD,IAAI,CAACC,CAAD,CAA1B,CAAL,EAAqC,OAAO,KAAP;EACtC;;EACD,OAAO,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}