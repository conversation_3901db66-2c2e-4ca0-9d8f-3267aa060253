{"ast": null, "code": "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n/** `Object#toString` result references. */\n\n\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n/** Built-in value references. */\n\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\n\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n\n  return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\n\nmodule.exports = baseGetTag;", "map": {"version": 3, "names": ["Symbol", "require", "getRawTag", "objectToString", "nullTag", "undefinedTag", "symToStringTag", "toStringTag", "undefined", "baseGetTag", "value", "Object", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseGetTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAD,CAApB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,cAAD,CADvB;AAAA,IAEIE,cAAc,GAAGF,OAAO,CAAC,mBAAD,CAF5B;AAIA;;;AACA,IAAIG,OAAO,GAAG,eAAd;AAAA,IACIC,YAAY,GAAG,oBADnB;AAGA;;AACA,IAAIC,cAAc,GAAGN,MAAM,GAAGA,MAAM,CAACO,WAAV,GAAwBC,SAAnD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,UAAT,CAAoBC,KAApB,EAA2B;EACzB,IAAIA,KAAK,IAAI,IAAb,EAAmB;IACjB,OAAOA,KAAK,KAAKF,SAAV,GAAsBH,YAAtB,GAAqCD,OAA5C;EACD;;EACD,OAAQE,cAAc,IAAIA,cAAc,IAAIK,MAAM,CAACD,KAAD,CAA3C,GACHR,SAAS,CAACQ,KAAD,CADN,GAEHP,cAAc,CAACO,KAAD,CAFlB;AAGD;;AAEDE,MAAM,CAACC,OAAP,GAAiBJ,UAAjB"}, "metadata": {}, "sourceType": "script"}