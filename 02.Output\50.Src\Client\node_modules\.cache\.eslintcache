[{"D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\index.js": "1", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\reportWebVitals.js": "2", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\App.js": "3", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\pages\\Home.js": "4", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\utils\\Util.js": "5", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\SplitScreen.js": "6", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\sounds\\Sound.js": "7", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Message.tsx": "8", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Deployment.js": "9", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Case.js": "10", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CaseHalf.js": "11", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CustomVehicle.js": "12", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CaseQuarter.js": "13", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Vehicle.js": "14", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\IncomingCallA.js": "15", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Attendance.js": "16", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Alarm.js": "17", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Weather.js": "18", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\TotalFrequency.js": "19", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\DoctorOnDuty.js": "20", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\IncomingCallB.js": "21", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Handover.js": "22", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Schedule.js": "23", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\DigitalRadio.js": "24", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\AmbulanceRate.js": "25", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\NoContent.js": "26", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Now.js": "27", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Title.js": "28", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\ArrayScroll.js": "29", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Cell.js": "30", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\CellBox.js": "31", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\BlinkBlock.js": "32", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\TextScroll.tsx": "33", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\ScaleBlock.js": "34", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\ExtendedVehicle.js": "35"}, {"size": 3771, "mtime": 1732673435405, "results": "36", "hashOfConfig": "37"}, {"size": 362, "mtime": 1717467661631, "results": "38", "hashOfConfig": "37"}, {"size": 1207, "mtime": 1751636323091, "results": "39", "hashOfConfig": "37"}, {"size": 5561, "mtime": 1728462522649, "results": "40", "hashOfConfig": "37"}, {"size": 4755, "mtime": 1717467661435, "results": "41", "hashOfConfig": "37"}, {"size": 6264, "mtime": 1751698735524, "results": "42", "hashOfConfig": "37"}, {"size": 4108, "mtime": 1717467661428, "results": "43", "hashOfConfig": "37"}, {"size": 658, "mtime": 1717467661589, "results": "44", "hashOfConfig": "37"}, {"size": 1811, "mtime": 1717467661630, "results": "45", "hashOfConfig": "37"}, {"size": 5066, "mtime": 1717467661629, "results": "46", "hashOfConfig": "37"}, {"size": 2851, "mtime": 1717467661628, "results": "47", "hashOfConfig": "37"}, {"size": 10200, "mtime": 1737461145833, "results": "48", "hashOfConfig": "37"}, {"size": 2136, "mtime": 1717467661630, "results": "49", "hashOfConfig": "37"}, {"size": 11016, "mtime": 1751636326676, "results": "50", "hashOfConfig": "37"}, {"size": 2660, "mtime": 1717467661587, "results": "51", "hashOfConfig": "37"}, {"size": 1661, "mtime": 1717467661628, "results": "52", "hashOfConfig": "37"}, {"size": 3165, "mtime": 1717467661585, "results": "53", "hashOfConfig": "37"}, {"size": 4122, "mtime": 1730418287139, "results": "54", "hashOfConfig": "37"}, {"size": 3551, "mtime": 1717467661627, "results": "55", "hashOfConfig": "37"}, {"size": 4593, "mtime": 1717467661586, "results": "56", "hashOfConfig": "37"}, {"size": 2748, "mtime": 1717467661626, "results": "57", "hashOfConfig": "37"}, {"size": 1713, "mtime": 1717467661628, "results": "58", "hashOfConfig": "37"}, {"size": 2071, "mtime": 1717467661586, "results": "59", "hashOfConfig": "37"}, {"size": 3893, "mtime": 1717467661585, "results": "60", "hashOfConfig": "37"}, {"size": 1966, "mtime": 1717467661627, "results": "61", "hashOfConfig": "37"}, {"size": 479, "mtime": 1732863949310, "results": "62", "hashOfConfig": "37"}, {"size": 2406, "mtime": 1717467661627, "results": "63", "hashOfConfig": "37"}, {"size": 639, "mtime": 1717467661589, "results": "64", "hashOfConfig": "37"}, {"size": 4660, "mtime": 1717467661588, "results": "65", "hashOfConfig": "37"}, {"size": 1549, "mtime": 1751725952172, "results": "66", "hashOfConfig": "37"}, {"size": 855, "mtime": 1717467661587, "results": "67", "hashOfConfig": "37"}, {"size": 7292, "mtime": 1751725938935, "results": "68", "hashOfConfig": "37"}, {"size": 3110, "mtime": 1717467661589, "results": "69", "hashOfConfig": "37"}, {"size": 1873, "mtime": 1717467661589, "results": "70", "hashOfConfig": "37"}, {"size": 10884, "mtime": 1751725870883, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, "145jfy<PERSON>", {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "85", "usedDeprecatedRules": "75"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "89", "usedDeprecatedRules": "75"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "93", "usedDeprecatedRules": "75"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "97", "usedDeprecatedRules": "75"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "114", "usedDeprecatedRules": "75"}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "121", "usedDeprecatedRules": "75"}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "134", "usedDeprecatedRules": "75"}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "153", "usedDeprecatedRules": "75"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "169", "usedDeprecatedRules": "75"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "101"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "75"}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\index.js", [], [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\reportWebVitals.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\App.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\pages\\Home.js", ["188", "189"], [], "import React, { useState, useEffect } from 'react';\r\nimport SplitScreen from '../components/SplitScreen';\r\nimport Sound from '../sounds/Sound';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\nimport Message from '../components/elements/Message';\r\nimport { useUpdateEffect } from 'ahooks';\r\n\r\n/**\r\n * 単面、或いは4分割の箱を作成する<br>\r\n * WebSocketを経由で、サーバーから、1面または1/4面でコンテンツ表示の制御を受けて、<br>\r\n * ①ブラウザの画面構成を調整する<br>\r\n * ②HtmlのRoot Fontサイズを設定。1面：16PX、1/4面：8px<br>\r\n * @module Home\r\n *\r\n * @return {*} Homeページ\r\n */\r\nfunction Home() {\r\n  const [rowColInfo, setRowColInfo] = useState({ rowNum: 1, colNum: 1 });\r\n\r\n  // http://operationcenter/contentapp?display_no=1&display_split_no=0\r\n  const [search, setSearch] = useSearchParams();\r\n\r\n  let splitNo = search.get('display_split_no');\r\n  let displayNo = search.get('display_no');\r\n\r\n  useEffect(() => {\r\n    document.title = '表示盤番号：' + displayNo + ', 面番号：' + splitNo;\r\n  }, [displayNo, splitNo]);\r\n\r\n  const receiveControl = (message) => {\r\n    console.info('receiveControl: ' + message.body);\r\n    let command = JSON.parse(message.body);\r\n    if (command) {\r\n      if (command.rowNum === 1 && command.colNum === 1) {\r\n        // HtmlのFontSizeを16pxにする\r\n        setRootFontSize(16);\r\n      } else {\r\n        // HtmlのFontSizeを8pxにする\r\n        setRootFontSize(8);\r\n      }\r\n      setRowColInfo(command);\r\n    }\r\n  };\r\n\r\n  const screenInfoPara = {\r\n    displayNo: displayNo,\r\n    splitNo: splitNo,\r\n  };\r\n  const wsEndpoint = getWsEndpoint(displayNo, splitNo);\r\n\r\n  useSubscription(wsEndpoint + '/setControl', receiveControl, screenInfoPara); // Url:  /monitor/0_1/setControl\r\n\r\n  const stompClient = useStompClient();\r\n\r\n  useEffect(() => {\r\n    if (rowColInfo?.id) {\r\n      sendResultMsg(stompClient, rowColInfo.id, 0);\r\n    }\r\n  }, [rowColInfo?.id, stompClient]);\r\n\r\n  if (splitNo == null || displayNo == null) {\r\n    return (\r\n      <Message\r\n        msg={\r\n          '表示盤番号、及び面番号を設定してください。例：http://operationcenter/contentapp/?display_no=0&display_split_no=0'\r\n        }\r\n      />\r\n    );\r\n  }\r\n\r\n  splitNo = Number(splitNo);\r\n  displayNo = Number(displayNo);\r\n\r\n  // モニターの自動画角調整機能対応\r\n  // モニターが描画範囲を検知して自動で画角調整を行うため、描画範囲を示すための目印を四隅に追加する。\r\n  const cornerCommonStyle = \"absolute h-[1px] w-[3px] bg-white\"; //四隅の横並び3pxを白色(#FFFFFF)に塗りつぶす\r\n\r\n  return (\r\n    <div className=\"static\">\r\n      <div className={`${cornerCommonStyle} left-0 top-0`}></div>\r\n      <div className={`${cornerCommonStyle} top-0 right-0`}></div>\r\n      <div className={`${cornerCommonStyle} bottom-0 left-0`}></div>\r\n      <div className={`${cornerCommonStyle} bottom-0 right-0`}></div>\r\n\r\n      {rowColInfo.rowNum === 2 && rowColInfo.colNum === 2 && (\r\n\r\n        <div className=\"bg-black grid min-h-screen grid-rows-2 grid-cols-2 place-content-stretch select-none overflow-hidden\">\r\n\r\n          {/* border-xxのClassで、分割したContent間の線を引く */}\r\n          <div className=\"flex flex-col text-white w-full h-full border-r-[1px] border-b-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={0}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-l-[1px] border-b-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={1}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-r-[1px] border-t-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={2}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-l-[1px] border-t-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={3}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {rowColInfo.rowNum === 1 && rowColInfo.colNum === 1 && (\r\n        <div className=\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-stretch select-none overflow-hidden\">\r\n          <div className=\"flex flex-col text-white w-full h-full p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={0}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {\r\n          <div className=\"bg-black\">\r\n          <div className=\"bg-black text-white\">\r\n            <Sound\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n            />\r\n          </div>\r\n        </div>\r\n      }\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * HtmlRootのFontSize変更\r\n *\r\n * @param {Number} size px単位のFontSizeの数字\r\n */\r\n function setRootFontSize(size) {\r\n  let fontSize = size || 16;\r\n\r\n  document.getElementsByTagName('html')[0].style['font-size'] = fontSize + 'px';\r\n}\r\n\r\nexport default Home;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\utils\\Util.js", ["190"], [], "/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。ClassNameがあれば、一緒につける\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @param {*} className\r\n * @return {*} Face Object\r\n */\r\nexport function getCellFace(obj, className) {\r\n  if (!obj) return;\r\n\r\n  const result = getTextFace(obj);\r\n  if (className) {\r\n    result.className = className;\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @return {*} Face Object\r\n */\r\nexport function getTextFace(obj) {\r\n  if (!obj) return;\r\n  return {\r\n    text_color: getHexColor(obj.text_color),\r\n    background_color: getHexColor(obj.background_color),\r\n    text: obj.display_text,\r\n  };\r\n}\r\n\r\n/**\r\n * propsは有効なSourceなのか\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} true: 有効なSourceデータ。false: 無効なSourceデータ\r\n */\r\nexport function isValidSource(props) {\r\n  if (!props) {\r\n    return false;\r\n  }\r\n\r\n  const entries = Object.entries(props);\r\n  if (entries.length <= 0) {\r\n    return false;\r\n  }\r\n\r\n  let hasValidProp = false;\r\n  for (const [key, value] of entries) {\r\n    if (value) {\r\n      hasValidProp = true;\r\n    }\r\n  }\r\n\r\n  if (!hasValidProp) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\n/**\r\n * Stompを経由で、Browser側で、該当Msgを受信できたことサーバーに返す\r\n *\r\n * @export\r\n * @param {*} stompClient\r\n * @param {*} id\r\n * @param {*} result\r\n */\r\nexport function sendResultMsg(stompClient, id, result) {\r\n  console.log('sendResultMsg start. id=' + id);\r\n  if (stompClient) {\r\n    var resultObj = { id: id, result: result };\r\n\r\n    //Send Message\r\n    stompClient.publish({\r\n      destination: '/app/wsresult',\r\n      body: JSON.stringify(resultObj),\r\n    });\r\n    console.log('sendResultMsg end. id=' + id);\r\n  }\r\n}\r\n\r\n/**\r\n * WebSocketのEndPointを組み立てる\r\n *\r\n * @export\r\n * @param {*} displayNo\r\n * @param {*} splitNo\r\n * @param {*} detailSplitNo\r\n * @return {*} EndPointの文字列\r\n */\r\nexport function getWsEndpoint(displayNo, splitNo, detailSplitNo) {\r\n  let endPoint = '/monitor/' + displayNo + '_' + splitNo;\r\n  if (detailSplitNo != null) {\r\n    endPoint = endPoint + '_' + detailSplitNo;\r\n  }\r\n  return endPoint;\r\n}\r\n\r\n/**\r\n * Colorを#付けて返す\r\n *\r\n * @export\r\n * @param {*} color\r\n * @return {*} #ありの文字列\r\n */\r\nexport function getHexColor(color) {\r\n  if (color == null) {\r\n    return color;\r\n  }\r\n\r\n  if (color.startsWith('#')) {\r\n    return color;\r\n  } else {\r\n    return '#' + color;\r\n  }\r\n}\r\n\r\n/**\r\n * display_colorとtext_color両方から、文字色を返す\r\n * text_colorが優先\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} 文字色\r\n */\r\nexport function getTextOrDisplayColor(props) {\r\n  return getHexColor(props?.text_color || props?.display_color);\r\n}\r\n\r\n/**\r\n * 10月11日12時13分形式の文字列に単位の文字に特別のFontSizeを付ける\r\n *\r\n * @export\r\n * @param {*} text 10月11日12時13分形式の文字列\r\n * @param {*} unitFontSize 単位のFontsize\r\n * @return {*}\r\n */\r\nexport function formateDatetimeText(text, unitFontSize) {\r\n  if (!text || !unitFontSize) return;\r\n\r\n  const regex = /(\\d+)(月|日|時|分)/g;\r\n  let result = [...text.matchAll(regex)];\r\n\r\n  let formattedText = '';\r\n  result.forEach((element) => {\r\n    const val = element[1];\r\n    const unit = element[2];\r\n\r\n    formattedText += `${val}<span style=\"font-size:${unitFontSize}\">${unit}</span>`;\r\n  });\r\n  return formattedText;\r\n}\r\n\r\n/**\r\n * 10月11日12時13分形式の文字列に日付と時間を分割して返す\r\n *\r\n * @export\r\n * @param {*} text\r\n * @return {*} date/timeありのObject\r\n */\r\nexport function splitDateAndTime(text) {\r\n  if (!text) return;\r\n\r\n  const regex = /(\\d+月\\d+日)(\\d+時\\d+分)/g;\r\n  let result = [...text.matchAll(regex)];\r\n\r\n  if (Array.isArray(result) && result.length >= 1) {\r\n    return { date: result[0][1], time: result[0][2] };\r\n  }\r\n  return undefined;\r\n}\r\n\r\n//Htmlを含むかを返す\r\nfunction hasHtmlTag(text) {\r\n  var reg = /<[^>]+>/gi;\r\n  return reg.test(text);\r\n}\r\n\r\n/**\r\n * Textの通常SpaceをHtmlのSpaceを入れ替える\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*}\r\n */\r\nexport function replaceWithHtmlSpace(text) {\r\n  let refinedText = text;\r\n  if (refinedText && !hasHtmlTag(refinedText)) {\r\n    refinedText = refinedText.replace(/ /g, '&nbsp;');\r\n    refinedText = refinedText.replace(/　/g, '&nbsp;&nbsp;'); //全角のSpace\r\n  }\r\n  return refinedText;\r\n}\r\n\r\n/**\r\n * 数字に,を入れて分割。12345678 -> '12,345,678'\r\n *\r\n * @export\r\n * @param {*} num\r\n * @return {*} 文字列\r\n */\r\nexport function toThousands(num) {\r\n  return (num || 0).toString().replace(/(\\d)(?=(?:\\d{3})+$)/g, '$1,');\r\n}\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\SplitScreen.js", ["191"], [], "import React, { useState, useEffect } from 'react';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport PropTypes from 'prop-types';\r\nimport Vehicle from './Vehicle';\r\nimport CustomVehicle from './CustomVehicle';\r\nimport Deployment from './Deployment';\r\nimport Case from './Case';\r\nimport CaseHalf from './CaseHalf';\r\nimport CaseQuarter from './CaseQuarter';\r\nimport IncomingCallA from './IncomingCallA';\r\nimport IncomingCallB from './IncomingCallB';\r\nimport Weather from './Weather';\r\nimport TotalFrequency from './TotalFrequency';\r\nimport Alarm from './Alarm';\r\nimport Attendance from './Attendance';\r\nimport DoctorOnDuty from './DoctorOnDuty';\r\nimport Schedule from './Schedule';\r\nimport HandOver from './Handover';\r\nimport DigitalRadio from './DigitalRadio';\r\nimport AmbulanceRate from './AmbulanceRate';\r\nimport ExtendedVehicle from './ExtendedVehicle';\r\nimport Now from './Now';\r\nimport NoContent from './NoContent';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\n\r\nconst propTypes = {\r\n  displayNo: PropTypes.number,\r\n  splitNo: PropTypes.number,\r\n  detailSplitNo: PropTypes.number,\r\n};\r\n\r\n/**\r\n * 実際の表示Contentの箱。SourceNoによって、それぞれのContentを表示\r\n * Source No仕様:<br>\r\n    1 車両コンテンツ情報更新<br>\r\n    2 配備状況コンテンツ情報更新<br>\r\n    3 事案コンテンツ情報更新<br>\r\n    4 簡易事案コンテンツ(1-2サイズ)情報更新<br>\r\n    5 簡易事案コンテンツ(1-4サイズ)情報更新<br>\r\n    6 時刻コンテンツ<br>\r\n    7 着信状況コンテンツA情報更新<br>\r\n    8 着信状況コンテンツB情報更新<br>\r\n    9 気象コンテンツ情報更新<br>\r\n    10 総合度数コンテンツ情報更新<br>\r\n    11 予警報コンテンツ情報更新<br>\r\n    12 出退コンテンツ情報更新<br>\r\n    13 当番医コンテンツ情報更新<br>\r\n    14 予定コンテンツ情報更新<br>\r\n    15 引継事項コンテンツ情報更新<br>\r\n    16 デジタル無線コンテンツ情報更新<br>\r\n    17 救急車稼働率コンテンツ情報更新<br>\r\n    18 拡張車両コンテンツ情報更新（50行表示）<br>\r\n    \r\n * @module SplitScreen\r\n * @component \r\n * @param {*} props\r\n * @return {*} SplitScreen\r\n */\r\nconst SplitScreen = (props) => {\r\n  /**\r\n * Server側のIF仕様\r\n * public class ContentInfo {\r\n      private Integer sourceNo;\r\n      private Integer sourceSplitNo;\r\n      private Integer detailSplitNo; // 面分割番号\r\n      private Object content; //実際のContent内容\r\n    }\r\n */\r\n  const [contentInfo, setContentInfo] = useState();\r\n\r\n  const receiveContent = (message) => {\r\n    console.info('receiveContent: ' + message.body);\r\n\r\n    let command = JSON.parse(message.body);\r\n    const isString = (val) => typeof val === 'string';\r\n    if (command.sourceData) {\r\n      if (isString(command.sourceData)) {\r\n        command.sourceData = JSON.parse(command.sourceData);\r\n      }\r\n    }\r\n\r\n    if (command) {\r\n      switch (command.detailSplitNo) {\r\n        case 1:\r\n        case 3:\r\n        case 9:\r\n        case 11:\r\n          if (props.detailSplitNo === 1) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 4:\r\n        case 6:\r\n        case 12:\r\n        case 14:\r\n          if (props.detailSplitNo === 2) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 5:\r\n        case 7:\r\n        case 13:\r\n        case 15:\r\n          if (props.detailSplitNo === 3) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 0:\r\n        case 2:\r\n        case 8:\r\n        case 10:\r\n          if (props.detailSplitNo === 0) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const wsEndpoint = getWsEndpoint(\r\n    props.displayNo,\r\n    props.splitNo,\r\n    props.detailSplitNo\r\n  );\r\n\r\n  useSubscription(wsEndpoint + '/setContent', receiveContent); // Url:  /monitor/0_1_2/setContent\r\n\r\n  const stompClient = useStompClient();\r\n  useEffect(() => {\r\n    if (contentInfo?.id) {\r\n      sendResultMsg(stompClient, contentInfo.id, 0);\r\n    }\r\n  }, [contentInfo?.id, stompClient]);\r\n\r\n  if (contentInfo && contentInfo?.id) {\r\n    console.log(`TaskID: ${contentInfo?.id}`);\r\n  }\r\n\r\n  if (contentInfo && contentInfo.sourceNo >= 0) {\r\n    const sourceData = contentInfo.sourceData;\r\n    sourceData.barTitle = contentInfo.sourceName;\r\n    sourceData.sourceDispPattern = contentInfo.sourceDispPattern;\r\n    switch (contentInfo.sourceNo) {\r\n      case 1:\r\n        return   sourceData.sourceDispPattern == 1 ?  <CustomVehicle {...sourceData} /> : <Vehicle {...sourceData} />;\r\n      case 2:\r\n        return <Deployment {...sourceData} />;\r\n      case 3:\r\n        return <Case {...sourceData} />;\r\n      case 4:\r\n        return <CaseHalf {...sourceData} />;\r\n      case 5:\r\n        return <CaseQuarter {...sourceData} />;\r\n      case 6:\r\n        return <Now {...sourceData} />;\r\n      case 7:\r\n        return <IncomingCallA {...sourceData} />;\r\n      case 8:\r\n        return <IncomingCallB {...sourceData} />;\r\n      case 9:\r\n        return <Weather {...sourceData} />;\r\n      case 10:\r\n        return <TotalFrequency {...sourceData} />;\r\n      case 11:\r\n        return <Alarm {...sourceData} />;\r\n      case 12:\r\n        return <Attendance {...sourceData} />;\r\n      case 13:\r\n        return <DoctorOnDuty {...sourceData} />;\r\n      case 14:\r\n        return <Schedule {...sourceData} />;\r\n      case 15:\r\n        return <HandOver {...sourceData} />;\r\n      case 16:\r\n        return <DigitalRadio {...sourceData} />;\r\n      case 17:\r\n        return <AmbulanceRate {...sourceData} />;\r\n      case 18:\r\n        return <ExtendedVehicle {...sourceData} />;\r\n      default:\r\n        return <NoContent />;\r\n    }\r\n  }\r\n\r\n  return <NoContent />;\r\n};\r\n\r\nSplitScreen.propTypes = propTypes;\r\nexport default SplitScreen;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\sounds\\Sound.js", ["192"], [], "import React, { useState, useEffect } from 'react';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport PropTypes from 'prop-types';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\n\r\nconst propTypes = {\r\n  displayNo: PropTypes.number,\r\n  splitNo: PropTypes.number,\r\n};\r\n\r\nconst fileUrl = process.env.PUBLIC_URL; \r\nconst filePath = \"/sounds\";\r\nconst fileName = \"/sound\";\r\nconst fileType = \".mp3\";\r\nconst sound = new Audio();\r\nlet playcount;\r\nlet repeatmax;\r\nlet result;\r\n\r\n// 再生終了イベント\r\nsound.addEventListener(\"ended\", function () {\r\n  console.log( 'soundPlayEnd: ended');\r\n}, false);\r\n\r\n/**\r\n * @module Sound\r\n * @component \r\n * @param {*} props\r\n * @return {*} Sound\r\n */\r\nconst Sound = (props) => {\r\n /**\r\n * public class SoundInfo {\r\n *     private Integer soundNo; //再生ファイル番号\r\n *     private Integer repeatCount; //リピート回数\r\n *     private Object sourceData; //実際のContent内容\r\n *     private String id; //該当コンテンツのID\r\n * }\r\n */\r\n  const [soundInfo, setSoundInfo] = useState();\r\n\r\n  const receiveSound = (message) => {\r\n    console.info('receiveSound: ' + message.body);\r\n\r\n    let command = JSON.parse(message.body);\r\n    const isString = (val) => typeof val === 'string';\r\n    if (command.sourceData) {\r\n      if (isString(command.sourceData)) {\r\n        command.sourceData = JSON.parse(command.sourceData);\r\n      }\r\n    }\r\n\r\n    if (command) {\r\n      // 実行結果をクリア\r\n      result = 0;\r\n\r\n      // 再生停止、ループ設定クリア\r\n      sound.pause();\r\n      sound.currentTime = 0;\r\n      sound.loop = false;\r\n\r\n      // ファイル番号設定\r\n      //console.log( 'command.sound_no = ' + command.soundNo);\r\n      //console.log( 'command.repeat_count = ' + command.repeatCount);\r\n      let no = command.soundNo;\r\n      let AudPath = `${filePath}${fileName}${no}${fileType}`\r\n      \r\n      // リピート設定\r\n      playcount = 0;\r\n      repeatmax = command.repeatCount;\r\n      if( repeatmax > 0 ){\r\n        sound.loop = true;\r\n      } else {\r\n        sound.loop = false;\r\n      }\r\n\r\n      console.info('receiveSound: Repeat='+ repeatmax + ', '+ AudPath);\r\n\r\n      // リソース設定して再生\r\n      sound.src = (fileUrl + AudPath);\r\n      sound.play();\r\n\r\n/* 必ず正常で応答する場合はここを有効化してイベント内の更新を無効化する\r\n      // 更新\r\n      setSoundInfo((split) => ({\r\n        ...split,\r\n        ...command,\r\n      }));\r\n*/\r\n\r\n      // errorイベント\r\n      //  - エラーによりリソースの読み込みができなかった場合に発行される\r\n      sound.onerror = function() {\r\n        console.log( 'sound play error: ' + sound.error.message);\r\n        sound.loop = false;\r\n        // 更新\r\n        result = -1;\r\n        setSoundInfo((split) => ({\r\n          ...split,\r\n          ...command,\r\n        }));\r\n      }\r\n\r\n      // playingイベント\r\n      //  - 再生が開始できる状態になったときに発行される\r\n      sound.onplaying = function() {\r\n        playcount++;\r\n        console.log( 'sound playing count(' + playcount +')' );\r\n\r\n        // リピートの停止処理\r\n        if( playcount <= repeatmax ){\r\n          console.log( 'sound repeat');\r\n        } else {\r\n          sound.loop = false;\r\n          console.log( 'sound repeat end ' );\r\n        }\r\n\r\n        // 更新\r\n        result = 0;\r\n        setSoundInfo((split) => ({\r\n          ...split,\r\n          ...command,\r\n        }));\r\n      }\r\n\r\n    } else {\r\n      console.info('receiveSound: command none');\r\n    }\r\n\r\n    //console.log( 'receiveSound end.');\r\n  };\r\n\r\n  const wsEndpoint = getWsEndpoint(\r\n    props.displayNo,\r\n    props.splitNo,\r\n  );\r\n\r\n  useSubscription(wsEndpoint + '/setSound', receiveSound); // Url:  /monitor/0_0/setSound\r\n\r\n  const stompClient = useStompClient();\r\n\r\n  useEffect(() => {\r\n    if (soundInfo?.id) {\r\n      sendResultMsg(stompClient, soundInfo.id, result);\r\n    }\r\n  }, [soundInfo?.id, stompClient]);\r\n\r\n  return;\r\n};\r\n\r\nSound.propTypes = propTypes;\r\nexport default Sound;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Message.tsx", [], [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Deployment.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Case.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CaseHalf.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CustomVehicle.js", ["193", "194", "195", "196", "197", "198", "199", "200", "201", "202"], [], "import React from 'react';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport BlinkBlock, { checkBlinkInfo } from './elements/BlinkBlock';\r\n\r\n\r\n/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */\r\nconst CustomVehicle = (props) => {\r\n\r\n    if (!isValidSource(props))\r\n        return;\r\n\r\n    if (!props.items)\r\n        return;\r\n\r\n    const showDelopy = props.is_deployment === 1;\r\n    const columnPosition = props.column_position;\r\n    const gridClass = getGridClass(props);\r\n\r\n    return (\r\n        <>\r\n            {isValidSource(props) && (\r\n                <div className={`grid ${gridClass}`}>\r\n                    {props.items.map((item, index) => {\r\n                        return (\r\n                            <Station\r\n                                {...item}\r\n                                showDelopy={showDelopy}\r\n                                columnPosition={columnPosition}\r\n                            />\r\n                        );\r\n                    })}\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * ソース表示パターンによってグリッドのクラス定義を取得する\r\n * @param {*} props\r\n * @returns クラス定義\r\n */\r\nconst getGridClass = (props) => {\r\n\r\n    if (!(props.sourceDispPattern === 1))\r\n        return 'grid-cols-2 grid-rows-16 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';\r\n\r\n    if (props.column_position == 'left')\r\n        return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pr-[16px]';\r\n\r\n    if (props.column_position == 'right')\r\n        return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pl-[16px]';\r\n\r\n    return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';\r\n};\r\n\r\n/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */\r\nconst Station = (entity) => {\r\n    let gridCol;\r\n    // gridCol = getGridRowClass(entity);\r\n    let subTitleSpan = 'col-span-full';\r\n    if (entity.showDelopy) {\r\n        gridCol = 'grid-cols-quarter-vehicle-deploy';\r\n    } else {\r\n        gridCol = 'grid-cols-quarter-vehicle-nodeploy';\r\n    }\r\n    const subTitleProp = getCellFace(\r\n        entity.title,\r\n        `${subTitleSpan} flex flex-col items-center`\r\n    );\r\n\r\n    return (\r\n        <>\r\n            {entity.title && (\r\n                < div className={`grid ${gridCol}`}>\r\n                    <CellBox {...subTitleProp}>\r\n                        <span>{entity.title.display_text}</span>\r\n                    </CellBox>\r\n                </div >\r\n            )}\r\n            {!entity.title && (\r\n                <VehicleDetailRow  {...entity} />\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */\r\nconst VehicleDetailRow = (entity) => {\r\n\r\n    let showInfoDeployment;\r\n    let showInfoCarName;\r\n    let showInfoTownName;\r\n    let showInfoDisasterType;\r\n    let showInfoAvmDynamicState;\r\n\r\n    if (entity.showDelopy && entity.deployment)\r\n        showInfoDeployment = entity.deployment;\r\n\r\n    if (entity.car_name)\r\n        showInfoCarName = entity.car_name;\r\n\r\n    if (entity.town_name)\r\n        showInfoTownName = entity.town_name;\r\n\r\n    if (entity.disaster_type)\r\n        showInfoDisasterType = entity.disaster_type;\r\n\r\n    if (entity.avm_dynamic_state)\r\n        showInfoAvmDynamicState = entity.avm_dynamic_state;\r\n\r\n    let status = entity.lighting_setting ? entity.lighting_setting.lighting_status : 1;\r\n\r\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\r\n    let baseObj = [entity.car_name, entity.town_name, entity.disaster_type, entity.avm_dynamic_state, entity.deployment].find(item => item);\r\n\r\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\r\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\r\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\r\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\r\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\r\n\r\n    let showInfoSeperator0 = { ...showInfoDeployment };\r\n    let showInfoSeperator1 = { ...showInfoDeployment };\r\n    let showInfoSeperator2 = { ...showInfoCarName };\r\n    let showInfoSeperator3 = { ...showInfoTownName };\r\n    let showInfoSeperator4 = { ...showInfoDisasterType };\r\n    let showInfoSeperator5 = { ...showInfoAvmDynamicState };\r\n\r\n    showInfoSeperator0.display_text = ' ';\r\n    showInfoSeperator1.display_text = ' ';\r\n    showInfoSeperator2.display_text = ' ';\r\n    showInfoSeperator3.display_text = ' ';\r\n    showInfoSeperator4.display_text = ' ';\r\n    showInfoSeperator5.display_text = ' ';\r\n\r\n    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする\r\n    if (status !== 3) {\r\n        showInfoSeperator0.background_color = undefined;\r\n        showInfoSeperator1.background_color = undefined;\r\n        showInfoSeperator2.background_color = undefined;\r\n        showInfoSeperator3.background_color = undefined;\r\n        showInfoSeperator4.background_color = undefined;\r\n        showInfoSeperator5.background_color = undefined;\r\n    }\r\n\r\n    let gridCol;\r\n    //gridCol = getGridRowClass(entity);\r\n    let showBlock = [];\r\n    if (entity.showDelopy) {\r\n        gridCol = 'grid-cols-quarter-vehicle-deploy';\r\n\r\n        if (entity.columnPosition == 'left') {\r\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n        }\r\n\r\n        showBlock.push({\r\n            showInfo: showInfoDeployment,\r\n            className: 'col-span-1 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-4',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-9',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-16',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-19',\r\n        });\r\n\r\n        if (entity.columnPosition == 'right') {\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n\r\n    } else {\r\n        gridCol = 'grid-cols-quarter-vehicle-nodeploy';\r\n\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-7',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-14',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-17',\r\n        });\r\n        if (entity.columnPosition == 'right') {\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\r\n            {entity.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={entity.lighting_setting}\r\n                    />\r\n                </div>\r\n            )}\r\n            {!entity.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={entity.lighting_setting}\r\n                    />\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * ソース表示パターンによってグリッド１行のクラス定義を取得する\r\n * @param {*} entity\r\n * @returns クラス定義\r\n */\r\nconst getGridRowClass = (entity) => {\r\n\r\n    if (entity.showDelopy) {\r\n\r\n        if (entity.columnPosition == 'left')\r\n            return '14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n\r\n        if (entity.columnPosition == 'right')\r\n            return '14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n    } else {\r\n\r\n        if (entity.columnPosition == 'left')\r\n            return '14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n\r\n        if (entity.columnPosition == 'right')\r\n            return '14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n    }\r\n\r\n    return '16px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n};\r\n\r\n\r\n\r\nexport default CustomVehicle;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\CaseQuarter.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Vehicle.js", ["203", "204"], [], "import React from 'react';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\r\n\r\nconst MAX_ROW = 32;\r\nconst GRID_COLS_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\r\nconst GRID_COLS_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\r\n\r\n/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */\r\nconst Vehicle = (props) => {\r\n    if (!isValidSource(props)) return;\r\n\r\n    let showDelopy = props.is_deployment === 1;\r\n\r\n    let totalRowCounter = 0;\r\n\r\n    let targetArray = [];\r\n\r\n    if (!props.title_name)\r\n        return;\r\n    \r\n    //最大MaxRowのデータを取り出す\r\n    for (const item of props.title_name) {\r\n        \r\n        if (!item.car_name) \r\n            return;\r\n\r\n        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\r\n        let subRowCounter = item.car_name.length + 1;\r\n\r\n        if (totalRowCounter + subRowCounter <= MAX_ROW) {\r\n            targetArray.push(item);\r\n            totalRowCounter = totalRowCounter + subRowCounter;\r\n        } else if (totalRowCounter <= MAX_ROW - 2) {\r\n            subRowCounter = MAX_ROW - totalRowCounter;\r\n\r\n            // この - 1がSubTitleの行\r\n            const subItemCounter = subRowCounter - 1;\r\n\r\n            let newObj = { ...item };\r\n            newObj.deployment = newObj.deployment?.slice(0, subItemCounter);\r\n            newObj.car_name = newObj.car_name?.slice(0, subItemCounter);\r\n            newObj.town_name = newObj.town_name?.slice(0, subItemCounter);\r\n            newObj.disaster_type = newObj.disaster_type?.slice(0, subItemCounter);\r\n            newObj.avm_dynamic_state = newObj.avm_dynamic_state?.slice(\r\n                0,\r\n                subItemCounter\r\n            );\r\n            newObj.lighting_setting = newObj.lighting_setting?.slice(\r\n                0,\r\n                subItemCounter\r\n            );\r\n\r\n            targetArray.push(newObj);\r\n            totalRowCounter = totalRowCounter + subRowCounter;\r\n        }\r\n    }\r\n\r\n    let nextStartRow = 1;\r\n    return (\r\n        <>\r\n            {isValidSource(props) && (\r\n                <div className=\"grid grid-cols-2 grid-rows-16 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]\">\r\n                    {targetArray.map((item, index) => {\r\n                        let startRow = nextStartRow; //現在のStart\r\n                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算\r\n\r\n                        return (\r\n                            <Station\r\n                                key={index}\r\n                                {...item}\r\n                                index={index}\r\n                                showDelopy={showDelopy}\r\n                                startRow={startRow}\r\n                            />\r\n                        );\r\n                    })}\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} props\r\n * @returns 表示データ\r\n */\r\nconst Station = (props) => {\r\n    let gridCol;\r\n    let subTitleSpan = 'col-span-full';\r\n    if (props.showDelopy) {\r\n        gridCol = 'grid-cols-vehicle-deploy';\r\n        //gridCol = GRID_COLS_VEHICLE_DEPLOY;\r\n    } else {\r\n        gridCol = 'grid-cols-vehicle-nodeploy';\r\n        //gridCol = GRID_COLS_VEHICLE_NODEPLOY;\r\n    }\r\n    const subTitleProp = getCellFace(\r\n        props,\r\n        `${subTitleSpan} flex flex-col items-center`\r\n    );\r\n\r\n    return (\r\n        <>\r\n            <div className={`grid ${gridCol}`}>\r\n                <CellBox {...subTitleProp}>\r\n                    {props.display_text && <span>{props.display_text}</span>}\r\n                </CellBox>\r\n            </div>\r\n            {props.car_name.map((item, index) => {\r\n                return <VehicleDetailRow key={index} {...props} index={index} />;\r\n            })}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} props\r\n * @returns 表示データ\r\n */\r\nconst VehicleDetailRow = (props) => {\r\n    let showInfoDeployment,\r\n        showInfoCarName,\r\n        showInfoTownName,\r\n        showInfoDisasterType,\r\n        showInfoAvmDynamicState;\r\n    if (props.showDelopy && props.deployment && props.lighting_setting) {\r\n        showInfoDeployment = props.deployment[props.index];\r\n    }\r\n    if (props.car_name && props.lighting_setting) {\r\n        showInfoCarName = props.car_name[props.index];\r\n    }\r\n    if (props.town_name && props.lighting_setting) {\r\n        showInfoTownName = props.town_name[props.index];\r\n    }\r\n    if (props.disaster_type && props.lighting_setting) {\r\n        showInfoDisasterType = props.disaster_type[props.index];\r\n    }\r\n    if (props.avm_dynamic_state && props.lighting_setting) {\r\n        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\r\n    }\r\n\r\n    let status = 1;\r\n    if (props.lighting_setting && props.lighting_setting[props.index]) {\r\n        status = props.lighting_setting[props.index].lighting_status;\r\n    }\r\n\r\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\r\n    let baseObj = props.car_name[props.index];\r\n    if (!baseObj) { baseObj = props.town_name[props.index] }\r\n    if (!baseObj) { baseObj = props.disaster_type[props.index] }\r\n    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }\r\n    if (!baseObj) { baseObj = props.deployment[props.index] }\r\n\r\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\r\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\r\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\r\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\r\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\r\n\r\n    let showInfoSeperator0 = { ...showInfoDeployment };\r\n    let showInfoSeperator1 = { ...showInfoDeployment };\r\n    let showInfoSeperator2 = { ...showInfoCarName };\r\n    let showInfoSeperator3 = { ...showInfoTownName };\r\n    let showInfoSeperator4 = { ...showInfoDisasterType };\r\n    let showInfoSeperator5 = { ...showInfoAvmDynamicState };\r\n\r\n    showInfoSeperator0.display_text = ' ';\r\n    showInfoSeperator1.display_text = ' ';\r\n    showInfoSeperator2.display_text = ' ';\r\n    showInfoSeperator3.display_text = ' ';\r\n    showInfoSeperator4.display_text = ' ';\r\n    showInfoSeperator5.display_text = ' ';\r\n\r\n    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする\r\n    if (status !== 3) {\r\n        showInfoSeperator0.background_color = undefined;\r\n        showInfoSeperator1.background_color = undefined;\r\n        showInfoSeperator2.background_color = undefined;\r\n        showInfoSeperator3.background_color = undefined;\r\n        showInfoSeperator4.background_color = undefined;\r\n        showInfoSeperator5.background_color = undefined;\r\n    }\r\n\r\n    let gridCol;\r\n    let showBlock = [];\r\n    if (props.showDelopy) {\r\n        gridCol = 'grid-cols-vehicle-deploy';\r\n        //gridCol = GRID_COLS_VEHICLE_DEPLOY;\r\n\r\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\r\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n        }\r\n        showBlock.push({\r\n            showInfo: showInfoDeployment,\r\n            className: 'col-span-1 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-4',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-9',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-16',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-19',\r\n        });\r\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n    } else {\r\n        gridCol = 'grid-cols-vehicle-nodeploy';\r\n        //gridCol = GRID_COLS_VEHICLE_NODEPLOY;\r\n\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-7',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-14',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-17',\r\n        });\r\n\r\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\r\n            {props.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={props.lighting_setting[props.index]}\r\n                    />\r\n                </div>\r\n            )}\r\n            {!props.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={props.lighting_setting[props.index]}\r\n                    />\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n\r\n\r\nexport default Vehicle;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\IncomingCallA.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Attendance.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Alarm.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Weather.js", ["205", "206"], [], "import React, { Component } from 'react';\r\nimport Cell from './elements/Cell.js';\r\nimport { getCellFace, isValidSource, toThousands } from '../utils/Util.js';\r\nimport Title from './elements/Title.js';\r\nimport CellBox from './elements/CellBox.js';\r\nimport './Weather.css';\r\n\r\n/**\r\n * 気象状況コンテンツ<br>\r\n * propsは、「3.10気象コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Weather\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Weather = (props) => {\r\n  props.barTitle = props.barTitle == null ? '気象状況' : props.barTitle;\r\n  console.log(`props.barTitle: ${props.barTitle}`);\r\n  return (\r\n    <>\r\n      <Title title={props.barTitle}/>\r\n      {isValidSource(props) && (\r\n        <div className=\"flex flex-row\">\r\n          <div className=\"basis-[54.25%] grid grid-cols-1\">\r\n\t\t\t<div className=\"p-[3.6rem] mt-[.7rem]\">\r\n\t\t\t  <WindDirection {...props.wind_direction} />\r\n\t\t\t</div>\r\n\t\t  </div>\r\n          <div className=\"basis-[45.75%] ml-[2rem]\">\r\n            <div className=\"grid grid-cols-[repeat(4,5.1rem)_3.1rem_repeat(3,5.5rem)_3.1rem_repeat(2,5rem)] grid-rows-8 gap-y-[2.3rem] mt-[3.8rem] mb-[3rem] text-5xl leading-[1] mr-[3rem]\">\r\n              <WeatherRow weather=\"最大風速\" detail={props.wind_speed_max} />\r\n              <WeatherRow weather=\"平均風速\" detail={props.wind_speed} />\r\n              <WeatherRow weather=\"気温\" detail={props.temperature} />\r\n              <WeatherRow weather=\"雨量\" detail={props.rainfall} />\r\n              <WeatherRow\r\n                weather=\"実効湿度\"\r\n                detail={props.effective_humidity}\r\n              />\r\n              <WeatherRow weather=\"相対湿度\" detail={props.relative_humidity} />\r\n              <WeatherRow weather=\"気圧\" detail={props.atmospheric_pressure} />\r\n              {/* <WeatherRow weather='観測時刻' detail={props.observation_time} /> */}\r\n              <WeatherRow weather=\"天候\" detail={props.weather} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\n// 気象状況コンテンツの風向\r\nconst WindDirection = (props) => {\r\n  const imageMap = {\r\n    北: 'North.png',\r\n    北_北東: 'North_Northeast.png',\r\n    北東: 'Northeast.png',\r\n    北東_東: 'Northeast_East.png',\r\n    東: 'East.png',\r\n    東_南東: 'East_Southeast.png',\r\n    南東: 'Southeast.png',\r\n    南東_南: 'Southeast_South.png',\r\n    南: 'South.png',\r\n    南_南西: 'South_Southwest.png',\r\n    南西: 'Southwest.png',\r\n    南西_西: 'Southwest_West.png',\r\n    西: 'West.png',\r\n    西_北西: 'West_Northwest.png',\r\n    北西: 'Northwest.png',\r\n    北西_北: 'Northwest_North.png',\r\n  };\r\n\r\n  if (!props || !props.display_text) {\r\n    return null;\r\n  }\r\n\r\n  // 風向の表示色なし(画像のまま)。背景色は黒のまま\r\n  let imgPath = `/images/${imageMap[props.display_text.trim()]}`;\r\n\r\n  return (\r\n    <img className=\"min-w-full\"\r\n      src={process.env.PUBLIC_URL + imgPath}\r\n      alt={props.display_text}\r\n    />\r\n  );\r\n};\r\n\r\n// 気象状況コンテンツの一行天気\r\nconst WeatherRow = (props) => {\r\n  const unitMap = {\r\n    平均風速: 'm/s',\r\n    風向: '',\r\n    最大風速: 'm/s',\r\n    気温: '&#8451;',\r\n    雨量: 'mm',\r\n    実効湿度: '%',\r\n    相対湿度: '%',\r\n    気圧: 'hPa',\r\n    観測時刻: '',\r\n    天候: '',\r\n  };\r\n  const cell1Props = {\r\n    text_color: '#080808',\r\n    background_color: '#fff',\r\n    text: props.weather,\r\n    className: 'col-start-1 col-span-4 justify',\r\n  };\r\n  const cell2Props = getCellFace(\r\n    props.detail,\r\n    'col-span-3 col-start-6 place-self-end w-fit'\r\n  );\r\n\r\n  const cell3Props = {\r\n    text_color: '#fff',\r\n    background_color: '#000',\r\n    className: 'col-start-10 col-span-2',\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Cell {...cell1Props} />\r\n      <Cell {...cell2Props} />\r\n      {props.detail?.display_text && (\r\n        <CellBox {...cell3Props}>\r\n          <span dangerouslySetInnerHTML={{ __html: unitMap[props.weather] }} />\r\n        </CellBox>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Weather;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\TotalFrequency.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\DoctorOnDuty.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\IncomingCallB.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Handover.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Schedule.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\DigitalRadio.js", ["207", "208"], [], "import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport Title from './elements/Title';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport BlinkBlock from './elements/BlinkBlock';\r\n\r\n/**\r\n * デジタル無線コンテンツ<br>\r\n * propsは、「3.17デジタル無線コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module DigitalRadio\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst DigitalRadio = (props) => {\r\n  const MAX_ROW = 4;\r\n  return (\r\n    <>\r\n      <Title title={'デジタル無線通信状況'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"grid min-h-full grid-rows-2 grid-cols-2 place-content-stretch text-5xl leading-[1]\">\r\n          {props.wireless_channel_name.map((item, index) => {\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <DigitalRadioSubPart key={index} {...item} index={index} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nconst DigitalRadioSubPart = (props) => {\r\n  const MAX_ROW = 2;\r\n\r\n  let borderStyle = '';\r\n  if (props.index % 2 === 1) {\r\n    borderStyle = 'border-l-2';\r\n  }\r\n  \r\n  let appendGridClassName;\r\n\r\n  if (props.index % 2 == 0) {\r\n    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)]'\r\n  } else {\r\n    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)_1rem]'\r\n  }\r\n  let row1Cell1Props = getCellFace(\r\n      props,\r\n      `col-span-full justify-self-center text-[4.2rem] w-fit`\r\n  );\r\n  return (\r\n    <div\r\n      className={`grid content-start ${appendGridClassName} ${borderStyle} gap-y-10 pt-2`}\r\n    >\r\n      <Cell {...row1Cell1Props} />\r\n\r\n      {props.outgoing_call_move_station_name &&\r\n        props.outgoing_call_move_station_name.map((item, index) => {\r\n          if (index >= MAX_ROW) return undefined;\r\n\r\n          return <RadioDetailRow key={index} {...props} index={index} rightPadding={props.index % 2 == 1}/>;\r\n        })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// デジタル無線コンテンツ\r\nconst RadioDetailRow = (props) => {\r\n  let showInfo1 = props.outgoing_call_move_station_name[props.index];\r\n  let showInfoSeperator1 = { ...showInfo1 };\r\n  showInfoSeperator1.display_text = ' ';\r\n  let showInfoSeperator0 = { ...showInfoSeperator1 };\r\n\r\n  let showInfo2 = {};\r\n  let showInfoSeperator2 = {};\r\n  if (\r\n    props.incoming_call_move_station_name &&\r\n    props.incoming_call_move_station_name[props.index]\r\n  ) {\r\n    showInfo2 = props.incoming_call_move_station_name[props.index];\r\n    showInfoSeperator2 = { ...showInfo2 };\r\n    showInfoSeperator2.display_text = ' ';\r\n  }\r\n\r\n  let showInfo3 = {};\r\n  if (props.incoming_call_ts && props.incoming_call_ts[props.index]) {\r\n    showInfo3 = props.incoming_call_ts[props.index];\r\n  }\r\n  let showInfoSeperator3 = { ...showInfo3 };\r\n  showInfoSeperator3.display_text = ' ';\r\n\r\n  let showBlock = [];\r\n  /* 1行で点滅する為、仕様より、Span+1(隙間列)にする */\r\n  showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo1, className: 'col-span-4 col-start-2' });\r\n  showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo2, className: 'col-span-3 col-start-7' });\r\n  showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo3, className: 'col-span-5 col-start-11' });\r\n  if (props.rightPadding) {\r\n    showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n  }\r\n\r\n  return (\r\n    <BlinkBlock\r\n      block={showBlock}\r\n      blink_setting={\r\n        props.blink_setting ? props.blink_setting[props.index] : {}\r\n      }\r\n    />\r\n  );\r\n};\r\n\r\nexport default DigitalRadio;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\AmbulanceRate.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\NoContent.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\Now.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Title.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\ArrayScroll.js", ["209"], [], "import React, { useRef, useState, useEffect } from 'react';\r\nimport Cell from './Cell';\r\nimport { useDeepCompareEffect } from 'ahooks';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  max: PropTypes.number,\r\n  change_setting: PropTypes.object,\r\n  display_data: PropTypes.array,\r\n  gridLevelProps: PropTypes.string,\r\n  cellLevelProps: PropTypes.array,\r\n};\r\n\r\n/**\r\n * 切替設定に従い、配列をスクロールして表示する <br>\r\n * ただ、切替設定/切替サイズが0の場合は切り替えを行わない。\r\n * @module ArrayScroll\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\r\nconst ArrayScroll = (props) => {\r\n  const maxNum = props?.max;\r\n  //最大のChangeSizeを表示箱に超えないようにガイド\r\n  if (props?.change_setting?.change_size > maxNum) {\r\n    props.change_setting.change_size = maxNum;\r\n  }\r\n\r\n  //最初の表示は、最大サイズ(props.max)で配列を初期化する\r\n  let defaultSubArray = props?.display_data;\r\n  if (maxNum) {\r\n    defaultSubArray = props?.display_data?.slice(0, maxNum);\r\n    \r\n    defaultSubArray = fillArrayWithDummy(defaultSubArray, maxNum);\r\n  }\r\n\r\n  const [showInfo, setShowInfo] = useState({\r\n    subArray: defaultSubArray,\r\n    changeSize: 0,\r\n  });\r\n  // RefでsetIntervalの各Intervalで、表示配列を共有する\r\n  const showInfoRef = useRef(showInfo);\r\n\r\n  useDeepCompareEffect(() => {\r\n    let id = null;\r\n\r\n    // change_time/change_sizeが0の場合は切り替えを行わない\r\n    if (canScroll(props)) {\r\n      id = setInterval(() => {\r\n        let nextSize;\r\n        if (\r\n          showInfoRef.current.changeSize + maxNum >=\r\n          props?.display_data?.length\r\n        ) {\r\n          nextSize = 0;\r\n        } else {\r\n          nextSize =\r\n            showInfoRef.current.changeSize + props?.change_setting?.change_size;\r\n        }\r\n\r\n        //次の表示サイズは、最大の表示サイズMaxNumより、少ない場合、MaxNumを表示する\r\n        if (nextSize + maxNum >= props?.display_data?.length) { \r\n          nextSize = props?.display_data?.length - maxNum;\r\n        }\r\n\r\n        const newState = {\r\n          changeSize: nextSize,\r\n          subArray: props?.display_data?.slice(nextSize, nextSize + maxNum),\r\n        };\r\n        setShowInfo(newState);\r\n        showInfoRef.current = newState;\r\n      }, props?.change_setting?.change_time * 1000);\r\n    } else if (props?.display_data) {\r\n        const newState = {\r\n          changeSize: 0,\r\n          subArray: props?.display_data?.slice(0, 0 + maxNum),\r\n        };\r\n        newState.subArray = fillArrayWithDummy(newState.subArray, maxNum);\r\n\r\n        setShowInfo(newState);\r\n        showInfoRef.current = newState;\r\n    }\r\n\r\n    if (id) {\r\n      return () => clearInterval(id);\r\n    }\r\n  }, [props?.change_setting, props?.display_data]);\r\n\r\n  if (!props) {\r\n    console.error('ArrayScroll: props is null.');\r\n    return;\r\n  }\r\n\r\n  return (\r\n    <div className={`grid ${props?.gridLevelProps}`}>\r\n      {showInfo.subArray?.map((item, index) => {\r\n        if (Array.isArray(item)) {\r\n          return (\r\n            <span> 配列表示をサポートしません </span>\r\n            // <ArrayRow key={index} items={item} cellLevelProps={props?.cellLevelProps} />\r\n          );\r\n        } else {\r\n          const cellPropsIndex = index % props?.cellLevelProps?.length;\r\n          let row4Cell = {\r\n            text_color: item.text_color,\r\n            text: item.display_text,\r\n            background_color: item.background_color,\r\n            className: props?.cellLevelProps[cellPropsIndex],\r\n          };\r\n\r\n          return (\r\n            <Cell\r\n              key={index}\r\n              {...row4Cell}\r\n            />\r\n          );\r\n        }\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// 表示箱より少ない場合、Dummyデータを作成\r\nfunction fillArrayWithDummy(defaultSubArray, maxNum) {\r\n  if (defaultSubArray.length < maxNum) {\r\n    const dummyCounter = maxNum - defaultSubArray.length;\r\n    for (let i = 0; i < dummyCounter; i++) {\r\n      defaultSubArray.push({ display_text: '　' });\r\n    }\r\n  }\r\n  return defaultSubArray;\r\n}\r\n\r\n/**\r\n * change_size/change_timeが有効な値があれば、Scrollすると返します。\r\n * @param {*} obj\r\n * @returns true: Scroll. false: Scrollしない\r\n */\r\nfunction canScroll(obj) {\r\n  if (!obj) return false;\r\n\r\n  return (\r\n    obj?.display_data &&\r\n    obj?.display_data.length > obj?.max &&\r\n    obj?.change_setting &&\r\n    obj?.change_setting?.change_time > 0 &&\r\n    obj?.change_setting?.change_size > 0\r\n  );\r\n}\r\n\r\nArrayScroll.propTypes = propTypes;\r\n\r\nexport default ArrayScroll;\r\n", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\Cell.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\CellBox.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\BlinkBlock.js", ["210"], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\TextScroll.tsx", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\elements\\ScaleBlock.js", [], [], "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\src\\components\\ExtendedVehicle.js", ["211", "212"], [], {"ruleId": "213", "severity": 1, "message": "214", "line": 8, "column": 10, "nodeType": "215", "messageId": "216", "endLine": 8, "endColumn": 25}, {"ruleId": "213", "severity": 1, "message": "217", "line": 23, "column": 18, "nodeType": "215", "messageId": "216", "endLine": 23, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "218", "line": 53, "column": 15, "nodeType": "215", "messageId": "216", "endLine": 53, "endColumn": 18}, {"ruleId": "219", "severity": 1, "message": "220", "line": 159, "column": 47, "nodeType": "221", "messageId": "222", "endLine": 159, "endColumn": 49}, {"ruleId": "213", "severity": 1, "message": "223", "line": 1, "column": 8, "nodeType": "215", "messageId": "216", "endLine": 1, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "220", "line": 56, "column": 31, "nodeType": "221", "messageId": "222", "endLine": 56, "endColumn": 33}, {"ruleId": "219", "severity": 1, "message": "220", "line": 59, "column": 31, "nodeType": "221", "messageId": "222", "endLine": 59, "endColumn": 33}, {"ruleId": "219", "severity": 1, "message": "220", "line": 169, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 169, "endColumn": 37}, {"ruleId": "219", "severity": 1, "message": "220", "line": 198, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 198, "endColumn": 37}, {"ruleId": "219", "severity": 1, "message": "220", "line": 225, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 225, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "224", "line": 259, "column": 7, "nodeType": "215", "messageId": "216", "endLine": 259, "endColumn": 22}, {"ruleId": "219", "severity": 1, "message": "220", "line": 263, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 263, "endColumn": 37}, {"ruleId": "219", "severity": 1, "message": "220", "line": 266, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 266, "endColumn": 37}, {"ruleId": "219", "severity": 1, "message": "220", "line": 270, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 270, "endColumn": 37}, {"ruleId": "219", "severity": 1, "message": "220", "line": 273, "column": 35, "nodeType": "221", "messageId": "222", "endLine": 273, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "225", "line": 7, "column": 7, "nodeType": "215", "messageId": "216", "endLine": 7, "endColumn": 31}, {"ruleId": "213", "severity": 1, "message": "226", "line": 8, "column": 7, "nodeType": "215", "messageId": "216", "endLine": 8, "endColumn": 33}, {"ruleId": "213", "severity": 1, "message": "227", "line": 1, "column": 17, "nodeType": "215", "messageId": "216", "endLine": 1, "endColumn": 26}, {"ruleId": "213", "severity": 1, "message": "228", "line": 3, "column": 38, "nodeType": "215", "messageId": "216", "endLine": 3, "endColumn": 49}, {"ruleId": "219", "severity": 1, "message": "220", "line": 44, "column": 23, "nodeType": "221", "messageId": "222", "endLine": 44, "endColumn": 25}, {"ruleId": "219", "severity": 1, "message": "220", "line": 63, "column": 101, "nodeType": "221", "messageId": "222", "endLine": 63, "endColumn": 103}, {"ruleId": "213", "severity": 1, "message": "229", "line": 1, "column": 35, "nodeType": "215", "messageId": "216", "endLine": 1, "endColumn": 44}, {"ruleId": "230", "severity": 1, "message": "231", "line": 46, "column": 8, "nodeType": "232", "endLine": 46, "endColumn": 30, "suggestions": "233"}, {"ruleId": "213", "severity": 1, "message": "234", "line": 7, "column": 7, "nodeType": "215", "messageId": "216", "endLine": 7, "endColumn": 40}, {"ruleId": "213", "severity": 1, "message": "235", "line": 8, "column": 7, "nodeType": "215", "messageId": "216", "endLine": 8, "endColumn": 42}, "no-unused-vars", "'useUpdateEffect' is defined but never used.", "Identifier", "unusedVar", "'setSearch' is assigned a value but never used.", "'key' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'React' is defined but never used.", "'getGridRowClass' is assigned a value but never used.", "'GRID_COLS_VEHICLE_DEPLOY' is assigned a value but never used.", "'GRID_COLS_VEHICLE_NODEPLOY' is assigned a value but never used.", "'Component' is defined but never used.", "'toThousands' is defined but never used.", "'useEffect' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'delay'. Either include it or remove the dependency array.", "ArrayExpression", ["236"], "'GRID_COLS_EXTENDED_VEHICLE_DEPLOY' is assigned a value but never used.", "'GRID_COLS_EXTENDED_VEHICLE_NODEPLOY' is assigned a value but never used.", {"desc": "237", "fix": "238"}, "Update the dependencies array to be: [delay, props?.blink_setting]", {"range": "239", "text": "240"}, [1437, 1459], "[delay, props?.blink_setting]"]