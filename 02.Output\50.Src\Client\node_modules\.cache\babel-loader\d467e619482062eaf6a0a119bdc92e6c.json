{"ast": null, "code": "/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n/** Used to resolve the decompiled source of functions. */\n\nvar funcToString = funcProto.toString;\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\n\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n\n    try {\n      return func + '';\n    } catch (e) {}\n  }\n\n  return '';\n}\n\nmodule.exports = toSource;", "map": {"version": 3, "names": ["funcProto", "Function", "prototype", "funcToString", "toString", "toSource", "func", "call", "e", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_toSource.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\nmodule.exports = toSource;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,QAAQ,CAACC,SAAzB;AAEA;;AACA,IAAIC,YAAY,GAAGH,SAAS,CAACI,QAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,IAAlB,EAAwB;EACtB,IAAIA,IAAI,IAAI,IAAZ,EAAkB;IAChB,IAAI;MACF,OAAOH,YAAY,CAACI,IAAb,CAAkBD,IAAlB,CAAP;IACD,CAFD,CAEE,OAAOE,CAAP,EAAU,CAAE;;IACd,IAAI;MACF,OAAQF,IAAI,GAAG,EAAf;IACD,CAFD,CAEE,OAAOE,CAAP,EAAU,CAAE;EACf;;EACD,OAAO,EAAP;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}