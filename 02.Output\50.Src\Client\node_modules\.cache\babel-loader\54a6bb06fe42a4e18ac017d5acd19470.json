{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n/** `Object#toString` result references. */\n\n\nvar symbolTag = '[object Symbol]';\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\n\nfunction isSymbol(value) {\n  return typeof value == 'symbol' || isObjectLike(value) && baseGetTag(value) == symbolTag;\n}\n\nmodule.exports = isSymbol;", "map": {"version": 3, "names": ["baseGetTag", "require", "isObjectLike", "symbolTag", "isSymbol", "value", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/isSymbol.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nmodule.exports = isSymbol;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,gBAAD,CAD1B;AAGA;;;AACA,IAAIE,SAAS,GAAG,iBAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;EACvB,OAAO,OAAOA,KAAP,IAAgB,QAAhB,IACJH,YAAY,CAACG,KAAD,CAAZ,IAAuBL,UAAU,CAACK,KAAD,CAAV,IAAqBF,SAD/C;AAED;;AAEDG,MAAM,CAACC,OAAP,GAAiBH,QAAjB"}, "metadata": {}, "sourceType": "script"}