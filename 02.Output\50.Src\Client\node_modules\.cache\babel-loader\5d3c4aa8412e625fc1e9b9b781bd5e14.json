{"ast": null, "code": "export var isObject = function isObject(value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function isFunction(value) {\n  return typeof value === 'function';\n};\nexport var isString = function isString(value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function isBoolean(value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function isNumber(value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function isUndef(value) {\n  return typeof value === 'undefined';\n};", "map": {"version": 3, "names": ["isObject", "value", "isFunction", "isString", "isBoolean", "isNumber", "isUndef"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/index.js"], "sourcesContent": ["export var isObject = function isObject(value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function isFunction(value) {\n  return typeof value === 'function';\n};\nexport var isString = function isString(value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function isBoolean(value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function isNumber(value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function isUndef(value) {\n  return typeof value === 'undefined';\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG,SAASA,QAAT,CAAkBC,KAAlB,EAAyB;EAC7C,OAAOA,KAAK,KAAK,IAAV,IAAkB,OAAOA,KAAP,KAAiB,QAA1C;AACD,CAFM;AAGP,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAT,CAAoBD,KAApB,EAA2B;EACjD,OAAO,OAAOA,KAAP,KAAiB,UAAxB;AACD,CAFM;AAGP,OAAO,IAAIE,QAAQ,GAAG,SAASA,QAAT,CAAkBF,KAAlB,EAAyB;EAC7C,OAAO,OAAOA,KAAP,KAAiB,QAAxB;AACD,CAFM;AAGP,OAAO,IAAIG,SAAS,GAAG,SAASA,SAAT,CAAmBH,KAAnB,EAA0B;EAC/C,OAAO,OAAOA,KAAP,KAAiB,SAAxB;AACD,CAFM;AAGP,OAAO,IAAII,QAAQ,GAAG,SAASA,QAAT,CAAkBJ,KAAlB,EAAyB;EAC7C,OAAO,OAAOA,KAAP,KAAiB,QAAxB;AACD,CAFM;AAGP,OAAO,IAAIK,OAAO,GAAG,SAASA,OAAT,CAAiBL,KAAjB,EAAwB;EAC3C,OAAO,OAAOA,KAAP,KAAiB,WAAxB;AACD,CAFM"}, "metadata": {}, "sourceType": "module"}