{"ast": null, "code": "var isKeyable = require('./_isKeyable');\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\n\n\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key) ? data[typeof key == 'string' ? 'string' : 'hash'] : data.map;\n}\n\nmodule.exports = getMapData;", "map": {"version": 3, "names": ["isKeyable", "require", "getMapData", "map", "key", "data", "__data__", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getMapData.js"], "sourcesContent": ["var isKeyable = require('./_isKeyable');\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\nmodule.exports = getMapData;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBC,GAApB,EAAyBC,GAAzB,EAA8B;EAC5B,IAAIC,IAAI,GAAGF,GAAG,CAACG,QAAf;EACA,OAAON,SAAS,CAACI,GAAD,CAAT,GACHC,IAAI,CAAC,OAAOD,GAAP,IAAc,QAAd,GAAyB,QAAzB,GAAoC,MAArC,CADD,GAEHC,IAAI,CAACF,GAFT;AAGD;;AAEDI,MAAM,CAACC,OAAP,GAAiBN,UAAjB"}, "metadata": {}, "sourceType": "script"}