{"version": 3, "file": "static/css/main.fa95262d.css", "mappings": "AAAA;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,kCAAc,CAAd,gMAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,qHAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mDAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,yEAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,wBAAc,CAAd,kFAAc,CAAd,SAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,gCAAc,CAAd,cAAc,CAAd,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,0CAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAEd,uBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,cAAmB,CAAnB,YAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,wCAAmB,CAAnB,wCAAmB,CAAnB,+BAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,wCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,mBAAmB,CAAnB,wCAAmB,CAAnB,sDAAmB,CAAnB,sDAAmB,CAAnB,sDAAmB,CAAnB,yDAAmB,CAAnB,6BAAmB,CAAnB,iBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,iBAAmB,CAAnB,4BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,6BAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,qBAAmB,CAAnB,8BAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,sBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,gNAAmB,CAAnB,6LAAmB,CAAnB,qCAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,6CAAmB,CAAnB,oDAAmB,CAAnB,6BAAmB,CAAnB,oCAAmB,CAAnB,0DAAmB,CAAnB,iNAAmB,CAAnB,mKAAmB,CAAnB,mKAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,uOAAmB,CAAnB,4DAAmB,CAAnB,oNAAmB,CAAnB,8QAAmB,CAAnB,mKAAmB,CAAnB,4DAAmB,CAAnB,4DAAmB,CAAnB,0MAAmB,CAAnB,4DAAmB,CAAnB,kMAAmB,CAAnB,+KAAmB,CAAnB,4DAAmB,CAAnB,oPAAmB,CAAnB,8PAAmB,CAAnB,uOAAmB,CAAnB,+MAAmB,CAAnB,yLAAmB,CAAnB,2UAAmB,CAAnB,mKAAmB,CAAnB,0NAAmB,CAAnB,gEAAmB,CAAnB,sMAAmB,CAAnB,gLAAmB,CAAnB,qLAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,yDAAmB,CAAnB,uDAAmB,CAAnB,yDAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,0CAAmB,CAAnB,4CAAmB,CAAnB,sCAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,eAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,iCAAmB,CAAnB,uCAAmB,CAAnB,eAAmB,CAAnB,iCAAmB,CAAnB,4CAAmB,CAAnB,iBAAmB,CAAnB,uCAAmB,CAAnB,eAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,iCAAmB,CAAnB,8CAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,iCAAmB,CAAnB,uCAAmB,CAAnB,eAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,wCAAmB,CAAnB,gCAAmB,CAAnB,wCAAmB,CAAnB,8BAAmB,CAAnB,6EAAmB,CAAnB,wBAAmB,CAAnB,yCAAmB,CAAnB,uBAAmB,CAAnB,iCAAmB,CAAnB,wCAAmB,CAAnB,yCAAmB,CAAnB,uCAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,gDAAmB,CAAnB,2BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,oDAAmB,CAAnB,6BAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,uBAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,2BAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,4CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,4CAAmB,CAAnB,sCAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,2CAAmB,CAEnB,WACE,2BAA8B,CAG9B,iBAAkB,CADlB,eAAmB,CADnB,gGAGF,CCTA,SACE,uBACF,CCFA,aAOE,kBAAmB,CADnB,YAAa,CAJb,WAAY,CACZ,eAAgB,CAEhB,kBAAmB,CAJnB,UAAW,CAGX,mBAIF,CAEA,eACE,oBACF", "sources": ["index.css", "components/Weather.css", "components/elements/TextScroll.css"], "sourcesContent": ["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\n@font-face {\r\n  font-family: 'GenSenRoundedJP';\r\n  src: url('../public/fonts/GenSenRounded-JP-R.ttf') format('truetype');\r\n  font-weight: normal;\r\n  font-style: normal;\r\n}\r\n\r\n@layer base {\r\n  html {\r\n    font-family: \"GenSenRoundedJP\";\r\n    font-size: 16px;\r\n  }\r\n}\r\n", ".justify {\r\n  text-align-last: justify;\r\n}\r\n", ".marquee_box {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  word-break: keep-all;\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.marquee_box p {\r\n  display: inline-block;\r\n}\r\n"], "names": [], "sourceRoot": ""}