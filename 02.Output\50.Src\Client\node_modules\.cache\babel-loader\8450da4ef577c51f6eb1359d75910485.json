{"ast": null, "code": "'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function (length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function (length) {\n    var bytes = new Array(length);\n\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n\n    return bytes;\n  };\n}", "map": {"version": 3, "names": ["global", "crypto", "getRandomValues", "module", "exports", "randomBytes", "length", "bytes", "Uint8Array", "Array", "i", "Math", "floor", "random"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/browser-crypto.js"], "sourcesContent": ["'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Array(length);\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n    return bytes;\n  };\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM,CAACC,MAAP,IAAiBD,MAAM,CAACC,MAAP,CAAcC,eAAnC,EAAoD;EAClDC,MAAM,CAACC,OAAP,CAAeC,WAAf,GAA6B,UAASC,MAAT,EAAiB;IAC5C,IAAIC,KAAK,GAAG,IAAIC,UAAJ,CAAeF,MAAf,CAAZ;IACAN,MAAM,CAACC,MAAP,CAAcC,eAAd,CAA8BK,KAA9B;IACA,OAAOA,KAAP;EACD,CAJD;AAKD,CAND,MAMO;EACLJ,MAAM,CAACC,OAAP,CAAeC,WAAf,GAA6B,UAASC,MAAT,EAAiB;IAC5C,IAAIC,KAAK,GAAG,IAAIE,KAAJ,CAAUH,MAAV,CAAZ;;IACA,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAApB,EAA4BI,CAAC,EAA7B,EAAiC;MAC/BH,KAAK,CAACG,CAAD,CAAL,GAAWC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,GAA3B,CAAX;IACD;;IACD,OAAON,KAAP;EACD,CAND;AAOD"}, "metadata": {}, "sourceType": "script"}