{"ast": null, "code": "'use strict';\n\nrequire('./shims');\n\nvar URL = require('url-parse'),\n    inherits = require('inherits'),\n    random = require('./utils/random'),\n    escape = require('./utils/escape'),\n    urlUtils = require('./utils/url'),\n    eventUtils = require('./utils/event'),\n    transport = require('./utils/transport'),\n    objectUtils = require('./utils/object'),\n    browser = require('./utils/browser'),\n    log = require('./utils/log'),\n    Event = require('./event/event'),\n    EventTarget = require('./event/eventtarget'),\n    loc = require('./location'),\n    CloseEvent = require('./event/close'),\n    TransportMessageEvent = require('./event/trans-message'),\n    InfoReceiver = require('./info-receiver');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\n\nvar transports; // follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\n\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n\n  EventTarget.call(this);\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = ''; // non-standard extension\n\n  options = options || {};\n\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n  var sessionId = options.sessionId || 8;\n\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function () {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n\n  this._server = options.server || random.numberString(1000); // Step 1 of WS spec - parse and validate the url. Issue #8\n\n  var parsedUrl = new URL(url);\n\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n\n  var secure = parsedUrl.protocol === 'https:'; // Step 2 - don't allow secure origin with an insecure protocol\n\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  } // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n\n\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  } // Step 5 - check protocols argument\n\n\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function (proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n\n    if (i < sortedProtocols.length - 1 && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  }); // Step 6 - convert origin\n\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null; // remove the trailing slash\n\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, '')); // store the sanitized url\n\n  this.url = parsedUrl.href;\n  debug('using url', this.url); // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain(),\n    sameOrigin: urlUtils.isOriginEqual(this.url, loc.href),\n    sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\n\ninherits(SockJS, EventTarget);\n\nfunction userSetCode(code) {\n  return code === 1000 || code >= 3000 && code <= 4999;\n}\n\nSockJS.prototype.close = function (code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  } // Step 2.4 states the max is 123 bytes, but we are just checking length\n\n\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  } // Step 3.1\n\n\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  } // TODO look at docs to determine how to set this\n\n\n  var wasClean = true;\n\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\n\nSockJS.prototype.send = function (data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n\n  this._transport.send(escape.quote(data));\n};\n\nSockJS.version = require('./version');\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\n\nSockJS.prototype._receiveInfo = function (info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n\n    return;\n  } // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n\n\n  this._rto = this.countRTO(rtt); // allow server to override url used for the actual transport\n\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info); // determine list of desired and supported transports\n\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n\n  this._connect();\n};\n\nSockJS.prototype._connect = function () {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n\n    if (Transport.needBody) {\n      if (!global.document.body || typeof global.document.readyState !== 'undefined' && global.document.readyState !== 'complete' && global.document.readyState !== 'interactive') {\n        debug('waiting for body');\n\n        this._transports.unshift(Transport);\n\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    } // calculate timeout based on RTO and round trips. Default to 5s\n\n\n    var timeoutMs = Math.max(this._timeout, this._rto * Transport.roundTrips || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n    return;\n  }\n\n  this._close(2000, 'All transports failed', false);\n};\n\nSockJS.prototype._transportTimeout = function () {\n  debug('_transportTimeout');\n\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\n\nSockJS.prototype._transportMessage = function (msg) {\n  debug('_transportMessage', msg);\n  var self = this,\n      type = msg.slice(0, 1),\n      content = msg.slice(1),\n      payload; // first check for messages that don't need a payload\n\n  switch (type) {\n    case 'o':\n      this._open();\n\n      return;\n\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function (p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n\n      break;\n\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n\n      break;\n  }\n};\n\nSockJS.prototype._transportClose = function (code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n\n  if (this._transport) {\n    this._transport.removeAllListeners();\n\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n\n    return;\n  }\n\n  this._close(code, reason);\n};\n\nSockJS.prototype._open = function () {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\n\nSockJS.prototype._close = function (code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n\n  if (this._ir) {\n    forceFail = true;\n\n    this._ir.close();\n\n    this._ir = null;\n  }\n\n  if (this._transport) {\n    this._transport.close();\n\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function () {\n    this.readyState = SockJS.CLOSED;\n\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n}; // See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\n\n\nSockJS.prototype.countRTO = function (rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\n\nmodule.exports = function (availableTransports) {\n  transports = transport(availableTransports);\n\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n\n  return SockJS;\n};", "map": {"version": 3, "names": ["require", "URL", "inherits", "random", "escape", "urlUtils", "eventUtils", "transport", "objectUtils", "browser", "log", "Event", "EventTarget", "loc", "CloseEvent", "TransportMessageEvent", "InfoReceiver", "debug", "process", "env", "NODE_ENV", "transports", "SockJS", "url", "protocols", "options", "arguments", "length", "TypeError", "call", "readyState", "CONNECTING", "extensions", "protocol", "protocols_whitelist", "warn", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_transportOptions", "transportOptions", "_timeout", "timeout", "sessionId", "_generateSessionId", "string", "_server", "server", "numberString", "parsedUrl", "host", "SyntaxError", "hash", "secure", "isLoopbackAddr", "hostname", "Error", "Array", "isArray", "sortedProtocols", "sort", "for<PERSON>ach", "proto", "i", "o", "<PERSON><PERSON><PERSON><PERSON>", "href", "_origin", "toLowerCase", "set", "pathname", "replace", "_urlInfo", "<PERSON><PERSON><PERSON><PERSON>", "hasDomain", "<PERSON><PERSON><PERSON><PERSON>", "isOriginEqual", "sameScheme", "isSchemeEqual", "_ir", "once", "_receiveInfo", "bind", "userSetCode", "code", "prototype", "close", "reason", "CLOSING", "CLOSED", "<PERSON><PERSON><PERSON>", "_close", "send", "data", "OPEN", "_transport", "quote", "version", "info", "rtt", "_rto", "countRTO", "_transUrl", "base_url", "extend", "enabledTransports", "filterToEnabled", "_transports", "main", "_connect", "Transport", "shift", "transportName", "needBody", "global", "document", "body", "unshift", "attachEvent", "timeoutMs", "Math", "max", "roundTrips", "_transportTimeoutId", "setTimeout", "_transportTimeout", "transportUrl", "addPath", "transportObj", "on", "_transportMessage", "_transportClose", "msg", "self", "type", "slice", "content", "payload", "_open", "dispatchEvent", "JSON", "parse", "e", "p", "removeAllListeners", "clearTimeout", "forceFail", "onmessage", "onclose", "onerror", "module", "exports", "availableTransports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/main.js"], "sourcesContent": ["'use strict';\n\nrequire('./shims');\n\nvar URL = require('url-parse')\n  , inherits = require('inherits')\n  , random = require('./utils/random')\n  , escape = require('./utils/escape')\n  , urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , transport = require('./utils/transport')\n  , objectUtils = require('./utils/object')\n  , browser = require('./utils/browser')\n  , log = require('./utils/log')\n  , Event = require('./event/event')\n  , EventTarget = require('./event/eventtarget')\n  , loc = require('./location')\n  , CloseEvent = require('./event/close')\n  , TransportMessageEvent = require('./event/trans-message')\n  , InfoReceiver = require('./info-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\n\nvar transports;\n\n// follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n  EventTarget.call(this);\n\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = '';\n\n  // non-standard extension\n  options = options || {};\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n\n  var sessionId = options.sessionId || 8;\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function() {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n\n  this._server = options.server || random.numberString(1000);\n\n  // Step 1 of WS spec - parse and validate the url. Issue #8\n  var parsedUrl = new URL(url);\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n\n  var secure = parsedUrl.protocol === 'https:';\n  // Step 2 - don't allow secure origin with an insecure protocol\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  }\n\n  // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  }\n\n  // Step 5 - check protocols argument\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function(proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n    if (i < (sortedProtocols.length - 1) && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  });\n\n  // Step 6 - convert origin\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null;\n\n  // remove the trailing slash\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, ''));\n\n  // store the sanitized url\n  this.url = parsedUrl.href;\n  debug('using url', this.url);\n\n  // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain()\n  , sameOrigin: urlUtils.isOriginEqual(this.url, loc.href)\n  , sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\n\ninherits(SockJS, EventTarget);\n\nfunction userSetCode(code) {\n  return code === 1000 || (code >= 3000 && code <= 4999);\n}\n\nSockJS.prototype.close = function(code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  }\n  // Step 2.4 states the max is 123 bytes, but we are just checking length\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  }\n\n  // Step 3.1\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  }\n\n  // TODO look at docs to determine how to set this\n  var wasClean = true;\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\n\nSockJS.prototype.send = function(data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n  this._transport.send(escape.quote(data));\n};\n\nSockJS.version = require('./version');\n\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\n\nSockJS.prototype._receiveInfo = function(info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n    return;\n  }\n\n  // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n  this._rto = this.countRTO(rtt);\n  // allow server to override url used for the actual transport\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info);\n  // determine list of desired and supported transports\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n\n  this._connect();\n};\n\nSockJS.prototype._connect = function() {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n    if (Transport.needBody) {\n      if (!global.document.body ||\n          (typeof global.document.readyState !== 'undefined' &&\n            global.document.readyState !== 'complete' &&\n            global.document.readyState !== 'interactive')) {\n        debug('waiting for body');\n        this._transports.unshift(Transport);\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    }\n\n    // calculate timeout based on RTO and round trips. Default to 5s\n    var timeoutMs = Math.max(this._timeout, (this._rto * Transport.roundTrips) || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n\n    return;\n  }\n  this._close(2000, 'All transports failed', false);\n};\n\nSockJS.prototype._transportTimeout = function() {\n  debug('_transportTimeout');\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\n\nSockJS.prototype._transportMessage = function(msg) {\n  debug('_transportMessage', msg);\n  var self = this\n    , type = msg.slice(0, 1)\n    , content = msg.slice(1)\n    , payload\n    ;\n\n  // first check for messages that don't need a payload\n  switch (type) {\n    case 'o':\n      this._open();\n      return;\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function(p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n      break;\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n      break;\n  }\n};\n\nSockJS.prototype._transportClose = function(code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n  if (this._transport) {\n    this._transport.removeAllListeners();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n    return;\n  }\n\n  this._close(code, reason);\n};\n\nSockJS.prototype._open = function() {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\n\nSockJS.prototype._close = function(code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n\n  if (this._ir) {\n    forceFail = true;\n    this._ir.close();\n    this._ir = null;\n  }\n  if (this._transport) {\n    this._transport.close();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function() {\n    this.readyState = SockJS.CLOSED;\n\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n};\n\n// See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\nSockJS.prototype.countRTO = function(rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\n\nmodule.exports = function(availableTransports) {\n  transports = transport(availableTransports);\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n  return SockJS;\n};\n"], "mappings": "AAAA;;AAEAA,OAAO,CAAC,SAAD,CAAP;;AAEA,IAAIC,GAAG,GAAGD,OAAO,CAAC,WAAD,CAAjB;AAAA,IACIE,QAAQ,GAAGF,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIG,MAAM,GAAGH,OAAO,CAAC,gBAAD,CAFpB;AAAA,IAGII,MAAM,GAAGJ,OAAO,CAAC,gBAAD,CAHpB;AAAA,IAIIK,QAAQ,GAAGL,OAAO,CAAC,aAAD,CAJtB;AAAA,IAKIM,UAAU,GAAGN,OAAO,CAAC,eAAD,CALxB;AAAA,IAMIO,SAAS,GAAGP,OAAO,CAAC,mBAAD,CANvB;AAAA,IAOIQ,WAAW,GAAGR,OAAO,CAAC,gBAAD,CAPzB;AAAA,IAQIS,OAAO,GAAGT,OAAO,CAAC,iBAAD,CARrB;AAAA,IASIU,GAAG,GAAGV,OAAO,CAAC,aAAD,CATjB;AAAA,IAUIW,KAAK,GAAGX,OAAO,CAAC,eAAD,CAVnB;AAAA,IAWIY,WAAW,GAAGZ,OAAO,CAAC,qBAAD,CAXzB;AAAA,IAYIa,GAAG,GAAGb,OAAO,CAAC,YAAD,CAZjB;AAAA,IAaIc,UAAU,GAAGd,OAAO,CAAC,eAAD,CAbxB;AAAA,IAcIe,qBAAqB,GAAGf,OAAO,CAAC,uBAAD,CAdnC;AAAA,IAeIgB,YAAY,GAAGhB,OAAO,CAAC,iBAAD,CAf1B;;AAkBA,IAAIiB,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGjB,OAAO,CAAC,OAAD,CAAP,CAAiB,oBAAjB,CAAR;AACD;;AAED,IAAIqB,UAAJ,C,CAEA;;AACA,SAASC,MAAT,CAAgBC,GAAhB,EAAqBC,SAArB,EAAgCC,OAAhC,EAAyC;EACvC,IAAI,EAAE,gBAAgBH,MAAlB,CAAJ,EAA+B;IAC7B,OAAO,IAAIA,MAAJ,CAAWC,GAAX,EAAgBC,SAAhB,EAA2BC,OAA3B,CAAP;EACD;;EACD,IAAIC,SAAS,CAACC,MAAV,GAAmB,CAAvB,EAA0B;IACxB,MAAM,IAAIC,SAAJ,CAAc,sEAAd,CAAN;EACD;;EACDhB,WAAW,CAACiB,IAAZ,CAAiB,IAAjB;EAEA,KAAKC,UAAL,GAAkBR,MAAM,CAACS,UAAzB;EACA,KAAKC,UAAL,GAAkB,EAAlB;EACA,KAAKC,QAAL,GAAgB,EAAhB,CAXuC,CAavC;;EACAR,OAAO,GAAGA,OAAO,IAAI,EAArB;;EACA,IAAIA,OAAO,CAACS,mBAAZ,EAAiC;IAC/BxB,GAAG,CAACyB,IAAJ,CAAS,gEAAT;EACD;;EACD,KAAKC,oBAAL,GAA4BX,OAAO,CAACJ,UAApC;EACA,KAAKgB,iBAAL,GAAyBZ,OAAO,CAACa,gBAAR,IAA4B,EAArD;EACA,KAAKC,QAAL,GAAgBd,OAAO,CAACe,OAAR,IAAmB,CAAnC;EAEA,IAAIC,SAAS,GAAGhB,OAAO,CAACgB,SAAR,IAAqB,CAArC;;EACA,IAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;IACnC,KAAKC,kBAAL,GAA0BD,SAA1B;EACD,CAFD,MAEO,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;IACxC,KAAKC,kBAAL,GAA0B,YAAW;MACnC,OAAOvC,MAAM,CAACwC,MAAP,CAAcF,SAAd,CAAP;IACD,CAFD;EAGD,CAJM,MAIA;IACL,MAAM,IAAIb,SAAJ,CAAc,6EAAd,CAAN;EACD;;EAED,KAAKgB,OAAL,GAAenB,OAAO,CAACoB,MAAR,IAAkB1C,MAAM,CAAC2C,YAAP,CAAoB,IAApB,CAAjC,CAjCuC,CAmCvC;;EACA,IAAIC,SAAS,GAAG,IAAI9C,GAAJ,CAAQsB,GAAR,CAAhB;;EACA,IAAI,CAACwB,SAAS,CAACC,IAAX,IAAmB,CAACD,SAAS,CAACd,QAAlC,EAA4C;IAC1C,MAAM,IAAIgB,WAAJ,CAAgB,cAAc1B,GAAd,GAAoB,cAApC,CAAN;EACD,CAFD,MAEO,IAAIwB,SAAS,CAACG,IAAd,EAAoB;IACzB,MAAM,IAAID,WAAJ,CAAgB,qCAAhB,CAAN;EACD,CAFM,MAEA,IAAIF,SAAS,CAACd,QAAV,KAAuB,OAAvB,IAAkCc,SAAS,CAACd,QAAV,KAAuB,QAA7D,EAAuE;IAC5E,MAAM,IAAIgB,WAAJ,CAAgB,2DAA2DF,SAAS,CAACd,QAArE,GAAgF,mBAAhG,CAAN;EACD;;EAED,IAAIkB,MAAM,GAAGJ,SAAS,CAACd,QAAV,KAAuB,QAApC,CA7CuC,CA8CvC;;EACA,IAAIpB,GAAG,CAACoB,QAAJ,KAAiB,QAAjB,IAA6B,CAACkB,MAAlC,EAA0C;IACxC;IACA,IAAI,CAAC9C,QAAQ,CAAC+C,cAAT,CAAwBL,SAAS,CAACM,QAAlC,CAAL,EAAkD;MAChD,MAAM,IAAIC,KAAJ,CAAU,iGAAV,CAAN;IACD;EACF,CApDsC,CAsDvC;EACA;;;EACA,IAAI,CAAC9B,SAAL,EAAgB;IACdA,SAAS,GAAG,EAAZ;EACD,CAFD,MAEO,IAAI,CAAC+B,KAAK,CAACC,OAAN,CAAchC,SAAd,CAAL,EAA+B;IACpCA,SAAS,GAAG,CAACA,SAAD,CAAZ;EACD,CA5DsC,CA8DvC;;;EACA,IAAIiC,eAAe,GAAGjC,SAAS,CAACkC,IAAV,EAAtB;EACAD,eAAe,CAACE,OAAhB,CAAwB,UAASC,KAAT,EAAgBC,CAAhB,EAAmB;IACzC,IAAI,CAACD,KAAL,EAAY;MACV,MAAM,IAAIX,WAAJ,CAAgB,0BAA0BW,KAA1B,GAAkC,eAAlD,CAAN;IACD;;IACD,IAAIC,CAAC,GAAIJ,eAAe,CAAC9B,MAAhB,GAAyB,CAA9B,IAAoCiC,KAAK,KAAKH,eAAe,CAACI,CAAC,GAAG,CAAL,CAAjE,EAA0E;MACxE,MAAM,IAAIZ,WAAJ,CAAgB,0BAA0BW,KAA1B,GAAkC,kBAAlD,CAAN;IACD;EACF,CAPD,EAhEuC,CAyEvC;;EACA,IAAIE,CAAC,GAAGzD,QAAQ,CAAC0D,SAAT,CAAmBlD,GAAG,CAACmD,IAAvB,CAAR;EACA,KAAKC,OAAL,GAAeH,CAAC,GAAGA,CAAC,CAACI,WAAF,EAAH,GAAqB,IAArC,CA3EuC,CA6EvC;;EACAnB,SAAS,CAACoB,GAAV,CAAc,UAAd,EAA0BpB,SAAS,CAACqB,QAAV,CAAmBC,OAAnB,CAA2B,MAA3B,EAAmC,EAAnC,CAA1B,EA9EuC,CAgFvC;;EACA,KAAK9C,GAAL,GAAWwB,SAAS,CAACiB,IAArB;EACA/C,KAAK,CAAC,WAAD,EAAc,KAAKM,GAAnB,CAAL,CAlFuC,CAoFvC;EACA;EACA;;EACA,KAAK+C,QAAL,GAAgB;IACdC,UAAU,EAAE,CAAC9D,OAAO,CAAC+D,SAAR,EADC;IAEdC,UAAU,EAAEpE,QAAQ,CAACqE,aAAT,CAAuB,KAAKnD,GAA5B,EAAiCV,GAAG,CAACmD,IAArC,CAFE;IAGdW,UAAU,EAAEtE,QAAQ,CAACuE,aAAT,CAAuB,KAAKrD,GAA5B,EAAiCV,GAAG,CAACmD,IAArC;EAHE,CAAhB;EAMA,KAAKa,GAAL,GAAW,IAAI7D,YAAJ,CAAiB,KAAKO,GAAtB,EAA2B,KAAK+C,QAAhC,CAAX;;EACA,KAAKO,GAAL,CAASC,IAAT,CAAc,QAAd,EAAwB,KAAKC,YAAL,CAAkBC,IAAlB,CAAuB,IAAvB,CAAxB;AACD;;AAED9E,QAAQ,CAACoB,MAAD,EAASV,WAAT,CAAR;;AAEA,SAASqE,WAAT,CAAqBC,IAArB,EAA2B;EACzB,OAAOA,IAAI,KAAK,IAAT,IAAkBA,IAAI,IAAI,IAAR,IAAgBA,IAAI,IAAI,IAAjD;AACD;;AAED5D,MAAM,CAAC6D,SAAP,CAAiBC,KAAjB,GAAyB,UAASF,IAAT,EAAeG,MAAf,EAAuB;EAC9C;EACA,IAAIH,IAAI,IAAI,CAACD,WAAW,CAACC,IAAD,CAAxB,EAAgC;IAC9B,MAAM,IAAI5B,KAAJ,CAAU,kCAAV,CAAN;EACD,CAJ6C,CAK9C;;;EACA,IAAI+B,MAAM,IAAIA,MAAM,CAAC1D,MAAP,GAAgB,GAA9B,EAAmC;IACjC,MAAM,IAAIsB,WAAJ,CAAgB,uCAAhB,CAAN;EACD,CAR6C,CAU9C;;;EACA,IAAI,KAAKnB,UAAL,KAAoBR,MAAM,CAACgE,OAA3B,IAAsC,KAAKxD,UAAL,KAAoBR,MAAM,CAACiE,MAArE,EAA6E;IAC3E;EACD,CAb6C,CAe9C;;;EACA,IAAIC,QAAQ,GAAG,IAAf;;EACA,KAAKC,MAAL,CAAYP,IAAI,IAAI,IAApB,EAA0BG,MAAM,IAAI,gBAApC,EAAsDG,QAAtD;AACD,CAlBD;;AAoBAlE,MAAM,CAAC6D,SAAP,CAAiBO,IAAjB,GAAwB,UAASC,IAAT,EAAe;EACrC;EACA;EACA,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;IAC5BA,IAAI,GAAG,KAAKA,IAAZ;EACD;;EACD,IAAI,KAAK7D,UAAL,KAAoBR,MAAM,CAACS,UAA/B,EAA2C;IACzC,MAAM,IAAIuB,KAAJ,CAAU,gEAAV,CAAN;EACD;;EACD,IAAI,KAAKxB,UAAL,KAAoBR,MAAM,CAACsE,IAA/B,EAAqC;IACnC;EACD;;EACD,KAAKC,UAAL,CAAgBH,IAAhB,CAAqBtF,MAAM,CAAC0F,KAAP,CAAaH,IAAb,CAArB;AACD,CAbD;;AAeArE,MAAM,CAACyE,OAAP,GAAiB/F,OAAO,CAAC,WAAD,CAAxB;AAEAsB,MAAM,CAACS,UAAP,GAAoB,CAApB;AACAT,MAAM,CAACsE,IAAP,GAAc,CAAd;AACAtE,MAAM,CAACgE,OAAP,GAAiB,CAAjB;AACAhE,MAAM,CAACiE,MAAP,GAAgB,CAAhB;;AAEAjE,MAAM,CAAC6D,SAAP,CAAiBJ,YAAjB,GAAgC,UAASiB,IAAT,EAAeC,GAAf,EAAoB;EAClDhF,KAAK,CAAC,cAAD,EAAiBgF,GAAjB,CAAL;EACA,KAAKpB,GAAL,GAAW,IAAX;;EACA,IAAI,CAACmB,IAAL,EAAW;IACT,KAAKP,MAAL,CAAY,IAAZ,EAAkB,0BAAlB;;IACA;EACD,CANiD,CAQlD;EACA;;;EACA,KAAKS,IAAL,GAAY,KAAKC,QAAL,CAAcF,GAAd,CAAZ,CAVkD,CAWlD;;EACA,KAAKG,SAAL,GAAiBJ,IAAI,CAACK,QAAL,GAAgBL,IAAI,CAACK,QAArB,GAAgC,KAAK9E,GAAtD;EACAyE,IAAI,GAAGxF,WAAW,CAAC8F,MAAZ,CAAmBN,IAAnB,EAAyB,KAAK1B,QAA9B,CAAP;EACArD,KAAK,CAAC,MAAD,EAAS+E,IAAT,CAAL,CAdkD,CAelD;;EACA,IAAIO,iBAAiB,GAAGlF,UAAU,CAACmF,eAAX,CAA2B,KAAKpE,oBAAhC,EAAsD4D,IAAtD,CAAxB;EACA,KAAKS,WAAL,GAAmBF,iBAAiB,CAACG,IAArC;EACAzF,KAAK,CAAC,KAAKwF,WAAL,CAAiB9E,MAAjB,GAA0B,qBAA3B,CAAL;;EAEA,KAAKgF,QAAL;AACD,CArBD;;AAuBArF,MAAM,CAAC6D,SAAP,CAAiBwB,QAAjB,GAA4B,YAAW;EACrC,KAAK,IAAIC,SAAS,GAAG,KAAKH,WAAL,CAAiBI,KAAjB,EAArB,EAA+CD,SAA/C,EAA0DA,SAAS,GAAG,KAAKH,WAAL,CAAiBI,KAAjB,EAAtE,EAAgG;IAC9F5F,KAAK,CAAC,SAAD,EAAY2F,SAAS,CAACE,aAAtB,CAAL;;IACA,IAAIF,SAAS,CAACG,QAAd,EAAwB;MACtB,IAAI,CAACC,MAAM,CAACC,QAAP,CAAgBC,IAAjB,IACC,OAAOF,MAAM,CAACC,QAAP,CAAgBnF,UAAvB,KAAsC,WAAtC,IACCkF,MAAM,CAACC,QAAP,CAAgBnF,UAAhB,KAA+B,UADhC,IAECkF,MAAM,CAACC,QAAP,CAAgBnF,UAAhB,KAA+B,aAHrC,EAGqD;QACnDb,KAAK,CAAC,kBAAD,CAAL;;QACA,KAAKwF,WAAL,CAAiBU,OAAjB,CAAyBP,SAAzB;;QACAtG,UAAU,CAAC8G,WAAX,CAAuB,MAAvB,EAA+B,KAAKT,QAAL,CAAc3B,IAAd,CAAmB,IAAnB,CAA/B;QACA;MACD;IACF,CAZ6F,CAc9F;;;IACA,IAAIqC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKhF,QAAd,EAAyB,KAAK2D,IAAL,GAAYU,SAAS,CAACY,UAAvB,IAAsC,IAA9D,CAAhB;IACA,KAAKC,mBAAL,GAA2BC,UAAU,CAAC,KAAKC,iBAAL,CAAuB3C,IAAvB,CAA4B,IAA5B,CAAD,EAAoCqC,SAApC,CAArC;IACApG,KAAK,CAAC,eAAD,EAAkBoG,SAAlB,CAAL;IAEA,IAAIO,YAAY,GAAGvH,QAAQ,CAACwH,OAAT,CAAiB,KAAKzB,SAAtB,EAAiC,MAAM,KAAKxD,OAAX,GAAqB,GAArB,GAA2B,KAAKF,kBAAL,EAA5D,CAAnB;IACA,IAAIjB,OAAO,GAAG,KAAKY,iBAAL,CAAuBuE,SAAS,CAACE,aAAjC,CAAd;IACA7F,KAAK,CAAC,eAAD,EAAkB2G,YAAlB,CAAL;IACA,IAAIE,YAAY,GAAG,IAAIlB,SAAJ,CAAcgB,YAAd,EAA4B,KAAKxB,SAAjC,EAA4C3E,OAA5C,CAAnB;IACAqG,YAAY,CAACC,EAAb,CAAgB,SAAhB,EAA2B,KAAKC,iBAAL,CAAuBhD,IAAvB,CAA4B,IAA5B,CAA3B;IACA8C,YAAY,CAAChD,IAAb,CAAkB,OAAlB,EAA2B,KAAKmD,eAAL,CAAqBjD,IAArB,CAA0B,IAA1B,CAA3B;IACA8C,YAAY,CAAChB,aAAb,GAA6BF,SAAS,CAACE,aAAvC;IACA,KAAKjB,UAAL,GAAkBiC,YAAlB;IAEA;EACD;;EACD,KAAKrC,MAAL,CAAY,IAAZ,EAAkB,uBAAlB,EAA2C,KAA3C;AACD,CAhCD;;AAkCAnE,MAAM,CAAC6D,SAAP,CAAiBwC,iBAAjB,GAAqC,YAAW;EAC9C1G,KAAK,CAAC,mBAAD,CAAL;;EACA,IAAI,KAAKa,UAAL,KAAoBR,MAAM,CAACS,UAA/B,EAA2C;IACzC,IAAI,KAAK8D,UAAT,EAAqB;MACnB,KAAKA,UAAL,CAAgBT,KAAhB;IACD;;IAED,KAAK6C,eAAL,CAAqB,IAArB,EAA2B,qBAA3B;EACD;AACF,CATD;;AAWA3G,MAAM,CAAC6D,SAAP,CAAiB6C,iBAAjB,GAAqC,UAASE,GAAT,EAAc;EACjDjH,KAAK,CAAC,mBAAD,EAAsBiH,GAAtB,CAAL;EACA,IAAIC,IAAI,GAAG,IAAX;EAAA,IACIC,IAAI,GAAGF,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAb,CADX;EAAA,IAEIC,OAAO,GAAGJ,GAAG,CAACG,KAAJ,CAAU,CAAV,CAFd;EAAA,IAGIE,OAHJ,CAFiD,CAQjD;;EACA,QAAQH,IAAR;IACE,KAAK,GAAL;MACE,KAAKI,KAAL;;MACA;;IACF,KAAK,GAAL;MACE,KAAKC,aAAL,CAAmB,IAAI9H,KAAJ,CAAU,WAAV,CAAnB;MACAM,KAAK,CAAC,WAAD,EAAc,KAAKV,SAAnB,CAAL;MACA;EAPJ;;EAUA,IAAI+H,OAAJ,EAAa;IACX,IAAI;MACFC,OAAO,GAAGG,IAAI,CAACC,KAAL,CAAWL,OAAX,CAAV;IACD,CAFD,CAEE,OAAOM,CAAP,EAAU;MACV3H,KAAK,CAAC,UAAD,EAAaqH,OAAb,CAAL;IACD;EACF;;EAED,IAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAoC;IAClCtH,KAAK,CAAC,eAAD,EAAkBqH,OAAlB,CAAL;IACA;EACD;;EAED,QAAQF,IAAR;IACE,KAAK,GAAL;MACE,IAAI7E,KAAK,CAACC,OAAN,CAAc+E,OAAd,CAAJ,EAA4B;QAC1BA,OAAO,CAAC5E,OAAR,CAAgB,UAASkF,CAAT,EAAY;UAC1B5H,KAAK,CAAC,SAAD,EAAYkH,IAAI,CAAC5H,SAAjB,EAA4BsI,CAA5B,CAAL;UACAV,IAAI,CAACM,aAAL,CAAmB,IAAI1H,qBAAJ,CAA0B8H,CAA1B,CAAnB;QACD,CAHD;MAID;;MACD;;IACF,KAAK,GAAL;MACE5H,KAAK,CAAC,SAAD,EAAY,KAAKV,SAAjB,EAA4BgI,OAA5B,CAAL;MACA,KAAKE,aAAL,CAAmB,IAAI1H,qBAAJ,CAA0BwH,OAA1B,CAAnB;MACA;;IACF,KAAK,GAAL;MACE,IAAIhF,KAAK,CAACC,OAAN,CAAc+E,OAAd,KAA0BA,OAAO,CAAC5G,MAAR,KAAmB,CAAjD,EAAoD;QAClD,KAAK8D,MAAL,CAAY8C,OAAO,CAAC,CAAD,CAAnB,EAAwBA,OAAO,CAAC,CAAD,CAA/B,EAAoC,IAApC;MACD;;MACD;EAjBJ;AAmBD,CAnDD;;AAqDAjH,MAAM,CAAC6D,SAAP,CAAiB8C,eAAjB,GAAmC,UAAS/C,IAAT,EAAeG,MAAf,EAAuB;EACxDpE,KAAK,CAAC,iBAAD,EAAoB,KAAKV,SAAzB,EAAoC2E,IAApC,EAA0CG,MAA1C,CAAL;;EACA,IAAI,KAAKQ,UAAT,EAAqB;IACnB,KAAKA,UAAL,CAAgBiD,kBAAhB;;IACA,KAAKjD,UAAL,GAAkB,IAAlB;IACA,KAAKtF,SAAL,GAAiB,IAAjB;EACD;;EAED,IAAI,CAAC0E,WAAW,CAACC,IAAD,CAAZ,IAAsBA,IAAI,KAAK,IAA/B,IAAuC,KAAKpD,UAAL,KAAoBR,MAAM,CAACS,UAAtE,EAAkF;IAChF,KAAK4E,QAAL;;IACA;EACD;;EAED,KAAKlB,MAAL,CAAYP,IAAZ,EAAkBG,MAAlB;AACD,CAdD;;AAgBA/D,MAAM,CAAC6D,SAAP,CAAiBqD,KAAjB,GAAyB,YAAW;EAClCvH,KAAK,CAAC,OAAD,EAAU,KAAK4E,UAAL,IAAmB,KAAKA,UAAL,CAAgBiB,aAA7C,EAA4D,KAAKhF,UAAjE,CAAL;;EACA,IAAI,KAAKA,UAAL,KAAoBR,MAAM,CAACS,UAA/B,EAA2C;IACzC,IAAI,KAAK0F,mBAAT,EAA8B;MAC5BsB,YAAY,CAAC,KAAKtB,mBAAN,CAAZ;MACA,KAAKA,mBAAL,GAA2B,IAA3B;IACD;;IACD,KAAK3F,UAAL,GAAkBR,MAAM,CAACsE,IAAzB;IACA,KAAKrF,SAAL,GAAiB,KAAKsF,UAAL,CAAgBiB,aAAjC;IACA,KAAK2B,aAAL,CAAmB,IAAI9H,KAAJ,CAAU,MAAV,CAAnB;IACAM,KAAK,CAAC,WAAD,EAAc,KAAKV,SAAnB,CAAL;EACD,CATD,MASO;IACL;IACA;IACA,KAAKkF,MAAL,CAAY,IAAZ,EAAkB,qBAAlB;EACD;AACF,CAhBD;;AAkBAnE,MAAM,CAAC6D,SAAP,CAAiBM,MAAjB,GAA0B,UAASP,IAAT,EAAeG,MAAf,EAAuBG,QAAvB,EAAiC;EACzDvE,KAAK,CAAC,QAAD,EAAW,KAAKV,SAAhB,EAA2B2E,IAA3B,EAAiCG,MAAjC,EAAyCG,QAAzC,EAAmD,KAAK1D,UAAxD,CAAL;EACA,IAAIkH,SAAS,GAAG,KAAhB;;EAEA,IAAI,KAAKnE,GAAT,EAAc;IACZmE,SAAS,GAAG,IAAZ;;IACA,KAAKnE,GAAL,CAASO,KAAT;;IACA,KAAKP,GAAL,GAAW,IAAX;EACD;;EACD,IAAI,KAAKgB,UAAT,EAAqB;IACnB,KAAKA,UAAL,CAAgBT,KAAhB;;IACA,KAAKS,UAAL,GAAkB,IAAlB;IACA,KAAKtF,SAAL,GAAiB,IAAjB;EACD;;EAED,IAAI,KAAKuB,UAAL,KAAoBR,MAAM,CAACiE,MAA/B,EAAuC;IACrC,MAAM,IAAIjC,KAAJ,CAAU,mDAAV,CAAN;EACD;;EAED,KAAKxB,UAAL,GAAkBR,MAAM,CAACgE,OAAzB;EACAoC,UAAU,CAAC,YAAW;IACpB,KAAK5F,UAAL,GAAkBR,MAAM,CAACiE,MAAzB;;IAEA,IAAIyD,SAAJ,EAAe;MACb,KAAKP,aAAL,CAAmB,IAAI9H,KAAJ,CAAU,OAAV,CAAnB;IACD;;IAED,IAAIiI,CAAC,GAAG,IAAI9H,UAAJ,CAAe,OAAf,CAAR;IACA8H,CAAC,CAACpD,QAAF,GAAaA,QAAQ,IAAI,KAAzB;IACAoD,CAAC,CAAC1D,IAAF,GAASA,IAAI,IAAI,IAAjB;IACA0D,CAAC,CAACvD,MAAF,GAAWA,MAAX;IAEA,KAAKoD,aAAL,CAAmBG,CAAnB;IACA,KAAKK,SAAL,GAAiB,KAAKC,OAAL,GAAe,KAAKC,OAAL,GAAe,IAA/C;IACAlI,KAAK,CAAC,cAAD,CAAL;EACD,CAfU,CAeT+D,IAfS,CAeJ,IAfI,CAAD,EAeI,CAfJ,CAAV;AAgBD,CApCD,C,CAsCA;AACA;;;AACA1D,MAAM,CAAC6D,SAAP,CAAiBgB,QAAjB,GAA4B,UAASF,GAAT,EAAc;EACxC;EACA;EACA;EACA;EACA;EACA;EACA,IAAIA,GAAG,GAAG,GAAV,EAAe;IACb,OAAO,IAAIA,GAAX,CADa,CACG;EACjB;;EACD,OAAO,MAAMA,GAAb,CAVwC,CAUtB;AACnB,CAXD;;AAaAmD,MAAM,CAACC,OAAP,GAAiB,UAASC,mBAAT,EAA8B;EAC7CjI,UAAU,GAAGd,SAAS,CAAC+I,mBAAD,CAAtB;;EACAtJ,OAAO,CAAC,oBAAD,CAAP,CAA8BsB,MAA9B,EAAsCgI,mBAAtC;;EACA,OAAOhI,MAAP;AACD,CAJD"}, "metadata": {}, "sourceType": "script"}