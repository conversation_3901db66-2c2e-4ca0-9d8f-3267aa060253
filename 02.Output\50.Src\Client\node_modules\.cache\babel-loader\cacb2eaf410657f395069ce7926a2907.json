{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect'; // support refreshDeps & ready\n\nvar useAutoRunPlugin = function useAutoRunPlugin(fetchInstance, _a) {\n  var manual = _a.manual,\n      _b = _a.ready,\n      ready = _b === void 0 ? true : _b,\n      _c = _a.defaultParams,\n      defaultParams = _c === void 0 ? [] : _c,\n      _d = _a.refreshDeps,\n      refreshDeps = _d === void 0 ? [] : _d,\n      refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n\n    if (!manual) {\n      hasAutoRun.current = true;\n\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function onBefore() {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\n\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n      ready = _b === void 0 ? true : _b,\n      manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\n\nexport default useAutoRunPlugin;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useRef", "useUpdateEffect", "useAutoRunPlugin", "fetchInstance", "_a", "manual", "_b", "ready", "_c", "defaultParams", "_d", "refreshDeps", "refreshDepsAction", "hasAutoRun", "current", "run", "apply", "refresh", "onBefore", "stopNow", "onInit", "loading"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\n// support refreshDeps & ready\nvar useAutoRunPlugin = function useAutoRunPlugin(fetchInstance, _a) {\n  var manual = _a.manual,\n    _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    _c = _a.defaultParams,\n    defaultParams = _c === void 0 ? [] : _c,\n    _d = _a.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    refreshDepsAction = _a.refreshDepsAction;\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      if (refreshDepsAction) {\n        refreshDepsAction();\n      } else {\n        fetchInstance.refresh();\n      }\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return {\n    onBefore: function onBefore() {\n      if (!ready) {\n        return {\n          stopNow: true\n        };\n      }\n    }\n  };\n};\nuseAutoRunPlugin.onInit = function (_a) {\n  var _b = _a.ready,\n    ready = _b === void 0 ? true : _b,\n    manual = _a.manual;\n  return {\n    loading: !manual && ready\n  };\n};\nexport default useAutoRunPlugin;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASS,MAAT,QAAuB,OAAvB;AACA,OAAOC,eAAP,MAA4B,0BAA5B,C,CACA;;AACA,IAAIC,gBAAgB,GAAG,SAASA,gBAAT,CAA0BC,aAA1B,EAAyCC,EAAzC,EAA6C;EAClE,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAhB;EAAA,IACEC,EAAE,GAAGF,EAAE,CAACG,KADV;EAAA,IAEEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EAFjC;EAAA,IAGEE,EAAE,GAAGJ,EAAE,CAACK,aAHV;EAAA,IAIEA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EAJvC;EAAA,IAKEE,EAAE,GAAGN,EAAE,CAACO,WALV;EAAA,IAMEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EANrC;EAAA,IAOEE,iBAAiB,GAAGR,EAAE,CAACQ,iBAPzB;EAQA,IAAIC,UAAU,GAAGb,MAAM,CAAC,KAAD,CAAvB;EACAa,UAAU,CAACC,OAAX,GAAqB,KAArB;EACAb,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACI,MAAD,IAAWE,KAAf,EAAsB;MACpBM,UAAU,CAACC,OAAX,GAAqB,IAArB;MACAX,aAAa,CAACY,GAAd,CAAkBC,KAAlB,CAAwBb,aAAxB,EAAuCd,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACoC,aAAD,CAAX,EAA4B,KAA5B,CAApD;IACD;EACF,CALc,EAKZ,CAACF,KAAD,CALY,CAAf;EAMAN,eAAe,CAAC,YAAY;IAC1B,IAAIY,UAAU,CAACC,OAAf,EAAwB;MACtB;IACD;;IACD,IAAI,CAACT,MAAL,EAAa;MACXQ,UAAU,CAACC,OAAX,GAAqB,IAArB;;MACA,IAAIF,iBAAJ,EAAuB;QACrBA,iBAAiB;MAClB,CAFD,MAEO;QACLT,aAAa,CAACc,OAAd;MACD;IACF;EACF,CAZc,EAYZ5B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsC,WAAD,CAAX,EAA0B,KAA1B,CAZD,CAAf;EAaA,OAAO;IACLO,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5B,IAAI,CAACX,KAAL,EAAY;QACV,OAAO;UACLY,OAAO,EAAE;QADJ,CAAP;MAGD;IACF;EAPI,CAAP;AASD,CAvCD;;AAwCAjB,gBAAgB,CAACkB,MAAjB,GAA0B,UAAUhB,EAAV,EAAc;EACtC,IAAIE,EAAE,GAAGF,EAAE,CAACG,KAAZ;EAAA,IACEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EADjC;EAAA,IAEED,MAAM,GAAGD,EAAE,CAACC,MAFd;EAGA,OAAO;IACLgB,OAAO,EAAE,CAAChB,MAAD,IAAWE;EADf,CAAP;AAGD,CAPD;;AAQA,eAAeL,gBAAf"}, "metadata": {}, "sourceType": "module"}