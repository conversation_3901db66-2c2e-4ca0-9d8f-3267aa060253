{"ast": null, "code": "// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\n\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\n\nif (isBrowser) {\n  var revalidate = function revalidate() {\n    if (!isDocumentVisible() || !isOnline()) return;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\n\nexport default subscribe;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "isOnline", "listeners", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice", "revalidate", "i", "length", "window", "addEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js"], "sourcesContent": ["// from swr\nimport isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nimport isOnline from './isOnline';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    if (index > -1) {\n      listeners.splice(index, 1);\n    }\n  };\n}\nif (isBrowser) {\n  var revalidate = function revalidate() {\n    if (!isDocumentVisible() || !isOnline()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n  window.addEventListener('focus', revalidate, false);\n}\nexport default subscribe;"], "mappings": "AAAA;AACA,OAAOA,SAAP,MAAsB,0BAAtB;AACA,OAAOC,iBAAP,MAA8B,qBAA9B;AACA,OAAOC,QAAP,MAAqB,YAArB;AACA,IAAIC,SAAS,GAAG,EAAhB;;AACA,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAC3BF,SAAS,CAACG,IAAV,CAAeD,QAAf;EACA,OAAO,SAASE,WAAT,GAAuB;IAC5B,IAAIC,KAAK,GAAGL,SAAS,CAACM,OAAV,CAAkBJ,QAAlB,CAAZ;;IACA,IAAIG,KAAK,GAAG,CAAC,CAAb,EAAgB;MACdL,SAAS,CAACO,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;IACD;EACF,CALD;AAMD;;AACD,IAAIR,SAAJ,EAAe;EACb,IAAIW,UAAU,GAAG,SAASA,UAAT,GAAsB;IACrC,IAAI,CAACV,iBAAiB,EAAlB,IAAwB,CAACC,QAAQ,EAArC,EAAyC;;IACzC,KAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,SAAS,CAACU,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzC,IAAIP,QAAQ,GAAGF,SAAS,CAACS,CAAD,CAAxB;MACAP,QAAQ;IACT;EACF,CAND;;EAOAS,MAAM,CAACC,gBAAP,CAAwB,kBAAxB,EAA4CJ,UAA5C,EAAwD,KAAxD;EACAG,MAAM,CAACC,gBAAP,CAAwB,OAAxB,EAAiCJ,UAAjC,EAA6C,KAA7C;AACD;;AACD,eAAeP,SAAf"}, "metadata": {}, "sourceType": "module"}