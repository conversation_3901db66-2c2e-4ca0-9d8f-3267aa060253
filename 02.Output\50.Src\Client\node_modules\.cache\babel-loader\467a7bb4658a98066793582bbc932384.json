{"ast": null, "code": "var assocIndexOf = require('./_assocIndexOf');\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\n\n\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n\n  return this;\n}\n\nmodule.exports = listCacheSet;", "map": {"version": 3, "names": ["assocIndexOf", "require", "listCacheSet", "key", "value", "data", "__data__", "index", "size", "push", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_listCacheSet.js"], "sourcesContent": ["var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\nmodule.exports = listCacheSet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,YAAT,CAAsBC,GAAtB,EAA2BC,KAA3B,EAAkC;EAChC,IAAIC,IAAI,GAAG,KAAKC,QAAhB;EAAA,IACIC,KAAK,GAAGP,YAAY,CAACK,IAAD,EAAOF,GAAP,CADxB;;EAGA,IAAII,KAAK,GAAG,CAAZ,EAAe;IACb,EAAE,KAAKC,IAAP;IACAH,IAAI,CAACI,IAAL,CAAU,CAACN,GAAD,EAAMC,KAAN,CAAV;EACD,CAHD,MAGO;IACLC,IAAI,CAACE,KAAD,CAAJ,CAAY,CAAZ,IAAiBH,KAAjB;EACD;;EACD,OAAO,IAAP;AACD;;AAEDM,MAAM,CAACC,OAAP,GAAiBT,YAAjB"}, "metadata": {}, "sourceType": "script"}