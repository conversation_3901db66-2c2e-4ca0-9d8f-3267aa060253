{"ast": null, "code": "import { useRef } from 'react';\n\nvar useLoadingDelayPlugin = function useLoadingDelayPlugin(fetchInstance, _a) {\n  var loadingDelay = _a.loadingDelay;\n  var timerRef = useRef();\n\n  if (!loadingDelay) {\n    return {};\n  }\n\n  var cancelTimeout = function cancelTimeout() {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  };\n\n  return {\n    onBefore: function onBefore() {\n      cancelTimeout();\n      timerRef.current = setTimeout(function () {\n        fetchInstance.setState({\n          loading: true\n        });\n      }, loadingDelay);\n      return {\n        loading: false\n      };\n    },\n    onFinally: function onFinally() {\n      cancelTimeout();\n    },\n    onCancel: function onCancel() {\n      cancelTimeout();\n    }\n  };\n};\n\nexport default useLoadingDelayPlugin;", "map": {"version": 3, "names": ["useRef", "useLoadingDelayPlugin", "fetchInstance", "_a", "loadingDelay", "timerRef", "cancelTimeout", "current", "clearTimeout", "onBefore", "setTimeout", "setState", "loading", "onFinally", "onCancel"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nvar useLoadingDelayPlugin = function useLoadingDelayPlugin(fetchInstance, _a) {\n  var loadingDelay = _a.loadingDelay;\n  var timerRef = useRef();\n  if (!loadingDelay) {\n    return {};\n  }\n  var cancelTimeout = function cancelTimeout() {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  };\n  return {\n    onBefore: function onBefore() {\n      cancelTimeout();\n      timerRef.current = setTimeout(function () {\n        fetchInstance.setState({\n          loading: true\n        });\n      }, loadingDelay);\n      return {\n        loading: false\n      };\n    },\n    onFinally: function onFinally() {\n      cancelTimeout();\n    },\n    onCancel: function onCancel() {\n      cancelTimeout();\n    }\n  };\n};\nexport default useLoadingDelayPlugin;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;;AACA,IAAIC,qBAAqB,GAAG,SAASA,qBAAT,CAA+BC,aAA/B,EAA8CC,EAA9C,EAAkD;EAC5E,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAtB;EACA,IAAIC,QAAQ,GAAGL,MAAM,EAArB;;EACA,IAAI,CAACI,YAAL,EAAmB;IACjB,OAAO,EAAP;EACD;;EACD,IAAIE,aAAa,GAAG,SAASA,aAAT,GAAyB;IAC3C,IAAID,QAAQ,CAACE,OAAb,EAAsB;MACpBC,YAAY,CAACH,QAAQ,CAACE,OAAV,CAAZ;IACD;EACF,CAJD;;EAKA,OAAO;IACLE,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5BH,aAAa;MACbD,QAAQ,CAACE,OAAT,GAAmBG,UAAU,CAAC,YAAY;QACxCR,aAAa,CAACS,QAAd,CAAuB;UACrBC,OAAO,EAAE;QADY,CAAvB;MAGD,CAJ4B,EAI1BR,YAJ0B,CAA7B;MAKA,OAAO;QACLQ,OAAO,EAAE;MADJ,CAAP;IAGD,CAXI;IAYLC,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9BP,aAAa;IACd,CAdI;IAeLQ,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5BR,aAAa;IACd;EAjBI,CAAP;AAmBD,CA9BD;;AA+BA,eAAeL,qBAAf"}, "metadata": {}, "sourceType": "module"}