{"ast": null, "code": "'use strict';\n\nimport AxiosError from './AxiosError.js';\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\n\nexport default function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError('Request failed with status code ' + response.status, [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4], response.config, response.request, response));\n  }\n}", "map": {"version": 3, "names": ["AxiosError", "settle", "resolve", "reject", "response", "validateStatus", "config", "status", "ERR_BAD_REQUEST", "ERR_BAD_RESPONSE", "Math", "floor", "request"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/settle.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from './AxiosError.js';\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\nexport default function settle(resolve, reject, response) {\n  const validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError(\n      'Request failed with status code ' + response.status,\n      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],\n      response.config,\n      response.request,\n      response\n    ));\n  }\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,UAAP,MAAuB,iBAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,MAAT,CAAgBC,OAAhB,EAAyBC,MAAzB,EAAiCC,QAAjC,EAA2C;EACxD,IAAMC,cAAc,GAAGD,QAAQ,CAACE,MAAT,CAAgBD,cAAvC;;EACA,IAAI,CAACD,QAAQ,CAACG,MAAV,IAAoB,CAACF,cAArB,IAAuCA,cAAc,CAACD,QAAQ,CAACG,MAAV,CAAzD,EAA4E;IAC1EL,OAAO,CAACE,QAAD,CAAP;EACD,CAFD,MAEO;IACLD,MAAM,CAAC,IAAIH,UAAJ,CACL,qCAAqCI,QAAQ,CAACG,MADzC,EAEL,CAACP,UAAU,CAACQ,eAAZ,EAA6BR,UAAU,CAACS,gBAAxC,EAA0DC,IAAI,CAACC,KAAL,CAAWP,QAAQ,CAACG,MAAT,GAAkB,GAA7B,IAAoC,CAA9F,CAFK,EAGLH,QAAQ,CAACE,MAHJ,EAILF,QAAQ,CAACQ,OAJJ,EAKLR,QALK,CAAD,CAAN;EAOD;AACF"}, "metadata": {}, "sourceType": "module"}