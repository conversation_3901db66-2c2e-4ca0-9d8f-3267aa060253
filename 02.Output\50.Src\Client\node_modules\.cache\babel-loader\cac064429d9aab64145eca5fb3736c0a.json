{"ast": null, "code": "/* eslint-env browser */\nmodule.exports = typeof self == 'object' ? self.FormData : window.FormData;", "map": {"version": 3, "names": ["module", "exports", "self", "FormData", "window"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/node_modules/form-data/lib/browser.js"], "sourcesContent": ["/* eslint-env browser */\nmodule.exports = typeof self == 'object' ? self.FormData : window.FormData;\n"], "mappings": "AAAA;AACAA,MAAM,CAACC,OAAP,GAAiB,OAAOC,IAAP,IAAe,QAAf,GAA0BA,IAAI,CAACC,QAA/B,GAA0CC,MAAM,CAACD,QAAlE"}, "metadata": {}, "sourceType": "script"}