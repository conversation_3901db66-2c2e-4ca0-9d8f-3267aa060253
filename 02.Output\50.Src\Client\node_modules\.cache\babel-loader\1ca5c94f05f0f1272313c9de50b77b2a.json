{"ast": null, "code": "var baseIsArguments = require('./_baseIsArguments'),\n    isObjectLike = require('./isObjectLike');\n/** Used for built-in method references. */\n\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/** Built-in value references. */\n\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\n\nvar isArguments = baseIsArguments(function () {\n  return arguments;\n}()) ? baseIsArguments : function (value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') && !propertyIsEnumerable.call(value, 'callee');\n};\nmodule.exports = isArguments;", "map": {"version": 3, "names": ["baseIsArguments", "require", "isObjectLike", "objectProto", "Object", "prototype", "hasOwnProperty", "propertyIsEnumerable", "isArguments", "arguments", "value", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/isArguments.js"], "sourcesContent": ["var baseIsArguments = require('./_baseIsArguments'),\n    isObjectLike = require('./isObjectLike');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\nmodule.exports = isArguments;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAD,CAA7B;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,gBAAD,CAD1B;AAGA;;;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;;AACA,IAAIC,oBAAoB,GAAGJ,WAAW,CAACI,oBAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,WAAW,GAAGR,eAAe,CAAC,YAAW;EAAE,OAAOS,SAAP;AAAmB,CAAhC,EAAD,CAAf,GAAsDT,eAAtD,GAAwE,UAASU,KAAT,EAAgB;EACxG,OAAOR,YAAY,CAACQ,KAAD,CAAZ,IAAuBJ,cAAc,CAACK,IAAf,CAAoBD,KAApB,EAA2B,QAA3B,CAAvB,IACL,CAACH,oBAAoB,CAACI,IAArB,CAA0BD,KAA1B,EAAiC,QAAjC,CADH;AAED,CAHD;AAKAE,MAAM,CAACC,OAAP,GAAiBL,WAAjB"}, "metadata": {}, "sourceType": "script"}