{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n    label: 0,\n    sent: function sent() {\n      if (t[0] & 1) throw t[1];\n      return t[1];\n    },\n    trys: [],\n    ops: []\n  },\n      f,\n      y,\n      t,\n      g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n\n          case 7:\n            op = _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n\n              _.ops.push(op);\n\n              break;\n            }\n\n            if (t[2]) _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n        }\n\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useMemo, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\n\nvar useInfiniteScroll = function useInfiniteScroll(service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var target = options.target,\n      isNoMore = options.isNoMore,\n      _a = options.threshold,\n      threshold = _a === void 0 ? 100 : _a,\n      _b = options.reloadDeps,\n      reloadDeps = _b === void 0 ? [] : _b,\n      manual = options.manual,\n      _onBefore = options.onBefore,\n      _onSuccess = options.onSuccess,\n      _onError = options.onError,\n      _onFinally = options.onFinally;\n\n  var _c = __read(useState(), 2),\n      finalData = _c[0],\n      setFinalData = _c[1];\n\n  var _d = __read(useState(false), 2),\n      loadingMore = _d[0],\n      setLoadingMore = _d[1];\n\n  var noMore = useMemo(function () {\n    if (!isNoMore) return false;\n    return isNoMore(finalData);\n  }, [finalData]);\n\n  var _e = useRequest(function (lastData) {\n    return __awaiter(void 0, void 0, void 0, function () {\n      var currentData;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4\n            /*yield*/\n            , service(lastData)];\n\n          case 1:\n            currentData = _a.sent();\n\n            if (!lastData) {\n              setFinalData(currentData);\n            } else {\n              setFinalData(__assign(__assign({}, currentData), {\n                // @ts-ignore\n                list: __spreadArray(__spreadArray([], __read(lastData.list), false), __read(currentData.list), false)\n              }));\n            }\n\n            return [2\n            /*return*/\n            , currentData];\n        }\n      });\n    });\n  }, {\n    manual: manual,\n    onFinally: function onFinally(_, d, e) {\n      setLoadingMore(false);\n      _onFinally === null || _onFinally === void 0 ? void 0 : _onFinally(d, e);\n    },\n    onBefore: function onBefore() {\n      return _onBefore === null || _onBefore === void 0 ? void 0 : _onBefore();\n    },\n    onSuccess: function onSuccess(d) {\n      setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        scrollMethod();\n      });\n      _onSuccess === null || _onSuccess === void 0 ? void 0 : _onSuccess(d);\n    },\n    onError: function onError(e) {\n      return _onError === null || _onError === void 0 ? void 0 : _onError(e);\n    }\n  }),\n      loading = _e.loading,\n      run = _e.run,\n      runAsync = _e.runAsync,\n      cancel = _e.cancel;\n\n  var loadMore = function loadMore() {\n    if (noMore) return;\n    setLoadingMore(true);\n    run(finalData);\n  };\n\n  var loadMoreAsync = function loadMoreAsync() {\n    if (noMore) return Promise.reject();\n    setLoadingMore(true);\n    return runAsync(finalData);\n  };\n\n  var reload = function reload() {\n    return run();\n  };\n\n  var reloadAsync = function reloadAsync() {\n    return runAsync();\n  };\n\n  var scrollMethod = function scrollMethod() {\n    var el = getTargetElement(target);\n\n    if (!el) {\n      return;\n    }\n\n    var scrollTop = getScrollTop(el);\n    var scrollHeight = getScrollHeight(el);\n    var clientHeight = getClientHeight(el);\n\n    if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: useMemoizedFn(loadMore),\n    loadMoreAsync: useMemoizedFn(loadMoreAsync),\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\n\nexport default useInfiniteScroll;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "__generator", "body", "_", "label", "sent", "trys", "ops", "f", "y", "g", "verb", "Symbol", "iterator", "v", "op", "TypeError", "pop", "push", "__read", "o", "m", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "useMemo", "useState", "useEventListener", "useMemoizedFn", "useRequest", "useUpdateEffect", "getTargetElement", "getClientHeight", "getScrollHeight", "getScrollTop", "useInfiniteScroll", "service", "options", "target", "isNoMore", "_a", "threshold", "_b", "reloadDeps", "manual", "_onBefore", "onBefore", "_onSuccess", "onSuccess", "_onError", "onError", "_onFinally", "onFinally", "_c", "finalData", "setFinalData", "_d", "loadingMore", "setLoadingMore", "noMore", "_e", "lastData", "currentData", "list", "d", "setTimeout", "scrollMethod", "loading", "run", "runAsync", "cancel", "loadMore", "loadMoreAsync", "reload", "reloadAsync", "el", "scrollTop", "scrollHeight", "clientHeight", "data", "mutate"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useInfiniteScroll/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function sent() {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n          case 7:\n            op = _.ops.pop();\n            _.trys.pop();\n            continue;\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n              _.ops.push(op);\n              break;\n            }\n            if (t[2]) _.ops.pop();\n            _.trys.pop();\n            continue;\n        }\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useMemo, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\nvar useInfiniteScroll = function useInfiniteScroll(service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var target = options.target,\n    isNoMore = options.isNoMore,\n    _a = options.threshold,\n    threshold = _a === void 0 ? 100 : _a,\n    _b = options.reloadDeps,\n    reloadDeps = _b === void 0 ? [] : _b,\n    manual = options.manual,\n    _onBefore = options.onBefore,\n    _onSuccess = options.onSuccess,\n    _onError = options.onError,\n    _onFinally = options.onFinally;\n  var _c = __read(useState(), 2),\n    finalData = _c[0],\n    setFinalData = _c[1];\n  var _d = __read(useState(false), 2),\n    loadingMore = _d[0],\n    setLoadingMore = _d[1];\n  var noMore = useMemo(function () {\n    if (!isNoMore) return false;\n    return isNoMore(finalData);\n  }, [finalData]);\n  var _e = useRequest(function (lastData) {\n      return __awaiter(void 0, void 0, void 0, function () {\n        var currentData;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              return [4 /*yield*/, service(lastData)];\n            case 1:\n              currentData = _a.sent();\n              if (!lastData) {\n                setFinalData(currentData);\n              } else {\n                setFinalData(__assign(__assign({}, currentData), {\n                  // @ts-ignore\n                  list: __spreadArray(__spreadArray([], __read(lastData.list), false), __read(currentData.list), false)\n                }));\n              }\n              return [2 /*return*/, currentData];\n          }\n        });\n      });\n    }, {\n      manual: manual,\n      onFinally: function onFinally(_, d, e) {\n        setLoadingMore(false);\n        _onFinally === null || _onFinally === void 0 ? void 0 : _onFinally(d, e);\n      },\n      onBefore: function onBefore() {\n        return _onBefore === null || _onBefore === void 0 ? void 0 : _onBefore();\n      },\n      onSuccess: function onSuccess(d) {\n        setTimeout(function () {\n          // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          scrollMethod();\n        });\n        _onSuccess === null || _onSuccess === void 0 ? void 0 : _onSuccess(d);\n      },\n      onError: function onError(e) {\n        return _onError === null || _onError === void 0 ? void 0 : _onError(e);\n      }\n    }),\n    loading = _e.loading,\n    run = _e.run,\n    runAsync = _e.runAsync,\n    cancel = _e.cancel;\n  var loadMore = function loadMore() {\n    if (noMore) return;\n    setLoadingMore(true);\n    run(finalData);\n  };\n  var loadMoreAsync = function loadMoreAsync() {\n    if (noMore) return Promise.reject();\n    setLoadingMore(true);\n    return runAsync(finalData);\n  };\n  var reload = function reload() {\n    return run();\n  };\n  var reloadAsync = function reloadAsync() {\n    return runAsync();\n  };\n  var scrollMethod = function scrollMethod() {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var scrollTop = getScrollTop(el);\n    var scrollHeight = getScrollHeight(el);\n    var clientHeight = getClientHeight(el);\n    if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: useMemoizedFn(loadMore),\n    loadMoreAsync: useMemoizedFn(loadMoreAsync),\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\nexport default useInfiniteScroll;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,SAAS,GAAG,QAAQ,KAAKA,SAAb,IAA0B,UAAUC,OAAV,EAAmBC,UAAnB,EAA+BC,CAA/B,EAAkCC,SAAlC,EAA6C;EACrF,SAASC,KAAT,CAAeC,KAAf,EAAsB;IACpB,OAAOA,KAAK,YAAYH,CAAjB,GAAqBG,KAArB,GAA6B,IAAIH,CAAJ,CAAM,UAAUI,OAAV,EAAmB;MAC3DA,OAAO,CAACD,KAAD,CAAP;IACD,CAFmC,CAApC;EAGD;;EACD,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAT,CAAN,EAAyB,UAAUD,OAAV,EAAmBE,MAAnB,EAA2B;IACzD,SAASC,SAAT,CAAmBJ,KAAnB,EAA0B;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAV,CAAeN,KAAf,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASC,QAAT,CAAkBR,KAAlB,EAAyB;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAD,CAAT,CAAmBE,KAAnB,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASF,IAAT,CAAcI,MAAd,EAAsB;MACpBA,MAAM,CAACC,IAAP,GAAcT,OAAO,CAACQ,MAAM,CAACT,KAAR,CAArB,GAAsCD,KAAK,CAACU,MAAM,CAACT,KAAR,CAAL,CAAoBW,IAApB,CAAyBP,SAAzB,EAAoCI,QAApC,CAAtC;IACD;;IACDH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACL,KAAV,CAAgBE,OAAhB,EAAyBC,UAAU,IAAI,EAAvC,CAAb,EAAyDU,IAAzD,EAAD,CAAJ;EACD,CAnBM,CAAP;AAoBD,CA1BD;;AA2BA,IAAIM,WAAW,GAAG,QAAQ,KAAKA,WAAb,IAA4B,UAAUjB,OAAV,EAAmBkB,IAAnB,EAAyB;EACrE,IAAIC,CAAC,GAAG;IACJC,KAAK,EAAE,CADH;IAEJC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIjC,CAAC,CAAC,CAAD,CAAD,GAAO,CAAX,EAAc,MAAMA,CAAC,CAAC,CAAD,CAAP;MACd,OAAOA,CAAC,CAAC,CAAD,CAAR;IACD,CALG;IAMJkC,IAAI,EAAE,EANF;IAOJC,GAAG,EAAE;EAPD,CAAR;EAAA,IASEC,CATF;EAAA,IAUEC,CAVF;EAAA,IAWErC,CAXF;EAAA,IAYEsC,CAZF;EAaA,OAAOA,CAAC,GAAG;IACTf,IAAI,EAAEgB,IAAI,CAAC,CAAD,CADD;IAET,SAASA,IAAI,CAAC,CAAD,CAFJ;IAGT,UAAUA,IAAI,CAAC,CAAD;EAHL,CAAJ,EAIJ,OAAOC,MAAP,KAAkB,UAAlB,KAAiCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAD,GAAqB,YAAY;IACnE,OAAO,IAAP;EACD,CAFE,CAJI,EAMHH,CANJ;;EAOA,SAASC,IAAT,CAAcpC,CAAd,EAAiB;IACf,OAAO,UAAUuC,CAAV,EAAa;MAClB,OAAOpB,IAAI,CAAC,CAACnB,CAAD,EAAIuC,CAAJ,CAAD,CAAX;IACD,CAFD;EAGD;;EACD,SAASpB,IAAT,CAAcqB,EAAd,EAAkB;IAChB,IAAIP,CAAJ,EAAO,MAAM,IAAIQ,SAAJ,CAAc,iCAAd,CAAN;;IACP,OAAOb,CAAP,EAAU;MACR,IAAI;QACF,IAAIK,CAAC,GAAG,CAAJ,EAAOC,CAAC,KAAKrC,CAAC,GAAG2C,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR,GAAYN,CAAC,CAAC,QAAD,CAAb,GAA0BM,EAAE,CAAC,CAAD,CAAF,GAAQN,CAAC,CAAC,OAAD,CAAD,KAAe,CAACrC,CAAC,GAAGqC,CAAC,CAAC,QAAD,CAAN,KAAqBrC,CAAC,CAACS,IAAF,CAAO4B,CAAP,CAArB,EAAgC,CAA/C,CAAR,GAA4DA,CAAC,CAACd,IAAjG,CAAD,IAA2G,CAAC,CAACvB,CAAC,GAAGA,CAAC,CAACS,IAAF,CAAO4B,CAAP,EAAUM,EAAE,CAAC,CAAD,CAAZ,CAAL,EAAuBhB,IAA9I,EAAoJ,OAAO3B,CAAP;QACpJ,IAAIqC,CAAC,GAAG,CAAJ,EAAOrC,CAAX,EAAc2C,EAAE,GAAG,CAACA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAT,EAAY3C,CAAC,CAACiB,KAAd,CAAL;;QACd,QAAQ0B,EAAE,CAAC,CAAD,CAAV;UACE,KAAK,CAAL;UACA,KAAK,CAAL;YACE3C,CAAC,GAAG2C,EAAJ;YACA;;UACF,KAAK,CAAL;YACEZ,CAAC,CAACC,KAAF;YACA,OAAO;cACLf,KAAK,EAAE0B,EAAE,CAAC,CAAD,CADJ;cAELhB,IAAI,EAAE;YAFD,CAAP;;UAIF,KAAK,CAAL;YACEI,CAAC,CAACC,KAAF;YACAK,CAAC,GAAGM,EAAE,CAAC,CAAD,CAAN;YACAA,EAAE,GAAG,CAAC,CAAD,CAAL;YACA;;UACF,KAAK,CAAL;YACEA,EAAE,GAAGZ,CAAC,CAACI,GAAF,CAAMU,GAAN,EAAL;;YACAd,CAAC,CAACG,IAAF,CAAOW,GAAP;;YACA;;UACF;YACE,IAAI,EAAE7C,CAAC,GAAG+B,CAAC,CAACG,IAAN,EAAYlC,CAAC,GAAGA,CAAC,CAACK,MAAF,GAAW,CAAX,IAAgBL,CAAC,CAACA,CAAC,CAACK,MAAF,GAAW,CAAZ,CAAnC,MAAuDsC,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAeA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAhF,CAAJ,EAAwF;cACtFZ,CAAC,GAAG,CAAJ;cACA;YACD;;YACD,IAAIY,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,KAAgB,CAAC3C,CAAD,IAAM2C,EAAE,CAAC,CAAD,CAAF,GAAQ3C,CAAC,CAAC,CAAD,CAAT,IAAgB2C,EAAE,CAAC,CAAD,CAAF,GAAQ3C,CAAC,CAAC,CAAD,CAA/C,CAAJ,EAAyD;cACvD+B,CAAC,CAACC,KAAF,GAAUW,EAAE,CAAC,CAAD,CAAZ;cACA;YACD;;YACD,IAAIA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAeZ,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAA9B,EAAmC;cACjC+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAAX;cACAA,CAAC,GAAG2C,EAAJ;cACA;YACD;;YACD,IAAI3C,CAAC,IAAI+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAApB,EAAyB;cACvB+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAAX;;cACA+B,CAAC,CAACI,GAAF,CAAMW,IAAN,CAAWH,EAAX;;cACA;YACD;;YACD,IAAI3C,CAAC,CAAC,CAAD,CAAL,EAAU+B,CAAC,CAACI,GAAF,CAAMU,GAAN;;YACVd,CAAC,CAACG,IAAF,CAAOW,GAAP;;YACA;QAzCJ;;QA2CAF,EAAE,GAAGb,IAAI,CAACrB,IAAL,CAAUG,OAAV,EAAmBmB,CAAnB,CAAL;MACD,CA/CD,CA+CE,OAAOP,CAAP,EAAU;QACVmB,EAAE,GAAG,CAAC,CAAD,EAAInB,CAAJ,CAAL;QACAa,CAAC,GAAG,CAAJ;MACD,CAlDD,SAkDU;QACRD,CAAC,GAAGpC,CAAC,GAAG,CAAR;MACD;IACF;;IACD,IAAI2C,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAZ,EAAe,MAAMA,EAAE,CAAC,CAAD,CAAR;IACf,OAAO;MACL1B,KAAK,EAAE0B,EAAE,CAAC,CAAD,CAAF,GAAQA,EAAE,CAAC,CAAD,CAAV,GAAgB,KAAK,CADvB;MAELhB,IAAI,EAAE;IAFD,CAAP;EAID;AACF,CAzFD;;AA0FA,IAAIoB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAa7C,CAAb,EAAgB;EAClD,IAAI8C,CAAC,GAAG,OAAOT,MAAP,KAAkB,UAAlB,IAAgCQ,CAAC,CAACR,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACQ,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAI9C,CAAC,GAAG+C,CAAC,CAACxC,IAAF,CAAOuC,CAAP,CAAR;EAAA,IACEE,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGE3B,CAHF;;EAIA,IAAI;IACF,OAAO,CAACrB,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAAC+C,CAAC,GAAGhD,CAAC,CAACqB,IAAF,EAAL,EAAeI,IAApD,EAA0D;MACxDwB,EAAE,CAACL,IAAH,CAAQI,CAAC,CAACjC,KAAV;IACD;EACF,CAJD,CAIE,OAAOmC,KAAP,EAAc;IACd5B,CAAC,GAAG;MACF4B,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIF,CAAC,IAAI,CAACA,CAAC,CAACvB,IAAR,KAAiBsB,CAAC,GAAG/C,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuC+C,CAAC,CAACxC,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIsB,CAAJ,EAAO,MAAMA,CAAC,CAAC4B,KAAR;IACR;EACF;;EACD,OAAOD,EAAP;AACD,CAvBD;;AAwBA,IAAIE,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIpD,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIH,CAAC,GAAG,CAAR,EAAWuD,CAAC,GAAGF,IAAI,CAAClD,MAApB,EAA4B8C,EAAjC,EAAqCjD,CAAC,GAAGuD,CAAzC,EAA4CvD,CAAC,EAA7C,EAAiD;IACnF,IAAIiD,EAAE,IAAI,EAAEjD,CAAC,IAAIqD,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACJ,EAAL,EAASA,EAAE,GAAGO,KAAK,CAACnD,SAAN,CAAgBoD,KAAhB,CAAsBlD,IAAtB,CAA2B8C,IAA3B,EAAiC,CAAjC,EAAoCrD,CAApC,CAAL;MACTiD,EAAE,CAACjD,CAAD,CAAF,GAAQqD,IAAI,CAACrD,CAAD,CAAZ;IACD;EACF;EACD,OAAOoD,EAAE,CAACM,MAAH,CAAUT,EAAE,IAAIO,KAAK,CAACnD,SAAN,CAAgBoD,KAAhB,CAAsBlD,IAAtB,CAA2B8C,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASM,OAAT,EAAkBC,QAAlB,QAAkC,OAAlC;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,OAAOC,eAAP,MAA4B,oBAA5B;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,eAAT,EAA0BC,eAA1B,EAA2CC,YAA3C,QAA+D,eAA/D;;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAT,CAA2BC,OAA3B,EAAoCC,OAApC,EAA6C;EACnE,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,MAAM,GAAGD,OAAO,CAACC,MAArB;EAAA,IACEC,QAAQ,GAAGF,OAAO,CAACE,QADrB;EAAA,IAEEC,EAAE,GAAGH,OAAO,CAACI,SAFf;EAAA,IAGEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,GAAhB,GAAsBA,EAHpC;EAAA,IAIEE,EAAE,GAAGL,OAAO,CAACM,UAJf;EAAA,IAKEA,UAAU,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EALpC;EAAA,IAMEE,MAAM,GAAGP,OAAO,CAACO,MANnB;EAAA,IAOEC,SAAS,GAAGR,OAAO,CAACS,QAPtB;EAAA,IAQEC,UAAU,GAAGV,OAAO,CAACW,SARvB;EAAA,IASEC,QAAQ,GAAGZ,OAAO,CAACa,OATrB;EAAA,IAUEC,UAAU,GAAGd,OAAO,CAACe,SAVvB;;EAWA,IAAIC,EAAE,GAAG1C,MAAM,CAACe,QAAQ,EAAT,EAAa,CAAb,CAAf;EAAA,IACE4B,SAAS,GAAGD,EAAE,CAAC,CAAD,CADhB;EAAA,IAEEE,YAAY,GAAGF,EAAE,CAAC,CAAD,CAFnB;;EAGA,IAAIG,EAAE,GAAG7C,MAAM,CAACe,QAAQ,CAAC,KAAD,CAAT,EAAkB,CAAlB,CAAf;EAAA,IACE+B,WAAW,GAAGD,EAAE,CAAC,CAAD,CADlB;EAAA,IAEEE,cAAc,GAAGF,EAAE,CAAC,CAAD,CAFrB;;EAGA,IAAIG,MAAM,GAAGlC,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACc,QAAL,EAAe,OAAO,KAAP;IACf,OAAOA,QAAQ,CAACe,SAAD,CAAf;EACD,CAHmB,EAGjB,CAACA,SAAD,CAHiB,CAApB;;EAIA,IAAIM,EAAE,GAAG/B,UAAU,CAAC,UAAUgC,QAAV,EAAoB;IACpC,OAAOtF,SAAS,CAAC,KAAK,CAAN,EAAS,KAAK,CAAd,EAAiB,KAAK,CAAtB,EAAyB,YAAY;MACnD,IAAIuF,WAAJ;MACA,OAAOrE,WAAW,CAAC,IAAD,EAAO,UAAU+C,EAAV,EAAc;QACrC,QAAQA,EAAE,CAAC5C,KAAX;UACE,KAAK,CAAL;YACE,OAAO,CAAC;YAAE;YAAH,EAAcwC,OAAO,CAACyB,QAAD,CAArB,CAAP;;UACF,KAAK,CAAL;YACEC,WAAW,GAAGtB,EAAE,CAAC3C,IAAH,EAAd;;YACA,IAAI,CAACgE,QAAL,EAAe;cACbN,YAAY,CAACO,WAAD,CAAZ;YACD,CAFD,MAEO;cACLP,YAAY,CAAC9F,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKqG,WAAL,CAAT,EAA4B;gBAC/C;gBACAC,IAAI,EAAE9C,aAAa,CAACA,aAAa,CAAC,EAAD,EAAKN,MAAM,CAACkD,QAAQ,CAACE,IAAV,CAAX,EAA4B,KAA5B,CAAd,EAAkDpD,MAAM,CAACmD,WAAW,CAACC,IAAb,CAAxD,EAA4E,KAA5E;cAF4B,CAA5B,CAAT,CAAZ;YAID;;YACD,OAAO,CAAC;YAAE;YAAH,EAAeD,WAAf,CAAP;QAbJ;MAeD,CAhBiB,CAAlB;IAiBD,CAnBe,CAAhB;EAoBD,CArBgB,EAqBd;IACDlB,MAAM,EAAEA,MADP;IAEDQ,SAAS,EAAE,SAASA,SAAT,CAAmBzD,CAAnB,EAAsBqE,CAAtB,EAAyB5E,CAAzB,EAA4B;MACrCsE,cAAc,CAAC,KAAD,CAAd;MACAP,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACa,CAAD,EAAI5E,CAAJ,CAAlE;IACD,CALA;IAMD0D,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5B,OAAOD,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,EAAtE;IACD,CARA;IASDG,SAAS,EAAE,SAASA,SAAT,CAAmBgB,CAAnB,EAAsB;MAC/BC,UAAU,CAAC,YAAY;QACrB;QACAC,YAAY;MACb,CAHS,CAAV;MAIAnB,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACiB,CAAD,CAAlE;IACD,CAfA;IAgBDd,OAAO,EAAE,SAASA,OAAT,CAAiB9D,CAAjB,EAAoB;MAC3B,OAAO6D,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC7D,CAAD,CAAnE;IACD;EAlBA,CArBc,CAAnB;EAAA,IAyCE+E,OAAO,GAAGP,EAAE,CAACO,OAzCf;EAAA,IA0CEC,GAAG,GAAGR,EAAE,CAACQ,GA1CX;EAAA,IA2CEC,QAAQ,GAAGT,EAAE,CAACS,QA3ChB;EAAA,IA4CEC,MAAM,GAAGV,EAAE,CAACU,MA5Cd;;EA6CA,IAAIC,QAAQ,GAAG,SAASA,QAAT,GAAoB;IACjC,IAAIZ,MAAJ,EAAY;IACZD,cAAc,CAAC,IAAD,CAAd;IACAU,GAAG,CAACd,SAAD,CAAH;EACD,CAJD;;EAKA,IAAIkB,aAAa,GAAG,SAASA,aAAT,GAAyB;IAC3C,IAAIb,MAAJ,EAAY,OAAO5E,OAAO,CAACC,MAAR,EAAP;IACZ0E,cAAc,CAAC,IAAD,CAAd;IACA,OAAOW,QAAQ,CAACf,SAAD,CAAf;EACD,CAJD;;EAKA,IAAImB,MAAM,GAAG,SAASA,MAAT,GAAkB;IAC7B,OAAOL,GAAG,EAAV;EACD,CAFD;;EAGA,IAAIM,WAAW,GAAG,SAASA,WAAT,GAAuB;IACvC,OAAOL,QAAQ,EAAf;EACD,CAFD;;EAGA,IAAIH,YAAY,GAAG,SAASA,YAAT,GAAwB;IACzC,IAAIS,EAAE,GAAG5C,gBAAgB,CAACO,MAAD,CAAzB;;IACA,IAAI,CAACqC,EAAL,EAAS;MACP;IACD;;IACD,IAAIC,SAAS,GAAG1C,YAAY,CAACyC,EAAD,CAA5B;IACA,IAAIE,YAAY,GAAG5C,eAAe,CAAC0C,EAAD,CAAlC;IACA,IAAIG,YAAY,GAAG9C,eAAe,CAAC2C,EAAD,CAAlC;;IACA,IAAIE,YAAY,GAAGD,SAAf,IAA4BE,YAAY,GAAGrC,SAA/C,EAA0D;MACxD8B,QAAQ;IACT;EACF,CAXD;;EAYA5C,gBAAgB,CAAC,QAAD,EAAW,YAAY;IACrC,IAAIwC,OAAO,IAAIV,WAAf,EAA4B;MAC1B;IACD;;IACDS,YAAY;EACb,CALe,EAKb;IACD5B,MAAM,EAAEA;EADP,CALa,CAAhB;EAQAR,eAAe,CAAC,YAAY;IAC1BsC,GAAG;EACJ,CAFc,EAEZnD,aAAa,CAAC,EAAD,EAAKN,MAAM,CAACgC,UAAD,CAAX,EAAyB,KAAzB,CAFD,CAAf;EAGA,OAAO;IACLoC,IAAI,EAAEzB,SADD;IAELa,OAAO,EAAE,CAACV,WAAD,IAAgBU,OAFpB;IAGLV,WAAW,EAAEA,WAHR;IAILE,MAAM,EAAEA,MAJH;IAKLY,QAAQ,EAAE3C,aAAa,CAAC2C,QAAD,CALlB;IAMLC,aAAa,EAAE5C,aAAa,CAAC4C,aAAD,CANvB;IAOLC,MAAM,EAAE7C,aAAa,CAAC6C,MAAD,CAPhB;IAQLC,WAAW,EAAE9C,aAAa,CAAC8C,WAAD,CARrB;IASLM,MAAM,EAAEzB,YATH;IAULe,MAAM,EAAEA;EAVH,CAAP;AAYD,CAzHD;;AA0HA,eAAenC,iBAAf"}, "metadata": {}, "sourceType": "module"}