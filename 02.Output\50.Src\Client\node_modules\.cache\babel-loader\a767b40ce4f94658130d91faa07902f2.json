{"ast": null, "code": "'use strict';\n\nvar random = require('./random');\n\nvar onUnload = {},\n    afterUnload = false // detect google chrome packaged apps because they don't allow the 'unload' event\n,\n    isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime;\nmodule.exports = {\n  attachEvent: function attachEvent(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener); // I get 'window' for ie8.\n\n      global.attachEvent('on' + event, listener);\n    }\n  },\n  detachEvent: function detachEvent(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  },\n  unloadAdd: function unloadAdd(listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n\n    return ref;\n  },\n  unloadDel: function unloadDel(ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  },\n  triggerUnloadCallbacks: function triggerUnloadCallbacks() {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\n\nvar unloadTriggered = function unloadTriggered() {\n  if (afterUnload) {\n    return;\n  }\n\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n}; // 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\n\n\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}", "map": {"version": 3, "names": ["random", "require", "onUnload", "afterUnload", "isChromePackagedApp", "global", "chrome", "app", "runtime", "module", "exports", "attachEvent", "event", "listener", "addEventListener", "document", "detachEvent", "removeEventListener", "unloadAdd", "ref", "string", "setTimeout", "triggerUnloadCallbacks", "unloadDel", "unloadTriggered"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/event.js"], "sourcesContent": ["'use strict';\n\nvar random = require('./random');\n\nvar onUnload = {}\n  , afterUnload = false\n    // detect google chrome packaged apps because they don't allow the 'unload' event\n  , isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime\n  ;\n\nmodule.exports = {\n  attachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  }\n\n, detachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  }\n\n, unloadAdd: function(listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  }\n\n, unloadDel: function(ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  }\n\n, triggerUnloadCallbacks: function() {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\n\nvar unloadTriggered = function() {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAD,CAApB;;AAEA,IAAIC,QAAQ,GAAG,EAAf;AAAA,IACIC,WAAW,GAAG,KADlB,CAEI;AAFJ;AAAA,IAGIC,mBAAmB,GAAGC,MAAM,CAACC,MAAP,IAAiBD,MAAM,CAACC,MAAP,CAAcC,GAA/B,IAAsCF,MAAM,CAACC,MAAP,CAAcC,GAAd,CAAkBC,OAHlF;AAMAC,MAAM,CAACC,OAAP,GAAiB;EACfC,WAAW,EAAE,qBAASC,KAAT,EAAgBC,QAAhB,EAA0B;IACrC,IAAI,OAAOR,MAAM,CAACS,gBAAd,KAAmC,WAAvC,EAAoD;MAClDT,MAAM,CAACS,gBAAP,CAAwBF,KAAxB,EAA+BC,QAA/B,EAAyC,KAAzC;IACD,CAFD,MAEO,IAAIR,MAAM,CAACU,QAAP,IAAmBV,MAAM,CAACM,WAA9B,EAA2C;MAChD;MACA;MACA;MACAN,MAAM,CAACU,QAAP,CAAgBJ,WAAhB,CAA4B,OAAOC,KAAnC,EAA0CC,QAA1C,EAJgD,CAKhD;;MACAR,MAAM,CAACM,WAAP,CAAmB,OAAOC,KAA1B,EAAiCC,QAAjC;IACD;EACF,CAZc;EAcfG,WAAW,EAAE,qBAASJ,KAAT,EAAgBC,QAAhB,EAA0B;IACrC,IAAI,OAAOR,MAAM,CAACS,gBAAd,KAAmC,WAAvC,EAAoD;MAClDT,MAAM,CAACY,mBAAP,CAA2BL,KAA3B,EAAkCC,QAAlC,EAA4C,KAA5C;IACD,CAFD,MAEO,IAAIR,MAAM,CAACU,QAAP,IAAmBV,MAAM,CAACW,WAA9B,EAA2C;MAChDX,MAAM,CAACU,QAAP,CAAgBC,WAAhB,CAA4B,OAAOJ,KAAnC,EAA0CC,QAA1C;MACAR,MAAM,CAACW,WAAP,CAAmB,OAAOJ,KAA1B,EAAiCC,QAAjC;IACD;EACF,CArBc;EAuBfK,SAAS,EAAE,mBAASL,QAAT,EAAmB;IAC5B,IAAIT,mBAAJ,EAAyB;MACvB,OAAO,IAAP;IACD;;IAED,IAAIe,GAAG,GAAGnB,MAAM,CAACoB,MAAP,CAAc,CAAd,CAAV;IACAlB,QAAQ,CAACiB,GAAD,CAAR,GAAgBN,QAAhB;;IACA,IAAIV,WAAJ,EAAiB;MACfkB,UAAU,CAAC,KAAKC,sBAAN,EAA8B,CAA9B,CAAV;IACD;;IACD,OAAOH,GAAP;EACD,CAlCc;EAoCfI,SAAS,EAAE,mBAASJ,GAAT,EAAc;IACvB,IAAIA,GAAG,IAAIjB,QAAX,EAAqB;MACnB,OAAOA,QAAQ,CAACiB,GAAD,CAAf;IACD;EACF,CAxCc;EA0CfG,sBAAsB,EAAE,kCAAW;IACjC,KAAK,IAAIH,GAAT,IAAgBjB,QAAhB,EAA0B;MACxBA,QAAQ,CAACiB,GAAD,CAAR;MACA,OAAOjB,QAAQ,CAACiB,GAAD,CAAf;IACD;EACF;AA/Cc,CAAjB;;AAkDA,IAAIK,eAAe,GAAG,SAAlBA,eAAkB,GAAW;EAC/B,IAAIrB,WAAJ,EAAiB;IACf;EACD;;EACDA,WAAW,GAAG,IAAd;EACAM,MAAM,CAACC,OAAP,CAAeY,sBAAf;AACD,CAND,C,CAQA;AACA;;;AACA,IAAI,CAAClB,mBAAL,EAA0B;EACxBK,MAAM,CAACC,OAAP,CAAeC,WAAf,CAA2B,QAA3B,EAAqCa,eAArC;AACD"}, "metadata": {}, "sourceType": "script"}