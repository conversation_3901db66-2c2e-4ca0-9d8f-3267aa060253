{"ast": null, "code": "var root = require('./_root'),\n    stubFalse = require('./stubFalse');\n/** Detect free variable `exports`. */\n\n\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */\n\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */\n\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n/** Built-in value references. */\n\nvar Buffer = moduleExports ? root.Buffer : undefined;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\n\nvar isBuffer = nativeIsBuffer || stubFalse;\nmodule.exports = isBuffer;", "map": {"version": 3, "names": ["root", "require", "stubFalse", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "<PERSON><PERSON><PERSON>", "undefined", "nativeIsBuffer", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/isBuffer.js"], "sourcesContent": ["var root = require('./_root'),\n    stubFalse = require('./stubFalse');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\nmodule.exports = isBuffer;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAD,CAAlB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,aAAD,CADvB;AAGA;;;AACA,IAAIE,WAAW,GAAG,OAAOC,OAAP,IAAkB,QAAlB,IAA8BA,OAA9B,IAAyC,CAACA,OAAO,CAACC,QAAlD,IAA8DD,OAAhF;AAEA;;AACA,IAAIE,UAAU,GAAGH,WAAW,IAAI,OAAOI,MAAP,IAAiB,QAAhC,IAA4CA,MAA5C,IAAsD,CAACA,MAAM,CAACF,QAA9D,IAA0EE,MAA3F;AAEA;;AACA,IAAIC,aAAa,GAAGF,UAAU,IAAIA,UAAU,CAACF,OAAX,KAAuBD,WAAzD;AAEA;;AACA,IAAIM,MAAM,GAAGD,aAAa,GAAGR,IAAI,CAACS,MAAR,GAAiBC,SAA3C;AAEA;;AACA,IAAIC,cAAc,GAAGF,MAAM,GAAGA,MAAM,CAACG,QAAV,GAAqBF,SAAhD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIE,QAAQ,GAAGD,cAAc,IAAIT,SAAjC;AAEAK,MAAM,CAACH,OAAP,GAAiBQ,QAAjB"}, "metadata": {}, "sourceType": "script"}