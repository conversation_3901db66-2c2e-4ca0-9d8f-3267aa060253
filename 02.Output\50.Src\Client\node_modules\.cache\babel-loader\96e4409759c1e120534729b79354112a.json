{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _createForOfIteratorHelper from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js\";import React from'react';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource}from'../utils/Util.js';import BlinkBlock,{checkBlinkInfo}from'./elements/BlinkBlock';import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var MAX_ROW=32;var GRID_COLS_VEHICLE_DEPLOY='16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';var GRID_COLS_VEHICLE_NODEPLOY='16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */var Vehicle=function Vehicle(props){if(!isValidSource(props))return;var showDelopy=props.is_deployment===1;var totalRowCounter=0;var targetArray=[];if(!props.title_name)return;//最大MaxRowのデータを取り出す\nvar _iterator=_createForOfIteratorHelper(props.title_name),_step;try{for(_iterator.s();!(_step=_iterator.n()).done;){var item=_step.value;if(!item.car_name)return;// 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\nvar subRowCounter=item.car_name.length+1;if(totalRowCounter+subRowCounter<=MAX_ROW){targetArray.push(item);totalRowCounter=totalRowCounter+subRowCounter;}else if(totalRowCounter<=MAX_ROW-2){var _newObj$deployment,_newObj$car_name,_newObj$town_name,_newObj$disaster_type,_newObj$avm_dynamic_s,_newObj$lighting_sett;subRowCounter=MAX_ROW-totalRowCounter;// この - 1がSubTitleの行\nvar subItemCounter=subRowCounter-1;var newObj=_objectSpread({},item);newObj.deployment=(_newObj$deployment=newObj.deployment)===null||_newObj$deployment===void 0?void 0:_newObj$deployment.slice(0,subItemCounter);newObj.car_name=(_newObj$car_name=newObj.car_name)===null||_newObj$car_name===void 0?void 0:_newObj$car_name.slice(0,subItemCounter);newObj.town_name=(_newObj$town_name=newObj.town_name)===null||_newObj$town_name===void 0?void 0:_newObj$town_name.slice(0,subItemCounter);newObj.disaster_type=(_newObj$disaster_type=newObj.disaster_type)===null||_newObj$disaster_type===void 0?void 0:_newObj$disaster_type.slice(0,subItemCounter);newObj.avm_dynamic_state=(_newObj$avm_dynamic_s=newObj.avm_dynamic_state)===null||_newObj$avm_dynamic_s===void 0?void 0:_newObj$avm_dynamic_s.slice(0,subItemCounter);newObj.lighting_setting=(_newObj$lighting_sett=newObj.lighting_setting)===null||_newObj$lighting_sett===void 0?void 0:_newObj$lighting_sett.slice(0,subItemCounter);targetArray.push(newObj);totalRowCounter=totalRowCounter+subRowCounter;}}}catch(err){_iterator.e(err);}finally{_iterator.f();}var nextStartRow=1;return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 grid-rows-16 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]\",children:targetArray.map(function(item,index){var _item$car_name;var startRow=nextStartRow;//現在のStart\nnextStartRow=nextStartRow+(item===null||item===void 0?void 0:(_item$car_name=item.car_name)===null||_item$car_name===void 0?void 0:_item$car_name.length)+1;//次のStartRowを計算\nreturn/*#__PURE__*/_jsx(Station,_objectSpread(_objectSpread({},item),{},{index:index,showDelopy:showDelopy,startRow:startRow}),index);})})});};/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} props\r\n * @returns 表示データ\r\n */var Station=function Station(props){var gridCol;var subTitleSpan='col-span-full';if(props.showDelopy){gridCol='grid-cols-vehicle-deploy';//gridCol = GRID_COLS_VEHICLE_DEPLOY;\n}else{gridCol='grid-cols-vehicle-nodeploy';//gridCol = GRID_COLS_VEHICLE_NODEPLOY;\n}var subTitleProp=getCellFace(props,\"\".concat(subTitleSpan,\" flex flex-col items-center\"));return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},subTitleProp),{},{children:props.display_text&&/*#__PURE__*/_jsx(\"span\",{children:props.display_text})}))}),props.car_name.map(function(item,index){return/*#__PURE__*/_jsx(VehicleDetailRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})]});};/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} props\r\n * @returns 表示データ\r\n */var VehicleDetailRow=function VehicleDetailRow(props){var showInfoDeployment,showInfoCarName,showInfoTownName,showInfoDisasterType,showInfoAvmDynamicState;if(props.showDelopy&&props.deployment&&props.lighting_setting){showInfoDeployment=props.deployment[props.index];}if(props.car_name&&props.lighting_setting){showInfoCarName=props.car_name[props.index];}if(props.town_name&&props.lighting_setting){showInfoTownName=props.town_name[props.index];}if(props.disaster_type&&props.lighting_setting){showInfoDisasterType=props.disaster_type[props.index];}if(props.avm_dynamic_state&&props.lighting_setting){showInfoAvmDynamicState=props.avm_dynamic_state[props.index];}var status=1;if(props.lighting_setting&&props.lighting_setting[props.index]){status=props.lighting_setting[props.index].lighting_status;}//該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\nvar baseObj=props.car_name[props.index];if(!baseObj){baseObj=props.town_name[props.index];}if(!baseObj){baseObj=props.disaster_type[props.index];}if(!baseObj){baseObj=props.avm_dynamic_state[props.index];}if(!baseObj){baseObj=props.deployment[props.index];}showInfoDeployment=checkBlinkInfo(showInfoDeployment,baseObj);showInfoCarName=checkBlinkInfo(showInfoCarName,baseObj);showInfoTownName=checkBlinkInfo(showInfoTownName,baseObj);showInfoDisasterType=checkBlinkInfo(showInfoDisasterType,baseObj);showInfoAvmDynamicState=checkBlinkInfo(showInfoAvmDynamicState,baseObj);var showInfoSeperator0=_objectSpread({},showInfoDeployment);var showInfoSeperator1=_objectSpread({},showInfoDeployment);var showInfoSeperator2=_objectSpread({},showInfoCarName);var showInfoSeperator3=_objectSpread({},showInfoTownName);var showInfoSeperator4=_objectSpread({},showInfoDisasterType);var showInfoSeperator5=_objectSpread({},showInfoAvmDynamicState);showInfoSeperator0.display_text=' ';showInfoSeperator1.display_text=' ';showInfoSeperator2.display_text=' ';showInfoSeperator3.display_text=' ';showInfoSeperator4.display_text=' ';showInfoSeperator5.display_text=' ';// Status=3 点滅以外、背景色を表示する必要がないので、クリアする\nif(status!==3){showInfoSeperator0.background_color=undefined;showInfoSeperator1.background_color=undefined;showInfoSeperator2.background_color=undefined;showInfoSeperator3.background_color=undefined;showInfoSeperator4.background_color=undefined;showInfoSeperator5.background_color=undefined;}var gridCol;var showBlock=[];if(props.showDelopy){gridCol='grid-cols-vehicle-deploy';//gridCol = GRID_COLS_VEHICLE_DEPLOY;\nif(props.startRow+props.index<=MAX_ROW/2){//左半分\nshowBlock.push({showInfo:showInfoSeperator0,className:'col-span-1'});}showBlock.push({showInfo:showInfoDeployment,className:'col-span-1 col-start-2'});showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-4'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-9'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-16'});showBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-19'});if(props.startRow+props.index>MAX_ROW/2){//右半分\nshowBlock.push({showInfo:showInfoSeperator5,className:'col-span-1'});}}else{gridCol='grid-cols-vehicle-nodeploy';//gridCol = GRID_COLS_VEHICLE_NODEPLOY;\nshowBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-2'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-7'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-14'});showBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-17'});if(props.startRow+props.index>MAX_ROW/2){//右半分\nshowBlock.push({showInfo:showInfoSeperator5,className:'col-span-1'});}}return/*#__PURE__*/_jsxs(_Fragment,{children:[props.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:props.lighting_setting[props.index]})}),!props.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:props.lighting_setting[props.index]})})]});};export default Vehicle;", "map": {"version": 3, "names": ["React", "CellBox", "getCellFace", "isValidSource", "BlinkBlock", "checkBlinkInfo", "MAX_ROW", "GRID_COLS_VEHICLE_DEPLOY", "GRID_COLS_VEHICLE_NODEPLOY", "Vehicle", "props", "showDelopy", "is_deployment", "totalRowCounter", "targetArray", "title_name", "item", "car_name", "subRowCounter", "length", "push", "subItemCounter", "newObj", "deployment", "slice", "town_name", "disaster_type", "avm_dynamic_state", "lighting_setting", "nextStartRow", "map", "index", "startRow", "Station", "gridCol", "subTitleSpan", "subTitleProp", "display_text", "VehicleDetailRow", "showInfoDeployment", "showInfoCarName", "showInfoTownName", "showInfoDisasterType", "showInfoAvmDynamicState", "status", "lighting_status", "baseObj", "showInfoSeperator0", "showInfoSeperator1", "showInfoSeperator2", "showInfoSeperator3", "showInfoSeperator4", "showInfoSeperator5", "background_color", "undefined", "showBlock", "showInfo", "className"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Vehicle.js"], "sourcesContent": ["import React from 'react';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\r\n\r\nconst MAX_ROW = 32;\r\nconst GRID_COLS_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\r\nconst GRID_COLS_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\r\n\r\n/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */\r\nconst Vehicle = (props) => {\r\n    if (!isValidSource(props)) return;\r\n\r\n    let showDelopy = props.is_deployment === 1;\r\n\r\n    let totalRowCounter = 0;\r\n\r\n    let targetArray = [];\r\n\r\n    if (!props.title_name)\r\n        return;\r\n    \r\n    //最大MaxRowのデータを取り出す\r\n    for (const item of props.title_name) {\r\n        \r\n        if (!item.car_name) \r\n            return;\r\n\r\n        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\r\n        let subRowCounter = item.car_name.length + 1;\r\n\r\n        if (totalRowCounter + subRowCounter <= MAX_ROW) {\r\n            targetArray.push(item);\r\n            totalRowCounter = totalRowCounter + subRowCounter;\r\n        } else if (totalRowCounter <= MAX_ROW - 2) {\r\n            subRowCounter = MAX_ROW - totalRowCounter;\r\n\r\n            // この - 1がSubTitleの行\r\n            const subItemCounter = subRowCounter - 1;\r\n\r\n            let newObj = { ...item };\r\n            newObj.deployment = newObj.deployment?.slice(0, subItemCounter);\r\n            newObj.car_name = newObj.car_name?.slice(0, subItemCounter);\r\n            newObj.town_name = newObj.town_name?.slice(0, subItemCounter);\r\n            newObj.disaster_type = newObj.disaster_type?.slice(0, subItemCounter);\r\n            newObj.avm_dynamic_state = newObj.avm_dynamic_state?.slice(\r\n                0,\r\n                subItemCounter\r\n            );\r\n            newObj.lighting_setting = newObj.lighting_setting?.slice(\r\n                0,\r\n                subItemCounter\r\n            );\r\n\r\n            targetArray.push(newObj);\r\n            totalRowCounter = totalRowCounter + subRowCounter;\r\n        }\r\n    }\r\n\r\n    let nextStartRow = 1;\r\n    return (\r\n        <>\r\n            {isValidSource(props) && (\r\n                <div className=\"grid grid-cols-2 grid-rows-16 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]\">\r\n                    {targetArray.map((item, index) => {\r\n                        let startRow = nextStartRow; //現在のStart\r\n                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算\r\n\r\n                        return (\r\n                            <Station\r\n                                key={index}\r\n                                {...item}\r\n                                index={index}\r\n                                showDelopy={showDelopy}\r\n                                startRow={startRow}\r\n                            />\r\n                        );\r\n                    })}\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} props\r\n * @returns 表示データ\r\n */\r\nconst Station = (props) => {\r\n    let gridCol;\r\n    let subTitleSpan = 'col-span-full';\r\n    if (props.showDelopy) {\r\n        gridCol = 'grid-cols-vehicle-deploy';\r\n        //gridCol = GRID_COLS_VEHICLE_DEPLOY;\r\n    } else {\r\n        gridCol = 'grid-cols-vehicle-nodeploy';\r\n        //gridCol = GRID_COLS_VEHICLE_NODEPLOY;\r\n    }\r\n    const subTitleProp = getCellFace(\r\n        props,\r\n        `${subTitleSpan} flex flex-col items-center`\r\n    );\r\n\r\n    return (\r\n        <>\r\n            <div className={`grid ${gridCol}`}>\r\n                <CellBox {...subTitleProp}>\r\n                    {props.display_text && <span>{props.display_text}</span>}\r\n                </CellBox>\r\n            </div>\r\n            {props.car_name.map((item, index) => {\r\n                return <VehicleDetailRow key={index} {...props} index={index} />;\r\n            })}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} props\r\n * @returns 表示データ\r\n */\r\nconst VehicleDetailRow = (props) => {\r\n    let showInfoDeployment,\r\n        showInfoCarName,\r\n        showInfoTownName,\r\n        showInfoDisasterType,\r\n        showInfoAvmDynamicState;\r\n    if (props.showDelopy && props.deployment && props.lighting_setting) {\r\n        showInfoDeployment = props.deployment[props.index];\r\n    }\r\n    if (props.car_name && props.lighting_setting) {\r\n        showInfoCarName = props.car_name[props.index];\r\n    }\r\n    if (props.town_name && props.lighting_setting) {\r\n        showInfoTownName = props.town_name[props.index];\r\n    }\r\n    if (props.disaster_type && props.lighting_setting) {\r\n        showInfoDisasterType = props.disaster_type[props.index];\r\n    }\r\n    if (props.avm_dynamic_state && props.lighting_setting) {\r\n        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\r\n    }\r\n\r\n    let status = 1;\r\n    if (props.lighting_setting && props.lighting_setting[props.index]) {\r\n        status = props.lighting_setting[props.index].lighting_status;\r\n    }\r\n\r\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\r\n    let baseObj = props.car_name[props.index];\r\n    if (!baseObj) { baseObj = props.town_name[props.index] }\r\n    if (!baseObj) { baseObj = props.disaster_type[props.index] }\r\n    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }\r\n    if (!baseObj) { baseObj = props.deployment[props.index] }\r\n\r\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\r\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\r\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\r\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\r\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\r\n\r\n    let showInfoSeperator0 = { ...showInfoDeployment };\r\n    let showInfoSeperator1 = { ...showInfoDeployment };\r\n    let showInfoSeperator2 = { ...showInfoCarName };\r\n    let showInfoSeperator3 = { ...showInfoTownName };\r\n    let showInfoSeperator4 = { ...showInfoDisasterType };\r\n    let showInfoSeperator5 = { ...showInfoAvmDynamicState };\r\n\r\n    showInfoSeperator0.display_text = ' ';\r\n    showInfoSeperator1.display_text = ' ';\r\n    showInfoSeperator2.display_text = ' ';\r\n    showInfoSeperator3.display_text = ' ';\r\n    showInfoSeperator4.display_text = ' ';\r\n    showInfoSeperator5.display_text = ' ';\r\n\r\n    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする\r\n    if (status !== 3) {\r\n        showInfoSeperator0.background_color = undefined;\r\n        showInfoSeperator1.background_color = undefined;\r\n        showInfoSeperator2.background_color = undefined;\r\n        showInfoSeperator3.background_color = undefined;\r\n        showInfoSeperator4.background_color = undefined;\r\n        showInfoSeperator5.background_color = undefined;\r\n    }\r\n\r\n    let gridCol;\r\n    let showBlock = [];\r\n    if (props.showDelopy) {\r\n        gridCol = 'grid-cols-vehicle-deploy';\r\n        //gridCol = GRID_COLS_VEHICLE_DEPLOY;\r\n\r\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\r\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n        }\r\n        showBlock.push({\r\n            showInfo: showInfoDeployment,\r\n            className: 'col-span-1 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-4',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-9',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-16',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-19',\r\n        });\r\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n    } else {\r\n        gridCol = 'grid-cols-vehicle-nodeploy';\r\n        //gridCol = GRID_COLS_VEHICLE_NODEPLOY;\r\n\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-7',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-14',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-17',\r\n        });\r\n\r\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\r\n            {props.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={props.lighting_setting[props.index]}\r\n                    />\r\n                </div>\r\n            )}\r\n            {!props.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={props.lighting_setting[props.index]}\r\n                    />\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n\r\n\r\nexport default Vehicle;\r\n"], "mappings": "2SAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,WAAP,EAAqBC,cAArB,KAA2C,uBAA3C,C,6IAEA,GAAMC,QAAO,CAAG,EAAhB,CACA,GAAMC,yBAAwB,CAAG,gKAAjC,CACA,GAAMC,2BAA0B,CAAG,wIAAnC,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,QAAO,CAAG,QAAVA,QAAU,CAACC,KAAD,CAAW,CACvB,GAAI,CAACP,aAAa,CAACO,KAAD,CAAlB,CAA2B,OAE3B,GAAIC,WAAU,CAAGD,KAAK,CAACE,aAAN,GAAwB,CAAzC,CAEA,GAAIC,gBAAe,CAAG,CAAtB,CAEA,GAAIC,YAAW,CAAG,EAAlB,CAEA,GAAI,CAACJ,KAAK,CAACK,UAAX,CACI,OAEJ;AAZuB,yCAaJL,KAAK,CAACK,UAbF,YAavB,+CAAqC,IAA1BC,KAA0B,aAEjC,GAAI,CAACA,IAAI,CAACC,QAAV,CACI,OAEJ;AACA,GAAIC,cAAa,CAAGF,IAAI,CAACC,QAAL,CAAcE,MAAd,CAAuB,CAA3C,CAEA,GAAIN,eAAe,CAAGK,aAAlB,EAAmCZ,OAAvC,CAAgD,CAC5CQ,WAAW,CAACM,IAAZ,CAAiBJ,IAAjB,EACAH,eAAe,CAAGA,eAAe,CAAGK,aAApC,CACH,CAHD,IAGO,IAAIL,eAAe,EAAIP,OAAO,CAAG,CAAjC,CAAoC,6HACvCY,aAAa,CAAGZ,OAAO,CAAGO,eAA1B,CAEA;AACA,GAAMQ,eAAc,CAAGH,aAAa,CAAG,CAAvC,CAEA,GAAII,OAAM,kBAAQN,IAAR,CAAV,CACAM,MAAM,CAACC,UAAP,qBAAoBD,MAAM,CAACC,UAA3B,6CAAoB,mBAAmBC,KAAnB,CAAyB,CAAzB,CAA4BH,cAA5B,CAApB,CACAC,MAAM,CAACL,QAAP,mBAAkBK,MAAM,CAACL,QAAzB,2CAAkB,iBAAiBO,KAAjB,CAAuB,CAAvB,CAA0BH,cAA1B,CAAlB,CACAC,MAAM,CAACG,SAAP,oBAAmBH,MAAM,CAACG,SAA1B,4CAAmB,kBAAkBD,KAAlB,CAAwB,CAAxB,CAA2BH,cAA3B,CAAnB,CACAC,MAAM,CAACI,aAAP,wBAAuBJ,MAAM,CAACI,aAA9B,gDAAuB,sBAAsBF,KAAtB,CAA4B,CAA5B,CAA+BH,cAA/B,CAAvB,CACAC,MAAM,CAACK,iBAAP,wBAA2BL,MAAM,CAACK,iBAAlC,gDAA2B,sBAA0BH,KAA1B,CACvB,CADuB,CAEvBH,cAFuB,CAA3B,CAIAC,MAAM,CAACM,gBAAP,wBAA0BN,MAAM,CAACM,gBAAjC,gDAA0B,sBAAyBJ,KAAzB,CACtB,CADsB,CAEtBH,cAFsB,CAA1B,CAKAP,WAAW,CAACM,IAAZ,CAAiBE,MAAjB,EACAT,eAAe,CAAGA,eAAe,CAAGK,aAApC,CACH,CACJ,CA/CsB,qDAiDvB,GAAIW,aAAY,CAAG,CAAnB,CACA,mBACI,yBACK1B,aAAa,CAACO,KAAD,CAAb,eACG,YAAK,SAAS,CAAC,uGAAf,UACKI,WAAW,CAACgB,GAAZ,CAAgB,SAACd,IAAD,CAAOe,KAAP,CAAiB,oBAC9B,GAAIC,SAAQ,CAAGH,YAAf,CAA6B;AAC7BA,YAAY,CAAGA,YAAY,EAAGb,IAAH,SAAGA,IAAH,iCAAGA,IAAI,CAAEC,QAAT,yCAAG,eAAgBE,MAAnB,CAAZ,CAAwC,CAAvD,CAA0D;AAE1D,mBACI,KAAC,OAAD,gCAEQH,IAFR,MAGI,KAAK,CAAEe,KAHX,CAII,UAAU,CAAEpB,UAJhB,CAKI,QAAQ,CAAEqB,QALd,GACSD,KADT,CADJ,CASH,CAbA,CADL,EAFR,EADJ,CAsBH,CAxED,CA0EA;AACA;AACA;AACA;AACA,GACA,GAAME,QAAO,CAAG,QAAVA,QAAU,CAACvB,KAAD,CAAW,CACvB,GAAIwB,QAAJ,CACA,GAAIC,aAAY,CAAG,eAAnB,CACA,GAAIzB,KAAK,CAACC,UAAV,CAAsB,CAClBuB,OAAO,CAAG,0BAAV,CACA;AACH,CAHD,IAGO,CACHA,OAAO,CAAG,4BAAV,CACA;AACH,CACD,GAAME,aAAY,CAAGlC,WAAW,CAC5BQ,KAD4B,WAEzByB,YAFyB,gCAAhC,CAKA,mBACI,wCACI,YAAK,SAAS,gBAAUD,OAAV,CAAd,uBACI,KAAC,OAAD,gCAAaE,YAAb,eACK1B,KAAK,CAAC2B,YAAN,eAAsB,sBAAO3B,KAAK,CAAC2B,YAAb,EAD3B,GADJ,EADJ,CAMK3B,KAAK,CAACO,QAAN,CAAea,GAAf,CAAmB,SAACd,IAAD,CAAOe,KAAP,CAAiB,CACjC,mBAAO,KAAC,gBAAD,gCAAkCrB,KAAlC,MAAyC,KAAK,CAAEqB,KAAhD,GAAuBA,KAAvB,CAAP,CACH,CAFA,CANL,GADJ,CAYH,CA3BD,CA6BA;AACA;AACA;AACA;AACA,GACA,GAAMO,iBAAgB,CAAG,QAAnBA,iBAAmB,CAAC5B,KAAD,CAAW,CAChC,GAAI6B,mBAAJ,CACIC,eADJ,CAEIC,gBAFJ,CAGIC,oBAHJ,CAIIC,uBAJJ,CAKA,GAAIjC,KAAK,CAACC,UAAN,EAAoBD,KAAK,CAACa,UAA1B,EAAwCb,KAAK,CAACkB,gBAAlD,CAAoE,CAChEW,kBAAkB,CAAG7B,KAAK,CAACa,UAAN,CAAiBb,KAAK,CAACqB,KAAvB,CAArB,CACH,CACD,GAAIrB,KAAK,CAACO,QAAN,EAAkBP,KAAK,CAACkB,gBAA5B,CAA8C,CAC1CY,eAAe,CAAG9B,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACqB,KAArB,CAAlB,CACH,CACD,GAAIrB,KAAK,CAACe,SAAN,EAAmBf,KAAK,CAACkB,gBAA7B,CAA+C,CAC3Ca,gBAAgB,CAAG/B,KAAK,CAACe,SAAN,CAAgBf,KAAK,CAACqB,KAAtB,CAAnB,CACH,CACD,GAAIrB,KAAK,CAACgB,aAAN,EAAuBhB,KAAK,CAACkB,gBAAjC,CAAmD,CAC/Cc,oBAAoB,CAAGhC,KAAK,CAACgB,aAAN,CAAoBhB,KAAK,CAACqB,KAA1B,CAAvB,CACH,CACD,GAAIrB,KAAK,CAACiB,iBAAN,EAA2BjB,KAAK,CAACkB,gBAArC,CAAuD,CACnDe,uBAAuB,CAAGjC,KAAK,CAACiB,iBAAN,CAAwBjB,KAAK,CAACqB,KAA9B,CAA1B,CACH,CAED,GAAIa,OAAM,CAAG,CAAb,CACA,GAAIlC,KAAK,CAACkB,gBAAN,EAA0BlB,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B,CAA9B,CAAmE,CAC/Da,MAAM,CAAGlC,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B,EAAoCc,eAA7C,CACH,CAED;AACA,GAAIC,QAAO,CAAGpC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACqB,KAArB,CAAd,CACA,GAAI,CAACe,OAAL,CAAc,CAAEA,OAAO,CAAGpC,KAAK,CAACe,SAAN,CAAgBf,KAAK,CAACqB,KAAtB,CAAV,CAAwC,CACxD,GAAI,CAACe,OAAL,CAAc,CAAEA,OAAO,CAAGpC,KAAK,CAACgB,aAAN,CAAoBhB,KAAK,CAACqB,KAA1B,CAAV,CAA4C,CAC5D,GAAI,CAACe,OAAL,CAAc,CAAEA,OAAO,CAAGpC,KAAK,CAACiB,iBAAN,CAAwBjB,KAAK,CAACqB,KAA9B,CAAV,CAAgD,CAChE,GAAI,CAACe,OAAL,CAAc,CAAEA,OAAO,CAAGpC,KAAK,CAACa,UAAN,CAAiBb,KAAK,CAACqB,KAAvB,CAAV,CAAyC,CAEzDQ,kBAAkB,CAAGlC,cAAc,CAACkC,kBAAD,CAAqBO,OAArB,CAAnC,CACAN,eAAe,CAAGnC,cAAc,CAACmC,eAAD,CAAkBM,OAAlB,CAAhC,CACAL,gBAAgB,CAAGpC,cAAc,CAACoC,gBAAD,CAAmBK,OAAnB,CAAjC,CACAJ,oBAAoB,CAAGrC,cAAc,CAACqC,oBAAD,CAAuBI,OAAvB,CAArC,CACAH,uBAAuB,CAAGtC,cAAc,CAACsC,uBAAD,CAA0BG,OAA1B,CAAxC,CAEA,GAAIC,mBAAkB,kBAAQR,kBAAR,CAAtB,CACA,GAAIS,mBAAkB,kBAAQT,kBAAR,CAAtB,CACA,GAAIU,mBAAkB,kBAAQT,eAAR,CAAtB,CACA,GAAIU,mBAAkB,kBAAQT,gBAAR,CAAtB,CACA,GAAIU,mBAAkB,kBAAQT,oBAAR,CAAtB,CACA,GAAIU,mBAAkB,kBAAQT,uBAAR,CAAtB,CAEAI,kBAAkB,CAACV,YAAnB,CAAkC,GAAlC,CACAW,kBAAkB,CAACX,YAAnB,CAAkC,GAAlC,CACAY,kBAAkB,CAACZ,YAAnB,CAAkC,GAAlC,CACAa,kBAAkB,CAACb,YAAnB,CAAkC,GAAlC,CACAc,kBAAkB,CAACd,YAAnB,CAAkC,GAAlC,CACAe,kBAAkB,CAACf,YAAnB,CAAkC,GAAlC,CAEA;AACA,GAAIO,MAAM,GAAK,CAAf,CAAkB,CACdG,kBAAkB,CAACM,gBAAnB,CAAsCC,SAAtC,CACAN,kBAAkB,CAACK,gBAAnB,CAAsCC,SAAtC,CACAL,kBAAkB,CAACI,gBAAnB,CAAsCC,SAAtC,CACAJ,kBAAkB,CAACG,gBAAnB,CAAsCC,SAAtC,CACAH,kBAAkB,CAACE,gBAAnB,CAAsCC,SAAtC,CACAF,kBAAkB,CAACC,gBAAnB,CAAsCC,SAAtC,CACH,CAED,GAAIpB,QAAJ,CACA,GAAIqB,UAAS,CAAG,EAAhB,CACA,GAAI7C,KAAK,CAACC,UAAV,CAAsB,CAClBuB,OAAO,CAAG,0BAAV,CACA;AAEA,GAAKxB,KAAK,CAACsB,QAAN,CAAiBtB,KAAK,CAACqB,KAAxB,EAAmCzB,OAAO,CAAG,CAAjD,CAAqD,CAAE;AACnDiD,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACH,CACDF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEjB,kBADC,CAEXkB,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAER,kBAAZ,CAAgCS,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEhB,eADC,CAEXiB,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEP,kBAAZ,CAAgCQ,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEf,gBADC,CAEXgB,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEN,kBAAZ,CAAgCO,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEd,oBADC,CAEXe,SAAS,CAAE,yBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEL,kBAAZ,CAAgCM,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEb,uBADC,CAEXc,SAAS,CAAE,yBAFA,CAAf,EAIA,GAAK/C,KAAK,CAACsB,QAAN,CAAiBtB,KAAK,CAACqB,KAAxB,CAAkCzB,OAAO,CAAG,CAAhD,CAAoD,CAAE;AAClDiD,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEJ,kBAAZ,CAAgCK,SAAS,CAAE,YAA3C,CAAf,EACH,CACJ,CAlCD,IAkCO,CACHvB,OAAO,CAAG,4BAAV,CACA;AAEAqB,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAER,kBAAZ,CAAgCS,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEhB,eADC,CAEXiB,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEP,kBAAZ,CAAgCQ,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEf,gBADC,CAEXgB,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEN,kBAAZ,CAAgCO,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEd,oBADC,CAEXe,SAAS,CAAE,yBAFA,CAAf,EAIAF,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEL,kBAAZ,CAAgCM,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACnC,IAAV,CAAe,CACXoC,QAAQ,CAAEb,uBADC,CAEXc,SAAS,CAAE,yBAFA,CAAf,EAKA,GAAK/C,KAAK,CAACsB,QAAN,CAAiBtB,KAAK,CAACqB,KAAxB,CAAkCzB,OAAO,CAAG,CAAhD,CAAoD,CAAE;AAClDiD,SAAS,CAACnC,IAAV,CAAe,CAAEoC,QAAQ,CAAEJ,kBAAZ,CAAgCK,SAAS,CAAE,YAA3C,CAAf,EACH,CACJ,CAED,mBACI,2BAEK/C,KAAK,CAACC,UAAN,eACG,YAAK,SAAS,gBAAUuB,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAEqB,SADX,CAEI,aAAa,CAAE7C,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B,CAFnB,EADJ,EAHR,CAUK,CAACrB,KAAK,CAACC,UAAP,eACG,YAAK,SAAS,gBAAUuB,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAEqB,SADX,CAEI,aAAa,CAAE7C,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B,CAFnB,EADJ,EAXR,GADJ,CAqBH,CAvJD,CA2JA,cAAetB,QAAf"}, "metadata": {}, "sourceType": "module"}