{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\n\nvar useRefreshOnWindowFocusPlugin = function useRefreshOnWindowFocusPlugin(fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n      _b = _a.focusTimespan,\n      focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef();\n\n  var stopSubscribe = function stopSubscribe() {\n    var _a;\n\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\n\nexport default useRefreshOnWindowFocusPlugin;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmount", "limit", "subscribeFocus", "useRefreshOnWindowFocusPlugin", "fetchInstance", "_a", "refreshOnWindowFocus", "_b", "focusTimespan", "unsubscribeRef", "stopSubscribe", "current", "call", "limitRefresh_1", "refresh", "bind"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport useUnmount from '../../../useUnmount';\nimport limit from '../utils/limit';\nimport subscribeFocus from '../utils/subscribeFocus';\nvar useRefreshOnWindowFocusPlugin = function useRefreshOnWindowFocusPlugin(fetchInstance, _a) {\n  var refreshOnWindowFocus = _a.refreshOnWindowFocus,\n    _b = _a.focusTimespan,\n    focusTimespan = _b === void 0 ? 5000 : _b;\n  var unsubscribeRef = useRef();\n  var stopSubscribe = function stopSubscribe() {\n    var _a;\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useEffect(function () {\n    if (refreshOnWindowFocus) {\n      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);\n      unsubscribeRef.current = subscribeFocus(function () {\n        limitRefresh_1();\n      });\n    }\n    return function () {\n      stopSubscribe();\n    };\n  }, [refreshOnWindowFocus, focusTimespan]);\n  useUnmount(function () {\n    stopSubscribe();\n  });\n  return {};\n};\nexport default useRefreshOnWindowFocusPlugin;"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;AACA,OAAOC,UAAP,MAAuB,qBAAvB;AACA,OAAOC,KAAP,MAAkB,gBAAlB;AACA,OAAOC,cAAP,MAA2B,yBAA3B;;AACA,IAAIC,6BAA6B,GAAG,SAASA,6BAAT,CAAuCC,aAAvC,EAAsDC,EAAtD,EAA0D;EAC5F,IAAIC,oBAAoB,GAAGD,EAAE,CAACC,oBAA9B;EAAA,IACEC,EAAE,GAAGF,EAAE,CAACG,aADV;EAAA,IAEEA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EAFzC;EAGA,IAAIE,cAAc,GAAGV,MAAM,EAA3B;;EACA,IAAIW,aAAa,GAAG,SAASA,aAAT,GAAyB;IAC3C,IAAIL,EAAJ;;IACA,CAACA,EAAE,GAAGI,cAAc,CAACE,OAArB,MAAkC,IAAlC,IAA0CN,EAAE,KAAK,KAAK,CAAtD,GAA0D,KAAK,CAA/D,GAAmEA,EAAE,CAACO,IAAH,CAAQH,cAAR,CAAnE;EACD,CAHD;;EAIAX,SAAS,CAAC,YAAY;IACpB,IAAIQ,oBAAJ,EAA0B;MACxB,IAAIO,cAAc,GAAGZ,KAAK,CAACG,aAAa,CAACU,OAAd,CAAsBC,IAAtB,CAA2BX,aAA3B,CAAD,EAA4CI,aAA5C,CAA1B;MACAC,cAAc,CAACE,OAAf,GAAyBT,cAAc,CAAC,YAAY;QAClDW,cAAc;MACf,CAFsC,CAAvC;IAGD;;IACD,OAAO,YAAY;MACjBH,aAAa;IACd,CAFD;EAGD,CAVQ,EAUN,CAACJ,oBAAD,EAAuBE,aAAvB,CAVM,CAAT;EAWAR,UAAU,CAAC,YAAY;IACrBU,aAAa;EACd,CAFS,CAAV;EAGA,OAAO,EAAP;AACD,CAxBD;;AAyBA,eAAeP,6BAAf"}, "metadata": {}, "sourceType": "module"}