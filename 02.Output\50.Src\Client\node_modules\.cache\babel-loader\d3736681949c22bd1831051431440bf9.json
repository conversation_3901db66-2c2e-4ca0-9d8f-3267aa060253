{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n      m = s && o[s],\n      i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nimport { useRef, useEffect } from 'react';\n\nvar EventEmitter =\n/** @class */\nfunction () {\n  function EventEmitter() {\n    var _this = this;\n\n    this.subscriptions = new Set();\n\n    this.emit = function (val) {\n      var e_1, _a;\n\n      try {\n        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var subscription = _c.value;\n          subscription(val);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b[\"return\"])) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    };\n\n    this.useSubscription = function (callback) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      var callbackRef = useRef();\n      callbackRef.current = callback; // eslint-disable-next-line react-hooks/rules-of-hooks\n\n      useEffect(function () {\n        function subscription(val) {\n          if (callbackRef.current) {\n            callbackRef.current(val);\n          }\n        }\n\n        _this.subscriptions.add(subscription);\n\n        return function () {\n          _this.subscriptions[\"delete\"](subscription);\n        };\n      }, []);\n    };\n  }\n\n  return EventEmitter;\n}();\n\nexport { EventEmitter };\nexport default function useEventEmitter() {\n  var ref = useRef();\n\n  if (!ref.current) {\n    ref.current = new EventEmitter();\n  }\n\n  return ref.current;\n}", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "useRef", "useEffect", "EventEmitter", "_this", "subscriptions", "Set", "emit", "val", "e_1", "_a", "_b", "_c", "subscription", "e_1_1", "error", "useSubscription", "callback", "callback<PERSON><PERSON>", "current", "add", "useEventEmitter", "ref"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useEventEmitter/index.js"], "sourcesContent": ["var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { useRef, useEffect } from 'react';\nvar EventEmitter = /** @class */function () {\n  function EventEmitter() {\n    var _this = this;\n    this.subscriptions = new Set();\n    this.emit = function (val) {\n      var e_1, _a;\n      try {\n        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var subscription = _c.value;\n          subscription(val);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b[\"return\"])) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    };\n    this.useSubscription = function (callback) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      var callbackRef = useRef();\n      callbackRef.current = callback;\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useEffect(function () {\n        function subscription(val) {\n          if (callbackRef.current) {\n            callbackRef.current(val);\n          }\n        }\n        _this.subscriptions.add(subscription);\n        return function () {\n          _this.subscriptions[\"delete\"](subscription);\n        };\n      }, []);\n    };\n  }\n  return EventEmitter;\n}();\nexport { EventEmitter };\nexport default function useEventEmitter() {\n  var ref = useRef();\n  if (!ref.current) {\n    ref.current = new EventEmitter();\n  }\n  return ref.current;\n}"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,UAAUC,CAAV,EAAa;EACnD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA/C;EAAA,IACEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAD,CADZ;EAAA,IAEEI,CAAC,GAAG,CAFN;EAGA,IAAID,CAAJ,EAAO,OAAOA,CAAC,CAACE,IAAF,CAAON,CAAP,CAAP;EACP,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAT,KAAoB,QAA7B,EAAuC,OAAO;IAC5CC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAhB,EAAwBP,CAAC,GAAG,KAAK,CAAT;MACxB,OAAO;QACLS,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAF,CADR;QAELK,IAAI,EAAE,CAACV;MAFF,CAAP;IAID;EAP2C,CAAP;EASvC,MAAM,IAAIW,SAAJ,CAAcV,CAAC,GAAG,yBAAH,GAA+B,iCAA9C,CAAN;AACD,CAfD;;AAgBA,SAASW,MAAT,EAAiBC,SAAjB,QAAkC,OAAlC;;AACA,IAAIC,YAAY;AAAG;AAAa,YAAY;EAC1C,SAASA,YAAT,GAAwB;IACtB,IAAIC,KAAK,GAAG,IAAZ;;IACA,KAAKC,aAAL,GAAqB,IAAIC,GAAJ,EAArB;;IACA,KAAKC,IAAL,GAAY,UAAUC,GAAV,EAAe;MACzB,IAAIC,GAAJ,EAASC,EAAT;;MACA,IAAI;QACF,KAAK,IAAIC,EAAE,GAAGvB,QAAQ,CAACgB,KAAK,CAACC,aAAP,CAAjB,EAAwCO,EAAE,GAAGD,EAAE,CAACd,IAAH,EAAlD,EAA6D,CAACe,EAAE,CAACb,IAAjE,EAAuEa,EAAE,GAAGD,EAAE,CAACd,IAAH,EAA5E,EAAuF;UACrF,IAAIgB,YAAY,GAAGD,EAAE,CAACd,KAAtB;UACAe,YAAY,CAACL,GAAD,CAAZ;QACD;MACF,CALD,CAKE,OAAOM,KAAP,EAAc;QACdL,GAAG,GAAG;UACJM,KAAK,EAAED;QADH,CAAN;MAGD,CATD,SASU;QACR,IAAI;UACF,IAAIF,EAAE,IAAI,CAACA,EAAE,CAACb,IAAV,KAAmBW,EAAE,GAAGC,EAAE,CAAC,QAAD,CAA1B,CAAJ,EAA2CD,EAAE,CAACf,IAAH,CAAQgB,EAAR;QAC5C,CAFD,SAEU;UACR,IAAIF,GAAJ,EAAS,MAAMA,GAAG,CAACM,KAAV;QACV;MACF;IACF,CAlBD;;IAmBA,KAAKC,eAAL,GAAuB,UAAUC,QAAV,EAAoB;MACzC;MACA,IAAIC,WAAW,GAAGjB,MAAM,EAAxB;MACAiB,WAAW,CAACC,OAAZ,GAAsBF,QAAtB,CAHyC,CAIzC;;MACAf,SAAS,CAAC,YAAY;QACpB,SAASW,YAAT,CAAsBL,GAAtB,EAA2B;UACzB,IAAIU,WAAW,CAACC,OAAhB,EAAyB;YACvBD,WAAW,CAACC,OAAZ,CAAoBX,GAApB;UACD;QACF;;QACDJ,KAAK,CAACC,aAAN,CAAoBe,GAApB,CAAwBP,YAAxB;;QACA,OAAO,YAAY;UACjBT,KAAK,CAACC,aAAN,CAAoB,QAApB,EAA8BQ,YAA9B;QACD,CAFD;MAGD,CAVQ,EAUN,EAVM,CAAT;IAWD,CAhBD;EAiBD;;EACD,OAAOV,YAAP;AACD,CA1C+B,EAAhC;;AA2CA,SAASA,YAAT;AACA,eAAe,SAASkB,eAAT,GAA2B;EACxC,IAAIC,GAAG,GAAGrB,MAAM,EAAhB;;EACA,IAAI,CAACqB,GAAG,CAACH,OAAT,EAAkB;IAChBG,GAAG,CAACH,OAAJ,GAAc,IAAIhB,YAAJ,EAAd;EACD;;EACD,OAAOmB,GAAG,CAACH,OAAX;AACD"}, "metadata": {}, "sourceType": "module"}