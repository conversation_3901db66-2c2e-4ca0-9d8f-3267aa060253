{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import ArrayScroll from'./elements/ArrayScroll';import{getCellFace,isValidSource,getHexColor}from'../utils/Util.js';/**\r\n * 簡易事案コンテンツ(1-2サイズ)<br>\r\n * propsは、「3.6簡易事案コンテンツ(1-2サイズ)情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module CaseHalf\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var CaseHalf=function CaseHalf(props){var MAX_ROW=2;return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&props.case.map(function(item,index){//・1コンテンツあたり最大2件の情報を表示可能。\nif(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(CaseHalfRow,{item:item,index:index},index);})});};/**\r\n * 簡易事案コンテンツ(1-2サイズ)の一事案\r\n * auto-cols-fr で列を同幅にする\r\n */var CaseHalfRow=function CaseHalfRow(props){var _props$item,_props$item$car_name,_props$item2,_props$item2$car_name;var row1Cell1Props,row1Cell2Props,row1Cell3Props,row1Props;var row2Cell1Props,row2Cell2Props;var Max_Cars=8;var data=props===null||props===void 0?void 0:props.item;var obj=data===null||data===void 0?void 0:data.disaster_class;row1Props={backgroundColor:getHexColor(obj===null||obj===void 0?void 0:obj.background_color)};row1Cell1Props=getCellFace(obj===null||obj===void 0?void 0:obj.disaster_type,'col-span-4');row1Cell2Props=getCellFace(obj===null||obj===void 0?void 0:obj.case_no,'col-span-7 col-start-6');row1Cell3Props=getCellFace(obj===null||obj===void 0?void 0:obj.fire_station_name,'col-span-4 col-start-14');row2Cell1Props=getCellFace(data===null||data===void 0?void 0:data.awareness_time,'col-span-8 col-start-2 w-fit');row2Cell2Props=getCellFace(data===null||data===void 0?void 0:data.town_name,'col-span-10 col-start-10 w-fit');return/*#__PURE__*/_jsxs(\"div\",{className:\"leading-[1]\",children:[/*#__PURE__*/_jsxs(\"div\",{style:row1Props,className:\"border-transparent border-x-[1rem] grid grid-cols-19 auto-cols-fr text-6xl\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell2Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell3Props))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-[minmax(6rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(6rem,2fr)] auto-cols-fr my-[3.1rem] text-5xl\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell2Props))]}),/*#__PURE__*/_jsx(ArrayScroll,{max:Max_Cars,gridLevelProps:'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr mb-[3rem] gap-y-[1.2rem] gap-x-[1.2rem] text-6xl',cellLevelProps:['col-span-4 col-start-2','col-span-4','col-span-4','col-span-4'],display_data:props===null||props===void 0?void 0:(_props$item=props.item)===null||_props$item===void 0?void 0:(_props$item$car_name=_props$item.car_name)===null||_props$item$car_name===void 0?void 0:_props$item$car_name.display_data,change_setting:props===null||props===void 0?void 0:(_props$item2=props.item)===null||_props$item2===void 0?void 0:(_props$item2$car_name=_props$item2.car_name)===null||_props$item2$car_name===void 0?void 0:_props$item2$car_name.change_setting})]});};export default CaseHalf;", "map": {"version": 3, "names": ["React", "Cell", "ArrayScroll", "getCellFace", "isValidSource", "getHexColor", "CaseHalf", "props", "MAX_ROW", "case", "map", "item", "index", "undefined", "CaseHalfRow", "row1Cell1Props", "row1Cell2Props", "row1Cell3Props", "row1Props", "row2Cell1Props", "row2Cell2Props", "Max_Cars", "data", "obj", "disaster_class", "backgroundColor", "background_color", "disaster_type", "case_no", "fire_station_name", "awareness_time", "town_name", "car_name", "display_data", "change_setting"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/CaseHalf.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport ArrayScroll from './elements/ArrayScroll';\r\nimport { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';\r\n\r\n/**\r\n * 簡易事案コンテンツ(1-2サイズ)<br>\r\n * propsは、「3.6簡易事案コンテンツ(1-2サイズ)情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module CaseHalf\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst CaseHalf = (props) => {\r\n  const MAX_ROW = 2;\r\n  return (\r\n    <>\r\n      {isValidSource(props) &&\r\n        props.case.map((item, index) => {\r\n          //・1コンテンツあたり最大2件の情報を表示可能。\r\n          if (index >= MAX_ROW) return undefined;\r\n\r\n          return <CaseHalfRow key={index} item={item} index={index} />;\r\n        })}\r\n    </>\r\n  );\r\n};\r\n\r\n/**\r\n * 簡易事案コンテンツ(1-2サイズ)の一事案\r\n * auto-cols-fr で列を同幅にする\r\n */\r\nconst CaseHalfRow = (props) => {\r\n  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;\r\n  let row2Cell1Props, row2Cell2Props;\r\n  const Max_Cars = 8;\r\n\r\n  const data = props?.item;\r\n\r\n  let obj = data?.disaster_class;\r\n  row1Props = { backgroundColor: getHexColor(obj?.background_color) };\r\n  row1Cell1Props = getCellFace(obj?.disaster_type, 'col-span-4');\r\n  row1Cell2Props = getCellFace(obj?.case_no, 'col-span-7 col-start-6');\r\n  row1Cell3Props = getCellFace(\r\n    obj?.fire_station_name,\r\n    'col-span-4 col-start-14'\r\n  );\r\n\r\n  row2Cell1Props = getCellFace(data?.awareness_time, 'col-span-8 col-start-2 w-fit');\r\n  row2Cell2Props = getCellFace(\r\n    data?.town_name,\r\n    'col-span-10 col-start-10 w-fit'\r\n  );\r\n\r\n  return (\r\n    <div className=\"leading-[1]\">\r\n      <div\r\n        style={row1Props}\r\n        className=\"border-transparent border-x-[1rem] grid grid-cols-19 auto-cols-fr text-6xl\"\r\n      >\r\n        <Cell {...row1Cell1Props} />\r\n        <Cell {...row1Cell2Props} />\r\n        <Cell {...row1Cell3Props} />\r\n      </div>\r\n      <div className=\"grid grid-cols-[minmax(6rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(6rem,2fr)] auto-cols-fr my-[3.1rem] text-5xl\">\r\n        <Cell {...row2Cell1Props} />\r\n        <Cell {...row2Cell2Props} />\r\n      </div>\r\n\r\n      <ArrayScroll\r\n        max={Max_Cars}\r\n        gridLevelProps={\r\n          'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr mb-[3rem] gap-y-[1.2rem] gap-x-[1.2rem] text-6xl'\r\n        }\r\n        cellLevelProps={[\r\n          'col-span-4 col-start-2',\r\n          'col-span-4',\r\n          'col-span-4',\r\n          'col-span-4',\r\n        ]}\r\n        display_data={props?.item?.car_name?.display_data}\r\n        change_setting={props?.item?.car_name?.change_setting}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CaseHalf;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,YAAP,KAAwB,wBAAxB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,SAAQ,CAAG,QAAXA,SAAW,CAACC,KAAD,CAAW,CAC1B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,yBACGJ,aAAa,CAACG,KAAD,CAAb,EACCA,KAAK,CAACE,IAAN,CAAWC,GAAX,CAAe,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC9B;AACA,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,WAAD,EAAyB,IAAI,CAAEF,IAA/B,CAAqC,KAAK,CAAEC,KAA5C,EAAkBA,KAAlB,CAAP,CACD,CALD,CAFJ,EADF,CAWD,CAbD,CAeA;AACA;AACA;AACA,GACA,GAAME,YAAW,CAAG,QAAdA,YAAc,CAACP,KAAD,CAAW,yEAC7B,GAAIQ,eAAJ,CAAoBC,cAApB,CAAoCC,cAApC,CAAoDC,SAApD,CACA,GAAIC,eAAJ,CAAoBC,cAApB,CACA,GAAMC,SAAQ,CAAG,CAAjB,CAEA,GAAMC,KAAI,CAAGf,KAAH,SAAGA,KAAH,iBAAGA,KAAK,CAAEI,IAApB,CAEA,GAAIY,IAAG,CAAGD,IAAH,SAAGA,IAAH,iBAAGA,IAAI,CAAEE,cAAhB,CACAN,SAAS,CAAG,CAAEO,eAAe,CAAEpB,WAAW,CAACkB,GAAD,SAACA,GAAD,iBAACA,GAAG,CAAEG,gBAAN,CAA9B,CAAZ,CACAX,cAAc,CAAGZ,WAAW,CAACoB,GAAD,SAACA,GAAD,iBAACA,GAAG,CAAEI,aAAN,CAAqB,YAArB,CAA5B,CACAX,cAAc,CAAGb,WAAW,CAACoB,GAAD,SAACA,GAAD,iBAACA,GAAG,CAAEK,OAAN,CAAe,wBAAf,CAA5B,CACAX,cAAc,CAAGd,WAAW,CAC1BoB,GAD0B,SAC1BA,GAD0B,iBAC1BA,GAAG,CAAEM,iBADqB,CAE1B,yBAF0B,CAA5B,CAKAV,cAAc,CAAGhB,WAAW,CAACmB,IAAD,SAACA,IAAD,iBAACA,IAAI,CAAEQ,cAAP,CAAuB,8BAAvB,CAA5B,CACAV,cAAc,CAAGjB,WAAW,CAC1BmB,IAD0B,SAC1BA,IAD0B,iBAC1BA,IAAI,CAAES,SADoB,CAE1B,gCAF0B,CAA5B,CAKA,mBACE,aAAK,SAAS,CAAC,aAAf,wBACE,aACE,KAAK,CAAEb,SADT,CAEE,SAAS,CAAC,4EAFZ,wBAIE,KAAC,IAAD,kBAAUH,cAAV,EAJF,cAKE,KAAC,IAAD,kBAAUC,cAAV,EALF,cAME,KAAC,IAAD,kBAAUC,cAAV,EANF,GADF,cASE,aAAK,SAAS,CAAC,4HAAf,wBACE,KAAC,IAAD,kBAAUE,cAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,cAAV,EAFF,GATF,cAcE,KAAC,WAAD,EACE,GAAG,CAAEC,QADP,CAEE,cAAc,CACZ,+KAHJ,CAKE,cAAc,CAAE,CACd,wBADc,CAEd,YAFc,CAGd,YAHc,CAId,YAJc,CALlB,CAWE,YAAY,CAAEd,KAAF,SAAEA,KAAF,8BAAEA,KAAK,CAAEI,IAAT,4DAAE,YAAaqB,QAAf,+CAAE,qBAAuBC,YAXvC,CAYE,cAAc,CAAE1B,KAAF,SAAEA,KAAF,+BAAEA,KAAK,CAAEI,IAAT,8DAAE,aAAaqB,QAAf,gDAAE,sBAAuBE,cAZzC,EAdF,GADF,CA+BD,CArDD,CAuDA,cAAe5B,SAAf"}, "metadata": {}, "sourceType": "module"}