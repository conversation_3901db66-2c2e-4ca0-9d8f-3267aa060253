{"ast": null, "code": "import { useEffect, useRef } from 'react';\n\nvar useUnmountedRef = function useUnmountedRef() {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\n\nexport default useUnmountedRef;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmountedRef", "unmountedRef", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useUnmountedRef/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nvar useUnmountedRef = function useUnmountedRef() {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\nexport default useUnmountedRef;"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;;AACA,IAAIC,eAAe,GAAG,SAASA,eAAT,GAA2B;EAC/C,IAAIC,YAAY,GAAGF,MAAM,CAAC,KAAD,CAAzB;EACAD,SAAS,CAAC,YAAY;IACpBG,YAAY,CAACC,OAAb,GAAuB,KAAvB;IACA,OAAO,YAAY;MACjBD,YAAY,CAACC,OAAb,GAAuB,IAAvB;IACD,CAFD;EAGD,CALQ,EAKN,EALM,CAAT;EAMA,OAAOD,YAAP;AACD,CATD;;AAUA,eAAeD,eAAf"}, "metadata": {}, "sourceType": "module"}