{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\ExtendedVehicle.js\";\nimport React from 'react';\nimport CellBox from './elements/CellBox';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MAX_ROW = 50;\nconst GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\nconst GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\n/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */\n\nconst ExtendedVehicle = props => {\n  if (!isValidSource(props)) return;\n  let showDelopy = props.is_deployment === 1;\n  let totalRowCounter = 0;\n  let targetArray = [];\n  if (!props.title_name) return; //最大MaxRowのデータを取り出す\n\n  for (const item of props.title_name) {\n    if (!item.car_name) return; // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\n\n    let subRowCounter = item.car_name.length + 1;\n\n    if (totalRowCounter + subRowCounter <= MAX_ROW) {\n      let newObj = {\n        display_text: item.display_text,\n        text_color: item.text_color,\n        background_color: item.background_color,\n        car_name: item.car_name,\n        town_name: item.town_name,\n        disaster_type: item.disaster_type,\n        avm_dynamic_state: item.avm_dynamic_state,\n        deployment: item.deployment,\n        lighting_setting: item.lighting_setting\n      };\n      targetArray.push(newObj);\n      totalRowCounter = totalRowCounter + subRowCounter;\n    }\n  }\n\n  let nextStartRow = 1;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isValidSource(props) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.3rem] leading-[1] gap-x-[2.25rem] gap-y-[0.4rem]\",\n      children: targetArray.map((item, index) => {\n        var _item$car_name;\n\n        let startRow = nextStartRow; //現在のStart\n\n        nextStartRow = nextStartRow + (item === null || item === void 0 ? void 0 : (_item$car_name = item.car_name) === null || _item$car_name === void 0 ? void 0 : _item$car_name.length) + 1; //次のStartRowを計算\n\n        return /*#__PURE__*/_jsxDEV(ExtendedStation, { ...item,\n          index: index,\n          showDelopy: showDelopy,\n          startRow: startRow\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */\n\n\n_c = ExtendedVehicle;\n\nconst ExtendedStation = props => {\n  let gridCol;\n  let subTitleSpan = 'col-span-full';\n\n  if (props.showDelopy) {\n    gridCol = 'grid-cols-extended-vehicle-deploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n  } else {\n    gridCol = 'grid-cols-extended-vehicle-nodeploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n  }\n\n  const subTitleProp = getCellFace(props, `${subTitleSpan} flex flex-col items-center`);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(CellBox, { ...subTitleProp,\n        children: props.display_text && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: props.display_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this), props.car_name.map((item, index) => {\n      return /*#__PURE__*/_jsxDEV(ExtendedVehicleDetailRow, { ...props,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 24\n      }, this);\n    })]\n  }, void 0, true);\n};\n/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */\n\n\n_c2 = ExtendedStation;\n\nconst ExtendedVehicleDetailRow = props => {\n  let showInfoSeperator0 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator1 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator2 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator3 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator4 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator5 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoDeployment = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoCarName = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoTownName = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoDisasterType = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoAvmDynamicState = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n\n  if (props.deployment && props.deployment[props.index]) {\n    showInfoDeployment = props.deployment[props.index];\n  }\n\n  if (props.car_name && props.car_name[props.index]) {\n    showInfoCarName = props.car_name[props.index];\n  }\n\n  if (props.town_name && props.town_name[props.index]) {\n    showInfoTownName = props.town_name[props.index];\n  }\n\n  if (props.disaster_type && props.disaster_type[props.index]) {\n    showInfoDisasterType = props.disaster_type[props.index];\n  }\n\n  if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {\n    showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\n  } //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\n\n\n  let baseObj = props.car_name[props.index];\n\n  if (!baseObj) {\n    baseObj = props.town_name[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.disaster_type[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.avm_dynamic_state[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.deployment[props.index];\n  }\n\n  showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\n  showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\n  showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\n  showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\n  showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\n  showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);\n  showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);\n  showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);\n  showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);\n  showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);\n  showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);\n  let gridCol;\n  let showBlock = [];\n\n  if (props.showDelopy) {\n    gridCol = 'grid-cols-extended-vehicle-deploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n\n    if (props.startRow + props.index <= MAX_ROW / 2) {\n      //左半分\n      showBlock.push({\n        showInfo: showInfoSeperator0,\n        className: 'col-span-1'\n      });\n    }\n\n    showBlock.push({\n      showInfo: showInfoDeployment,\n      className: 'col-span-1 col-start-2'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator1,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoCarName,\n      className: 'col-span-4 col-start-4'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator2,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoTownName,\n      className: 'col-span-6 col-start-9'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator3,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoDisasterType,\n      className: 'col-span-2 col-start-16'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator4,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoAvmDynamicState,\n      className: 'col-span-2 col-start-19'\n    });\n\n    if (props.startRow + props.index <= MAX_ROW / 2) {\n      //左半分\n      showBlock.push({\n        showInfo: showInfoSeperator5,\n        className: 'col-span-1'\n      });\n    }\n  } else {\n    gridCol = 'grid-cols-extended-vehicle-nodeploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n\n    if (props.startRow + props.index <= MAX_ROW / 2) {\n      //左半分\n      showBlock.push({\n        showInfo: showInfoSeperator0,\n        className: 'col-span-1'\n      });\n    }\n\n    showBlock.push({\n      showInfo: showInfoCarName,\n      className: 'col-span-4 col-start-2'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator1,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoTownName,\n      className: 'col-span-6 col-start-7'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator2,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoDisasterType,\n      className: 'col-span-2 col-start-14'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator3,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoAvmDynamicState,\n      className: 'col-span-2 col-start-17'\n    });\n\n    if (props.startRow + props.index <= MAX_ROW / 2) {\n      //左半分\n      showBlock.push({\n        showInfo: showInfoSeperator4,\n        className: 'col-span-1'\n      });\n    }\n  }\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [props.showDelopy && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(BlinkBlock, {\n        block: showBlock,\n        blink_setting: props.lighting_setting[props.index]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 17\n    }, this), !props.showDelopy && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(BlinkBlock, {\n        block: showBlock,\n        blink_setting: props.lighting_setting[props.index]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true);\n};\n\n_c3 = ExtendedVehicleDetailRow;\nexport default ExtendedVehicle;\n\nvar _c, _c2, _c3;\n\n$RefreshReg$(_c, \"ExtendedVehicle\");\n$RefreshReg$(_c2, \"ExtendedStation\");\n$RefreshReg$(_c3, \"ExtendedVehicleDetailRow\");", "map": {"version": 3, "names": ["React", "CellBox", "getCellFace", "isValidSource", "BlinkBlock", "checkBlinkInfo", "MAX_ROW", "GRID_COLS_EXTENDED_VEHICLE_DEPLOY", "GRID_COLS_EXTENDED_VEHICLE_NODEPLOY", "ExtendedVehicle", "props", "showDelopy", "is_deployment", "totalRowCounter", "targetArray", "title_name", "item", "car_name", "subRowCounter", "length", "newObj", "display_text", "text_color", "background_color", "town_name", "disaster_type", "avm_dynamic_state", "deployment", "lighting_setting", "push", "nextStartRow", "map", "index", "startRow", "ExtendedStation", "gridCol", "subTitleSpan", "subTitleProp", "ExtendedVehicleDetailRow", "showInfoSeperator0", "showInfoSeperator1", "showInfoSeperator2", "showInfoSeperator3", "showInfoSeperator4", "showInfoSeperator5", "showInfoDeployment", "showInfoCarName", "showInfoTownName", "showInfoDisasterType", "showInfoAvmDynamicState", "baseObj", "showBlock", "showInfo", "className"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/ExtendedVehicle.js"], "sourcesContent": ["import React from 'react';\nimport CellBox from './elements/CellBox';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\n\nconst MAX_ROW = 50;\nconst GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\nconst GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\n\n/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */\nconst ExtendedVehicle = (props) => {\n    if (!isValidSource(props)) return;\n\n    let showDelopy = props.is_deployment === 1;\n\n    let totalRowCounter = 0;\n\n    let targetArray = [];\n\n    if (!props.title_name)\n        return;\n    \n    //最大MaxRowのデータを取り出す\n    for (const item of props.title_name) {\n        \n        if (!item.car_name) \n            return;\n\n        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\n        let subRowCounter = item.car_name.length + 1;\n\n        if (totalRowCounter + subRowCounter <= MAX_ROW) {\n            let newObj = {\n                display_text: item.display_text,\n                text_color: item.text_color,\n                background_color: item.background_color,\n                car_name: item.car_name,\n                town_name: item.town_name,\n                disaster_type: item.disaster_type,\n                avm_dynamic_state: item.avm_dynamic_state,\n                deployment: item.deployment,\n                lighting_setting: item.lighting_setting,\n            };\n\n            targetArray.push(newObj);\n            totalRowCounter = totalRowCounter + subRowCounter;\n        }\n    }\n\n    let nextStartRow = 1;\n    return (\n        <>\n            {isValidSource(props) && (\n                <div className=\"grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.3rem] leading-[1] gap-x-[2.25rem] gap-y-[0.4rem]\">\n                    {targetArray.map((item, index) => {\n                        let startRow = nextStartRow; //現在のStart\n                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算\n\n                        return (\n                            <ExtendedStation\n                                key={index}\n                                {...item}\n                                index={index}\n                                showDelopy={showDelopy}\n                                startRow={startRow}\n                            />\n                        );\n                    })}\n                </div>\n            )}\n        </>\n    );\n};\n\n/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedStation = (props) => {\n    let gridCol;\n    let subTitleSpan = 'col-span-full';\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n    }\n    const subTitleProp = getCellFace(\n        props,\n        `${subTitleSpan} flex flex-col items-center`\n    );\n\n    return (\n        <>\n            <div className={`grid ${gridCol}`}>\n                <CellBox {...subTitleProp}>\n                    {props.display_text && <span>{props.display_text}</span>}\n                </CellBox>\n            </div>\n            {props.car_name.map((item, index) => {\n                return <ExtendedVehicleDetailRow key={index} {...props} index={index} />;\n            })}\n        </>\n    );\n};\n\n/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedVehicleDetailRow = (props) => {\n    let showInfoSeperator0 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator1 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator2 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator3 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator4 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator5 = { display_text: '', text_color: '', background_color: '' };\n\n    let showInfoDeployment = { display_text: '', text_color: '', background_color: '' };\n    let showInfoCarName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoTownName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoDisasterType = { display_text: '', text_color: '', background_color: '' };\n    let showInfoAvmDynamicState = { display_text: '', text_color: '', background_color: '' };\n\n    if (props.deployment && props.deployment[props.index]) {\n        showInfoDeployment = props.deployment[props.index];\n    }\n\n    if (props.car_name && props.car_name[props.index]) {\n        showInfoCarName = props.car_name[props.index];\n    }\n\n    if (props.town_name && props.town_name[props.index]) {\n        showInfoTownName = props.town_name[props.index];\n    }\n\n    if (props.disaster_type && props.disaster_type[props.index]) {\n        showInfoDisasterType = props.disaster_type[props.index];\n    }\n\n    if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {\n        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\n    }\n\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\n    let baseObj = props.car_name[props.index];\n    if (!baseObj) { baseObj = props.town_name[props.index] }\n    if (!baseObj) { baseObj = props.disaster_type[props.index] }\n    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }\n    if (!baseObj) { baseObj = props.deployment[props.index] }\n\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\n\n    showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);\n    showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);\n    showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);\n    showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);\n    showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);\n    showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);\n\n    let gridCol;\n    let showBlock = [];\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\n        }\n        showBlock.push({\n            showInfo: showInfoDeployment,\n            className: 'col-span-1 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-4',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-9',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-16',\n        });\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-19',\n        });\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\n        }\n\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\n        }\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-7',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-14',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-17',\n        });\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        }\n\n    }\n\n    return (\n        <>\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\n            {props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n            {!props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n        </>\n    );\n};\n\n\n\nexport default ExtendedVehicle;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,OAAP,MAAoB,oBAApB;AACA,SAASC,WAAT,EAAsBC,aAAtB,QAA2C,kBAA3C;AACA,OAAOC,UAAP,IAAqBC,cAArB,QAA2C,uBAA3C;;;AAEA,MAAMC,OAAO,GAAG,EAAhB;AACA,MAAMC,iCAAiC,GAAG,gKAA1C;AACA,MAAMC,mCAAmC,GAAG,wIAA5C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAIC,KAAD,IAAW;EAC/B,IAAI,CAACP,aAAa,CAACO,KAAD,CAAlB,EAA2B;EAE3B,IAAIC,UAAU,GAAGD,KAAK,CAACE,aAAN,KAAwB,CAAzC;EAEA,IAAIC,eAAe,GAAG,CAAtB;EAEA,IAAIC,WAAW,GAAG,EAAlB;EAEA,IAAI,CAACJ,KAAK,CAACK,UAAX,EACI,OAV2B,CAY/B;;EACA,KAAK,MAAMC,IAAX,IAAmBN,KAAK,CAACK,UAAzB,EAAqC;IAEjC,IAAI,CAACC,IAAI,CAACC,QAAV,EACI,OAH6B,CAKjC;;IACA,IAAIC,aAAa,GAAGF,IAAI,CAACC,QAAL,CAAcE,MAAd,GAAuB,CAA3C;;IAEA,IAAIN,eAAe,GAAGK,aAAlB,IAAmCZ,OAAvC,EAAgD;MAC5C,IAAIc,MAAM,GAAG;QACTC,YAAY,EAAEL,IAAI,CAACK,YADV;QAETC,UAAU,EAAEN,IAAI,CAACM,UAFR;QAGTC,gBAAgB,EAAEP,IAAI,CAACO,gBAHd;QAITN,QAAQ,EAAED,IAAI,CAACC,QAJN;QAKTO,SAAS,EAAER,IAAI,CAACQ,SALP;QAMTC,aAAa,EAAET,IAAI,CAACS,aANX;QAOTC,iBAAiB,EAAEV,IAAI,CAACU,iBAPf;QAQTC,UAAU,EAAEX,IAAI,CAACW,UARR;QASTC,gBAAgB,EAAEZ,IAAI,CAACY;MATd,CAAb;MAYAd,WAAW,CAACe,IAAZ,CAAiBT,MAAjB;MACAP,eAAe,GAAGA,eAAe,GAAGK,aAApC;IACH;EACJ;;EAED,IAAIY,YAAY,GAAG,CAAnB;EACA,oBACI;IAAA,UACK3B,aAAa,CAACO,KAAD,CAAb,iBACG;MAAK,SAAS,EAAC,sGAAf;MAAA,UACKI,WAAW,CAACiB,GAAZ,CAAgB,CAACf,IAAD,EAAOgB,KAAP,KAAiB;QAAA;;QAC9B,IAAIC,QAAQ,GAAGH,YAAf,CAD8B,CACD;;QAC7BA,YAAY,GAAGA,YAAY,IAAGd,IAAH,aAAGA,IAAH,yCAAGA,IAAI,CAAEC,QAAT,mDAAG,eAAgBE,MAAnB,CAAZ,GAAwC,CAAvD,CAF8B,CAE4B;;QAE1D,oBACI,QAAC,eAAD,OAEQH,IAFR;UAGI,KAAK,EAAEgB,KAHX;UAII,UAAU,EAAErB,UAJhB;UAKI,QAAQ,EAAEsB;QALd,GACSD,KADT;UAAA;UAAA;UAAA;QAAA,QADJ;MASH,CAbA;IADL;MAAA;MAAA;MAAA;IAAA;EAFR,iBADJ;AAsBH,CA9DD;AAgEA;AACA;AACA;AACA;AACA;;;KApEMvB,e;;AAqEN,MAAMyB,eAAe,GAAIxB,KAAD,IAAW;EAC/B,IAAIyB,OAAJ;EACA,IAAIC,YAAY,GAAG,eAAnB;;EACA,IAAI1B,KAAK,CAACC,UAAV,EAAsB;IAClBwB,OAAO,GAAG,mCAAV,CADkB,CAElB;EACH,CAHD,MAGO;IACHA,OAAO,GAAG,qCAAV,CADG,CAEH;EACH;;EACD,MAAME,YAAY,GAAGnC,WAAW,CAC5BQ,KAD4B,EAE3B,GAAE0B,YAAa,6BAFY,CAAhC;EAKA,oBACI;IAAA,wBACI;MAAK,SAAS,EAAG,QAAOD,OAAQ,EAAhC;MAAA,uBACI,QAAC,OAAD,OAAaE,YAAb;QAAA,UACK3B,KAAK,CAACW,YAAN,iBAAsB;UAAA,UAAOX,KAAK,CAACW;QAAb;UAAA;UAAA;UAAA;QAAA;MAD3B;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QADJ,EAMKX,KAAK,CAACO,QAAN,CAAec,GAAf,CAAmB,CAACf,IAAD,EAAOgB,KAAP,KAAiB;MACjC,oBAAO,QAAC,wBAAD,OAA0CtB,KAA1C;QAAiD,KAAK,EAAEsB;MAAxD,GAA+BA,KAA/B;QAAA;QAAA;QAAA;MAAA,QAAP;IACH,CAFA,CANL;EAAA,gBADJ;AAYH,CA3BD;AA6BA;AACA;AACA;AACA;AACA;;;MAjCME,e;;AAkCN,MAAMI,wBAAwB,GAAI5B,KAAD,IAAW;EACxC,IAAI6B,kBAAkB,GAAG;IAAElB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIiB,kBAAkB,GAAG;IAAEnB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIkB,kBAAkB,GAAG;IAAEpB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAImB,kBAAkB,GAAG;IAAErB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIoB,kBAAkB,GAAG;IAAEtB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIqB,kBAAkB,GAAG;IAAEvB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EAEA,IAAIsB,kBAAkB,GAAG;IAAExB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIuB,eAAe,GAAG;IAAEzB,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAtB;EACA,IAAIwB,gBAAgB,GAAG;IAAE1B,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAvB;EACA,IAAIyB,oBAAoB,GAAG;IAAE3B,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAA3B;EACA,IAAI0B,uBAAuB,GAAG;IAAE5B,YAAY,EAAE,EAAhB;IAAoBC,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAA9B;;EAEA,IAAIb,KAAK,CAACiB,UAAN,IAAoBjB,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAAxB,EAAuD;IACnDa,kBAAkB,GAAGnC,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAArB;EACH;;EAED,IAAItB,KAAK,CAACO,QAAN,IAAkBP,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAtB,EAAmD;IAC/Cc,eAAe,GAAGpC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAlB;EACH;;EAED,IAAItB,KAAK,CAACc,SAAN,IAAmBd,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAvB,EAAqD;IACjDe,gBAAgB,GAAGrC,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAnB;EACH;;EAED,IAAItB,KAAK,CAACe,aAAN,IAAuBf,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAA3B,EAA6D;IACzDgB,oBAAoB,GAAGtC,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAAvB;EACH;;EAED,IAAItB,KAAK,CAACgB,iBAAN,IAA2BhB,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAA/B,EAAqE;IACjEiB,uBAAuB,GAAGvC,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAA1B;EACH,CAhCuC,CAkCxC;;;EACA,IAAIkB,OAAO,GAAGxC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAd;;EACA,IAAI,CAACkB,OAAL,EAAc;IAAEA,OAAO,GAAGxC,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAV;EAAwC;;EACxD,IAAI,CAACkB,OAAL,EAAc;IAAEA,OAAO,GAAGxC,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAAV;EAA4C;;EAC5D,IAAI,CAACkB,OAAL,EAAc;IAAEA,OAAO,GAAGxC,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAAV;EAAgD;;EAChE,IAAI,CAACkB,OAAL,EAAc;IAAEA,OAAO,GAAGxC,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAAV;EAAyC;;EAEzDa,kBAAkB,GAAGxC,cAAc,CAACwC,kBAAD,EAAqBK,OAArB,CAAnC;EACAJ,eAAe,GAAGzC,cAAc,CAACyC,eAAD,EAAkBI,OAAlB,CAAhC;EACAH,gBAAgB,GAAG1C,cAAc,CAAC0C,gBAAD,EAAmBG,OAAnB,CAAjC;EACAF,oBAAoB,GAAG3C,cAAc,CAAC2C,oBAAD,EAAuBE,OAAvB,CAArC;EACAD,uBAAuB,GAAG5C,cAAc,CAAC4C,uBAAD,EAA0BC,OAA1B,CAAxC;EAEAX,kBAAkB,GAAGlC,cAAc,CAACkC,kBAAD,EAAqBW,OAArB,CAAnC;EACAV,kBAAkB,GAAGnC,cAAc,CAACmC,kBAAD,EAAqBU,OAArB,CAAnC;EACAT,kBAAkB,GAAGpC,cAAc,CAACoC,kBAAD,EAAqBS,OAArB,CAAnC;EACAR,kBAAkB,GAAGrC,cAAc,CAACqC,kBAAD,EAAqBQ,OAArB,CAAnC;EACAP,kBAAkB,GAAGtC,cAAc,CAACsC,kBAAD,EAAqBO,OAArB,CAAnC;EACAN,kBAAkB,GAAGvC,cAAc,CAACuC,kBAAD,EAAqBM,OAArB,CAAnC;EAEA,IAAIf,OAAJ;EACA,IAAIgB,SAAS,GAAG,EAAhB;;EACA,IAAIzC,KAAK,CAACC,UAAV,EAAsB;IAClBwB,OAAO,GAAG,mCAAV,CADkB,CAElB;;IAEA,IAAKzB,KAAK,CAACuB,QAAN,GAAiBvB,KAAK,CAACsB,KAAxB,IAAmC1B,OAAO,GAAG,CAAjD,EAAqD;MAAE;MACnD6C,SAAS,CAACtB,IAAV,CAAe;QAAEuB,QAAQ,EAAEb,kBAAZ;QAAgCc,SAAS,EAAE;MAA3C,CAAf;IACH;;IACDF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEP,kBADC;MAEXQ,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEZ,kBAAZ;MAAgCa,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEN,eADC;MAEXO,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEX,kBAAZ;MAAgCY,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEL,gBADC;MAEXM,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEV,kBAAZ;MAAgCW,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEJ,oBADC;MAEXK,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAET,kBAAZ;MAAgCU,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEH,uBADC;MAEXI,SAAS,EAAE;IAFA,CAAf;;IAKA,IAAK3C,KAAK,CAACuB,QAAN,GAAiBvB,KAAK,CAACsB,KAAxB,IAAmC1B,OAAO,GAAG,CAAjD,EAAqD;MAAE;MACnD6C,SAAS,CAACtB,IAAV,CAAe;QAAEuB,QAAQ,EAAER,kBAAZ;QAAgCS,SAAS,EAAE;MAA3C,CAAf;IACH;EAEJ,CApCD,MAoCO;IACHlB,OAAO,GAAG,qCAAV,CADG,CAEH;;IAEA,IAAKzB,KAAK,CAACuB,QAAN,GAAiBvB,KAAK,CAACsB,KAAxB,IAAmC1B,OAAO,GAAG,CAAjD,EAAqD;MAAE;MACnD6C,SAAS,CAACtB,IAAV,CAAe;QAAEuB,QAAQ,EAAEb,kBAAZ;QAAgCc,SAAS,EAAE;MAA3C,CAAf;IACH;;IACDF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEN,eADC;MAEXO,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEZ,kBAAZ;MAAgCa,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEL,gBADC;MAEXM,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEX,kBAAZ;MAAgCY,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEJ,oBADC;MAEXK,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACtB,IAAV,CAAe;MAAEuB,QAAQ,EAAEV,kBAAZ;MAAgCW,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACtB,IAAV,CAAe;MACXuB,QAAQ,EAAEH,uBADC;MAEXI,SAAS,EAAE;IAFA,CAAf;;IAKA,IAAK3C,KAAK,CAACuB,QAAN,GAAiBvB,KAAK,CAACsB,KAAxB,IAAmC1B,OAAO,GAAG,CAAjD,EAAqD;MAAE;MACnD6C,SAAS,CAACtB,IAAV,CAAe;QAAEuB,QAAQ,EAAET,kBAAZ;QAAgCU,SAAS,EAAE;MAA3C,CAAf;IACH;EAEJ;;EAED,oBACI;IAAA,WAEK3C,KAAK,CAACC,UAAN,iBACG;MAAK,SAAS,EAAG,QAAOwB,OAAQ,EAAhC;MAAA,uBACI,QAAC,UAAD;QACI,KAAK,EAAEgB,SADX;QAEI,aAAa,EAAEzC,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACsB,KAA7B;MAFnB;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QAHR,EAUK,CAACtB,KAAK,CAACC,UAAP,iBACG;MAAK,SAAS,EAAG,QAAOwB,OAAQ,EAAhC;MAAA,uBACI,QAAC,UAAD;QACI,KAAK,EAAEgB,SADX;QAEI,aAAa,EAAEzC,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACsB,KAA7B;MAFnB;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QAXR;EAAA,gBADJ;AAqBH,CAlJD;;MAAMM,wB;AAsJN,eAAe7B,eAAf"}, "metadata": {}, "sourceType": "module"}