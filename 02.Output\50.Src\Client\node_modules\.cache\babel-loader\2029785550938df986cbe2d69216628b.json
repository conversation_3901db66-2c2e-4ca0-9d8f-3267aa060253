{"ast": null, "code": "'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport { VERSION } from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\n\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context); // Copy axios.prototype to instance\n\n  utils.extend(instance, Axios.prototype, context, {\n    allOwnKeys: true\n  }); // Copy context to instance\n\n  utils.extend(instance, context, null, {\n    allOwnKeys: true\n  }); // Factory for creating new instances\n\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n} // Create the default instance to be exported\n\n\nvar axios = createInstance(defaults); // Expose Axios class to allow class inheritance\n\naxios.Axios = Axios; // Expose Cancel & CancelToken\n\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData; // Expose AxiosError class\n\naxios.AxiosError = AxiosError; // alias for CanceledError for backward compatibility\n\naxios.Cancel = axios.CanceledError; // Expose all/spread\n\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\n\naxios.spread = spread; // Expose isAxiosError\n\naxios.isAxiosError = isAxiosError;\n\naxios.formToJSON = function (thing) {\n  return formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\n};\n\nexport default axios;", "map": {"version": 3, "names": ["utils", "bind", "A<PERSON>os", "mergeConfig", "defaults", "formDataToJSON", "CanceledError", "CancelToken", "isCancel", "VERSION", "toFormData", "AxiosError", "spread", "isAxiosError", "createInstance", "defaultConfig", "context", "instance", "prototype", "request", "extend", "allOwnKeys", "create", "instanceConfig", "axios", "Cancel", "all", "promises", "Promise", "formToJSON", "thing", "isHTMLForm", "FormData"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/axios.js"], "sourcesContent": ["'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport {VERSION} from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  const context = new Axios(defaultConfig);\n  const instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context, {allOwnKeys: true});\n\n  // Copy context to instance\n  utils.extend(instance, context, null, {allOwnKeys: true});\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n}\n\n// Create the default instance to be exported\nconst axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\n\naxios.spread = spread;\n\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n\naxios.formToJSON = thing => {\n  return formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\n};\n\nexport default axios\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,YAAlB;AACA,OAAOC,IAAP,MAAiB,mBAAjB;AACA,OAAOC,KAAP,MAAkB,iBAAlB;AACA,OAAOC,WAAP,MAAwB,uBAAxB;AACA,OAAOC,QAAP,MAAqB,qBAArB;AACA,OAAOC,cAAP,MAA2B,6BAA3B;AACA,OAAOC,aAAP,MAA0B,2BAA1B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,SAAQC,OAAR,QAAsB,eAAtB;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,UAAP,MAAuB,sBAAvB;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,YAAP,MAAyB,2BAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBC,aAAxB,EAAuC;EACrC,IAAMC,OAAO,GAAG,IAAId,KAAJ,CAAUa,aAAV,CAAhB;EACA,IAAME,QAAQ,GAAGhB,IAAI,CAACC,KAAK,CAACgB,SAAN,CAAgBC,OAAjB,EAA0BH,OAA1B,CAArB,CAFqC,CAIrC;;EACAhB,KAAK,CAACoB,MAAN,CAAaH,QAAb,EAAuBf,KAAK,CAACgB,SAA7B,EAAwCF,OAAxC,EAAiD;IAACK,UAAU,EAAE;EAAb,CAAjD,EALqC,CAOrC;;EACArB,KAAK,CAACoB,MAAN,CAAaH,QAAb,EAAuBD,OAAvB,EAAgC,IAAhC,EAAsC;IAACK,UAAU,EAAE;EAAb,CAAtC,EARqC,CAUrC;;EACAJ,QAAQ,CAACK,MAAT,GAAkB,SAASA,MAAT,CAAgBC,cAAhB,EAAgC;IAChD,OAAOT,cAAc,CAACX,WAAW,CAACY,aAAD,EAAgBQ,cAAhB,CAAZ,CAArB;EACD,CAFD;;EAIA,OAAON,QAAP;AACD,C,CAED;;;AACA,IAAMO,KAAK,GAAGV,cAAc,CAACV,QAAD,CAA5B,C,CAEA;;AACAoB,KAAK,CAACtB,KAAN,GAAcA,KAAd,C,CAEA;;AACAsB,KAAK,CAAClB,aAAN,GAAsBA,aAAtB;AACAkB,KAAK,CAACjB,WAAN,GAAoBA,WAApB;AACAiB,KAAK,CAAChB,QAAN,GAAiBA,QAAjB;AACAgB,KAAK,CAACf,OAAN,GAAgBA,OAAhB;AACAe,KAAK,CAACd,UAAN,GAAmBA,UAAnB,C,CAEA;;AACAc,KAAK,CAACb,UAAN,GAAmBA,UAAnB,C,CAEA;;AACAa,KAAK,CAACC,MAAN,GAAeD,KAAK,CAAClB,aAArB,C,CAEA;;AACAkB,KAAK,CAACE,GAAN,GAAY,SAASA,GAAT,CAAaC,QAAb,EAAuB;EACjC,OAAOC,OAAO,CAACF,GAAR,CAAYC,QAAZ,CAAP;AACD,CAFD;;AAIAH,KAAK,CAACZ,MAAN,GAAeA,MAAf,C,CAEA;;AACAY,KAAK,CAACX,YAAN,GAAqBA,YAArB;;AAEAW,KAAK,CAACK,UAAN,GAAmB,UAAAC,KAAK,EAAI;EAC1B,OAAOzB,cAAc,CAACL,KAAK,CAAC+B,UAAN,CAAiBD,KAAjB,IAA0B,IAAIE,QAAJ,CAAaF,KAAb,CAA1B,GAAgDA,KAAjD,CAArB;AACD,CAFD;;AAIA,eAAeN,KAAf"}, "metadata": {}, "sourceType": "module"}