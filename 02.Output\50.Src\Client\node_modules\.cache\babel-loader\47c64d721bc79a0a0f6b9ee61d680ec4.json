{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\n\nfunction encode(val) {\n  return encodeURIComponent(val).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n}\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?object} options\n *\n * @returns {string} The formatted url\n */\n\n\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n\n  var _encode = options && options.encode || encode;\n\n  var serializeFn = options && options.serialize;\n  var serializedParams;\n\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf(\"#\");\n\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n}", "map": {"version": 3, "names": ["utils", "AxiosURLSearchParams", "encode", "val", "encodeURIComponent", "replace", "buildURL", "url", "params", "options", "_encode", "serializeFn", "serialize", "serializedParams", "isURLSearchParams", "toString", "hashmarkIndex", "indexOf", "slice"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/buildURL.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?object} options\n *\n * @returns {string} The formatted url\n */\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  \n  const _encode = options && options.encode || encode;\n\n  const serializeFn = options && options.serialize;\n\n  let serializedParams;\n\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ?\n      params.toString() :\n      new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n\n  if (serializedParams) {\n    const hashmarkIndex = url.indexOf(\"#\");\n\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,oBAAP,MAAiC,oCAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,MAAT,CAAgBC,GAAhB,EAAqB;EACnB,OAAOC,kBAAkB,CAACD,GAAD,CAAlB,CACLE,OADK,CACG,OADH,EACY,GADZ,EAELA,OAFK,CAEG,MAFH,EAEW,GAFX,EAGLA,OAHK,CAGG,OAHH,EAGY,GAHZ,EAILA,OAJK,CAIG,MAJH,EAIW,GAJX,EAKLA,OALK,CAKG,OALH,EAKY,GALZ,EAMLA,OANK,CAMG,OANH,EAMY,GANZ,CAAP;AAOD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,eAAe,SAASC,QAAT,CAAkBC,GAAlB,EAAuBC,MAAvB,EAA+BC,OAA/B,EAAwC;EACrD;EACA,IAAI,CAACD,MAAL,EAAa;IACX,OAAOD,GAAP;EACD;;EAED,IAAMG,OAAO,GAAGD,OAAO,IAAIA,OAAO,CAACP,MAAnB,IAA6BA,MAA7C;;EAEA,IAAMS,WAAW,GAAGF,OAAO,IAAIA,OAAO,CAACG,SAAvC;EAEA,IAAIC,gBAAJ;;EAEA,IAAIF,WAAJ,EAAiB;IACfE,gBAAgB,GAAGF,WAAW,CAACH,MAAD,EAASC,OAAT,CAA9B;EACD,CAFD,MAEO;IACLI,gBAAgB,GAAGb,KAAK,CAACc,iBAAN,CAAwBN,MAAxB,IACjBA,MAAM,CAACO,QAAP,EADiB,GAEjB,IAAId,oBAAJ,CAAyBO,MAAzB,EAAiCC,OAAjC,EAA0CM,QAA1C,CAAmDL,OAAnD,CAFF;EAGD;;EAED,IAAIG,gBAAJ,EAAsB;IACpB,IAAMG,aAAa,GAAGT,GAAG,CAACU,OAAJ,CAAY,GAAZ,CAAtB;;IAEA,IAAID,aAAa,KAAK,CAAC,CAAvB,EAA0B;MACxBT,GAAG,GAAGA,GAAG,CAACW,KAAJ,CAAU,CAAV,EAAaF,aAAb,CAAN;IACD;;IACDT,GAAG,IAAI,CAACA,GAAG,CAACU,OAAJ,CAAY,GAAZ,MAAqB,CAAC,CAAtB,GAA0B,GAA1B,GAAgC,GAAjC,IAAwCJ,gBAA/C;EACD;;EAED,OAAON,GAAP;AACD"}, "metadata": {}, "sourceType": "module"}