{"ast": null, "code": "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport isBrowser from '../utils/isBrowser';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar touchSupported = isBrowser && ( // @ts-ignore\n'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);\n\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n      _c = _b.delay,\n      delay = _c === void 0 ? 300 : _c,\n      moveThreshold = _b.moveThreshold,\n      onClick = _b.onClick,\n      onLongPressEnd = _b.onLongPressEnd;\n\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef();\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n\n    var overThreshold = function overThreshold(event) {\n      var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n\n    function getClientPosition(event) {\n      if (event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n\n      console.warn('Unsupported event type');\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n\n    var onStart = function onStart(event) {\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n            clientX = _a.clientX,\n            clientY = _a.clientY;\n\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n\n    var onMove = function onMove(event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearInterval(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n\n    var onEnd = function onEnd(event, shouldTriggerClick) {\n      var _a;\n\n      if (shouldTriggerClick === void 0) {\n        shouldTriggerClick = false;\n      }\n\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      }\n\n      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {\n        onClickRef.current(event);\n      }\n\n      isTriggeredRef.current = false;\n    };\n\n    var onEndWithClick = function onEndWithClick(event) {\n      return onEnd(event, true);\n    };\n\n    if (!touchSupported) {\n      targetElement.addEventListener('mousedown', onStart);\n      targetElement.addEventListener('mouseup', onEndWithClick);\n      targetElement.addEventListener('mouseleave', onEnd);\n      if (hasMoveThreshold) targetElement.addEventListener('mousemove', onMove);\n    } else {\n      targetElement.addEventListener('touchstart', onStart);\n      targetElement.addEventListener('touchend', onEndWithClick);\n      if (hasMoveThreshold) targetElement.addEventListener('touchmove', onMove);\n    }\n\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n\n      if (!touchSupported) {\n        targetElement.removeEventListener('mousedown', onStart);\n        targetElement.removeEventListener('mouseup', onEndWithClick);\n        targetElement.removeEventListener('mouseleave', onEnd);\n        if (hasMoveThreshold) targetElement.removeEventListener('mousemove', onMove);\n      } else {\n        targetElement.removeEventListener('touchstart', onStart);\n        targetElement.removeEventListener('touchend', onEndWithClick);\n        if (hasMoveThreshold) targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\n\nexport default useLongPress;", "map": {"version": 3, "names": ["useRef", "useLatest", "getTargetElement", "<PERSON><PERSON><PERSON><PERSON>", "useEffectWithTarget", "touchSupported", "window", "DocumentTouch", "document", "useLongPress", "onLongPress", "target", "_a", "_b", "_c", "delay", "moveT<PERSON><PERSON>old", "onClick", "onLongPressEnd", "onLongPressRef", "onClickRef", "onLongPressEndRef", "timerRef", "isTriggeredRef", "pervPositionRef", "x", "y", "hasMoveThreshold", "targetElement", "addEventListener", "overThreshold", "event", "getClientPosition", "clientX", "clientY", "offsetX", "Math", "abs", "current", "offsetY", "TouchEvent", "touches", "MouseEvent", "console", "warn", "onStart", "setTimeout", "onMove", "clearInterval", "undefined", "onEnd", "shouldTriggerClick", "clearTimeout", "call", "onEndWithClick", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useLongPress/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport isBrowser from '../utils/isBrowser';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar touchSupported = isBrowser && (\n// @ts-ignore\n'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.delay,\n    delay = _c === void 0 ? 300 : _c,\n    moveThreshold = _b.moveThreshold,\n    onClick = _b.onClick,\n    onLongPressEnd = _b.onLongPressEnd;\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef();\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var overThreshold = function overThreshold(event) {\n      var _a = getClientPosition(event),\n        clientX = _a.clientX,\n        clientY = _a.clientY;\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n    function getClientPosition(event) {\n      if (event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n      console.warn('Unsupported event type');\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n    var onStart = function onStart(event) {\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n    var onMove = function onMove(event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearInterval(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n    var onEnd = function onEnd(event, shouldTriggerClick) {\n      var _a;\n      if (shouldTriggerClick === void 0) {\n        shouldTriggerClick = false;\n      }\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      }\n      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onEndWithClick = function onEndWithClick(event) {\n      return onEnd(event, true);\n    };\n    if (!touchSupported) {\n      targetElement.addEventListener('mousedown', onStart);\n      targetElement.addEventListener('mouseup', onEndWithClick);\n      targetElement.addEventListener('mouseleave', onEnd);\n      if (hasMoveThreshold) targetElement.addEventListener('mousemove', onMove);\n    } else {\n      targetElement.addEventListener('touchstart', onStart);\n      targetElement.addEventListener('touchend', onEndWithClick);\n      if (hasMoveThreshold) targetElement.addEventListener('touchmove', onMove);\n    }\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n      if (!touchSupported) {\n        targetElement.removeEventListener('mousedown', onStart);\n        targetElement.removeEventListener('mouseup', onEndWithClick);\n        targetElement.removeEventListener('mouseleave', onEnd);\n        if (hasMoveThreshold) targetElement.removeEventListener('mousemove', onMove);\n      } else {\n        targetElement.removeEventListener('touchstart', onStart);\n        targetElement.removeEventListener('touchend', onEndWithClick);\n        if (hasMoveThreshold) targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\nexport default useLongPress;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;AACA,IAAIC,cAAc,GAAGF,SAAS,MAC9B;AACA,kBAAkBG,MAAlB,IAA4BA,MAAM,CAACC,aAAP,IAAwBC,QAAQ,YAAYD,aAF1C,CAA9B;;AAGA,SAASE,YAAT,CAAsBC,WAAtB,EAAmCC,MAAnC,EAA2CC,EAA3C,EAA+C;EAC7C,IAAIC,EAAE,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EAA9B;EAAA,IACEE,EAAE,GAAGD,EAAE,CAACE,KADV;EAAA,IAEEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,GAAhB,GAAsBA,EAFhC;EAAA,IAGEE,aAAa,GAAGH,EAAE,CAACG,aAHrB;EAAA,IAIEC,OAAO,GAAGJ,EAAE,CAACI,OAJf;EAAA,IAKEC,cAAc,GAAGL,EAAE,CAACK,cALtB;;EAMA,IAAIC,cAAc,GAAGlB,SAAS,CAACS,WAAD,CAA9B;EACA,IAAIU,UAAU,GAAGnB,SAAS,CAACgB,OAAD,CAA1B;EACA,IAAII,iBAAiB,GAAGpB,SAAS,CAACiB,cAAD,CAAjC;EACA,IAAII,QAAQ,GAAGtB,MAAM,EAArB;EACA,IAAIuB,cAAc,GAAGvB,MAAM,CAAC,KAAD,CAA3B;EACA,IAAIwB,eAAe,GAAGxB,MAAM,CAAC;IAC3ByB,CAAC,EAAE,CADwB;IAE3BC,CAAC,EAAE;EAFwB,CAAD,CAA5B;EAIA,IAAIC,gBAAgB,GAAG,CAAC,EAAE,CAACX,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACS,CAA7E,KAAmFT,aAAa,CAACS,CAAd,GAAkB,CAArG,IAA0G,CAACT,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACU,CAA7E,KAAmFV,aAAa,CAACU,CAAd,GAAkB,CAAjN,CAAxB;EACAtB,mBAAmB,CAAC,YAAY;IAC9B,IAAIwB,aAAa,GAAG1B,gBAAgB,CAACS,MAAD,CAApC;;IACA,IAAI,EAAEiB,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACC,gBAA9E,CAAJ,EAAqG;MACnG;IACD;;IACD,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,KAAvB,EAA8B;MAChD,IAAInB,EAAE,GAAGoB,iBAAiB,CAACD,KAAD,CAA1B;MAAA,IACEE,OAAO,GAAGrB,EAAE,CAACqB,OADf;MAAA,IAEEC,OAAO,GAAGtB,EAAE,CAACsB,OAFf;;MAGA,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAL,CAASJ,OAAO,GAAGT,eAAe,CAACc,OAAhB,CAAwBb,CAA3C,CAAd;MACA,IAAIc,OAAO,GAAGH,IAAI,CAACC,GAAL,CAASH,OAAO,GAAGV,eAAe,CAACc,OAAhB,CAAwBZ,CAA3C,CAAd;MACA,OAAO,CAAC,EAAE,CAACV,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACS,CAA7E,KAAmFU,OAAO,GAAGnB,aAAa,CAACS,CAA3G,IAAgH,CAACT,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACU,CAA7E,KAAmFa,OAAO,GAAGvB,aAAa,CAACU,CAA7N,CAAR;IACD,CAPD;;IAQA,SAASM,iBAAT,CAA2BD,KAA3B,EAAkC;MAChC,IAAIA,KAAK,YAAYS,UAArB,EAAiC;QAC/B,OAAO;UACLP,OAAO,EAAEF,KAAK,CAACU,OAAN,CAAc,CAAd,EAAiBR,OADrB;UAELC,OAAO,EAAEH,KAAK,CAACU,OAAN,CAAc,CAAd,EAAiBP;QAFrB,CAAP;MAID;;MACD,IAAIH,KAAK,YAAYW,UAArB,EAAiC;QAC/B,OAAO;UACLT,OAAO,EAAEF,KAAK,CAACE,OADV;UAELC,OAAO,EAAEH,KAAK,CAACG;QAFV,CAAP;MAID;;MACDS,OAAO,CAACC,IAAR,CAAa,wBAAb;MACA,OAAO;QACLX,OAAO,EAAE,CADJ;QAELC,OAAO,EAAE;MAFJ,CAAP;IAID;;IACD,IAAIW,OAAO,GAAG,SAASA,OAAT,CAAiBd,KAAjB,EAAwB;MACpC,IAAIJ,gBAAJ,EAAsB;QACpB,IAAIf,EAAE,GAAGoB,iBAAiB,CAACD,KAAD,CAA1B;QAAA,IACEE,OAAO,GAAGrB,EAAE,CAACqB,OADf;QAAA,IAEEC,OAAO,GAAGtB,EAAE,CAACsB,OAFf;;QAGAV,eAAe,CAACc,OAAhB,CAAwBb,CAAxB,GAA4BQ,OAA5B;QACAT,eAAe,CAACc,OAAhB,CAAwBZ,CAAxB,GAA4BQ,OAA5B;MACD;;MACDZ,QAAQ,CAACgB,OAAT,GAAmBQ,UAAU,CAAC,YAAY;QACxC3B,cAAc,CAACmB,OAAf,CAAuBP,KAAvB;QACAR,cAAc,CAACe,OAAf,GAAyB,IAAzB;MACD,CAH4B,EAG1BvB,KAH0B,CAA7B;IAID,CAZD;;IAaA,IAAIgC,MAAM,GAAG,SAASA,MAAT,CAAgBhB,KAAhB,EAAuB;MAClC,IAAIT,QAAQ,CAACgB,OAAT,IAAoBR,aAAa,CAACC,KAAD,CAArC,EAA8C;QAC5CiB,aAAa,CAAC1B,QAAQ,CAACgB,OAAV,CAAb;QACAhB,QAAQ,CAACgB,OAAT,GAAmBW,SAAnB;MACD;IACF,CALD;;IAMA,IAAIC,KAAK,GAAG,SAASA,KAAT,CAAenB,KAAf,EAAsBoB,kBAAtB,EAA0C;MACpD,IAAIvC,EAAJ;;MACA,IAAIuC,kBAAkB,KAAK,KAAK,CAAhC,EAAmC;QACjCA,kBAAkB,GAAG,KAArB;MACD;;MACD,IAAI7B,QAAQ,CAACgB,OAAb,EAAsB;QACpBc,YAAY,CAAC9B,QAAQ,CAACgB,OAAV,CAAZ;MACD;;MACD,IAAIf,cAAc,CAACe,OAAnB,EAA4B;QAC1B,CAAC1B,EAAE,GAAGS,iBAAiB,CAACiB,OAAxB,MAAqC,IAArC,IAA6C1B,EAAE,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,EAAE,CAACyC,IAAH,CAAQhC,iBAAR,EAA2BU,KAA3B,CAAtE;MACD;;MACD,IAAIoB,kBAAkB,IAAI,CAAC5B,cAAc,CAACe,OAAtC,IAAiDlB,UAAU,CAACkB,OAAhE,EAAyE;QACvElB,UAAU,CAACkB,OAAX,CAAmBP,KAAnB;MACD;;MACDR,cAAc,CAACe,OAAf,GAAyB,KAAzB;IACD,CAfD;;IAgBA,IAAIgB,cAAc,GAAG,SAASA,cAAT,CAAwBvB,KAAxB,EAA+B;MAClD,OAAOmB,KAAK,CAACnB,KAAD,EAAQ,IAAR,CAAZ;IACD,CAFD;;IAGA,IAAI,CAAC1B,cAAL,EAAqB;MACnBuB,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4CgB,OAA5C;MACAjB,aAAa,CAACC,gBAAd,CAA+B,SAA/B,EAA0CyB,cAA1C;MACA1B,aAAa,CAACC,gBAAd,CAA+B,YAA/B,EAA6CqB,KAA7C;MACA,IAAIvB,gBAAJ,EAAsBC,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4CkB,MAA5C;IACvB,CALD,MAKO;MACLnB,aAAa,CAACC,gBAAd,CAA+B,YAA/B,EAA6CgB,OAA7C;MACAjB,aAAa,CAACC,gBAAd,CAA+B,UAA/B,EAA2CyB,cAA3C;MACA,IAAI3B,gBAAJ,EAAsBC,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4CkB,MAA5C;IACvB;;IACD,OAAO,YAAY;MACjB,IAAIzB,QAAQ,CAACgB,OAAb,EAAsB;QACpBc,YAAY,CAAC9B,QAAQ,CAACgB,OAAV,CAAZ;QACAf,cAAc,CAACe,OAAf,GAAyB,KAAzB;MACD;;MACD,IAAI,CAACjC,cAAL,EAAqB;QACnBuB,aAAa,CAAC2B,mBAAd,CAAkC,WAAlC,EAA+CV,OAA/C;QACAjB,aAAa,CAAC2B,mBAAd,CAAkC,SAAlC,EAA6CD,cAA7C;QACA1B,aAAa,CAAC2B,mBAAd,CAAkC,YAAlC,EAAgDL,KAAhD;QACA,IAAIvB,gBAAJ,EAAsBC,aAAa,CAAC2B,mBAAd,CAAkC,WAAlC,EAA+CR,MAA/C;MACvB,CALD,MAKO;QACLnB,aAAa,CAAC2B,mBAAd,CAAkC,YAAlC,EAAgDV,OAAhD;QACAjB,aAAa,CAAC2B,mBAAd,CAAkC,UAAlC,EAA8CD,cAA9C;QACA,IAAI3B,gBAAJ,EAAsBC,aAAa,CAAC2B,mBAAd,CAAkC,WAAlC,EAA+CR,MAA/C;MACvB;IACF,CAfD;EAgBD,CAhGkB,EAgGhB,EAhGgB,EAgGZpC,MAhGY,CAAnB;AAiGD;;AACD,eAAeF,YAAf"}, "metadata": {}, "sourceType": "module"}