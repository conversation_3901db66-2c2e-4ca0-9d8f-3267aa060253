{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter;\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\n\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  this.bufferPosition = 0;\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function (status, text) {\n    debug('finish', status, text);\n\n    self._chunkHandler(status, text);\n\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n\n    self._cleanup();\n  });\n}\n\ninherits(XhrReceiver, EventEmitter);\n\nXhrReceiver.prototype._chunkHandler = function (status, text) {\n  debug('_chunkHandler', status);\n\n  if (status !== 200 || !text) {\n    return;\n  }\n\n  for (var idx = -1;; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n\n    if (idx === -1) {\n      break;\n    }\n\n    var msg = buf.slice(0, idx);\n\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\n\nXhrReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nXhrReceiver.prototype.abort = function () {\n  debug('abort');\n\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n\n  this._cleanup();\n};\n\nmodule.exports = XhrReceiver;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "XhrReceiver", "url", "AjaxObject", "call", "self", "bufferPosition", "xo", "on", "_<PERSON><PERSON><PERSON><PERSON>", "bind", "once", "status", "text", "reason", "emit", "_cleanup", "prototype", "idx", "buf", "slice", "indexOf", "msg", "removeAllListeners", "abort", "close", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/receiver/xhr.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\n\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n\n  this.bufferPosition = 0;\n\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function(status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\n\ninherits(XhrReceiver, EventEmitter);\n\nXhrReceiver.prototype._chunkHandler = function(status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n\n  for (var idx = -1; ; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\n\nXhrReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nXhrReceiver.prototype.abort = function() {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\n\nmodule.exports = XhrReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;;AAIA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAD,CAAP,CAAiB,4BAAjB,CAAR;AACD;;AAED,SAASM,WAAT,CAAqBC,GAArB,EAA0BC,UAA1B,EAAsC;EACpCN,KAAK,CAACK,GAAD,CAAL;EACAN,YAAY,CAACQ,IAAb,CAAkB,IAAlB;EACA,IAAIC,IAAI,GAAG,IAAX;EAEA,KAAKC,cAAL,GAAsB,CAAtB;EAEA,KAAKC,EAAL,GAAU,IAAIJ,UAAJ,CAAe,MAAf,EAAuBD,GAAvB,EAA4B,IAA5B,CAAV;EACA,KAAKK,EAAL,CAAQC,EAAR,CAAW,OAAX,EAAoB,KAAKC,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAApB;EACA,KAAKH,EAAL,CAAQI,IAAR,CAAa,QAAb,EAAuB,UAASC,MAAT,EAAiBC,IAAjB,EAAuB;IAC5ChB,KAAK,CAAC,QAAD,EAAWe,MAAX,EAAmBC,IAAnB,CAAL;;IACAR,IAAI,CAACI,aAAL,CAAmBG,MAAnB,EAA2BC,IAA3B;;IACAR,IAAI,CAACE,EAAL,GAAU,IAAV;IACA,IAAIO,MAAM,GAAGF,MAAM,KAAK,GAAX,GAAiB,SAAjB,GAA6B,WAA1C;IACAf,KAAK,CAAC,OAAD,EAAUiB,MAAV,CAAL;IACAT,IAAI,CAACU,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyBD,MAAzB;;IACAT,IAAI,CAACW,QAAL;EACD,CARD;AASD;;AAEDtB,QAAQ,CAACO,WAAD,EAAcL,YAAd,CAAR;;AAEAK,WAAW,CAACgB,SAAZ,CAAsBR,aAAtB,GAAsC,UAASG,MAAT,EAAiBC,IAAjB,EAAuB;EAC3DhB,KAAK,CAAC,eAAD,EAAkBe,MAAlB,CAAL;;EACA,IAAIA,MAAM,KAAK,GAAX,IAAkB,CAACC,IAAvB,EAA6B;IAC3B;EACD;;EAED,KAAK,IAAIK,GAAG,GAAG,CAAC,CAAhB,GAAqB,KAAKZ,cAAL,IAAuBY,GAAG,GAAG,CAAlD,EAAqD;IACnD,IAAIC,GAAG,GAAGN,IAAI,CAACO,KAAL,CAAW,KAAKd,cAAhB,CAAV;IACAY,GAAG,GAAGC,GAAG,CAACE,OAAJ,CAAY,IAAZ,CAAN;;IACA,IAAIH,GAAG,KAAK,CAAC,CAAb,EAAgB;MACd;IACD;;IACD,IAAII,GAAG,GAAGH,GAAG,CAACC,KAAJ,CAAU,CAAV,EAAaF,GAAb,CAAV;;IACA,IAAII,GAAJ,EAAS;MACPzB,KAAK,CAAC,SAAD,EAAYyB,GAAZ,CAAL;MACA,KAAKP,IAAL,CAAU,SAAV,EAAqBO,GAArB;IACD;EACF;AACF,CAlBD;;AAoBArB,WAAW,CAACgB,SAAZ,CAAsBD,QAAtB,GAAiC,YAAW;EAC1CnB,KAAK,CAAC,UAAD,CAAL;EACA,KAAK0B,kBAAL;AACD,CAHD;;AAKAtB,WAAW,CAACgB,SAAZ,CAAsBO,KAAtB,GAA8B,YAAW;EACvC3B,KAAK,CAAC,OAAD,CAAL;;EACA,IAAI,KAAKU,EAAT,EAAa;IACX,KAAKA,EAAL,CAAQkB,KAAR;IACA5B,KAAK,CAAC,OAAD,CAAL;IACA,KAAKkB,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyB,MAAzB;IACA,KAAKR,EAAL,GAAU,IAAV;EACD;;EACD,KAAKS,QAAL;AACD,CATD;;AAWAU,MAAM,CAACC,OAAP,GAAiB1B,WAAjB"}, "metadata": {}, "sourceType": "script"}