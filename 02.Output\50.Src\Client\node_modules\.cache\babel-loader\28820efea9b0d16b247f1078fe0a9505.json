{"ast": null, "code": "'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\n\nexport default function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n\n  return requestedURL;\n}", "map": {"version": 3, "names": ["isAbsoluteURL", "combineURLs", "buildFullPath", "baseURL", "requestedURL"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/buildFullPath.js"], "sourcesContent": ["'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\nexport default function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,aAAP,MAA0B,6BAA1B;AACA,OAAOC,WAAP,MAAwB,2BAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,aAAT,CAAuBC,OAAvB,EAAgCC,YAAhC,EAA8C;EAC3D,IAAID,OAAO,IAAI,CAACH,aAAa,CAACI,YAAD,CAA7B,EAA6C;IAC3C,OAAOH,WAAW,CAACE,OAAD,EAAUC,YAAV,CAAlB;EACD;;EACD,OAAOA,YAAP;AACD"}, "metadata": {}, "sourceType": "module"}