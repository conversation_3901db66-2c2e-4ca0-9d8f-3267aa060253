{"ast": null, "code": "import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\n\nvar useUnmount = function useUnmount(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\n\nexport default useUnmount;", "map": {"version": 3, "names": ["useEffect", "useLatest", "isFunction", "isDev", "useUnmount", "fn", "console", "error", "concat", "fnRef", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useUnmount/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useUnmount = function useUnmount(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\nexport default useUnmount;"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,KAAP,MAAkB,gBAAlB;;AACA,IAAIC,UAAU,GAAG,SAASA,UAAT,CAAoBC,EAApB,EAAwB;EACvC,IAAIF,KAAJ,EAAW;IACT,IAAI,CAACD,UAAU,CAACG,EAAD,CAAf,EAAqB;MACnBC,OAAO,CAACC,KAAR,CAAc,oDAAoDC,MAApD,CAA2D,OAAOH,EAAlE,CAAd;IACD;EACF;;EACD,IAAII,KAAK,GAAGR,SAAS,CAACI,EAAD,CAArB;EACAL,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBS,KAAK,CAACC,OAAN;IACD,CAFD;EAGD,CAJQ,EAIN,EAJM,CAAT;AAKD,CAZD;;AAaA,eAAeN,UAAf"}, "metadata": {}, "sourceType": "module"}