{"ast": null, "code": "var cachePromise = new Map();\n\nvar getCachePromise = function getCachePromise(cacheKey) {\n  return cachePromise.get(cacheKey);\n};\n\nvar setCachePromise = function setCachePromise(cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise); // no use promise.finally for compatibility\n\n  promise.then(function (res) {\n    cachePromise[\"delete\"](cacheKey);\n    return res;\n  })[\"catch\"](function () {\n    cachePromise[\"delete\"](cacheKey);\n  });\n};\n\nexport { getCachePromise, setCachePromise };", "map": {"version": 3, "names": ["cachePromise", "Map", "getCachePromise", "cache<PERSON>ey", "get", "setCachePromise", "promise", "set", "then", "res"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/cachePromise.js"], "sourcesContent": ["var cachePromise = new Map();\nvar getCachePromise = function getCachePromise(cacheKey) {\n  return cachePromise.get(cacheKey);\n};\nvar setCachePromise = function setCachePromise(cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise);\n  // no use promise.finally for compatibility\n  promise.then(function (res) {\n    cachePromise[\"delete\"](cacheKey);\n    return res;\n  })[\"catch\"](function () {\n    cachePromise[\"delete\"](cacheKey);\n  });\n};\nexport { getCachePromise, setCachePromise };"], "mappings": "AAAA,IAAIA,YAAY,GAAG,IAAIC,GAAJ,EAAnB;;AACA,IAAIC,eAAe,GAAG,SAASA,eAAT,CAAyBC,QAAzB,EAAmC;EACvD,OAAOH,YAAY,CAACI,GAAb,CAAiBD,QAAjB,CAAP;AACD,CAFD;;AAGA,IAAIE,eAAe,GAAG,SAASA,eAAT,CAAyBF,QAAzB,EAAmCG,OAAnC,EAA4C;EAChE;EACA;EACAN,YAAY,CAACO,GAAb,CAAiBJ,QAAjB,EAA2BG,OAA3B,EAHgE,CAIhE;;EACAA,OAAO,CAACE,IAAR,CAAa,UAAUC,GAAV,EAAe;IAC1BT,YAAY,CAAC,QAAD,CAAZ,CAAuBG,QAAvB;IACA,OAAOM,GAAP;EACD,CAHD,EAGG,OAHH,EAGY,YAAY;IACtBT,YAAY,CAAC,QAAD,CAAZ,CAAuBG,QAAvB;EACD,CALD;AAMD,CAXD;;AAYA,SAASD,eAAT,EAA0BG,eAA1B"}, "metadata": {}, "sourceType": "module"}