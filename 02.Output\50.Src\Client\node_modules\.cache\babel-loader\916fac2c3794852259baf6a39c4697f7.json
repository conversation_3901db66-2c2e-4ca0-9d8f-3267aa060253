{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import reportWebVitals from'./reportWebVitals';import{StompSessionProvider}from'react-stomp-hooks';import{ErrorBoundary}from'react-error-boundary';import{<PERSON><PERSON>erRouter,useLocation}from'react-router-dom';import axios from'axios';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var root=ReactDOM.createRoot(document.getElementById('root'));var context=process.env.REACT_APP_SERVER_CONTEXT;if(context&&!context.startsWith('/')){context=\"/\".concat(context);}var ws=process.env.REACT_APP_WEBSOCKET;if(ws&&!ws.startsWith('/')){ws=\"/\".concat(ws);}var reconnectDelay=5000;// 5秒後に再接続を試みる\nvar handleStompConfig=function handleStompConfig(){return{// STOMP configurations\nreconnectDelay:reconnectDelay,// 再接続までの遅延時間\nonConnect:function onConnect(){console.log('Connected to WebSocket');},onDisconnect:function onDisconnect(){console.log('Disconnected from WebSocket, will attempt to reconnect');},onWebSocketClose:function onWebSocketClose(){console.log('WebSocket closed, will attempt to reconnect');}// その他の設定オプションを追加可能\n};};var RootComponent=function RootComponent(){var location=useLocation();var params=new URLSearchParams(location.search);var server_url=params.get('server_url');if(server_url==null){var host=window.location.hostname;var port=window.location.port;var protocol=window.location.protocol;server_url=\"\".concat(protocol,\"//\").concat(host,\":\").concat(port);}var contentErrorHandler=function contentErrorHandler(error,info){// Do something with the error\nvar data={name:error===null||error===void 0?void 0:error.name,message:error===null||error===void 0?void 0:error.messsage,stack:error===null||error===void 0?void 0:error.stack,componentStack:info===null||info===void 0?void 0:info.componentStack};// エラー情報をサーバーに送る\naxios({headers:{'Content-Type':'application/json'},method:'post',url:\"\".concat(server_url).concat(context,\"/saveFrontError\"),data:data}).then(function(res){if(res&&res.status===200){console.log('Upload error success.');}else{console.log('Upload error fail.');}});};return/*#__PURE__*/_jsx(ErrorBoundary,{FallbackComponent:ErrorFallback,onError:contentErrorHandler,onReset:function onReset(){// reset the state of your app so the error doesn't happen again\n},children:/*#__PURE__*/_jsx(StompSessionProvider,{url:\"\".concat(server_url).concat(context).concat(ws),config:handleStompConfig,children:/*#__PURE__*/_jsx(App,{})})});};/**\r\n * ErrorBoundaryでエラーをCatchした時に、ユーザーに表示する画面\r\n *\r\n * @param {*} { error, resetErrorBoundary }\r\n * @return {*} エラー画面\r\n */function ErrorFallback(_ref){var error=_ref.error,resetErrorBoundary=_ref.resetErrorBoundary;return/*#__PURE__*/_jsx(\"div\",{className:\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center text-3xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center text-green-400\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u30A8\\u30E9\\u30FC\\u767A\\u751F: \"}),/*#__PURE__*/_jsx(\"pre\",{children:error.message}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"button\",{className:\"text-red-800\",onClick:resetErrorBoundary,children:\"\\u3053\\u3053\\u3092\\u30AF\\u30EA\\u30C3\\u30AF\\u3057\\u3066\\u3001\\u3082\\u3046\\u4E00\\u5EA6\\u8A66\\u3057\\u3066\\u304F\\u3060\\u3055\\u3044\"})]})});}root.render(/*#__PURE__*/_jsx(BrowserRouter,{basename:process.env.REACT_APP_APP_URL,children:/*#__PURE__*/_jsx(RootComponent,{})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "StompSessionProvider", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocation", "axios", "root", "createRoot", "document", "getElementById", "context", "process", "env", "REACT_APP_SERVER_CONTEXT", "startsWith", "ws", "REACT_APP_WEBSOCKET", "reconnectDelay", "handleStompConfig", "onConnect", "console", "log", "onDisconnect", "onWebSocketClose", "RootComponent", "location", "params", "URLSearchParams", "search", "server_url", "get", "host", "window", "hostname", "port", "protocol", "contentErrorHandler", "error", "info", "data", "name", "message", "messsage", "stack", "componentStack", "headers", "method", "url", "then", "res", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetErrorBoundary", "render", "REACT_APP_APP_URL"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport './index.css';\r\nimport App from './App';\r\nimport reportWebVitals from './reportWebVitals';\r\nimport { StompSessionProvider } from 'react-stomp-hooks';\r\nimport { ErrorBoundary } from 'react-error-boundary';\r\nimport { <PERSON>rowserRouter, useLocation } from 'react-router-dom';\r\nimport axios from 'axios';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\n\r\nlet context = process.env.REACT_APP_SERVER_CONTEXT;\r\nif (context && !context.startsWith('/')) {\r\n  context = `/${context}`;\r\n}\r\nlet ws = process.env.REACT_APP_WEBSOCKET;\r\nif (ws && !ws.startsWith('/')) {\r\n  ws = `/${ws}`;\r\n}\r\n\r\nconst reconnectDelay = 5000; // 5秒後に再接続を試みる\r\n\r\nconst handleStompConfig = () => ({\r\n  // STOMP configurations\r\n  reconnectDelay, // 再接続までの遅延時間\r\n  onConnect: () => {\r\n    console.log('Connected to WebSocket');\r\n  },\r\n  onDisconnect: () => {\r\n    console.log('Disconnected from WebSocket, will attempt to reconnect');\r\n  },\r\n  onWebSocketClose: () => {\r\n    console.log('WebSocket closed, will attempt to reconnect');\r\n  },\r\n  // その他の設定オプションを追加可能\r\n});\r\n\r\nconst RootComponent = () => {\r\n\r\n  const location = useLocation();\r\n  const params = new URLSearchParams(location.search);\r\n  let server_url = params.get('server_url');\r\n\r\n  if (server_url == null) {\r\n    const host = window.location.hostname;\r\n    const port = window.location.port;\r\n    const protocol = window.location.protocol;\r\n    server_url = `${protocol}//${host}:${port}`;\r\n  }\r\n\r\n\r\n  const contentErrorHandler = (error, info) => {\r\n    // Do something with the error\r\n    let data = {\r\n      name: error?.name,\r\n      message: error?.messsage,\r\n      stack: error?.stack,\r\n      componentStack: info?.componentStack,\r\n    };\r\n\r\n    // エラー情報をサーバーに送る\r\n    axios({\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      method: 'post',\r\n      url: `${server_url}${context}/saveFrontError`,\r\n      data: data,\r\n    }).then((res) => {\r\n      if (res && res.status === 200) {\r\n        console.log('Upload error success.');\r\n      } else {\r\n        console.log('Upload error fail.');\r\n      }\r\n    });\r\n  };\r\n\r\n  return (\r\n    <ErrorBoundary\r\n      FallbackComponent={ErrorFallback}\r\n      onError={contentErrorHandler}\r\n      onReset={() => {\r\n        // reset the state of your app so the error doesn't happen again\r\n      }}\r\n    >\r\n      <StompSessionProvider\r\n        url={`${server_url}${context}${ws}`}\r\n        config={handleStompConfig}\r\n      >\r\n        <App />\r\n      </StompSessionProvider>\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\n/**\r\n * ErrorBoundaryでエラーをCatchした時に、ユーザーに表示する画面\r\n *\r\n * @param {*} { error, resetErrorBoundary }\r\n * @return {*} エラー画面\r\n */\r\nfunction ErrorFallback({ error, resetErrorBoundary }) {\r\n  return (\r\n    <div className=\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center text-3xl\">\r\n      <div className=\"flex flex-col items-center text-green-400\">\r\n        <p>エラー発生: </p>\r\n        <pre>{error.message}</pre>\r\n        <br />\r\n        <br />\r\n        <button className=\"text-red-800\" onClick={resetErrorBoundary}>\r\n          ここをクリックして、もう一度試してください\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nroot.render(\r\n  <BrowserRouter basename={process.env.REACT_APP_APP_URL}>\r\n    <RootComponent />\r\n  </BrowserRouter>\r\n);\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\nreportWebVitals();\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,SAAP,KAAqB,kBAArB,CACA,MAAO,aAAP,CACA,MAAOC,IAAP,KAAgB,OAAhB,CACA,MAAOC,gBAAP,KAA4B,mBAA5B,CACA,OAASC,oBAAT,KAAqC,mBAArC,CACA,OAASC,aAAT,KAA8B,sBAA9B,CACA,OAASC,aAAT,CAAwBC,WAAxB,KAA2C,kBAA3C,CACA,MAAOC,MAAP,KAAkB,OAAlB,C,wFAEA,GAAMC,KAAI,CAAGR,QAAQ,CAACS,UAAT,CAAoBC,QAAQ,CAACC,cAAT,CAAwB,MAAxB,CAApB,CAAb,CAEA,GAAIC,QAAO,CAAGC,OAAO,CAACC,GAAR,CAAYC,wBAA1B,CACA,GAAIH,OAAO,EAAI,CAACA,OAAO,CAACI,UAAR,CAAmB,GAAnB,CAAhB,CAAyC,CACvCJ,OAAO,YAAOA,OAAP,CAAP,CACD,CACD,GAAIK,GAAE,CAAGJ,OAAO,CAACC,GAAR,CAAYI,mBAArB,CACA,GAAID,EAAE,EAAI,CAACA,EAAE,CAACD,UAAH,CAAc,GAAd,CAAX,CAA+B,CAC7BC,EAAE,YAAOA,EAAP,CAAF,CACD,CAED,GAAME,eAAc,CAAG,IAAvB,CAA6B;AAE7B,GAAMC,kBAAiB,CAAG,QAApBA,kBAAoB,SAAO,CAC/B;AACAD,cAAc,CAAdA,cAF+B,CAEf;AAChBE,SAAS,CAAE,oBAAM,CACfC,OAAO,CAACC,GAAR,CAAY,wBAAZ,EACD,CAL8B,CAM/BC,YAAY,CAAE,uBAAM,CAClBF,OAAO,CAACC,GAAR,CAAY,wDAAZ,EACD,CAR8B,CAS/BE,gBAAgB,CAAE,2BAAM,CACtBH,OAAO,CAACC,GAAR,CAAY,6CAAZ,EACD,CACD;AAZ+B,CAAP,EAA1B,CAeA,GAAMG,cAAa,CAAG,QAAhBA,cAAgB,EAAM,CAE1B,GAAMC,SAAQ,CAAGrB,WAAW,EAA5B,CACA,GAAMsB,OAAM,CAAG,GAAIC,gBAAJ,CAAoBF,QAAQ,CAACG,MAA7B,CAAf,CACA,GAAIC,WAAU,CAAGH,MAAM,CAACI,GAAP,CAAW,YAAX,CAAjB,CAEA,GAAID,UAAU,EAAI,IAAlB,CAAwB,CACtB,GAAME,KAAI,CAAGC,MAAM,CAACP,QAAP,CAAgBQ,QAA7B,CACA,GAAMC,KAAI,CAAGF,MAAM,CAACP,QAAP,CAAgBS,IAA7B,CACA,GAAMC,SAAQ,CAAGH,MAAM,CAACP,QAAP,CAAgBU,QAAjC,CACAN,UAAU,WAAMM,QAAN,cAAmBJ,IAAnB,aAA2BG,IAA3B,CAAV,CACD,CAGD,GAAME,oBAAmB,CAAG,QAAtBA,oBAAsB,CAACC,KAAD,CAAQC,IAAR,CAAiB,CAC3C;AACA,GAAIC,KAAI,CAAG,CACTC,IAAI,CAAEH,KAAF,SAAEA,KAAF,iBAAEA,KAAK,CAAEG,IADJ,CAETC,OAAO,CAAEJ,KAAF,SAAEA,KAAF,iBAAEA,KAAK,CAAEK,QAFP,CAGTC,KAAK,CAAEN,KAAF,SAAEA,KAAF,iBAAEA,KAAK,CAAEM,KAHL,CAITC,cAAc,CAAEN,IAAF,SAAEA,IAAF,iBAAEA,IAAI,CAAEM,cAJb,CAAX,CAOA;AACAvC,KAAK,CAAC,CACJwC,OAAO,CAAE,CACP,eAAgB,kBADT,CADL,CAIJC,MAAM,CAAE,MAJJ,CAKJC,GAAG,WAAKlB,UAAL,SAAkBnB,OAAlB,mBALC,CAMJ6B,IAAI,CAAEA,IANF,CAAD,CAAL,CAOGS,IAPH,CAOQ,SAACC,GAAD,CAAS,CACf,GAAIA,GAAG,EAAIA,GAAG,CAACC,MAAJ,GAAe,GAA1B,CAA+B,CAC7B9B,OAAO,CAACC,GAAR,CAAY,uBAAZ,EACD,CAFD,IAEO,CACLD,OAAO,CAACC,GAAR,CAAY,oBAAZ,EACD,CACF,CAbD,EAcD,CAxBD,CA0BA,mBACE,KAAC,aAAD,EACE,iBAAiB,CAAE8B,aADrB,CAEE,OAAO,CAAEf,mBAFX,CAGE,OAAO,CAAE,kBAAM,CACb;AACD,CALH,uBAOE,KAAC,oBAAD,EACE,GAAG,WAAKP,UAAL,SAAkBnB,OAAlB,SAA4BK,EAA5B,CADL,CAEE,MAAM,CAAEG,iBAFV,uBAIE,KAAC,GAAD,IAJF,EAPF,EADF,CAgBD,CAxDD,CA0DA;AACA;AACA;AACA;AACA;AACA,GACA,QAASiC,cAAT,MAAsD,IAA7Bd,MAA6B,MAA7BA,KAA6B,CAAtBe,kBAAsB,MAAtBA,kBAAsB,CACpD,mBACE,YAAK,SAAS,CAAC,kFAAf,uBACE,aAAK,SAAS,CAAC,2CAAf,wBACE,uDADF,cAEE,qBAAMf,KAAK,CAACI,OAAZ,EAFF,cAGE,aAHF,cAIE,aAJF,cAKE,eAAQ,SAAS,CAAC,cAAlB,CAAiC,OAAO,CAAEW,kBAA1C,4IALF,GADF,EADF,CAaD,CAED9C,IAAI,CAAC+C,MAAL,cACE,KAAC,aAAD,EAAe,QAAQ,CAAE1C,OAAO,CAACC,GAAR,CAAY0C,iBAArC,uBACE,KAAC,aAAD,IADF,EADF,EAMA;AACA;AACA;AACAtD,eAAe"}, "metadata": {}, "sourceType": "module"}