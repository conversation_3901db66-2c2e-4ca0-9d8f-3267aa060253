{"ast": null, "code": "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\nmodule.exports = freeGlobal;", "map": {"version": 3, "names": ["freeGlobal", "global", "Object", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_freeGlobal.js"], "sourcesContent": ["/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAG,OAAOC,MAAP,IAAiB,QAAjB,IAA6BA,MAA7B,IAAuCA,MAAM,CAACC,MAAP,KAAkBA,MAAzD,IAAmED,MAApF;AAEAE,MAAM,CAACC,OAAP,GAAiBJ,UAAjB"}, "metadata": {}, "sourceType": "script"}