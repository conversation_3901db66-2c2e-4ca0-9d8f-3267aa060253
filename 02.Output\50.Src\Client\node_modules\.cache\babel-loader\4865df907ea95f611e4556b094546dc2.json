{"ast": null, "code": "/**\n * Call [Client#subscribe]{@link Client#subscribe} to create a StompSubscription.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompSubscription {}", "map": {"version": 3, "mappings": "AAEA;;;;;AAKA,OAAM,MAAOA,iBAAP,CAAwB", "names": ["StompSubscription"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\stomp-subscription.ts"], "sourcesContent": ["import { StompHeaders } from './stomp-headers';\n\n/**\n * Call [Client#subscribe]{@link Client#subscribe} to create a StompSubscription.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompSubscription {\n  /**\n   * Id associated with this subscription.\n   */\n  public id: string;\n\n  /**\n   * Unsubscribe. See [Client#unsubscribe]{@link Client#unsubscribe} for an example.\n   */\n  public unsubscribe: (headers?: StompHeaders) => void;\n}\n"]}, "metadata": {}, "sourceType": "module"}