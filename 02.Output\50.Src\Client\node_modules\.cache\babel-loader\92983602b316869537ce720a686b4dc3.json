{"ast": null, "code": "var freeGlobal = require('./_freeGlobal');\n/** Detect free variable `exports`. */\n\n\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n/** Detect free variable `module`. */\n\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n/** Detect the popular CommonJS extension `module.exports`. */\n\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n/** Detect free variable `process` from Node.js. */\n\nvar freeProcess = moduleExports && freeGlobal.process;\n/** Used to access faster Node.js helpers. */\n\nvar nodeUtil = function () {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    } // Legacy `process.binding('util')` for Node.js < 10.\n\n\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}();\n\nmodule.exports = nodeUtil;", "map": {"version": 3, "names": ["freeGlobal", "require", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "freeProcess", "process", "nodeUtil", "types", "binding", "e"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_nodeUtil.js"], "sourcesContent": ["var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = (function() {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}());\n\nmodule.exports = nodeUtil;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;;;AACA,IAAIC,WAAW,GAAG,OAAOC,OAAP,IAAkB,QAAlB,IAA8BA,OAA9B,IAAyC,CAACA,OAAO,CAACC,QAAlD,IAA8DD,OAAhF;AAEA;;AACA,IAAIE,UAAU,GAAGH,WAAW,IAAI,OAAOI,MAAP,IAAiB,QAAhC,IAA4CA,MAA5C,IAAsD,CAACA,MAAM,CAACF,QAA9D,IAA0EE,MAA3F;AAEA;;AACA,IAAIC,aAAa,GAAGF,UAAU,IAAIA,UAAU,CAACF,OAAX,KAAuBD,WAAzD;AAEA;;AACA,IAAIM,WAAW,GAAGD,aAAa,IAAIP,UAAU,CAACS,OAA9C;AAEA;;AACA,IAAIC,QAAQ,GAAI,YAAW;EACzB,IAAI;IACF;IACA,IAAIC,KAAK,GAAGN,UAAU,IAAIA,UAAU,CAACJ,OAAzB,IAAoCI,UAAU,CAACJ,OAAX,CAAmB,MAAnB,EAA2BU,KAA3E;;IAEA,IAAIA,KAAJ,EAAW;MACT,OAAOA,KAAP;IACD,CANC,CAQF;;;IACA,OAAOH,WAAW,IAAIA,WAAW,CAACI,OAA3B,IAAsCJ,WAAW,CAACI,OAAZ,CAAoB,MAApB,CAA7C;EACD,CAVD,CAUE,OAAOC,CAAP,EAAU,CAAE;AACf,CAZe,EAAhB;;AAcAP,MAAM,CAACH,OAAP,GAAiBO,QAAjB"}, "metadata": {}, "sourceType": "script"}