{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    AjaxBasedTransport = require('./lib/ajax-based'),\n    EventSourceReceiver = require('./receiver/eventsource'),\n    XHRCorsObject = require('./sender/xhr-cors'),\n    EventSourceDriver = require('eventsource');\n\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\n\ninherits(EventSourceTransport, AjaxBasedTransport);\n\nEventSourceTransport.enabled = function () {\n  return !!EventSourceDriver;\n};\n\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\nmodule.exports = EventSourceTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "EventSourceReceiver", "XHRCorsObject", "EventSourceDriver", "EventSourceTransport", "transUrl", "enabled", "Error", "call", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/eventsource.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , EventSourceReceiver = require('./receiver/eventsource')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , EventSourceDriver = require('eventsource')\n  ;\n\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\n\ninherits(EventSourceTransport, AjaxBasedTransport);\n\nEventSourceTransport.enabled = function() {\n  return !!EventSourceDriver;\n};\n\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\n\nmodule.exports = EventSourceTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,kBAAkB,GAAGD,OAAO,CAAC,kBAAD,CADhC;AAAA,IAEIE,mBAAmB,GAAGF,OAAO,CAAC,wBAAD,CAFjC;AAAA,IAGIG,aAAa,GAAGH,OAAO,CAAC,mBAAD,CAH3B;AAAA,IAIII,iBAAiB,GAAGJ,OAAO,CAAC,aAAD,CAJ/B;;AAOA,SAASK,oBAAT,CAA8BC,QAA9B,EAAwC;EACtC,IAAI,CAACD,oBAAoB,CAACE,OAArB,EAAL,EAAqC;IACnC,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EAEDP,kBAAkB,CAACQ,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,cAAxC,EAAwDJ,mBAAxD,EAA6EC,aAA7E;AACD;;AAEDJ,QAAQ,CAACM,oBAAD,EAAuBJ,kBAAvB,CAAR;;AAEAI,oBAAoB,CAACE,OAArB,GAA+B,YAAW;EACxC,OAAO,CAAC,CAACH,iBAAT;AACD,CAFD;;AAIAC,oBAAoB,CAACK,aAArB,GAAqC,aAArC;AACAL,oBAAoB,CAACM,UAArB,GAAkC,CAAlC;AAEAC,MAAM,CAACC,OAAP,GAAiBR,oBAAjB"}, "metadata": {}, "sourceType": "script"}