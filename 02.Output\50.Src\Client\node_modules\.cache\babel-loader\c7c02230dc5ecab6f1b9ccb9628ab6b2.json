{"ast": null, "code": "import { useRef } from 'react';\nexport var createUpdateEffect = function createUpdateEffect(hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false); // for react-refresh\n\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;", "map": {"version": 3, "names": ["useRef", "createUpdateEffect", "hook", "effect", "deps", "isMounted", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/createUpdateEffect/index.js"], "sourcesContent": ["import { useRef } from 'react';\nexport var createUpdateEffect = function createUpdateEffect(hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false);\n    // for react-refresh\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAO,IAAIC,kBAAkB,GAAG,SAASA,kBAAT,CAA4BC,IAA5B,EAAkC;EAChE,OAAO,UAAUC,MAAV,EAAkBC,IAAlB,EAAwB;IAC7B,IAAIC,SAAS,GAAGL,MAAM,CAAC,KAAD,CAAtB,CAD6B,CAE7B;;IACAE,IAAI,CAAC,YAAY;MACf,OAAO,YAAY;QACjBG,SAAS,CAACC,OAAV,GAAoB,KAApB;MACD,CAFD;IAGD,CAJG,EAID,EAJC,CAAJ;IAKAJ,IAAI,CAAC,YAAY;MACf,IAAI,CAACG,SAAS,CAACC,OAAf,EAAwB;QACtBD,SAAS,CAACC,OAAV,GAAoB,IAApB;MACD,CAFD,MAEO;QACL,OAAOH,MAAM,EAAb;MACD;IACF,CANG,EAMDC,IANC,CAAJ;EAOD,CAfD;AAgBD,CAjBM;AAkBP,eAAeH,kBAAf"}, "metadata": {}, "sourceType": "module"}