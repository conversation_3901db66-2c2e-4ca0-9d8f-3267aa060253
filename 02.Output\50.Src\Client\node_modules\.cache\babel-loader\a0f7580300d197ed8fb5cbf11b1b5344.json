{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n      m = s && o[s],\n      i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget'; // 键盘事件 keyCode 别名\n\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  \"delete\": 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n}; // 修饰键\n\nvar modifierKey = {\n  ctrl: function ctrl(event) {\n    return event.ctrlKey;\n  },\n  shift: function shift(event) {\n    return event.shiftKey;\n  },\n  alt: function alt(event) {\n    return event.altKey;\n  },\n  meta: function meta(event) {\n    return event.metaKey;\n  }\n}; // 根据 event 计算激活键数量\n\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n\n    return total;\n  }, 0); // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns Boolean\n */\n\n\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a; // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n\n\n  if (!event.key) {\n    return false;\n  } // 数字类型直接匹配事件的 keyCode\n\n\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter;\n  } // 字符串依次判断是否有组合键\n\n\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value; // 组合键\n\n      var genModifier = modifierKey[key]; // keyCode 别名\n\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1[\"return\"])) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n\n\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length;\n  }\n\n  return genLen === genArr.length;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\n\n\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n\n  if (isString(keyFilter) || isNumber(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.some(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n\n  return keyFilter ? function () {\n    return true;\n  } : function () {\n    return false;\n  };\n}\n\nvar defaultEvents = ['keydown'];\n\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n      _b = _a.events,\n      events = _b === void 0 ? defaultEvents : _b,\n      target = _a.target,\n      _c = _a.exactMatch,\n      exactMatch = _c === void 0 ? false : _c;\n\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n\n    var _b;\n\n    var el = getTargetElement(target, window);\n\n    if (!el) {\n      return;\n    }\n\n    var callbackHandler = function callbackHandler(event) {\n      var _a;\n\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n\n      if (genGuard(event)) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event);\n      }\n    };\n\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1[\"return\"])) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n\n    return function () {\n      var e_3, _a;\n\n      var _b;\n\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2[\"return\"])) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\n\nexport default useKeyPress;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "useLatest", "isFunction", "isNumber", "isString", "getTargetElement", "useDeepCompareEffectWithTarget", "aliasKeyCodeMap", "backspace", "tab", "enter", "shift", "ctrl", "alt", "pausebreak", "capslock", "esc", "space", "pageup", "pagedown", "end", "home", "leftarrow", "uparrow", "rightarrow", "downarrow", "insert", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l", "n", "p", "q", "r", "t", "u", "v", "w", "x", "y", "z", "leftwindowkey", "rightwindow<PERSON>", "selectkey", "numpad0", "numpad1", "numpad2", "numpad3", "numpad4", "numpad5", "numpad6", "numpad7", "numpad8", "numpad9", "multiply", "add", "subtract", "decimalpoint", "divide", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8", "f9", "f10", "f11", "f12", "numlock", "scrolllock", "semicolon", "equalsign", "comma", "dash", "period", "forwardslash", "<PERSON><PERSON><PERSON>", "openbracket", "backslash", "closebracket", "singlequote", "modifierKey", "event", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "meta", "metaKey", "count<PERSON>ey<PERSON>yEvent", "countOfModifier", "Object", "keys", "reduce", "total", "key", "includes", "keyCode", "gen<PERSON><PERSON>er<PERSON><PERSON>", "keyFilter", "exactMatch", "e_1", "_a", "genArr", "split", "genLen", "genArr_1", "genArr_1_1", "genModifier", "aliasKeyCode", "toLowerCase", "e_1_1", "error", "gen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "some", "item", "defaultEvents", "useKeyPress", "<PERSON><PERSON><PERSON><PERSON>", "option", "_b", "events", "target", "_c", "eventHandlerRef", "keyFilterRef", "e_2", "el", "window", "call<PERSON><PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "current", "events_1", "events_1_1", "eventName", "addEventListener", "e_2_1", "e_3", "events_2", "events_2_1", "removeEventListener", "e_3_1"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useKeyPress/index.js"], "sourcesContent": ["var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\n// 键盘事件 keyCode 别名\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  \"delete\": 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n};\n// 修饰键\nvar modifierKey = {\n  ctrl: function ctrl(event) {\n    return event.ctrlKey;\n  },\n  shift: function shift(event) {\n    return event.shiftKey;\n  },\n  alt: function alt(event) {\n    return event.altKey;\n  },\n  meta: function meta(event) {\n    return event.metaKey;\n  }\n};\n// 根据 event 计算激活键数量\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n    return total;\n  }, 0);\n  // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns Boolean\n */\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a;\n  // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n  if (!event.key) {\n    return false;\n  }\n  // 数字类型直接匹配事件的 keyCode\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter;\n  }\n  // 字符串依次判断是否有组合键\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value;\n      // 组合键\n      var genModifier = modifierKey[key];\n      // keyCode 别名\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1[\"return\"])) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length;\n  }\n  return genLen === genArr.length;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n  if (isString(keyFilter) || isNumber(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.some(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n  return keyFilter ? function () {\n    return true;\n  } : function () {\n    return false;\n  };\n}\nvar defaultEvents = ['keydown'];\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n    _b = _a.events,\n    events = _b === void 0 ? defaultEvents : _b,\n    target = _a.target,\n    _c = _a.exactMatch,\n    exactMatch = _c === void 0 ? false : _c;\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n    var _b;\n    var el = getTargetElement(target, window);\n    if (!el) {\n      return;\n    }\n    var callbackHandler = function callbackHandler(event) {\n      var _a;\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n      if (genGuard(event)) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event);\n      }\n    };\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1[\"return\"])) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return function () {\n      var e_3, _a;\n      var _b;\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2[\"return\"])) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\nexport default useKeyPress;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,UAAUC,CAAV,EAAa;EACnD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA/C;EAAA,IACEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAD,CADZ;EAAA,IAEEI,CAAC,GAAG,CAFN;EAGA,IAAID,CAAJ,EAAO,OAAOA,CAAC,CAACE,IAAF,CAAON,CAAP,CAAP;EACP,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAT,KAAoB,QAA7B,EAAuC,OAAO;IAC5CC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAhB,EAAwBP,CAAC,GAAG,KAAK,CAAT;MACxB,OAAO;QACLS,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAF,CADR;QAELK,IAAI,EAAE,CAACV;MAFF,CAAP;IAID;EAP2C,CAAP;EASvC,MAAM,IAAIW,SAAJ,CAAcV,CAAC,GAAG,yBAAH,GAA+B,iCAA9C,CAAN;AACD,CAfD;;AAgBA,OAAOW,SAAP,MAAsB,cAAtB;AACA,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,QAA/B,QAA+C,UAA/C;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,8BAAP,MAA2C,mCAA3C,C,CACA;;AACA,IAAIC,eAAe,GAAG;EACpB,KAAK,EADe;EAEpB,KAAK,EAFe;EAGpB,KAAK,EAHe;EAIpB,KAAK,EAJe;EAKpB,KAAK,EALe;EAMpB,KAAK,EANe;EAOpB,KAAK,EAPe;EAQpB,KAAK,EARe;EASpB,KAAK,EATe;EAUpB,KAAK,EAVe;EAWpBC,SAAS,EAAE,CAXS;EAYpBC,GAAG,EAAE,CAZe;EAapBC,KAAK,EAAE,EAba;EAcpBC,KAAK,EAAE,EAda;EAepBC,IAAI,EAAE,EAfc;EAgBpBC,GAAG,EAAE,EAhBe;EAiBpBC,UAAU,EAAE,EAjBQ;EAkBpBC,QAAQ,EAAE,EAlBU;EAmBpBC,GAAG,EAAE,EAnBe;EAoBpBC,KAAK,EAAE,EApBa;EAqBpBC,MAAM,EAAE,EArBY;EAsBpBC,QAAQ,EAAE,EAtBU;EAuBpBC,GAAG,EAAE,EAvBe;EAwBpBC,IAAI,EAAE,EAxBc;EAyBpBC,SAAS,EAAE,EAzBS;EA0BpBC,OAAO,EAAE,EA1BW;EA2BpBC,UAAU,EAAE,EA3BQ;EA4BpBC,SAAS,EAAE,EA5BS;EA6BpBC,MAAM,EAAE,EA7BY;EA8BpB,UAAU,EA9BU;EA+BpBC,CAAC,EAAE,EA/BiB;EAgCpBC,CAAC,EAAE,EAhCiB;EAiCpBC,CAAC,EAAE,EAjCiB;EAkCpBC,CAAC,EAAE,EAlCiB;EAmCpBC,CAAC,EAAE,EAnCiB;EAoCpBC,CAAC,EAAE,EApCiB;EAqCpBC,CAAC,EAAE,EArCiB;EAsCpBC,CAAC,EAAE,EAtCiB;EAuCpBxC,CAAC,EAAE,EAvCiB;EAwCpByC,CAAC,EAAE,EAxCiB;EAyCpBC,CAAC,EAAE,EAzCiB;EA0CpBC,CAAC,EAAE,EA1CiB;EA2CpB5C,CAAC,EAAE,EA3CiB;EA4CpB6C,CAAC,EAAE,EA5CiB;EA6CpBjD,CAAC,EAAE,EA7CiB;EA8CpBkD,CAAC,EAAE,EA9CiB;EA+CpBC,CAAC,EAAE,EA/CiB;EAgDpBC,CAAC,EAAE,EAhDiB;EAiDpBnD,CAAC,EAAE,EAjDiB;EAkDpBoD,CAAC,EAAE,EAlDiB;EAmDpBC,CAAC,EAAE,EAnDiB;EAoDpBC,CAAC,EAAE,EApDiB;EAqDpBC,CAAC,EAAE,EArDiB;EAsDpBC,CAAC,EAAE,EAtDiB;EAuDpBC,CAAC,EAAE,EAvDiB;EAwDpBC,CAAC,EAAE,EAxDiB;EAyDpBC,aAAa,EAAE,EAzDK;EA0DpBC,cAAc,EAAE,EA1DI;EA2DpBC,SAAS,EAAE,EA3DS;EA4DpBC,OAAO,EAAE,EA5DW;EA6DpBC,OAAO,EAAE,EA7DW;EA8DpBC,OAAO,EAAE,EA9DW;EA+DpBC,OAAO,EAAE,EA/DW;EAgEpBC,OAAO,EAAE,GAhEW;EAiEpBC,OAAO,EAAE,GAjEW;EAkEpBC,OAAO,EAAE,GAlEW;EAmEpBC,OAAO,EAAE,GAnEW;EAoEpBC,OAAO,EAAE,GApEW;EAqEpBC,OAAO,EAAE,GArEW;EAsEpBC,QAAQ,EAAE,GAtEU;EAuEpBC,GAAG,EAAE,GAvEe;EAwEpBC,QAAQ,EAAE,GAxEU;EAyEpBC,YAAY,EAAE,GAzEM;EA0EpBC,MAAM,EAAE,GA1EY;EA2EpBC,EAAE,EAAE,GA3EgB;EA4EpBC,EAAE,EAAE,GA5EgB;EA6EpBC,EAAE,EAAE,GA7EgB;EA8EpBC,EAAE,EAAE,GA9EgB;EA+EpBC,EAAE,EAAE,GA/EgB;EAgFpBC,EAAE,EAAE,GAhFgB;EAiFpBC,EAAE,EAAE,GAjFgB;EAkFpBC,EAAE,EAAE,GAlFgB;EAmFpBC,EAAE,EAAE,GAnFgB;EAoFpBC,GAAG,EAAE,GApFe;EAqFpBC,GAAG,EAAE,GArFe;EAsFpBC,GAAG,EAAE,GAtFe;EAuFpBC,OAAO,EAAE,GAvFW;EAwFpBC,UAAU,EAAE,GAxFQ;EAyFpBC,SAAS,EAAE,GAzFS;EA0FpBC,SAAS,EAAE,GA1FS;EA2FpBC,KAAK,EAAE,GA3Fa;EA4FpBC,IAAI,EAAE,GA5Fc;EA6FpBC,MAAM,EAAE,GA7FY;EA8FpBC,YAAY,EAAE,GA9FM;EA+FpBC,WAAW,EAAE,GA/FO;EAgGpBC,WAAW,EAAE,GAhGO;EAiGpBC,SAAS,EAAE,GAjGS;EAkGpBC,YAAY,EAAE,GAlGM;EAmGpBC,WAAW,EAAE;AAnGO,CAAtB,C,CAqGA;;AACA,IAAIC,WAAW,GAAG;EAChBhF,IAAI,EAAE,SAASA,IAAT,CAAciF,KAAd,EAAqB;IACzB,OAAOA,KAAK,CAACC,OAAb;EACD,CAHe;EAIhBnF,KAAK,EAAE,SAASA,KAAT,CAAekF,KAAf,EAAsB;IAC3B,OAAOA,KAAK,CAACE,QAAb;EACD,CANe;EAOhBlF,GAAG,EAAE,SAASA,GAAT,CAAagF,KAAb,EAAoB;IACvB,OAAOA,KAAK,CAACG,MAAb;EACD,CATe;EAUhBC,IAAI,EAAE,SAASA,IAAT,CAAcJ,KAAd,EAAqB;IACzB,OAAOA,KAAK,CAACK,OAAb;EACD;AAZe,CAAlB,C,CAcA;;AACA,SAASC,eAAT,CAAyBN,KAAzB,EAAgC;EAC9B,IAAIO,eAAe,GAAGC,MAAM,CAACC,IAAP,CAAYV,WAAZ,EAAyBW,MAAzB,CAAgC,UAAUC,KAAV,EAAiBC,GAAjB,EAAsB;IAC1E,IAAIb,WAAW,CAACa,GAAD,CAAX,CAAiBZ,KAAjB,CAAJ,EAA6B;MAC3B,OAAOW,KAAK,GAAG,CAAf;IACD;;IACD,OAAOA,KAAP;EACD,CALqB,EAKnB,CALmB,CAAtB,CAD8B,CAO9B;;EACA,OAAO,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqBE,QAArB,CAA8Bb,KAAK,CAACc,OAApC,IAA+CP,eAA/C,GAAiEA,eAAe,GAAG,CAA1F;AACD;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASQ,YAAT,CAAsBf,KAAtB,EAA6BgB,SAA7B,EAAwCC,UAAxC,EAAoD;EAClD,IAAIC,GAAJ,EAASC,EAAT,CADkD,CAElD;;;EACA,IAAI,CAACnB,KAAK,CAACY,GAAX,EAAgB;IACd,OAAO,KAAP;EACD,CALiD,CAMlD;;;EACA,IAAItG,QAAQ,CAAC0G,SAAD,CAAZ,EAAyB;IACvB,OAAOhB,KAAK,CAACc,OAAN,KAAkBE,SAAzB;EACD,CATiD,CAUlD;;;EACA,IAAII,MAAM,GAAGJ,SAAS,CAACK,KAAV,CAAgB,GAAhB,CAAb;EACA,IAAIC,MAAM,GAAG,CAAb;;EACA,IAAI;IACF,KAAK,IAAIC,QAAQ,GAAGhI,QAAQ,CAAC6H,MAAD,CAAvB,EAAiCI,UAAU,GAAGD,QAAQ,CAACvH,IAAT,EAAnD,EAAoE,CAACwH,UAAU,CAACtH,IAAhF,EAAsFsH,UAAU,GAAGD,QAAQ,CAACvH,IAAT,EAAnG,EAAoH;MAClH,IAAI4G,GAAG,GAAGY,UAAU,CAACvH,KAArB,CADkH,CAElH;;MACA,IAAIwH,WAAW,GAAG1B,WAAW,CAACa,GAAD,CAA7B,CAHkH,CAIlH;;MACA,IAAIc,YAAY,GAAGhH,eAAe,CAACkG,GAAG,CAACe,WAAJ,EAAD,CAAlC;;MACA,IAAIF,WAAW,IAAIA,WAAW,CAACzB,KAAD,CAA1B,IAAqC0B,YAAY,IAAIA,YAAY,KAAK1B,KAAK,CAACc,OAAhF,EAAyF;QACvFQ,MAAM;MACP;IACF;EACF,CAXD,CAWE,OAAOM,KAAP,EAAc;IACdV,GAAG,GAAG;MACJW,KAAK,EAAED;IADH,CAAN;EAGD,CAfD,SAeU;IACR,IAAI;MACF,IAAIJ,UAAU,IAAI,CAACA,UAAU,CAACtH,IAA1B,KAAmCiH,EAAE,GAAGI,QAAQ,CAAC,QAAD,CAAhD,CAAJ,EAAiEJ,EAAE,CAACrH,IAAH,CAAQyH,QAAR;IAClE,CAFD,SAEU;MACR,IAAIL,GAAJ,EAAS,MAAMA,GAAG,CAACW,KAAV;IACV;EACF;EACD;AACF;AACA;AACA;AACA;AACA;;;EACE,IAAIZ,UAAJ,EAAgB;IACd,OAAOK,MAAM,KAAKF,MAAM,CAACrH,MAAlB,IAA4BuG,eAAe,CAACN,KAAD,CAAf,KAA2BoB,MAAM,CAACrH,MAArE;EACD;;EACD,OAAOuH,MAAM,KAAKF,MAAM,CAACrH,MAAzB;AACD;AACD;AACA;AACA;AACA;AACA;;;AACA,SAAS+H,eAAT,CAAyBd,SAAzB,EAAoCC,UAApC,EAAgD;EAC9C,IAAI5G,UAAU,CAAC2G,SAAD,CAAd,EAA2B;IACzB,OAAOA,SAAP;EACD;;EACD,IAAIzG,QAAQ,CAACyG,SAAD,CAAR,IAAuB1G,QAAQ,CAAC0G,SAAD,CAAnC,EAAgD;IAC9C,OAAO,UAAUhB,KAAV,EAAiB;MACtB,OAAOe,YAAY,CAACf,KAAD,EAAQgB,SAAR,EAAmBC,UAAnB,CAAnB;IACD,CAFD;EAGD;;EACD,IAAIc,KAAK,CAACC,OAAN,CAAchB,SAAd,CAAJ,EAA8B;IAC5B,OAAO,UAAUhB,KAAV,EAAiB;MACtB,OAAOgB,SAAS,CAACiB,IAAV,CAAe,UAAUC,IAAV,EAAgB;QACpC,OAAOnB,YAAY,CAACf,KAAD,EAAQkC,IAAR,EAAcjB,UAAd,CAAnB;MACD,CAFM,CAAP;IAGD,CAJD;EAKD;;EACD,OAAOD,SAAS,GAAG,YAAY;IAC7B,OAAO,IAAP;EACD,CAFe,GAEZ,YAAY;IACd,OAAO,KAAP;EACD,CAJD;AAKD;;AACD,IAAImB,aAAa,GAAG,CAAC,SAAD,CAApB;;AACA,SAASC,WAAT,CAAqBpB,SAArB,EAAgCqB,YAAhC,EAA8CC,MAA9C,EAAsD;EACpD,IAAInB,EAAE,GAAGmB,MAAM,IAAI,EAAnB;EAAA,IACEC,EAAE,GAAGpB,EAAE,CAACqB,MADV;EAAA,IAEEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgBJ,aAAhB,GAAgCI,EAF3C;EAAA,IAGEE,MAAM,GAAGtB,EAAE,CAACsB,MAHd;EAAA,IAIEC,EAAE,GAAGvB,EAAE,CAACF,UAJV;EAAA,IAKEA,UAAU,GAAGyB,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EALvC;;EAMA,IAAIC,eAAe,GAAGvI,SAAS,CAACiI,YAAD,CAA/B;EACA,IAAIO,YAAY,GAAGxI,SAAS,CAAC4G,SAAD,CAA5B;EACAvG,8BAA8B,CAAC,YAAY;IACzC,IAAIoI,GAAJ,EAAS1B,EAAT;;IACA,IAAIoB,EAAJ;;IACA,IAAIO,EAAE,GAAGtI,gBAAgB,CAACiI,MAAD,EAASM,MAAT,CAAzB;;IACA,IAAI,CAACD,EAAL,EAAS;MACP;IACD;;IACD,IAAIE,eAAe,GAAG,SAASA,eAAT,CAAyBhD,KAAzB,EAAgC;MACpD,IAAImB,EAAJ;;MACA,IAAI8B,QAAQ,GAAGnB,eAAe,CAACc,YAAY,CAACM,OAAd,EAAuBjC,UAAvB,CAA9B;;MACA,IAAIgC,QAAQ,CAACjD,KAAD,CAAZ,EAAqB;QACnB,OAAO,CAACmB,EAAE,GAAGwB,eAAe,CAACO,OAAtB,MAAmC,IAAnC,IAA2C/B,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAACrH,IAAH,CAAQ6I,eAAR,EAAyB3C,KAAzB,CAA3E;MACD;IACF,CAND;;IAOA,IAAI;MACF,KAAK,IAAImD,QAAQ,GAAG5J,QAAQ,CAACiJ,MAAD,CAAvB,EAAiCY,UAAU,GAAGD,QAAQ,CAACnJ,IAAT,EAAnD,EAAoE,CAACoJ,UAAU,CAAClJ,IAAhF,EAAsFkJ,UAAU,GAAGD,QAAQ,CAACnJ,IAAT,EAAnG,EAAoH;QAClH,IAAIqJ,SAAS,GAAGD,UAAU,CAACnJ,KAA3B;QACA,CAACsI,EAAE,GAAGO,EAAE,KAAK,IAAP,IAAeA,EAAE,KAAK,KAAK,CAA3B,GAA+B,KAAK,CAApC,GAAwCA,EAAE,CAACQ,gBAAjD,MAAuE,IAAvE,IAA+Ef,EAAE,KAAK,KAAK,CAA3F,GAA+F,KAAK,CAApG,GAAwGA,EAAE,CAACzI,IAAH,CAAQgJ,EAAR,EAAYO,SAAZ,EAAuBL,eAAvB,CAAxG;MACD;IACF,CALD,CAKE,OAAOO,KAAP,EAAc;MACdV,GAAG,GAAG;QACJhB,KAAK,EAAE0B;MADH,CAAN;IAGD,CATD,SASU;MACR,IAAI;QACF,IAAIH,UAAU,IAAI,CAACA,UAAU,CAAClJ,IAA1B,KAAmCiH,EAAE,GAAGgC,QAAQ,CAAC,QAAD,CAAhD,CAAJ,EAAiEhC,EAAE,CAACrH,IAAH,CAAQqJ,QAAR;MAClE,CAFD,SAEU;QACR,IAAIN,GAAJ,EAAS,MAAMA,GAAG,CAAChB,KAAV;MACV;IACF;;IACD,OAAO,YAAY;MACjB,IAAI2B,GAAJ,EAASrC,EAAT;;MACA,IAAIoB,EAAJ;;MACA,IAAI;QACF,KAAK,IAAIkB,QAAQ,GAAGlK,QAAQ,CAACiJ,MAAD,CAAvB,EAAiCkB,UAAU,GAAGD,QAAQ,CAACzJ,IAAT,EAAnD,EAAoE,CAAC0J,UAAU,CAACxJ,IAAhF,EAAsFwJ,UAAU,GAAGD,QAAQ,CAACzJ,IAAT,EAAnG,EAAoH;UAClH,IAAIqJ,SAAS,GAAGK,UAAU,CAACzJ,KAA3B;UACA,CAACsI,EAAE,GAAGO,EAAE,KAAK,IAAP,IAAeA,EAAE,KAAK,KAAK,CAA3B,GAA+B,KAAK,CAApC,GAAwCA,EAAE,CAACa,mBAAjD,MAA0E,IAA1E,IAAkFpB,EAAE,KAAK,KAAK,CAA9F,GAAkG,KAAK,CAAvG,GAA2GA,EAAE,CAACzI,IAAH,CAAQgJ,EAAR,EAAYO,SAAZ,EAAuBL,eAAvB,CAA3G;QACD;MACF,CALD,CAKE,OAAOY,KAAP,EAAc;QACdJ,GAAG,GAAG;UACJ3B,KAAK,EAAE+B;QADH,CAAN;MAGD,CATD,SASU;QACR,IAAI;UACF,IAAIF,UAAU,IAAI,CAACA,UAAU,CAACxJ,IAA1B,KAAmCiH,EAAE,GAAGsC,QAAQ,CAAC,QAAD,CAAhD,CAAJ,EAAiEtC,EAAE,CAACrH,IAAH,CAAQ2J,QAAR;QAClE,CAFD,SAEU;UACR,IAAID,GAAJ,EAAS,MAAMA,GAAG,CAAC3B,KAAV;QACV;MACF;IACF,CAnBD;EAoBD,CAlD6B,EAkD3B,CAACW,MAAD,CAlD2B,EAkDjBC,MAlDiB,CAA9B;AAmDD;;AACD,eAAeL,WAAf"}, "metadata": {}, "sourceType": "module"}