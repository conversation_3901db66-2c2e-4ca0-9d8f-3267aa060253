{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\n\nfunction useMap(initialValue) {\n  var getInitValue = function getInitValue() {\n    return initialValue === undefined ? new Map() : new Map(initialValue);\n  };\n\n  var _a = __read(useState(function () {\n    return getInitValue();\n  }), 2),\n      map = _a[0],\n      setMap = _a[1];\n\n  var set = function set(key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n\n  var setAll = function setAll(newMap) {\n    setMap(new Map(newMap));\n  };\n\n  var remove = function remove(key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp[\"delete\"](key);\n      return temp;\n    });\n  };\n\n  var reset = function reset() {\n    return setMap(getInitValue());\n  };\n\n  var get = function get(key) {\n    return map.get(key);\n  };\n\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\n\nexport default useMap;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useMemoizedFn", "useMap", "initialValue", "getInitValue", "undefined", "Map", "_a", "map", "setMap", "set", "key", "entry", "prev", "temp", "setAll", "newMap", "remove", "reset", "get"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useMap/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useMap(initialValue) {\n  var getInitValue = function getInitValue() {\n    return initialValue === undefined ? new Map() : new Map(initialValue);\n  };\n  var _a = __read(useState(function () {\n      return getInitValue();\n    }), 2),\n    map = _a[0],\n    setMap = _a[1];\n  var set = function set(key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n  var setAll = function setAll(newMap) {\n    setMap(new Map(newMap));\n  };\n  var remove = function remove(key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp[\"delete\"](key);\n      return temp;\n    });\n  };\n  var reset = function reset() {\n    return setMap(getInitValue());\n  };\n  var get = function get(key) {\n    return map.get(key);\n  };\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\nexport default useMap;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;;AACA,SAASC,MAAT,CAAgBC,YAAhB,EAA8B;EAC5B,IAAIC,YAAY,GAAG,SAASA,YAAT,GAAwB;IACzC,OAAOD,YAAY,KAAKE,SAAjB,GAA6B,IAAIC,GAAJ,EAA7B,GAAyC,IAAIA,GAAJ,CAAQH,YAAR,CAAhD;EACD,CAFD;;EAGA,IAAII,EAAE,GAAGvB,MAAM,CAACgB,QAAQ,CAAC,YAAY;IACjC,OAAOI,YAAY,EAAnB;EACD,CAFqB,CAAT,EAET,CAFS,CAAf;EAAA,IAGEI,GAAG,GAAGD,EAAE,CAAC,CAAD,CAHV;EAAA,IAIEE,MAAM,GAAGF,EAAE,CAAC,CAAD,CAJb;;EAKA,IAAIG,GAAG,GAAG,SAASA,GAAT,CAAaC,GAAb,EAAkBC,KAAlB,EAAyB;IACjCH,MAAM,CAAC,UAAUI,IAAV,EAAgB;MACrB,IAAIC,IAAI,GAAG,IAAIR,GAAJ,CAAQO,IAAR,CAAX;MACAC,IAAI,CAACJ,GAAL,CAASC,GAAT,EAAcC,KAAd;MACA,OAAOE,IAAP;IACD,CAJK,CAAN;EAKD,CAND;;EAOA,IAAIC,MAAM,GAAG,SAASA,MAAT,CAAgBC,MAAhB,EAAwB;IACnCP,MAAM,CAAC,IAAIH,GAAJ,CAAQU,MAAR,CAAD,CAAN;EACD,CAFD;;EAGA,IAAIC,MAAM,GAAG,SAASA,MAAT,CAAgBN,GAAhB,EAAqB;IAChCF,MAAM,CAAC,UAAUI,IAAV,EAAgB;MACrB,IAAIC,IAAI,GAAG,IAAIR,GAAJ,CAAQO,IAAR,CAAX;MACAC,IAAI,CAAC,QAAD,CAAJ,CAAeH,GAAf;MACA,OAAOG,IAAP;IACD,CAJK,CAAN;EAKD,CAND;;EAOA,IAAII,KAAK,GAAG,SAASA,KAAT,GAAiB;IAC3B,OAAOT,MAAM,CAACL,YAAY,EAAb,CAAb;EACD,CAFD;;EAGA,IAAIe,GAAG,GAAG,SAASA,GAAT,CAAaR,GAAb,EAAkB;IAC1B,OAAOH,GAAG,CAACW,GAAJ,CAAQR,GAAR,CAAP;EACD,CAFD;;EAGA,OAAO,CAACH,GAAD,EAAM;IACXE,GAAG,EAAET,aAAa,CAACS,GAAD,CADP;IAEXK,MAAM,EAAEd,aAAa,CAACc,MAAD,CAFV;IAGXE,MAAM,EAAEhB,aAAa,CAACgB,MAAD,CAHV;IAIXC,KAAK,EAAEjB,aAAa,CAACiB,KAAD,CAJT;IAKXC,GAAG,EAAElB,aAAa,CAACkB,GAAD;EALP,CAAN,CAAP;AAOD;;AACD,eAAejB,MAAf"}, "metadata": {}, "sourceType": "module"}