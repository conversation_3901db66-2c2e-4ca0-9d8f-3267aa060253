{"ast": null, "code": "'use strict'; // The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\n\nvar inherits = require('inherits'),\n    SenderReceiver = require('./lib/sender-receiver'),\n    JsonpReceiver = require('./receiver/jsonp'),\n    jsonpSender = require('./sender/jsonp');\n\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\n\ninherits(JsonPTransport, SenderReceiver);\n\nJsonPTransport.enabled = function () {\n  return !!global.document;\n};\n\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\nmodule.exports = JsonPTransport;", "map": {"version": 3, "names": ["inherits", "require", "SenderReceiver", "JsonpReceiver", "jsonpSender", "JsonPTransport", "transUrl", "enabled", "Error", "call", "global", "document", "transportName", "roundTrips", "needBody", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/jsonp-polling.js"], "sourcesContent": ["'use strict';\n\n// The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\n\nvar inherits = require('inherits')\n  , SenderReceiver = require('./lib/sender-receiver')\n  , JsonpReceiver = require('./receiver/jsonp')\n  , jsonpSender = require('./sender/jsonp')\n  ;\n\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\n\ninherits(JsonPTransport, SenderReceiver);\n\nJsonPTransport.enabled = function() {\n  return !!global.document;\n};\n\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\n\nmodule.exports = JsonPTransport;\n"], "mappings": "AAAA,a,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,cAAc,GAAGD,OAAO,CAAC,uBAAD,CAD5B;AAAA,IAEIE,aAAa,GAAGF,OAAO,CAAC,kBAAD,CAF3B;AAAA,IAGIG,WAAW,GAAGH,OAAO,CAAC,gBAAD,CAHzB;;AAMA,SAASI,cAAT,CAAwBC,QAAxB,EAAkC;EAChC,IAAI,CAACD,cAAc,CAACE,OAAf,EAAL,EAA+B;IAC7B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDN,cAAc,CAACO,IAAf,CAAoB,IAApB,EAA0BH,QAA1B,EAAoC,QAApC,EAA8CF,WAA9C,EAA2DD,aAA3D;AACD;;AAEDH,QAAQ,CAACK,cAAD,EAAiBH,cAAjB,CAAR;;AAEAG,cAAc,CAACE,OAAf,GAAyB,YAAW;EAClC,OAAO,CAAC,CAACG,MAAM,CAACC,QAAhB;AACD,CAFD;;AAIAN,cAAc,CAACO,aAAf,GAA+B,eAA/B;AACAP,cAAc,CAACQ,UAAf,GAA4B,CAA5B;AACAR,cAAc,CAACS,QAAf,GAA0B,IAA1B;AAEAC,MAAM,CAACC,OAAP,GAAiBX,cAAjB"}, "metadata": {}, "sourceType": "script"}