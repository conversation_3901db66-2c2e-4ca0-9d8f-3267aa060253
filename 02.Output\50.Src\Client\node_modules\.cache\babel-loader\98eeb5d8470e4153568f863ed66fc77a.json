{"ast": null, "code": "var hashClear = require('./_hashClear'),\n    hashDelete = require('./_hashDelete'),\n    hashGet = require('./_hashGet'),\n    hashHas = require('./_hashHas'),\n    hashSet = require('./_hashSet');\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\n\n\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n  this.clear();\n\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n} // Add methods to `Hash`.\n\n\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\nmodule.exports = Hash;", "map": {"version": 3, "names": ["hashClear", "require", "hashDelete", "hashGet", "hashHas", "hashSet", "Hash", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_Hash.js"], "sourcesContent": ["var hashClear = require('./_hashClear'),\n    hashDelete = require('./_hashDelete'),\n    hashGet = require('./_hashGet'),\n    hashHas = require('./_hashHas'),\n    hashSet = require('./_hashSet');\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\nmodule.exports = Hash;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,UAAU,GAAGD,OAAO,CAAC,eAAD,CADxB;AAAA,IAEIE,OAAO,GAAGF,OAAO,CAAC,YAAD,CAFrB;AAAA,IAGIG,OAAO,GAAGH,OAAO,CAAC,YAAD,CAHrB;AAAA,IAIII,OAAO,GAAGJ,OAAO,CAAC,YAAD,CAJrB;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,IAAT,CAAcC,OAAd,EAAuB;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGF,OAAO,IAAI,IAAX,GAAkB,CAAlB,GAAsBA,OAAO,CAACE,MAD3C;EAGA,KAAKC,KAAL;;EACA,OAAO,EAAEF,KAAF,GAAUC,MAAjB,EAAyB;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAD,CAAnB;IACA,KAAKI,GAAL,CAASD,KAAK,CAAC,CAAD,CAAd,EAAmBA,KAAK,CAAC,CAAD,CAAxB;EACD;AACF,C,CAED;;;AACAL,IAAI,CAACO,SAAL,CAAeH,KAAf,GAAuBV,SAAvB;AACAM,IAAI,CAACO,SAAL,CAAe,QAAf,IAA2BX,UAA3B;AACAI,IAAI,CAACO,SAAL,CAAeC,GAAf,GAAqBX,OAArB;AACAG,IAAI,CAACO,SAAL,CAAeE,GAAf,GAAqBX,OAArB;AACAE,IAAI,CAACO,SAAL,CAAeD,GAAf,GAAqBP,OAArB;AAEAW,MAAM,CAACC,OAAP,GAAiBX,IAAjB"}, "metadata": {}, "sourceType": "script"}