{"ast": null, "code": "'use strict';\n\nvar has = Object.prototype.hasOwnProperty,\n    undef;\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\n\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\n\n\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\n\n\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g,\n      result = {},\n      part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1]),\n        value = decode(part[2]); //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\n\n\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n  var pairs = [],\n      value,\n      key; //\n  // Optionally prefix with a '?' if needed\n  //\n\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key]; //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encode(key);\n      value = encode(value); //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n\n      if (key === null || value === null) continue;\n      pairs.push(key + '=' + value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n} //\n// Expose the module.\n//\n\n\nexports.stringify = querystringify;\nexports.parse = querystring;", "map": {"version": 3, "names": ["has", "Object", "prototype", "hasOwnProperty", "undef", "decode", "input", "decodeURIComponent", "replace", "e", "encode", "encodeURIComponent", "querystring", "query", "parser", "result", "part", "exec", "key", "value", "querystringify", "obj", "prefix", "pairs", "call", "isNaN", "push", "length", "join", "exports", "stringify", "parse"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/querystringify/index.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g\n    , result = {}\n    , part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1])\n      , value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n\n  var pairs = []\n    , value\n    , key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encode(key);\n      value = encode(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key +'='+ value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;\n"], "mappings": "AAAA;;AAEA,IAAIA,GAAG,GAAGC,MAAM,CAACC,SAAP,CAAiBC,cAA3B;AAAA,IACIC,KADJ;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,MAAT,CAAgBC,KAAhB,EAAuB;EACrB,IAAI;IACF,OAAOC,kBAAkB,CAACD,KAAK,CAACE,OAAN,CAAc,KAAd,EAAqB,GAArB,CAAD,CAAzB;EACD,CAFD,CAEE,OAAOC,CAAP,EAAU;IACV,OAAO,IAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,MAAT,CAAgBJ,KAAhB,EAAuB;EACrB,IAAI;IACF,OAAOK,kBAAkB,CAACL,KAAD,CAAzB;EACD,CAFD,CAEE,OAAOG,CAAP,EAAU;IACV,OAAO,IAAP;EACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,WAAT,CAAqBC,KAArB,EAA4B;EAC1B,IAAIC,MAAM,GAAG,sBAAb;EAAA,IACIC,MAAM,GAAG,EADb;EAAA,IAEIC,IAFJ;;EAIA,OAAOA,IAAI,GAAGF,MAAM,CAACG,IAAP,CAAYJ,KAAZ,CAAd,EAAkC;IAChC,IAAIK,GAAG,GAAGb,MAAM,CAACW,IAAI,CAAC,CAAD,CAAL,CAAhB;IAAA,IACIG,KAAK,GAAGd,MAAM,CAACW,IAAI,CAAC,CAAD,CAAL,CADlB,CADgC,CAIhC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIE,GAAG,KAAK,IAAR,IAAgBC,KAAK,KAAK,IAA1B,IAAkCD,GAAG,IAAIH,MAA7C,EAAqD;IACrDA,MAAM,CAACG,GAAD,CAAN,GAAcC,KAAd;EACD;;EAED,OAAOJ,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,cAAT,CAAwBC,GAAxB,EAA6BC,MAA7B,EAAqC;EACnCA,MAAM,GAAGA,MAAM,IAAI,EAAnB;EAEA,IAAIC,KAAK,GAAG,EAAZ;EAAA,IACIJ,KADJ;EAAA,IAEID,GAFJ,CAHmC,CAOnC;EACA;EACA;;EACA,IAAI,aAAa,OAAOI,MAAxB,EAAgCA,MAAM,GAAG,GAAT;;EAEhC,KAAKJ,GAAL,IAAYG,GAAZ,EAAiB;IACf,IAAIrB,GAAG,CAACwB,IAAJ,CAASH,GAAT,EAAcH,GAAd,CAAJ,EAAwB;MACtBC,KAAK,GAAGE,GAAG,CAACH,GAAD,CAAX,CADsB,CAGtB;MACA;MACA;MACA;;MACA,IAAI,CAACC,KAAD,KAAWA,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAKf,KAA5B,IAAqCqB,KAAK,CAACN,KAAD,CAArD,CAAJ,EAAmE;QACjEA,KAAK,GAAG,EAAR;MACD;;MAEDD,GAAG,GAAGR,MAAM,CAACQ,GAAD,CAAZ;MACAC,KAAK,GAAGT,MAAM,CAACS,KAAD,CAAd,CAZsB,CActB;MACA;MACA;MACA;;MACA,IAAID,GAAG,KAAK,IAAR,IAAgBC,KAAK,KAAK,IAA9B,EAAoC;MACpCI,KAAK,CAACG,IAAN,CAAWR,GAAG,GAAE,GAAL,GAAUC,KAArB;IACD;EACF;;EAED,OAAOI,KAAK,CAACI,MAAN,GAAeL,MAAM,GAAGC,KAAK,CAACK,IAAN,CAAW,GAAX,CAAxB,GAA0C,EAAjD;AACD,C,CAED;AACA;AACA;;;AACAC,OAAO,CAACC,SAAR,GAAoBV,cAApB;AACAS,OAAO,CAACE,KAAR,GAAgBnB,WAAhB"}, "metadata": {}, "sourceType": "script"}