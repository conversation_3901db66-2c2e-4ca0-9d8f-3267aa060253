{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\nimport useUpdateEffect from '../useUpdateEffect';\n\nfunction useThrottleEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n      flag = _a[0],\n      setFlag = _a[1];\n\n  var run = useThrottleFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\n\nexport default useThrottleEffect;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useState", "useThrottleFn", "useUpdateEffect", "useThrottleEffect", "effect", "deps", "options", "_a", "flag", "setFlag", "run"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useThrottleEffect/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useThrottleEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useThrottleFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useThrottleEffect;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,QAApB,QAAoC,OAApC;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,eAAP,MAA4B,oBAA5B;;AACA,SAASC,iBAAT,CAA2BC,MAA3B,EAAmCC,IAAnC,EAAyCC,OAAzC,EAAkD;EAChD,IAAIC,EAAE,GAAGxB,MAAM,CAACiB,QAAQ,CAAC,EAAD,CAAT,EAAe,CAAf,CAAf;EAAA,IACEQ,IAAI,GAAGD,EAAE,CAAC,CAAD,CADX;EAAA,IAEEE,OAAO,GAAGF,EAAE,CAAC,CAAD,CAFd;;EAGA,IAAIG,GAAG,GAAGT,aAAa,CAAC,YAAY;IAClCQ,OAAO,CAAC,EAAD,CAAP;EACD,CAFsB,EAEpBH,OAFoB,CAAb,CAEEI,GAFZ;EAGAX,SAAS,CAAC,YAAY;IACpB,OAAOW,GAAG,EAAV;EACD,CAFQ,EAENL,IAFM,CAAT;EAGAH,eAAe,CAACE,MAAD,EAAS,CAACI,IAAD,CAAT,CAAf;AACD;;AACD,eAAeL,iBAAf"}, "metadata": {}, "sourceType": "module"}