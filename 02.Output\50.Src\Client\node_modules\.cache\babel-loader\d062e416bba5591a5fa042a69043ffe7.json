{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\n\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n\n  var _a = __read(useState(initialState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) return;\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\n\nexport default useSafeState;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useCallback", "useState", "useUnmountedRef", "useSafeState", "initialState", "unmountedRef", "_a", "state", "setState", "setCurrentState", "currentState", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useSafeState/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) return;\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\nexport default useSafeState;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,WAAT,EAAsBC,QAAtB,QAAsC,OAAtC;AACA,OAAOC,eAAP,MAA4B,oBAA5B;;AACA,SAASC,YAAT,CAAsBC,YAAtB,EAAoC;EAClC,IAAIC,YAAY,GAAGH,eAAe,EAAlC;;EACA,IAAII,EAAE,GAAGtB,MAAM,CAACiB,QAAQ,CAACG,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEG,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,eAAe,GAAGT,WAAW,CAAC,UAAUU,YAAV,EAAwB;IACxD;IACA,IAAIL,YAAY,CAACM,OAAjB,EAA0B;IAC1BH,QAAQ,CAACE,YAAD,CAAR;EACD,CAJgC,EAI9B,EAJ8B,CAAjC;EAKA,OAAO,CAACH,KAAD,EAAQE,eAAR,CAAP;AACD;;AACD,eAAeN,YAAf"}, "metadata": {}, "sourceType": "module"}