{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import TextScroll from'./elements/TextScroll';import Cell from'./elements/Cell';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource,formateDatetimeText}from'../utils/Util.js';/**\r\n * 予定コンテンツ<br>\r\n * propsは、「3.15予定コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Schedule\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var Schedule=function Schedule(props){var _props$schedule_ts;var MAX_ROW=6;return/*#__PURE__*/_jsxs(\"div\",{className:\"text-4xl\",children:[/*#__PURE__*/_jsx(Title,{title:'予定'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-row-6 grid-cols-[repeat(9,4.7rem)_minmax(0.25rem,1fr)_repeat(10,7.5rem)] leading-[1] gap-y-[3.5rem] mt-[2rem] items-end\",children:(_props$schedule_ts=props.schedule_ts)===null||_props$schedule_ts===void 0?void 0:_props$schedule_ts.map(function(item,index){if(index>=MAX_ROW){return undefined;}return/*#__PURE__*/_jsx(ScheduleRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})})]});};var ScheduleRow=function ScheduleRow(props){if(!props.schedule_ts[props.index]){return;}var cell1=getCellFace(props.schedule_ts[props.index],'col-span-9 text-5xl self-end');cell1.text=formateDatetimeText(cell1.text,'3rem');var cell2={className:'col-span-10 col-start-11'};var cell2PropFrame;if(props.schedule_content&&props.schedule_content[props.index]){cell2=getCellFace(props.schedule_content[props.index],'col-span-10 col-start-11 text-7xl self-end');}cell2PropFrame={className:cell2.className,text_color:cell2.text_color};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1)),/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},cell2PropFrame),{},{children:/*#__PURE__*/_jsx(TextScroll,{content:cell2.text,background_color:cell2.background_color})}))]});};export default Schedule;", "map": {"version": 3, "names": ["React", "Title", "TextScroll", "Cell", "CellBox", "getCellFace", "isValidSource", "formateDatetimeText", "Schedule", "props", "MAX_ROW", "schedule_ts", "map", "item", "index", "undefined", "ScheduleRow", "cell1", "text", "cell2", "className", "cell2PropFrame", "schedule_content", "text_color", "background_color"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Schedule.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport TextScroll from './elements/TextScroll';\r\nimport Cell from './elements/Cell';\r\nimport CellBox from './elements/CellBox';\r\nimport {\r\n  getCellFace,\r\n  isValidSource,\r\n  formateDatetimeText,\r\n} from '../utils/Util.js';\r\n\r\n/**\r\n * 予定コンテンツ<br>\r\n * propsは、「3.15予定コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Schedule\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Schedule = (props) => {\r\n  const MAX_ROW = 6;\r\n  return (\r\n    <div className=\"text-4xl\">\r\n      <Title title={'予定'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-row-6 grid-cols-[repeat(9,4.7rem)_minmax(0.25rem,1fr)_repeat(10,7.5rem)] leading-[1] gap-y-[3.5rem] mt-[2rem] items-end\">\r\n          {props.schedule_ts?.map((item, index) => {\r\n            if (index >= MAX_ROW) {\r\n              return undefined;\r\n            }\r\n\r\n            return <ScheduleRow key={index} {...props} index={index} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ScheduleRow = (props) => {\r\n  if (!props.schedule_ts[props.index]) {\r\n    return;\r\n  }\r\n  let cell1 = getCellFace(\r\n    props.schedule_ts[props.index],\r\n    'col-span-9 text-5xl self-end'\r\n  );\r\n  cell1.text = formateDatetimeText(cell1.text, '3rem');\r\n\r\n  let cell2 = { className: 'col-span-10 col-start-11' };\r\n  let cell2PropFrame;\r\n  if (props.schedule_content && props.schedule_content[props.index]) {\r\n    cell2 = getCellFace(\r\n      props.schedule_content[props.index],\r\n      'col-span-10 col-start-11 text-7xl self-end'\r\n    );\r\n  }\r\n  cell2PropFrame = {\r\n    className: cell2.className,\r\n    text_color: cell2.text_color,\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Cell {...cell1} />\r\n      <CellBox {...cell2PropFrame}>\r\n        <TextScroll\r\n          content={cell2.text}\r\n          background_color={cell2.background_color}\r\n        />\r\n      </CellBox>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Schedule;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OACEC,WADF,CAEEC,aAFF,CAGEC,mBAHF,KAIO,kBAJP,CAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,SAAQ,CAAG,QAAXA,SAAW,CAACC,KAAD,CAAW,wBAC1B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,aAAK,SAAS,CAAC,UAAf,wBACE,KAAC,KAAD,EAAO,KAAK,CAAE,IAAd,EADF,CAEGJ,aAAa,CAACG,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,sKAAf,8BACGA,KAAK,CAACE,WADT,6CACG,mBAAmBC,GAAnB,CAAuB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACvC,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,CACpB,MAAOK,UAAP,CACD,CAED,mBAAO,KAAC,WAAD,gCAA6BN,KAA7B,MAAoC,KAAK,CAAEK,KAA3C,GAAkBA,KAAlB,CAAP,CACD,CANA,CADH,EAHJ,GADF,CAgBD,CAlBD,CAoBA,GAAME,YAAW,CAAG,QAAdA,YAAc,CAACP,KAAD,CAAW,CAC7B,GAAI,CAACA,KAAK,CAACE,WAAN,CAAkBF,KAAK,CAACK,KAAxB,CAAL,CAAqC,CACnC,OACD,CACD,GAAIG,MAAK,CAAGZ,WAAW,CACrBI,KAAK,CAACE,WAAN,CAAkBF,KAAK,CAACK,KAAxB,CADqB,CAErB,8BAFqB,CAAvB,CAIAG,KAAK,CAACC,IAAN,CAAaX,mBAAmB,CAACU,KAAK,CAACC,IAAP,CAAa,MAAb,CAAhC,CAEA,GAAIC,MAAK,CAAG,CAAEC,SAAS,CAAE,0BAAb,CAAZ,CACA,GAAIC,eAAJ,CACA,GAAIZ,KAAK,CAACa,gBAAN,EAA0Bb,KAAK,CAACa,gBAAN,CAAuBb,KAAK,CAACK,KAA7B,CAA9B,CAAmE,CACjEK,KAAK,CAAGd,WAAW,CACjBI,KAAK,CAACa,gBAAN,CAAuBb,KAAK,CAACK,KAA7B,CADiB,CAEjB,4CAFiB,CAAnB,CAID,CACDO,cAAc,CAAG,CACfD,SAAS,CAAED,KAAK,CAACC,SADF,CAEfG,UAAU,CAAEJ,KAAK,CAACI,UAFH,CAAjB,CAKA,mBACE,wCACE,KAAC,IAAD,kBAAUN,KAAV,EADF,cAEE,KAAC,OAAD,gCAAaI,cAAb,4BACE,KAAC,UAAD,EACE,OAAO,CAAEF,KAAK,CAACD,IADjB,CAEE,gBAAgB,CAAEC,KAAK,CAACK,gBAF1B,EADF,GAFF,GADF,CAWD,CAlCD,CAoCA,cAAehB,SAAf"}, "metadata": {}, "sourceType": "module"}