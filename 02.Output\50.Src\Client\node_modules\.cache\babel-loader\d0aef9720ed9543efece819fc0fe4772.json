{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits'),\n    eventUtils = require('../../utils/event'),\n    browser = require('../../utils/browser'),\n    urlUtils = require('../../utils/url');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n} // References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self._start(method, url, payload);\n  }, 0);\n}\n\ninherits(XDRObject, EventEmitter);\n\nXDRObject.prototype._start = function (method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest(); // IE caches even POSTs\n\n  url = urlUtils.addQuery(url, 't=' + +new Date());\n\n  xdr.onerror = function () {\n    debug('onerror');\n\n    self._error();\n  };\n\n  xdr.ontimeout = function () {\n    debug('ontimeout');\n\n    self._error();\n  };\n\n  xdr.onprogress = function () {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n\n  xdr.onload = function () {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n\n    self._cleanup(false);\n  };\n\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function () {\n    self._cleanup(true);\n  });\n\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\n\nXDRObject.prototype._error = function () {\n  this.emit('finish', 0, '');\n\n  this._cleanup(false);\n};\n\nXDRObject.prototype._cleanup = function (abort) {\n  debug('cleanup', abort);\n\n  if (!this.xdr) {\n    return;\n  }\n\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {// intentionally empty\n    }\n  }\n\n  this.unloadRef = this.xdr = null;\n};\n\nXDRObject.prototype.close = function () {\n  debug('close');\n\n  this._cleanup(true);\n}; // IE 8/9 if the request target uses the same scheme - #79\n\n\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\nmodule.exports = XDRObject;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "eventUtils", "browser", "urlUtils", "debug", "process", "env", "NODE_ENV", "XDRObject", "method", "url", "payload", "self", "call", "setTimeout", "_start", "prototype", "xdr", "global", "XDomainRequest", "<PERSON><PERSON><PERSON><PERSON>", "Date", "onerror", "_error", "ontimeout", "onprogress", "responseText", "emit", "onload", "_cleanup", "unloadRef", "unloadAdd", "open", "timeout", "send", "x", "abort", "removeAllListeners", "unloadDel", "close", "enabled", "hasDomain", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/sender/xdr.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , eventUtils = require('../../utils/event')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n}\n\n// References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self._start(method, url, payload);\n  }, 0);\n}\n\ninherits(XDRObject, EventEmitter);\n\nXDRObject.prototype._start = function(method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest();\n  // IE caches even POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  xdr.onerror = function() {\n    debug('onerror');\n    self._error();\n  };\n  xdr.ontimeout = function() {\n    debug('ontimeout');\n    self._error();\n  };\n  xdr.onprogress = function() {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n  xdr.onload = function() {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n    self._cleanup(false);\n  };\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function() {\n    self._cleanup(true);\n  });\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\n\nXDRObject.prototype._error = function() {\n  this.emit('finish', 0, '');\n  this._cleanup(false);\n};\n\nXDRObject.prototype._cleanup = function(abort) {\n  debug('cleanup', abort);\n  if (!this.xdr) {\n    return;\n  }\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xdr = null;\n};\n\nXDRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\n// IE 8/9 if the request target uses the same scheme - #79\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\n\nmodule.exports = XDRObject;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIE,UAAU,GAAGF,OAAO,CAAC,mBAAD,CAFxB;AAAA,IAGIG,OAAO,GAAGH,OAAO,CAAC,qBAAD,CAHrB;AAAA,IAIII,QAAQ,GAAGJ,OAAO,CAAC,iBAAD,CAJtB;;AAOA,IAAIK,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAD,CAAP,CAAiB,0BAAjB,CAAR;AACD,C,CAED;AACA;AACA;;;AAEA,SAASS,SAAT,CAAmBC,MAAnB,EAA2BC,GAA3B,EAAgCC,OAAhC,EAAyC;EACvCP,KAAK,CAACK,MAAD,EAASC,GAAT,CAAL;EACA,IAAIE,IAAI,GAAG,IAAX;EACAd,YAAY,CAACe,IAAb,CAAkB,IAAlB;EAEAC,UAAU,CAAC,YAAW;IACpBF,IAAI,CAACG,MAAL,CAAYN,MAAZ,EAAoBC,GAApB,EAAyBC,OAAzB;EACD,CAFS,EAEP,CAFO,CAAV;AAGD;;AAEDX,QAAQ,CAACQ,SAAD,EAAYV,YAAZ,CAAR;;AAEAU,SAAS,CAACQ,SAAV,CAAoBD,MAApB,GAA6B,UAASN,MAAT,EAAiBC,GAAjB,EAAsBC,OAAtB,EAA+B;EAC1DP,KAAK,CAAC,QAAD,CAAL;EACA,IAAIQ,IAAI,GAAG,IAAX;EACA,IAAIK,GAAG,GAAG,IAAIC,MAAM,CAACC,cAAX,EAAV,CAH0D,CAI1D;;EACAT,GAAG,GAAGP,QAAQ,CAACiB,QAAT,CAAkBV,GAAlB,EAAuB,OAAQ,CAAC,IAAIW,IAAJ,EAAhC,CAAN;;EAEAJ,GAAG,CAACK,OAAJ,GAAc,YAAW;IACvBlB,KAAK,CAAC,SAAD,CAAL;;IACAQ,IAAI,CAACW,MAAL;EACD,CAHD;;EAIAN,GAAG,CAACO,SAAJ,GAAgB,YAAW;IACzBpB,KAAK,CAAC,WAAD,CAAL;;IACAQ,IAAI,CAACW,MAAL;EACD,CAHD;;EAIAN,GAAG,CAACQ,UAAJ,GAAiB,YAAW;IAC1BrB,KAAK,CAAC,UAAD,EAAaa,GAAG,CAACS,YAAjB,CAAL;IACAd,IAAI,CAACe,IAAL,CAAU,OAAV,EAAmB,GAAnB,EAAwBV,GAAG,CAACS,YAA5B;EACD,CAHD;;EAIAT,GAAG,CAACW,MAAJ,GAAa,YAAW;IACtBxB,KAAK,CAAC,MAAD,CAAL;IACAQ,IAAI,CAACe,IAAL,CAAU,QAAV,EAAoB,GAApB,EAAyBV,GAAG,CAACS,YAA7B;;IACAd,IAAI,CAACiB,QAAL,CAAc,KAAd;EACD,CAJD;;EAKA,KAAKZ,GAAL,GAAWA,GAAX;EACA,KAAKa,SAAL,GAAiB7B,UAAU,CAAC8B,SAAX,CAAqB,YAAW;IAC/CnB,IAAI,CAACiB,QAAL,CAAc,IAAd;EACD,CAFgB,CAAjB;;EAGA,IAAI;IACF;IACA,KAAKZ,GAAL,CAASe,IAAT,CAAcvB,MAAd,EAAsBC,GAAtB;;IACA,IAAI,KAAKuB,OAAT,EAAkB;MAChB,KAAKhB,GAAL,CAASgB,OAAT,GAAmB,KAAKA,OAAxB;IACD;;IACD,KAAKhB,GAAL,CAASiB,IAAT,CAAcvB,OAAd;EACD,CAPD,CAOE,OAAOwB,CAAP,EAAU;IACV,KAAKZ,MAAL;EACD;AACF,CAtCD;;AAwCAf,SAAS,CAACQ,SAAV,CAAoBO,MAApB,GAA6B,YAAW;EACtC,KAAKI,IAAL,CAAU,QAAV,EAAoB,CAApB,EAAuB,EAAvB;;EACA,KAAKE,QAAL,CAAc,KAAd;AACD,CAHD;;AAKArB,SAAS,CAACQ,SAAV,CAAoBa,QAApB,GAA+B,UAASO,KAAT,EAAgB;EAC7ChC,KAAK,CAAC,SAAD,EAAYgC,KAAZ,CAAL;;EACA,IAAI,CAAC,KAAKnB,GAAV,EAAe;IACb;EACD;;EACD,KAAKoB,kBAAL;EACApC,UAAU,CAACqC,SAAX,CAAqB,KAAKR,SAA1B;EAEA,KAAKb,GAAL,CAASO,SAAT,GAAqB,KAAKP,GAAL,CAASK,OAAT,GAAmB,KAAKL,GAAL,CAASQ,UAAT,GAAsB,KAAKR,GAAL,CAASW,MAAT,GAAkB,IAAhF;;EACA,IAAIQ,KAAJ,EAAW;IACT,IAAI;MACF,KAAKnB,GAAL,CAASmB,KAAT;IACD,CAFD,CAEE,OAAOD,CAAP,EAAU,CACV;IACD;EACF;;EACD,KAAKL,SAAL,GAAiB,KAAKb,GAAL,GAAW,IAA5B;AACD,CAjBD;;AAmBAT,SAAS,CAACQ,SAAV,CAAoBuB,KAApB,GAA4B,YAAW;EACrCnC,KAAK,CAAC,OAAD,CAAL;;EACA,KAAKyB,QAAL,CAAc,IAAd;AACD,CAHD,C,CAKA;;;AACArB,SAAS,CAACgC,OAAV,GAAoB,CAAC,EAAEtB,MAAM,CAACC,cAAP,IAAyBjB,OAAO,CAACuC,SAAR,EAA3B,CAArB;AAEAC,MAAM,CAACC,OAAP,GAAiBnC,SAAjB"}, "metadata": {}, "sourceType": "script"}