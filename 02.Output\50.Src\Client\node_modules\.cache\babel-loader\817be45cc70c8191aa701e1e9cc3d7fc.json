{"ast": null, "code": "import React from'react';/**\r\n * エラー画面\r\n * \r\n * @module Message\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示画面\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var Message=function Message(props){var title=props.title||'エラー発生';return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{role:\"alert\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-500 text-white font-bold rounded-t px-4 py-2 text-2xl\",children:title}),/*#__PURE__*/_jsx(\"div\",{className:\"border border-t-0 border-red-400 rounded-b bg-red-100 px-4 py-3 text-red-700 text-4xl\",children:/*#__PURE__*/_jsx(\"p\",{children:props.msg})})]})});};export default Message;", "map": {"version": 3, "names": ["React", "Message", "props", "title", "msg"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/Message.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\n/**\r\n * エラー画面\r\n * \r\n * @module Message\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示画面\r\n */\r\nconst Message = (props: { msg: string; title: string }) => {\r\n  const title = props.title || 'エラー発生';\r\n\r\n  return (\r\n    <>\r\n      <div role=\"alert\">\r\n        <div className=\"bg-red-500 text-white font-bold rounded-t px-4 py-2 text-2xl\">\r\n          {title}\r\n        </div>\r\n        <div className=\"border border-t-0 border-red-400 rounded-b bg-red-100 px-4 py-3 text-red-700 text-4xl\">\r\n          <p>{props.msg}</p>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Message;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,QAAO,CAAG,QAAVA,QAAU,CAACC,KAAD,CAA2C,CACzD,GAAMC,MAAK,CAAGD,KAAK,CAACC,KAAN,EAAe,OAA7B,CAEA,mBACE,sCACE,aAAK,IAAI,CAAC,OAAV,wBACE,YAAK,SAAS,CAAC,8DAAf,UACGA,KADH,EADF,cAIE,YAAK,SAAS,CAAC,uFAAf,uBACE,mBAAID,KAAK,CAACE,GAAV,EADF,EAJF,GADF,EADF,CAYD,CAfD,CAiBA,cAAeH,QAAf"}, "metadata": {}, "sourceType": "module"}