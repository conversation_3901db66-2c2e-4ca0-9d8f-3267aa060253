{"ast": null, "code": "var ListCache = require('./_ListCache'),\n    stackClear = require('./_stackClear'),\n    stackDelete = require('./_stackDelete'),\n    stackGet = require('./_stackGet'),\n    stackHas = require('./_stackHas'),\n    stackSet = require('./_stackSet');\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\n\n\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n} // Add methods to `Stack`.\n\n\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\nmodule.exports = Stack;", "map": {"version": 3, "names": ["ListCache", "require", "stackClear", "stackDelete", "stackGet", "stackHas", "stackSet", "<PERSON><PERSON>", "entries", "data", "__data__", "size", "prototype", "clear", "get", "has", "set", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_Stack.js"], "sourcesContent": ["var ListCache = require('./_ListCache'),\n    stackClear = require('./_stackClear'),\n    stackDelete = require('./_stackDelete'),\n    stackGet = require('./_stackGet'),\n    stackHas = require('./_stackHas'),\n    stackSet = require('./_stackSet');\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\nmodule.exports = Stack;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,UAAU,GAAGD,OAAO,CAAC,eAAD,CADxB;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,aAAD,CAHtB;AAAA,IAIII,QAAQ,GAAGJ,OAAO,CAAC,aAAD,CAJtB;AAAA,IAKIK,QAAQ,GAAGL,OAAO,CAAC,aAAD,CALtB;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASM,KAAT,CAAeC,OAAf,EAAwB;EACtB,IAAIC,IAAI,GAAG,KAAKC,QAAL,GAAgB,IAAIV,SAAJ,CAAcQ,OAAd,CAA3B;EACA,KAAKG,IAAL,GAAYF,IAAI,CAACE,IAAjB;AACD,C,CAED;;;AACAJ,KAAK,CAACK,SAAN,CAAgBC,KAAhB,GAAwBX,UAAxB;AACAK,KAAK,CAACK,SAAN,CAAgB,QAAhB,IAA4BT,WAA5B;AACAI,KAAK,CAACK,SAAN,CAAgBE,GAAhB,GAAsBV,QAAtB;AACAG,KAAK,CAACK,SAAN,CAAgBG,GAAhB,GAAsBV,QAAtB;AACAE,KAAK,CAACK,SAAN,CAAgBI,GAAhB,GAAsBV,QAAtB;AAEAW,MAAM,CAACC,OAAP,GAAiBX,KAAjB"}, "metadata": {}, "sourceType": "script"}