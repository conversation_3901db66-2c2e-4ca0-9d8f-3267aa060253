{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\n\nfunction useInterval(fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var immediate = options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(null);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n\n    if (immediate) {\n      fnRef.current();\n    }\n\n    timerRef.current = setInterval(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\n\nexport default useInterval;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "useInterval", "fn", "delay", "options", "immediate", "fnRef", "timerRef", "current", "setInterval", "clearInterval", "clear"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useInterval/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nfunction useInterval(fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var immediate = options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(null);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setInterval(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\nexport default useInterval;"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,SAAtB,EAAiCC,MAAjC,QAA+C,OAA/C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,SAASC,WAAT,CAAqBC,EAArB,EAAyBC,KAAzB,EAAgCC,OAAhC,EAAyC;EACvC,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,SAAS,GAAGD,OAAO,CAACC,SAAxB;EACA,IAAIC,KAAK,GAAGP,SAAS,CAACG,EAAD,CAArB;EACA,IAAIK,QAAQ,GAAGT,MAAM,CAAC,IAAD,CAArB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAD,CAAT,IAAoBA,KAAK,GAAG,CAAhC,EAAmC;MACjC;IACD;;IACD,IAAIE,SAAJ,EAAe;MACbC,KAAK,CAACE,OAAN;IACD;;IACDD,QAAQ,CAACC,OAAT,GAAmBC,WAAW,CAAC,YAAY;MACzCH,KAAK,CAACE,OAAN;IACD,CAF6B,EAE3BL,KAF2B,CAA9B;IAGA,OAAO,YAAY;MACjB,IAAII,QAAQ,CAACC,OAAb,EAAsB;QACpBE,aAAa,CAACH,QAAQ,CAACC,OAAV,CAAb;MACD;IACF,CAJD;EAKD,CAfQ,EAeN,CAACL,KAAD,CAfM,CAAT;EAgBA,IAAIQ,KAAK,GAAGf,WAAW,CAAC,YAAY;IAClC,IAAIW,QAAQ,CAACC,OAAb,EAAsB;MACpBE,aAAa,CAACH,QAAQ,CAACC,OAAV,CAAb;IACD;EACF,CAJsB,EAIpB,EAJoB,CAAvB;EAKA,OAAOG,KAAP;AACD;;AACD,eAAeV,WAAf"}, "metadata": {}, "sourceType": "module"}