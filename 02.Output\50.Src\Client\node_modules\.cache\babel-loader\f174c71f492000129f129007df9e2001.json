{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function handler(event) {\n      var targets = Array.isArray(target) ? target : [target];\n\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n\n      onClickAwayRef.current(event);\n    };\n\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "getDocumentOrShadow", "useEffectWithTarget", "useClickAway", "onClickAway", "target", "eventName", "onClickAwayRef", "handler", "event", "targets", "Array", "isArray", "some", "item", "targetElement", "contains", "current", "documentOrShadow", "eventNames", "for<PERSON>ach", "addEventListener", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useClickAway/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function handler(event) {\n      var targets = Array.isArray(target) ? target : [target];\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n      onClickAwayRef.current(event);\n    };\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;AACA,eAAe,SAASC,YAAT,CAAsBC,WAAtB,EAAmCC,MAAnC,EAA2CC,SAA3C,EAAsD;EACnE,IAAIA,SAAS,KAAK,KAAK,CAAvB,EAA0B;IACxBA,SAAS,GAAG,OAAZ;EACD;;EACD,IAAIC,cAAc,GAAGR,SAAS,CAACK,WAAD,CAA9B;EACAF,mBAAmB,CAAC,YAAY;IAC9B,IAAIM,OAAO,GAAG,SAASA,OAAT,CAAiBC,KAAjB,EAAwB;MACpC,IAAIC,OAAO,GAAGC,KAAK,CAACC,OAAN,CAAcP,MAAd,IAAwBA,MAAxB,GAAiC,CAACA,MAAD,CAA/C;;MACA,IAAIK,OAAO,CAACG,IAAR,CAAa,UAAUC,IAAV,EAAgB;QAC/B,IAAIC,aAAa,GAAGf,gBAAgB,CAACc,IAAD,CAApC;QACA,OAAO,CAACC,aAAD,IAAkBA,aAAa,CAACC,QAAd,CAAuBP,KAAK,CAACJ,MAA7B,CAAzB;MACD,CAHG,CAAJ,EAGI;QACF;MACD;;MACDE,cAAc,CAACU,OAAf,CAAuBR,KAAvB;IACD,CATD;;IAUA,IAAIS,gBAAgB,GAAGjB,mBAAmB,CAACI,MAAD,CAA1C;IACA,IAAIc,UAAU,GAAGR,KAAK,CAACC,OAAN,CAAcN,SAAd,IAA2BA,SAA3B,GAAuC,CAACA,SAAD,CAAxD;IACAa,UAAU,CAACC,OAAX,CAAmB,UAAUX,KAAV,EAAiB;MAClC,OAAOS,gBAAgB,CAACG,gBAAjB,CAAkCZ,KAAlC,EAAyCD,OAAzC,CAAP;IACD,CAFD;IAGA,OAAO,YAAY;MACjBW,UAAU,CAACC,OAAX,CAAmB,UAAUX,KAAV,EAAiB;QAClC,OAAOS,gBAAgB,CAACI,mBAAjB,CAAqCb,KAArC,EAA4CD,OAA5C,CAAP;MACD,CAFD;IAGD,CAJD;EAKD,CArBkB,EAqBhBG,KAAK,CAACC,OAAN,CAAcN,SAAd,IAA2BA,SAA3B,GAAuC,CAACA,SAAD,CArBvB,EAqBoCD,MArBpC,CAAnB;AAsBD"}, "metadata": {}, "sourceType": "module"}