{"ast": null, "code": "import isEqual from 'lodash/isEqual';\nimport { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\n\nvar depsEqual = function depsEqual(aDeps, bDeps) {\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n\n  return isEqual(aDeps, bDeps);\n};\n\nvar useDeepCompareEffectWithTarget = function useDeepCompareEffectWithTarget(effect, deps, target) {\n  var ref = useRef();\n  var signalRef = useRef(0);\n\n  if (!depsEqual(deps, ref.current)) {\n    ref.current = deps;\n    signalRef.current += 1;\n  }\n\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\n\nexport default useDeepCompareEffectWithTarget;", "map": {"version": 3, "names": ["isEqual", "useRef", "useEffectWithTarget", "depsEqual", "aDeps", "bDeps", "useDeepCompareEffectWithTarget", "effect", "deps", "target", "ref", "signalRef", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/useDeepCompareWithTarget.js"], "sourcesContent": ["import isEqual from 'lodash/isEqual';\nimport { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\nvar depsEqual = function depsEqual(aDeps, bDeps) {\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n  return isEqual(aDeps, bDeps);\n};\nvar useDeepCompareEffectWithTarget = function useDeepCompareEffectWithTarget(effect, deps, target) {\n  var ref = useRef();\n  var signalRef = useRef(0);\n  if (!depsEqual(deps, ref.current)) {\n    ref.current = deps;\n    signalRef.current += 1;\n  }\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\nexport default useDeepCompareEffectWithTarget;"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,gBAApB;AACA,SAASC,MAAT,QAAuB,OAAvB;AACA,OAAOC,mBAAP,MAAgC,uBAAhC;;AACA,IAAIC,SAAS,GAAG,SAASA,SAAT,CAAmBC,KAAnB,EAA0BC,KAA1B,EAAiC;EAC/C,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,OAAOL,OAAO,CAACI,KAAD,EAAQC,KAAR,CAAd;AACD,CALD;;AAMA,IAAIC,8BAA8B,GAAG,SAASA,8BAAT,CAAwCC,MAAxC,EAAgDC,IAAhD,EAAsDC,MAAtD,EAA8D;EACjG,IAAIC,GAAG,GAAGT,MAAM,EAAhB;EACA,IAAIU,SAAS,GAAGV,MAAM,CAAC,CAAD,CAAtB;;EACA,IAAI,CAACE,SAAS,CAACK,IAAD,EAAOE,GAAG,CAACE,OAAX,CAAd,EAAmC;IACjCF,GAAG,CAACE,OAAJ,GAAcJ,IAAd;IACAG,SAAS,CAACC,OAAV,IAAqB,CAArB;EACD;;EACDV,mBAAmB,CAACK,MAAD,EAAS,CAACI,SAAS,CAACC,OAAX,CAAT,EAA8BH,MAA9B,CAAnB;AACD,CARD;;AASA,eAAeH,8BAAf"}, "metadata": {}, "sourceType": "module"}