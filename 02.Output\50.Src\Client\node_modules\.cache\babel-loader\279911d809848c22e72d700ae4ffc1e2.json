{"ast": null, "code": "/**\n * Possible states for the IStompSocket\n */\nexport var StompSocketState;\n\n(function (StompSocketState) {\n  StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n  StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n  StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n  StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\n\n\nexport var ActivationState;\n\n(function (ActivationState) {\n  ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n  ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));", "map": {"version": 3, "mappings": "AA0IA;;;AAGA,WAAYA,gBAAZ;;AAAA,WAAYA,gBAAZ,EAA4B;EAC1BA;EACAA;EACAA;EACAA;AACD,CALD,EAAYA,gBAAgB,KAAhBA,gBAAgB,MAA5B;AAOA;;;;;AAGA,WAAYC,eAAZ;;AAAA,WAAYA,eAAZ,EAA2B;EACzBA;EACAA;EACAA;AACD,CAJD,EAAYA,eAAe,KAAfA,eAAe,MAA3B", "names": ["StompSocketState", "ActivationState"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\types.ts"], "sourcesContent": ["import { <PERSON>rame } from './i-frame';\nimport { IMessage } from './i-message';\nimport { StompHeaders } from './stomp-headers';\n\n/**\n * This callback will receive a `string` as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type debugFnType = (msg: string) => void;\n\n/**\n * This callback will receive a {@link IMessage} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type messageCallbackType = (message: IMessage) => void;\n\n/**\n * This callback will receive a {@link IFrame} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type frameCallbackType = (receipt: IFrame) => void;\n\n/**\n * This callback will receive a [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type closeEventCallbackType<T = any> = (evt: T) => void;\n\n/**\n * This callback will receive an [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type wsErrorCallbackType<T = any> = (evt: T) => void;\n\n/**\n * Parameters for [Client#publish]{@link Client#publish}.\n * Aliased as publishParams as well.\n *\n * Part of `@stomp/stompjs`.\n */\nexport interface IPublishParams {\n  /**\n   * destination end point\n   */\n  destination: string;\n  /**\n   * headers (optional)\n   */\n  headers?: StompHeaders;\n  /**\n   * body (optional)\n   */\n  body?: string;\n  /**\n   * binary body (optional)\n   */\n  binaryBody?: Uint8Array;\n  /**\n   * By default a `content-length` header will be added in the Frame to the broker.\n   * Set it to `true` for the header to be skipped.\n   */\n  skipContentLengthHeader?: boolean;\n}\n\n/**\n * Backward compatibility, switch to {@link IPublishParams}.\n */\nexport type publishParams = IPublishParams;\n\n/**\n * Used in {@link IRawFrameType}\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport type RawHeaderType = [string, string];\n\n/**\n * The parser yield frames in this structure\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport interface IRawFrameType {\n  command: string;\n  headers: RawHeaderType[];\n  binaryBody: Uint8Array;\n}\n\n/**\n * @internal\n */\nexport interface IStompSocketMessageEvent {\n  data?: string | ArrayBuffer;\n}\n\n/**\n * Copied from Websocket interface to avoid dom typelib dependency.\n *\n * @internal\n */\nexport interface IStompSocket {\n  onclose: ((this: IStompSocket, ev?: any) => any) | null;\n  onerror: ((this: IStompSocket, ev: any) => any) | null;\n  onmessage: ((this: IStompSocket, ev: IStompSocketMessageEvent) => any) | null;\n  onopen: ((this: IStompSocket, ev?: any) => any) | null;\n  terminate?: ((this: IStompSocket) => any) | null;\n\n  /**\n   * Returns a string that indicates how binary data from the socket is exposed to scripts:\n   * We support only 'arraybuffer'.\n   */\n  binaryType: 'arraybuffer';\n\n  /**\n   * Returns the state of the socket connection. It can have the values of StompSocketState.\n   */\n  readonly readyState: number;\n\n  /**\n   * Closes the connection.\n   */\n  close(): void;\n  /**\n   * Transmits data using the connection. data can be a string or an ArrayBuffer.\n   */\n  send(data: string | ArrayBuffer): void;\n}\n\n/**\n * Possible states for the IStompSocket\n */\nexport enum StompSocketState {\n  CONNECTING,\n  OPEN,\n  CLOSING,\n  CLOSED,\n}\n\n/**\n * Possible activation state\n */\nexport enum ActivationState {\n  ACTIVE,\n  DEACTIVATING,\n  INACTIVE,\n}\n"]}, "metadata": {}, "sourceType": "module"}