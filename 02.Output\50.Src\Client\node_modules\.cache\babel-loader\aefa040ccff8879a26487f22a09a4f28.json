{"ast": null, "code": "/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = hashDelete;", "map": {"version": 3, "names": ["hashDelete", "key", "result", "has", "__data__", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_hashDelete.js"], "sourcesContent": ["/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = hashDelete;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAT,CAAoBC,GAApB,EAAyB;EACvB,IAAIC,MAAM,GAAG,KAAKC,GAAL,CAASF,GAAT,KAAiB,OAAO,KAAKG,QAAL,CAAcH,GAAd,CAArC;EACA,KAAKI,IAAL,IAAaH,MAAM,GAAG,CAAH,GAAO,CAA1B;EACA,OAAOA,MAAP;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiBP,UAAjB"}, "metadata": {}, "sourceType": "script"}