{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nvar listeners = [];\n\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    listeners.splice(index, 1);\n  };\n}\n\nif (isBrowser) {\n  var revalidate = function revalidate() {\n    if (!isDocumentVisible()) return;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n\n  window.addEventListener('visibilitychange', revalidate, false);\n}\n\nexport default subscribe;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "listeners", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice", "revalidate", "i", "length", "window", "addEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nimport isDocumentVisible from './isDocumentVisible';\nvar listeners = [];\nfunction subscribe(listener) {\n  listeners.push(listener);\n  return function unsubscribe() {\n    var index = listeners.indexOf(listener);\n    listeners.splice(index, 1);\n  };\n}\nif (isBrowser) {\n  var revalidate = function revalidate() {\n    if (!isDocumentVisible()) return;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n  };\n  window.addEventListener('visibilitychange', revalidate, false);\n}\nexport default subscribe;"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,0BAAtB;AACA,OAAOC,iBAAP,MAA8B,qBAA9B;AACA,IAAIC,SAAS,GAAG,EAAhB;;AACA,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAC3BF,SAAS,CAACG,IAAV,CAAeD,QAAf;EACA,OAAO,SAASE,WAAT,GAAuB;IAC5B,IAAIC,KAAK,GAAGL,SAAS,CAACM,OAAV,CAAkBJ,QAAlB,CAAZ;IACAF,SAAS,CAACO,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;EACD,CAHD;AAID;;AACD,IAAIP,SAAJ,EAAe;EACb,IAAIU,UAAU,GAAG,SAASA,UAAT,GAAsB;IACrC,IAAI,CAACT,iBAAiB,EAAtB,EAA0B;;IAC1B,KAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,SAAS,CAACU,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;MACzC,IAAIP,QAAQ,GAAGF,SAAS,CAACS,CAAD,CAAxB;MACAP,QAAQ;IACT;EACF,CAND;;EAOAS,MAAM,CAACC,gBAAP,CAAwB,kBAAxB,EAA4CJ,UAA5C,EAAwD,KAAxD;AACD;;AACD,eAAeP,SAAf"}, "metadata": {}, "sourceType": "module"}