{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\nexport default platform.isStandardBrowserEnv ? // Standard browser envs have full support of the APIs needed to test\n// whether the request URL is of the same origin as current location.\nfunction standardBrowserEnv() {\n  const msie = /(msie|trident)/i.test(navigator.userAgent);\n  const urlParsingNode = document.createElement('a');\n  let originURL;\n  /**\n  * Parse a URL to discover it's components\n  *\n  * @param {String} url The URL to be parsed\n  * @returns {Object}\n  */\n\n  function resolveURL(url) {\n    let href = url;\n\n    if (msie) {\n      // IE needs attribute set twice to normalize properties\n      urlParsingNode.setAttribute('href', href);\n      href = urlParsingNode.href;\n    }\n\n    urlParsingNode.setAttribute('href', href); // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n\n    return {\n      href: urlParsingNode.href,\n      protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n      host: urlParsingNode.host,\n      search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n      hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n      hostname: urlParsingNode.hostname,\n      port: urlParsingNode.port,\n      pathname: urlParsingNode.pathname.charAt(0) === '/' ? urlParsingNode.pathname : '/' + urlParsingNode.pathname\n    };\n  }\n\n  originURL = resolveURL(window.location.href);\n  /**\n  * Determine if a URL shares the same origin as the current location\n  *\n  * @param {String} requestURL The URL to test\n  * @returns {boolean} True if URL shares the same origin, otherwise false\n  */\n\n  return function isURLSameOrigin(requestURL) {\n    const parsed = utils.isString(requestURL) ? resolveURL(requestURL) : requestURL;\n    return parsed.protocol === originURL.protocol && parsed.host === originURL.host;\n  };\n}() : // Non standard browser envs (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n  return function isURLSameOrigin() {\n    return true;\n  };\n}();", "map": {"version": 3, "names": ["utils", "platform", "isStandardBrowserEnv", "standardBrowserEnv", "msie", "test", "navigator", "userAgent", "urlParsingNode", "document", "createElement", "originURL", "resolveURL", "url", "href", "setAttribute", "protocol", "replace", "host", "search", "hash", "hostname", "port", "pathname", "char<PERSON>t", "window", "location", "isURLSameOrigin", "requestURL", "parsed", "isString", "nonStandardBrowserEnv"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/isURLSameOrigin.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.isStandardBrowserEnv ?\n\n// Standard browser envs have full support of the APIs needed to test\n// whether the request URL is of the same origin as current location.\n  (function standardBrowserEnv() {\n    const msie = /(msie|trident)/i.test(navigator.userAgent);\n    const urlParsingNode = document.createElement('a');\n    let originURL;\n\n    /**\n    * Parse a URL to discover it's components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */\n    function resolveURL(url) {\n      let href = url;\n\n      if (msie) {\n        // IE needs attribute set twice to normalize properties\n        urlParsingNode.setAttribute('href', href);\n        href = urlParsingNode.href;\n      }\n\n      urlParsingNode.setAttribute('href', href);\n\n      // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n      return {\n        href: urlParsingNode.href,\n        protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',\n        host: urlParsingNode.host,\n        search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, '') : '',\n        hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',\n        hostname: urlParsingNode.hostname,\n        port: urlParsingNode.port,\n        pathname: (urlParsingNode.pathname.charAt(0) === '/') ?\n          urlParsingNode.pathname :\n          '/' + urlParsingNode.pathname\n      };\n    }\n\n    originURL = resolveURL(window.location.href);\n\n    /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */\n    return function isURLSameOrigin(requestURL) {\n      const parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;\n      return (parsed.protocol === originURL.protocol &&\n          parsed.host === originURL.host);\n    };\n  })() :\n\n  // Non standard browser envs (web workers, react-native) lack needed support.\n  (function nonStandardBrowserEnv() {\n    return function isURLSameOrigin() {\n      return true;\n    };\n  })();\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AAEA,eAAeA,QAAQ,CAACC,oBAAT,GAEf;AACA;AACG,SAASC,kBAAT,GAA8B;EAC7B,MAAMC,IAAI,GAAG,kBAAkBC,IAAlB,CAAuBC,SAAS,CAACC,SAAjC,CAAb;EACA,MAAMC,cAAc,GAAGC,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAvB;EACA,IAAIC,SAAJ;EAEA;AACJ;AACA;AACA;AACA;AACA;;EACI,SAASC,UAAT,CAAoBC,GAApB,EAAyB;IACvB,IAAIC,IAAI,GAAGD,GAAX;;IAEA,IAAIT,IAAJ,EAAU;MACR;MACAI,cAAc,CAACO,YAAf,CAA4B,MAA5B,EAAoCD,IAApC;MACAA,IAAI,GAAGN,cAAc,CAACM,IAAtB;IACD;;IAEDN,cAAc,CAACO,YAAf,CAA4B,MAA5B,EAAoCD,IAApC,EATuB,CAWvB;;IACA,OAAO;MACLA,IAAI,EAAEN,cAAc,CAACM,IADhB;MAELE,QAAQ,EAAER,cAAc,CAACQ,QAAf,GAA0BR,cAAc,CAACQ,QAAf,CAAwBC,OAAxB,CAAgC,IAAhC,EAAsC,EAAtC,CAA1B,GAAsE,EAF3E;MAGLC,IAAI,EAAEV,cAAc,CAACU,IAHhB;MAILC,MAAM,EAAEX,cAAc,CAACW,MAAf,GAAwBX,cAAc,CAACW,MAAf,CAAsBF,OAAtB,CAA8B,KAA9B,EAAqC,EAArC,CAAxB,GAAmE,EAJtE;MAKLG,IAAI,EAAEZ,cAAc,CAACY,IAAf,GAAsBZ,cAAc,CAACY,IAAf,CAAoBH,OAApB,CAA4B,IAA5B,EAAkC,EAAlC,CAAtB,GAA8D,EAL/D;MAMLI,QAAQ,EAAEb,cAAc,CAACa,QANpB;MAOLC,IAAI,EAAEd,cAAc,CAACc,IAPhB;MAQLC,QAAQ,EAAGf,cAAc,CAACe,QAAf,CAAwBC,MAAxB,CAA+B,CAA/B,MAAsC,GAAvC,GACRhB,cAAc,CAACe,QADP,GAER,MAAMf,cAAc,CAACe;IAVlB,CAAP;EAYD;;EAEDZ,SAAS,GAAGC,UAAU,CAACa,MAAM,CAACC,QAAP,CAAgBZ,IAAjB,CAAtB;EAEA;AACJ;AACA;AACA;AACA;AACA;;EACI,OAAO,SAASa,eAAT,CAAyBC,UAAzB,EAAqC;IAC1C,MAAMC,MAAM,GAAI7B,KAAK,CAAC8B,QAAN,CAAeF,UAAf,CAAD,GAA+BhB,UAAU,CAACgB,UAAD,CAAzC,GAAwDA,UAAvE;IACA,OAAQC,MAAM,CAACb,QAAP,KAAoBL,SAAS,CAACK,QAA9B,IACJa,MAAM,CAACX,IAAP,KAAgBP,SAAS,CAACO,IAD9B;EAED,CAJD;AAKD,CAlDD,EAJa,GAwDb;AACC,SAASa,qBAAT,GAAiC;EAChC,OAAO,SAASJ,eAAT,GAA2B;IAChC,OAAO,IAAP;EACD,CAFD;AAGD,CAJD,EAzDF"}, "metadata": {}, "sourceType": "module"}