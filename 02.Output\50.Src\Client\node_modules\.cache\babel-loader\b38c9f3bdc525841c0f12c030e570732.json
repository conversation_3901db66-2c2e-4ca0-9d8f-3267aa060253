{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\n\nexport default function useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _a = options.reconnectLimit,\n      reconnectLimit = _a === void 0 ? 3 : _a,\n      _b = options.reconnectInterval,\n      reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n      _c = options.manual,\n      manual = _c === void 0 ? false : _c,\n      onOpen = options.onOpen,\n      onClose = options.onClose,\n      onMessage = options.onMessage,\n      onError = options.onError,\n      protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef();\n  var websocketRef = useRef();\n  var unmountedRef = useRef(false);\n\n  var _d = __read(useState(), 2),\n      latestMessage = _d[0],\n      setLatestMessage = _d[1];\n\n  var _e = __read(useState(ReadyState.Closed), 2),\n      readyState = _e[0],\n      setReadyState = _e[1];\n\n  var reconnect = function reconnect() {\n    var _a;\n\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n\n  var connectWs = function connectWs() {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n\n    ws.onerror = function (event) {\n      var _a;\n\n      if (unmountedRef.current) {\n        return;\n      }\n\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n\n    ws.onopen = function (event) {\n      var _a;\n\n      if (unmountedRef.current) {\n        return;\n      }\n\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n\n    ws.onmessage = function (message) {\n      var _a;\n\n      if (unmountedRef.current) {\n        return;\n      }\n\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n\n    ws.onclose = function (event) {\n      var _a;\n\n      if (unmountedRef.current) {\n        return;\n      }\n\n      reconnect();\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n\n    websocketRef.current = ws;\n  };\n\n  var sendMessage = function sendMessage(message) {\n    var _a;\n\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n\n  var connect = function connect() {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n\n  var disconnect = function disconnect() {\n    var _a;\n\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n  };\n\n  useEffect(function () {\n    if (!manual) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    unmountedRef.current = true;\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useRef", "useState", "useLatest", "useMemoizedFn", "useUnmount", "ReadyState", "useWebSocket", "socketUrl", "options", "_a", "reconnectLimit", "_b", "reconnectInterval", "_c", "manual", "onOpen", "onClose", "onMessage", "onError", "protocols", "onOpenRef", "onCloseRef", "onMessageRef", "onErrorRef", "reconnectTimesRef", "reconnectTimerRef", "websocketRef", "unmountedRef", "_d", "latestMessage", "setLatestMessage", "_e", "Closed", "readyState", "setReadyState", "reconnect", "current", "Open", "clearTimeout", "setTimeout", "connectWs", "close", "ws", "WebSocket", "Connecting", "onerror", "event", "onopen", "onmessage", "message", "onclose", "sendMessage", "send", "Error", "connect", "disconnect", "webSocketIns"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useWebSocket/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\nexport default function useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.reconnectLimit,\n    reconnectLimit = _a === void 0 ? 3 : _a,\n    _b = options.reconnectInterval,\n    reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onMessage = options.onMessage,\n    onError = options.onError,\n    protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef();\n  var websocketRef = useRef();\n  var unmountedRef = useRef(false);\n  var _d = __read(useState(), 2),\n    latestMessage = _d[0],\n    setLatestMessage = _d[1];\n  var _e = __read(useState(ReadyState.Closed), 2),\n    readyState = _e[0],\n    setReadyState = _e[1];\n  var reconnect = function reconnect() {\n    var _a;\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n  var connectWs = function connectWs() {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n    ws.onerror = function (event) {\n      var _a;\n      if (unmountedRef.current) {\n        return;\n      }\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    ws.onopen = function (event) {\n      var _a;\n      if (unmountedRef.current) {\n        return;\n      }\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n    ws.onmessage = function (message) {\n      var _a;\n      if (unmountedRef.current) {\n        return;\n      }\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n    ws.onclose = function (event) {\n      var _a;\n      if (unmountedRef.current) {\n        return;\n      }\n      reconnect();\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    websocketRef.current = ws;\n  };\n  var sendMessage = function sendMessage(message) {\n    var _a;\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n  var connect = function connect() {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n  var disconnect = function disconnect() {\n    var _a;\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n  };\n  useEffect(function () {\n    if (!manual) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    unmountedRef.current = true;\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,MAApB,EAA4BC,QAA5B,QAA4C,OAA5C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,OAAO,IAAIC,UAAJ;;AACP,CAAC,UAAUA,UAAV,EAAsB;EACrBA,UAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;EACAA,UAAU,CAACA,UAAU,CAAC,MAAD,CAAV,GAAqB,CAAtB,CAAV,GAAqC,MAArC;EACAA,UAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC;EACAA,UAAU,CAACA,UAAU,CAAC,QAAD,CAAV,GAAuB,CAAxB,CAAV,GAAuC,QAAvC;AACD,CALD,EAKGA,UAAU,KAAKA,UAAU,GAAG,EAAlB,CALb;;AAMA,eAAe,SAASC,YAAT,CAAsBC,SAAtB,EAAiCC,OAAjC,EAA0C;EACvD,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,EAAE,GAAGD,OAAO,CAACE,cAAjB;EAAA,IACEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAhB,GAAoBA,EADvC;EAAA,IAEEE,EAAE,GAAGH,OAAO,CAACI,iBAFf;EAAA,IAGEA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAI,IAApB,GAA2BA,EAHjD;EAAA,IAIEE,EAAE,GAAGL,OAAO,CAACM,MAJf;EAAA,IAKEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EALnC;EAAA,IAMEE,MAAM,GAAGP,OAAO,CAACO,MANnB;EAAA,IAOEC,OAAO,GAAGR,OAAO,CAACQ,OAPpB;EAAA,IAQEC,SAAS,GAAGT,OAAO,CAACS,SARtB;EAAA,IASEC,OAAO,GAAGV,OAAO,CAACU,OATpB;EAAA,IAUEC,SAAS,GAAGX,OAAO,CAACW,SAVtB;EAWA,IAAIC,SAAS,GAAGlB,SAAS,CAACa,MAAD,CAAzB;EACA,IAAIM,UAAU,GAAGnB,SAAS,CAACc,OAAD,CAA1B;EACA,IAAIM,YAAY,GAAGpB,SAAS,CAACe,SAAD,CAA5B;EACA,IAAIM,UAAU,GAAGrB,SAAS,CAACgB,OAAD,CAA1B;EACA,IAAIM,iBAAiB,GAAGxB,MAAM,CAAC,CAAD,CAA9B;EACA,IAAIyB,iBAAiB,GAAGzB,MAAM,EAA9B;EACA,IAAI0B,YAAY,GAAG1B,MAAM,EAAzB;EACA,IAAI2B,YAAY,GAAG3B,MAAM,CAAC,KAAD,CAAzB;;EACA,IAAI4B,EAAE,GAAG7C,MAAM,CAACkB,QAAQ,EAAT,EAAa,CAAb,CAAf;EAAA,IACE4B,aAAa,GAAGD,EAAE,CAAC,CAAD,CADpB;EAAA,IAEEE,gBAAgB,GAAGF,EAAE,CAAC,CAAD,CAFvB;;EAGA,IAAIG,EAAE,GAAGhD,MAAM,CAACkB,QAAQ,CAACI,UAAU,CAAC2B,MAAZ,CAAT,EAA8B,CAA9B,CAAf;EAAA,IACEC,UAAU,GAAGF,EAAE,CAAC,CAAD,CADjB;EAAA,IAEEG,aAAa,GAAGH,EAAE,CAAC,CAAD,CAFpB;;EAGA,IAAII,SAAS,GAAG,SAASA,SAAT,GAAqB;IACnC,IAAI1B,EAAJ;;IACA,IAAIe,iBAAiB,CAACY,OAAlB,GAA4B1B,cAA5B,IAA8C,CAAC,CAACD,EAAE,GAAGiB,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwC3B,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACwB,UAArE,MAAqF5B,UAAU,CAACgC,IAAlJ,EAAwJ;MACtJ,IAAIZ,iBAAiB,CAACW,OAAtB,EAA+B;QAC7BE,YAAY,CAACb,iBAAiB,CAACW,OAAnB,CAAZ;MACD;;MACDX,iBAAiB,CAACW,OAAlB,GAA4BG,UAAU,CAAC,YAAY;QACjD;QACAC,SAAS;QACThB,iBAAiB,CAACY,OAAlB;MACD,CAJqC,EAInCxB,iBAJmC,CAAtC;IAKD;EACF,CAZD;;EAaA,IAAI4B,SAAS,GAAG,SAASA,SAAT,GAAqB;IACnC,IAAIf,iBAAiB,CAACW,OAAtB,EAA+B;MAC7BE,YAAY,CAACb,iBAAiB,CAACW,OAAnB,CAAZ;IACD;;IACD,IAAIV,YAAY,CAACU,OAAjB,EAA0B;MACxBV,YAAY,CAACU,OAAb,CAAqBK,KAArB;IACD;;IACD,IAAIC,EAAE,GAAG,IAAIC,SAAJ,CAAcpC,SAAd,EAAyBY,SAAzB,CAAT;IACAe,aAAa,CAAC7B,UAAU,CAACuC,UAAZ,CAAb;;IACAF,EAAE,CAACG,OAAH,GAAa,UAAUC,KAAV,EAAiB;MAC5B,IAAIrC,EAAJ;;MACA,IAAIkB,YAAY,CAACS,OAAjB,EAA0B;QACxB;MACD;;MACDD,SAAS;MACT,CAAC1B,EAAE,GAAGc,UAAU,CAACa,OAAjB,MAA8B,IAA9B,IAAsC3B,EAAE,KAAK,KAAK,CAAlD,GAAsD,KAAK,CAA3D,GAA+DA,EAAE,CAACnB,IAAH,CAAQiC,UAAR,EAAoBuB,KAApB,EAA2BJ,EAA3B,CAA/D;MACAR,aAAa,CAACQ,EAAE,CAACT,UAAH,IAAiB5B,UAAU,CAAC2B,MAA7B,CAAb;IACD,CARD;;IASAU,EAAE,CAACK,MAAH,GAAY,UAAUD,KAAV,EAAiB;MAC3B,IAAIrC,EAAJ;;MACA,IAAIkB,YAAY,CAACS,OAAjB,EAA0B;QACxB;MACD;;MACD,CAAC3B,EAAE,GAAGW,SAAS,CAACgB,OAAhB,MAA6B,IAA7B,IAAqC3B,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACnB,IAAH,CAAQ8B,SAAR,EAAmB0B,KAAnB,EAA0BJ,EAA1B,CAA9D;MACAlB,iBAAiB,CAACY,OAAlB,GAA4B,CAA5B;MACAF,aAAa,CAACQ,EAAE,CAACT,UAAH,IAAiB5B,UAAU,CAACgC,IAA7B,CAAb;IACD,CARD;;IASAK,EAAE,CAACM,SAAH,GAAe,UAAUC,OAAV,EAAmB;MAChC,IAAIxC,EAAJ;;MACA,IAAIkB,YAAY,CAACS,OAAjB,EAA0B;QACxB;MACD;;MACD,CAAC3B,EAAE,GAAGa,YAAY,CAACc,OAAnB,MAAgC,IAAhC,IAAwC3B,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACnB,IAAH,CAAQgC,YAAR,EAAsB2B,OAAtB,EAA+BP,EAA/B,CAAjE;MACAZ,gBAAgB,CAACmB,OAAD,CAAhB;IACD,CAPD;;IAQAP,EAAE,CAACQ,OAAH,GAAa,UAAUJ,KAAV,EAAiB;MAC5B,IAAIrC,EAAJ;;MACA,IAAIkB,YAAY,CAACS,OAAjB,EAA0B;QACxB;MACD;;MACDD,SAAS;MACT,CAAC1B,EAAE,GAAGY,UAAU,CAACe,OAAjB,MAA8B,IAA9B,IAAsC3B,EAAE,KAAK,KAAK,CAAlD,GAAsD,KAAK,CAA3D,GAA+DA,EAAE,CAACnB,IAAH,CAAQ+B,UAAR,EAAoByB,KAApB,EAA2BJ,EAA3B,CAA/D;MACAR,aAAa,CAACQ,EAAE,CAACT,UAAH,IAAiB5B,UAAU,CAAC2B,MAA7B,CAAb;IACD,CARD;;IASAN,YAAY,CAACU,OAAb,GAAuBM,EAAvB;EACD,CA7CD;;EA8CA,IAAIS,WAAW,GAAG,SAASA,WAAT,CAAqBF,OAArB,EAA8B;IAC9C,IAAIxC,EAAJ;;IACA,IAAIwB,UAAU,KAAK5B,UAAU,CAACgC,IAA9B,EAAoC;MAClC,CAAC5B,EAAE,GAAGiB,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwC3B,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAAC2C,IAAH,CAAQH,OAAR,CAAjE;IACD,CAFD,MAEO;MACL,MAAM,IAAII,KAAJ,CAAU,wBAAV,CAAN;IACD;EACF,CAPD;;EAQA,IAAIC,OAAO,GAAG,SAASA,OAAT,GAAmB;IAC/B9B,iBAAiB,CAACY,OAAlB,GAA4B,CAA5B;IACAI,SAAS;EACV,CAHD;;EAIA,IAAIe,UAAU,GAAG,SAASA,UAAT,GAAsB;IACrC,IAAI9C,EAAJ;;IACA,IAAIgB,iBAAiB,CAACW,OAAtB,EAA+B;MAC7BE,YAAY,CAACb,iBAAiB,CAACW,OAAnB,CAAZ;IACD;;IACDZ,iBAAiB,CAACY,OAAlB,GAA4B1B,cAA5B;IACA,CAACD,EAAE,GAAGiB,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwC3B,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACgC,KAAH,EAAjE;EACD,CAPD;;EAQA1C,SAAS,CAAC,YAAY;IACpB,IAAI,CAACe,MAAL,EAAa;MACXwC,OAAO;IACR;EACF,CAJQ,EAIN,CAAC/C,SAAD,EAAYO,MAAZ,CAJM,CAAT;EAKAV,UAAU,CAAC,YAAY;IACrBuB,YAAY,CAACS,OAAb,GAAuB,IAAvB;IACAmB,UAAU;EACX,CAHS,CAAV;EAIA,OAAO;IACL1B,aAAa,EAAEA,aADV;IAELsB,WAAW,EAAEhD,aAAa,CAACgD,WAAD,CAFrB;IAGLG,OAAO,EAAEnD,aAAa,CAACmD,OAAD,CAHjB;IAILC,UAAU,EAAEpD,aAAa,CAACoD,UAAD,CAJpB;IAKLtB,UAAU,EAAEA,UALP;IAMLuB,YAAY,EAAE9B,YAAY,CAACU;EANtB,CAAP;AAQD"}, "metadata": {}, "sourceType": "module"}