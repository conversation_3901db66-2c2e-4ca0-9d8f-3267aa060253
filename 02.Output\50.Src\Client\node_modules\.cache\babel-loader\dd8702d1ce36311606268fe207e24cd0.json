{"ast": null, "code": "var ListCache = require('./_ListCache');\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\n\n\nfunction stackClear() {\n  this.__data__ = new ListCache();\n  this.size = 0;\n}\n\nmodule.exports = stackClear;", "map": {"version": 3, "names": ["ListCache", "require", "stackClear", "__data__", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_stackClear.js"], "sourcesContent": ["var ListCache = require('./_ListCache');\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\nmodule.exports = stackClear;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,GAAsB;EACpB,KAAKC,QAAL,GAAgB,IAAIH,SAAJ,EAAhB;EACA,KAAKI,IAAL,GAAY,CAAZ;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBJ,UAAjB"}, "metadata": {}, "sourceType": "script"}