{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";import React,{useRef,useState,useEffect}from'react';import Cell from'./Cell';import{useDeepCompareEffect}from'ahooks';import{jsx as _jsx}from\"react/jsx-runtime\";/**\r\n * 切替設定に従い、配列をスクロールして表示する <br>\r\n * ただ、切替設定/切替サイズが0の場合は切り替えを行わない。\r\n * @module ArrayScroll\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */var ArrayScroll=function ArrayScroll(props){var _props$change_setting,_showInfo$subArray;var maxNum=props===null||props===void 0?void 0:props.max;//最大のChangeSizeを表示箱に超えないようにガイド\nif((props===null||props===void 0?void 0:(_props$change_setting=props.change_setting)===null||_props$change_setting===void 0?void 0:_props$change_setting.change_size)>maxNum){props.change_setting.change_size=maxNum;}//最初の表示は、最大サイズ(props.max)で配列を初期化する\nvar defaultSubArray=props===null||props===void 0?void 0:props.display_data;if(maxNum){var _props$display_data;defaultSubArray=props===null||props===void 0?void 0:(_props$display_data=props.display_data)===null||_props$display_data===void 0?void 0:_props$display_data.slice(0,maxNum);defaultSubArray=fillArrayWithDummy(defaultSubArray,maxNum);}var _useState=useState({subArray:defaultSubArray,changeSize:0}),_useState2=_slicedToArray(_useState,2),showInfo=_useState2[0],setShowInfo=_useState2[1];// RefでsetIntervalの各Intervalで、表示配列を共有する\nvar showInfoRef=useRef(showInfo);useDeepCompareEffect(function(){var id=null;// change_time/change_sizeが0の場合は切り替えを行わない\nif(canScroll(props)){var _props$change_setting3;id=setInterval(function(){var _props$display_data2,_props$display_data3,_props$display_data5;var nextSize;if(showInfoRef.current.changeSize+maxNum>=(props===null||props===void 0?void 0:(_props$display_data2=props.display_data)===null||_props$display_data2===void 0?void 0:_props$display_data2.length)){nextSize=0;}else{var _props$change_setting2;nextSize=showInfoRef.current.changeSize+(props===null||props===void 0?void 0:(_props$change_setting2=props.change_setting)===null||_props$change_setting2===void 0?void 0:_props$change_setting2.change_size);}//次の表示サイズは、最大の表示サイズMaxNumより、少ない場合、MaxNumを表示する\nif(nextSize+maxNum>=(props===null||props===void 0?void 0:(_props$display_data3=props.display_data)===null||_props$display_data3===void 0?void 0:_props$display_data3.length)){var _props$display_data4;nextSize=(props===null||props===void 0?void 0:(_props$display_data4=props.display_data)===null||_props$display_data4===void 0?void 0:_props$display_data4.length)-maxNum;}var newState={changeSize:nextSize,subArray:props===null||props===void 0?void 0:(_props$display_data5=props.display_data)===null||_props$display_data5===void 0?void 0:_props$display_data5.slice(nextSize,nextSize+maxNum)};setShowInfo(newState);showInfoRef.current=newState;},(props===null||props===void 0?void 0:(_props$change_setting3=props.change_setting)===null||_props$change_setting3===void 0?void 0:_props$change_setting3.change_time)*1000);}else if(props!==null&&props!==void 0&&props.display_data){var _props$display_data6;var newState={changeSize:0,subArray:props===null||props===void 0?void 0:(_props$display_data6=props.display_data)===null||_props$display_data6===void 0?void 0:_props$display_data6.slice(0,0+maxNum)};newState.subArray=fillArrayWithDummy(newState.subArray,maxNum);setShowInfo(newState);showInfoRef.current=newState;}if(id){return function(){return clearInterval(id);};}},[props===null||props===void 0?void 0:props.change_setting,props===null||props===void 0?void 0:props.display_data]);if(!props){console.error('ArrayScroll: props is null.');return;}return/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(props===null||props===void 0?void 0:props.gridLevelProps),children:(_showInfo$subArray=showInfo.subArray)===null||_showInfo$subArray===void 0?void 0:_showInfo$subArray.map(function(item,index){if(Array.isArray(item)){return/*#__PURE__*/_jsx(\"span\",{children:\" \\u914D\\u5217\\u8868\\u793A\\u3092\\u30B5\\u30DD\\u30FC\\u30C8\\u3057\\u307E\\u305B\\u3093 \"})// <ArrayRow key={index} items={item} cellLevelProps={props?.cellLevelProps} />\n;}else{var _props$cellLevelProps;var cellPropsIndex=index%(props===null||props===void 0?void 0:(_props$cellLevelProps=props.cellLevelProps)===null||_props$cellLevelProps===void 0?void 0:_props$cellLevelProps.length);var row4Cell={text_color:item.text_color,text:item.display_text,background_color:item.background_color,className:props===null||props===void 0?void 0:props.cellLevelProps[cellPropsIndex]};return/*#__PURE__*/_jsx(Cell,_objectSpread({},row4Cell),index);}})});};// 表示箱より少ない場合、Dummyデータを作成\nfunction fillArrayWithDummy(defaultSubArray,maxNum){if(defaultSubArray.length<maxNum){var dummyCounter=maxNum-defaultSubArray.length;for(var i=0;i<dummyCounter;i++){defaultSubArray.push({display_text:'　'});}}return defaultSubArray;}/**\r\n * change_size/change_timeが有効な値があれば、Scrollすると返します。\r\n * @param {*} obj\r\n * @returns true: Scroll. false: Scrollしない\r\n */function canScroll(obj){var _obj$change_setting,_obj$change_setting2;if(!obj)return false;return(obj===null||obj===void 0?void 0:obj.display_data)&&(obj===null||obj===void 0?void 0:obj.display_data.length)>(obj===null||obj===void 0?void 0:obj.max)&&(obj===null||obj===void 0?void 0:obj.change_setting)&&(obj===null||obj===void 0?void 0:(_obj$change_setting=obj.change_setting)===null||_obj$change_setting===void 0?void 0:_obj$change_setting.change_time)>0&&(obj===null||obj===void 0?void 0:(_obj$change_setting2=obj.change_setting)===null||_obj$change_setting2===void 0?void 0:_obj$change_setting2.change_size)>0;}export default ArrayScroll;", "map": {"version": 3, "names": ["React", "useRef", "useState", "useEffect", "Cell", "useDeepCompareEffect", "ArrayScroll", "props", "maxNum", "max", "change_setting", "change_size", "defaultSubArray", "display_data", "slice", "fillArrayWithDummy", "subArray", "changeSize", "showInfo", "setShowInfo", "showInfoRef", "id", "canScroll", "setInterval", "nextSize", "current", "length", "newState", "change_time", "clearInterval", "console", "error", "gridLevelProps", "map", "item", "index", "Array", "isArray", "cellPropsIndex", "cellLevelProps", "row4Cell", "text_color", "text", "display_text", "background_color", "className", "dummy<PERSON><PERSON><PERSON>", "i", "push", "obj"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/ArrayScroll.js"], "sourcesContent": ["import React, { useRef, useState, useEffect } from 'react';\r\nimport Cell from './Cell';\r\nimport { useDeepCompareEffect } from 'ahooks';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  max: PropTypes.number,\r\n  change_setting: PropTypes.object,\r\n  display_data: PropTypes.array,\r\n  gridLevelProps: PropTypes.string,\r\n  cellLevelProps: PropTypes.array,\r\n};\r\n\r\n/**\r\n * 切替設定に従い、配列をスクロールして表示する <br>\r\n * ただ、切替設定/切替サイズが0の場合は切り替えを行わない。\r\n * @module ArrayScroll\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\r\nconst ArrayScroll = (props) => {\r\n  const maxNum = props?.max;\r\n  //最大のChangeSizeを表示箱に超えないようにガイド\r\n  if (props?.change_setting?.change_size > maxNum) {\r\n    props.change_setting.change_size = maxNum;\r\n  }\r\n\r\n  //最初の表示は、最大サイズ(props.max)で配列を初期化する\r\n  let defaultSubArray = props?.display_data;\r\n  if (maxNum) {\r\n    defaultSubArray = props?.display_data?.slice(0, maxNum);\r\n    \r\n    defaultSubArray = fillArrayWithDummy(defaultSubArray, maxNum);\r\n  }\r\n\r\n  const [showInfo, setShowInfo] = useState({\r\n    subArray: defaultSubArray,\r\n    changeSize: 0,\r\n  });\r\n  // RefでsetIntervalの各Intervalで、表示配列を共有する\r\n  const showInfoRef = useRef(showInfo);\r\n\r\n  useDeepCompareEffect(() => {\r\n    let id = null;\r\n\r\n    // change_time/change_sizeが0の場合は切り替えを行わない\r\n    if (canScroll(props)) {\r\n      id = setInterval(() => {\r\n        let nextSize;\r\n        if (\r\n          showInfoRef.current.changeSize + maxNum >=\r\n          props?.display_data?.length\r\n        ) {\r\n          nextSize = 0;\r\n        } else {\r\n          nextSize =\r\n            showInfoRef.current.changeSize + props?.change_setting?.change_size;\r\n        }\r\n\r\n        //次の表示サイズは、最大の表示サイズMaxNumより、少ない場合、MaxNumを表示する\r\n        if (nextSize + maxNum >= props?.display_data?.length) { \r\n          nextSize = props?.display_data?.length - maxNum;\r\n        }\r\n\r\n        const newState = {\r\n          changeSize: nextSize,\r\n          subArray: props?.display_data?.slice(nextSize, nextSize + maxNum),\r\n        };\r\n        setShowInfo(newState);\r\n        showInfoRef.current = newState;\r\n      }, props?.change_setting?.change_time * 1000);\r\n    } else if (props?.display_data) {\r\n        const newState = {\r\n          changeSize: 0,\r\n          subArray: props?.display_data?.slice(0, 0 + maxNum),\r\n        };\r\n        newState.subArray = fillArrayWithDummy(newState.subArray, maxNum);\r\n\r\n        setShowInfo(newState);\r\n        showInfoRef.current = newState;\r\n    }\r\n\r\n    if (id) {\r\n      return () => clearInterval(id);\r\n    }\r\n  }, [props?.change_setting, props?.display_data]);\r\n\r\n  if (!props) {\r\n    console.error('ArrayScroll: props is null.');\r\n    return;\r\n  }\r\n\r\n  return (\r\n    <div className={`grid ${props?.gridLevelProps}`}>\r\n      {showInfo.subArray?.map((item, index) => {\r\n        if (Array.isArray(item)) {\r\n          return (\r\n            <span> 配列表示をサポートしません </span>\r\n            // <ArrayRow key={index} items={item} cellLevelProps={props?.cellLevelProps} />\r\n          );\r\n        } else {\r\n          const cellPropsIndex = index % props?.cellLevelProps?.length;\r\n          let row4Cell = {\r\n            text_color: item.text_color,\r\n            text: item.display_text,\r\n            background_color: item.background_color,\r\n            className: props?.cellLevelProps[cellPropsIndex],\r\n          };\r\n\r\n          return (\r\n            <Cell\r\n              key={index}\r\n              {...row4Cell}\r\n            />\r\n          );\r\n        }\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// 表示箱より少ない場合、Dummyデータを作成\r\nfunction fillArrayWithDummy(defaultSubArray, maxNum) {\r\n  if (defaultSubArray.length < maxNum) {\r\n    const dummyCounter = maxNum - defaultSubArray.length;\r\n    for (let i = 0; i < dummyCounter; i++) {\r\n      defaultSubArray.push({ display_text: '　' });\r\n    }\r\n  }\r\n  return defaultSubArray;\r\n}\r\n\r\n/**\r\n * change_size/change_timeが有効な値があれば、Scrollすると返します。\r\n * @param {*} obj\r\n * @returns true: Scroll. false: Scrollしない\r\n */\r\nfunction canScroll(obj) {\r\n  if (!obj) return false;\r\n\r\n  return (\r\n    obj?.display_data &&\r\n    obj?.display_data.length > obj?.max &&\r\n    obj?.change_setting &&\r\n    obj?.change_setting?.change_time > 0 &&\r\n    obj?.change_setting?.change_size > 0\r\n  );\r\n}\r\n\r\nArrayScroll.propTypes = propTypes;\r\n\r\nexport default ArrayScroll;\r\n"], "mappings": "mRAAA,MAAOA,MAAP,EAAgBC,MAAhB,CAAwBC,QAAxB,CAAkCC,SAAlC,KAAmD,OAAnD,CACA,MAAOC,KAAP,KAAiB,QAAjB,CACA,OAASC,oBAAT,KAAqC,QAArC,C,2CAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,YAAW,CAAG,QAAdA,YAAc,CAACC,KAAD,CAAW,8CAC7B,GAAMC,OAAM,CAAGD,KAAH,SAAGA,KAAH,iBAAGA,KAAK,CAAEE,GAAtB,CACA;AACA,GAAI,CAAAF,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAEG,cAAP,sEAAuBC,WAAvB,EAAqCH,MAAzC,CAAiD,CAC/CD,KAAK,CAACG,cAAN,CAAqBC,WAArB,CAAmCH,MAAnC,CACD,CAED;AACA,GAAII,gBAAe,CAAGL,KAAH,SAAGA,KAAH,iBAAGA,KAAK,CAAEM,YAA7B,CACA,GAAIL,MAAJ,CAAY,yBACVI,eAAe,CAAGL,KAAH,SAAGA,KAAH,sCAAGA,KAAK,CAAEM,YAAV,8CAAG,oBAAqBC,KAArB,CAA2B,CAA3B,CAA8BN,MAA9B,CAAlB,CAEAI,eAAe,CAAGG,kBAAkB,CAACH,eAAD,CAAkBJ,MAAlB,CAApC,CACD,CAED,cAAgCN,QAAQ,CAAC,CACvCc,QAAQ,CAAEJ,eAD6B,CAEvCK,UAAU,CAAE,CAF2B,CAAD,CAAxC,wCAAOC,QAAP,eAAiBC,WAAjB,eAIA;AACA,GAAMC,YAAW,CAAGnB,MAAM,CAACiB,QAAD,CAA1B,CAEAb,oBAAoB,CAAC,UAAM,CACzB,GAAIgB,GAAE,CAAG,IAAT,CAEA;AACA,GAAIC,SAAS,CAACf,KAAD,CAAb,CAAsB,4BACpBc,EAAE,CAAGE,WAAW,CAAC,UAAM,oEACrB,GAAIC,SAAJ,CACA,GACEJ,WAAW,CAACK,OAAZ,CAAoBR,UAApB,CAAiCT,MAAjC,GACAD,KADA,SACAA,KADA,uCACAA,KAAK,CAAEM,YADP,+CACA,qBAAqBa,MADrB,CADF,CAGE,CACAF,QAAQ,CAAG,CAAX,CACD,CALD,IAKO,4BACLA,QAAQ,CACNJ,WAAW,CAACK,OAAZ,CAAoBR,UAApB,EAAiCV,KAAjC,SAAiCA,KAAjC,yCAAiCA,KAAK,CAAEG,cAAxC,iDAAiC,uBAAuBC,WAAxD,CADF,CAED,CAED;AACA,GAAIa,QAAQ,CAAGhB,MAAX,GAAqBD,KAArB,SAAqBA,KAArB,uCAAqBA,KAAK,CAAEM,YAA5B,+CAAqB,qBAAqBa,MAA1C,CAAJ,CAAsD,0BACpDF,QAAQ,CAAG,CAAAjB,KAAK,OAAL,EAAAA,KAAK,SAAL,8BAAAA,KAAK,CAAEM,YAAP,oEAAqBa,MAArB,EAA8BlB,MAAzC,CACD,CAED,GAAMmB,SAAQ,CAAG,CACfV,UAAU,CAAEO,QADG,CAEfR,QAAQ,CAAET,KAAF,SAAEA,KAAF,uCAAEA,KAAK,CAAEM,YAAT,+CAAE,qBAAqBC,KAArB,CAA2BU,QAA3B,CAAqCA,QAAQ,CAAGhB,MAAhD,CAFK,CAAjB,CAIAW,WAAW,CAACQ,QAAD,CAAX,CACAP,WAAW,CAACK,OAAZ,CAAsBE,QAAtB,CACD,CAvBe,CAuBb,CAAApB,KAAK,OAAL,EAAAA,KAAK,SAAL,gCAAAA,KAAK,CAAEG,cAAP,wEAAuBkB,WAAvB,EAAqC,IAvBxB,CAAhB,CAwBD,CAzBD,IAyBO,IAAIrB,KAAJ,SAAIA,KAAJ,WAAIA,KAAK,CAAEM,YAAX,CAAyB,0BAC5B,GAAMc,SAAQ,CAAG,CACfV,UAAU,CAAE,CADG,CAEfD,QAAQ,CAAET,KAAF,SAAEA,KAAF,uCAAEA,KAAK,CAAEM,YAAT,+CAAE,qBAAqBC,KAArB,CAA2B,CAA3B,CAA8B,EAAIN,MAAlC,CAFK,CAAjB,CAIAmB,QAAQ,CAACX,QAAT,CAAoBD,kBAAkB,CAACY,QAAQ,CAACX,QAAV,CAAoBR,MAApB,CAAtC,CAEAW,WAAW,CAACQ,QAAD,CAAX,CACAP,WAAW,CAACK,OAAZ,CAAsBE,QAAtB,CACH,CAED,GAAIN,EAAJ,CAAQ,CACN,MAAO,kBAAMQ,cAAa,CAACR,EAAD,CAAnB,EAAP,CACD,CACF,CA3CmB,CA2CjB,CAACd,KAAD,SAACA,KAAD,iBAACA,KAAK,CAAEG,cAAR,CAAwBH,KAAxB,SAAwBA,KAAxB,iBAAwBA,KAAK,CAAEM,YAA/B,CA3CiB,CAApB,CA6CA,GAAI,CAACN,KAAL,CAAY,CACVuB,OAAO,CAACC,KAAR,CAAc,6BAAd,EACA,OACD,CAED,mBACE,YAAK,SAAS,gBAAUxB,KAAV,SAAUA,KAAV,iBAAUA,KAAK,CAAEyB,cAAjB,CAAd,8BACGd,QAAQ,CAACF,QADZ,6CACG,mBAAmBiB,GAAnB,CAAuB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACvC,GAAIC,KAAK,CAACC,OAAN,CAAcH,IAAd,CAAJ,CAAyB,CACvB,mBACE,0GACA;AAFF,CAID,CALD,IAKO,2BACL,GAAMI,eAAc,CAAGH,KAAK,EAAG5B,KAAH,SAAGA,KAAH,wCAAGA,KAAK,CAAEgC,cAAV,gDAAG,sBAAuBb,MAA1B,CAA5B,CACA,GAAIc,SAAQ,CAAG,CACbC,UAAU,CAAEP,IAAI,CAACO,UADJ,CAEbC,IAAI,CAAER,IAAI,CAACS,YAFE,CAGbC,gBAAgB,CAAEV,IAAI,CAACU,gBAHV,CAIbC,SAAS,CAAEtC,KAAF,SAAEA,KAAF,iBAAEA,KAAK,CAAEgC,cAAP,CAAsBD,cAAtB,CAJE,CAAf,CAOA,mBACE,KAAC,IAAD,kBAEME,QAFN,EACOL,KADP,CADF,CAMD,CACF,CAtBA,CADH,EADF,CA2BD,CAnGD,CAqGA;AACA,QAASpB,mBAAT,CAA4BH,eAA5B,CAA6CJ,MAA7C,CAAqD,CACnD,GAAII,eAAe,CAACc,MAAhB,CAAyBlB,MAA7B,CAAqC,CACnC,GAAMsC,aAAY,CAAGtC,MAAM,CAAGI,eAAe,CAACc,MAA9C,CACA,IAAK,GAAIqB,EAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGD,YAApB,CAAkCC,CAAC,EAAnC,CAAuC,CACrCnC,eAAe,CAACoC,IAAhB,CAAqB,CAAEL,YAAY,CAAE,GAAhB,CAArB,EACD,CACF,CACD,MAAO/B,gBAAP,CACD,CAED;AACA;AACA;AACA;AACA,GACA,QAASU,UAAT,CAAmB2B,GAAnB,CAAwB,8CACtB,GAAI,CAACA,GAAL,CAAU,MAAO,MAAP,CAEV,MACE,CAAAA,GAAG,OAAH,EAAAA,GAAG,SAAH,QAAAA,GAAG,CAAEpC,YAAL,GACA,CAAAoC,GAAG,OAAH,EAAAA,GAAG,SAAH,QAAAA,GAAG,CAAEpC,YAAL,CAAkBa,MAAlB,GAA2BuB,GAA3B,SAA2BA,GAA3B,iBAA2BA,GAAG,CAAExC,GAAhC,CADA,GAEAwC,GAFA,SAEAA,GAFA,iBAEAA,GAAG,CAAEvC,cAFL,GAGA,CAAAuC,GAAG,OAAH,EAAAA,GAAG,SAAH,6BAAAA,GAAG,CAAEvC,cAAL,kEAAqBkB,WAArB,EAAmC,CAHnC,EAIA,CAAAqB,GAAG,OAAH,EAAAA,GAAG,SAAH,8BAAAA,GAAG,CAAEvC,cAAL,oEAAqBC,WAArB,EAAmC,CALrC,CAOD,CAID,cAAeL,YAAf"}, "metadata": {}, "sourceType": "module"}