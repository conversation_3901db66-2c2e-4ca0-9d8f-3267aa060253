{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter,\n    EventSourceDriver = require('eventsource');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\n\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n\n  es.onmessage = function (e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n\n  es.onerror = function (e) {\n    debug('error', es.readyState, e); // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n\n    var reason = es.readyState !== 2 ? 'network' : 'permanent';\n\n    self._cleanup();\n\n    self._close(reason);\n  };\n}\n\ninherits(EventSourceReceiver, EventEmitter);\n\nEventSourceReceiver.prototype.abort = function () {\n  debug('abort');\n\n  this._cleanup();\n\n  this._close('user');\n};\n\nEventSourceReceiver.prototype._cleanup = function () {\n  debug('cleanup');\n  var es = this.es;\n\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\n\nEventSourceReceiver.prototype._close = function (reason) {\n  debug('close', reason);\n  var self = this; // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n\n  setTimeout(function () {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\n\nmodule.exports = EventSourceReceiver;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "EventSourceDriver", "debug", "process", "env", "NODE_ENV", "EventSourceReceiver", "url", "call", "self", "es", "onmessage", "e", "data", "emit", "decodeURI", "onerror", "readyState", "reason", "_cleanup", "_close", "prototype", "abort", "close", "setTimeout", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/receiver/eventsource.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , EventSourceDriver = require('eventsource')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\n\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function(e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function(e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = (es.readyState !== 2 ? 'network' : 'permanent');\n    self._cleanup();\n    self._close(reason);\n  };\n}\n\ninherits(EventSourceReceiver, EventEmitter);\n\nEventSourceReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nEventSourceReceiver.prototype._cleanup = function() {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\n\nEventSourceReceiver.prototype._close = function(reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function() {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\n\nmodule.exports = EventSourceReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;AAAA,IAEIC,iBAAiB,GAAGF,OAAO,CAAC,aAAD,CAF/B;;AAKA,IAAIG,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAD,CAAP,CAAiB,oCAAjB,CAAR;AACD;;AAED,SAASO,mBAAT,CAA6BC,GAA7B,EAAkC;EAChCL,KAAK,CAACK,GAAD,CAAL;EACAP,YAAY,CAACQ,IAAb,CAAkB,IAAlB;EAEA,IAAIC,IAAI,GAAG,IAAX;EACA,IAAIC,EAAE,GAAG,KAAKA,EAAL,GAAU,IAAIT,iBAAJ,CAAsBM,GAAtB,CAAnB;;EACAG,EAAE,CAACC,SAAH,GAAe,UAASC,CAAT,EAAY;IACzBV,KAAK,CAAC,SAAD,EAAYU,CAAC,CAACC,IAAd,CAAL;IACAJ,IAAI,CAACK,IAAL,CAAU,SAAV,EAAqBC,SAAS,CAACH,CAAC,CAACC,IAAH,CAA9B;EACD,CAHD;;EAIAH,EAAE,CAACM,OAAH,GAAa,UAASJ,CAAT,EAAY;IACvBV,KAAK,CAAC,OAAD,EAAUQ,EAAE,CAACO,UAAb,EAAyBL,CAAzB,CAAL,CADuB,CAEvB;IACA;;IACA,IAAIM,MAAM,GAAIR,EAAE,CAACO,UAAH,KAAkB,CAAlB,GAAsB,SAAtB,GAAkC,WAAhD;;IACAR,IAAI,CAACU,QAAL;;IACAV,IAAI,CAACW,MAAL,CAAYF,MAAZ;EACD,CAPD;AAQD;;AAEDpB,QAAQ,CAACQ,mBAAD,EAAsBN,YAAtB,CAAR;;AAEAM,mBAAmB,CAACe,SAApB,CAA8BC,KAA9B,GAAsC,YAAW;EAC/CpB,KAAK,CAAC,OAAD,CAAL;;EACA,KAAKiB,QAAL;;EACA,KAAKC,MAAL,CAAY,MAAZ;AACD,CAJD;;AAMAd,mBAAmB,CAACe,SAApB,CAA8BF,QAA9B,GAAyC,YAAW;EAClDjB,KAAK,CAAC,SAAD,CAAL;EACA,IAAIQ,EAAE,GAAG,KAAKA,EAAd;;EACA,IAAIA,EAAJ,EAAQ;IACNA,EAAE,CAACC,SAAH,GAAeD,EAAE,CAACM,OAAH,GAAa,IAA5B;IACAN,EAAE,CAACa,KAAH;IACA,KAAKb,EAAL,GAAU,IAAV;EACD;AACF,CARD;;AAUAJ,mBAAmB,CAACe,SAApB,CAA8BD,MAA9B,GAAuC,UAASF,MAAT,EAAiB;EACtDhB,KAAK,CAAC,OAAD,EAAUgB,MAAV,CAAL;EACA,IAAIT,IAAI,GAAG,IAAX,CAFsD,CAGtD;EACA;EACA;;EACAe,UAAU,CAAC,YAAW;IACpBf,IAAI,CAACK,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyBI,MAAzB;IACAT,IAAI,CAACgB,kBAAL;EACD,CAHS,EAGP,GAHO,CAAV;AAID,CAVD;;AAYAC,MAAM,CAACC,OAAP,GAAiBrB,mBAAjB"}, "metadata": {}, "sourceType": "script"}