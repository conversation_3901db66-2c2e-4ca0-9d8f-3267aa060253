{"ast": null, "code": "'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}", "map": {"version": 3, "names": ["bind", "fn", "thisArg", "wrap", "apply", "arguments"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/bind.js"], "sourcesContent": ["'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}\n"], "mappings": "AAAA;;AAEA,eAAe,SAASA,IAAT,CAAcC,EAAd,EAAkBC,OAAlB,EAA2B;EACxC,OAAO,SAASC,IAAT,GAAgB;IACrB,OAAOF,EAAE,CAACG,KAAH,CAASF,OAAT,EAAkBG,SAAlB,CAAP;EACD,CAFD;AAGD"}, "metadata": {}, "sourceType": "module"}