{"ast": null, "code": "import { useRef } from 'react';\nimport isEqual from 'lodash/isEqual';\n\nvar depsEqual = function depsEqual(aDeps, bDeps) {\n  if (aDeps === void 0) {\n    aDeps = [];\n  }\n\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n\n  return isEqual(aDeps, bDeps);\n};\n\nexport var createDeepCompareEffect = function createDeepCompareEffect(hook) {\n  return function (effect, deps) {\n    var ref = useRef();\n    var signalRef = useRef(0);\n\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      ref.current = deps;\n      signalRef.current += 1;\n    }\n\n    hook(effect, [signalRef.current]);\n  };\n};", "map": {"version": 3, "names": ["useRef", "isEqual", "depsEqual", "aDeps", "bDeps", "createDeepCompareEffect", "hook", "effect", "deps", "ref", "signalRef", "undefined", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/createDeepCompareEffect/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport isEqual from 'lodash/isEqual';\nvar depsEqual = function depsEqual(aDeps, bDeps) {\n  if (aDeps === void 0) {\n    aDeps = [];\n  }\n  if (bDeps === void 0) {\n    bDeps = [];\n  }\n  return isEqual(aDeps, bDeps);\n};\nexport var createDeepCompareEffect = function createDeepCompareEffect(hook) {\n  return function (effect, deps) {\n    var ref = useRef();\n    var signalRef = useRef(0);\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      ref.current = deps;\n      signalRef.current += 1;\n    }\n    hook(effect, [signalRef.current]);\n  };\n};"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,OAAP,MAAoB,gBAApB;;AACA,IAAIC,SAAS,GAAG,SAASA,SAAT,CAAmBC,KAAnB,EAA0BC,KAA1B,EAAiC;EAC/C,IAAID,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,IAAIC,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,OAAOH,OAAO,CAACE,KAAD,EAAQC,KAAR,CAAd;AACD,CARD;;AASA,OAAO,IAAIC,uBAAuB,GAAG,SAASA,uBAAT,CAAiCC,IAAjC,EAAuC;EAC1E,OAAO,UAAUC,MAAV,EAAkBC,IAAlB,EAAwB;IAC7B,IAAIC,GAAG,GAAGT,MAAM,EAAhB;IACA,IAAIU,SAAS,GAAGV,MAAM,CAAC,CAAD,CAAtB;;IACA,IAAIQ,IAAI,KAAKG,SAAT,IAAsB,CAACT,SAAS,CAACM,IAAD,EAAOC,GAAG,CAACG,OAAX,CAApC,EAAyD;MACvDH,GAAG,CAACG,OAAJ,GAAcJ,IAAd;MACAE,SAAS,CAACE,OAAV,IAAqB,CAArB;IACD;;IACDN,IAAI,CAACC,MAAD,EAAS,CAACG,SAAS,CAACE,OAAX,CAAT,CAAJ;EACD,CARD;AASD,CAVM"}, "metadata": {}, "sourceType": "module"}