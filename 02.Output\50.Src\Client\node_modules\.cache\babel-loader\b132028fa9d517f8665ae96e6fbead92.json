{"ast": null, "code": "'use strict';\n\nvar URL = require('url-parse');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\n\nmodule.exports = {\n  getOrigin: function getOrigin(url) {\n    if (!url) {\n      return null;\n    }\n\n    var p = new URL(url);\n\n    if (p.protocol === 'file:') {\n      return null;\n    }\n\n    var port = p.port;\n\n    if (!port) {\n      port = p.protocol === 'https:' ? '443' : '80';\n    }\n\n    return p.protocol + '//' + p.hostname + ':' + port;\n  },\n  isOriginEqual: function isOriginEqual(a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  },\n  isSchemeEqual: function isSchemeEqual(a, b) {\n    return a.split(':')[0] === b.split(':')[0];\n  },\n  addPath: function addPath(url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  },\n  addQuery: function addQuery(url, q) {\n    return url + (url.indexOf('?') === -1 ? '?' + q : '&' + q);\n  },\n  isLoopbackAddr: function isLoopbackAddr(addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};", "map": {"version": 3, "names": ["URL", "require", "debug", "process", "env", "NODE_ENV", "module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "url", "p", "protocol", "port", "hostname", "isOriginEqual", "a", "b", "res", "isSchemeEqual", "split", "addPath", "path", "qs", "<PERSON><PERSON><PERSON><PERSON>", "q", "indexOf", "isLoopbackAddr", "addr", "test"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/url.js"], "sourcesContent": ["'use strict';\n\nvar URL = require('url-parse');\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\n\nmodule.exports = {\n  getOrigin: function(url) {\n    if (!url) {\n      return null;\n    }\n\n    var p = new URL(url);\n    if (p.protocol === 'file:') {\n      return null;\n    }\n\n    var port = p.port;\n    if (!port) {\n      port = (p.protocol === 'https:') ? '443' : '80';\n    }\n\n    return p.protocol + '//' + p.hostname + ':' + port;\n  }\n\n, isOriginEqual: function(a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  }\n\n, isSchemeEqual: function(a, b) {\n    return (a.split(':')[0] === b.split(':')[0]);\n  }\n\n, addPath: function (url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  }\n\n, addQuery: function (url, q) {\n    return url + (url.indexOf('?') === -1 ? ('?' + q) : ('&' + q));\n  }\n\n, isLoopbackAddr: function (addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,GAAG,GAAGC,OAAO,CAAC,WAAD,CAAjB;;AAEA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGD,OAAO,CAAC,OAAD,CAAP,CAAiB,yBAAjB,CAAR;AACD;;AAEDK,MAAM,CAACC,OAAP,GAAiB;EACfC,SAAS,EAAE,mBAASC,GAAT,EAAc;IACvB,IAAI,CAACA,GAAL,EAAU;MACR,OAAO,IAAP;IACD;;IAED,IAAIC,CAAC,GAAG,IAAIV,GAAJ,CAAQS,GAAR,CAAR;;IACA,IAAIC,CAAC,CAACC,QAAF,KAAe,OAAnB,EAA4B;MAC1B,OAAO,IAAP;IACD;;IAED,IAAIC,IAAI,GAAGF,CAAC,CAACE,IAAb;;IACA,IAAI,CAACA,IAAL,EAAW;MACTA,IAAI,GAAIF,CAAC,CAACC,QAAF,KAAe,QAAhB,GAA4B,KAA5B,GAAoC,IAA3C;IACD;;IAED,OAAOD,CAAC,CAACC,QAAF,GAAa,IAAb,GAAoBD,CAAC,CAACG,QAAtB,GAAiC,GAAjC,GAAuCD,IAA9C;EACD,CAjBc;EAmBfE,aAAa,EAAE,uBAASC,CAAT,EAAYC,CAAZ,EAAe;IAC5B,IAAIC,GAAG,GAAG,KAAKT,SAAL,CAAeO,CAAf,MAAsB,KAAKP,SAAL,CAAeQ,CAAf,CAAhC;IACAd,KAAK,CAAC,MAAD,EAASa,CAAT,EAAYC,CAAZ,EAAeC,GAAf,CAAL;IACA,OAAOA,GAAP;EACD,CAvBc;EAyBfC,aAAa,EAAE,uBAASH,CAAT,EAAYC,CAAZ,EAAe;IAC5B,OAAQD,CAAC,CAACI,KAAF,CAAQ,GAAR,EAAa,CAAb,MAAoBH,CAAC,CAACG,KAAF,CAAQ,GAAR,EAAa,CAAb,CAA5B;EACD,CA3Bc;EA6BfC,OAAO,EAAE,iBAAUX,GAAV,EAAeY,IAAf,EAAqB;IAC5B,IAAIC,EAAE,GAAGb,GAAG,CAACU,KAAJ,CAAU,GAAV,CAAT;IACA,OAAOG,EAAE,CAAC,CAAD,CAAF,GAAQD,IAAR,IAAgBC,EAAE,CAAC,CAAD,CAAF,GAAQ,MAAMA,EAAE,CAAC,CAAD,CAAhB,GAAsB,EAAtC,CAAP;EACD,CAhCc;EAkCfC,QAAQ,EAAE,kBAAUd,GAAV,EAAee,CAAf,EAAkB;IAC1B,OAAOf,GAAG,IAAIA,GAAG,CAACgB,OAAJ,CAAY,GAAZ,MAAqB,CAAC,CAAtB,GAA2B,MAAMD,CAAjC,GAAuC,MAAMA,CAAjD,CAAV;EACD,CApCc;EAsCfE,cAAc,EAAE,wBAAUC,IAAV,EAAgB;IAC9B,OAAO,mDAAmDC,IAAnD,CAAwDD,IAAxD,KAAiE,YAAYC,IAAZ,CAAiBD,IAAjB,CAAxE;EACD;AAxCc,CAAjB"}, "metadata": {}, "sourceType": "script"}