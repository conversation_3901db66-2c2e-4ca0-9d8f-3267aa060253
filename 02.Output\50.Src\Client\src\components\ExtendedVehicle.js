import React from 'react';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource } from '../utils/Util.js';
import Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';

const MAX_ROW = 50;
const GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';
const GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';

/**
 * 拡張車両コンテンツ（50行表示）<br>
 * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 * @module ExtendedVehicle
 * @component
 * @param {*} props
 * @return {*} 表示データ
 */
const ExtendedVehicle = (props) => {
    if (!isValidSource(props)) return;

    let showDelopy = props.is_deployment === 1;

    let totalRowCounter = 0;

    let targetArray = [];

    if (!props.title_name)
        return;
    
    //最大MaxRowのデータを取り出す
    for (const item of props.title_name) {
        
        if (!item.car_name) 
            return;

        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行
        let subRowCounter = item.car_name.length + 1;

        if (totalRowCounter + subRowCounter <= MAX_ROW) {
            let newObj = {
                display_text: item.display_text,
                text_color: item.text_color,
                background_color: item.background_color,
                car_name: item.car_name,
                town_name: item.town_name,
                disaster_type: item.disaster_type,
                avm_dynamic_state: item.avm_dynamic_state,
                deployment: item.deployment,
                lighting_setting: item.lighting_setting,
            };

            targetArray.push(newObj);
            totalRowCounter = totalRowCounter + subRowCounter;
        }
    }

    let nextStartRow = 1;
    return (
        <>
            {isValidSource(props) && (
                <div className="grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.31rem] leading-[1] gap-x-[2.25rem] gap-y-[0.4rem]">
                    {targetArray.map((item, index) => {
                        let startRow = nextStartRow; //現在のStart
                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算

                        return (
                            <ExtendedStation
                                key={index}
                                {...item}
                                index={index}
                                showDelopy={showDelopy}
                                startRow={startRow}
                            />
                        );
                    })}
                </div>
            )}
        </>
    );
};

/**
 * 署所(部隊)名または車両種別単位のデータを表示
 * @param {*} props
 * @returns 表示データ
 */
const ExtendedStation = (props) => {
    let gridCol;
    let subTitleSpan = 'col-span-full';
    if (props.showDelopy) {
        gridCol = 'grid-cols-extended-vehicle-deploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;
    } else {
        gridCol = 'grid-cols-extended-vehicle-nodeploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;
    }
    const subTitleProp = getCellFace(
        props,
        `${subTitleSpan} flex flex-col items-center`
    );

    return (
        <>
            <div className={`grid ${gridCol}`}>
                <CellBox {...subTitleProp}>
                    {props.display_text && <span>{props.display_text}</span>}
                </CellBox>
            </div>
            {props.car_name.map((item, index) => {
                return <ExtendedVehicleDetailRow key={index} {...props} index={index} />;
            })}
        </>
    );
};

/**
 * 車両詳細行の表示
 * @param {*} props
 * @returns 表示データ
 */
const ExtendedVehicleDetailRow = (props) => {
    let showInfoSeperator0 = { display_text: '', text_color: '', background_color: '' };
    let showInfoSeperator1 = { display_text: '', text_color: '', background_color: '' };
    let showInfoSeperator2 = { display_text: '', text_color: '', background_color: '' };
    let showInfoSeperator3 = { display_text: '', text_color: '', background_color: '' };
    let showInfoSeperator4 = { display_text: '', text_color: '', background_color: '' };
    let showInfoSeperator5 = { display_text: '', text_color: '', background_color: '' };

    let showInfoDeployment = { display_text: '', text_color: '', background_color: '' };
    let showInfoCarName = { display_text: '', text_color: '', background_color: '' };
    let showInfoTownName = { display_text: '', text_color: '', background_color: '' };
    let showInfoDisasterType = { display_text: '', text_color: '', background_color: '' };
    let showInfoAvmDynamicState = { display_text: '', text_color: '', background_color: '' };

    if (props.deployment && props.deployment[props.index]) {
        showInfoDeployment = props.deployment[props.index];
    }

    if (props.car_name && props.car_name[props.index]) {
        showInfoCarName = props.car_name[props.index];
    }

    if (props.town_name && props.town_name[props.index]) {
        showInfoTownName = props.town_name[props.index];
    }

    if (props.disaster_type && props.disaster_type[props.index]) {
        showInfoDisasterType = props.disaster_type[props.index];
    }

    if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {
        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];
    }

    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ
    let baseObj = props.car_name[props.index];
    if (!baseObj) { baseObj = props.town_name[props.index] }
    if (!baseObj) { baseObj = props.disaster_type[props.index] }
    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }
    if (!baseObj) { baseObj = props.deployment[props.index] }

    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);
    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);
    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);
    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);
    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);

    showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);
    showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);
    showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);
    showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);
    showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);
    showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);

    let gridCol;
    let showBlock = [];
    if (props.showDelopy) {
        gridCol = 'grid-cols-extended-vehicle-deploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;

        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分
            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });
        }
        showBlock.push({
            showInfo: showInfoDeployment,
            className: 'col-span-1 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-4',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-9',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-16',
        });
        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-19',
        });

        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分
            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });
        }

    } else {
        gridCol = 'grid-cols-extended-vehicle-nodeploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;

        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分
            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });
        }
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-7',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-14',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-17',
        });

        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分
            showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        }

    }

    return (
        <>
            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}
            {props.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={props.lighting_setting[props.index]}
                    />
                </div>
            )}
            {!props.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={props.lighting_setting[props.index]}
                    />
                </div>
            )}
        </>
    );
};



export default ExtendedVehicle;
