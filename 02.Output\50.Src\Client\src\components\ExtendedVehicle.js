import React from 'react';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource } from '../utils/Util.js';
import Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';

const MAX_ROW = 50;
const GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';
const GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';

/**
 * 拡張車両コンテンツ（50行表示）<br>
 * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 * @module ExtendedVehicle
 * @component
 * @param {*} props
 * @return {*} 表示データ
 */
const ExtendedVehicle = (props) => {
    if (!isValidSource(props)) return;

    let showDelopy = props.is_deployment === 1;

    let totalRowCounter = 0;

    let targetArray = [];

    if (!props.title_name)
        return;
    
    //最大MaxRowのデータを取り出す
    for (const item of props.title_name) {
        
        if (!item.car_name) 
            return;

        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行
        let subRowCounter = item.car_name.length + 1;

        if (totalRowCounter + subRowCounter <= MAX_ROW) {
            targetArray.push(item);
            totalRowCounter = totalRowCounter + subRowCounter;
        } else if (totalRowCounter <= MAX_ROW - 2) {
            subRowCounter = MAX_ROW - totalRowCounter;

            // この - 1がSubTitleの行
            const subItemCounter = subRowCounter - 1;

            let newObj = { ...item };
            newObj.deployment = newObj.deployment?.slice(0, subItemCounter);
            newObj.car_name = newObj.car_name?.slice(0, subItemCounter);
            newObj.town_name = newObj.town_name?.slice(0, subItemCounter);
            newObj.disaster_type = newObj.disaster_type?.slice(0, subItemCounter);
            newObj.avm_dynamic_state = newObj.avm_dynamic_state?.slice(
                0,
                subItemCounter
            );
            newObj.lighting_setting = newObj.lighting_setting?.slice(
                0,
                subItemCounter
            );

            targetArray.push(newObj);
            totalRowCounter = totalRowCounter + subRowCounter;
        }
    }

    let nextStartRow = 1;
    return (
        <>
            {isValidSource(props) && (
                <div className="grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.31rem] leading-[1] gap-x-[2.25rem] gap-y-[0.4rem]">
                    {targetArray.map((item, index) => {
                        let startRow = nextStartRow; //現在のStart
                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算

                        return (
                            <ExtendedStation
                                key={index}
                                {...item}
                                index={index}
                                showDelopy={showDelopy}
                                startRow={startRow}
                            />
                        );
                    })}
                </div>
            )}
        </>
    );
};

/**
 * 署所(部隊)名または車両種別単位のデータを表示
 * @param {*} props
 * @returns 表示データ
 */
const ExtendedStation = (props) => {
    let gridCol;
    let subTitleSpan = 'col-span-full';
    if (props.showDelopy) {
        gridCol = 'grid-cols-extended-vehicle-deploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;
    } else {
        gridCol = 'grid-cols-extended-vehicle-nodeploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;
    }
    const subTitleProp = getCellFace(
        props,
        `${subTitleSpan} flex flex-col items-center`
    );

    return (
        <>
            <div className={`grid ${gridCol}`}>
                <CellBox {...subTitleProp}>
                    {props.display_text && <span>{props.display_text}</span>}
                </CellBox>
            </div>
            {props.car_name.map((item, index) => {
                return <ExtendedVehicleDetailRow key={index} {...props} index={index} />;
            })}
        </>
    );
};

/**
 * 車両詳細行の表示
 * @param {*} props
 * @returns 表示データ
 */
const ExtendedVehicleDetailRow = (props) => {
    let showInfoDeployment,
        showInfoCarName,
        showInfoTownName,
        showInfoDisasterType,
        showInfoAvmDynamicState;
    if (props.showDelopy && props.deployment && props.lighting_setting) {
        showInfoDeployment = props.deployment[props.index];
    }
    if (props.car_name && props.lighting_setting) {
        showInfoCarName = props.car_name[props.index];
    }
    if (props.town_name && props.lighting_setting) {
        showInfoTownName = props.town_name[props.index];
    }
    if (props.disaster_type && props.lighting_setting) {
        showInfoDisasterType = props.disaster_type[props.index];
    }
    if (props.avm_dynamic_state && props.lighting_setting) {
        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];
    }

    let status = 1;
    if (props.lighting_setting && props.lighting_setting[props.index]) {
        status = props.lighting_setting[props.index].lighting_status;
    }

    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ
    let baseObj = props.car_name[props.index];
    if (!baseObj) { baseObj = props.town_name[props.index] }
    if (!baseObj) { baseObj = props.disaster_type[props.index] }
    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }
    if (!baseObj) { baseObj = props.deployment[props.index] }

    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);
    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);
    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);
    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);
    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);

    let showInfoSeperator0 = { ...showInfoDeployment };
    let showInfoSeperator1 = { ...showInfoDeployment };
    let showInfoSeperator2 = { ...showInfoCarName };
    let showInfoSeperator3 = { ...showInfoTownName };
    let showInfoSeperator4 = { ...showInfoDisasterType };
    let showInfoSeperator5 = { ...showInfoAvmDynamicState };
    showInfoSeperator0.display_text = ' ';
    showInfoSeperator1.display_text = ' ';
    showInfoSeperator2.display_text = ' ';
    showInfoSeperator3.display_text = ' ';
    showInfoSeperator4.display_text = ' ';
    showInfoSeperator5.display_text = ' ';
    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする
    if (status !== 3) {
        showInfoSeperator0.background_color = undefined;
        showInfoSeperator1.background_color = undefined;
        showInfoSeperator2.background_color = undefined;
        showInfoSeperator3.background_color = undefined;
        showInfoSeperator4.background_color = undefined;
        showInfoSeperator5.background_color = undefined;
    }

    let gridCol;
    let showBlock = [];
    if (props.showDelopy) {
        gridCol = 'grid-cols-extended-vehicle-deploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;

        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分
            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });
        }
        showBlock.push({
            showInfo: showInfoDeployment,
            className: 'col-span-1 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-4',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-9',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-16',
        });
        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-19',
        });

        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分
            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });
        }

    } else {
        gridCol = 'grid-cols-extended-vehicle-nodeploy';
        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;

        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-7',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-14',
        });
        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-17',
        });

        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分
            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });
        }

    }

    return (
        <>
            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}
            {props.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={props.lighting_setting[props.index]}
                    />
                </div>
            )}
            {!props.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={props.lighting_setting[props.index]}
                    />
                </div>
            )}
        </>
    );
};



export default ExtendedVehicle;
