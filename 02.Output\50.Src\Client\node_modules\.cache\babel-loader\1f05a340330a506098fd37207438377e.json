{"ast": null, "code": "/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function (value) {\n    return func(value);\n  };\n}\n\nmodule.exports = baseUnary;", "map": {"version": 3, "names": ["baseUnary", "func", "value", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseUnary.js"], "sourcesContent": ["/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\n\nmodule.exports = baseUnary;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;EACvB,OAAO,UAASC,KAAT,EAAgB;IACrB,OAAOD,IAAI,CAACC,KAAD,CAAX;EACD,CAFD;AAGD;;AAEDC,MAAM,CAACC,OAAP,GAAiBJ,SAAjB"}, "metadata": {}, "sourceType": "script"}