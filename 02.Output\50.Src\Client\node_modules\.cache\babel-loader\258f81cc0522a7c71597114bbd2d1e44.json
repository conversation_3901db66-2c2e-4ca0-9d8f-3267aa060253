{"ast": null, "code": "var getScrollTop = function getScrollTop(el) {\n  if (el === document || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n\n  return el.scrollTop;\n};\n\nvar getScrollHeight = function getScrollHeight(el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\n\nvar getClientHeight = function getClientHeight(el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\n\nexport { getScrollTop, getScrollHeight, getClientHeight };", "map": {"version": 3, "names": ["getScrollTop", "el", "document", "body", "Math", "max", "window", "pageYOffset", "documentElement", "scrollTop", "getScrollHeight", "scrollHeight", "getClientHeight", "clientHeight"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/rect.js"], "sourcesContent": ["var getScrollTop = function getScrollTop(el) {\n  if (el === document || el === document.body) {\n    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);\n  }\n  return el.scrollTop;\n};\nvar getScrollHeight = function getScrollHeight(el) {\n  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);\n};\nvar getClientHeight = function getClientHeight(el) {\n  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);\n};\nexport { getScrollTop, getScrollHeight, getClientHeight };"], "mappings": "AAAA,IAAIA,YAAY,GAAG,SAASA,YAAT,CAAsBC,EAAtB,EAA0B;EAC3C,IAAIA,EAAE,KAAKC,QAAP,IAAmBD,EAAE,KAAKC,QAAQ,CAACC,IAAvC,EAA6C;IAC3C,OAAOC,IAAI,CAACC,GAAL,CAASC,MAAM,CAACC,WAAhB,EAA6BL,QAAQ,CAACM,eAAT,CAAyBC,SAAtD,EAAiEP,QAAQ,CAACC,IAAT,CAAcM,SAA/E,CAAP;EACD;;EACD,OAAOR,EAAE,CAACQ,SAAV;AACD,CALD;;AAMA,IAAIC,eAAe,GAAG,SAASA,eAAT,CAAyBT,EAAzB,EAA6B;EACjD,OAAOA,EAAE,CAACU,YAAH,IAAmBP,IAAI,CAACC,GAAL,CAASH,QAAQ,CAACM,eAAT,CAAyBG,YAAlC,EAAgDT,QAAQ,CAACC,IAAT,CAAcQ,YAA9D,CAA1B;AACD,CAFD;;AAGA,IAAIC,eAAe,GAAG,SAASA,eAAT,CAAyBX,EAAzB,EAA6B;EACjD,OAAOA,EAAE,CAACY,YAAH,IAAmBT,IAAI,CAACC,GAAL,CAASH,QAAQ,CAACM,eAAT,CAAyBK,YAAlC,EAAgDX,QAAQ,CAACC,IAAT,CAAcU,YAA9D,CAA1B;AACD,CAFD;;AAGA,SAASb,YAAT,EAAuBU,eAAvB,EAAwCE,eAAxC"}, "metadata": {}, "sourceType": "module"}