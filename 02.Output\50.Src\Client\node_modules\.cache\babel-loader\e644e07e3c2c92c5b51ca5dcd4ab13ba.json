{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useMemo, useState } from 'react';\n\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n\n  var _a = __read(useState(defaultValue), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n\n    var toggle = function toggle() {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n\n    var set = function set(value) {\n      return setState(value);\n    };\n\n    var setLeft = function setLeft() {\n      return setState(defaultValue);\n    };\n\n    var setRight = function setRight() {\n      return setState(reverseValueOrigin);\n    };\n\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    }; // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\n\nexport default useToggle;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useMemo", "useState", "useToggle", "defaultValue", "reverseValue", "_a", "state", "setState", "actions", "reverseValueOrigin", "undefined", "toggle", "s", "set", "setLeft", "setRight"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useToggle/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useMemo, useState } from 'react';\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useState(defaultValue), 2),\n    state = _a[0],\n    setState = _a[1];\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n    var toggle = function toggle() {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n    var set = function set(value) {\n      return setState(value);\n    };\n    var setLeft = function setLeft() {\n      return setState(defaultValue);\n    };\n    var setRight = function setRight() {\n      return setState(reverseValueOrigin);\n    };\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    };\n    // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\nexport default useToggle;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,OAAT,EAAkBC,QAAlB,QAAkC,OAAlC;;AACA,SAASC,SAAT,CAAmBC,YAAnB,EAAiCC,YAAjC,EAA+C;EAC7C,IAAID,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAG,KAAf;EACD;;EACD,IAAIE,EAAE,GAAGrB,MAAM,CAACiB,QAAQ,CAACE,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEG,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,OAAO,GAAGR,OAAO,CAAC,YAAY;IAChC,IAAIS,kBAAkB,GAAGL,YAAY,KAAKM,SAAjB,GAA6B,CAACP,YAA9B,GAA6CC,YAAtE;;IACA,IAAIO,MAAM,GAAG,SAASA,MAAT,GAAkB;MAC7B,OAAOJ,QAAQ,CAAC,UAAUK,CAAV,EAAa;QAC3B,OAAOA,CAAC,KAAKT,YAAN,GAAqBM,kBAArB,GAA0CN,YAAjD;MACD,CAFc,CAAf;IAGD,CAJD;;IAKA,IAAIU,GAAG,GAAG,SAASA,GAAT,CAAaf,KAAb,EAAoB;MAC5B,OAAOS,QAAQ,CAACT,KAAD,CAAf;IACD,CAFD;;IAGA,IAAIgB,OAAO,GAAG,SAASA,OAAT,GAAmB;MAC/B,OAAOP,QAAQ,CAACJ,YAAD,CAAf;IACD,CAFD;;IAGA,IAAIY,QAAQ,GAAG,SAASA,QAAT,GAAoB;MACjC,OAAOR,QAAQ,CAACE,kBAAD,CAAf;IACD,CAFD;;IAGA,OAAO;MACLE,MAAM,EAAEA,MADH;MAELE,GAAG,EAAEA,GAFA;MAGLC,OAAO,EAAEA,OAHJ;MAILC,QAAQ,EAAEA;IAJL,CAAP,CAhBgC,CAsBhC;IACA;EACD,CAxBoB,EAwBlB,EAxBkB,CAArB;EAyBA,OAAO,CAACT,KAAD,EAAQE,OAAR,CAAP;AACD;;AACD,eAAeN,SAAf"}, "metadata": {}, "sourceType": "module"}