{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";var _templateObject,_templateObject2;import React,{useRef,useEffect,useState}from'react';import'./TextScroll.css';import styled from'styled-components';/**\r\n * 文字列を自動的にスクロールできるコンポーネント\r\n *\r\n * @module TextScroll\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";function TextScroll(props){var defaultState={contentWidth:0,boxWidth:0,duration:props.duration};var _useState=useState(defaultState),_useState2=_slicedToArray(_useState,2),state=_useState2[0],setState=_useState2[1];var _useState3=useState(false),_useState4=_slicedToArray(_useState3,2),isScroll=_useState4[0],setIsScroll=_useState4[1];var ref=useRef(null);useEffect(function(){var scroll=false;var _ref=ref.current,offsetWidth=_ref.offsetWidth,parentElement=_ref.parentElement;setState({contentWidth:offsetWidth,boxWidth:parentElement.offsetWidth,duration:Math.ceil(offsetWidth/parentElement.offsetWidth*10000)//Durationは、実際のContent長さと外側の箱の横幅の倍数の2倍\n});if(parentElement&&offsetWidth>parentElement.offsetWidth){scroll=true;}setIsScroll(scroll);},[props.content]);if(isScroll){var animationName=\"marquee_\".concat(state.contentWidth);return/*#__PURE__*/_jsx(\"div\",{className:\"marquee_box\",children:/*#__PURE__*/_jsx(Text,{ref:ref,animationName:animationName,duration:state.duration,contentWidth:state.contentWidth,boxWidth:state.boxWidth,color:props.color,background_color:props.background_color,children:props.content})});}else{return/*#__PURE__*/_jsx(\"div\",{className:\"marquee_box\",children:/*#__PURE__*/_jsx(StaticText,{ref:ref,color:props.color,background_color:props.background_color,children:props.content})});}}var Text=styled.p(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  color: \",\";\\n  background-color: \",\";\\n  position: relative;\\n  will-change: transform;\\n  animation: \",\" \",\"ms\\n    linear infinite running both normal;\\n\\n  @keyframes \",\" {\\n    0%,5% {\\n      transform: translateX(0px);\\n    }\\n    100% {\\n      transform: translateX(\\n        -\",\"px\\n      );\\n    }\\n  }\\n\"])),function(props){return props.color;},function(props){return props.background_color;},function(props){return props.animationName;},function(props){return props.duration;},function(props){return props.animationName;},function(props){return props.contentWidth;});var StaticText=styled.p(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  position: relative;\\n  color: \",\";\\n  background-color: \",\";\\n\"])),function(props){return props.color;},function(props){return props.background_color;});TextScroll.defaultProps={content:'',duration:3};export default TextScroll;", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "styled", "TextScroll", "props", "defaultState", "contentWidth", "boxWidth", "duration", "state", "setState", "isScroll", "setIsScroll", "ref", "scroll", "current", "offsetWidth", "parentElement", "Math", "ceil", "content", "animationName", "color", "background_color", "Text", "p", "StaticText", "defaultProps"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/TextScroll.tsx"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\nimport './TextScroll.css';\r\nimport styled from 'styled-components';\r\n\r\n/**\r\n * 文字列を自動的にスクロールできるコンポーネント\r\n *\r\n * @module TextScroll\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\r\nfunction TextScroll(props: {\r\n  content: string;\r\n  color: string;\r\n  background_color: string;\r\n  duration: number;\r\n}) {\r\n  const defaultState = {\r\n    contentWidth: 0,\r\n    boxWidth: 0,\r\n    duration: props.duration,\r\n  };\r\n\r\n  const [state, setState] = useState(defaultState);\r\n  const [isScroll, setIsScroll] = useState(false);\r\n  let ref = useRef<HTMLParagraphElement>(null);\r\n\r\n  useEffect(() => {\r\n    let scroll = false;\r\n    const { offsetWidth, parentElement } = ref.current as HTMLParagraphElement;\r\n    setState({\r\n      contentWidth: offsetWidth,\r\n      boxWidth: parentElement!.offsetWidth,\r\n      duration: Math.ceil((offsetWidth) / parentElement!.offsetWidth * 10000), //Durationは、実際のContent長さと外側の箱の横幅の倍数の2倍\r\n    });\r\n\r\n    if (parentElement && offsetWidth > parentElement!.offsetWidth) {\r\n      scroll = true;\r\n    }\r\n    setIsScroll(scroll);\r\n  }, [props.content]);\r\n\r\n  if (isScroll) {\r\n    const animationName = `marquee_${state.contentWidth}`;\r\n    return (\r\n      <div className=\"marquee_box\">\r\n        <Text\r\n          ref={ref}\r\n          animationName={animationName}\r\n          duration={state.duration}\r\n          contentWidth={state.contentWidth}\r\n          boxWidth={state.boxWidth}\r\n          color={props.color}\r\n          background_color={props.background_color}\r\n        >\r\n          {props.content}\r\n        </Text>\r\n      </div>\r\n    );\r\n  } else {\r\n    return (\r\n      <div className=\"marquee_box\">\r\n        <StaticText\r\n          ref={ref}\r\n          color={props.color}\r\n          background_color={props.background_color}\r\n        >\r\n          {props.content}\r\n        </StaticText>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\ninterface pProps {\r\n  readonly animationName: string;\r\n  readonly duration: number;\r\n  readonly contentWidth: number;\r\n  readonly boxWidth: number;\r\n  readonly color: string;\r\n  readonly background_color: string;\r\n}\r\n\r\nconst Text = styled.p<pProps>`\r\n  color: ${(props) => props.color};\r\n  background-color: ${(props) => props.background_color};\r\n  position: relative;\r\n  will-change: transform;\r\n  animation: ${(props) => props.animationName} ${(props) => props.duration}ms\r\n    linear infinite running both normal;\r\n\r\n  @keyframes ${(props) => props.animationName} {\r\n    0%,5% {\r\n      transform: translateX(0px);\r\n    }\r\n    100% {\r\n      transform: translateX(\r\n        -${(props) => props.contentWidth}px\r\n      );\r\n    }\r\n  }\r\n`;\r\n\r\ninterface pStaticProps {\r\n  readonly color: string;\r\n  readonly background_color: string;\r\n}\r\n\r\nconst StaticText = styled.p<pStaticProps>`\r\n  position: relative;\r\n  color: ${(props) => props.color};\r\n  background-color: ${(props) => props.background_color};\r\n`;\r\n\r\nTextScroll.defaultProps = {\r\n  content: '',\r\n  duration: 3,\r\n};\r\n\r\nexport default TextScroll;\r\n"], "mappings": "yUAAA,MAAOA,MAAP,EAAgBC,MAAhB,CAAwBC,SAAxB,CAAmCC,QAAnC,KAAmD,OAAnD,CACA,MAAO,kBAAP,CACA,MAAOC,OAAP,KAAmB,mBAAnB,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,2CACA,QAASC,WAAT,CAAoBC,KAApB,CAKG,CACD,GAAMC,aAAY,CAAG,CACnBC,YAAY,CAAE,CADK,CAEnBC,QAAQ,CAAE,CAFS,CAGnBC,QAAQ,CAAEJ,KAAK,CAACI,QAHG,CAArB,CAMA,cAA0BP,QAAQ,CAACI,YAAD,CAAlC,wCAAOI,KAAP,eAAcC,QAAd,eACA,eAAgCT,QAAQ,CAAC,KAAD,CAAxC,yCAAOU,QAAP,eAAiBC,WAAjB,eACA,GAAIC,IAAG,CAAGd,MAAM,CAAuB,IAAvB,CAAhB,CAEAC,SAAS,CAAC,UAAM,CACd,GAAIc,OAAM,CAAG,KAAb,CACA,SAAuCD,GAAG,CAACE,OAA3C,CAAQC,WAAR,MAAQA,WAAR,CAAqBC,aAArB,MAAqBA,aAArB,CACAP,QAAQ,CAAC,CACPJ,YAAY,CAAEU,WADP,CAEPT,QAAQ,CAAEU,aAAa,CAAED,WAFlB,CAGPR,QAAQ,CAAEU,IAAI,CAACC,IAAL,CAAWH,WAAD,CAAgBC,aAAa,CAAED,WAA/B,CAA6C,KAAvD,CAA+D;AAHlE,CAAD,CAAR,CAMA,GAAIC,aAAa,EAAID,WAAW,CAAGC,aAAa,CAAED,WAAlD,CAA+D,CAC7DF,MAAM,CAAG,IAAT,CACD,CACDF,WAAW,CAACE,MAAD,CAAX,CACD,CAbQ,CAaN,CAACV,KAAK,CAACgB,OAAP,CAbM,CAAT,CAeA,GAAIT,QAAJ,CAAc,CACZ,GAAMU,cAAa,mBAAcZ,KAAK,CAACH,YAApB,CAAnB,CACA,mBACE,YAAK,SAAS,CAAC,aAAf,uBACE,KAAC,IAAD,EACE,GAAG,CAAEO,GADP,CAEE,aAAa,CAAEQ,aAFjB,CAGE,QAAQ,CAAEZ,KAAK,CAACD,QAHlB,CAIE,YAAY,CAAEC,KAAK,CAACH,YAJtB,CAKE,QAAQ,CAAEG,KAAK,CAACF,QALlB,CAME,KAAK,CAAEH,KAAK,CAACkB,KANf,CAOE,gBAAgB,CAAElB,KAAK,CAACmB,gBAP1B,UASGnB,KAAK,CAACgB,OATT,EADF,EADF,CAeD,CAjBD,IAiBO,CACL,mBACE,YAAK,SAAS,CAAC,aAAf,uBACE,KAAC,UAAD,EACE,GAAG,CAAEP,GADP,CAEE,KAAK,CAAET,KAAK,CAACkB,KAFf,CAGE,gBAAgB,CAAElB,KAAK,CAACmB,gBAH1B,UAKGnB,KAAK,CAACgB,OALT,EADF,EADF,CAWD,CACF,CAWD,GAAMI,KAAI,CAAGtB,MAAM,CAACuB,CAAV,6XACC,SAACrB,KAAD,QAAWA,MAAK,CAACkB,KAAjB,EADD,CAEY,SAAClB,KAAD,QAAWA,MAAK,CAACmB,gBAAjB,EAFZ,CAKK,SAACnB,KAAD,QAAWA,MAAK,CAACiB,aAAjB,EALL,CAKuC,SAACjB,KAAD,QAAWA,MAAK,CAACI,QAAjB,EALvC,CAQK,SAACJ,KAAD,QAAWA,MAAK,CAACiB,aAAjB,EARL,CAcC,SAACjB,KAAD,QAAWA,MAAK,CAACE,YAAjB,EAdD,CAAV,CAyBA,GAAMoB,WAAU,CAAGxB,MAAM,CAACuB,CAAV,qIAEL,SAACrB,KAAD,QAAWA,MAAK,CAACkB,KAAjB,EAFK,CAGM,SAAClB,KAAD,QAAWA,MAAK,CAACmB,gBAAjB,EAHN,CAAhB,CAMApB,UAAU,CAACwB,YAAX,CAA0B,CACxBP,OAAO,CAAE,EADe,CAExBZ,QAAQ,CAAE,CAFc,CAA1B,CAKA,cAAeL,WAAf"}, "metadata": {}, "sourceType": "module"}