{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\n\nvar useFusionTable = function useFusionTable(service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\n\nexport default useFusionTable;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "useAntdTable", "fieldAdapter", "resultAdapter", "useFusionTable", "service", "options", "ret", "form", "field", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useFusionTable/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\nvar useFusionTable = function useFusionTable(service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\nexport default useFusionTable;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,OAAOO,YAAP,MAAyB,iBAAzB;AACA,SAASC,YAAT,EAAuBC,aAAvB,QAA4C,iBAA5C;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,OAAxB,EAAiCC,OAAjC,EAA0C;EAC7D,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,GAAG,GAAGN,YAAY,CAACI,OAAD,EAAUlB,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKmB,OAAL,CAAT,EAAwB;IAC9DE,IAAI,EAAEF,OAAO,CAACG,KAAR,GAAgBP,YAAY,CAACI,OAAO,CAACG,KAAT,CAA5B,GAA8CC;EADU,CAAxB,CAAlB,CAAtB;EAGA,OAAOP,aAAa,CAACI,GAAD,CAApB;AACD,CARD;;AASA,eAAeH,cAAf"}, "metadata": {}, "sourceType": "module"}