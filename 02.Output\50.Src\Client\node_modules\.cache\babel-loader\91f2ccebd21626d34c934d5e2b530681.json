{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import{getCellFace,isValidSource,getHexColor}from'../utils/Util.js';/**\r\n * 簡易事案コンテンツ(1-4サイズ)<br>\r\n * propsは、「3.7簡易事案コンテンツ(1-4サイズ)情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module CaseQuarter\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var CaseQuarter=function CaseQuarter(props){var MAX_ROW=4;return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&props.case.map(function(item,index){//・1コンテンツあたり最大4件の情報を表示可能。\nif(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(CaseQuarterRow,{item:item,index:index},index);})});};// 簡易事案コンテンツ(1-4サイズ)の一事案\nvar CaseQuarterRow=function CaseQuarterRow(props){var row1Cell1Props,row1Cell2Props,row1Cell3Props,row1Props;var row2Cell1Props,row2Cell2Props;var data=props.item;var obj=data.disaster_class;row1Props={backgroundColor:getHexColor(obj.background_color)};row1Cell1Props=getCellFace(obj.disaster_type,'col-span-4');row1Cell2Props=getCellFace(obj.case_no,'col-span-7 col-start-6');row1Cell3Props=getCellFace(obj.fire_station_name,'col-span-4 col-start-14');row2Cell1Props=getCellFace(data.awareness_time,'col-span-8 col-start-2 w-fit');row2Cell2Props=getCellFace(data===null||data===void 0?void 0:data.town_name,'col-span-10 col-start-11 w-fit');return/*#__PURE__*/_jsxs(\"div\",{className:\"text-6xl leading-[1]\",children:[/*#__PURE__*/_jsxs(\"div\",{style:row1Props,className:\"border-transparent border-x-[1rem] grid grid-cols-19 leading-none auto-cols-fr\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell2Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell3Props))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-21 text-6xl auto-cols-fr my-[2.35rem]\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell2Props))]})]});};export default CaseQuarter;", "map": {"version": 3, "names": ["React", "Cell", "getCellFace", "isValidSource", "getHexColor", "CaseQuarter", "props", "MAX_ROW", "case", "map", "item", "index", "undefined", "CaseQuarterRow", "row1Cell1Props", "row1Cell2Props", "row1Cell3Props", "row1Props", "row2Cell1Props", "row2Cell2Props", "data", "obj", "disaster_class", "backgroundColor", "background_color", "disaster_type", "case_no", "fire_station_name", "awareness_time", "town_name"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/CaseQuarter.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';\r\n\r\n/**\r\n * 簡易事案コンテンツ(1-4サイズ)<br>\r\n * propsは、「3.7簡易事案コンテンツ(1-4サイズ)情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module CaseQuarter\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst CaseQuarter = (props) => {\r\n  const MAX_ROW = 4;\r\n  return (\r\n    <>\r\n      {isValidSource(props) &&\r\n        props.case.map((item, index) => {\r\n          //・1コンテンツあたり最大4件の情報を表示可能。\r\n          if (index >= MAX_ROW) return undefined;\r\n\r\n          return <CaseQuarterRow key={index} item={item} index={index} />;\r\n        })}\r\n    </>\r\n  );\r\n};\r\n\r\n// 簡易事案コンテンツ(1-4サイズ)の一事案\r\nconst CaseQuarterRow = (props) => {\r\n  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;\r\n  let row2Cell1Props, row2Cell2Props;\r\n\r\n  const data = props.item;\r\n  let obj = data.disaster_class;\r\n  row1Props = { backgroundColor: getHexColor(obj.background_color) };\r\n  row1Cell1Props = getCellFace(obj.disaster_type, 'col-span-4');\r\n  row1Cell2Props = getCellFace(obj.case_no, 'col-span-7 col-start-6');\r\n  row1Cell3Props = getCellFace(\r\n    obj.fire_station_name,\r\n    'col-span-4 col-start-14'\r\n  );\r\n\r\n  row2Cell1Props = getCellFace(\r\n    data.awareness_time,\r\n    'col-span-8 col-start-2 w-fit'\r\n  );\r\n  row2Cell2Props = getCellFace(\r\n    data?.town_name,\r\n    'col-span-10 col-start-11 w-fit'\r\n  );\r\n  \r\n  return (\r\n    <div className=\"text-6xl leading-[1]\">\r\n      <div\r\n        style={row1Props}\r\n        className=\"border-transparent border-x-[1rem] grid grid-cols-19 leading-none auto-cols-fr\"\r\n      >\r\n        <Cell {...row1Cell1Props} />\r\n        <Cell {...row1Cell2Props} />\r\n        <Cell {...row1Cell3Props} />\r\n      </div>\r\n      <div className=\"grid grid-cols-21 text-6xl auto-cols-fr my-[2.35rem]\">\r\n        <Cell {...row2Cell1Props} />\r\n        <Cell {...row2Cell2Props} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CaseQuarter;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,YAAW,CAAG,QAAdA,YAAc,CAACC,KAAD,CAAW,CAC7B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,yBACGJ,aAAa,CAACG,KAAD,CAAb,EACCA,KAAK,CAACE,IAAN,CAAWC,GAAX,CAAe,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC9B;AACA,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,cAAD,EAA4B,IAAI,CAAEF,IAAlC,CAAwC,KAAK,CAAEC,KAA/C,EAAqBA,KAArB,CAAP,CACD,CALD,CAFJ,EADF,CAWD,CAbD,CAeA;AACA,GAAME,eAAc,CAAG,QAAjBA,eAAiB,CAACP,KAAD,CAAW,CAChC,GAAIQ,eAAJ,CAAoBC,cAApB,CAAoCC,cAApC,CAAoDC,SAApD,CACA,GAAIC,eAAJ,CAAoBC,cAApB,CAEA,GAAMC,KAAI,CAAGd,KAAK,CAACI,IAAnB,CACA,GAAIW,IAAG,CAAGD,IAAI,CAACE,cAAf,CACAL,SAAS,CAAG,CAAEM,eAAe,CAAEnB,WAAW,CAACiB,GAAG,CAACG,gBAAL,CAA9B,CAAZ,CACAV,cAAc,CAAGZ,WAAW,CAACmB,GAAG,CAACI,aAAL,CAAoB,YAApB,CAA5B,CACAV,cAAc,CAAGb,WAAW,CAACmB,GAAG,CAACK,OAAL,CAAc,wBAAd,CAA5B,CACAV,cAAc,CAAGd,WAAW,CAC1BmB,GAAG,CAACM,iBADsB,CAE1B,yBAF0B,CAA5B,CAKAT,cAAc,CAAGhB,WAAW,CAC1BkB,IAAI,CAACQ,cADqB,CAE1B,8BAF0B,CAA5B,CAIAT,cAAc,CAAGjB,WAAW,CAC1BkB,IAD0B,SAC1BA,IAD0B,iBAC1BA,IAAI,CAAES,SADoB,CAE1B,gCAF0B,CAA5B,CAKA,mBACE,aAAK,SAAS,CAAC,sBAAf,wBACE,aACE,KAAK,CAAEZ,SADT,CAEE,SAAS,CAAC,gFAFZ,wBAIE,KAAC,IAAD,kBAAUH,cAAV,EAJF,cAKE,KAAC,IAAD,kBAAUC,cAAV,EALF,cAME,KAAC,IAAD,kBAAUC,cAAV,EANF,GADF,cASE,aAAK,SAAS,CAAC,sDAAf,wBACE,KAAC,IAAD,kBAAUE,cAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,cAAV,EAFF,GATF,GADF,CAgBD,CAvCD,CAyCA,cAAed,YAAf"}, "metadata": {}, "sourceType": "module"}