{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar cache = new Map();\n\nvar setCache = function setCache(key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n\n  var timer = undefined;\n\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache[\"delete\"](key);\n    }, cacheTime);\n  }\n\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\n\nvar getCache = function getCache(key) {\n  return cache.get(key);\n};\n\nvar clearCache = function clearCache(key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache[\"delete\"](cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\n\nexport { getCache, setCache, clearCache };", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "cache", "Map", "setCache", "key", "cacheTime", "cachedData", "currentCache", "get", "timer", "clearTimeout", "undefined", "setTimeout", "set", "getCache", "clearCache", "cacheKeys", "Array", "isArray", "for<PERSON>ach", "cache<PERSON>ey", "clear"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/cache.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar cache = new Map();\nvar setCache = function setCache(key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n  var timer = undefined;\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache[\"delete\"](key);\n    }, cacheTime);\n  }\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\nvar getCache = function getCache(key) {\n  return cache.get(key);\n};\nvar clearCache = function clearCache(key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache[\"delete\"](cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\nexport { getCache, setCache, clearCache };"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,KAAK,GAAG,IAAIC,GAAJ,EAAZ;;AACA,IAAIC,QAAQ,GAAG,SAASA,QAAT,CAAkBC,GAAlB,EAAuBC,SAAvB,EAAkCC,UAAlC,EAA8C;EAC3D,IAAIC,YAAY,GAAGN,KAAK,CAACO,GAAN,CAAUJ,GAAV,CAAnB;;EACA,IAAIG,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAACE,KAA7E,EAAoF;IAClFC,YAAY,CAACH,YAAY,CAACE,KAAd,CAAZ;EACD;;EACD,IAAIA,KAAK,GAAGE,SAAZ;;EACA,IAAIN,SAAS,GAAG,CAAC,CAAjB,EAAoB;IAClB;IACAI,KAAK,GAAGG,UAAU,CAAC,YAAY;MAC7BX,KAAK,CAAC,QAAD,CAAL,CAAgBG,GAAhB;IACD,CAFiB,EAEfC,SAFe,CAAlB;EAGD;;EACDJ,KAAK,CAACY,GAAN,CAAUT,GAAV,EAAejB,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKmB,UAAL,CAAT,EAA2B;IAChDG,KAAK,EAAEA;EADyC,CAA3B,CAAvB;AAGD,CAfD;;AAgBA,IAAIK,QAAQ,GAAG,SAASA,QAAT,CAAkBV,GAAlB,EAAuB;EACpC,OAAOH,KAAK,CAACO,GAAN,CAAUJ,GAAV,CAAP;AACD,CAFD;;AAGA,IAAIW,UAAU,GAAG,SAASA,UAAT,CAAoBX,GAApB,EAAyB;EACxC,IAAIA,GAAJ,EAAS;IACP,IAAIY,SAAS,GAAGC,KAAK,CAACC,OAAN,CAAcd,GAAd,IAAqBA,GAArB,GAA2B,CAACA,GAAD,CAA3C;IACAY,SAAS,CAACG,OAAV,CAAkB,UAAUC,QAAV,EAAoB;MACpC,OAAOnB,KAAK,CAAC,QAAD,CAAL,CAAgBmB,QAAhB,CAAP;IACD,CAFD;EAGD,CALD,MAKO;IACLnB,KAAK,CAACoB,KAAN;EACD;AACF,CATD;;AAUA,SAASP,QAAT,EAAmBX,QAAnB,EAA6BY,UAA7B"}, "metadata": {}, "sourceType": "module"}