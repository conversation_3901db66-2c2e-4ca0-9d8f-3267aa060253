{"ast": null, "code": "'use strict';\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\n\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n      return port !== 80;\n\n    case 'https':\n    case 'wss':\n      return port !== 443;\n\n    case 'ftp':\n      return port !== 21;\n\n    case 'gopher':\n      return port !== 70;\n\n    case 'file':\n      return false;\n  }\n\n  return port !== 0;\n};", "map": {"version": 3, "names": ["module", "exports", "required", "port", "protocol", "split"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/requires-port/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n    return port !== 80;\n\n    case 'https':\n    case 'wss':\n    return port !== 443;\n\n    case 'ftp':\n    return port !== 21;\n\n    case 'gopher':\n    return port !== 70;\n\n    case 'file':\n    return false;\n  }\n\n  return port !== 0;\n};\n"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAA,MAAM,CAACC,OAAP,GAAiB,SAASC,QAAT,CAAkBC,IAAlB,EAAwBC,QAAxB,EAAkC;EACjDA,QAAQ,GAAGA,QAAQ,CAACC,KAAT,CAAe,GAAf,EAAoB,CAApB,CAAX;EACAF,IAAI,GAAG,CAACA,IAAR;EAEA,IAAI,CAACA,IAAL,EAAW,OAAO,KAAP;;EAEX,QAAQC,QAAR;IACE,KAAK,MAAL;IACA,KAAK,IAAL;MACA,OAAOD,IAAI,KAAK,EAAhB;;IAEA,KAAK,OAAL;IACA,KAAK,KAAL;MACA,OAAOA,IAAI,KAAK,GAAhB;;IAEA,KAAK,KAAL;MACA,OAAOA,IAAI,KAAK,EAAhB;;IAEA,KAAK,QAAL;MACA,OAAOA,IAAI,KAAK,EAAhB;;IAEA,KAAK,MAAL;MACA,OAAO,KAAP;EAhBF;;EAmBA,OAAOA,IAAI,KAAK,CAAhB;AACD,CA1BD"}, "metadata": {}, "sourceType": "script"}