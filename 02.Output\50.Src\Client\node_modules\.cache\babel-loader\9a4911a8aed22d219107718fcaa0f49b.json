{"ast": null, "code": "import _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\n\n/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport var StompHeaders = /*#__PURE__*/_createClass(function StompHeaders() {\n  _classCallCheck(this, StompHeaders);\n});", "map": {"version": 3, "mappings": ";;;AAAA;;;;;;;;;AASA,WAAaA,YAAb;EAAA;AAAA", "names": ["StompHeaders"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\stomp-headers.ts"], "sourcesContent": ["/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {\n  [key: string]: string;\n}\n"]}, "metadata": {}, "sourceType": "module"}