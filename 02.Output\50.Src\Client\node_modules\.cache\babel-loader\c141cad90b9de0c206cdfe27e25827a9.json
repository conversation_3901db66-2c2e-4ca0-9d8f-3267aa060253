{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState, useRef, useCallback } from 'react';\n\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var stateRef = useRef(state);\n  stateRef.current = state;\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\n\nexport default useGetState;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useRef", "useCallback", "useGetState", "initialState", "_a", "state", "setState", "stateRef", "current", "getState"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useGetState/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState, useRef, useCallback } from 'react';\nfunction useGetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  stateRef.current = state;\n  var getState = useCallback(function () {\n    return stateRef.current;\n  }, []);\n  return [state, setState, getState];\n}\nexport default useGetState;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,EAAmBC,MAAnB,EAA2BC,WAA3B,QAA8C,OAA9C;;AACA,SAASC,WAAT,CAAqBC,YAArB,EAAmC;EACjC,IAAIC,EAAE,GAAGrB,MAAM,CAACgB,QAAQ,CAACI,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEE,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,QAAQ,GAAGP,MAAM,CAACK,KAAD,CAArB;EACAE,QAAQ,CAACC,OAAT,GAAmBH,KAAnB;EACA,IAAII,QAAQ,GAAGR,WAAW,CAAC,YAAY;IACrC,OAAOM,QAAQ,CAACC,OAAhB;EACD,CAFyB,EAEvB,EAFuB,CAA1B;EAGA,OAAO,CAACH,KAAD,EAAQC,QAAR,EAAkBG,QAAlB,CAAP;AACD;;AACD,eAAeP,WAAf"}, "metadata": {}, "sourceType": "module"}