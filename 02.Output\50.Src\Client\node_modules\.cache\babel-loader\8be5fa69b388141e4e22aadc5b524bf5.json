{"ast": null, "code": "'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80',\n  protocol: 'http:',\n  host: 'localhost',\n  port: 80,\n  href: 'http://localhost/',\n  hash: ''\n};", "map": {"version": 3, "names": ["module", "exports", "global", "location", "origin", "protocol", "host", "port", "href", "hash"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/location.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80'\n, protocol: 'http:'\n, host: 'localhost'\n, port: 80\n, href: 'http://localhost/'\n, hash: ''\n};\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiBC,MAAM,CAACC,QAAP,IAAmB;EAClCC,MAAM,EAAE,qBAD0B;EAElCC,QAAQ,EAAE,OAFwB;EAGlCC,IAAI,EAAE,WAH4B;EAIlCC,IAAI,EAAE,EAJ4B;EAKlCC,IAAI,EAAE,mBAL4B;EAMlCC,IAAI,EAAE;AAN4B,CAApC"}, "metadata": {}, "sourceType": "script"}