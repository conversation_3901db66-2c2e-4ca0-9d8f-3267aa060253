{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport Fetch from './Fetch';\n\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  if (plugins === void 0) {\n    plugins = [];\n  }\n\n  var _a = options.manual,\n      manual = _a === void 0 ? false : _a,\n      rest = __rest(options, [\"manual\"]);\n\n  var fetchOptions = __assign({\n    manual: manual\n  }, rest);\n\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions; // run all plugins hooks\n\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || []; // @ts-ignore\n\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\n\nexport default useRequestImplement;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "useCreation", "useLatest", "useMemoizedFn", "useMount", "useUnmount", "useUpdate", "<PERSON>tch", "useRequestImplement", "service", "options", "plugins", "_a", "manual", "rest", "fetchOptions", "serviceRef", "update", "fetchInstance", "initState", "map", "onInit", "filter", "Boolean", "pluginImpls", "params", "state", "defaultParams", "run", "cancel", "loading", "data", "bind", "refresh", "refreshAsync", "runAsync", "mutate"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/useRequestImplement.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport useCreation from '../../useCreation';\nimport useLatest from '../../useLatest';\nimport useMemoizedFn from '../../useMemoizedFn';\nimport useMount from '../../useMount';\nimport useUnmount from '../../useUnmount';\nimport useUpdate from '../../useUpdate';\nimport Fetch from './Fetch';\nfunction useRequestImplement(service, options, plugins) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  var _a = options.manual,\n    manual = _a === void 0 ? false : _a,\n    rest = __rest(options, [\"manual\"]);\n  var fetchOptions = __assign({\n    manual: manual\n  }, rest);\n  var serviceRef = useLatest(service);\n  var update = useUpdate();\n  var fetchInstance = useCreation(function () {\n    var initState = plugins.map(function (p) {\n      var _a;\n      return (_a = p === null || p === void 0 ? void 0 : p.onInit) === null || _a === void 0 ? void 0 : _a.call(p, fetchOptions);\n    }).filter(Boolean);\n    return new Fetch(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState), false)));\n  }, []);\n  fetchInstance.options = fetchOptions;\n  // run all plugins hooks\n  fetchInstance.pluginImpls = plugins.map(function (p) {\n    return p(fetchInstance, fetchOptions);\n  });\n  useMount(function () {\n    if (!manual) {\n      // useCachePlugin can set fetchInstance.state.params from cache when init\n      var params = fetchInstance.state.params || options.defaultParams || [];\n      // @ts-ignore\n      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));\n    }\n  });\n  useUnmount(function () {\n    fetchInstance.cancel();\n  });\n  return {\n    loading: fetchInstance.state.loading,\n    data: fetchInstance.state.data,\n    error: fetchInstance.state.error,\n    params: fetchInstance.state.params || [],\n    cancel: useMemoizedFn(fetchInstance.cancel.bind(fetchInstance)),\n    refresh: useMemoizedFn(fetchInstance.refresh.bind(fetchInstance)),\n    refreshAsync: useMemoizedFn(fetchInstance.refreshAsync.bind(fetchInstance)),\n    run: useMemoizedFn(fetchInstance.run.bind(fetchInstance)),\n    runAsync: useMemoizedFn(fetchInstance.runAsync.bind(fetchInstance)),\n    mutate: useMemoizedFn(fetchInstance.mutate.bind(fetchInstance))\n  };\n}\nexport default useRequestImplement;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUV,CAAV,EAAaW,CAAb,EAAgB;EAClD,IAAIZ,CAAC,GAAG,EAAR;;EACA,KAAK,IAAIM,CAAT,IAAcL,CAAd,EAAiB;IACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,KAA8CM,CAAC,CAACC,OAAF,CAAUP,CAAV,IAAe,CAAjE,EAAoEN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;EACrE;;EACD,IAAIL,CAAC,IAAI,IAAL,IAAa,OAAOH,MAAM,CAACgB,qBAAd,KAAwC,UAAzD,EAAqE,KAAK,IAAIZ,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAGR,MAAM,CAACgB,qBAAP,CAA6Bb,CAA7B,CAApB,EAAqDC,CAAC,GAAGI,CAAC,CAACD,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IAC3I,IAAIU,CAAC,CAACC,OAAF,CAAUP,CAAC,CAACJ,CAAD,CAAX,IAAkB,CAAlB,IAAuBJ,MAAM,CAACS,SAAP,CAAiBQ,oBAAjB,CAAsCN,IAAtC,CAA2CR,CAA3C,EAA8CK,CAAC,CAACJ,CAAD,CAA/C,CAA3B,EAAgFF,CAAC,CAACM,CAAC,CAACJ,CAAD,CAAF,CAAD,GAAUD,CAAC,CAACK,CAAC,CAACJ,CAAD,CAAF,CAAX;EACjF;EACD,OAAOF,CAAP;AACD,CATD;;AAUA,IAAIgB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAad,CAAb,EAAgB;EAClD,IAAIe,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIf,CAAC,GAAGgB,CAAC,CAACT,IAAF,CAAOQ,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEV,CAHF;;EAIA,IAAI;IACF,OAAO,CAACT,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACkB,CAAC,GAAGnB,CAAC,CAACqB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDF,EAAE,CAACG,IAAH,CAAQJ,CAAC,CAACK,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdf,CAAC,GAAG;MACFe,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIN,CAAC,IAAI,CAACA,CAAC,CAACG,IAAR,KAAiBN,CAAC,GAAGhB,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCgB,CAAC,CAACT,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIU,CAAJ,EAAO,MAAMA,CAAC,CAACe,KAAR;IACR;EACF;;EACD,OAAOL,EAAP;AACD,CAvBD;;AAwBA,IAAIM,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAI3B,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIH,CAAC,GAAG,CAAR,EAAW8B,CAAC,GAAGF,IAAI,CAACzB,MAApB,EAA4BiB,EAAjC,EAAqCpB,CAAC,GAAG8B,CAAzC,EAA4C9B,CAAC,EAA7C,EAAiD;IACnF,IAAIoB,EAAE,IAAI,EAAEpB,CAAC,IAAI4B,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACR,EAAL,EAASA,EAAE,GAAGW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,EAAiC,CAAjC,EAAoC5B,CAApC,CAAL;MACToB,EAAE,CAACpB,CAAD,CAAF,GAAQ4B,IAAI,CAAC5B,CAAD,CAAZ;IACD;EACF;EACD,OAAO2B,EAAE,CAACM,MAAH,CAAUb,EAAE,IAAIW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,OAAOM,WAAP,MAAwB,mBAAxB;AACA,OAAOC,SAAP,MAAsB,iBAAtB;AACA,OAAOC,aAAP,MAA0B,qBAA1B;AACA,OAAOC,QAAP,MAAqB,gBAArB;AACA,OAAOC,UAAP,MAAuB,kBAAvB;AACA,OAAOC,SAAP,MAAsB,iBAAtB;AACA,OAAOC,KAAP,MAAkB,SAAlB;;AACA,SAASC,mBAAT,CAA6BC,OAA7B,EAAsCC,OAAtC,EAA+CC,OAA/C,EAAwD;EACtD,IAAID,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,EAAE,GAAGF,OAAO,CAACG,MAAjB;EAAA,IACEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EADnC;EAAA,IAEEE,IAAI,GAAGtC,MAAM,CAACkC,OAAD,EAAU,CAAC,QAAD,CAAV,CAFf;;EAGA,IAAIK,YAAY,GAAGrD,QAAQ,CAAC;IAC1BmD,MAAM,EAAEA;EADkB,CAAD,EAExBC,IAFwB,CAA3B;;EAGA,IAAIE,UAAU,GAAGd,SAAS,CAACO,OAAD,CAA1B;EACA,IAAIQ,MAAM,GAAGX,SAAS,EAAtB;EACA,IAAIY,aAAa,GAAGjB,WAAW,CAAC,YAAY;IAC1C,IAAIkB,SAAS,GAAGR,OAAO,CAACS,GAAR,CAAY,UAAUjD,CAAV,EAAa;MACvC,IAAIyC,EAAJ;;MACA,OAAO,CAACA,EAAE,GAAGzC,CAAC,KAAK,IAAN,IAAcA,CAAC,KAAK,KAAK,CAAzB,GAA6B,KAAK,CAAlC,GAAsCA,CAAC,CAACkD,MAA9C,MAA0D,IAA1D,IAAkET,EAAE,KAAK,KAAK,CAA9E,GAAkF,KAAK,CAAvF,GAA2FA,EAAE,CAACtC,IAAH,CAAQH,CAAR,EAAW4C,YAAX,CAAlG;IACD,CAHe,EAGbO,MAHa,CAGNC,OAHM,CAAhB;IAIA,OAAO,IAAIhB,KAAJ,CAAUS,UAAV,EAAsBD,YAAtB,EAAoCE,MAApC,EAA4CtD,MAAM,CAACC,MAAP,CAAcW,KAAd,CAAoBZ,MAApB,EAA4B8B,aAAa,CAAC,CAAC,EAAD,CAAD,EAAOZ,MAAM,CAACsC,SAAD,CAAb,EAA0B,KAA1B,CAAzC,CAA5C,CAAP;EACD,CAN8B,EAM5B,EAN4B,CAA/B;EAOAD,aAAa,CAACR,OAAd,GAAwBK,YAAxB,CAtBsD,CAuBtD;;EACAG,aAAa,CAACM,WAAd,GAA4Bb,OAAO,CAACS,GAAR,CAAY,UAAUjD,CAAV,EAAa;IACnD,OAAOA,CAAC,CAAC+C,aAAD,EAAgBH,YAAhB,CAAR;EACD,CAF2B,CAA5B;EAGAX,QAAQ,CAAC,YAAY;IACnB,IAAI,CAACS,MAAL,EAAa;MACX;MACA,IAAIY,MAAM,GAAGP,aAAa,CAACQ,KAAd,CAAoBD,MAApB,IAA8Bf,OAAO,CAACiB,aAAtC,IAAuD,EAApE,CAFW,CAGX;;MACAT,aAAa,CAACU,GAAd,CAAkBrD,KAAlB,CAAwB2C,aAAxB,EAAuCzB,aAAa,CAAC,EAAD,EAAKZ,MAAM,CAAC4C,MAAD,CAAX,EAAqB,KAArB,CAApD;IACD;EACF,CAPO,CAAR;EAQApB,UAAU,CAAC,YAAY;IACrBa,aAAa,CAACW,MAAd;EACD,CAFS,CAAV;EAGA,OAAO;IACLC,OAAO,EAAEZ,aAAa,CAACQ,KAAd,CAAoBI,OADxB;IAELC,IAAI,EAAEb,aAAa,CAACQ,KAAd,CAAoBK,IAFrB;IAGLvC,KAAK,EAAE0B,aAAa,CAACQ,KAAd,CAAoBlC,KAHtB;IAILiC,MAAM,EAAEP,aAAa,CAACQ,KAAd,CAAoBD,MAApB,IAA8B,EAJjC;IAKLI,MAAM,EAAE1B,aAAa,CAACe,aAAa,CAACW,MAAd,CAAqBG,IAArB,CAA0Bd,aAA1B,CAAD,CALhB;IAMLe,OAAO,EAAE9B,aAAa,CAACe,aAAa,CAACe,OAAd,CAAsBD,IAAtB,CAA2Bd,aAA3B,CAAD,CANjB;IAOLgB,YAAY,EAAE/B,aAAa,CAACe,aAAa,CAACgB,YAAd,CAA2BF,IAA3B,CAAgCd,aAAhC,CAAD,CAPtB;IAQLU,GAAG,EAAEzB,aAAa,CAACe,aAAa,CAACU,GAAd,CAAkBI,IAAlB,CAAuBd,aAAvB,CAAD,CARb;IASLiB,QAAQ,EAAEhC,aAAa,CAACe,aAAa,CAACiB,QAAd,CAAuBH,IAAvB,CAA4Bd,aAA5B,CAAD,CATlB;IAULkB,MAAM,EAAEjC,aAAa,CAACe,aAAa,CAACkB,MAAd,CAAqBJ,IAArB,CAA0Bd,aAA1B,CAAD;EAVhB,CAAP;AAYD;;AACD,eAAeV,mBAAf"}, "metadata": {}, "sourceType": "module"}