{"ast": null, "code": "import { useEffect, useRef } from 'react';\n\nvar diffTwoDeps = function diffTwoDeps(deps1, deps2) {\n  //Let's do a reference equality check on 2 dependency list.\n  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  //As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_ele, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_ele, idx) {\n    return idx;\n  }) : [];\n};\n\nvar useTrackedEffect = function useTrackedEffect(effect, deps) {\n  var previousDepsRef = useRef();\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\n\nexport default useTrackedEffect;", "map": {"version": 3, "names": ["useEffect", "useRef", "diffTwoDeps", "deps1", "deps2", "map", "_ele", "idx", "Object", "is", "filter", "ele", "useTrackedEffect", "effect", "deps", "previousDepsRef", "changes", "current", "previousDeps"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useTrackedEffect/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nvar diffTwoDeps = function diffTwoDeps(deps1, deps2) {\n  //Let's do a reference equality check on 2 dependency list.\n  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  //As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_ele, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_ele, idx) {\n    return idx;\n  }) : [];\n};\nvar useTrackedEffect = function useTrackedEffect(effect, deps) {\n  var previousDepsRef = useRef();\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\nexport default useTrackedEffect;"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;;AACA,IAAIC,WAAW,GAAG,SAASA,WAAT,CAAqBC,KAArB,EAA4BC,KAA5B,EAAmC;EACnD;EACA;EACA;EACA,OAAOD,KAAK,GAAGA,KAAK,CAACE,GAAN,CAAU,UAAUC,IAAV,EAAgBC,GAAhB,EAAqB;IAC5C,OAAO,CAACC,MAAM,CAACC,EAAP,CAAUN,KAAK,CAACI,GAAD,CAAf,EAAsBH,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACG,GAAD,CAAzE,CAAD,GAAmFA,GAAnF,GAAyF,CAAC,CAAjG;EACD,CAFc,EAEZG,MAFY,CAEL,UAAUC,GAAV,EAAe;IACvB,OAAOA,GAAG,IAAI,CAAd;EACD,CAJc,CAAH,GAIPP,KAAK,GAAGA,KAAK,CAACC,GAAN,CAAU,UAAUC,IAAV,EAAgBC,GAAhB,EAAqB;IAC1C,OAAOA,GAAP;EACD,CAFY,CAAH,GAEL,EANL;AAOD,CAXD;;AAYA,IAAIK,gBAAgB,GAAG,SAASA,gBAAT,CAA0BC,MAA1B,EAAkCC,IAAlC,EAAwC;EAC7D,IAAIC,eAAe,GAAGd,MAAM,EAA5B;EACAD,SAAS,CAAC,YAAY;IACpB,IAAIgB,OAAO,GAAGd,WAAW,CAACa,eAAe,CAACE,OAAjB,EAA0BH,IAA1B,CAAzB;IACA,IAAII,YAAY,GAAGH,eAAe,CAACE,OAAnC;IACAF,eAAe,CAACE,OAAhB,GAA0BH,IAA1B;IACA,OAAOD,MAAM,CAACG,OAAD,EAAUE,YAAV,EAAwBJ,IAAxB,CAAb;EACD,CALQ,EAKNA,IALM,CAAT;AAMD,CARD;;AASA,eAAeF,gBAAf"}, "metadata": {}, "sourceType": "module"}