{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\nexport default platform.isStandardBrowserEnv ? // Standard browser envs support document.cookie\nfunction standardBrowserEnv() {\n  return {\n    write: function write(name, value, expires, path, domain, secure) {\n      const cookie = [];\n      cookie.push(name + '=' + encodeURIComponent(value));\n\n      if (utils.isNumber(expires)) {\n        cookie.push('expires=' + new Date(expires).toGMTString());\n      }\n\n      if (utils.isString(path)) {\n        cookie.push('path=' + path);\n      }\n\n      if (utils.isString(domain)) {\n        cookie.push('domain=' + domain);\n      }\n\n      if (secure === true) {\n        cookie.push('secure');\n      }\n\n      document.cookie = cookie.join('; ');\n    },\n    read: function read(name) {\n      const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return match ? decodeURIComponent(match[3]) : null;\n    },\n    remove: function remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  };\n}() : // Non standard browser env (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n  return {\n    write: function write() {},\n    read: function read() {\n      return null;\n    },\n    remove: function remove() {}\n  };\n}();", "map": {"version": 3, "names": ["utils", "platform", "isStandardBrowserEnv", "standardBrowserEnv", "write", "name", "value", "expires", "path", "domain", "secure", "cookie", "push", "encodeURIComponent", "isNumber", "Date", "toGMTString", "isString", "document", "join", "read", "match", "RegExp", "decodeURIComponent", "remove", "now", "nonStandardBrowserEnv"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/cookies.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.isStandardBrowserEnv ?\n\n// Standard browser envs support document.cookie\n  (function standardBrowserEnv() {\n    return {\n      write: function write(name, value, expires, path, domain, secure) {\n        const cookie = [];\n        cookie.push(name + '=' + encodeURIComponent(value));\n\n        if (utils.isNumber(expires)) {\n          cookie.push('expires=' + new Date(expires).toGMTString());\n        }\n\n        if (utils.isString(path)) {\n          cookie.push('path=' + path);\n        }\n\n        if (utils.isString(domain)) {\n          cookie.push('domain=' + domain);\n        }\n\n        if (secure === true) {\n          cookie.push('secure');\n        }\n\n        document.cookie = cookie.join('; ');\n      },\n\n      read: function read(name) {\n        const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n        return (match ? decodeURIComponent(match[3]) : null);\n      },\n\n      remove: function remove(name) {\n        this.write(name, '', Date.now() - 86400000);\n      }\n    };\n  })() :\n\n// Non standard browser env (web workers, react-native) lack needed support.\n  (function nonStandardBrowserEnv() {\n    return {\n      write: function write() {},\n      read: function read() { return null; },\n      remove: function remove() {}\n    };\n  })();\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AAEA,eAAeA,QAAQ,CAACC,oBAAT,GAEf;AACG,SAASC,kBAAT,GAA8B;EAC7B,OAAO;IACLC,KAAK,EAAE,SAASA,KAAT,CAAeC,IAAf,EAAqBC,KAArB,EAA4BC,OAA5B,EAAqCC,IAArC,EAA2CC,MAA3C,EAAmDC,MAAnD,EAA2D;MAChE,MAAMC,MAAM,GAAG,EAAf;MACAA,MAAM,CAACC,IAAP,CAAYP,IAAI,GAAG,GAAP,GAAaQ,kBAAkB,CAACP,KAAD,CAA3C;;MAEA,IAAIN,KAAK,CAACc,QAAN,CAAeP,OAAf,CAAJ,EAA6B;QAC3BI,MAAM,CAACC,IAAP,CAAY,aAAa,IAAIG,IAAJ,CAASR,OAAT,EAAkBS,WAAlB,EAAzB;MACD;;MAED,IAAIhB,KAAK,CAACiB,QAAN,CAAeT,IAAf,CAAJ,EAA0B;QACxBG,MAAM,CAACC,IAAP,CAAY,UAAUJ,IAAtB;MACD;;MAED,IAAIR,KAAK,CAACiB,QAAN,CAAeR,MAAf,CAAJ,EAA4B;QAC1BE,MAAM,CAACC,IAAP,CAAY,YAAYH,MAAxB;MACD;;MAED,IAAIC,MAAM,KAAK,IAAf,EAAqB;QACnBC,MAAM,CAACC,IAAP,CAAY,QAAZ;MACD;;MAEDM,QAAQ,CAACP,MAAT,GAAkBA,MAAM,CAACQ,IAAP,CAAY,IAAZ,CAAlB;IACD,CAtBI;IAwBLC,IAAI,EAAE,SAASA,IAAT,CAAcf,IAAd,EAAoB;MACxB,MAAMgB,KAAK,GAAGH,QAAQ,CAACP,MAAT,CAAgBU,KAAhB,CAAsB,IAAIC,MAAJ,CAAW,eAAejB,IAAf,GAAsB,WAAjC,CAAtB,CAAd;MACA,OAAQgB,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAAC,CAAD,CAAN,CAArB,GAAkC,IAA/C;IACD,CA3BI;IA6BLG,MAAM,EAAE,SAASA,MAAT,CAAgBnB,IAAhB,EAAsB;MAC5B,KAAKD,KAAL,CAAWC,IAAX,EAAiB,EAAjB,EAAqBU,IAAI,CAACU,GAAL,KAAa,QAAlC;IACD;EA/BI,CAAP;AAiCD,CAlCD,EAHa,GAuCf;AACG,SAASC,qBAAT,GAAiC;EAChC,OAAO;IACLtB,KAAK,EAAE,SAASA,KAAT,GAAiB,CAAE,CADrB;IAELgB,IAAI,EAAE,SAASA,IAAT,GAAgB;MAAE,OAAO,IAAP;IAAc,CAFjC;IAGLI,MAAM,EAAE,SAASA,MAAT,GAAkB,CAAE;EAHvB,CAAP;AAKD,CAND,EAxCF"}, "metadata": {}, "sourceType": "module"}