{"ast": null, "code": "import { useEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useEffect);", "map": {"version": 3, "names": ["useEffect", "createDeepCompareEffect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useDeepCompareEffect/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useEffect);"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,SAASC,uBAAT,QAAwC,4BAAxC;AACA,eAAeA,uBAAuB,CAACD,SAAD,CAAtC"}, "metadata": {}, "sourceType": "module"}