{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport dayjs from 'dayjs';\nimport { useEffect, useMemo, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils/index';\n\nvar calcLeft = function calcLeft(target) {\n  if (!target) {\n    return 0;\n  } // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n\n\n  var left = dayjs(target).valueOf() - Date.now();\n  return left < 0 ? 0 : left;\n};\n\nvar parseMs = function parseMs(milliseconds) {\n  return {\n    days: Math.floor(milliseconds / 86400000),\n    hours: Math.floor(milliseconds / 3600000) % 24,\n    minutes: Math.floor(milliseconds / 60000) % 60,\n    seconds: Math.floor(milliseconds / 1000) % 60,\n    milliseconds: Math.floor(milliseconds) % 1000\n  };\n};\n\nvar useCountdown = function useCountdown(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _a = options || {},\n      leftTime = _a.leftTime,\n      targetDate = _a.targetDate,\n      _b = _a.interval,\n      interval = _b === void 0 ? 1000 : _b,\n      onEnd = _a.onEnd;\n\n  var target = useMemo(function () {\n    if ('leftTime' in options) {\n      return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : undefined;\n    } else {\n      return targetDate;\n    }\n  }, [leftTime, targetDate]);\n\n  var _c = __read(useState(function () {\n    return calcLeft(target);\n  }), 2),\n      timeLeft = _c[0],\n      setTimeLeft = _c[1];\n\n  var onEndRef = useLatest(onEnd);\n  useEffect(function () {\n    if (!target) {\n      // for stop\n      setTimeLeft(0);\n      return;\n    } // 立即执行一次\n\n\n    setTimeLeft(calcLeft(target));\n    var timer = setInterval(function () {\n      var _a;\n\n      var targetLeft = calcLeft(target);\n      setTimeLeft(targetLeft);\n\n      if (targetLeft === 0) {\n        clearInterval(timer);\n        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);\n      }\n    }, interval);\n    return function () {\n      return clearInterval(timer);\n    };\n  }, [target, interval]);\n  var formattedRes = useMemo(function () {\n    return parseMs(timeLeft);\n  }, [timeLeft]);\n  return [timeLeft, formattedRes];\n};\n\nexport default useCountdown;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "dayjs", "useEffect", "useMemo", "useState", "useLatest", "isNumber", "calcLeft", "target", "left", "valueOf", "Date", "now", "parseMs", "milliseconds", "days", "Math", "floor", "hours", "minutes", "seconds", "useCountdown", "options", "_a", "leftTime", "targetDate", "_b", "interval", "onEnd", "undefined", "_c", "timeLeft", "setTimeLeft", "onEndRef", "timer", "setInterval", "targetLeft", "clearInterval", "current", "formattedRes"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useCountDown/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport dayjs from 'dayjs';\nimport { useEffect, useMemo, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils/index';\nvar calcLeft = function calcLeft(target) {\n  if (!target) {\n    return 0;\n  }\n  // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n  var left = dayjs(target).valueOf() - Date.now();\n  return left < 0 ? 0 : left;\n};\nvar parseMs = function parseMs(milliseconds) {\n  return {\n    days: Math.floor(milliseconds / 86400000),\n    hours: Math.floor(milliseconds / 3600000) % 24,\n    minutes: Math.floor(milliseconds / 60000) % 60,\n    seconds: Math.floor(milliseconds / 1000) % 60,\n    milliseconds: Math.floor(milliseconds) % 1000\n  };\n};\nvar useCountdown = function useCountdown(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options || {},\n    leftTime = _a.leftTime,\n    targetDate = _a.targetDate,\n    _b = _a.interval,\n    interval = _b === void 0 ? 1000 : _b,\n    onEnd = _a.onEnd;\n  var target = useMemo(function () {\n    if ('leftTime' in options) {\n      return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : undefined;\n    } else {\n      return targetDate;\n    }\n  }, [leftTime, targetDate]);\n  var _c = __read(useState(function () {\n      return calcLeft(target);\n    }), 2),\n    timeLeft = _c[0],\n    setTimeLeft = _c[1];\n  var onEndRef = useLatest(onEnd);\n  useEffect(function () {\n    if (!target) {\n      // for stop\n      setTimeLeft(0);\n      return;\n    }\n    // 立即执行一次\n    setTimeLeft(calcLeft(target));\n    var timer = setInterval(function () {\n      var _a;\n      var targetLeft = calcLeft(target);\n      setTimeLeft(targetLeft);\n      if (targetLeft === 0) {\n        clearInterval(timer);\n        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);\n      }\n    }, interval);\n    return function () {\n      return clearInterval(timer);\n    };\n  }, [target, interval]);\n  var formattedRes = useMemo(function () {\n    return parseMs(timeLeft);\n  }, [timeLeft]);\n  return [timeLeft, formattedRes];\n};\nexport default useCountdown;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,OAAOO,KAAP,MAAkB,OAAlB;AACA,SAASC,SAAT,EAAoBC,OAApB,EAA6BC,QAA7B,QAA6C,OAA7C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,gBAAzB;;AACA,IAAIC,QAAQ,GAAG,SAASA,QAAT,CAAkBC,MAAlB,EAA0B;EACvC,IAAI,CAACA,MAAL,EAAa;IACX,OAAO,CAAP;EACD,CAHsC,CAIvC;;;EACA,IAAIC,IAAI,GAAGR,KAAK,CAACO,MAAD,CAAL,CAAcE,OAAd,KAA0BC,IAAI,CAACC,GAAL,EAArC;EACA,OAAOH,IAAI,GAAG,CAAP,GAAW,CAAX,GAAeA,IAAtB;AACD,CAPD;;AAQA,IAAII,OAAO,GAAG,SAASA,OAAT,CAAiBC,YAAjB,EAA+B;EAC3C,OAAO;IACLC,IAAI,EAAEC,IAAI,CAACC,KAAL,CAAWH,YAAY,GAAG,QAA1B,CADD;IAELI,KAAK,EAAEF,IAAI,CAACC,KAAL,CAAWH,YAAY,GAAG,OAA1B,IAAqC,EAFvC;IAGLK,OAAO,EAAEH,IAAI,CAACC,KAAL,CAAWH,YAAY,GAAG,KAA1B,IAAmC,EAHvC;IAILM,OAAO,EAAEJ,IAAI,CAACC,KAAL,CAAWH,YAAY,GAAG,IAA1B,IAAkC,EAJtC;IAKLA,YAAY,EAAEE,IAAI,CAACC,KAAL,CAAWH,YAAX,IAA2B;EALpC,CAAP;AAOD,CARD;;AASA,IAAIO,YAAY,GAAG,SAASA,YAAT,CAAsBC,OAAtB,EAA+B;EAChD,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,EAAE,GAAGD,OAAO,IAAI,EAApB;EAAA,IACEE,QAAQ,GAAGD,EAAE,CAACC,QADhB;EAAA,IAEEC,UAAU,GAAGF,EAAE,CAACE,UAFlB;EAAA,IAGEC,EAAE,GAAGH,EAAE,CAACI,QAHV;EAAA,IAIEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EAJpC;EAAA,IAKEE,KAAK,GAAGL,EAAE,CAACK,KALb;;EAMA,IAAIpB,MAAM,GAAGL,OAAO,CAAC,YAAY;IAC/B,IAAI,cAAcmB,OAAlB,EAA2B;MACzB,OAAOhB,QAAQ,CAACkB,QAAD,CAAR,IAAsBA,QAAQ,GAAG,CAAjC,GAAqCb,IAAI,CAACC,GAAL,KAAaY,QAAlD,GAA6DK,SAApE;IACD,CAFD,MAEO;MACL,OAAOJ,UAAP;IACD;EACF,CANmB,EAMjB,CAACD,QAAD,EAAWC,UAAX,CANiB,CAApB;;EAOA,IAAIK,EAAE,GAAG7C,MAAM,CAACmB,QAAQ,CAAC,YAAY;IACjC,OAAOG,QAAQ,CAACC,MAAD,CAAf;EACD,CAFqB,CAAT,EAET,CAFS,CAAf;EAAA,IAGEuB,QAAQ,GAAGD,EAAE,CAAC,CAAD,CAHf;EAAA,IAIEE,WAAW,GAAGF,EAAE,CAAC,CAAD,CAJlB;;EAKA,IAAIG,QAAQ,GAAG5B,SAAS,CAACuB,KAAD,CAAxB;EACA1B,SAAS,CAAC,YAAY;IACpB,IAAI,CAACM,MAAL,EAAa;MACX;MACAwB,WAAW,CAAC,CAAD,CAAX;MACA;IACD,CALmB,CAMpB;;;IACAA,WAAW,CAACzB,QAAQ,CAACC,MAAD,CAAT,CAAX;IACA,IAAI0B,KAAK,GAAGC,WAAW,CAAC,YAAY;MAClC,IAAIZ,EAAJ;;MACA,IAAIa,UAAU,GAAG7B,QAAQ,CAACC,MAAD,CAAzB;MACAwB,WAAW,CAACI,UAAD,CAAX;;MACA,IAAIA,UAAU,KAAK,CAAnB,EAAsB;QACpBC,aAAa,CAACH,KAAD,CAAb;QACA,CAACX,EAAE,GAAGU,QAAQ,CAACK,OAAf,MAA4B,IAA5B,IAAoCf,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAC/B,IAAH,CAAQyC,QAAR,CAA7D;MACD;IACF,CARsB,EAQpBN,QARoB,CAAvB;IASA,OAAO,YAAY;MACjB,OAAOU,aAAa,CAACH,KAAD,CAApB;IACD,CAFD;EAGD,CApBQ,EAoBN,CAAC1B,MAAD,EAASmB,QAAT,CApBM,CAAT;EAqBA,IAAIY,YAAY,GAAGpC,OAAO,CAAC,YAAY;IACrC,OAAOU,OAAO,CAACkB,QAAD,CAAd;EACD,CAFyB,EAEvB,CAACA,QAAD,CAFuB,CAA1B;EAGA,OAAO,CAACA,QAAD,EAAWQ,YAAX,CAAP;AACD,CAhDD;;AAiDA,eAAelB,YAAf"}, "metadata": {}, "sourceType": "module"}