{"ast": null, "code": "var getMapData = require('./_getMapData');\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\n\n\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\nmodule.exports = mapCacheHas;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheHas", "key", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapCacheHas.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\nmodule.exports = mapCacheHas;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBC,GAArB,EAA0B;EACxB,OAAOH,UAAU,CAAC,IAAD,EAAOG,GAAP,CAAV,CAAsBC,GAAtB,CAA0BD,GAA1B,CAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBJ,WAAjB"}, "metadata": {}, "sourceType": "script"}