{"ast": null, "code": "'use strict';\n\nvar required = require('requires-port'),\n    qs = require('querystringify'),\n    controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/,\n    CRHTLF = /[\\n\\r\\t]/g,\n    slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//,\n    port = /:\\d+$/,\n    protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i,\n    windowsDriveLetter = /^[a-zA-Z]:/;\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\n\n\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\n\n\nvar rules = [['#', 'hash'], // Extract from the back.\n['?', 'query'], // Extract from the back.\nfunction sanitize(address, url) {\n  // Sanitize what is left of the address\n  return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n}, ['/', 'pathname'], // Extract from the back.\n['@', 'auth', 1], // Extract from the front.\n[NaN, 'host', undefined, 1, 1], // Set left over value.\n[/:(\\d*)$/, 'port', undefined, 1], // RegExp the back.\n[NaN, 'hostname', undefined, 1, 1] // Set left over.\n];\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\n\nvar ignore = {\n  hash: 1,\n  query: 1\n};\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\n\nfunction lolcation(loc) {\n  var globalVar;\n  if (typeof window !== 'undefined') globalVar = window;else if (typeof global !== 'undefined') globalVar = global;else if (typeof self !== 'undefined') globalVar = self;else globalVar = {};\n  var location = globalVar.location || {};\n  loc = loc || location;\n  var finaldestination = {},\n      type = typeof loc,\n      key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n\n    for (key in ignore) {\n      delete finaldestination[key];\n    }\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\n\n\nfunction isSpecial(scheme) {\n  return scheme === 'file:' || scheme === 'ftp:' || scheme === 'http:' || scheme === 'https:' || scheme === 'ws:' || scheme === 'wss:';\n}\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\n\n\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4];\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\n\n\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/')),\n      i = path.length,\n      last = path[i - 1],\n      unshift = false,\n      up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n  return path.join('/');\n}\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\n\n\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative,\n      extracted,\n      parse,\n      instruction,\n      index,\n      key,\n      instructions = rules.slice(),\n      type = typeof location,\n      url = this,\n      i = 0; //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n  location = lolcation(location); //\n  // Extract protocol information before running the instructions.\n  //\n\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest; //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n\n  if (extracted.protocol === 'file:' && (extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) || !extracted.slashes && (extracted.protocol || extracted.slashesCount < 2 || !isSpecial(url.protocol))) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@' ? address.lastIndexOf(parse) : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if (index = parse.exec(address)) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (relative && instruction[3] ? location[key] || '' : ''); //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  } //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n\n\n  if (parser) url.query = parser(url.query); //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n\n  if (relative && location.slashes && url.pathname.charAt(0) !== '/' && (url.pathname !== '' || location.pathname !== '')) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  } //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n\n\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  } //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n\n\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  } //\n  // Parse down the `auth` for the username and password.\n  //\n\n\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password));\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username + ':' + url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null'; //\n  // The href is just the compiled result.\n  //\n\n  url.href = url.toString();\n}\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\n\n\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname + ':' + value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n      if (url.port) value += ':' + url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username + ':' + url.password : url.username;\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host ? url.protocol + '//' + url.host : 'null';\n  url.href = url.toString();\n  return url;\n}\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\n\n\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n  var query,\n      url = this,\n      host = url.host,\n      protocol = url.protocol;\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n  var result = protocol + (url.protocol && url.slashes || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':' + url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':' + url.password;\n    result += '@';\n  } else if (url.protocol !== 'file:' && isSpecial(url.protocol) && !host && url.pathname !== '/') {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  } //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n\n\n  if (host[host.length - 1] === ':' || port.test(url.hostname) && !url.port) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?' + query : query;\n  if (url.hash) result += url.hash;\n  return result;\n}\n\nUrl.prototype = {\n  set: set,\n  toString: toString\n}; //\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\n\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\nmodule.exports = Url;", "map": {"version": 3, "names": ["required", "require", "qs", "controlOrWhitespace", "CRHTLF", "slashes", "port", "protocolre", "windowsDriveLetter", "trimLeft", "str", "toString", "replace", "rules", "sanitize", "address", "url", "isSpecial", "protocol", "NaN", "undefined", "ignore", "hash", "query", "lolcation", "loc", "globalVar", "window", "global", "self", "location", "finaldestination", "type", "key", "Url", "unescape", "pathname", "test", "href", "scheme", "extractProtocol", "match", "exec", "toLowerCase", "forwardSlashes", "otherSlashes", "slashesCount", "rest", "length", "slice", "resolve", "relative", "base", "path", "split", "concat", "i", "last", "unshift", "up", "splice", "push", "join", "parser", "extracted", "parse", "instruction", "index", "instructions", "lastIndexOf", "indexOf", "char<PERSON>t", "host", "hostname", "username", "password", "auth", "encodeURIComponent", "decodeURIComponent", "origin", "set", "part", "value", "fn", "pop", "char", "ins", "stringify", "result", "prototype", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/url-parse/index.js"], "sourcesContent": ["'use strict';\n\nvar required = require('requires-port')\n  , qs = require('querystringify')\n  , controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/\n  , CRHTLF = /[\\n\\r\\t]/g\n  , slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//\n  , port = /:\\d+$/\n  , protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i\n  , windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [\n  ['#', 'hash'],                        // Extract from the back.\n  ['?', 'query'],                       // Extract from the back.\n  function sanitize(address, url) {     // Sanitize what is left of the address\n    return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n  },\n  ['/', 'pathname'],                    // Extract from the back.\n  ['@', 'auth', 1],                     // Extract from the front.\n  [NaN, 'host', undefined, 1, 1],       // Set left over value.\n  [/:(\\d*)$/, 'port', undefined, 1],    // RegExp the back.\n  [NaN, 'hostname', undefined, 1, 1]    // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = { hash: 1, query: 1 };\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n\n  if (typeof window !== 'undefined') globalVar = window;\n  else if (typeof global !== 'undefined') globalVar = global;\n  else if (typeof self !== 'undefined') globalVar = self;\n  else globalVar = {};\n\n  var location = globalVar.location || {};\n  loc = loc || location;\n\n  var finaldestination = {}\n    , type = typeof loc\n    , key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return (\n    scheme === 'file:' ||\n    scheme === 'ftp:' ||\n    scheme === 'http:' ||\n    scheme === 'https:' ||\n    scheme === 'ws:' ||\n    scheme === 'wss:'\n  );\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4]\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/'))\n    , i = path.length\n    , last = path[i - 1]\n    , unshift = false\n    , up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative, extracted, parse, instruction, index, key\n    , instructions = rules.slice()\n    , type = typeof location\n    , url = this\n    , i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (\n    extracted.protocol === 'file:' && (\n      extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) ||\n    (!extracted.slashes &&\n      (extracted.protocol ||\n        extracted.slashesCount < 2 ||\n        !isSpecial(url.protocol)))\n  ) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@'\n        ? address.lastIndexOf(parse)\n        : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if ((index = parse.exec(address))) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (\n      relative && instruction[3] ? location[key] || '' : ''\n    );\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (\n      relative\n    && location.slashes\n    && url.pathname.charAt(0) !== '/'\n    && (url.pathname !== '' || location.pathname !== '')\n  ) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password))\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username +':'+ url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname +':'+ value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n\n      if (url.port) value += ':'+ url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username +':'+ url.password : url.username;\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  url.href = url.toString();\n\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n\n  var query\n    , url = this\n    , host = url.host\n    , protocol = url.protocol;\n\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n\n  var result =\n    protocol +\n    ((url.protocol && url.slashes) || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':'+ url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':'+ url.password;\n    result += '@';\n  } else if (\n    url.protocol !== 'file:' &&\n    isSpecial(url.protocol) &&\n    !host &&\n    url.pathname !== '/'\n  ) {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || (port.test(url.hostname) && !url.port)) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?'+ query : query;\n\n  if (url.hash) result += url.hash;\n\n  return result;\n}\n\nUrl.prototype = { set: set, toString: toString };\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\n\nmodule.exports = Url;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,eAAD,CAAtB;AAAA,IACIC,EAAE,GAAGD,OAAO,CAAC,gBAAD,CADhB;AAAA,IAEIE,mBAAmB,GAAG,4EAF1B;AAAA,IAGIC,MAAM,GAAG,WAHb;AAAA,IAIIC,OAAO,GAAG,+BAJd;AAAA,IAKIC,IAAI,GAAG,OALX;AAAA,IAMIC,UAAU,GAAG,kDANjB;AAAA,IAOIC,kBAAkB,GAAG,YAPzB;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,QAAT,CAAkBC,GAAlB,EAAuB;EACrB,OAAO,CAACA,GAAG,GAAGA,GAAH,GAAS,EAAb,EAAiBC,QAAjB,GAA4BC,OAA5B,CAAoCT,mBAApC,EAAyD,EAAzD,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIU,KAAK,GAAG,CACV,CAAC,GAAD,EAAM,MAAN,CADU,EAC4B;AACtC,CAAC,GAAD,EAAM,OAAN,CAFU,EAE4B;AACtC,SAASC,QAAT,CAAkBC,OAAlB,EAA2BC,GAA3B,EAAgC;EAAM;EACpC,OAAOC,SAAS,CAACD,GAAG,CAACE,QAAL,CAAT,GAA0BH,OAAO,CAACH,OAAR,CAAgB,KAAhB,EAAuB,GAAvB,CAA1B,GAAwDG,OAA/D;AACD,CALS,EAMV,CAAC,GAAD,EAAM,UAAN,CANU,EAM4B;AACtC,CAAC,GAAD,EAAM,MAAN,EAAc,CAAd,CAPU,EAO4B;AACtC,CAACI,GAAD,EAAM,MAAN,EAAcC,SAAd,EAAyB,CAAzB,EAA4B,CAA5B,CARU,EAQ4B;AACtC,CAAC,SAAD,EAAY,MAAZ,EAAoBA,SAApB,EAA+B,CAA/B,CATU,EAS4B;AACtC,CAACD,GAAD,EAAM,UAAN,EAAkBC,SAAlB,EAA6B,CAA7B,EAAgC,CAAhC,CAVU,CAU4B;AAV5B,CAAZ;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,MAAM,GAAG;EAAEC,IAAI,EAAE,CAAR;EAAWC,KAAK,EAAE;AAAlB,CAAb;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,CAAmBC,GAAnB,EAAwB;EACtB,IAAIC,SAAJ;EAEA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmCD,SAAS,GAAGC,MAAZ,CAAnC,KACK,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmCF,SAAS,GAAGE,MAAZ,CAAnC,KACA,IAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiCH,SAAS,GAAGG,IAAZ,CAAjC,KACAH,SAAS,GAAG,EAAZ;EAEL,IAAII,QAAQ,GAAGJ,SAAS,CAACI,QAAV,IAAsB,EAArC;EACAL,GAAG,GAAGA,GAAG,IAAIK,QAAb;EAEA,IAAIC,gBAAgB,GAAG,EAAvB;EAAA,IACIC,IAAI,GAAG,OAAOP,GADlB;EAAA,IAEIQ,GAFJ;;EAIA,IAAI,YAAYR,GAAG,CAACP,QAApB,EAA8B;IAC5Ba,gBAAgB,GAAG,IAAIG,GAAJ,CAAQC,QAAQ,CAACV,GAAG,CAACW,QAAL,CAAhB,EAAgC,EAAhC,CAAnB;EACD,CAFD,MAEO,IAAI,aAAaJ,IAAjB,EAAuB;IAC5BD,gBAAgB,GAAG,IAAIG,GAAJ,CAAQT,GAAR,EAAa,EAAb,CAAnB;;IACA,KAAKQ,GAAL,IAAYZ,MAAZ;MAAoB,OAAOU,gBAAgB,CAACE,GAAD,CAAvB;IAApB;EACD,CAHM,MAGA,IAAI,aAAaD,IAAjB,EAAuB;IAC5B,KAAKC,GAAL,IAAYR,GAAZ,EAAiB;MACf,IAAIQ,GAAG,IAAIZ,MAAX,EAAmB;MACnBU,gBAAgB,CAACE,GAAD,CAAhB,GAAwBR,GAAG,CAACQ,GAAD,CAA3B;IACD;;IAED,IAAIF,gBAAgB,CAAC1B,OAAjB,KAA6Be,SAAjC,EAA4C;MAC1CW,gBAAgB,CAAC1B,OAAjB,GAA2BA,OAAO,CAACgC,IAAR,CAAaZ,GAAG,CAACa,IAAjB,CAA3B;IACD;EACF;;EAED,OAAOP,gBAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASd,SAAT,CAAmBsB,MAAnB,EAA2B;EACzB,OACEA,MAAM,KAAK,OAAX,IACAA,MAAM,KAAK,MADX,IAEAA,MAAM,KAAK,OAFX,IAGAA,MAAM,KAAK,QAHX,IAIAA,MAAM,KAAK,KAJX,IAKAA,MAAM,KAAK,MANb;AAQD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,eAAT,CAAyBzB,OAAzB,EAAkCe,QAAlC,EAA4C;EAC1Cf,OAAO,GAAGN,QAAQ,CAACM,OAAD,CAAlB;EACAA,OAAO,GAAGA,OAAO,CAACH,OAAR,CAAgBR,MAAhB,EAAwB,EAAxB,CAAV;EACA0B,QAAQ,GAAGA,QAAQ,IAAI,EAAvB;EAEA,IAAIW,KAAK,GAAGlC,UAAU,CAACmC,IAAX,CAAgB3B,OAAhB,CAAZ;EACA,IAAIG,QAAQ,GAAGuB,KAAK,CAAC,CAAD,CAAL,GAAWA,KAAK,CAAC,CAAD,CAAL,CAASE,WAAT,EAAX,GAAoC,EAAnD;EACA,IAAIC,cAAc,GAAG,CAAC,CAACH,KAAK,CAAC,CAAD,CAA5B;EACA,IAAII,YAAY,GAAG,CAAC,CAACJ,KAAK,CAAC,CAAD,CAA1B;EACA,IAAIK,YAAY,GAAG,CAAnB;EACA,IAAIC,IAAJ;;EAEA,IAAIH,cAAJ,EAAoB;IAClB,IAAIC,YAAJ,EAAkB;MAChBE,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAL,GAAWA,KAAK,CAAC,CAAD,CAAhB,GAAsBA,KAAK,CAAC,CAAD,CAAlC;MACAK,YAAY,GAAGL,KAAK,CAAC,CAAD,CAAL,CAASO,MAAT,GAAkBP,KAAK,CAAC,CAAD,CAAL,CAASO,MAA1C;IACD,CAHD,MAGO;MACLD,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAL,GAAWA,KAAK,CAAC,CAAD,CAAvB;MACAK,YAAY,GAAGL,KAAK,CAAC,CAAD,CAAL,CAASO,MAAxB;IACD;EACF,CARD,MAQO;IACL,IAAIH,YAAJ,EAAkB;MAChBE,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAL,GAAWA,KAAK,CAAC,CAAD,CAAvB;MACAK,YAAY,GAAGL,KAAK,CAAC,CAAD,CAAL,CAASO,MAAxB;IACD,CAHD,MAGO;MACLD,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAZ;IACD;EACF;;EAED,IAAIvB,QAAQ,KAAK,OAAjB,EAA0B;IACxB,IAAI4B,YAAY,IAAI,CAApB,EAAuB;MACrBC,IAAI,GAAGA,IAAI,CAACE,KAAL,CAAW,CAAX,CAAP;IACD;EACF,CAJD,MAIO,IAAIhC,SAAS,CAACC,QAAD,CAAb,EAAyB;IAC9B6B,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAZ;EACD,CAFM,MAEA,IAAIvB,QAAJ,EAAc;IACnB,IAAI0B,cAAJ,EAAoB;MAClBG,IAAI,GAAGA,IAAI,CAACE,KAAL,CAAW,CAAX,CAAP;IACD;EACF,CAJM,MAIA,IAAIH,YAAY,IAAI,CAAhB,IAAqB7B,SAAS,CAACa,QAAQ,CAACZ,QAAV,CAAlC,EAAuD;IAC5D6B,IAAI,GAAGN,KAAK,CAAC,CAAD,CAAZ;EACD;;EAED,OAAO;IACLvB,QAAQ,EAAEA,QADL;IAELb,OAAO,EAAEuC,cAAc,IAAI3B,SAAS,CAACC,QAAD,CAF/B;IAGL4B,YAAY,EAAEA,YAHT;IAILC,IAAI,EAAEA;EAJD,CAAP;AAMD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,OAAT,CAAiBC,QAAjB,EAA2BC,IAA3B,EAAiC;EAC/B,IAAID,QAAQ,KAAK,EAAjB,EAAqB,OAAOC,IAAP;EAErB,IAAIC,IAAI,GAAG,CAACD,IAAI,IAAI,GAAT,EAAcE,KAAd,CAAoB,GAApB,EAAyBL,KAAzB,CAA+B,CAA/B,EAAkC,CAAC,CAAnC,EAAsCM,MAAtC,CAA6CJ,QAAQ,CAACG,KAAT,CAAe,GAAf,CAA7C,CAAX;EAAA,IACIE,CAAC,GAAGH,IAAI,CAACL,MADb;EAAA,IAEIS,IAAI,GAAGJ,IAAI,CAACG,CAAC,GAAG,CAAL,CAFf;EAAA,IAGIE,OAAO,GAAG,KAHd;EAAA,IAIIC,EAAE,GAAG,CAJT;;EAMA,OAAOH,CAAC,EAAR,EAAY;IACV,IAAIH,IAAI,CAACG,CAAD,CAAJ,KAAY,GAAhB,EAAqB;MACnBH,IAAI,CAACO,MAAL,CAAYJ,CAAZ,EAAe,CAAf;IACD,CAFD,MAEO,IAAIH,IAAI,CAACG,CAAD,CAAJ,KAAY,IAAhB,EAAsB;MAC3BH,IAAI,CAACO,MAAL,CAAYJ,CAAZ,EAAe,CAAf;MACAG,EAAE;IACH,CAHM,MAGA,IAAIA,EAAJ,EAAQ;MACb,IAAIH,CAAC,KAAK,CAAV,EAAaE,OAAO,GAAG,IAAV;MACbL,IAAI,CAACO,MAAL,CAAYJ,CAAZ,EAAe,CAAf;MACAG,EAAE;IACH;EACF;;EAED,IAAID,OAAJ,EAAaL,IAAI,CAACK,OAAL,CAAa,EAAb;EACb,IAAID,IAAI,KAAK,GAAT,IAAgBA,IAAI,KAAK,IAA7B,EAAmCJ,IAAI,CAACQ,IAAL,CAAU,EAAV;EAEnC,OAAOR,IAAI,CAACS,IAAL,CAAU,GAAV,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS5B,GAAT,CAAanB,OAAb,EAAsBe,QAAtB,EAAgCiC,MAAhC,EAAwC;EACtChD,OAAO,GAAGN,QAAQ,CAACM,OAAD,CAAlB;EACAA,OAAO,GAAGA,OAAO,CAACH,OAAR,CAAgBR,MAAhB,EAAwB,EAAxB,CAAV;;EAEA,IAAI,EAAE,gBAAgB8B,GAAlB,CAAJ,EAA4B;IAC1B,OAAO,IAAIA,GAAJ,CAAQnB,OAAR,EAAiBe,QAAjB,EAA2BiC,MAA3B,CAAP;EACD;;EAED,IAAIZ,QAAJ;EAAA,IAAca,SAAd;EAAA,IAAyBC,KAAzB;EAAA,IAAgCC,WAAhC;EAAA,IAA6CC,KAA7C;EAAA,IAAoDlC,GAApD;EAAA,IACImC,YAAY,GAAGvD,KAAK,CAACoC,KAAN,EADnB;EAAA,IAEIjB,IAAI,GAAG,OAAOF,QAFlB;EAAA,IAGId,GAAG,GAAG,IAHV;EAAA,IAIIwC,CAAC,GAAG,CAJR,CARsC,CActC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI,aAAaxB,IAAb,IAAqB,aAAaA,IAAtC,EAA4C;IAC1C+B,MAAM,GAAGjC,QAAT;IACAA,QAAQ,GAAG,IAAX;EACD;;EAED,IAAIiC,MAAM,IAAI,eAAe,OAAOA,MAApC,EAA4CA,MAAM,GAAG7D,EAAE,CAAC+D,KAAZ;EAE5CnC,QAAQ,GAAGN,SAAS,CAACM,QAAD,CAApB,CAhCsC,CAkCtC;EACA;EACA;;EACAkC,SAAS,GAAGxB,eAAe,CAACzB,OAAO,IAAI,EAAZ,EAAgBe,QAAhB,CAA3B;EACAqB,QAAQ,GAAG,CAACa,SAAS,CAAC9C,QAAX,IAAuB,CAAC8C,SAAS,CAAC3D,OAA7C;EACAW,GAAG,CAACX,OAAJ,GAAc2D,SAAS,CAAC3D,OAAV,IAAqB8C,QAAQ,IAAIrB,QAAQ,CAACzB,OAAxD;EACAW,GAAG,CAACE,QAAJ,GAAe8C,SAAS,CAAC9C,QAAV,IAAsBY,QAAQ,CAACZ,QAA/B,IAA2C,EAA1D;EACAH,OAAO,GAAGiD,SAAS,CAACjB,IAApB,CAzCsC,CA2CtC;EACA;EACA;EACA;;EACA,IACEiB,SAAS,CAAC9C,QAAV,KAAuB,OAAvB,KACE8C,SAAS,CAAClB,YAAV,KAA2B,CAA3B,IAAgCtC,kBAAkB,CAAC6B,IAAnB,CAAwBtB,OAAxB,CADlC,KAEC,CAACiD,SAAS,CAAC3D,OAAX,KACE2D,SAAS,CAAC9C,QAAV,IACC8C,SAAS,CAAClB,YAAV,GAAyB,CAD1B,IAEC,CAAC7B,SAAS,CAACD,GAAG,CAACE,QAAL,CAHb,CAHH,EAOE;IACAkD,YAAY,CAAC,CAAD,CAAZ,GAAkB,CAAC,MAAD,EAAS,UAAT,CAAlB;EACD;;EAED,OAAOZ,CAAC,GAAGY,YAAY,CAACpB,MAAxB,EAAgCQ,CAAC,EAAjC,EAAqC;IACnCU,WAAW,GAAGE,YAAY,CAACZ,CAAD,CAA1B;;IAEA,IAAI,OAAOU,WAAP,KAAuB,UAA3B,EAAuC;MACrCnD,OAAO,GAAGmD,WAAW,CAACnD,OAAD,EAAUC,GAAV,CAArB;MACA;IACD;;IAEDiD,KAAK,GAAGC,WAAW,CAAC,CAAD,CAAnB;IACAjC,GAAG,GAAGiC,WAAW,CAAC,CAAD,CAAjB;;IAEA,IAAID,KAAK,KAAKA,KAAd,EAAqB;MACnBjD,GAAG,CAACiB,GAAD,CAAH,GAAWlB,OAAX;IACD,CAFD,MAEO,IAAI,aAAa,OAAOkD,KAAxB,EAA+B;MACpCE,KAAK,GAAGF,KAAK,KAAK,GAAV,GACJlD,OAAO,CAACsD,WAAR,CAAoBJ,KAApB,CADI,GAEJlD,OAAO,CAACuD,OAAR,CAAgBL,KAAhB,CAFJ;;MAIA,IAAI,CAACE,KAAL,EAAY;QACV,IAAI,aAAa,OAAOD,WAAW,CAAC,CAAD,CAAnC,EAAwC;UACtClD,GAAG,CAACiB,GAAD,CAAH,GAAWlB,OAAO,CAACkC,KAAR,CAAc,CAAd,EAAiBkB,KAAjB,CAAX;UACApD,OAAO,GAAGA,OAAO,CAACkC,KAAR,CAAckB,KAAK,GAAGD,WAAW,CAAC,CAAD,CAAjC,CAAV;QACD,CAHD,MAGO;UACLlD,GAAG,CAACiB,GAAD,CAAH,GAAWlB,OAAO,CAACkC,KAAR,CAAckB,KAAd,CAAX;UACApD,OAAO,GAAGA,OAAO,CAACkC,KAAR,CAAc,CAAd,EAAiBkB,KAAjB,CAAV;QACD;MACF;IACF,CAdM,MAcA,IAAKA,KAAK,GAAGF,KAAK,CAACvB,IAAN,CAAW3B,OAAX,CAAb,EAAmC;MACxCC,GAAG,CAACiB,GAAD,CAAH,GAAWkC,KAAK,CAAC,CAAD,CAAhB;MACApD,OAAO,GAAGA,OAAO,CAACkC,KAAR,CAAc,CAAd,EAAiBkB,KAAK,CAACA,KAAvB,CAAV;IACD;;IAEDnD,GAAG,CAACiB,GAAD,CAAH,GAAWjB,GAAG,CAACiB,GAAD,CAAH,KACTkB,QAAQ,IAAIe,WAAW,CAAC,CAAD,CAAvB,GAA6BpC,QAAQ,CAACG,GAAD,CAAR,IAAiB,EAA9C,GAAmD,EAD1C,CAAX,CAhCmC,CAoCnC;IACA;IACA;IACA;;IACA,IAAIiC,WAAW,CAAC,CAAD,CAAf,EAAoBlD,GAAG,CAACiB,GAAD,CAAH,GAAWjB,GAAG,CAACiB,GAAD,CAAH,CAASU,WAAT,EAAX;EACrB,CAnGqC,CAqGtC;EACA;EACA;EACA;EACA;;;EACA,IAAIoB,MAAJ,EAAY/C,GAAG,CAACO,KAAJ,GAAYwC,MAAM,CAAC/C,GAAG,CAACO,KAAL,CAAlB,CA1G0B,CA4GtC;EACA;EACA;;EACA,IACI4B,QAAQ,IACPrB,QAAQ,CAACzB,OADV,IAECW,GAAG,CAACoB,QAAJ,CAAamC,MAAb,CAAoB,CAApB,MAA2B,GAF5B,KAGEvD,GAAG,CAACoB,QAAJ,KAAiB,EAAjB,IAAuBN,QAAQ,CAACM,QAAT,KAAsB,EAH/C,CADJ,EAKE;IACApB,GAAG,CAACoB,QAAJ,GAAec,OAAO,CAAClC,GAAG,CAACoB,QAAL,EAAeN,QAAQ,CAACM,QAAxB,CAAtB;EACD,CAtHqC,CAwHtC;EACA;EACA;EACA;;;EACA,IAAIpB,GAAG,CAACoB,QAAJ,CAAamC,MAAb,CAAoB,CAApB,MAA2B,GAA3B,IAAkCtD,SAAS,CAACD,GAAG,CAACE,QAAL,CAA/C,EAA+D;IAC7DF,GAAG,CAACoB,QAAJ,GAAe,MAAMpB,GAAG,CAACoB,QAAzB;EACD,CA9HqC,CAgItC;EACA;EACA;EACA;EACA;;;EACA,IAAI,CAACpC,QAAQ,CAACgB,GAAG,CAACV,IAAL,EAAWU,GAAG,CAACE,QAAf,CAAb,EAAuC;IACrCF,GAAG,CAACwD,IAAJ,GAAWxD,GAAG,CAACyD,QAAf;IACAzD,GAAG,CAACV,IAAJ,GAAW,EAAX;EACD,CAxIqC,CA0ItC;EACA;EACA;;;EACAU,GAAG,CAAC0D,QAAJ,GAAe1D,GAAG,CAAC2D,QAAJ,GAAe,EAA9B;;EAEA,IAAI3D,GAAG,CAAC4D,IAAR,EAAc;IACZT,KAAK,GAAGnD,GAAG,CAAC4D,IAAJ,CAASN,OAAT,CAAiB,GAAjB,CAAR;;IAEA,IAAI,CAACH,KAAL,EAAY;MACVnD,GAAG,CAAC0D,QAAJ,GAAe1D,GAAG,CAAC4D,IAAJ,CAAS3B,KAAT,CAAe,CAAf,EAAkBkB,KAAlB,CAAf;MACAnD,GAAG,CAAC0D,QAAJ,GAAeG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC0D,QAAL,CAAnB,CAAjC;MAEA1D,GAAG,CAAC2D,QAAJ,GAAe3D,GAAG,CAAC4D,IAAJ,CAAS3B,KAAT,CAAekB,KAAK,GAAG,CAAvB,CAAf;MACAnD,GAAG,CAAC2D,QAAJ,GAAeE,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC2D,QAAL,CAAnB,CAAjC;IACD,CAND,MAMO;MACL3D,GAAG,CAAC0D,QAAJ,GAAeG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC4D,IAAL,CAAnB,CAAjC;IACD;;IAED5D,GAAG,CAAC4D,IAAJ,GAAW5D,GAAG,CAAC2D,QAAJ,GAAe3D,GAAG,CAAC0D,QAAJ,GAAc,GAAd,GAAmB1D,GAAG,CAAC2D,QAAtC,GAAiD3D,GAAG,CAAC0D,QAAhE;EACD;;EAED1D,GAAG,CAAC+D,MAAJ,GAAa/D,GAAG,CAACE,QAAJ,KAAiB,OAAjB,IAA4BD,SAAS,CAACD,GAAG,CAACE,QAAL,CAArC,IAAuDF,GAAG,CAACwD,IAA3D,GACTxD,GAAG,CAACE,QAAJ,GAAc,IAAd,GAAoBF,GAAG,CAACwD,IADf,GAET,MAFJ,CA/JsC,CAmKtC;EACA;EACA;;EACAxD,GAAG,CAACsB,IAAJ,GAAWtB,GAAG,CAACL,QAAJ,EAAX;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASqE,GAAT,CAAaC,IAAb,EAAmBC,KAAnB,EAA0BC,EAA1B,EAA8B;EAC5B,IAAInE,GAAG,GAAG,IAAV;;EAEA,QAAQiE,IAAR;IACE,KAAK,OAAL;MACE,IAAI,aAAa,OAAOC,KAApB,IAA6BA,KAAK,CAAClC,MAAvC,EAA+C;QAC7CkC,KAAK,GAAG,CAACC,EAAE,IAAIjF,EAAE,CAAC+D,KAAV,EAAiBiB,KAAjB,CAAR;MACD;;MAEDlE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAZ;MACA;;IAEF,KAAK,MAAL;MACElE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAZ;;MAEA,IAAI,CAAClF,QAAQ,CAACkF,KAAD,EAAQlE,GAAG,CAACE,QAAZ,CAAb,EAAoC;QAClCF,GAAG,CAACwD,IAAJ,GAAWxD,GAAG,CAACyD,QAAf;QACAzD,GAAG,CAACiE,IAAD,CAAH,GAAY,EAAZ;MACD,CAHD,MAGO,IAAIC,KAAJ,EAAW;QAChBlE,GAAG,CAACwD,IAAJ,GAAWxD,GAAG,CAACyD,QAAJ,GAAc,GAAd,GAAmBS,KAA9B;MACD;;MAED;;IAEF,KAAK,UAAL;MACElE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAZ;MAEA,IAAIlE,GAAG,CAACV,IAAR,EAAc4E,KAAK,IAAI,MAAKlE,GAAG,CAACV,IAAlB;MACdU,GAAG,CAACwD,IAAJ,GAAWU,KAAX;MACA;;IAEF,KAAK,MAAL;MACElE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAZ;;MAEA,IAAI5E,IAAI,CAAC+B,IAAL,CAAU6C,KAAV,CAAJ,EAAsB;QACpBA,KAAK,GAAGA,KAAK,CAAC5B,KAAN,CAAY,GAAZ,CAAR;QACAtC,GAAG,CAACV,IAAJ,GAAW4E,KAAK,CAACE,GAAN,EAAX;QACApE,GAAG,CAACyD,QAAJ,GAAeS,KAAK,CAACpB,IAAN,CAAW,GAAX,CAAf;MACD,CAJD,MAIO;QACL9C,GAAG,CAACyD,QAAJ,GAAeS,KAAf;QACAlE,GAAG,CAACV,IAAJ,GAAW,EAAX;MACD;;MAED;;IAEF,KAAK,UAAL;MACEU,GAAG,CAACE,QAAJ,GAAegE,KAAK,CAACvC,WAAN,EAAf;MACA3B,GAAG,CAACX,OAAJ,GAAc,CAAC8E,EAAf;MACA;;IAEF,KAAK,UAAL;IACA,KAAK,MAAL;MACE,IAAID,KAAJ,EAAW;QACT,IAAIG,IAAI,GAAGJ,IAAI,KAAK,UAAT,GAAsB,GAAtB,GAA4B,GAAvC;QACAjE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAK,CAACX,MAAN,CAAa,CAAb,MAAoBc,IAApB,GAA2BA,IAAI,GAAGH,KAAlC,GAA0CA,KAAtD;MACD,CAHD,MAGO;QACLlE,GAAG,CAACiE,IAAD,CAAH,GAAYC,KAAZ;MACD;;MACD;;IAEF,KAAK,UAAL;IACA,KAAK,UAAL;MACElE,GAAG,CAACiE,IAAD,CAAH,GAAYJ,kBAAkB,CAACK,KAAD,CAA9B;MACA;;IAEF,KAAK,MAAL;MACE,IAAIf,KAAK,GAAGe,KAAK,CAACZ,OAAN,CAAc,GAAd,CAAZ;;MAEA,IAAI,CAACH,KAAL,EAAY;QACVnD,GAAG,CAAC0D,QAAJ,GAAeQ,KAAK,CAACjC,KAAN,CAAY,CAAZ,EAAekB,KAAf,CAAf;QACAnD,GAAG,CAAC0D,QAAJ,GAAeG,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC0D,QAAL,CAAnB,CAAjC;QAEA1D,GAAG,CAAC2D,QAAJ,GAAeO,KAAK,CAACjC,KAAN,CAAYkB,KAAK,GAAG,CAApB,CAAf;QACAnD,GAAG,CAAC2D,QAAJ,GAAeE,kBAAkB,CAACC,kBAAkB,CAAC9D,GAAG,CAAC2D,QAAL,CAAnB,CAAjC;MACD,CAND,MAMO;QACL3D,GAAG,CAAC0D,QAAJ,GAAeG,kBAAkB,CAACC,kBAAkB,CAACI,KAAD,CAAnB,CAAjC;MACD;;EAzEL;;EA4EA,KAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG3C,KAAK,CAACmC,MAA1B,EAAkCQ,CAAC,EAAnC,EAAuC;IACrC,IAAI8B,GAAG,GAAGzE,KAAK,CAAC2C,CAAD,CAAf;IAEA,IAAI8B,GAAG,CAAC,CAAD,CAAP,EAAYtE,GAAG,CAACsE,GAAG,CAAC,CAAD,CAAJ,CAAH,GAActE,GAAG,CAACsE,GAAG,CAAC,CAAD,CAAJ,CAAH,CAAY3C,WAAZ,EAAd;EACb;;EAED3B,GAAG,CAAC4D,IAAJ,GAAW5D,GAAG,CAAC2D,QAAJ,GAAe3D,GAAG,CAAC0D,QAAJ,GAAc,GAAd,GAAmB1D,GAAG,CAAC2D,QAAtC,GAAiD3D,GAAG,CAAC0D,QAAhE;EAEA1D,GAAG,CAAC+D,MAAJ,GAAa/D,GAAG,CAACE,QAAJ,KAAiB,OAAjB,IAA4BD,SAAS,CAACD,GAAG,CAACE,QAAL,CAArC,IAAuDF,GAAG,CAACwD,IAA3D,GACTxD,GAAG,CAACE,QAAJ,GAAc,IAAd,GAAoBF,GAAG,CAACwD,IADf,GAET,MAFJ;EAIAxD,GAAG,CAACsB,IAAJ,GAAWtB,GAAG,CAACL,QAAJ,EAAX;EAEA,OAAOK,GAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASL,QAAT,CAAkB4E,SAAlB,EAA6B;EAC3B,IAAI,CAACA,SAAD,IAAc,eAAe,OAAOA,SAAxC,EAAmDA,SAAS,GAAGrF,EAAE,CAACqF,SAAf;EAEnD,IAAIhE,KAAJ;EAAA,IACIP,GAAG,GAAG,IADV;EAAA,IAEIwD,IAAI,GAAGxD,GAAG,CAACwD,IAFf;EAAA,IAGItD,QAAQ,GAAGF,GAAG,CAACE,QAHnB;EAKA,IAAIA,QAAQ,IAAIA,QAAQ,CAACqD,MAAT,CAAgBrD,QAAQ,CAAC8B,MAAT,GAAkB,CAAlC,MAAyC,GAAzD,EAA8D9B,QAAQ,IAAI,GAAZ;EAE9D,IAAIsE,MAAM,GACRtE,QAAQ,IACNF,GAAG,CAACE,QAAJ,IAAgBF,GAAG,CAACX,OAArB,IAAiCY,SAAS,CAACD,GAAG,CAACE,QAAL,CAA1C,GAA2D,IAA3D,GAAkE,EAD3D,CADV;;EAIA,IAAIF,GAAG,CAAC0D,QAAR,EAAkB;IAChBc,MAAM,IAAIxE,GAAG,CAAC0D,QAAd;IACA,IAAI1D,GAAG,CAAC2D,QAAR,EAAkBa,MAAM,IAAI,MAAKxE,GAAG,CAAC2D,QAAnB;IAClBa,MAAM,IAAI,GAAV;EACD,CAJD,MAIO,IAAIxE,GAAG,CAAC2D,QAAR,EAAkB;IACvBa,MAAM,IAAI,MAAKxE,GAAG,CAAC2D,QAAnB;IACAa,MAAM,IAAI,GAAV;EACD,CAHM,MAGA,IACLxE,GAAG,CAACE,QAAJ,KAAiB,OAAjB,IACAD,SAAS,CAACD,GAAG,CAACE,QAAL,CADT,IAEA,CAACsD,IAFD,IAGAxD,GAAG,CAACoB,QAAJ,KAAiB,GAJZ,EAKL;IACA;IACA;IACA;IACA;IACAoD,MAAM,IAAI,GAAV;EACD,CAhC0B,CAkC3B;EACA;EACA;EACA;EACA;;;EACA,IAAIhB,IAAI,CAACA,IAAI,CAACxB,MAAL,GAAc,CAAf,CAAJ,KAA0B,GAA1B,IAAkC1C,IAAI,CAAC+B,IAAL,CAAUrB,GAAG,CAACyD,QAAd,KAA2B,CAACzD,GAAG,CAACV,IAAtE,EAA6E;IAC3EkE,IAAI,IAAI,GAAR;EACD;;EAEDgB,MAAM,IAAIhB,IAAI,GAAGxD,GAAG,CAACoB,QAArB;EAEAb,KAAK,GAAG,aAAa,OAAOP,GAAG,CAACO,KAAxB,GAAgCgE,SAAS,CAACvE,GAAG,CAACO,KAAL,CAAzC,GAAuDP,GAAG,CAACO,KAAnE;EACA,IAAIA,KAAJ,EAAWiE,MAAM,IAAI,QAAQjE,KAAK,CAACgD,MAAN,CAAa,CAAb,CAAR,GAA0B,MAAKhD,KAA/B,GAAuCA,KAAjD;EAEX,IAAIP,GAAG,CAACM,IAAR,EAAckE,MAAM,IAAIxE,GAAG,CAACM,IAAd;EAEd,OAAOkE,MAAP;AACD;;AAEDtD,GAAG,CAACuD,SAAJ,GAAgB;EAAET,GAAG,EAAEA,GAAP;EAAYrE,QAAQ,EAAEA;AAAtB,CAAhB,C,CAEA;AACA;AACA;AACA;;AACAuB,GAAG,CAACM,eAAJ,GAAsBA,eAAtB;AACAN,GAAG,CAACJ,QAAJ,GAAeN,SAAf;AACAU,GAAG,CAACzB,QAAJ,GAAeA,QAAf;AACAyB,GAAG,CAAChC,EAAJ,GAASA,EAAT;AAEAwF,MAAM,CAACC,OAAP,GAAiBzD,GAAjB"}, "metadata": {}, "sourceType": "script"}