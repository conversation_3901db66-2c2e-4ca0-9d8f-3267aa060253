{"ast": null, "code": "import { BYTE } from './byte';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\n\nexport class FrameImpl {\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader\n    } = params;\n    this.command = command;\n    this.headers = Object.assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n  /**\n   * body of the frame\n   */\n\n\n  get body() {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n\n    return this._body;\n  }\n  /**\n   * body as Uint8Array\n   */\n\n\n  get binaryBody() {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n\n    return this._binaryBody;\n  }\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n\n\n  static fromRawFrame(rawFrame, escapeHeaderValues) {\n    const headers = {};\n\n    const trim = str => str.replace(/^\\s+|\\s+$/g, ''); // In case of repeated headers, as per standards, first value need to be used\n\n\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n\n      if (escapeHeaderValues && rawFrame.command !== 'CONNECT' && rawFrame.command !== 'CONNECTED') {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n\n      headers[key] = value;\n    }\n\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues\n    });\n  }\n  /**\n   * @internal\n   */\n\n\n  toString() {\n    return this.serializeCmdAndHeaders();\n  }\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n\n\n  serialize() {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n\n  serializeCmdAndHeaders() {\n    const lines = [this.command];\n\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n\n      if (this.escapeHeaderValues && this.command !== 'CONNECT' && this.command !== 'CONNECTED') {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n\n    if (this.isBinaryBody || !this.isBodyEmpty() && !this.skipContentLengthHeader) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n\n  isBodyEmpty() {\n    return this.bodyLength() === 0;\n  }\n\n  bodyLength() {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n\n\n  static sizeOfUTF8(s) {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n\n  static toUnit8Array(cmdAndHeaders, binaryBody) {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n\n\n  static marshall(params) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n  /**\n   *  Escape header values\n   */\n\n\n  static hdrValueEscape(str) {\n    return str.replace(/\\\\/g, '\\\\\\\\').replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/:/g, '\\\\c');\n  }\n  /**\n   * UnEscape header values\n   */\n\n\n  static hdrValueUnEscape(str) {\n    return str.replace(/\\\\r/g, '\\r').replace(/\\\\n/g, '\\n').replace(/\\\\c/g, ':').replace(/\\\\\\\\/g, '\\\\');\n  }\n\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,IAAT,QAAqB,QAArB;AAKA;;;;;;AAKA,OAAM,MAAOC,SAAP,CAAgB;EAyCpB;;;;;EAKAC,YAAYC,MAAZ,EAOC;IACC,MAAM;MACJC,OADI;MAEJC,OAFI;MAGJC,IAHI;MAIJC,UAJI;MAKJC,kBALI;MAMJC;IANI,IAOFN,MAPJ;IAQA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAgBK,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BN,OAAO,IAAI,EAArC,CAAhB;;IAEA,IAAIE,UAAJ,EAAgB;MACd,KAAKK,WAAL,GAAmBL,UAAnB;MACA,KAAKM,YAAL,GAAoB,IAApB;IACD,CAHD,MAGO;MACL,KAAKC,KAAL,GAAaR,IAAI,IAAI,EAArB;MACA,KAAKO,YAAL,GAAoB,KAApB;IACD;;IACD,KAAKL,kBAAL,GAA0BA,kBAAkB,IAAI,KAAhD;IACA,KAAKC,uBAAL,GAA+BA,uBAAuB,IAAI,KAA1D;EACD;EA1DD;;;;;EAGQ,IAAJH,IAAI;IACN,IAAI,CAAC,KAAKQ,KAAN,IAAe,KAAKD,YAAxB,EAAsC;MACpC,KAAKC,KAAL,GAAa,IAAIC,WAAJ,GAAkBC,MAAlB,CAAyB,KAAKJ,WAA9B,CAAb;IACD;;IACD,OAAO,KAAKE,KAAZ;EACD;EAGD;;;;;EAGc,IAAVP,UAAU;IACZ,IAAI,CAAC,KAAKK,WAAN,IAAqB,CAAC,KAAKC,YAA/B,EAA6C;MAC3C,KAAKD,WAAL,GAAmB,IAAIK,WAAJ,GAAkBC,MAAlB,CAAyB,KAAKJ,KAA9B,CAAnB;IACD;;IACD,OAAO,KAAKF,WAAZ;EACD;EAyCD;;;;;;;EAK0B,OAAZO,YAAY,CACxBC,QADwB,EAExBZ,kBAFwB,EAEG;IAE3B,MAAMH,OAAO,GAAiB,EAA9B;;IACA,MAAMgB,IAAI,GAAIC,GAAD,IAAyBA,GAAG,CAACC,OAAJ,CAAY,YAAZ,EAA0B,EAA1B,CAAtC,CAH2B,CAK3B;;;IACA,KAAK,MAAMC,MAAX,IAAqBJ,QAAQ,CAACf,OAAT,CAAiBoB,OAAjB,EAArB,EAAiD;MAC/C,MAAMC,GAAG,GAAGF,MAAM,CAACG,OAAP,CAAe,GAAf,CAAZ;MAEA,MAAMC,GAAG,GAAGP,IAAI,CAACG,MAAM,CAAC,CAAD,CAAP,CAAhB;MACA,IAAIK,KAAK,GAAGR,IAAI,CAACG,MAAM,CAAC,CAAD,CAAP,CAAhB;;MAEA,IACEhB,kBAAkB,IAClBY,QAAQ,CAAChB,OAAT,KAAqB,SADrB,IAEAgB,QAAQ,CAAChB,OAAT,KAAqB,WAHvB,EAIE;QACAyB,KAAK,GAAG5B,SAAS,CAAC6B,gBAAV,CAA2BD,KAA3B,CAAR;MACD;;MAEDxB,OAAO,CAACuB,GAAD,CAAP,GAAeC,KAAf;IACD;;IAED,OAAO,IAAI5B,SAAJ,CAAc;MACnBG,OAAO,EAAEgB,QAAQ,CAAChB,OADC;MAEnBC,OAFmB;MAGnBE,UAAU,EAAEa,QAAQ,CAACb,UAHF;MAInBC;IAJmB,CAAd,CAAP;EAMD;EAED;;;;;EAGOuB,QAAQ;IACb,OAAO,KAAKC,sBAAL,EAAP;EACD;EAED;;;;;;;;;EAOOC,SAAS;IACd,MAAMC,aAAa,GAAG,KAAKF,sBAAL,EAAtB;;IAEA,IAAI,KAAKnB,YAAT,EAAuB;MACrB,OAAOZ,SAAS,CAACkC,YAAV,CAAuBD,aAAvB,EAAsC,KAAKtB,WAA3C,EAAwDwB,MAA/D;IACD,CAFD,MAEO;MACL,OAAOF,aAAa,GAAG,KAAKpB,KAArB,GAA6Bd,IAAI,CAACqC,IAAzC;IACD;EACF;;EAEOL,sBAAsB;IAC5B,MAAMM,KAAK,GAAG,CAAC,KAAKlC,OAAN,CAAd;;IACA,IAAI,KAAKK,uBAAT,EAAkC;MAChC,OAAO,KAAKJ,OAAL,CAAa,gBAAb,CAAP;IACD;;IAED,KAAK,MAAMkC,IAAX,IAAmB7B,MAAM,CAAC8B,IAAP,CAAY,KAAKnC,OAAL,IAAgB,EAA5B,CAAnB,EAAoD;MAClD,MAAMwB,KAAK,GAAG,KAAKxB,OAAL,CAAakC,IAAb,CAAd;;MACA,IACE,KAAK/B,kBAAL,IACA,KAAKJ,OAAL,KAAiB,SADjB,IAEA,KAAKA,OAAL,KAAiB,WAHnB,EAIE;QACAkC,KAAK,CAACG,IAAN,CAAW,GAAGF,IAAI,IAAItC,SAAS,CAACyC,cAAV,CAAyB,GAAGb,KAAK,EAAjC,CAAoC,EAA1D;MACD,CAND,MAMO;QACLS,KAAK,CAACG,IAAN,CAAW,GAAGF,IAAI,IAAIV,KAAK,EAA3B;MACD;IACF;;IACD,IACE,KAAKhB,YAAL,IACC,CAAC,KAAK8B,WAAL,EAAD,IAAuB,CAAC,KAAKlC,uBAFhC,EAGE;MACA6B,KAAK,CAACG,IAAN,CAAW,kBAAkB,KAAKG,UAAL,EAAiB,EAA9C;IACD;;IACD,OAAON,KAAK,CAACO,IAAN,CAAW7C,IAAI,CAAC8C,EAAhB,IAAsB9C,IAAI,CAAC8C,EAA3B,GAAgC9C,IAAI,CAAC8C,EAA5C;EACD;;EAEOH,WAAW;IACjB,OAAO,KAAKC,UAAL,OAAsB,CAA7B;EACD;;EAEOA,UAAU;IAChB,MAAMrC,UAAU,GAAG,KAAKA,UAAxB;IACA,OAAOA,UAAU,GAAGA,UAAU,CAACwC,MAAd,GAAuB,CAAxC;EACD;EAED;;;;;;EAIyB,OAAVC,UAAU,CAACC,CAAD,EAAU;IACjC,OAAOA,CAAC,GAAG,IAAIhC,WAAJ,GAAkBC,MAAlB,CAAyB+B,CAAzB,EAA4BF,MAA/B,GAAwC,CAAhD;EACD;;EAE0B,OAAZZ,YAAY,CACzBD,aADyB,EAEzB3B,UAFyB,EAEH;IAEtB,MAAM2C,kBAAkB,GAAG,IAAIjC,WAAJ,GAAkBC,MAAlB,CAAyBgB,aAAzB,CAA3B;IACA,MAAMiB,cAAc,GAAG,IAAIC,UAAJ,CAAe,CAAC,CAAD,CAAf,CAAvB;IACA,MAAMC,UAAU,GAAG,IAAID,UAAJ,CACjBF,kBAAkB,CAACH,MAAnB,GAA4BxC,UAAU,CAACwC,MAAvC,GAAgDI,cAAc,CAACJ,MAD9C,CAAnB;IAIAM,UAAU,CAACC,GAAX,CAAeJ,kBAAf;IACAG,UAAU,CAACC,GAAX,CAAe/C,UAAf,EAA2B2C,kBAAkB,CAACH,MAA9C;IACAM,UAAU,CAACC,GAAX,CACEH,cADF,EAEED,kBAAkB,CAACH,MAAnB,GAA4BxC,UAAU,CAACwC,MAFzC;IAKA,OAAOM,UAAP;EACD;EACD;;;;;;;EAKsB,OAARE,QAAQ,CAACpD,MAAD,EAOrB;IACC,MAAMqD,KAAK,GAAG,IAAIvD,SAAJ,CAAcE,MAAd,CAAd;IACA,OAAOqD,KAAK,CAACvB,SAAN,EAAP;EACD;EAED;;;;;EAG6B,OAAdS,cAAc,CAACpB,GAAD,EAAY;IACvC,OAAOA,GAAG,CACPC,OADI,CACI,KADJ,EACW,MADX,EAEJA,OAFI,CAEI,KAFJ,EAEW,KAFX,EAGJA,OAHI,CAGI,KAHJ,EAGW,KAHX,EAIJA,OAJI,CAII,IAJJ,EAIU,KAJV,CAAP;EAKD;EAED;;;;;EAG+B,OAAhBO,gBAAgB,CAACR,GAAD,EAAY;IACzC,OAAOA,GAAG,CACPC,OADI,CACI,MADJ,EACY,IADZ,EAEJA,OAFI,CAEI,MAFJ,EAEY,IAFZ,EAGJA,OAHI,CAGI,MAHJ,EAGY,GAHZ,EAIJA,OAJI,CAII,OAJJ,EAIa,IAJb,CAAP;EAKD;;AA9OmB", "names": ["BYTE", "FrameImpl", "constructor", "params", "command", "headers", "body", "binaryBody", "escapeHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "_binaryBody", "isBinaryBody", "_body", "TextDecoder", "decode", "TextEncoder", "encode", "fromRawFrame", "rawFrame", "trim", "str", "replace", "header", "reverse", "idx", "indexOf", "key", "value", "hdrValueUnEscape", "toString", "serializeCmdAndHeaders", "serialize", "cmdAndHeaders", "toUnit8Array", "buffer", "NULL", "lines", "name", "keys", "push", "hdrValueEscape", "isBodyEmpty", "<PERSON><PERSON><PERSON><PERSON>", "join", "LF", "length", "sizeOfUTF8", "s", "uint8CmdAndHeaders", "nullTerminator", "Uint8Array", "uint8Frame", "set", "marshall", "frame"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\frame-impl.ts"], "sourcesContent": ["import { BYTE } from './byte';\nimport { <PERSON>rame } from './i-frame';\nimport { StompHeaders } from './stomp-headers';\nimport { IRawFrameType } from './types';\n\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl implements IFrame {\n  /**\n   * STOMP Command\n   */\n  public command: string;\n\n  /**\n   * Headers, key value pairs.\n   */\n  public headers: StompHeaders;\n\n  /**\n   * Is this frame binary (based on whether body/binaryBody was passed when creating this frame).\n   */\n  public isBinaryBody: boolean;\n\n  /**\n   * body of the frame\n   */\n  get body(): string {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body;\n  }\n  private _body: string;\n\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody(): Uint8Array {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    return this._binaryBody;\n  }\n  private _binaryBody: Uint8Array;\n\n  private escapeHeaderValues: boolean;\n  private skipContentLengthHeader: boolean;\n\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader,\n    } = params;\n    this.command = command;\n    this.headers = (Object as any).assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  public static fromRawFrame(\n    rawFrame: IRawFrameType,\n    escapeHeaderValues: boolean\n  ): FrameImpl {\n    const headers: StompHeaders = {};\n    const trim = (str: string): string => str.replace(/^\\s+|\\s+$/g, '');\n\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n\n      if (\n        escapeHeaderValues &&\n        rawFrame.command !== 'CONNECT' &&\n        rawFrame.command !== 'CONNECTED'\n      ) {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n\n      headers[key] = value;\n    }\n\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  public toString(): string {\n    return this.serializeCmdAndHeaders();\n  }\n\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  public serialize(): string | ArrayBuffer {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n\n  private serializeCmdAndHeaders(): string {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (\n        this.escapeHeaderValues &&\n        this.command !== 'CONNECT' &&\n        this.command !== 'CONNECTED'\n      ) {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (\n      this.isBinaryBody ||\n      (!this.isBodyEmpty() && !this.skipContentLengthHeader)\n    ) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n\n  private isBodyEmpty(): boolean {\n    return this.bodyLength() === 0;\n  }\n\n  private bodyLength(): number {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  private static sizeOfUTF8(s: string): number {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n\n  private static toUnit8Array(\n    cmdAndHeaders: string,\n    binaryBody: Uint8Array\n  ): Uint8Array {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(\n      uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length\n    );\n\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(\n      nullTerminator,\n      uint8CmdAndHeaders.length + binaryBody.length\n    );\n\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  public static marshall(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n\n  /**\n   *  Escape header values\n   */\n  private static hdrValueEscape(str: string): string {\n    return str\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/:/g, '\\\\c');\n  }\n\n  /**\n   * UnEscape header values\n   */\n  private static hdrValueUnEscape(str: string): string {\n    return str\n      .replace(/\\\\r/g, '\\r')\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\c/g, ':')\n      .replace(/\\\\\\\\/g, '\\\\');\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}