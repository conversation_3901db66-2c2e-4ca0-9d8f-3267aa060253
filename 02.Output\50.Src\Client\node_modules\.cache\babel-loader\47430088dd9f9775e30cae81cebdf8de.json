{"ast": null, "code": "import { useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport depsAreSame from './depsAreSame';\nimport { getTargetElement } from './domTarget';\n\nvar createEffectWithTarget = function createEffectWithTarget(useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function useEffectWithTarget(effect, deps, target) {\n    var hasInitRef = useRef(false);\n    var lastElementRef = useRef([]);\n    var lastDepsRef = useRef([]);\n    var unLoadRef = useRef();\n    useEffectType(function () {\n      var _a;\n\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return getTargetElement(item);\n      }); // init run\n\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n\n      if (els.length !== lastElementRef.current.length || !depsAreSame(els, lastElementRef.current) || !depsAreSame(deps, lastDepsRef.current)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    useUnmount(function () {\n      var _a;\n\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef); // for react-refresh\n\n      hasInitRef.current = false;\n    });\n  };\n\n  return useEffectWithTarget;\n};\n\nexport default createEffectWithTarget;", "map": {"version": 3, "names": ["useRef", "useUnmount", "depsAreSame", "getTargetElement", "createEffectWithTarget", "useEffectType", "useEffectWithTarget", "effect", "deps", "target", "hasInitRef", "lastElementRef", "lastDepsRef", "unLoadRef", "_a", "targets", "Array", "isArray", "els", "map", "item", "current", "length", "call"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/createEffectWithTarget.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport depsAreSame from './depsAreSame';\nimport { getTargetElement } from './domTarget';\nvar createEffectWithTarget = function createEffectWithTarget(useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function useEffectWithTarget(effect, deps, target) {\n    var hasInitRef = useRef(false);\n    var lastElementRef = useRef([]);\n    var lastDepsRef = useRef([]);\n    var unLoadRef = useRef();\n    useEffectType(function () {\n      var _a;\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return getTargetElement(item);\n      });\n      // init run\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n      if (els.length !== lastElementRef.current.length || !depsAreSame(els, lastElementRef.current) || !depsAreSame(deps, lastDepsRef.current)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    useUnmount(function () {\n      var _a;\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n      // for react-refresh\n      hasInitRef.current = false;\n    });\n  };\n  return useEffectWithTarget;\n};\nexport default createEffectWithTarget;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,OAAOC,WAAP,MAAwB,eAAxB;AACA,SAASC,gBAAT,QAAiC,aAAjC;;AACA,IAAIC,sBAAsB,GAAG,SAASA,sBAAT,CAAgCC,aAAhC,EAA+C;EAC1E;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,mBAAmB,GAAG,SAASA,mBAAT,CAA6BC,MAA7B,EAAqCC,IAArC,EAA2CC,MAA3C,EAAmD;IAC3E,IAAIC,UAAU,GAAGV,MAAM,CAAC,KAAD,CAAvB;IACA,IAAIW,cAAc,GAAGX,MAAM,CAAC,EAAD,CAA3B;IACA,IAAIY,WAAW,GAAGZ,MAAM,CAAC,EAAD,CAAxB;IACA,IAAIa,SAAS,GAAGb,MAAM,EAAtB;IACAK,aAAa,CAAC,YAAY;MACxB,IAAIS,EAAJ;;MACA,IAAIC,OAAO,GAAGC,KAAK,CAACC,OAAN,CAAcR,MAAd,IAAwBA,MAAxB,GAAiC,CAACA,MAAD,CAA/C;MACA,IAAIS,GAAG,GAAGH,OAAO,CAACI,GAAR,CAAY,UAAUC,IAAV,EAAgB;QACpC,OAAOjB,gBAAgB,CAACiB,IAAD,CAAvB;MACD,CAFS,CAAV,CAHwB,CAMxB;;MACA,IAAI,CAACV,UAAU,CAACW,OAAhB,EAAyB;QACvBX,UAAU,CAACW,OAAX,GAAqB,IAArB;QACAV,cAAc,CAACU,OAAf,GAAyBH,GAAzB;QACAN,WAAW,CAACS,OAAZ,GAAsBb,IAAtB;QACAK,SAAS,CAACQ,OAAV,GAAoBd,MAAM,EAA1B;QACA;MACD;;MACD,IAAIW,GAAG,CAACI,MAAJ,KAAeX,cAAc,CAACU,OAAf,CAAuBC,MAAtC,IAAgD,CAACpB,WAAW,CAACgB,GAAD,EAAMP,cAAc,CAACU,OAArB,CAA5D,IAA6F,CAACnB,WAAW,CAACM,IAAD,EAAOI,WAAW,CAACS,OAAnB,CAA7G,EAA0I;QACxI,CAACP,EAAE,GAAGD,SAAS,CAACQ,OAAhB,MAA6B,IAA7B,IAAqCP,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACS,IAAH,CAAQV,SAAR,CAA9D;QACAF,cAAc,CAACU,OAAf,GAAyBH,GAAzB;QACAN,WAAW,CAACS,OAAZ,GAAsBb,IAAtB;QACAK,SAAS,CAACQ,OAAV,GAAoBd,MAAM,EAA1B;MACD;IACF,CApBY,CAAb;IAqBAN,UAAU,CAAC,YAAY;MACrB,IAAIa,EAAJ;;MACA,CAACA,EAAE,GAAGD,SAAS,CAACQ,OAAhB,MAA6B,IAA7B,IAAqCP,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACS,IAAH,CAAQV,SAAR,CAA9D,CAFqB,CAGrB;;MACAH,UAAU,CAACW,OAAX,GAAqB,KAArB;IACD,CALS,CAAV;EAMD,CAhCD;;EAiCA,OAAOf,mBAAP;AACD,CAzCD;;AA0CA,eAAeF,sBAAf"}, "metadata": {}, "sourceType": "module"}