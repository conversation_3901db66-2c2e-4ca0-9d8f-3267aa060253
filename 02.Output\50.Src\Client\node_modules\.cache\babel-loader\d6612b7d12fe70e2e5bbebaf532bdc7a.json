{"ast": null, "code": "export * from './client';\nexport * from './frame-impl';\nexport * from './parser';\nexport * from './stomp-config';\nexport * from './stomp-headers';\nexport * from './stomp-subscription';\nexport * from './types';\nexport * from './versions'; // Compatibility code\n\nexport * from './compatibility/compat-client';\nexport * from './compatibility/stomp';", "map": {"version": 3, "mappings": "AAAA,cAAc,UAAd;AACA,cAAc,cAAd;AAGA,cAAc,UAAd;AACA,cAAc,gBAAd;AACA,cAAc,iBAAd;AACA,cAAc,sBAAd;AAEA,cAAc,SAAd;AACA,cAAc,YAAd,C,CAEA;;AACA,cAAc,+BAAd;AACA,cAAc,uBAAd", "names": [], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\index.ts"], "sourcesContent": ["export * from './client';\nexport * from './frame-impl';\nexport * from './i-frame';\nexport * from './i-message';\nexport * from './parser';\nexport * from './stomp-config';\nexport * from './stomp-headers';\nexport * from './stomp-subscription';\nexport * from './i-transaction';\nexport * from './types';\nexport * from './versions';\n\n// Compatibility code\nexport * from './compatibility/compat-client';\nexport * from './compatibility/stomp';\n"]}, "metadata": {}, "sourceType": "module"}