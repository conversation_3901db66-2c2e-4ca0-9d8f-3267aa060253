{"ast": null, "code": "import useRequest from './src/useRequest';\nimport { clearCache } from './src/utils/cache';\nexport { clearCache };\nexport default useRequest;", "map": {"version": 3, "names": ["useRequest", "clearCache"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/index.js"], "sourcesContent": ["import useRequest from './src/useRequest';\nimport { clearCache } from './src/utils/cache';\nexport { clearCache };\nexport default useRequest;"], "mappings": "AAAA,OAAOA,UAAP,MAAuB,kBAAvB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASA,UAAT;AACA,eAAeD,UAAf"}, "metadata": {}, "sourceType": "module"}