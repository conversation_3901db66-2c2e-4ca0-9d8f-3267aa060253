{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    Event = require('./event');\n\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\n\ninherits(TransportMessageEvent, Event);\nmodule.exports = TransportMessageEvent;", "map": {"version": 3, "names": ["inherits", "require", "Event", "TransportMessageEvent", "data", "call", "initEvent", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/event/trans-message.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\n\ninherits(TransportMessageEvent, Event);\n\nmodule.exports = TransportMessageEvent;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,KAAK,GAAGD,OAAO,CAAC,SAAD,CADnB;;AAIA,SAASE,qBAAT,CAA+BC,IAA/B,EAAqC;EACnCF,KAAK,CAACG,IAAN,CAAW,IAAX;EACA,KAAKC,SAAL,CAAe,SAAf,EAA0B,KAA1B,EAAiC,KAAjC;EACA,KAAKF,IAAL,GAAYA,IAAZ;AACD;;AAEDJ,QAAQ,CAACG,qBAAD,EAAwBD,KAAxB,CAAR;AAEAK,MAAM,CAACC,OAAP,GAAiBL,qBAAjB"}, "metadata": {}, "sourceType": "script"}