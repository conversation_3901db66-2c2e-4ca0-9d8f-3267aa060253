{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\n\nvar useThrottlePlugin = function useThrottlePlugin(fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n      throttleLeading = _a.throttleLeading,\n      throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef();\n  var options = {};\n\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options); // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n\n      fetchInstance.runAsync = function () {\n        var args = [];\n\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n\n        return new Promise(function (resolve, reject) {\n          var _a;\n\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve)[\"catch\"](reject);\n          });\n        });\n      };\n\n      return function () {\n        var _a;\n\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n\n  if (!throttleWait) {\n    return {};\n  }\n\n  return {\n    onCancel: function onCancel() {\n      var _a;\n\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\n\nexport default useThrottlePlugin;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "throttle", "useEffect", "useRef", "useThrottlePlugin", "fetchInstance", "_a", "throttleWait", "throttleLeading", "throttleTrailing", "throttledRef", "options", "undefined", "leading", "trailing", "_originRunAsync_1", "runAsync", "bind", "current", "callback", "args", "_i", "Promise", "resolve", "reject", "apply", "then", "cancel", "onCancel"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport throttle from 'lodash/throttle';\nimport { useEffect, useRef } from 'react';\nvar useThrottlePlugin = function useThrottlePlugin(fetchInstance, _a) {\n  var throttleWait = _a.throttleWait,\n    throttleLeading = _a.throttleLeading,\n    throttleTrailing = _a.throttleTrailing;\n  var throttledRef = useRef();\n  var options = {};\n  if (throttleLeading !== undefined) {\n    options.leading = throttleLeading;\n  }\n  if (throttleTrailing !== undefined) {\n    options.trailing = throttleTrailing;\n  }\n  useEffect(function () {\n    if (throttleWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      throttledRef.current = throttle(function (callback) {\n        callback();\n      }, throttleWait, options);\n      // throttle runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.call(throttledRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve)[\"catch\"](reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        fetchInstance.runAsync = _originRunAsync_1;\n        (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n      };\n    }\n  }, [throttleWait, throttleLeading, throttleTrailing]);\n  if (!throttleWait) {\n    return {};\n  }\n  return {\n    onCancel: function onCancel() {\n      var _a;\n      (_a = throttledRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useThrottlePlugin;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,OAAOS,QAAP,MAAqB,iBAArB;AACA,SAASC,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAT,CAA2BC,aAA3B,EAA0CC,EAA1C,EAA8C;EACpE,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAtB;EAAA,IACEC,eAAe,GAAGF,EAAE,CAACE,eADvB;EAAA,IAEEC,gBAAgB,GAAGH,EAAE,CAACG,gBAFxB;EAGA,IAAIC,YAAY,GAAGP,MAAM,EAAzB;EACA,IAAIQ,OAAO,GAAG,EAAd;;EACA,IAAIH,eAAe,KAAKI,SAAxB,EAAmC;IACjCD,OAAO,CAACE,OAAR,GAAkBL,eAAlB;EACD;;EACD,IAAIC,gBAAgB,KAAKG,SAAzB,EAAoC;IAClCD,OAAO,CAACG,QAAR,GAAmBL,gBAAnB;EACD;;EACDP,SAAS,CAAC,YAAY;IACpB,IAAIK,YAAJ,EAAkB;MAChB,IAAIQ,iBAAiB,GAAGV,aAAa,CAACW,QAAd,CAAuBC,IAAvB,CAA4BZ,aAA5B,CAAxB;;MACAK,YAAY,CAACQ,OAAb,GAAuBjB,QAAQ,CAAC,UAAUkB,QAAV,EAAoB;QAClDA,QAAQ;MACT,CAF8B,EAE5BZ,YAF4B,EAEdI,OAFc,CAA/B,CAFgB,CAKhB;MACA;;MACAN,aAAa,CAACW,QAAd,GAAyB,YAAY;QACnC,IAAII,IAAI,GAAG,EAAX;;QACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG3B,SAAS,CAACC,MAAhC,EAAwC0B,EAAE,EAA1C,EAA8C;UAC5CD,IAAI,CAACC,EAAD,CAAJ,GAAW3B,SAAS,CAAC2B,EAAD,CAApB;QACD;;QACD,OAAO,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;UAC5C,IAAIlB,EAAJ;;UACA,CAACA,EAAE,GAAGI,YAAY,CAACQ,OAAnB,MAAgC,IAAhC,IAAwCZ,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACzB,IAAH,CAAQ6B,YAAR,EAAsB,YAAY;YACjGK,iBAAiB,CAACU,KAAlB,CAAwB,KAAK,CAA7B,EAAgCnC,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAAC8C,IAAD,CAAX,EAAmB,KAAnB,CAA7C,EAAwEM,IAAxE,CAA6EH,OAA7E,EAAsF,OAAtF,EAA+FC,MAA/F;UACD,CAFgE,CAAjE;QAGD,CALM,CAAP;MAMD,CAXD;;MAYA,OAAO,YAAY;QACjB,IAAIlB,EAAJ;;QACAD,aAAa,CAACW,QAAd,GAAyBD,iBAAzB;QACA,CAACT,EAAE,GAAGI,YAAY,CAACQ,OAAnB,MAAgC,IAAhC,IAAwCZ,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACqB,MAAH,EAAjE;MACD,CAJD;IAKD;EACF,CA1BQ,EA0BN,CAACpB,YAAD,EAAeC,eAAf,EAAgCC,gBAAhC,CA1BM,CAAT;;EA2BA,IAAI,CAACF,YAAL,EAAmB;IACjB,OAAO,EAAP;EACD;;EACD,OAAO;IACLqB,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5B,IAAItB,EAAJ;;MACA,CAACA,EAAE,GAAGI,YAAY,CAACQ,OAAnB,MAAgC,IAAhC,IAAwCZ,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACqB,MAAH,EAAjE;IACD;EAJI,CAAP;AAMD,CAhDD;;AAiDA,eAAevB,iBAAf"}, "metadata": {}, "sourceType": "module"}