{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _createForOfIteratorHelper from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js\";import React from'react';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource}from'../utils/Util.js';import BlinkBlock,{checkBlinkInfo}from'./elements/BlinkBlock';import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var MAX_ROW=50;var GRID_COLS_EXTENDED_VEHICLE_DEPLOY='16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';var GRID_COLS_EXTENDED_VEHICLE_NODEPLOY='16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */var ExtendedVehicle=function ExtendedVehicle(props){if(!isValidSource(props))return;var showDelopy=props.is_deployment===1;var totalRowCounter=0;var targetArray=[];if(!props.title_name)return;//最大MaxRowのデータを取り出す\nvar _iterator=_createForOfIteratorHelper(props.title_name),_step;try{for(_iterator.s();!(_step=_iterator.n()).done;){var item=_step.value;if(!item.car_name)return;// 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\nvar subRowCounter=item.car_name.length+1;if(totalRowCounter+subRowCounter<=MAX_ROW){var newObj={display_text:item.display_text,text_color:item.text_color,background_color:item.background_color,car_name:item.car_name,town_name:item.town_name,disaster_type:item.disaster_type,avm_dynamic_state:item.avm_dynamic_state,deployment:item.deployment,lighting_setting:item.lighting_setting};targetArray.push(newObj);totalRowCounter=totalRowCounter+subRowCounter;}}}catch(err){_iterator.e(err);}finally{_iterator.f();}var nextStartRow=1;return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 grid-rows-25 grid-flow-col text-[3.2rem] leading-[0.95] gap-x-[2.25rem] gap-y-[0.5rem]\",children:targetArray.map(function(item,index){var _item$car_name;var startRow=nextStartRow;//現在のStart\nnextStartRow=nextStartRow+(item===null||item===void 0?void 0:(_item$car_name=item.car_name)===null||_item$car_name===void 0?void 0:_item$car_name.length)+1;//次のStartRowを計算\nreturn/*#__PURE__*/_jsx(ExtendedStation,_objectSpread(_objectSpread({},item),{},{index:index,showDelopy:showDelopy,startRow:startRow}),index);})})});};/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */var ExtendedStation=function ExtendedStation(props){var gridCol;var subTitleSpan='col-span-full';if(props.showDelopy){gridCol='grid-cols-extended-vehicle-deploy';//gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n}else{gridCol='grid-cols-extended-vehicle-nodeploy';//gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n}var subTitleProp=getCellFace(props,\"\".concat(subTitleSpan,\" flex flex-col items-center\"));return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},subTitleProp),{},{children:props.display_text&&/*#__PURE__*/_jsx(\"span\",{children:props.display_text})}))}),props.car_name.map(function(item,index){return/*#__PURE__*/_jsx(ExtendedVehicleDetailRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})]});};/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */var ExtendedVehicleDetailRow=function ExtendedVehicleDetailRow(props){var showInfoSeperator0={display_text:'',text_color:'',background_color:''};var showInfoSeperator1={display_text:'',text_color:'',background_color:''};var showInfoSeperator2={display_text:'',text_color:'',background_color:''};var showInfoSeperator3={display_text:'',text_color:'',background_color:''};var showInfoSeperator4={display_text:'',text_color:'',background_color:''};var showInfoSeperator5={display_text:'',text_color:'',background_color:''};var showInfoDeployment={display_text:'',text_color:'',background_color:''};var showInfoCarName={display_text:'',text_color:'',background_color:''};var showInfoTownName={display_text:'',text_color:'',background_color:''};var showInfoDisasterType={display_text:'',text_color:'',background_color:''};var showInfoAvmDynamicState={display_text:'',text_color:'',background_color:''};if(props.deployment&&props.deployment[props.index]){showInfoDeployment=props.deployment[props.index];}if(props.car_name&&props.car_name[props.index]){showInfoCarName=props.car_name[props.index];}if(props.town_name&&props.town_name[props.index]){showInfoTownName=props.town_name[props.index];}if(props.disaster_type&&props.disaster_type[props.index]){showInfoDisasterType=props.disaster_type[props.index];}if(props.avm_dynamic_state&&props.avm_dynamic_state[props.index]){showInfoAvmDynamicState=props.avm_dynamic_state[props.index];}//該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\nvar baseObj=props.car_name[props.index];if(!baseObj){baseObj=props.town_name[props.index];}if(!baseObj){baseObj=props.disaster_type[props.index];}if(!baseObj){baseObj=props.avm_dynamic_state[props.index];}if(!baseObj){baseObj=props.deployment[props.index];}showInfoDeployment=checkBlinkInfo(showInfoDeployment,baseObj);showInfoCarName=checkBlinkInfo(showInfoCarName,baseObj);showInfoTownName=checkBlinkInfo(showInfoTownName,baseObj);showInfoDisasterType=checkBlinkInfo(showInfoDisasterType,baseObj);showInfoAvmDynamicState=checkBlinkInfo(showInfoAvmDynamicState,baseObj);showInfoSeperator0=checkBlinkInfo(showInfoSeperator0,baseObj);showInfoSeperator1=checkBlinkInfo(showInfoSeperator1,baseObj);showInfoSeperator2=checkBlinkInfo(showInfoSeperator2,baseObj);showInfoSeperator3=checkBlinkInfo(showInfoSeperator3,baseObj);showInfoSeperator4=checkBlinkInfo(showInfoSeperator4,baseObj);showInfoSeperator5=checkBlinkInfo(showInfoSeperator5,baseObj);var gridCol;var showBlock=[];if(props.showDelopy){gridCol='grid-cols-extended-vehicle-deploy';//gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\nif(props.startRow+props.index<=MAX_ROW/2){//左半分\nshowBlock.push({showInfo:showInfoSeperator0,className:'col-span-1'});}showBlock.push({showInfo:showInfoDeployment,className:'col-span-1 col-start-2'});showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-4'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-9'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-16'});showBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-19'});if(props.startRow+props.index<=MAX_ROW/2){//左半分\nshowBlock.push({showInfo:showInfoSeperator5,className:'col-span-1'});}}else{gridCol='grid-cols-extended-vehicle-nodeploy';//gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\nif(props.startRow+props.index<=MAX_ROW/2){//左半分\nshowBlock.push({showInfo:showInfoSeperator0,className:'col-span-1'});}showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-2'});showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-7'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-14'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-17'});if(props.startRow+props.index<=MAX_ROW/2){//左半分\nshowBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});}}return/*#__PURE__*/_jsxs(_Fragment,{children:[props.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:props.lighting_setting[props.index]})}),!props.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:props.lighting_setting[props.index]})})]});};export default ExtendedVehicle;", "map": {"version": 3, "names": ["React", "CellBox", "getCellFace", "isValidSource", "BlinkBlock", "checkBlinkInfo", "MAX_ROW", "GRID_COLS_EXTENDED_VEHICLE_DEPLOY", "GRID_COLS_EXTENDED_VEHICLE_NODEPLOY", "ExtendedVehicle", "props", "showDelopy", "is_deployment", "totalRowCounter", "targetArray", "title_name", "item", "car_name", "subRowCounter", "length", "newObj", "display_text", "text_color", "background_color", "town_name", "disaster_type", "avm_dynamic_state", "deployment", "lighting_setting", "push", "nextStartRow", "map", "index", "startRow", "ExtendedStation", "gridCol", "subTitleSpan", "subTitleProp", "ExtendedVehicleDetailRow", "showInfoSeperator0", "showInfoSeperator1", "showInfoSeperator2", "showInfoSeperator3", "showInfoSeperator4", "showInfoSeperator5", "showInfoDeployment", "showInfoCarName", "showInfoTownName", "showInfoDisasterType", "showInfoAvmDynamicState", "baseObj", "showBlock", "showInfo", "className"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/ExtendedVehicle.js"], "sourcesContent": ["import React from 'react';\nimport CellBox from './elements/CellBox';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\n\nconst MAX_ROW = 50;\nconst GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\nconst GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\n\n/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */\nconst ExtendedVehicle = (props) => {\n    if (!isValidSource(props)) return;\n\n    let showDelopy = props.is_deployment === 1;\n\n    let totalRowCounter = 0;\n\n    let targetArray = [];\n\n    if (!props.title_name)\n        return;\n    \n    //最大MaxRowのデータを取り出す\n    for (const item of props.title_name) {\n        \n        if (!item.car_name) \n            return;\n\n        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\n        let subRowCounter = item.car_name.length + 1;\n\n        if (totalRowCounter + subRowCounter <= MAX_ROW) {\n            let newObj = {\n                display_text: item.display_text,\n                text_color: item.text_color,\n                background_color: item.background_color,\n                car_name: item.car_name,\n                town_name: item.town_name,\n                disaster_type: item.disaster_type,\n                avm_dynamic_state: item.avm_dynamic_state,\n                deployment: item.deployment,\n                lighting_setting: item.lighting_setting,\n            };\n\n            targetArray.push(newObj);\n            totalRowCounter = totalRowCounter + subRowCounter;\n        }\n    }\n\n    let nextStartRow = 1;\n    return (\n        <>\n            {isValidSource(props) && (\n                <div className=\"grid grid-cols-2 grid-rows-25 grid-flow-col text-[3.2rem] leading-[0.95] gap-x-[2.25rem] gap-y-[0.5rem]\">\n                    {targetArray.map((item, index) => {\n                        let startRow = nextStartRow; //現在のStart\n                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算\n\n                        return (\n                            <ExtendedStation\n                                key={index}\n                                {...item}\n                                index={index}\n                                showDelopy={showDelopy}\n                                startRow={startRow}\n                            />\n                        );\n                    })}\n                </div>\n            )}\n        </>\n    );\n};\n\n/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedStation = (props) => {\n    let gridCol;\n    let subTitleSpan = 'col-span-full';\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n    }\n    const subTitleProp = getCellFace(\n        props,\n        `${subTitleSpan} flex flex-col items-center`\n    );\n\n    return (\n        <>\n            <div className={`grid ${gridCol}`}>\n                <CellBox {...subTitleProp}>\n                    {props.display_text && <span>{props.display_text}</span>}\n                </CellBox>\n            </div>\n            {props.car_name.map((item, index) => {\n                return <ExtendedVehicleDetailRow key={index} {...props} index={index} />;\n            })}\n        </>\n    );\n};\n\n/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedVehicleDetailRow = (props) => {\n    let showInfoSeperator0 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator1 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator2 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator3 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator4 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator5 = { display_text: '', text_color: '', background_color: '' };\n\n    let showInfoDeployment = { display_text: '', text_color: '', background_color: '' };\n    let showInfoCarName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoTownName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoDisasterType = { display_text: '', text_color: '', background_color: '' };\n    let showInfoAvmDynamicState = { display_text: '', text_color: '', background_color: '' };\n\n    if (props.deployment && props.deployment[props.index]) {\n        showInfoDeployment = props.deployment[props.index];\n    }\n\n    if (props.car_name && props.car_name[props.index]) {\n        showInfoCarName = props.car_name[props.index];\n    }\n\n    if (props.town_name && props.town_name[props.index]) {\n        showInfoTownName = props.town_name[props.index];\n    }\n\n    if (props.disaster_type && props.disaster_type[props.index]) {\n        showInfoDisasterType = props.disaster_type[props.index];\n    }\n\n    if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {\n        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\n    }\n\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\n    let baseObj = props.car_name[props.index];\n    if (!baseObj) { baseObj = props.town_name[props.index] }\n    if (!baseObj) { baseObj = props.disaster_type[props.index] }\n    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }\n    if (!baseObj) { baseObj = props.deployment[props.index] }\n\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\n\n    showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);\n    showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);\n    showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);\n    showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);\n    showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);\n    showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);\n\n    let gridCol;\n    let showBlock = [];\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\n        }\n        showBlock.push({\n            showInfo: showInfoDeployment,\n            className: 'col-span-1 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-4',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-9',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-16',\n        });\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-19',\n        });\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\n        }\n\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\n        }\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-7',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-14',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-17',\n        });\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        }\n\n    }\n\n    return (\n        <>\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\n            {props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n            {!props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n        </>\n    );\n};\n\n\n\nexport default ExtendedVehicle;\n"], "mappings": "2SAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,WAAP,EAAqBC,cAArB,KAA2C,uBAA3C,C,6IAEA,GAAMC,QAAO,CAAG,EAAhB,CACA,GAAMC,kCAAiC,CAAG,gKAA1C,CACA,GAAMC,oCAAmC,CAAG,wIAA5C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,gBAAe,CAAG,QAAlBA,gBAAkB,CAACC,KAAD,CAAW,CAC/B,GAAI,CAACP,aAAa,CAACO,KAAD,CAAlB,CAA2B,OAE3B,GAAIC,WAAU,CAAGD,KAAK,CAACE,aAAN,GAAwB,CAAzC,CAEA,GAAIC,gBAAe,CAAG,CAAtB,CAEA,GAAIC,YAAW,CAAG,EAAlB,CAEA,GAAI,CAACJ,KAAK,CAACK,UAAX,CACI,OAEJ;AAZ+B,yCAaZL,KAAK,CAACK,UAbM,YAa/B,+CAAqC,IAA1BC,KAA0B,aAEjC,GAAI,CAACA,IAAI,CAACC,QAAV,CACI,OAEJ;AACA,GAAIC,cAAa,CAAGF,IAAI,CAACC,QAAL,CAAcE,MAAd,CAAuB,CAA3C,CAEA,GAAIN,eAAe,CAAGK,aAAlB,EAAmCZ,OAAvC,CAAgD,CAC5C,GAAIc,OAAM,CAAG,CACTC,YAAY,CAAEL,IAAI,CAACK,YADV,CAETC,UAAU,CAAEN,IAAI,CAACM,UAFR,CAGTC,gBAAgB,CAAEP,IAAI,CAACO,gBAHd,CAITN,QAAQ,CAAED,IAAI,CAACC,QAJN,CAKTO,SAAS,CAAER,IAAI,CAACQ,SALP,CAMTC,aAAa,CAAET,IAAI,CAACS,aANX,CAOTC,iBAAiB,CAAEV,IAAI,CAACU,iBAPf,CAQTC,UAAU,CAAEX,IAAI,CAACW,UARR,CASTC,gBAAgB,CAAEZ,IAAI,CAACY,gBATd,CAAb,CAYAd,WAAW,CAACe,IAAZ,CAAiBT,MAAjB,EACAP,eAAe,CAAGA,eAAe,CAAGK,aAApC,CACH,CACJ,CArC8B,qDAuC/B,GAAIY,aAAY,CAAG,CAAnB,CACA,mBACI,yBACK3B,aAAa,CAACO,KAAD,CAAb,eACG,YAAK,SAAS,CAAC,yGAAf,UACKI,WAAW,CAACiB,GAAZ,CAAgB,SAACf,IAAD,CAAOgB,KAAP,CAAiB,oBAC9B,GAAIC,SAAQ,CAAGH,YAAf,CAA6B;AAC7BA,YAAY,CAAGA,YAAY,EAAGd,IAAH,SAAGA,IAAH,iCAAGA,IAAI,CAAEC,QAAT,yCAAG,eAAgBE,MAAnB,CAAZ,CAAwC,CAAvD,CAA0D;AAE1D,mBACI,KAAC,eAAD,gCAEQH,IAFR,MAGI,KAAK,CAAEgB,KAHX,CAII,UAAU,CAAErB,UAJhB,CAKI,QAAQ,CAAEsB,QALd,GACSD,KADT,CADJ,CASH,CAbA,CADL,EAFR,EADJ,CAsBH,CA9DD,CAgEA;AACA;AACA;AACA;AACA,GACA,GAAME,gBAAe,CAAG,QAAlBA,gBAAkB,CAACxB,KAAD,CAAW,CAC/B,GAAIyB,QAAJ,CACA,GAAIC,aAAY,CAAG,eAAnB,CACA,GAAI1B,KAAK,CAACC,UAAV,CAAsB,CAClBwB,OAAO,CAAG,mCAAV,CACA;AACH,CAHD,IAGO,CACHA,OAAO,CAAG,qCAAV,CACA;AACH,CACD,GAAME,aAAY,CAAGnC,WAAW,CAC5BQ,KAD4B,WAEzB0B,YAFyB,gCAAhC,CAKA,mBACI,wCACI,YAAK,SAAS,gBAAUD,OAAV,CAAd,uBACI,KAAC,OAAD,gCAAaE,YAAb,eACK3B,KAAK,CAACW,YAAN,eAAsB,sBAAOX,KAAK,CAACW,YAAb,EAD3B,GADJ,EADJ,CAMKX,KAAK,CAACO,QAAN,CAAec,GAAf,CAAmB,SAACf,IAAD,CAAOgB,KAAP,CAAiB,CACjC,mBAAO,KAAC,wBAAD,gCAA0CtB,KAA1C,MAAiD,KAAK,CAAEsB,KAAxD,GAA+BA,KAA/B,CAAP,CACH,CAFA,CANL,GADJ,CAYH,CA3BD,CA6BA;AACA;AACA;AACA;AACA,GACA,GAAMM,yBAAwB,CAAG,QAA3BA,yBAA2B,CAAC5B,KAAD,CAAW,CACxC,GAAI6B,mBAAkB,CAAG,CAAElB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAIiB,mBAAkB,CAAG,CAAEnB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAIkB,mBAAkB,CAAG,CAAEpB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAImB,mBAAkB,CAAG,CAAErB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAIoB,mBAAkB,CAAG,CAAEtB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAIqB,mBAAkB,CAAG,CAAEvB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CAEA,GAAIsB,mBAAkB,CAAG,CAAExB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAzB,CACA,GAAIuB,gBAAe,CAAG,CAAEzB,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAtB,CACA,GAAIwB,iBAAgB,CAAG,CAAE1B,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAAvB,CACA,GAAIyB,qBAAoB,CAAG,CAAE3B,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAA3B,CACA,GAAI0B,wBAAuB,CAAG,CAAE5B,YAAY,CAAE,EAAhB,CAAoBC,UAAU,CAAE,EAAhC,CAAoCC,gBAAgB,CAAE,EAAtD,CAA9B,CAEA,GAAIb,KAAK,CAACiB,UAAN,EAAoBjB,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAAxB,CAAuD,CACnDa,kBAAkB,CAAGnC,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAArB,CACH,CAED,GAAItB,KAAK,CAACO,QAAN,EAAkBP,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAtB,CAAmD,CAC/Cc,eAAe,CAAGpC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAlB,CACH,CAED,GAAItB,KAAK,CAACc,SAAN,EAAmBd,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAvB,CAAqD,CACjDe,gBAAgB,CAAGrC,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAnB,CACH,CAED,GAAItB,KAAK,CAACe,aAAN,EAAuBf,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAA3B,CAA6D,CACzDgB,oBAAoB,CAAGtC,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAAvB,CACH,CAED,GAAItB,KAAK,CAACgB,iBAAN,EAA2BhB,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAA/B,CAAqE,CACjEiB,uBAAuB,CAAGvC,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAA1B,CACH,CAED;AACA,GAAIkB,QAAO,CAAGxC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACsB,KAArB,CAAd,CACA,GAAI,CAACkB,OAAL,CAAc,CAAEA,OAAO,CAAGxC,KAAK,CAACc,SAAN,CAAgBd,KAAK,CAACsB,KAAtB,CAAV,CAAwC,CACxD,GAAI,CAACkB,OAAL,CAAc,CAAEA,OAAO,CAAGxC,KAAK,CAACe,aAAN,CAAoBf,KAAK,CAACsB,KAA1B,CAAV,CAA4C,CAC5D,GAAI,CAACkB,OAAL,CAAc,CAAEA,OAAO,CAAGxC,KAAK,CAACgB,iBAAN,CAAwBhB,KAAK,CAACsB,KAA9B,CAAV,CAAgD,CAChE,GAAI,CAACkB,OAAL,CAAc,CAAEA,OAAO,CAAGxC,KAAK,CAACiB,UAAN,CAAiBjB,KAAK,CAACsB,KAAvB,CAAV,CAAyC,CAEzDa,kBAAkB,CAAGxC,cAAc,CAACwC,kBAAD,CAAqBK,OAArB,CAAnC,CACAJ,eAAe,CAAGzC,cAAc,CAACyC,eAAD,CAAkBI,OAAlB,CAAhC,CACAH,gBAAgB,CAAG1C,cAAc,CAAC0C,gBAAD,CAAmBG,OAAnB,CAAjC,CACAF,oBAAoB,CAAG3C,cAAc,CAAC2C,oBAAD,CAAuBE,OAAvB,CAArC,CACAD,uBAAuB,CAAG5C,cAAc,CAAC4C,uBAAD,CAA0BC,OAA1B,CAAxC,CAEAX,kBAAkB,CAAGlC,cAAc,CAACkC,kBAAD,CAAqBW,OAArB,CAAnC,CACAV,kBAAkB,CAAGnC,cAAc,CAACmC,kBAAD,CAAqBU,OAArB,CAAnC,CACAT,kBAAkB,CAAGpC,cAAc,CAACoC,kBAAD,CAAqBS,OAArB,CAAnC,CACAR,kBAAkB,CAAGrC,cAAc,CAACqC,kBAAD,CAAqBQ,OAArB,CAAnC,CACAP,kBAAkB,CAAGtC,cAAc,CAACsC,kBAAD,CAAqBO,OAArB,CAAnC,CACAN,kBAAkB,CAAGvC,cAAc,CAACuC,kBAAD,CAAqBM,OAArB,CAAnC,CAEA,GAAIf,QAAJ,CACA,GAAIgB,UAAS,CAAG,EAAhB,CACA,GAAIzC,KAAK,CAACC,UAAV,CAAsB,CAClBwB,OAAO,CAAG,mCAAV,CACA;AAEA,GAAKzB,KAAK,CAACuB,QAAN,CAAiBvB,KAAK,CAACsB,KAAxB,EAAmC1B,OAAO,CAAG,CAAjD,CAAqD,CAAE;AACnD6C,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEb,kBAAZ,CAAgCc,SAAS,CAAE,YAA3C,CAAf,EACH,CACDF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEP,kBADC,CAEXQ,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEZ,kBAAZ,CAAgCa,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEN,eADC,CAEXO,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEX,kBAAZ,CAAgCY,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEL,gBADC,CAEXM,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEV,kBAAZ,CAAgCW,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEJ,oBADC,CAEXK,SAAS,CAAE,yBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEH,uBADC,CAEXI,SAAS,CAAE,yBAFA,CAAf,EAKA,GAAK3C,KAAK,CAACuB,QAAN,CAAiBvB,KAAK,CAACsB,KAAxB,EAAmC1B,OAAO,CAAG,CAAjD,CAAqD,CAAE;AACnD6C,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAER,kBAAZ,CAAgCS,SAAS,CAAE,YAA3C,CAAf,EACH,CAEJ,CApCD,IAoCO,CACHlB,OAAO,CAAG,qCAAV,CACA;AAEA,GAAKzB,KAAK,CAACuB,QAAN,CAAiBvB,KAAK,CAACsB,KAAxB,EAAmC1B,OAAO,CAAG,CAAjD,CAAqD,CAAE;AACnD6C,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEb,kBAAZ,CAAgCc,SAAS,CAAE,YAA3C,CAAf,EACH,CACDF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEN,eADC,CAEXO,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEZ,kBAAZ,CAAgCa,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEL,gBADC,CAEXM,SAAS,CAAE,wBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEX,kBAAZ,CAAgCY,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEJ,oBADC,CAEXK,SAAS,CAAE,yBAFA,CAAf,EAIAF,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAEV,kBAAZ,CAAgCW,SAAS,CAAE,YAA3C,CAAf,EACAF,SAAS,CAACtB,IAAV,CAAe,CACXuB,QAAQ,CAAEH,uBADC,CAEXI,SAAS,CAAE,yBAFA,CAAf,EAKA,GAAK3C,KAAK,CAACuB,QAAN,CAAiBvB,KAAK,CAACsB,KAAxB,EAAmC1B,OAAO,CAAG,CAAjD,CAAqD,CAAE;AACnD6C,SAAS,CAACtB,IAAV,CAAe,CAAEuB,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACH,CAEJ,CAED,mBACI,2BAEK3C,KAAK,CAACC,UAAN,eACG,YAAK,SAAS,gBAAUwB,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAEgB,SADX,CAEI,aAAa,CAAEzC,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACsB,KAA7B,CAFnB,EADJ,EAHR,CAUK,CAACtB,KAAK,CAACC,UAAP,eACG,YAAK,SAAS,gBAAUwB,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAEgB,SADX,CAEI,aAAa,CAAEzC,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACsB,KAA7B,CAFnB,EADJ,EAXR,GADJ,CAqBH,CAlJD,CAsJA,cAAevB,gBAAf"}, "metadata": {}, "sourceType": "module"}