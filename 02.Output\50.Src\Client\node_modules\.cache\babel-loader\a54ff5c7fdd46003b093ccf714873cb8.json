{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import Cell from'./elements/Cell';import{getCellFace,isValidSource}from'../utils/Util.js';/**\r\n * 出退コンテンツ<br>\r\n * propsは、「3.13出退コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Attendance\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var Attendance=function Attendance(props){var _props$official_posit;var MAX_ROW=14;return/*#__PURE__*/_jsxs(\"div\",{className:\"text-6xl leading-tight\",children:[/*#__PURE__*/_jsx(Title,{title:'出退状況'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-2 grid-rows-7 grid-flow-col gap-4 auto-cols-fr leading-[1] gap-y-[2.7rem] gap-x-[7rem] mt-[3rem]\",children:(_props$official_posit=props.official_position_name)===null||_props$official_posit===void 0?void 0:_props$official_posit.map(function(item,index){if(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(AttendanceRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})})]});};var AttendanceRow=function AttendanceRow(props){if(!props.official_position_name[props.index]||!props.attendance_dynamic_state_name||!props.attendance_dynamic_state_name[props.index]){return;}var cell1=getCellFace(props.official_position_name[props.index],'col-span-6 w-fit');var cell2=getCellFace(props.attendance_dynamic_state_name[props.index],'col-span-2 col-start-8 w-fit');return/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-9 grid-rows-1 auto-cols-fr\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell2))]});};export default Attendance;", "map": {"version": 3, "names": ["React", "Title", "Cell", "getCellFace", "isValidSource", "Attendance", "props", "MAX_ROW", "official_position_name", "map", "item", "index", "undefined", "AttendanceRow", "attendance_dynamic_state_name", "cell1", "cell2"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Attendance.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 出退コンテンツ<br>\r\n * propsは、「3.13出退コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Attendance\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Attendance = (props) => {\r\n  const MAX_ROW = 14;\r\n  return (\r\n    <div className=\"text-6xl leading-tight\">\r\n      <Title title={'出退状況'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-2 grid-rows-7 grid-flow-col gap-4 auto-cols-fr leading-[1] gap-y-[2.7rem] gap-x-[7rem] mt-[3rem]\">\r\n          {props.official_position_name?.map((item, index) => {\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <AttendanceRow key={index} {...props} index={index} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AttendanceRow = (props) => {\r\n  if (\r\n    !props.official_position_name[props.index] ||\r\n    !props.attendance_dynamic_state_name || \r\n    !props.attendance_dynamic_state_name[props.index]\r\n  ) {\r\n    return;\r\n  }\r\n\r\n  let cell1 = getCellFace(\r\n    props.official_position_name[props.index],\r\n    'col-span-6 w-fit'\r\n  );\r\n  let cell2 = getCellFace(\r\n    props.attendance_dynamic_state_name[props.index],\r\n    'col-span-2 col-start-8 w-fit'\r\n  );\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-9 grid-rows-1 auto-cols-fr\">\r\n      <Cell {...cell1} />\r\n      <Cell {...cell2} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Attendance;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,wFACA,GAAMC,WAAU,CAAG,QAAbA,WAAa,CAACC,KAAD,CAAW,2BAC5B,GAAMC,QAAO,CAAG,EAAhB,CACA,mBACE,aAAK,SAAS,CAAC,wBAAf,wBACE,KAAC,KAAD,EAAO,KAAK,CAAE,MAAd,EADF,CAEGH,aAAa,CAACE,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,oJAAf,iCACGA,KAAK,CAACE,sBADT,gDACG,sBAA8BC,GAA9B,CAAkC,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAClD,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,aAAD,gCAA+BN,KAA/B,MAAsC,KAAK,CAAEK,KAA7C,GAAoBA,KAApB,CAAP,CACD,CAJA,CADH,EAHJ,GADF,CAcD,CAhBD,CAkBA,GAAME,cAAa,CAAG,QAAhBA,cAAgB,CAACP,KAAD,CAAW,CAC/B,GACE,CAACA,KAAK,CAACE,sBAAN,CAA6BF,KAAK,CAACK,KAAnC,CAAD,EACA,CAACL,KAAK,CAACQ,6BADP,EAEA,CAACR,KAAK,CAACQ,6BAAN,CAAoCR,KAAK,CAACK,KAA1C,CAHH,CAIE,CACA,OACD,CAED,GAAII,MAAK,CAAGZ,WAAW,CACrBG,KAAK,CAACE,sBAAN,CAA6BF,KAAK,CAACK,KAAnC,CADqB,CAErB,kBAFqB,CAAvB,CAIA,GAAIK,MAAK,CAAGb,WAAW,CACrBG,KAAK,CAACQ,6BAAN,CAAoCR,KAAK,CAACK,KAA1C,CADqB,CAErB,8BAFqB,CAAvB,CAKA,mBACE,aAAK,SAAS,CAAC,2CAAf,wBACE,KAAC,IAAD,kBAAUI,KAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,KAAV,EAFF,GADF,CAMD,CAxBD,CA0BA,cAAeX,WAAf"}, "metadata": {}, "sourceType": "module"}