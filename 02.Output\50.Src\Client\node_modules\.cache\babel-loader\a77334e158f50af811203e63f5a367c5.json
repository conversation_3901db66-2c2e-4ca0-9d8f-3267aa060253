{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import Title from'./elements/Title';import{getCellFace,isValidSource}from'../utils/Util.js';import BlinkBlock from'./elements/BlinkBlock';/**\r\n * デジタル無線コンテンツ<br>\r\n * propsは、「3.17デジタル無線コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module DigitalRadio\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var DigitalRadio=function DigitalRadio(props){var MAX_ROW=4;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Title,{title:'デジタル無線通信状況'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"grid min-h-full grid-rows-2 grid-cols-2 place-content-stretch text-5xl leading-[1]\",children:props.wireless_channel_name.map(function(item,index){if(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(DigitalRadioSubPart,_objectSpread(_objectSpread({},item),{},{index:index}),index);})})]});};var DigitalRadioSubPart=function DigitalRadioSubPart(props){var MAX_ROW=2;var borderStyle='';if(props.index%2===1){borderStyle='border-l-2';}var appendGridClassName;if(props.index%2==0){appendGridClassName='grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)]';}else{appendGridClassName='grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)_1rem]';}var row1Cell1Props=getCellFace(props,\"col-span-full justify-self-center text-[4.2rem] w-fit\");return/*#__PURE__*/_jsxs(\"div\",{className:\"grid content-start \".concat(appendGridClassName,\" \").concat(borderStyle,\" gap-y-10 pt-2\"),children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell1Props)),props.outgoing_call_move_station_name&&props.outgoing_call_move_station_name.map(function(item,index){if(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(RadioDetailRow,_objectSpread(_objectSpread({},props),{},{index:index,rightPadding:props.index%2==1}),index);})]});};// デジタル無線コンテンツ\nvar RadioDetailRow=function RadioDetailRow(props){var showInfo1=props.outgoing_call_move_station_name[props.index];var showInfoSeperator1=_objectSpread({},showInfo1);showInfoSeperator1.display_text=' ';var showInfoSeperator0=_objectSpread({},showInfoSeperator1);var showInfo2={};var showInfoSeperator2={};if(props.incoming_call_move_station_name&&props.incoming_call_move_station_name[props.index]){showInfo2=props.incoming_call_move_station_name[props.index];showInfoSeperator2=_objectSpread({},showInfo2);showInfoSeperator2.display_text=' ';}var showInfo3={};if(props.incoming_call_ts&&props.incoming_call_ts[props.index]){showInfo3=props.incoming_call_ts[props.index];}var showInfoSeperator3=_objectSpread({},showInfo3);showInfoSeperator3.display_text=' ';var showBlock=[];/* 1行で点滅する為、仕様より、Span+1(隙間列)にする */showBlock.push({showInfo:showInfoSeperator0,className:'col-span-1'});showBlock.push({showInfo:showInfo1,className:'col-span-4 col-start-2'});showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfo2,className:'col-span-3 col-start-7'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfo3,className:'col-span-5 col-start-11'});if(props.rightPadding){showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});}return/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:props.blink_setting?props.blink_setting[props.index]:{}});};export default DigitalRadio;", "map": {"version": 3, "names": ["React", "Cell", "Title", "getCellFace", "isValidSource", "BlinkBlock", "DigitalRadio", "props", "MAX_ROW", "wireless_channel_name", "map", "item", "index", "undefined", "DigitalRadioSubPart", "borderStyle", "appendGridClassName", "row1Cell1Props", "outgoing_call_move_station_name", "RadioDetailRow", "showInfo1", "showInfoSeperator1", "display_text", "showInfoSeperator0", "showInfo2", "showInfoSeperator2", "incoming_call_move_station_name", "showInfo3", "incoming_call_ts", "showInfoSeperator3", "showBlock", "push", "showInfo", "className", "rightPadding", "blink_setting"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/DigitalRadio.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport Title from './elements/Title';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport BlinkBlock from './elements/BlinkBlock';\r\n\r\n/**\r\n * デジタル無線コンテンツ<br>\r\n * propsは、「3.17デジタル無線コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module DigitalRadio\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst DigitalRadio = (props) => {\r\n  const MAX_ROW = 4;\r\n  return (\r\n    <>\r\n      <Title title={'デジタル無線通信状況'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"grid min-h-full grid-rows-2 grid-cols-2 place-content-stretch text-5xl leading-[1]\">\r\n          {props.wireless_channel_name.map((item, index) => {\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <DigitalRadioSubPart key={index} {...item} index={index} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nconst DigitalRadioSubPart = (props) => {\r\n  const MAX_ROW = 2;\r\n\r\n  let borderStyle = '';\r\n  if (props.index % 2 === 1) {\r\n    borderStyle = 'border-l-2';\r\n  }\r\n  \r\n  let appendGridClassName;\r\n\r\n  if (props.index % 2 == 0) {\r\n    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)]'\r\n  } else {\r\n    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)_1rem]'\r\n  }\r\n  let row1Cell1Props = getCellFace(\r\n      props,\r\n      `col-span-full justify-self-center text-[4.2rem] w-fit`\r\n  );\r\n  return (\r\n    <div\r\n      className={`grid content-start ${appendGridClassName} ${borderStyle} gap-y-10 pt-2`}\r\n    >\r\n      <Cell {...row1Cell1Props} />\r\n\r\n      {props.outgoing_call_move_station_name &&\r\n        props.outgoing_call_move_station_name.map((item, index) => {\r\n          if (index >= MAX_ROW) return undefined;\r\n\r\n          return <RadioDetailRow key={index} {...props} index={index} rightPadding={props.index % 2 == 1}/>;\r\n        })}\r\n    </div>\r\n  );\r\n};\r\n\r\n// デジタル無線コンテンツ\r\nconst RadioDetailRow = (props) => {\r\n  let showInfo1 = props.outgoing_call_move_station_name[props.index];\r\n  let showInfoSeperator1 = { ...showInfo1 };\r\n  showInfoSeperator1.display_text = ' ';\r\n  let showInfoSeperator0 = { ...showInfoSeperator1 };\r\n\r\n  let showInfo2 = {};\r\n  let showInfoSeperator2 = {};\r\n  if (\r\n    props.incoming_call_move_station_name &&\r\n    props.incoming_call_move_station_name[props.index]\r\n  ) {\r\n    showInfo2 = props.incoming_call_move_station_name[props.index];\r\n    showInfoSeperator2 = { ...showInfo2 };\r\n    showInfoSeperator2.display_text = ' ';\r\n  }\r\n\r\n  let showInfo3 = {};\r\n  if (props.incoming_call_ts && props.incoming_call_ts[props.index]) {\r\n    showInfo3 = props.incoming_call_ts[props.index];\r\n  }\r\n  let showInfoSeperator3 = { ...showInfo3 };\r\n  showInfoSeperator3.display_text = ' ';\r\n\r\n  let showBlock = [];\r\n  /* 1行で点滅する為、仕様より、Span+1(隙間列)にする */\r\n  showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo1, className: 'col-span-4 col-start-2' });\r\n  showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo2, className: 'col-span-3 col-start-7' });\r\n  showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n  showBlock.push({ showInfo: showInfo3, className: 'col-span-5 col-start-11' });\r\n  if (props.rightPadding) {\r\n    showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n  }\r\n\r\n  return (\r\n    <BlinkBlock\r\n      block={showBlock}\r\n      blink_setting={\r\n        props.blink_setting ? props.blink_setting[props.index] : {}\r\n      }\r\n    />\r\n  );\r\n};\r\n\r\nexport default DigitalRadio;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,aAAY,CAAG,QAAfA,aAAe,CAACC,KAAD,CAAW,CAC9B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,wCACE,KAAC,KAAD,EAAO,KAAK,CAAE,YAAd,EADF,CAEGJ,aAAa,CAACG,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,oFAAf,UACGA,KAAK,CAACE,qBAAN,CAA4BC,GAA5B,CAAgC,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAChD,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,mBAAD,gCAAqCF,IAArC,MAA2C,KAAK,CAAEC,KAAlD,GAA0BA,KAA1B,CAAP,CACD,CAJA,CADH,EAHJ,GADF,CAcD,CAhBD,CAkBA,GAAME,oBAAmB,CAAG,QAAtBA,oBAAsB,CAACP,KAAD,CAAW,CACrC,GAAMC,QAAO,CAAG,CAAhB,CAEA,GAAIO,YAAW,CAAG,EAAlB,CACA,GAAIR,KAAK,CAACK,KAAN,CAAc,CAAd,GAAoB,CAAxB,CAA2B,CACzBG,WAAW,CAAG,YAAd,CACD,CAED,GAAIC,oBAAJ,CAEA,GAAIT,KAAK,CAACK,KAAN,CAAc,CAAd,EAAmB,CAAvB,CAA0B,CACxBI,mBAAmB,CAAG,wGAAtB,CACD,CAFD,IAEO,CACLA,mBAAmB,CAAG,6GAAtB,CACD,CACD,GAAIC,eAAc,CAAGd,WAAW,CAC5BI,KAD4B,yDAAhC,CAIA,mBACE,aACE,SAAS,8BAAwBS,mBAAxB,aAA+CD,WAA/C,kBADX,wBAGE,KAAC,IAAD,kBAAUE,cAAV,EAHF,CAKGV,KAAK,CAACW,+BAAN,EACCX,KAAK,CAACW,+BAAN,CAAsCR,GAAtC,CAA0C,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACzD,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,cAAD,gCAAgCN,KAAhC,MAAuC,KAAK,CAAEK,KAA9C,CAAqD,YAAY,CAAEL,KAAK,CAACK,KAAN,CAAc,CAAd,EAAmB,CAAtF,GAAqBA,KAArB,CAAP,CACD,CAJD,CANJ,GADF,CAcD,CAjCD,CAmCA;AACA,GAAMO,eAAc,CAAG,QAAjBA,eAAiB,CAACZ,KAAD,CAAW,CAChC,GAAIa,UAAS,CAAGb,KAAK,CAACW,+BAAN,CAAsCX,KAAK,CAACK,KAA5C,CAAhB,CACA,GAAIS,mBAAkB,kBAAQD,SAAR,CAAtB,CACAC,kBAAkB,CAACC,YAAnB,CAAkC,GAAlC,CACA,GAAIC,mBAAkB,kBAAQF,kBAAR,CAAtB,CAEA,GAAIG,UAAS,CAAG,EAAhB,CACA,GAAIC,mBAAkB,CAAG,EAAzB,CACA,GACElB,KAAK,CAACmB,+BAAN,EACAnB,KAAK,CAACmB,+BAAN,CAAsCnB,KAAK,CAACK,KAA5C,CAFF,CAGE,CACAY,SAAS,CAAGjB,KAAK,CAACmB,+BAAN,CAAsCnB,KAAK,CAACK,KAA5C,CAAZ,CACAa,kBAAkB,kBAAQD,SAAR,CAAlB,CACAC,kBAAkB,CAACH,YAAnB,CAAkC,GAAlC,CACD,CAED,GAAIK,UAAS,CAAG,EAAhB,CACA,GAAIpB,KAAK,CAACqB,gBAAN,EAA0BrB,KAAK,CAACqB,gBAAN,CAAuBrB,KAAK,CAACK,KAA7B,CAA9B,CAAmE,CACjEe,SAAS,CAAGpB,KAAK,CAACqB,gBAAN,CAAuBrB,KAAK,CAACK,KAA7B,CAAZ,CACD,CACD,GAAIiB,mBAAkB,kBAAQF,SAAR,CAAtB,CACAE,kBAAkB,CAACP,YAAnB,CAAkC,GAAlC,CAEA,GAAIQ,UAAS,CAAG,EAAhB,CACA,kCACAA,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEZ,SAAZ,CAAuBa,SAAS,CAAE,wBAAlC,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEX,kBAAZ,CAAgCY,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAER,SAAZ,CAAuBS,SAAS,CAAE,wBAAlC,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEP,kBAAZ,CAAgCQ,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEL,SAAZ,CAAuBM,SAAS,CAAE,yBAAlC,CAAf,EACA,GAAI1B,KAAK,CAAC2B,YAAV,CAAwB,CACtBJ,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEH,kBAAZ,CAAgCI,SAAS,CAAE,YAA3C,CAAf,EACD,CAED,mBACE,KAAC,UAAD,EACE,KAAK,CAAEH,SADT,CAEE,aAAa,CACXvB,KAAK,CAAC4B,aAAN,CAAsB5B,KAAK,CAAC4B,aAAN,CAAoB5B,KAAK,CAACK,KAA1B,CAAtB,CAAyD,EAH7D,EADF,CAQD,CA5CD,CA8CA,cAAeN,aAAf"}, "metadata": {}, "sourceType": "module"}