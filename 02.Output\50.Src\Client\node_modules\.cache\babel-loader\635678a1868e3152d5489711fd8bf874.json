{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\n\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\n\nexport default useTitle;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmount", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "restoreOnUnmount", "useTitle", "title", "options", "titleRef", "document", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useTitle/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\nexport default useTitle;"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,IAAIC,eAAe,GAAG;EACpBC,gBAAgB,EAAE;AADE,CAAtB;;AAGA,SAASC,QAAT,CAAkBC,KAAlB,EAAyBC,OAAzB,EAAkC;EAChC,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAGJ,eAAV;EACD;;EACD,IAAIK,QAAQ,GAAGR,MAAM,CAACE,SAAS,GAAGO,QAAQ,CAACH,KAAZ,GAAoB,EAA9B,CAArB;EACAP,SAAS,CAAC,YAAY;IACpBU,QAAQ,CAACH,KAAT,GAAiBA,KAAjB;EACD,CAFQ,EAEN,CAACA,KAAD,CAFM,CAAT;EAGAL,UAAU,CAAC,YAAY;IACrB,IAAIM,OAAO,CAACH,gBAAZ,EAA8B;MAC5BK,QAAQ,CAACH,KAAT,GAAiBE,QAAQ,CAACE,OAA1B;IACD;EACF,CAJS,CAAV;AAKD;;AACD,eAAeL,QAAf"}, "metadata": {}, "sourceType": "module"}