{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";var _templateObject;import React,{useState,useRef,useEffect,useLayoutEffect}from'react';import{replaceWithHtmlSpace}from'../../utils/Util.js';import styled from'styled-components';import{jsx as _jsx}from\"react/jsx-runtime\";var ScaleBlock=function ScaleBlock(props){var defaultState={scale:1,translate:0};var _useState=useState(defaultState),_useState2=_slicedToArray(_useState,2),state=_useState2[0],setState=_useState2[1];var _useWindowSize=useWindowSize(),_useWindowSize2=_slicedToArray(_useWindowSize,2),width=_useWindowSize2[0],height=_useWindowSize2[1];var ref=useRef(null);useEffect(function(){var _ref$current=ref.current,offsetWidth=_ref$current.offsetWidth,parentElement=_ref$current.parentElement;var contentWidth=offsetWidth;var boxWidth=parentElement===null||parentElement===void 0?void 0:parentElement.offsetWidth;var tempScale=1;if(contentWidth&&boxWidth){tempScale=boxWidth/contentWidth;if(tempScale<1){setState({scale:tempScale,translate:(1-tempScale)/2*100});}}},[props.display_text,width,height]);var refinedText=replaceWithHtmlSpace((props===null||props===void 0?void 0:props.display_text)||(props===null||props===void 0?void 0:props.text));return/*#__PURE__*/_jsx(StyledSpan,{ref:ref,dangerouslySetInnerHTML:{__html:refinedText},scale:state.scale,translate:state.translate});};var StyledSpan=styled.span(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  white-space: nowrap;\\n  display: inline-block;\\n  transform: scaleX(\",\");\\n  translate: -\",\"%;\\n\"])),function(props){return props.scale;},function(props){return props.translate;});function useWindowSize(){var _useState3=useState([0,0]),_useState4=_slicedToArray(_useState3,2),size=_useState4[0],setSize=_useState4[1];useLayoutEffect(function(){function updateSize(){setSize([window.innerWidth,window.innerHeight]);}window.addEventListener('resize',updateSize);updateSize();return function(){return window.removeEventListener('resize',updateSize);};},[]);return size;}export default ScaleBlock;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useLayoutEffect", "replaceWithHtmlSpace", "styled", "ScaleBlock", "props", "defaultState", "scale", "translate", "state", "setState", "useWindowSize", "width", "height", "ref", "current", "offsetWidth", "parentElement", "contentWidth", "boxWidth", "tempScale", "display_text", "refinedText", "text", "__html", "StyledSpan", "span", "size", "setSize", "updateSize", "window", "innerWidth", "innerHeight", "addEventListener", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/ScaleBlock.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';\r\nimport { replaceWithHtmlSpace } from '../../utils/Util.js';\r\nimport PropTypes from 'prop-types';\r\nimport styled from 'styled-components';\r\n\r\nconst propTypes = {\r\n  display_text: PropTypes.string,\r\n};\r\n\r\nconst ScaleBlock = (props) => {\r\n  const defaultState = {\r\n    scale: 1,\r\n    translate: 0,\r\n  };\r\n\r\n  const [state, setState] = useState(defaultState);\r\n  const [width, height] = useWindowSize();\r\n\r\n  let ref = useRef(null);\r\n  useEffect(() => {\r\n    const { offsetWidth, parentElement } = ref.current;\r\n    let contentWidth = offsetWidth;\r\n    let boxWidth = parentElement?.offsetWidth;\r\n\r\n    let tempScale = 1;\r\n    if (contentWidth && boxWidth) {\r\n      tempScale = boxWidth / contentWidth;\r\n\r\n      if (tempScale < 1) {\r\n        setState({\r\n          scale: tempScale,\r\n          translate: ((1 - tempScale) / 2) * 100,\r\n        });\r\n      }\r\n    }\r\n  }, [props.display_text, width, height]);\r\n\r\n  let refinedText = replaceWithHtmlSpace(props?.display_text || props?.text);\r\n\r\n  return (\r\n    <StyledSpan\r\n      ref={ref}\r\n      dangerouslySetInnerHTML={{ __html: refinedText }}\r\n      scale={state.scale}\r\n      translate={state.translate}\r\n    ></StyledSpan>\r\n  );\r\n};\r\n\r\nconst StyledSpan = styled.span`\r\n  white-space: nowrap;\r\n  display: inline-block;\r\n  transform: scaleX(${(props) => props.scale});\r\n  translate: -${(props) => props.translate}%;\r\n`;\r\n\r\nfunction useWindowSize() {\r\n  const [size, setSize] = useState([0, 0]);\r\n  useLayoutEffect(() => {\r\n    function updateSize() {\r\n      setSize([window.innerWidth, window.innerHeight]);\r\n    }\r\n    window.addEventListener('resize', updateSize);\r\n    updateSize();\r\n    return () => window.removeEventListener('resize', updateSize);\r\n  }, []);\r\n  return size;\r\n}\r\n\r\nScaleBlock.propTypes = propTypes;\r\nexport default ScaleBlock;\r\n"], "mappings": "wTAAA,MAAOA,MAAP,EAAgBC,QAAhB,CAA0BC,MAA1B,CAAkCC,SAAlC,CAA6CC,eAA7C,KAAoE,OAApE,CACA,OAASC,oBAAT,KAAqC,qBAArC,CAEA,MAAOC,OAAP,KAAmB,mBAAnB,C,2CAMA,GAAMC,WAAU,CAAG,QAAbA,WAAa,CAACC,KAAD,CAAW,CAC5B,GAAMC,aAAY,CAAG,CACnBC,KAAK,CAAE,CADY,CAEnBC,SAAS,CAAE,CAFQ,CAArB,CAKA,cAA0BV,QAAQ,CAACQ,YAAD,CAAlC,wCAAOG,KAAP,eAAcC,QAAd,eACA,mBAAwBC,aAAa,EAArC,kDAAOC,KAAP,oBAAcC,MAAd,oBAEA,GAAIC,IAAG,CAAGf,MAAM,CAAC,IAAD,CAAhB,CACAC,SAAS,CAAC,UAAM,CACd,iBAAuCc,GAAG,CAACC,OAA3C,CAAQC,WAAR,cAAQA,WAAR,CAAqBC,aAArB,cAAqBA,aAArB,CACA,GAAIC,aAAY,CAAGF,WAAnB,CACA,GAAIG,SAAQ,CAAGF,aAAH,SAAGA,aAAH,iBAAGA,aAAa,CAAED,WAA9B,CAEA,GAAII,UAAS,CAAG,CAAhB,CACA,GAAIF,YAAY,EAAIC,QAApB,CAA8B,CAC5BC,SAAS,CAAGD,QAAQ,CAAGD,YAAvB,CAEA,GAAIE,SAAS,CAAG,CAAhB,CAAmB,CACjBV,QAAQ,CAAC,CACPH,KAAK,CAAEa,SADA,CAEPZ,SAAS,CAAG,CAAC,EAAIY,SAAL,EAAkB,CAAnB,CAAwB,GAF5B,CAAD,CAAR,CAID,CACF,CACF,CAhBQ,CAgBN,CAACf,KAAK,CAACgB,YAAP,CAAqBT,KAArB,CAA4BC,MAA5B,CAhBM,CAAT,CAkBA,GAAIS,YAAW,CAAGpB,oBAAoB,CAAC,CAAAG,KAAK,OAAL,EAAAA,KAAK,SAAL,QAAAA,KAAK,CAAEgB,YAAP,IAAuBhB,KAAvB,SAAuBA,KAAvB,iBAAuBA,KAAK,CAAEkB,IAA9B,CAAD,CAAtC,CAEA,mBACE,KAAC,UAAD,EACE,GAAG,CAAET,GADP,CAEE,uBAAuB,CAAE,CAAEU,MAAM,CAAEF,WAAV,CAF3B,CAGE,KAAK,CAAEb,KAAK,CAACF,KAHf,CAIE,SAAS,CAAEE,KAAK,CAACD,SAJnB,EADF,CAQD,CAtCD,CAwCA,GAAMiB,WAAU,CAAGtB,MAAM,CAACuB,IAAV,qKAGM,SAACrB,KAAD,QAAWA,MAAK,CAACE,KAAjB,EAHN,CAIA,SAACF,KAAD,QAAWA,MAAK,CAACG,SAAjB,EAJA,CAAhB,CAOA,QAASG,cAAT,EAAyB,CACvB,eAAwBb,QAAQ,CAAC,CAAC,CAAD,CAAI,CAAJ,CAAD,CAAhC,yCAAO6B,IAAP,eAAaC,OAAb,eACA3B,eAAe,CAAC,UAAM,CACpB,QAAS4B,WAAT,EAAsB,CACpBD,OAAO,CAAC,CAACE,MAAM,CAACC,UAAR,CAAoBD,MAAM,CAACE,WAA3B,CAAD,CAAP,CACD,CACDF,MAAM,CAACG,gBAAP,CAAwB,QAAxB,CAAkCJ,UAAlC,EACAA,UAAU,GACV,MAAO,kBAAMC,OAAM,CAACI,mBAAP,CAA2B,QAA3B,CAAqCL,UAArC,CAAN,EAAP,CACD,CAPc,CAOZ,EAPY,CAAf,CAQA,MAAOF,KAAP,CACD,CAGD,cAAevB,WAAf"}, "metadata": {}, "sourceType": "module"}