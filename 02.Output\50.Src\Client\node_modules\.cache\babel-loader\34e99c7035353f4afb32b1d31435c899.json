{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n    visitor: function visitor(value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n\n      return helpers.defaultVisitor.apply(this, arguments);\n    }\n  }, options));\n}", "map": {"version": 3, "names": ["utils", "toFormData", "platform", "toURLEncodedForm", "data", "options", "classes", "URLSearchParams", "Object", "assign", "visitor", "value", "key", "path", "helpers", "isNode", "<PERSON><PERSON><PERSON><PERSON>", "append", "toString", "defaultVisitor", "apply", "arguments"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/toURLEncodedForm.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\n\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n    visitor: function(value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n\n      return helpers.defaultVisitor.apply(this, arguments);\n    }\n  }, options));\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AAEA,eAAe,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyC;EACtD,OAAOJ,UAAU,CAACG,IAAD,EAAO,IAAIF,QAAQ,CAACI,OAAT,CAAiBC,eAArB,EAAP,EAA+CC,MAAM,CAACC,MAAP,CAAc;IAC5EC,OAAO,EAAE,iBAASC,KAAT,EAAgBC,GAAhB,EAAqBC,IAArB,EAA2BC,OAA3B,EAAoC;MAC3C,IAAIZ,QAAQ,CAACa,MAAT,IAAmBf,KAAK,CAACgB,QAAN,CAAeL,KAAf,CAAvB,EAA8C;QAC5C,KAAKM,MAAL,CAAYL,GAAZ,EAAiBD,KAAK,CAACO,QAAN,CAAe,QAAf,CAAjB;QACA,OAAO,KAAP;MACD;;MAED,OAAOJ,OAAO,CAACK,cAAR,CAAuBC,KAAvB,CAA6B,IAA7B,EAAmCC,SAAnC,CAAP;IACD;EAR2E,CAAd,EAS7DhB,OAT6D,CAA/C,CAAjB;AAUD"}, "metadata": {}, "sourceType": "module"}