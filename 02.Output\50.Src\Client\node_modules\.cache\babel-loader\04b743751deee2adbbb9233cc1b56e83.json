{"ast": null, "code": "function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport default memoize;", "map": {"version": 3, "names": ["memoize", "fn", "cache", "Object", "create", "arg", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@emotion/memoize/dist/emotion-memoize.esm.js"], "sourcesContent": ["function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport default memoize;\n"], "mappings": "AAAA,SAASA,OAAT,CAAiBC,EAAjB,EAAqB;EACnB,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;EACA,OAAO,UAAUC,GAAV,EAAe;IACpB,IAAIH,KAAK,CAACG,GAAD,CAAL,KAAeC,SAAnB,EAA8BJ,KAAK,CAACG,GAAD,CAAL,GAAaJ,EAAE,CAACI,GAAD,CAAf;IAC9B,OAAOH,KAAK,CAACG,GAAD,CAAZ;EACD,CAHD;AAID;;AAED,eAAeL,OAAf"}, "metadata": {}, "sourceType": "module"}