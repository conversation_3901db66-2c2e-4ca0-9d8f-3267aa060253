{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\n\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && payload.isAxiosError === true;\n}", "map": {"version": 3, "names": ["utils", "isAxiosError", "payload", "isObject"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/isAxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && (payload.isAxiosError === true);\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,YAAT,CAAsBC,OAAtB,EAA+B;EAC5C,OAAOF,KAAK,CAACG,QAAN,CAAeD,OAAf,KAA4BA,OAAO,CAACD,YAAR,KAAyB,IAA5D;AACD"}, "metadata": {}, "sourceType": "module"}