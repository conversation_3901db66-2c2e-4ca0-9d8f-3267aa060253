{"ast": null, "code": "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';", "map": {"version": 3, "names": ["getBuiltIn", "require", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/core-js-pure/internals/engine-user-agent.js"], "sourcesContent": ["var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,2BAAD,CAAxB;;AAEAC,MAAM,CAACC,OAAP,GAAiBH,UAAU,CAAC,WAAD,EAAc,WAAd,CAAV,IAAwC,EAAzD"}, "metadata": {}, "sourceType": "script"}