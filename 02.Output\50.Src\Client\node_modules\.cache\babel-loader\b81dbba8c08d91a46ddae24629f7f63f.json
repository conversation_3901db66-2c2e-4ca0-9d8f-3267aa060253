{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    urlUtils = require('../../utils/url'),\n    SenderReceiver = require('./sender-receiver');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\n\nfunction createAjaxSender(AjaxObject) {\n  return function (url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n\n    if (typeof payload === 'string') {\n      opt.headers = {\n        'Content-type': 'text/plain'\n      };\n    }\n\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function (status) {\n      debug('finish', status);\n      xo = null;\n\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n\n      callback();\n    });\n    return function () {\n      debug('abort');\n      xo.close();\n      xo = null;\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\n\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\n\ninherits(AjaxBasedTransport, SenderReceiver);\nmodule.exports = AjaxBasedTransport;", "map": {"version": 3, "names": ["inherits", "require", "urlUtils", "SenderReceiver", "debug", "process", "env", "NODE_ENV", "createAjaxSender", "AjaxObject", "url", "payload", "callback", "opt", "headers", "ajaxUrl", "addPath", "xo", "once", "status", "Error", "close", "err", "code", "AjaxBasedTransport", "transUrl", "urlSuffix", "Receiver", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/lib/ajax-based.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , SenderReceiver = require('./sender-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\n\nfunction createAjaxSender(AjaxObject) {\n  return function(url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {'Content-type': 'text/plain'};\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function(status) {\n      debug('finish', status);\n      xo = null;\n\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function() {\n      debug('abort');\n      xo.close();\n      xo = null;\n\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\n\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\n\ninherits(AjaxBasedTransport, SenderReceiver);\n\nmodule.exports = AjaxBasedTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,iBAAD,CADtB;AAAA,IAEIE,cAAc,GAAGF,OAAO,CAAC,mBAAD,CAF5B;;AAKA,IAAIG,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAD,CAAP,CAAiB,0BAAjB,CAAR;AACD;;AAED,SAASO,gBAAT,CAA0BC,UAA1B,EAAsC;EACpC,OAAO,UAASC,GAAT,EAAcC,OAAd,EAAuBC,QAAvB,EAAiC;IACtCR,KAAK,CAAC,oBAAD,EAAuBM,GAAvB,EAA4BC,OAA5B,CAAL;IACA,IAAIE,GAAG,GAAG,EAAV;;IACA,IAAI,OAAOF,OAAP,KAAmB,QAAvB,EAAiC;MAC/BE,GAAG,CAACC,OAAJ,GAAc;QAAC,gBAAgB;MAAjB,CAAd;IACD;;IACD,IAAIC,OAAO,GAAGb,QAAQ,CAACc,OAAT,CAAiBN,GAAjB,EAAsB,WAAtB,CAAd;IACA,IAAIO,EAAE,GAAG,IAAIR,UAAJ,CAAe,MAAf,EAAuBM,OAAvB,EAAgCJ,OAAhC,EAAyCE,GAAzC,CAAT;IACAI,EAAE,CAACC,IAAH,CAAQ,QAAR,EAAkB,UAASC,MAAT,EAAiB;MACjCf,KAAK,CAAC,QAAD,EAAWe,MAAX,CAAL;MACAF,EAAE,GAAG,IAAL;;MAEA,IAAIE,MAAM,KAAK,GAAX,IAAkBA,MAAM,KAAK,GAAjC,EAAsC;QACpC,OAAOP,QAAQ,CAAC,IAAIQ,KAAJ,CAAU,iBAAiBD,MAA3B,CAAD,CAAf;MACD;;MACDP,QAAQ;IACT,CARD;IASA,OAAO,YAAW;MAChBR,KAAK,CAAC,OAAD,CAAL;MACAa,EAAE,CAACI,KAAH;MACAJ,EAAE,GAAG,IAAL;MAEA,IAAIK,GAAG,GAAG,IAAIF,KAAJ,CAAU,SAAV,CAAV;MACAE,GAAG,CAACC,IAAJ,GAAW,IAAX;MACAX,QAAQ,CAACU,GAAD,CAAR;IACD,CARD;EASD,CA1BD;AA2BD;;AAED,SAASE,kBAAT,CAA4BC,QAA5B,EAAsCC,SAAtC,EAAiDC,QAAjD,EAA2DlB,UAA3D,EAAuE;EACrEN,cAAc,CAACyB,IAAf,CAAoB,IAApB,EAA0BH,QAA1B,EAAoCC,SAApC,EAA+ClB,gBAAgB,CAACC,UAAD,CAA/D,EAA6EkB,QAA7E,EAAuFlB,UAAvF;AACD;;AAEDT,QAAQ,CAACwB,kBAAD,EAAqBrB,cAArB,CAAR;AAEA0B,MAAM,CAACC,OAAP,GAAiBN,kBAAjB"}, "metadata": {}, "sourceType": "script"}