{"ast": null, "code": "import _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\n\n/**\n * Call [Client#subscribe]{@link Client#subscribe} to create a StompSubscription.\n *\n * Part of `@stomp/stompjs`.\n */\nexport var StompSubscription = /*#__PURE__*/_createClass(function StompSubscription() {\n  _classCallCheck(this, StompSubscription);\n});", "map": {"version": 3, "mappings": ";;;AAEA;;;;;AAKA,WAAaA,iBAAb;EAAA;AAAA", "names": ["StompSubscription"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\stomp-subscription.ts"], "sourcesContent": ["import { StompHeaders } from './stomp-headers';\n\n/**\n * Call [Client#subscribe]{@link Client#subscribe} to create a StompSubscription.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompSubscription {\n  /**\n   * Id associated with this subscription.\n   */\n  public id: string;\n\n  /**\n   * Unsubscribe. See [Client#unsubscribe]{@link Client#unsubscribe} for an example.\n   */\n  public unsubscribe: (headers?: StompHeaders) => void;\n}\n"]}, "metadata": {}, "sourceType": "module"}