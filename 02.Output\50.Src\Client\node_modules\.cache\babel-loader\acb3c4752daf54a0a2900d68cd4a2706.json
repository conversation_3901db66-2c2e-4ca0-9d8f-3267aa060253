{"ast": null, "code": "var DESCRIPTORS = require('../internals/descriptors');\n\nvar call = require('../internals/function-call');\n\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\n\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nvar toIndexedObject = require('../internals/to-indexed-object');\n\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar hasOwn = require('../internals/has-own-property');\n\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define'); // eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\n\n\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor; // `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\n\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) {\n    /* empty */\n  }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "call", "propertyIsEnumerableModule", "createPropertyDescriptor", "toIndexedObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwn", "IE8_DOM_DEFINE", "$getOwnPropertyDescriptor", "Object", "getOwnPropertyDescriptor", "exports", "f", "O", "P", "error"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/core-js-pure/internals/object-get-own-property-descriptor.js"], "sourcesContent": ["var DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es-x/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAAD,CAAzB;;AACA,IAAIC,IAAI,GAAGD,OAAO,CAAC,4BAAD,CAAlB;;AACA,IAAIE,0BAA0B,GAAGF,OAAO,CAAC,4CAAD,CAAxC;;AACA,IAAIG,wBAAwB,GAAGH,OAAO,CAAC,yCAAD,CAAtC;;AACA,IAAII,eAAe,GAAGJ,OAAO,CAAC,gCAAD,CAA7B;;AACA,IAAIK,aAAa,GAAGL,OAAO,CAAC,8BAAD,CAA3B;;AACA,IAAIM,MAAM,GAAGN,OAAO,CAAC,+BAAD,CAApB;;AACA,IAAIO,cAAc,GAAGP,OAAO,CAAC,6BAAD,CAA5B,C,CAEA;;;AACA,IAAIQ,yBAAyB,GAAGC,MAAM,CAACC,wBAAvC,C,CAEA;AACA;;AACAC,OAAO,CAACC,CAAR,GAAYb,WAAW,GAAGS,yBAAH,GAA+B,SAASE,wBAAT,CAAkCG,CAAlC,EAAqCC,CAArC,EAAwC;EAC5FD,CAAC,GAAGT,eAAe,CAACS,CAAD,CAAnB;EACAC,CAAC,GAAGT,aAAa,CAACS,CAAD,CAAjB;EACA,IAAIP,cAAJ,EAAoB,IAAI;IACtB,OAAOC,yBAAyB,CAACK,CAAD,EAAIC,CAAJ,CAAhC;EACD,CAFmB,CAElB,OAAOC,KAAP,EAAc;IAAE;EAAa;EAC/B,IAAIT,MAAM,CAACO,CAAD,EAAIC,CAAJ,CAAV,EAAkB,OAAOX,wBAAwB,CAAC,CAACF,IAAI,CAACC,0BAA0B,CAACU,CAA5B,EAA+BC,CAA/B,EAAkCC,CAAlC,CAAN,EAA4CD,CAAC,CAACC,CAAD,CAA7C,CAA/B;AACnB,CAPD"}, "metadata": {}, "sourceType": "script"}