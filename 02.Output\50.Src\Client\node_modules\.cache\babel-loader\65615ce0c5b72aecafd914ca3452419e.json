{"ast": null, "code": "import React from'react';/**\r\n * 空のコンテンツ<br>\r\n * 確認しやすい為、”コンテンツなし”と表示。正式リリースする時に空文字にする\r\n *\r\n * @module NoContent\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";var NoContent=function NoContent(props){return/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col items-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"m-10 text-6xl text-[#39e2e2]\"})});};export default NoContent;", "map": {"version": 3, "names": ["React", "NoContent", "props"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/NoContent.js"], "sourcesContent": ["import React from 'react';\r\n\r\n/**\r\n * 空のコンテンツ<br>\r\n * 確認しやすい為、”コンテンツなし”と表示。正式リリースする時に空文字にする\r\n *\r\n * @module NoContent\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst NoContent = (props) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"m-10 text-6xl text-[#39e2e2]\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NoContent;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,2CACA,GAAMC,UAAS,CAAG,QAAZA,UAAY,CAACC,KAAD,CAAW,CAC3B,mBACE,YAAK,SAAS,CAAC,4BAAf,uBACE,YAAK,SAAS,CAAC,8BAAf,EADF,EADF,CAKD,CAND,CAQA,cAAeD,UAAf"}, "metadata": {}, "sourceType": "module"}