{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    AjaxBasedTransport = require('./lib/ajax-based'),\n    XhrReceiver = require('./receiver/xhr'),\n    XHRCorsObject = require('./sender/xhr-cors'),\n    XHRLocalObject = require('./sender/xhr-local'),\n    browser = require('../utils/browser');\n\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrStreamingTransport, AjaxBasedTransport);\n\nXhrStreamingTransport.enabled = function (info) {\n  if (info.nullOrigin) {\n    return false;\n  } // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n\n\n  if (browser.isOpera()) {\n    return false;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\n\nXhrStreamingTransport.needBody = !!global.document;\nmodule.exports = XhrStreamingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XHRCorsObject", "XHRLocalObject", "browser", "XhrStreamingTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "isOpera", "transportName", "roundTrips", "needBody", "global", "document", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/xhr-streaming.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , browser = require('../utils/browser')\n  ;\n\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrStreamingTransport, AjaxBasedTransport);\n\nXhrStreamingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n  if (browser.isOpera()) {\n    return false;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\nXhrStreamingTransport.needBody = !!global.document;\n\nmodule.exports = XhrStreamingTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,kBAAkB,GAAGD,OAAO,CAAC,kBAAD,CADhC;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAAA,IAGIG,aAAa,GAAGH,OAAO,CAAC,mBAAD,CAH3B;AAAA,IAIII,cAAc,GAAGJ,OAAO,CAAC,oBAAD,CAJ5B;AAAA,IAKIK,OAAO,GAAGL,OAAO,CAAC,kBAAD,CALrB;;AAQA,SAASM,qBAAT,CAA+BC,QAA/B,EAAyC;EACvC,IAAI,CAACH,cAAc,CAACI,OAAhB,IAA2B,CAACL,aAAa,CAACK,OAA9C,EAAuD;IACrD,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDR,kBAAkB,CAACS,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,gBAAxC,EAA0DL,WAA1D,EAAuEC,aAAvE;AACD;;AAEDJ,QAAQ,CAACO,qBAAD,EAAwBL,kBAAxB,CAAR;;AAEAK,qBAAqB,CAACE,OAAtB,GAAgC,UAASG,IAAT,EAAe;EAC7C,IAAIA,IAAI,CAACC,UAAT,EAAqB;IACnB,OAAO,KAAP;EACD,CAH4C,CAI7C;EACA;;;EACA,IAAIP,OAAO,CAACQ,OAAR,EAAJ,EAAuB;IACrB,OAAO,KAAP;EACD;;EAED,OAAOV,aAAa,CAACK,OAArB;AACD,CAXD;;AAaAF,qBAAqB,CAACQ,aAAtB,GAAsC,eAAtC;AACAR,qBAAqB,CAACS,UAAtB,GAAmC,CAAnC,C,CAAsC;AAEtC;AACA;AACA;;AACAT,qBAAqB,CAACU,QAAtB,GAAiC,CAAC,CAACC,MAAM,CAACC,QAA1C;AAEAC,MAAM,CAACC,OAAP,GAAiBd,qBAAjB"}, "metadata": {}, "sourceType": "script"}