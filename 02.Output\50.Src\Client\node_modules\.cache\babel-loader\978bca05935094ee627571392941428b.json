{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";import React,{useState,useEffect}from'react';import{useSubscription,useStompClient}from'react-stomp-hooks';import Vehicle from'./Vehicle';import CustomVehicle from'./CustomVehicle';import Deployment from'./Deployment';import Case from'./Case';import CaseHalf from'./CaseHalf';import CaseQuarter from'./CaseQuarter';import IncomingCallA from'./IncomingCallA';import IncomingCallB from'./IncomingCallB';import Weather from'./Weather';import TotalFrequency from'./TotalFrequency';import Alarm from'./Alarm';import Attendance from'./Attendance';import DoctorOnDuty from'./DoctorOnDuty';import Schedule from'./Schedule';import HandOver from'./Handover';import DigitalRadio from'./DigitalRadio';import AmbulanceRate from'./AmbulanceRate';import ExtendedVehicle from'./ExtendedVehicle';import Now from'./Now';import NoContent from'./NoContent';import{sendResultMsg,getWsEndpoint}from'../utils/Util.js';import{jsx as _jsx}from\"react/jsx-runtime\";/**\r\n * 実際の表示Contentの箱。SourceNoによって、それぞれのContentを表示\r\n * Source No仕様:<br>\r\n    1 車両コンテンツ情報更新<br>\r\n    2 配備状況コンテンツ情報更新<br>\r\n    3 事案コンテンツ情報更新<br>\r\n    4 簡易事案コンテンツ(1-2サイズ)情報更新<br>\r\n    5 簡易事案コンテンツ(1-4サイズ)情報更新<br>\r\n    6 時刻コンテンツ<br>\r\n    7 着信状況コンテンツA情報更新<br>\r\n    8 着信状況コンテンツB情報更新<br>\r\n    9 気象コンテンツ情報更新<br>\r\n    10 総合度数コンテンツ情報更新<br>\r\n    11 予警報コンテンツ情報更新<br>\r\n    12 出退コンテンツ情報更新<br>\r\n    13 当番医コンテンツ情報更新<br>\r\n    14 予定コンテンツ情報更新<br>\r\n    15 引継事項コンテンツ情報更新<br>\r\n    16 デジタル無線コンテンツ情報更新<br>\r\n    17 救急車稼働率コンテンツ情報更新<br>\r\n    18 拡張車両コンテンツ情報更新（50行表示）<br>\r\n    \r\n * @module SplitScreen\r\n * @component \r\n * @param {*} props\r\n * @return {*} SplitScreen\r\n */var SplitScreen=function SplitScreen(props){/**\r\n * Server側のIF仕様\r\n * public class ContentInfo {\r\n      private Integer sourceNo;\r\n      private Integer sourceSplitNo;\r\n      private Integer detailSplitNo; // 面分割番号\r\n      private Object content; //実際のContent内容\r\n    }\r\n */var _useState=useState(),_useState2=_slicedToArray(_useState,2),contentInfo=_useState2[0],setContentInfo=_useState2[1];var receiveContent=function receiveContent(message){console.info('receiveContent: '+message.body);var command=JSON.parse(message.body);var isString=function isString(val){return typeof val==='string';};if(command.sourceData){if(isString(command.sourceData)){command.sourceData=JSON.parse(command.sourceData);}}if(command){switch(command.detailSplitNo){case 1:case 3:case 9:case 11:if(props.detailSplitNo===1){setContentInfo(function(split){return _objectSpread(_objectSpread({},split),command);});}break;case 4:case 6:case 12:case 14:if(props.detailSplitNo===2){setContentInfo(function(split){return _objectSpread(_objectSpread({},split),command);});}break;case 5:case 7:case 13:case 15:if(props.detailSplitNo===3){setContentInfo(function(split){return _objectSpread(_objectSpread({},split),command);});}break;case 0:case 2:case 8:case 10:if(props.detailSplitNo===0){setContentInfo(function(split){return _objectSpread(_objectSpread({},split),command);});}break;default:break;}}};var wsEndpoint=getWsEndpoint(props.displayNo,props.splitNo,props.detailSplitNo);useSubscription(wsEndpoint+'/setContent',receiveContent);// Url:  /monitor/0_1_2/setContent\nvar stompClient=useStompClient();useEffect(function(){if(contentInfo!==null&&contentInfo!==void 0&&contentInfo.id){sendResultMsg(stompClient,contentInfo.id,0);}},[contentInfo===null||contentInfo===void 0?void 0:contentInfo.id,stompClient]);if(contentInfo&&contentInfo!==null&&contentInfo!==void 0&&contentInfo.id){console.log(\"TaskID: \".concat(contentInfo===null||contentInfo===void 0?void 0:contentInfo.id));}if(contentInfo&&contentInfo.sourceNo>=0){var sourceData=contentInfo.sourceData;sourceData.barTitle=contentInfo.sourceName;sourceData.sourceDispPattern=contentInfo.sourceDispPattern;switch(contentInfo.sourceNo){case 1:return sourceData.sourceDispPattern==1?/*#__PURE__*/_jsx(CustomVehicle,_objectSpread({},sourceData)):/*#__PURE__*/_jsx(Vehicle,_objectSpread({},sourceData));case 2:return/*#__PURE__*/_jsx(Deployment,_objectSpread({},sourceData));case 3:return/*#__PURE__*/_jsx(Case,_objectSpread({},sourceData));case 4:return/*#__PURE__*/_jsx(CaseHalf,_objectSpread({},sourceData));case 5:return/*#__PURE__*/_jsx(CaseQuarter,_objectSpread({},sourceData));case 6:return/*#__PURE__*/_jsx(Now,_objectSpread({},sourceData));case 7:return/*#__PURE__*/_jsx(IncomingCallA,_objectSpread({},sourceData));case 8:return/*#__PURE__*/_jsx(IncomingCallB,_objectSpread({},sourceData));case 9:return/*#__PURE__*/_jsx(Weather,_objectSpread({},sourceData));case 10:return/*#__PURE__*/_jsx(TotalFrequency,_objectSpread({},sourceData));case 11:return/*#__PURE__*/_jsx(Alarm,_objectSpread({},sourceData));case 12:return/*#__PURE__*/_jsx(Attendance,_objectSpread({},sourceData));case 13:return/*#__PURE__*/_jsx(DoctorOnDuty,_objectSpread({},sourceData));case 14:return/*#__PURE__*/_jsx(Schedule,_objectSpread({},sourceData));case 15:return/*#__PURE__*/_jsx(HandOver,_objectSpread({},sourceData));case 16:return/*#__PURE__*/_jsx(DigitalRadio,_objectSpread({},sourceData));case 17:return/*#__PURE__*/_jsx(AmbulanceRate,_objectSpread({},sourceData));case 18:return/*#__PURE__*/_jsx(ExtendedVehicle,_objectSpread({},sourceData));default:return/*#__PURE__*/_jsx(NoContent,{});}}return/*#__PURE__*/_jsx(NoContent,{});};export default SplitScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSubscription", "useStompClient", "Vehicle", "CustomVehicle", "Deployment", "Case", "CaseHalf", "CaseQuarter", "IncomingCallA", "IncomingCallB", "Weather", "TotalFrequency", "Alarm", "Attendance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Schedule", "HandOver", "DigitalRadio", "AmbulanceRate", "ExtendedVehicle", "Now", "NoContent", "sendResultMsg", "getWsEndpoint", "SplitScreen", "props", "contentInfo", "setContentInfo", "receiveContent", "message", "console", "info", "body", "command", "JSON", "parse", "isString", "val", "sourceData", "detailSplitNo", "split", "wsEndpoint", "displayNo", "splitNo", "stompClient", "id", "log", "sourceNo", "bar<PERSON>itle", "sourceName", "sourceDispPattern"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/SplitScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport PropTypes from 'prop-types';\r\nimport Vehicle from './Vehicle';\r\nimport CustomVehicle from './CustomVehicle';\r\nimport Deployment from './Deployment';\r\nimport Case from './Case';\r\nimport CaseHalf from './CaseHalf';\r\nimport CaseQuarter from './CaseQuarter';\r\nimport IncomingCallA from './IncomingCallA';\r\nimport IncomingCallB from './IncomingCallB';\r\nimport Weather from './Weather';\r\nimport TotalFrequency from './TotalFrequency';\r\nimport Alarm from './Alarm';\r\nimport Attendance from './Attendance';\r\nimport DoctorOnDuty from './DoctorOnDuty';\r\nimport Schedule from './Schedule';\r\nimport HandOver from './Handover';\r\nimport DigitalRadio from './DigitalRadio';\r\nimport AmbulanceRate from './AmbulanceRate';\r\nimport ExtendedVehicle from './ExtendedVehicle';\r\nimport Now from './Now';\r\nimport NoContent from './NoContent';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\n\r\nconst propTypes = {\r\n  displayNo: PropTypes.number,\r\n  splitNo: PropTypes.number,\r\n  detailSplitNo: PropTypes.number,\r\n};\r\n\r\n/**\r\n * 実際の表示Contentの箱。SourceNoによって、それぞれのContentを表示\r\n * Source No仕様:<br>\r\n    1 車両コンテンツ情報更新<br>\r\n    2 配備状況コンテンツ情報更新<br>\r\n    3 事案コンテンツ情報更新<br>\r\n    4 簡易事案コンテンツ(1-2サイズ)情報更新<br>\r\n    5 簡易事案コンテンツ(1-4サイズ)情報更新<br>\r\n    6 時刻コンテンツ<br>\r\n    7 着信状況コンテンツA情報更新<br>\r\n    8 着信状況コンテンツB情報更新<br>\r\n    9 気象コンテンツ情報更新<br>\r\n    10 総合度数コンテンツ情報更新<br>\r\n    11 予警報コンテンツ情報更新<br>\r\n    12 出退コンテンツ情報更新<br>\r\n    13 当番医コンテンツ情報更新<br>\r\n    14 予定コンテンツ情報更新<br>\r\n    15 引継事項コンテンツ情報更新<br>\r\n    16 デジタル無線コンテンツ情報更新<br>\r\n    17 救急車稼働率コンテンツ情報更新<br>\r\n    18 拡張車両コンテンツ情報更新（50行表示）<br>\r\n    \r\n * @module SplitScreen\r\n * @component \r\n * @param {*} props\r\n * @return {*} SplitScreen\r\n */\r\nconst SplitScreen = (props) => {\r\n  /**\r\n * Server側のIF仕様\r\n * public class ContentInfo {\r\n      private Integer sourceNo;\r\n      private Integer sourceSplitNo;\r\n      private Integer detailSplitNo; // 面分割番号\r\n      private Object content; //実際のContent内容\r\n    }\r\n */\r\n  const [contentInfo, setContentInfo] = useState();\r\n\r\n  const receiveContent = (message) => {\r\n    console.info('receiveContent: ' + message.body);\r\n\r\n    let command = JSON.parse(message.body);\r\n    const isString = (val) => typeof val === 'string';\r\n    if (command.sourceData) {\r\n      if (isString(command.sourceData)) {\r\n        command.sourceData = JSON.parse(command.sourceData);\r\n      }\r\n    }\r\n\r\n    if (command) {\r\n      switch (command.detailSplitNo) {\r\n        case 1:\r\n        case 3:\r\n        case 9:\r\n        case 11:\r\n          if (props.detailSplitNo === 1) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 4:\r\n        case 6:\r\n        case 12:\r\n        case 14:\r\n          if (props.detailSplitNo === 2) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 5:\r\n        case 7:\r\n        case 13:\r\n        case 15:\r\n          if (props.detailSplitNo === 3) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        case 0:\r\n        case 2:\r\n        case 8:\r\n        case 10:\r\n          if (props.detailSplitNo === 0) {\r\n            setContentInfo((split) => ({\r\n              ...split,\r\n              ...command,\r\n            }));\r\n          }\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  const wsEndpoint = getWsEndpoint(\r\n    props.displayNo,\r\n    props.splitNo,\r\n    props.detailSplitNo\r\n  );\r\n\r\n  useSubscription(wsEndpoint + '/setContent', receiveContent); // Url:  /monitor/0_1_2/setContent\r\n\r\n  const stompClient = useStompClient();\r\n  useEffect(() => {\r\n    if (contentInfo?.id) {\r\n      sendResultMsg(stompClient, contentInfo.id, 0);\r\n    }\r\n  }, [contentInfo?.id, stompClient]);\r\n\r\n  if (contentInfo && contentInfo?.id) {\r\n    console.log(`TaskID: ${contentInfo?.id}`);\r\n  }\r\n\r\n  if (contentInfo && contentInfo.sourceNo >= 0) {\r\n    const sourceData = contentInfo.sourceData;\r\n    sourceData.barTitle = contentInfo.sourceName;\r\n    sourceData.sourceDispPattern = contentInfo.sourceDispPattern;\r\n    switch (contentInfo.sourceNo) {\r\n      case 1:\r\n        return   sourceData.sourceDispPattern == 1 ?  <CustomVehicle {...sourceData} /> : <Vehicle {...sourceData} />;\r\n      case 2:\r\n        return <Deployment {...sourceData} />;\r\n      case 3:\r\n        return <Case {...sourceData} />;\r\n      case 4:\r\n        return <CaseHalf {...sourceData} />;\r\n      case 5:\r\n        return <CaseQuarter {...sourceData} />;\r\n      case 6:\r\n        return <Now {...sourceData} />;\r\n      case 7:\r\n        return <IncomingCallA {...sourceData} />;\r\n      case 8:\r\n        return <IncomingCallB {...sourceData} />;\r\n      case 9:\r\n        return <Weather {...sourceData} />;\r\n      case 10:\r\n        return <TotalFrequency {...sourceData} />;\r\n      case 11:\r\n        return <Alarm {...sourceData} />;\r\n      case 12:\r\n        return <Attendance {...sourceData} />;\r\n      case 13:\r\n        return <DoctorOnDuty {...sourceData} />;\r\n      case 14:\r\n        return <Schedule {...sourceData} />;\r\n      case 15:\r\n        return <HandOver {...sourceData} />;\r\n      case 16:\r\n        return <DigitalRadio {...sourceData} />;\r\n      case 17:\r\n        return <AmbulanceRate {...sourceData} />;\r\n      case 18:\r\n        return <ExtendedVehicle {...sourceData} />;\r\n      default:\r\n        return <NoContent />;\r\n    }\r\n  }\r\n\r\n  return <NoContent />;\r\n};\r\n\r\nSplitScreen.propTypes = propTypes;\r\nexport default SplitScreen;\r\n"], "mappings": "mRAAA,MAAOA,MAAP,EAAgBC,QAAhB,CAA0BC,SAA1B,KAA2C,OAA3C,CACA,OAASC,eAAT,CAA0BC,cAA1B,KAAgD,mBAAhD,CAEA,MAAOC,QAAP,KAAoB,WAApB,CACA,MAAOC,cAAP,KAA0B,iBAA1B,CACA,MAAOC,WAAP,KAAuB,cAAvB,CACA,MAAOC,KAAP,KAAiB,QAAjB,CACA,MAAOC,SAAP,KAAqB,YAArB,CACA,MAAOC,YAAP,KAAwB,eAAxB,CACA,MAAOC,cAAP,KAA0B,iBAA1B,CACA,MAAOC,cAAP,KAA0B,iBAA1B,CACA,MAAOC,QAAP,KAAoB,WAApB,CACA,MAAOC,eAAP,KAA2B,kBAA3B,CACA,MAAOC,MAAP,KAAkB,SAAlB,CACA,MAAOC,WAAP,KAAuB,cAAvB,CACA,MAAOC,aAAP,KAAyB,gBAAzB,CACA,MAAOC,SAAP,KAAqB,YAArB,CACA,MAAOC,SAAP,KAAqB,YAArB,CACA,MAAOC,aAAP,KAAyB,gBAAzB,CACA,MAAOC,cAAP,KAA0B,iBAA1B,CACA,MAAOC,gBAAP,KAA4B,mBAA5B,CACA,MAAOC,IAAP,KAAgB,OAAhB,CACA,MAAOC,UAAP,KAAsB,aAAtB,CACA,OAASC,aAAT,CAAwBC,aAAxB,KAA6C,kBAA7C,C,2CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,YAAW,CAAG,QAAdA,YAAc,CAACC,KAAD,CAAW,CAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACE,cAAsC3B,QAAQ,EAA9C,wCAAO4B,WAAP,eAAoBC,cAApB,eAEA,GAAMC,eAAc,CAAG,QAAjBA,eAAiB,CAACC,OAAD,CAAa,CAClCC,OAAO,CAACC,IAAR,CAAa,mBAAqBF,OAAO,CAACG,IAA1C,EAEA,GAAIC,QAAO,CAAGC,IAAI,CAACC,KAAL,CAAWN,OAAO,CAACG,IAAnB,CAAd,CACA,GAAMI,SAAQ,CAAG,QAAXA,SAAW,CAACC,GAAD,QAAS,OAAOA,IAAP,GAAe,QAAxB,EAAjB,CACA,GAAIJ,OAAO,CAACK,UAAZ,CAAwB,CACtB,GAAIF,QAAQ,CAACH,OAAO,CAACK,UAAT,CAAZ,CAAkC,CAChCL,OAAO,CAACK,UAAR,CAAqBJ,IAAI,CAACC,KAAL,CAAWF,OAAO,CAACK,UAAnB,CAArB,CACD,CACF,CAED,GAAIL,OAAJ,CAAa,CACX,OAAQA,OAAO,CAACM,aAAhB,EACE,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,GAAL,CACE,GAAId,KAAK,CAACc,aAAN,GAAwB,CAA5B,CAA+B,CAC7BZ,cAAc,CAAC,SAACa,KAAD,wCACVA,KADU,EAEVP,OAFU,GAAD,CAAd,CAID,CACD,MACF,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,GAAL,CACA,IAAK,GAAL,CACE,GAAIR,KAAK,CAACc,aAAN,GAAwB,CAA5B,CAA+B,CAC7BZ,cAAc,CAAC,SAACa,KAAD,wCACVA,KADU,EAEVP,OAFU,GAAD,CAAd,CAID,CACD,MACF,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,GAAL,CACA,IAAK,GAAL,CACE,GAAIR,KAAK,CAACc,aAAN,GAAwB,CAA5B,CAA+B,CAC7BZ,cAAc,CAAC,SAACa,KAAD,wCACVA,KADU,EAEVP,OAFU,GAAD,CAAd,CAID,CACD,MACF,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,EAAL,CACA,IAAK,GAAL,CACE,GAAIR,KAAK,CAACc,aAAN,GAAwB,CAA5B,CAA+B,CAC7BZ,cAAc,CAAC,SAACa,KAAD,wCACVA,KADU,EAEVP,OAFU,GAAD,CAAd,CAID,CACD,MACF,QACE,MA9CJ,CAgDD,CACF,CA7DD,CA+DA,GAAMQ,WAAU,CAAGlB,aAAa,CAC9BE,KAAK,CAACiB,SADwB,CAE9BjB,KAAK,CAACkB,OAFwB,CAG9BlB,KAAK,CAACc,aAHwB,CAAhC,CAMAvC,eAAe,CAACyC,UAAU,CAAG,aAAd,CAA6Bb,cAA7B,CAAf,CAA6D;AAE7D,GAAMgB,YAAW,CAAG3C,cAAc,EAAlC,CACAF,SAAS,CAAC,UAAM,CACd,GAAI2B,WAAJ,SAAIA,WAAJ,WAAIA,WAAW,CAAEmB,EAAjB,CAAqB,CACnBvB,aAAa,CAACsB,WAAD,CAAclB,WAAW,CAACmB,EAA1B,CAA8B,CAA9B,CAAb,CACD,CACF,CAJQ,CAIN,CAACnB,WAAD,SAACA,WAAD,iBAACA,WAAW,CAAEmB,EAAd,CAAkBD,WAAlB,CAJM,CAAT,CAMA,GAAIlB,WAAW,EAAIA,WAAJ,SAAIA,WAAJ,WAAIA,WAAW,CAAEmB,EAAhC,CAAoC,CAClCf,OAAO,CAACgB,GAAR,mBAAuBpB,WAAvB,SAAuBA,WAAvB,iBAAuBA,WAAW,CAAEmB,EAApC,GACD,CAED,GAAInB,WAAW,EAAIA,WAAW,CAACqB,QAAZ,EAAwB,CAA3C,CAA8C,CAC5C,GAAMT,WAAU,CAAGZ,WAAW,CAACY,UAA/B,CACAA,UAAU,CAACU,QAAX,CAAsBtB,WAAW,CAACuB,UAAlC,CACAX,UAAU,CAACY,iBAAX,CAA+BxB,WAAW,CAACwB,iBAA3C,CACA,OAAQxB,WAAW,CAACqB,QAApB,EACE,IAAK,EAAL,CACE,MAAST,WAAU,CAACY,iBAAX,EAAgC,CAAhC,cAAqC,KAAC,aAAD,kBAAmBZ,UAAnB,EAArC,cAAyE,KAAC,OAAD,kBAAaA,UAAb,EAAlF,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,UAAD,kBAAgBA,UAAhB,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,IAAD,kBAAUA,UAAV,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,QAAD,kBAAcA,UAAd,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,WAAD,kBAAiBA,UAAjB,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,GAAD,kBAASA,UAAT,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,aAAD,kBAAmBA,UAAnB,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,aAAD,kBAAmBA,UAAnB,EAAP,CACF,IAAK,EAAL,CACE,mBAAO,KAAC,OAAD,kBAAaA,UAAb,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,cAAD,kBAAoBA,UAApB,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,KAAD,kBAAWA,UAAX,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,UAAD,kBAAgBA,UAAhB,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,YAAD,kBAAkBA,UAAlB,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,QAAD,kBAAcA,UAAd,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,QAAD,kBAAcA,UAAd,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,YAAD,kBAAkBA,UAAlB,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,aAAD,kBAAmBA,UAAnB,EAAP,CACF,IAAK,GAAL,CACE,mBAAO,KAAC,eAAD,kBAAqBA,UAArB,EAAP,CACF,QACE,mBAAO,KAAC,SAAD,IAAP,CAtCJ,CAwCD,CAED,mBAAO,KAAC,SAAD,IAAP,CACD,CA7ID,CAgJA,cAAed,YAAf"}, "metadata": {}, "sourceType": "module"}