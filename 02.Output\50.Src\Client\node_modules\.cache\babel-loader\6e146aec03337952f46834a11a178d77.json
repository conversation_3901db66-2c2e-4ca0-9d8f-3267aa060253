{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useState } from 'react';\nimport { isObject } from '../utils';\nvar NetworkEventType;\n\n(function (NetworkEventType) {\n  NetworkEventType[\"ONLINE\"] = \"online\";\n  NetworkEventType[\"OFFLINE\"] = \"offline\";\n  NetworkEventType[\"CHANGE\"] = \"change\";\n})(NetworkEventType || (NetworkEventType = {}));\n\nfunction getConnection() {\n  var nav = navigator;\n  if (!isObject(nav)) return null;\n  return nav.connection || nav.mozConnection || nav.webkitConnection;\n}\n\nfunction getConnectionProperty() {\n  var c = getConnection();\n  if (!c) return {};\n  return {\n    rtt: c.rtt,\n    type: c.type,\n    saveData: c.saveData,\n    downlink: c.downlink,\n    downlinkMax: c.downlinkMax,\n    effectiveType: c.effectiveType\n  };\n}\n\nfunction useNetwork() {\n  var _a = __read(useState(function () {\n    return __assign({\n      since: undefined,\n      online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine\n    }, getConnectionProperty());\n  }), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  useEffect(function () {\n    var onOnline = function onOnline() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: true,\n          since: new Date()\n        });\n      });\n    };\n\n    var onOffline = function onOffline() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: false,\n          since: new Date()\n        });\n      });\n    };\n\n    var onConnectionChange = function onConnectionChange() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), getConnectionProperty());\n      });\n    };\n\n    window.addEventListener(NetworkEventType.ONLINE, onOnline);\n    window.addEventListener(NetworkEventType.OFFLINE, onOffline);\n    var connection = getConnection();\n    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    return function () {\n      window.removeEventListener(NetworkEventType.ONLINE, onOnline);\n      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);\n      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    };\n  }, []);\n  return state;\n}\n\nexport default useNetwork;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useState", "isObject", "NetworkEventType", "getConnection", "nav", "navigator", "connection", "mozConnection", "webkitConnection", "getConnectionProperty", "c", "rtt", "type", "saveData", "downlink", "downlinkMax", "effectiveType", "useNetwork", "_a", "since", "undefined", "online", "onLine", "state", "setState", "onOnline", "prevState", "Date", "onOffline", "onConnectionChange", "window", "addEventListener", "ONLINE", "OFFLINE", "CHANGE", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useNetwork/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useState } from 'react';\nimport { isObject } from '../utils';\nvar NetworkEventType;\n(function (NetworkEventType) {\n  NetworkEventType[\"ONLINE\"] = \"online\";\n  NetworkEventType[\"OFFLINE\"] = \"offline\";\n  NetworkEventType[\"CHANGE\"] = \"change\";\n})(NetworkEventType || (NetworkEventType = {}));\nfunction getConnection() {\n  var nav = navigator;\n  if (!isObject(nav)) return null;\n  return nav.connection || nav.mozConnection || nav.webkitConnection;\n}\nfunction getConnectionProperty() {\n  var c = getConnection();\n  if (!c) return {};\n  return {\n    rtt: c.rtt,\n    type: c.type,\n    saveData: c.saveData,\n    downlink: c.downlink,\n    downlinkMax: c.downlinkMax,\n    effectiveType: c.effectiveType\n  };\n}\nfunction useNetwork() {\n  var _a = __read(useState(function () {\n      return __assign({\n        since: undefined,\n        online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine\n      }, getConnectionProperty());\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    var onOnline = function onOnline() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: true,\n          since: new Date()\n        });\n      });\n    };\n    var onOffline = function onOffline() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: false,\n          since: new Date()\n        });\n      });\n    };\n    var onConnectionChange = function onConnectionChange() {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), getConnectionProperty());\n      });\n    };\n    window.addEventListener(NetworkEventType.ONLINE, onOnline);\n    window.addEventListener(NetworkEventType.OFFLINE, onOffline);\n    var connection = getConnection();\n    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    return function () {\n      window.removeEventListener(NetworkEventType.ONLINE, onOnline);\n      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);\n      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    };\n  }, []);\n  return state;\n}\nexport default useNetwork;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaT,CAAb,EAAgB;EAClD,IAAIU,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIV,CAAC,GAAGW,CAAC,CAACJ,IAAF,CAAOG,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACf,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACa,CAAC,GAAGd,CAAC,CAACiB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBP,CAAC,GAAGX,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCW,CAAC,CAACJ,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIgB,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,QAApB,QAAoC,OAApC;AACA,SAASC,QAAT,QAAyB,UAAzB;AACA,IAAIC,gBAAJ;;AACA,CAAC,UAAUA,gBAAV,EAA4B;EAC3BA,gBAAgB,CAAC,QAAD,CAAhB,GAA6B,QAA7B;EACAA,gBAAgB,CAAC,SAAD,CAAhB,GAA8B,SAA9B;EACAA,gBAAgB,CAAC,QAAD,CAAhB,GAA6B,QAA7B;AACD,CAJD,EAIGA,gBAAgB,KAAKA,gBAAgB,GAAG,EAAxB,CAJnB;;AAKA,SAASC,aAAT,GAAyB;EACvB,IAAIC,GAAG,GAAGC,SAAV;EACA,IAAI,CAACJ,QAAQ,CAACG,GAAD,CAAb,EAAoB,OAAO,IAAP;EACpB,OAAOA,GAAG,CAACE,UAAJ,IAAkBF,GAAG,CAACG,aAAtB,IAAuCH,GAAG,CAACI,gBAAlD;AACD;;AACD,SAASC,qBAAT,GAAiC;EAC/B,IAAIC,CAAC,GAAGP,aAAa,EAArB;EACA,IAAI,CAACO,CAAL,EAAQ,OAAO,EAAP;EACR,OAAO;IACLC,GAAG,EAAED,CAAC,CAACC,GADF;IAELC,IAAI,EAAEF,CAAC,CAACE,IAFH;IAGLC,QAAQ,EAAEH,CAAC,CAACG,QAHP;IAILC,QAAQ,EAAEJ,CAAC,CAACI,QAJP;IAKLC,WAAW,EAAEL,CAAC,CAACK,WALV;IAMLC,aAAa,EAAEN,CAAC,CAACM;EANZ,CAAP;AAQD;;AACD,SAASC,UAAT,GAAsB;EACpB,IAAIC,EAAE,GAAGhC,MAAM,CAACc,QAAQ,CAAC,YAAY;IACjC,OAAO5B,QAAQ,CAAC;MACd+C,KAAK,EAAEC,SADO;MAEdC,MAAM,EAAEhB,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACiB;IAF1D,CAAD,EAGZb,qBAAqB,EAHT,CAAf;EAID,CALqB,CAAT,EAKT,CALS,CAAf;EAAA,IAMEc,KAAK,GAAGL,EAAE,CAAC,CAAD,CANZ;EAAA,IAOEM,QAAQ,GAAGN,EAAE,CAAC,CAAD,CAPf;;EAQAnB,SAAS,CAAC,YAAY;IACpB,IAAI0B,QAAQ,GAAG,SAASA,QAAT,GAAoB;MACjCD,QAAQ,CAAC,UAAUE,SAAV,EAAqB;QAC5B,OAAOtD,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKsD,SAAL,CAAT,EAA0B;UACvCL,MAAM,EAAE,IAD+B;UAEvCF,KAAK,EAAE,IAAIQ,IAAJ;QAFgC,CAA1B,CAAf;MAID,CALO,CAAR;IAMD,CAPD;;IAQA,IAAIC,SAAS,GAAG,SAASA,SAAT,GAAqB;MACnCJ,QAAQ,CAAC,UAAUE,SAAV,EAAqB;QAC5B,OAAOtD,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKsD,SAAL,CAAT,EAA0B;UACvCL,MAAM,EAAE,KAD+B;UAEvCF,KAAK,EAAE,IAAIQ,IAAJ;QAFgC,CAA1B,CAAf;MAID,CALO,CAAR;IAMD,CAPD;;IAQA,IAAIE,kBAAkB,GAAG,SAASA,kBAAT,GAA8B;MACrDL,QAAQ,CAAC,UAAUE,SAAV,EAAqB;QAC5B,OAAOtD,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKsD,SAAL,CAAT,EAA0BjB,qBAAqB,EAA/C,CAAf;MACD,CAFO,CAAR;IAGD,CAJD;;IAKAqB,MAAM,CAACC,gBAAP,CAAwB7B,gBAAgB,CAAC8B,MAAzC,EAAiDP,QAAjD;IACAK,MAAM,CAACC,gBAAP,CAAwB7B,gBAAgB,CAAC+B,OAAzC,EAAkDL,SAAlD;IACA,IAAItB,UAAU,GAAGH,aAAa,EAA9B;IACAG,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAACyB,gBAAX,CAA4B7B,gBAAgB,CAACgC,MAA7C,EAAqDL,kBAArD,CAAxD;IACA,OAAO,YAAY;MACjBC,MAAM,CAACK,mBAAP,CAA2BjC,gBAAgB,CAAC8B,MAA5C,EAAoDP,QAApD;MACAK,MAAM,CAACK,mBAAP,CAA2BjC,gBAAgB,CAAC+B,OAA5C,EAAqDL,SAArD;MACAtB,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAAC6B,mBAAX,CAA+BjC,gBAAgB,CAACgC,MAAhD,EAAwDL,kBAAxD,CAAxD;IACD,CAJD;EAKD,CA/BQ,EA+BN,EA/BM,CAAT;EAgCA,OAAON,KAAP;AACD;;AACD,eAAeN,UAAf"}, "metadata": {}, "sourceType": "module"}