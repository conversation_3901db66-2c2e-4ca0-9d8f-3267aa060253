{"ast": null, "code": "var getNative = require('./_getNative'),\n    root = require('./_root');\n/* Built-in method references that are verified to be native. */\n\n\nvar Set = getNative(root, 'Set');\nmodule.exports = Set;", "map": {"version": 3, "names": ["getNative", "require", "root", "Set", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_Set.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\n\nmodule.exports = Set;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,IAAI,GAAGD,OAAO,CAAC,SAAD,CADlB;AAGA;;;AACA,IAAIE,GAAG,GAAGH,SAAS,CAACE,IAAD,EAAO,KAAP,CAAnB;AAEAE,MAAM,CAACC,OAAP,GAAiBF,GAAjB"}, "metadata": {}, "sourceType": "script"}