{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement'; // function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\n\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\n\nexport default useRequest;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useAutoRunPlugin", "useCachePlugin", "useDebouncePlugin", "useLoadingDelayPlugin", "usePollingPlugin", "useRefreshOnWindowFocusPlugin", "useRetryPlugin", "useThrottlePlugin", "useRequestImplement", "useRequest", "service", "options", "plugins"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/useRequest.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport useAutoRunPlugin from './plugins/useAutoRunPlugin';\nimport useCachePlugin from './plugins/useCachePlugin';\nimport useDebouncePlugin from './plugins/useDebouncePlugin';\nimport useLoadingDelayPlugin from './plugins/useLoadingDelayPlugin';\nimport usePollingPlugin from './plugins/usePollingPlugin';\nimport useRefreshOnWindowFocusPlugin from './plugins/useRefreshOnWindowFocusPlugin';\nimport useRetryPlugin from './plugins/useRetryPlugin';\nimport useThrottlePlugin from './plugins/useThrottlePlugin';\nimport useRequestImplement from './useRequestImplement';\n// function useRequest<TData, TParams extends any[], TFormated, TTFormated extends TFormated = any>(\n//   service: Service<TData, TParams>,\n//   options: OptionsWithFormat<TData, TParams, TFormated, TTFormated>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TFormated, TParams>\n// function useRequest<TData, TParams extends any[]>(\n//   service: Service<TData, TParams>,\n//   options?: OptionsWithoutFormat<TData, TParams>,\n//   plugins?: Plugin<TData, TParams>[],\n// ): Result<TData, TParams>\nfunction useRequest(service, options, plugins) {\n  return useRequestImplement(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin, useLoadingDelayPlugin, usePollingPlugin, useRefreshOnWindowFocusPlugin, useThrottlePlugin, useAutoRunPlugin, useCachePlugin, useRetryPlugin], false));\n}\nexport default useRequest;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,OAAOS,gBAAP,MAA6B,4BAA7B;AACA,OAAOC,cAAP,MAA2B,0BAA3B;AACA,OAAOC,iBAAP,MAA8B,6BAA9B;AACA,OAAOC,qBAAP,MAAkC,iCAAlC;AACA,OAAOC,gBAAP,MAA6B,4BAA7B;AACA,OAAOC,6BAAP,MAA0C,yCAA1C;AACA,OAAOC,cAAP,MAA2B,0BAA3B;AACA,OAAOC,iBAAP,MAA8B,6BAA9B;AACA,OAAOC,mBAAP,MAAgC,uBAAhC,C,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,OAA7B,EAAsCC,OAAtC,EAA+C;EAC7C,OAAOJ,mBAAmB,CAACE,OAAD,EAAUC,OAAV,EAAmBtB,aAAa,CAACA,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACuC,OAAO,IAAI,EAAZ,CAAX,EAA4B,KAA5B,CAAd,EAAkD,CAACV,iBAAD,EAAoBC,qBAApB,EAA2CC,gBAA3C,EAA6DC,6BAA7D,EAA4FE,iBAA5F,EAA+GP,gBAA/G,EAAiIC,cAAjI,EAAiJK,cAAjJ,CAAlD,EAAoN,KAApN,CAAhC,CAA1B;AACD;;AACD,eAAeG,UAAf"}, "metadata": {}, "sourceType": "module"}