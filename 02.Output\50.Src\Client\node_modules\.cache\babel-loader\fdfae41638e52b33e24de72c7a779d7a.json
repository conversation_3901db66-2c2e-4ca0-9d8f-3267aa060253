{"ast": null, "code": "/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n  webSocket.terminate = function () {\n    var noOp = function noOp() {}; // set all callbacks to no op\n\n\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n    var ts = new Date();\n    var origOnClose = this.onclose; // Track delay in actual closure of the socket\n\n    this.onclose = function (closeEvent) {\n      var delay = new Date().getTime() - ts.getTime();\n      debug(\"Discarded socket closed after \".concat(delay, \"ms, with code/reason: \").concat(closeEvent.code, \"/\").concat(closeEvent.reason));\n    };\n\n    this.close();\n    origOnClose.call(this, {\n      code: 4001,\n      reason: 'Heartbeat failure, discarding the socket',\n      wasClean: false\n    });\n  };\n}", "map": {"version": 3, "mappings": "AAEA;;;AAGA,OAAM,SAAUA,gBAAV,CACJC,SADI,EAEJC,KAFI,EAEwB;EAE5BD,SAAS,CAACE,SAAV,GAAsB;IACpB,IAAMC,IAAI,GAAG,SAAPA,IAAO,GAAK,CAAG,CAArB,CADoB,CAGpB;;;IACA,KAAKC,OAAL,GAAeD,IAAf;IACA,KAAKE,SAAL,GAAiBF,IAAjB;IACA,KAAKG,MAAL,GAAcH,IAAd;IAEA,IAAMI,EAAE,GAAG,IAAIC,IAAJ,EAAX;IAEA,IAAMC,WAAW,GAAG,KAAKC,OAAzB,CAVoB,CAYpB;;IACA,KAAKA,OAAL,GAAe,oBAAU,EAAG;MAC1B,IAAMC,KAAK,GAAG,IAAIH,IAAJ,GAAWI,OAAX,KAAuBL,EAAE,CAACK,OAAH,EAArC;MACAX,KAAK,yCAC8BU,KAD9B,mCAC4DE,UAAU,CAACC,IADvE,cAC+ED,UAAU,CAACE,MAD1F,EAAL;IAGD,CALD;;IAOA,KAAKC,KAAL;IAEAP,WAAW,CAACQ,IAAZ,CAAiB,IAAjB,EAAuB;MACrBH,IAAI,EAAE,IADe;MAErBC,MAAM,EAAE,0CAFa;MAGrBG,QAAQ,EAAE;IAHW,CAAvB;EAKD,CA3BD;AA4BD", "names": ["augmentWebsocket", "webSocket", "debug", "terminate", "noOp", "onerror", "onmessage", "onopen", "ts", "Date", "origOnClose", "onclose", "delay", "getTime", "closeEvent", "code", "reason", "close", "call", "<PERSON><PERSON><PERSON>"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\augment-websocket.ts"], "sourcesContent": ["import { IStompSocket } from './types';\n\n/**\n * @internal\n */\nexport function augmentWebsocket(\n  webSocket: IStompSocket,\n  debug: (msg: string) => void\n) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n\n    const ts = new Date();\n\n    const origOnClose = this.onclose;\n\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(\n        `Discarded socket closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`\n      );\n    };\n\n    this.close();\n\n    origOnClose.call(this, {\n      code: 4001,\n      reason: 'Heartbeat failure, discarding the socket',\n      wasClean: false,\n    });\n  };\n}\n"]}, "metadata": {}, "sourceType": "module"}