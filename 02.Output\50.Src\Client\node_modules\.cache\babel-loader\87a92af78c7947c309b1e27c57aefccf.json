{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\n\nfunction useSize(target) {\n  var _a = __read(useRafState(), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n\n    if (!el) {\n      return;\n    }\n\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n            clientWidth = _a.clientWidth,\n            clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\n\nexport default useSize;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "ResizeObserver", "useRafState", "getTargetElement", "useIsomorphicLayoutEffectWithTarget", "useSize", "target", "_a", "state", "setState", "el", "resizeObserver", "entries", "for<PERSON>ach", "entry", "clientWidth", "clientHeight", "width", "height", "observe", "disconnect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useSize/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\nfunction useSize(target) {\n  var _a = __read(useRafState(), 2),\n    state = _a[0],\n    setState = _a[1];\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\nexport default useSize;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,OAAOO,cAAP,MAA2B,0BAA3B;AACA,OAAOC,WAAP,MAAwB,gBAAxB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mCAAP,MAAgD,8CAAhD;;AACA,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;EACvB,IAAIC,EAAE,GAAGtB,MAAM,CAACiB,WAAW,EAAZ,EAAgB,CAAhB,CAAf;EAAA,IACEM,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGAH,mCAAmC,CAAC,YAAY;IAC9C,IAAIM,EAAE,GAAGP,gBAAgB,CAACG,MAAD,CAAzB;;IACA,IAAI,CAACI,EAAL,EAAS;MACP;IACD;;IACD,IAAIC,cAAc,GAAG,IAAIV,cAAJ,CAAmB,UAAUW,OAAV,EAAmB;MACzDA,OAAO,CAACC,OAAR,CAAgB,UAAUC,KAAV,EAAiB;QAC/B,IAAIP,EAAE,GAAGO,KAAK,CAACR,MAAf;QAAA,IACES,WAAW,GAAGR,EAAE,CAACQ,WADnB;QAAA,IAEEC,YAAY,GAAGT,EAAE,CAACS,YAFpB;QAGAP,QAAQ,CAAC;UACPQ,KAAK,EAAEF,WADA;UAEPG,MAAM,EAAEF;QAFD,CAAD,CAAR;MAID,CARD;IASD,CAVoB,CAArB;IAWAL,cAAc,CAACQ,OAAf,CAAuBT,EAAvB;IACA,OAAO,YAAY;MACjBC,cAAc,CAACS,UAAf;IACD,CAFD;EAGD,CApBkC,EAoBhC,EApBgC,EAoB5Bd,MApB4B,CAAnC;EAqBA,OAAOE,KAAP;AACD;;AACD,eAAeH,OAAf"}, "metadata": {}, "sourceType": "module"}