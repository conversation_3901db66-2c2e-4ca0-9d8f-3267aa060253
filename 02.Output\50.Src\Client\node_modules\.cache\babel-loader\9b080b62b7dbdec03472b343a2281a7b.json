{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\elements\\\\CellBox.js\";\nimport React from 'react';\nimport { getHexColor } from '../../utils/Util.js';\nimport PropTypes from 'prop-types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst propTypes = {\n  className: PropTypes.string,\n  text_color: PropTypes.string,\n  background_color: PropTypes.string\n};\n/**\r\n * 色/背景色/文字列の情報を従い、子供のHtmlTagを表示する\r\n * @module CellBox\r\n * @component\r\n * @param {*} props \r\n * @returns div箱\r\n */\n\nconst CellBox = props => {\n  var _props$className;\n\n  const textStyle = {};\n\n  if (props.text_color) {\n    textStyle.color = getHexColor(props.text_color);\n  }\n\n  if (props.background_color) {\n    textStyle.backgroundColor = getHexColor(props.background_color);\n  }\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: props === null || props === void 0 ? void 0 : (_props$className = props.className) === null || _props$className === void 0 ? void 0 : _props$className.trim(),\n    style: textStyle,\n    children: props.children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n\n_c = CellBox;\nCellBox.propTypes = propTypes;\nexport default CellBox;\n\nvar _c;\n\n$RefreshReg$(_c, \"CellBox\");", "map": {"version": 3, "names": ["React", "getHexColor", "PropTypes", "propTypes", "className", "string", "text_color", "background_color", "CellBox", "props", "textStyle", "color", "backgroundColor", "trim", "children"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/CellBox.js"], "sourcesContent": ["import React from 'react';\r\nimport { getHexColor } from '../../utils/Util.js';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  className: PropTypes.string,\r\n  text_color: PropTypes.string,\r\n  background_color: PropTypes.string,\r\n};\r\n\r\n/**\r\n * 色/背景色/文字列の情報を従い、子供のHtmlTagを表示する\r\n * @module CellBox\r\n * @component\r\n * @param {*} props \r\n * @returns div箱\r\n */\r\nconst CellBox = (props) => {\r\n  const textStyle = {};\r\n\r\n  if (props.text_color) {\r\n    textStyle.color = getHexColor(props.text_color);\r\n  }\r\n\r\n  if (props.background_color) {\r\n    textStyle.backgroundColor = getHexColor(props.background_color);\r\n  }\r\n\r\n  return (\r\n    <div className={props?.className?.trim()} style={textStyle}>\r\n      {props.children}\r\n    </div>\r\n  );\r\n};\r\n\r\nCellBox.propTypes = propTypes;\r\nexport default CellBox;\r\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,OAAOC,SAAP,MAAsB,YAAtB;;AAEA,MAAMC,SAAS,GAAG;EAChBC,SAAS,EAAEF,SAAS,CAACG,MADL;EAEhBC,UAAU,EAAEJ,SAAS,CAACG,MAFN;EAGhBE,gBAAgB,EAAEL,SAAS,CAACG;AAHZ,CAAlB;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMG,OAAO,GAAIC,KAAD,IAAW;EAAA;;EACzB,MAAMC,SAAS,GAAG,EAAlB;;EAEA,IAAID,KAAK,CAACH,UAAV,EAAsB;IACpBI,SAAS,CAACC,KAAV,GAAkBV,WAAW,CAACQ,KAAK,CAACH,UAAP,CAA7B;EACD;;EAED,IAAIG,KAAK,CAACF,gBAAV,EAA4B;IAC1BG,SAAS,CAACE,eAAV,GAA4BX,WAAW,CAACQ,KAAK,CAACF,gBAAP,CAAvC;EACD;;EAED,oBACE;IAAK,SAAS,EAAEE,KAAF,aAAEA,KAAF,2CAAEA,KAAK,CAAEL,SAAT,qDAAE,iBAAkBS,IAAlB,EAAhB;IAA0C,KAAK,EAAEH,SAAjD;IAAA,UACGD,KAAK,CAACK;EADT;IAAA;IAAA;IAAA;EAAA,QADF;AAKD,CAhBD;;KAAMN,O;AAkBNA,OAAO,CAACL,SAAR,GAAoBA,SAApB;AACA,eAAeK,OAAf"}, "metadata": {}, "sourceType": "module"}