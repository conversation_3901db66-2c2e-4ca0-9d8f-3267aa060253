{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n/** Used to stand-in for `undefined` hash values. */\n\n\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\n\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n  return this;\n}\n\nmodule.exports = hashSet;", "map": {"version": 3, "names": ["nativeCreate", "require", "HASH_UNDEFINED", "hashSet", "key", "value", "data", "__data__", "size", "has", "undefined", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_hashSet.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\nmodule.exports = hashSet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;;;AACA,IAAIC,cAAc,GAAG,2BAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBC,GAAjB,EAAsBC,KAAtB,EAA6B;EAC3B,IAAIC,IAAI,GAAG,KAAKC,QAAhB;EACA,KAAKC,IAAL,IAAa,KAAKC,GAAL,CAASL,GAAT,IAAgB,CAAhB,GAAoB,CAAjC;EACAE,IAAI,CAACF,GAAD,CAAJ,GAAaJ,YAAY,IAAIK,KAAK,KAAKK,SAA3B,GAAwCR,cAAxC,GAAyDG,KAArE;EACA,OAAO,IAAP;AACD;;AAEDM,MAAM,CAACC,OAAP,GAAiBT,OAAjB"}, "metadata": {}, "sourceType": "script"}