{"ast": null, "code": "'use strict';\n\nvar urlUtils = require('./utils/url'),\n    eventUtils = require('./utils/event'),\n    FacadeJS = require('./facade'),\n    InfoIframeReceiver = require('./info-iframe-receiver'),\n    iframeUtils = require('./utils/iframe'),\n    loc = require('./location');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\n\nmodule.exports = function (SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function (at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  }); // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n  /* eslint-disable camelcase */\n\n  SockJS.bootstrap_iframe = function () {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n\n    var onMessage = function onMessage(e) {\n      if (e.source !== parent) {\n        return;\n      }\n\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n\n      var iframeMessage;\n\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n\n      switch (iframeMessage.type) {\n        case 's':\n          var p;\n\n          try {\n            p = JSON.parse(iframeMessage.data);\n          } catch (ignored) {\n            debug('bad json', iframeMessage.data);\n            break;\n          }\n\n          var version = p[0];\n          var transport = p[1];\n          var transUrl = p[2];\n          var baseUrl = p[3];\n          debug(version, transport, transUrl, baseUrl); // change this to semver logic\n\n          if (version !== SockJS.version) {\n            throw new Error('Incompatible SockJS! Main site uses:' + ' \"' + version + '\", the iframe:' + ' \"' + SockJS.version + '\".');\n          }\n\n          if (!urlUtils.isOriginEqual(transUrl, loc.href) || !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n            throw new Error('Can\\'t connect to different domain from within an ' + 'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n          }\n\n          facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n          break;\n\n        case 'm':\n          facade._send(iframeMessage.data);\n\n          break;\n\n        case 'c':\n          if (facade) {\n            facade._close();\n          }\n\n          facade = null;\n          break;\n      }\n    };\n\n    eventUtils.attachEvent('message', onMessage); // Start\n\n    iframeUtils.postMessage('s');\n  };\n};", "map": {"version": 3, "names": ["urlUtils", "require", "eventUtils", "FacadeJS", "InfoIframeReceiver", "iframe<PERSON><PERSON>s", "loc", "debug", "process", "env", "NODE_ENV", "module", "exports", "SockJS", "availableTransports", "transportMap", "for<PERSON>ach", "at", "facadeTransport", "transportName", "parent<PERSON><PERSON>in", "bootstrap_iframe", "facade", "currentWindowId", "hash", "slice", "onMessage", "e", "source", "parent", "origin", "iframeMessage", "JSON", "parse", "data", "ignored", "windowId", "type", "p", "version", "transport", "transUrl", "baseUrl", "Error", "isOriginEqual", "href", "_send", "_close", "attachEvent", "postMessage"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/iframe-bootstrap.js"], "sourcesContent": ["'use strict';\n\nvar urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , FacadeJS = require('./facade')\n  , InfoIframeReceiver = require('./info-iframe-receiver')\n  , iframeUtils = require('./utils/iframe')\n  , loc = require('./location')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\n\nmodule.exports = function(SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function(at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  });\n\n  // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n\n  /* eslint-disable camelcase */\n  SockJS.bootstrap_iframe = function() {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n    var onMessage = function(e) {\n      if (e.source !== parent) {\n        return;\n      }\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n\n      var iframeMessage;\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n      switch (iframeMessage.type) {\n      case 's':\n        var p;\n        try {\n          p = JSON.parse(iframeMessage.data);\n        } catch (ignored) {\n          debug('bad json', iframeMessage.data);\n          break;\n        }\n        var version = p[0];\n        var transport = p[1];\n        var transUrl = p[2];\n        var baseUrl = p[3];\n        debug(version, transport, transUrl, baseUrl);\n        // change this to semver logic\n        if (version !== SockJS.version) {\n          throw new Error('Incompatible SockJS! Main site uses:' +\n                    ' \"' + version + '\", the iframe:' +\n                    ' \"' + SockJS.version + '\".');\n        }\n\n        if (!urlUtils.isOriginEqual(transUrl, loc.href) ||\n            !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n          throw new Error('Can\\'t connect to different domain from within an ' +\n                    'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n        }\n        facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n        break;\n      case 'm':\n        facade._send(iframeMessage.data);\n        break;\n      case 'c':\n        if (facade) {\n          facade._close();\n        }\n        facade = null;\n        break;\n      }\n    };\n\n    eventUtils.attachEvent('message', onMessage);\n\n    // Start\n    iframeUtils.postMessage('s');\n  };\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAD,CAAtB;AAAA,IACIC,UAAU,GAAGD,OAAO,CAAC,eAAD,CADxB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,UAAD,CAFtB;AAAA,IAGIG,kBAAkB,GAAGH,OAAO,CAAC,wBAAD,CAHhC;AAAA,IAIII,WAAW,GAAGJ,OAAO,CAAC,gBAAD,CAJzB;AAAA,IAKIK,GAAG,GAAGL,OAAO,CAAC,YAAD,CALjB;;AAQA,IAAIM,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGN,OAAO,CAAC,OAAD,CAAP,CAAiB,gCAAjB,CAAR;AACD;;AAEDU,MAAM,CAACC,OAAP,GAAiB,UAASC,MAAT,EAAiBC,mBAAjB,EAAsC;EACrD,IAAIC,YAAY,GAAG,EAAnB;EACAD,mBAAmB,CAACE,OAApB,CAA4B,UAASC,EAAT,EAAa;IACvC,IAAIA,EAAE,CAACC,eAAP,EAAwB;MACtBH,YAAY,CAACE,EAAE,CAACC,eAAH,CAAmBC,aAApB,CAAZ,GAAiDF,EAAE,CAACC,eAApD;IACD;EACF,CAJD,EAFqD,CAQrD;EACA;;EACAH,YAAY,CAACX,kBAAkB,CAACe,aAApB,CAAZ,GAAiDf,kBAAjD;EACA,IAAIgB,YAAJ;EAEA;;EACAP,MAAM,CAACQ,gBAAP,GAA0B,YAAW;IACnC;IACA,IAAIC,MAAJ;IACAjB,WAAW,CAACkB,eAAZ,GAA8BjB,GAAG,CAACkB,IAAJ,CAASC,KAAT,CAAe,CAAf,CAA9B;;IACA,IAAIC,SAAS,GAAG,SAAZA,SAAY,CAASC,CAAT,EAAY;MAC1B,IAAIA,CAAC,CAACC,MAAF,KAAaC,MAAjB,EAAyB;QACvB;MACD;;MACD,IAAI,OAAOT,YAAP,KAAwB,WAA5B,EAAyC;QACvCA,YAAY,GAAGO,CAAC,CAACG,MAAjB;MACD;;MACD,IAAIH,CAAC,CAACG,MAAF,KAAaV,YAAjB,EAA+B;QAC7B;MACD;;MAED,IAAIW,aAAJ;;MACA,IAAI;QACFA,aAAa,GAAGC,IAAI,CAACC,KAAL,CAAWN,CAAC,CAACO,IAAb,CAAhB;MACD,CAFD,CAEE,OAAOC,OAAP,EAAgB;QAChB5B,KAAK,CAAC,UAAD,EAAaoB,CAAC,CAACO,IAAf,CAAL;QACA;MACD;;MAED,IAAIH,aAAa,CAACK,QAAd,KAA2B/B,WAAW,CAACkB,eAA3C,EAA4D;QAC1D;MACD;;MACD,QAAQQ,aAAa,CAACM,IAAtB;QACA,KAAK,GAAL;UACE,IAAIC,CAAJ;;UACA,IAAI;YACFA,CAAC,GAAGN,IAAI,CAACC,KAAL,CAAWF,aAAa,CAACG,IAAzB,CAAJ;UACD,CAFD,CAEE,OAAOC,OAAP,EAAgB;YAChB5B,KAAK,CAAC,UAAD,EAAawB,aAAa,CAACG,IAA3B,CAAL;YACA;UACD;;UACD,IAAIK,OAAO,GAAGD,CAAC,CAAC,CAAD,CAAf;UACA,IAAIE,SAAS,GAAGF,CAAC,CAAC,CAAD,CAAjB;UACA,IAAIG,QAAQ,GAAGH,CAAC,CAAC,CAAD,CAAhB;UACA,IAAII,OAAO,GAAGJ,CAAC,CAAC,CAAD,CAAf;UACA/B,KAAK,CAACgC,OAAD,EAAUC,SAAV,EAAqBC,QAArB,EAA+BC,OAA/B,CAAL,CAZF,CAaE;;UACA,IAAIH,OAAO,KAAK1B,MAAM,CAAC0B,OAAvB,EAAgC;YAC9B,MAAM,IAAII,KAAJ,CAAU,yCACN,IADM,GACCJ,OADD,GACW,gBADX,GAEN,IAFM,GAEC1B,MAAM,CAAC0B,OAFR,GAEkB,IAF5B,CAAN;UAGD;;UAED,IAAI,CAACvC,QAAQ,CAAC4C,aAAT,CAAuBH,QAAvB,EAAiCnC,GAAG,CAACuC,IAArC,CAAD,IACA,CAAC7C,QAAQ,CAAC4C,aAAT,CAAuBF,OAAvB,EAAgCpC,GAAG,CAACuC,IAApC,CADL,EACgD;YAC9C,MAAM,IAAIF,KAAJ,CAAU,uDACN,WADM,GACQrC,GAAG,CAACuC,IADZ,GACmB,IADnB,GAC0BJ,QAD1B,GACqC,IADrC,GAC4CC,OAD5C,GACsD,GADhE,CAAN;UAED;;UACDpB,MAAM,GAAG,IAAInB,QAAJ,CAAa,IAAIY,YAAY,CAACyB,SAAD,CAAhB,CAA4BC,QAA5B,EAAsCC,OAAtC,CAAb,CAAT;UACA;;QACF,KAAK,GAAL;UACEpB,MAAM,CAACwB,KAAP,CAAaf,aAAa,CAACG,IAA3B;;UACA;;QACF,KAAK,GAAL;UACE,IAAIZ,MAAJ,EAAY;YACVA,MAAM,CAACyB,MAAP;UACD;;UACDzB,MAAM,GAAG,IAAT;UACA;MApCF;IAsCD,CA5DD;;IA8DApB,UAAU,CAAC8C,WAAX,CAAuB,SAAvB,EAAkCtB,SAAlC,EAlEmC,CAoEnC;;IACArB,WAAW,CAAC4C,WAAZ,CAAwB,GAAxB;EACD,CAtED;AAuED,CArFD"}, "metadata": {}, "sourceType": "script"}