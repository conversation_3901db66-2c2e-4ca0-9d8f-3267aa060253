{"ast": null, "code": "/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n  map.forEach(function (value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\nmodule.exports = mapToArray;", "map": {"version": 3, "names": ["mapToArray", "map", "index", "result", "Array", "size", "for<PERSON>ach", "value", "key", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapToArray.js"], "sourcesContent": ["/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\nmodule.exports = mapToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAT,CAAoBC,GAApB,EAAyB;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAL,CADlB;EAGAJ,GAAG,CAACK,OAAJ,CAAY,UAASC,KAAT,EAAgBC,GAAhB,EAAqB;IAC/BL,MAAM,CAAC,EAAED,KAAH,CAAN,GAAkB,CAACM,GAAD,EAAMD,KAAN,CAAlB;EACD,CAFD;EAGA,OAAOJ,MAAP;AACD;;AAEDM,MAAM,CAACC,OAAP,GAAiBV,UAAjB"}, "metadata": {}, "sourceType": "script"}