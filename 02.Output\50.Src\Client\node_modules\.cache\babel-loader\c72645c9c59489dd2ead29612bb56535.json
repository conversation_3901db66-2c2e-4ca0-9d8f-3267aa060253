{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport settle from './../core/settle.js';\nimport cookies from './../helpers/cookies.js';\nimport buildURL from './../helpers/buildURL.js';\nimport buildFullPath from '../core/buildFullPath.js';\nimport isURLSameOrigin from './../helpers/isURLSameOrigin.js';\nimport transitionalDefaults from '../defaults/transitional.js';\nimport AxiosError from '../core/AxiosError.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport parseProtocol from '../helpers/parseProtocol.js';\nimport platform from '../platform/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport speedometer from '../helpers/speedometer.js';\n\nfunction progressEventReducer(listener, isDownloadStream) {\n  var bytesNotified = 0;\n\n  var _speedometer = speedometer(50, 250);\n\n  return function (e) {\n    var loaded = e.loaded;\n    var total = e.lengthComputable ? e.total : undefined;\n    var progressBytes = loaded - bytesNotified;\n\n    var rate = _speedometer(progressBytes);\n\n    var inRange = loaded <= total;\n    bytesNotified = loaded;\n    var data = {\n      loaded: loaded,\n      total: total,\n      progress: total ? loaded / total : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined\n    };\n    data[isDownloadStream ? 'download' : 'upload'] = true;\n    listener(data);\n  };\n}\n\nexport default function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    var requestData = config.data;\n    var requestHeaders = AxiosHeaders.from(config.headers).normalize();\n    var responseType = config.responseType;\n    var onCanceled;\n\n    function done() {\n      if (config.cancelToken) {\n        config.cancelToken.unsubscribe(onCanceled);\n      }\n\n      if (config.signal) {\n        config.signal.removeEventListener('abort', onCanceled);\n      }\n    }\n\n    if (utils.isFormData(requestData) && platform.isStandardBrowserEnv) {\n      requestHeaders.setContentType(false); // Let the browser set it\n    }\n\n    var request = new XMLHttpRequest(); // HTTP basic authentication\n\n    if (config.auth) {\n      var username = config.auth.username || '';\n      var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';\n      requestHeaders.set('Authorization', 'Basic ' + btoa(username + ':' + password));\n    }\n\n    var fullPath = buildFullPath(config.baseURL, config.url);\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true); // Set the request timeout in MS\n\n    request.timeout = config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      } // Prepare the response\n\n\n      var responseHeaders = AxiosHeaders.from('getAllResponseHeaders' in request && request.getAllResponseHeaders());\n      var responseData = !responseType || responseType === 'text' || responseType === 'json' ? request.responseText : request.response;\n      var response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config: config,\n        request: request\n      };\n      settle(function _resolve(value) {\n        resolve(value);\n        done();\n      }, function _reject(err) {\n        reject(err);\n        done();\n      }, response); // Clean up request\n\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        } // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n\n\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        } // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n\n\n        setTimeout(onloadend);\n      };\n    } // Handle browser request cancellation (as opposed to a manual cancellation)\n\n\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(new AxiosError('Request aborted', AxiosError.ECONNABORTED, config, request)); // Clean up request\n\n      request = null;\n    }; // Handle low level network errors\n\n\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request)); // Clean up request\n\n      request = null;\n    }; // Handle timeout\n\n\n    request.ontimeout = function handleTimeout() {\n      var timeoutErrorMessage = config.timeout ? 'timeout of ' + config.timeout + 'ms exceeded' : 'timeout exceeded';\n      var transitional = config.transitional || transitionalDefaults;\n\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n\n      reject(new AxiosError(timeoutErrorMessage, transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED, config, request)); // Clean up request\n\n      request = null;\n    }; // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n\n\n    if (platform.isStandardBrowserEnv) {\n      // Add xsrf header\n      var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName && cookies.read(config.xsrfCookieName);\n\n      if (xsrfValue) {\n        requestHeaders.set(config.xsrfHeaderName, xsrfValue);\n      }\n    } // Remove Content-Type if data is undefined\n\n\n    requestData === undefined && requestHeaders.setContentType(null); // Add headers to the request\n\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n        request.setRequestHeader(key, val);\n      });\n    } // Add withCredentials to request if needed\n\n\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    } // Add responseType to request if needed\n\n\n    if (responseType && responseType !== 'json') {\n      request.responseType = config.responseType;\n    } // Handle progress if needed\n\n\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', progressEventReducer(config.onDownloadProgress, true));\n    } // Not all browsers support upload events\n\n\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', progressEventReducer(config.onUploadProgress));\n    }\n\n    if (config.cancelToken || config.signal) {\n      // Handle cancellation\n      // eslint-disable-next-line func-names\n      onCanceled = function onCanceled(cancel) {\n        if (!request) {\n          return;\n        }\n\n        reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n        request.abort();\n        request = null;\n      };\n\n      config.cancelToken && config.cancelToken.subscribe(onCanceled);\n\n      if (config.signal) {\n        config.signal.aborted ? onCanceled() : config.signal.addEventListener('abort', onCanceled);\n      }\n    }\n\n    var protocol = parseProtocol(fullPath);\n\n    if (protocol && platform.protocols.indexOf(protocol) === -1) {\n      reject(new AxiosError('Unsupported protocol ' + protocol + ':', AxiosError.ERR_BAD_REQUEST, config));\n      return;\n    } // Send the request\n\n\n    request.send(requestData || null);\n  });\n}", "map": {"version": 3, "names": ["utils", "settle", "cookies", "buildURL", "buildFullPath", "isURLSameOrigin", "transitionalD<PERSON>ault<PERSON>", "AxiosError", "CanceledError", "parseProtocol", "platform", "AxiosHeaders", "speedometer", "progressEventReducer", "listener", "isDownloadStream", "bytesNotified", "_speedometer", "e", "loaded", "total", "lengthComputable", "undefined", "progressBytes", "rate", "inRange", "data", "progress", "bytes", "estimated", "xhrAdapter", "config", "Promise", "dispatchXhrRequest", "resolve", "reject", "requestData", "requestHeaders", "from", "headers", "normalize", "responseType", "onCanceled", "done", "cancelToken", "unsubscribe", "signal", "removeEventListener", "isFormData", "isStandardBrowserEnv", "setContentType", "request", "XMLHttpRequest", "auth", "username", "password", "unescape", "encodeURIComponent", "set", "btoa", "fullPath", "baseURL", "url", "open", "method", "toUpperCase", "params", "paramsSerializer", "timeout", "onloadend", "responseHeaders", "getAllResponseHeaders", "responseData", "responseText", "response", "status", "statusText", "_resolve", "value", "_reject", "err", "onreadystatechange", "handleLoad", "readyState", "responseURL", "indexOf", "setTimeout", "<PERSON>ab<PERSON>", "handleAbort", "ECONNABORTED", "onerror", "handleError", "ERR_NETWORK", "ontimeout", "handleTimeout", "timeoutErrorMessage", "transitional", "clarifyTimeoutError", "ETIMEDOUT", "xsrfValue", "withCredentials", "xsrfCookieName", "read", "xsrfHeaderName", "for<PERSON>ach", "toJSON", "setRequestHeader", "val", "key", "isUndefined", "onDownloadProgress", "addEventListener", "onUploadProgress", "upload", "cancel", "type", "abort", "subscribe", "aborted", "protocol", "protocols", "ERR_BAD_REQUEST", "send"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/adapters/xhr.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport settle from './../core/settle.js';\nimport cookies from './../helpers/cookies.js';\nimport buildURL from './../helpers/buildURL.js';\nimport buildFullPath from '../core/buildFullPath.js';\nimport isURLSameOrigin from './../helpers/isURLSameOrigin.js';\nimport transitionalDefaults from '../defaults/transitional.js';\nimport AxiosError from '../core/AxiosError.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport parseProtocol from '../helpers/parseProtocol.js';\nimport platform from '../platform/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport speedometer from '../helpers/speedometer.js';\n\nfunction progressEventReducer(listener, isDownloadStream) {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined\n    };\n\n    data[isDownloadStream ? 'download' : 'upload'] = true;\n\n    listener(data);\n  };\n}\n\nexport default function xhrAdapter(config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    let requestData = config.data;\n    const requestHeaders = AxiosHeaders.from(config.headers).normalize();\n    const responseType = config.responseType;\n    let onCanceled;\n    function done() {\n      if (config.cancelToken) {\n        config.cancelToken.unsubscribe(onCanceled);\n      }\n\n      if (config.signal) {\n        config.signal.removeEventListener('abort', onCanceled);\n      }\n    }\n\n    if (utils.isFormData(requestData) && platform.isStandardBrowserEnv) {\n      requestHeaders.setContentType(false); // Let the browser set it\n    }\n\n    let request = new XMLHttpRequest();\n\n    // HTTP basic authentication\n    if (config.auth) {\n      const username = config.auth.username || '';\n      const password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';\n      requestHeaders.set('Authorization', 'Basic ' + btoa(username + ':' + password));\n    }\n\n    const fullPath = buildFullPath(config.baseURL, config.url);\n\n    request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);\n\n    // Set the request timeout in MS\n    request.timeout = config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      const responseHeaders = AxiosHeaders.from(\n        'getAllResponseHeaders' in request && request.getAllResponseHeaders()\n      );\n      const responseData = !responseType || responseType === 'text' ||  responseType === 'json' ?\n        request.responseText : request.response;\n      const response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config,\n        request\n      };\n\n      settle(function _resolve(value) {\n        resolve(value);\n        done();\n      }, function _reject(err) {\n        reject(err);\n        done();\n      }, response);\n\n      // Clean up request\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(new AxiosError('Request aborted', AxiosError.ECONNABORTED, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      let timeoutErrorMessage = config.timeout ? 'timeout of ' + config.timeout + 'ms exceeded' : 'timeout exceeded';\n      const transitional = config.transitional || transitionalDefaults;\n      if (config.timeoutErrorMessage) {\n        timeoutErrorMessage = config.timeoutErrorMessage;\n      }\n      reject(new AxiosError(\n        timeoutErrorMessage,\n        transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,\n        config,\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (platform.isStandardBrowserEnv) {\n      // Add xsrf header\n      const xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath))\n        && config.xsrfCookieName && cookies.read(config.xsrfCookieName);\n\n      if (xsrfValue) {\n        requestHeaders.set(config.xsrfHeaderName, xsrfValue);\n      }\n    }\n\n    // Remove Content-Type if data is undefined\n    requestData === undefined && requestHeaders.setContentType(null);\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n        request.setRequestHeader(key, val);\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(config.withCredentials)) {\n      request.withCredentials = !!config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = config.responseType;\n    }\n\n    // Handle progress if needed\n    if (typeof config.onDownloadProgress === 'function') {\n      request.addEventListener('progress', progressEventReducer(config.onDownloadProgress, true));\n    }\n\n    // Not all browsers support upload events\n    if (typeof config.onUploadProgress === 'function' && request.upload) {\n      request.upload.addEventListener('progress', progressEventReducer(config.onUploadProgress));\n    }\n\n    if (config.cancelToken || config.signal) {\n      // Handle cancellation\n      // eslint-disable-next-line func-names\n      onCanceled = cancel => {\n        if (!request) {\n          return;\n        }\n        reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n        request.abort();\n        request = null;\n      };\n\n      config.cancelToken && config.cancelToken.subscribe(onCanceled);\n      if (config.signal) {\n        config.signal.aborted ? onCanceled() : config.signal.addEventListener('abort', onCanceled);\n      }\n    }\n\n    const protocol = parseProtocol(fullPath);\n\n    if (protocol && platform.protocols.indexOf(protocol) === -1) {\n      reject(new AxiosError('Unsupported protocol ' + protocol + ':', AxiosError.ERR_BAD_REQUEST, config));\n      return;\n    }\n\n\n    // Send the request\n    request.send(requestData || null);\n  });\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,OAAP,MAAoB,yBAApB;AACA,OAAOC,QAAP,MAAqB,0BAArB;AACA,OAAOC,aAAP,MAA0B,0BAA1B;AACA,OAAOC,eAAP,MAA4B,iCAA5B;AACA,OAAOC,oBAAP,MAAiC,6BAAjC;AACA,OAAOC,UAAP,MAAuB,uBAAvB;AACA,OAAOC,aAAP,MAA0B,4BAA1B;AACA,OAAOC,aAAP,MAA0B,6BAA1B;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAOC,YAAP,MAAyB,yBAAzB;AACA,OAAOC,WAAP,MAAwB,2BAAxB;;AAEA,SAASC,oBAAT,CAA8BC,QAA9B,EAAwCC,gBAAxC,EAA0D;EACxD,IAAIC,aAAa,GAAG,CAApB;;EACA,IAAMC,YAAY,GAAGL,WAAW,CAAC,EAAD,EAAK,GAAL,CAAhC;;EAEA,OAAO,UAAAM,CAAC,EAAI;IACV,IAAMC,MAAM,GAAGD,CAAC,CAACC,MAAjB;IACA,IAAMC,KAAK,GAAGF,CAAC,CAACG,gBAAF,GAAqBH,CAAC,CAACE,KAAvB,GAA+BE,SAA7C;IACA,IAAMC,aAAa,GAAGJ,MAAM,GAAGH,aAA/B;;IACA,IAAMQ,IAAI,GAAGP,YAAY,CAACM,aAAD,CAAzB;;IACA,IAAME,OAAO,GAAGN,MAAM,IAAIC,KAA1B;IAEAJ,aAAa,GAAGG,MAAhB;IAEA,IAAMO,IAAI,GAAG;MACXP,MAAM,EAANA,MADW;MAEXC,KAAK,EAALA,KAFW;MAGXO,QAAQ,EAAEP,KAAK,GAAID,MAAM,GAAGC,KAAb,GAAsBE,SAH1B;MAIXM,KAAK,EAAEL,aAJI;MAKXC,IAAI,EAAEA,IAAI,GAAGA,IAAH,GAAUF,SALT;MAMXO,SAAS,EAAEL,IAAI,IAAIJ,KAAR,IAAiBK,OAAjB,GAA2B,CAACL,KAAK,GAAGD,MAAT,IAAmBK,IAA9C,GAAqDF;IANrD,CAAb;IASAI,IAAI,CAACX,gBAAgB,GAAG,UAAH,GAAgB,QAAjC,CAAJ,GAAiD,IAAjD;IAEAD,QAAQ,CAACY,IAAD,CAAR;EACD,CArBD;AAsBD;;AAED,eAAe,SAASI,UAAT,CAAoBC,MAApB,EAA4B;EACzC,OAAO,IAAIC,OAAJ,CAAY,SAASC,kBAAT,CAA4BC,OAA5B,EAAqCC,MAArC,EAA6C;IAC9D,IAAIC,WAAW,GAAGL,MAAM,CAACL,IAAzB;IACA,IAAMW,cAAc,GAAG1B,YAAY,CAAC2B,IAAb,CAAkBP,MAAM,CAACQ,OAAzB,EAAkCC,SAAlC,EAAvB;IACA,IAAMC,YAAY,GAAGV,MAAM,CAACU,YAA5B;IACA,IAAIC,UAAJ;;IACA,SAASC,IAAT,GAAgB;MACd,IAAIZ,MAAM,CAACa,WAAX,EAAwB;QACtBb,MAAM,CAACa,WAAP,CAAmBC,WAAnB,CAA+BH,UAA/B;MACD;;MAED,IAAIX,MAAM,CAACe,MAAX,EAAmB;QACjBf,MAAM,CAACe,MAAP,CAAcC,mBAAd,CAAkC,OAAlC,EAA2CL,UAA3C;MACD;IACF;;IAED,IAAI1C,KAAK,CAACgD,UAAN,CAAiBZ,WAAjB,KAAiC1B,QAAQ,CAACuC,oBAA9C,EAAoE;MAClEZ,cAAc,CAACa,cAAf,CAA8B,KAA9B,EADkE,CAC5B;IACvC;;IAED,IAAIC,OAAO,GAAG,IAAIC,cAAJ,EAAd,CAnB8D,CAqB9D;;IACA,IAAIrB,MAAM,CAACsB,IAAX,EAAiB;MACf,IAAMC,QAAQ,GAAGvB,MAAM,CAACsB,IAAP,CAAYC,QAAZ,IAAwB,EAAzC;MACA,IAAMC,QAAQ,GAAGxB,MAAM,CAACsB,IAAP,CAAYE,QAAZ,GAAuBC,QAAQ,CAACC,kBAAkB,CAAC1B,MAAM,CAACsB,IAAP,CAAYE,QAAb,CAAnB,CAA/B,GAA4E,EAA7F;MACAlB,cAAc,CAACqB,GAAf,CAAmB,eAAnB,EAAoC,WAAWC,IAAI,CAACL,QAAQ,GAAG,GAAX,GAAiBC,QAAlB,CAAnD;IACD;;IAED,IAAMK,QAAQ,GAAGxD,aAAa,CAAC2B,MAAM,CAAC8B,OAAR,EAAiB9B,MAAM,CAAC+B,GAAxB,CAA9B;IAEAX,OAAO,CAACY,IAAR,CAAahC,MAAM,CAACiC,MAAP,CAAcC,WAAd,EAAb,EAA0C9D,QAAQ,CAACyD,QAAD,EAAW7B,MAAM,CAACmC,MAAlB,EAA0BnC,MAAM,CAACoC,gBAAjC,CAAlD,EAAsG,IAAtG,EA9B8D,CAgC9D;;IACAhB,OAAO,CAACiB,OAAR,GAAkBrC,MAAM,CAACqC,OAAzB;;IAEA,SAASC,SAAT,GAAqB;MACnB,IAAI,CAAClB,OAAL,EAAc;QACZ;MACD,CAHkB,CAInB;;;MACA,IAAMmB,eAAe,GAAG3D,YAAY,CAAC2B,IAAb,CACtB,2BAA2Ba,OAA3B,IAAsCA,OAAO,CAACoB,qBAAR,EADhB,CAAxB;MAGA,IAAMC,YAAY,GAAG,CAAC/B,YAAD,IAAiBA,YAAY,KAAK,MAAlC,IAA6CA,YAAY,KAAK,MAA9D,GACnBU,OAAO,CAACsB,YADW,GACItB,OAAO,CAACuB,QADjC;MAEA,IAAMA,QAAQ,GAAG;QACfhD,IAAI,EAAE8C,YADS;QAEfG,MAAM,EAAExB,OAAO,CAACwB,MAFD;QAGfC,UAAU,EAAEzB,OAAO,CAACyB,UAHL;QAIfrC,OAAO,EAAE+B,eAJM;QAKfvC,MAAM,EAANA,MALe;QAMfoB,OAAO,EAAPA;MANe,CAAjB;MASAlD,MAAM,CAAC,SAAS4E,QAAT,CAAkBC,KAAlB,EAAyB;QAC9B5C,OAAO,CAAC4C,KAAD,CAAP;QACAnC,IAAI;MACL,CAHK,EAGH,SAASoC,OAAT,CAAiBC,GAAjB,EAAsB;QACvB7C,MAAM,CAAC6C,GAAD,CAAN;QACArC,IAAI;MACL,CANK,EAMH+B,QANG,CAAN,CAnBmB,CA2BnB;;MACAvB,OAAO,GAAG,IAAV;IACD;;IAED,IAAI,eAAeA,OAAnB,EAA4B;MAC1B;MACAA,OAAO,CAACkB,SAAR,GAAoBA,SAApB;IACD,CAHD,MAGO;MACL;MACAlB,OAAO,CAAC8B,kBAAR,GAA6B,SAASC,UAAT,GAAsB;QACjD,IAAI,CAAC/B,OAAD,IAAYA,OAAO,CAACgC,UAAR,KAAuB,CAAvC,EAA0C;UACxC;QACD,CAHgD,CAKjD;QACA;QACA;QACA;;;QACA,IAAIhC,OAAO,CAACwB,MAAR,KAAmB,CAAnB,IAAwB,EAAExB,OAAO,CAACiC,WAAR,IAAuBjC,OAAO,CAACiC,WAAR,CAAoBC,OAApB,CAA4B,OAA5B,MAAyC,CAAlE,CAA5B,EAAkG;UAChG;QACD,CAXgD,CAYjD;QACA;;;QACAC,UAAU,CAACjB,SAAD,CAAV;MACD,CAfD;IAgBD,CAvF6D,CAyF9D;;;IACAlB,OAAO,CAACoC,OAAR,GAAkB,SAASC,WAAT,GAAuB;MACvC,IAAI,CAACrC,OAAL,EAAc;QACZ;MACD;;MAEDhB,MAAM,CAAC,IAAI5B,UAAJ,CAAe,iBAAf,EAAkCA,UAAU,CAACkF,YAA7C,EAA2D1D,MAA3D,EAAmEoB,OAAnE,CAAD,CAAN,CALuC,CAOvC;;MACAA,OAAO,GAAG,IAAV;IACD,CATD,CA1F8D,CAqG9D;;;IACAA,OAAO,CAACuC,OAAR,GAAkB,SAASC,WAAT,GAAuB;MACvC;MACA;MACAxD,MAAM,CAAC,IAAI5B,UAAJ,CAAe,eAAf,EAAgCA,UAAU,CAACqF,WAA3C,EAAwD7D,MAAxD,EAAgEoB,OAAhE,CAAD,CAAN,CAHuC,CAKvC;;MACAA,OAAO,GAAG,IAAV;IACD,CAPD,CAtG8D,CA+G9D;;;IACAA,OAAO,CAAC0C,SAAR,GAAoB,SAASC,aAAT,GAAyB;MAC3C,IAAIC,mBAAmB,GAAGhE,MAAM,CAACqC,OAAP,GAAiB,gBAAgBrC,MAAM,CAACqC,OAAvB,GAAiC,aAAlD,GAAkE,kBAA5F;MACA,IAAM4B,YAAY,GAAGjE,MAAM,CAACiE,YAAP,IAAuB1F,oBAA5C;;MACA,IAAIyB,MAAM,CAACgE,mBAAX,EAAgC;QAC9BA,mBAAmB,GAAGhE,MAAM,CAACgE,mBAA7B;MACD;;MACD5D,MAAM,CAAC,IAAI5B,UAAJ,CACLwF,mBADK,EAELC,YAAY,CAACC,mBAAb,GAAmC1F,UAAU,CAAC2F,SAA9C,GAA0D3F,UAAU,CAACkF,YAFhE,EAGL1D,MAHK,EAILoB,OAJK,CAAD,CAAN,CAN2C,CAY3C;;MACAA,OAAO,GAAG,IAAV;IACD,CAdD,CAhH8D,CAgI9D;IACA;IACA;;;IACA,IAAIzC,QAAQ,CAACuC,oBAAb,EAAmC;MACjC;MACA,IAAMkD,SAAS,GAAG,CAACpE,MAAM,CAACqE,eAAP,IAA0B/F,eAAe,CAACuD,QAAD,CAA1C,KACb7B,MAAM,CAACsE,cADM,IACYnG,OAAO,CAACoG,IAAR,CAAavE,MAAM,CAACsE,cAApB,CAD9B;;MAGA,IAAIF,SAAJ,EAAe;QACb9D,cAAc,CAACqB,GAAf,CAAmB3B,MAAM,CAACwE,cAA1B,EAA0CJ,SAA1C;MACD;IACF,CA3I6D,CA6I9D;;;IACA/D,WAAW,KAAKd,SAAhB,IAA6Be,cAAc,CAACa,cAAf,CAA8B,IAA9B,CAA7B,CA9I8D,CAgJ9D;;IACA,IAAI,sBAAsBC,OAA1B,EAAmC;MACjCnD,KAAK,CAACwG,OAAN,CAAcnE,cAAc,CAACoE,MAAf,EAAd,EAAuC,SAASC,gBAAT,CAA0BC,GAA1B,EAA+BC,GAA/B,EAAoC;QACzEzD,OAAO,CAACuD,gBAAR,CAAyBE,GAAzB,EAA8BD,GAA9B;MACD,CAFD;IAGD,CArJ6D,CAuJ9D;;;IACA,IAAI,CAAC3G,KAAK,CAAC6G,WAAN,CAAkB9E,MAAM,CAACqE,eAAzB,CAAL,EAAgD;MAC9CjD,OAAO,CAACiD,eAAR,GAA0B,CAAC,CAACrE,MAAM,CAACqE,eAAnC;IACD,CA1J6D,CA4J9D;;;IACA,IAAI3D,YAAY,IAAIA,YAAY,KAAK,MAArC,EAA6C;MAC3CU,OAAO,CAACV,YAAR,GAAuBV,MAAM,CAACU,YAA9B;IACD,CA/J6D,CAiK9D;;;IACA,IAAI,OAAOV,MAAM,CAAC+E,kBAAd,KAAqC,UAAzC,EAAqD;MACnD3D,OAAO,CAAC4D,gBAAR,CAAyB,UAAzB,EAAqClG,oBAAoB,CAACkB,MAAM,CAAC+E,kBAAR,EAA4B,IAA5B,CAAzD;IACD,CApK6D,CAsK9D;;;IACA,IAAI,OAAO/E,MAAM,CAACiF,gBAAd,KAAmC,UAAnC,IAAiD7D,OAAO,CAAC8D,MAA7D,EAAqE;MACnE9D,OAAO,CAAC8D,MAAR,CAAeF,gBAAf,CAAgC,UAAhC,EAA4ClG,oBAAoB,CAACkB,MAAM,CAACiF,gBAAR,CAAhE;IACD;;IAED,IAAIjF,MAAM,CAACa,WAAP,IAAsBb,MAAM,CAACe,MAAjC,EAAyC;MACvC;MACA;MACAJ,UAAU,GAAG,oBAAAwE,MAAM,EAAI;QACrB,IAAI,CAAC/D,OAAL,EAAc;UACZ;QACD;;QACDhB,MAAM,CAAC,CAAC+E,MAAD,IAAWA,MAAM,CAACC,IAAlB,GAAyB,IAAI3G,aAAJ,CAAkB,IAAlB,EAAwBuB,MAAxB,EAAgCoB,OAAhC,CAAzB,GAAoE+D,MAArE,CAAN;QACA/D,OAAO,CAACiE,KAAR;QACAjE,OAAO,GAAG,IAAV;MACD,CAPD;;MASApB,MAAM,CAACa,WAAP,IAAsBb,MAAM,CAACa,WAAP,CAAmByE,SAAnB,CAA6B3E,UAA7B,CAAtB;;MACA,IAAIX,MAAM,CAACe,MAAX,EAAmB;QACjBf,MAAM,CAACe,MAAP,CAAcwE,OAAd,GAAwB5E,UAAU,EAAlC,GAAuCX,MAAM,CAACe,MAAP,CAAciE,gBAAd,CAA+B,OAA/B,EAAwCrE,UAAxC,CAAvC;MACD;IACF;;IAED,IAAM6E,QAAQ,GAAG9G,aAAa,CAACmD,QAAD,CAA9B;;IAEA,IAAI2D,QAAQ,IAAI7G,QAAQ,CAAC8G,SAAT,CAAmBnC,OAAnB,CAA2BkC,QAA3B,MAAyC,CAAC,CAA1D,EAA6D;MAC3DpF,MAAM,CAAC,IAAI5B,UAAJ,CAAe,0BAA0BgH,QAA1B,GAAqC,GAApD,EAAyDhH,UAAU,CAACkH,eAApE,EAAqF1F,MAArF,CAAD,CAAN;MACA;IACD,CAlM6D,CAqM9D;;;IACAoB,OAAO,CAACuE,IAAR,CAAatF,WAAW,IAAI,IAA5B;EACD,CAvMM,CAAP;AAwMD"}, "metadata": {}, "sourceType": "module"}