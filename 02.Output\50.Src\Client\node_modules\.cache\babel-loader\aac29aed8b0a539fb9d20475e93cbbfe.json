{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import Cell from'./elements/Cell';import{getCellFace,isValidSource,toThousands}from'../utils/Util.js';/**\r\n * 総合度数コンテンツ<br>\r\n * propsは、「3.11総合度数コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module TotalFrequency\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var TotalFrequency=function TotalFrequency(props){var cell1,cell2,cell3;if(isValidSource(props)&&props.agg_unit){cell1=getCellFace(props.agg_unit[0],'text-right col-start-2 justify');cell2=getCellFace(props.agg_unit[1],'text-right col-start-2 justify');cell3=getCellFace(props.agg_unit[2],'text-right col-start-2 justify');}var MAX_ROW=5;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{title:'119着信度数'}),isValidSource(props)&&/*#__PURE__*/_jsxs(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]\",children:[props.agg_unit&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"col-start-3 grid grid-cols-[1fr,13.5rem]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1))}),/*#__PURE__*/_jsx(\"div\",{className:\"col-start-5 grid grid-cols-[1fr,13.5rem]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},cell2))}),/*#__PURE__*/_jsx(\"div\",{className:\"col-start-7 grid grid-cols-[1fr,13.5rem]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},cell3))})]}),props.agg_info&&props.agg_info.map(function(infoItem,infoIndex){if(infoIndex>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(TotalFrequencyRow,{agg_info:infoItem},infoIndex);})]})]});};var TotalFrequencyRow=function TotalFrequencyRow(props){var _props$agg_info,_props$agg_info$numbe;var typeProp=getCellFace(props.agg_info,'text-5xl justify');var numCell1;var numCell2;var numCell3;(_props$agg_info=props.agg_info)===null||_props$agg_info===void 0?void 0:(_props$agg_info$numbe=_props$agg_info.number_list)===null||_props$agg_info$numbe===void 0?void 0:_props$agg_info$numbe.forEach(function(numItem,numIndex){if(numIndex===0){numCell1=getCellFace(numItem,'justify-self-end w-fit text-6xl col-start-3');numCell1.text=toThousands(numCell1.text);addUnit(numCell1);}else if(numIndex===1){numCell2=getCellFace(numItem,'justify-self-end w-fit text-6xl col-start-5');numCell2.text=toThousands(numCell2.text);addUnit(numCell2);}else if(numIndex===2){numCell3=getCellFace(numItem,'justify-self-end w-fit text-6xl col-start-7');numCell3.text=toThousands(numCell3.text);addUnit(numCell3);}});// props.agg_info.number_listのLength<3の場合に、Dummyの列情報を作る\nif(!numCell1){numCell1={className:'col-start-3'};}if(!numCell2){numCell2={className:'col-start-5'};}if(!numCell3){numCell3={className:'col-start-7'};}return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},typeProp)),/*#__PURE__*/_jsx(Cell,_objectSpread({},numCell1)),/*#__PURE__*/_jsx(Cell,_objectSpread({},numCell2)),/*#__PURE__*/_jsx(Cell,_objectSpread({},numCell3))]});};function addUnit(countObj){if(countObj.text&&countObj.text.endsWith('件')){countObj.text=countObj.text.substring(countObj.text.length-1);}countObj.unit='件';countObj.unitStyle={fontSize:'3.5rem'};}export default TotalFrequency;", "map": {"version": 3, "names": ["React", "Title", "Cell", "getCellFace", "isValidSource", "toThousands", "TotalFrequency", "props", "cell1", "cell2", "cell3", "agg_unit", "MAX_ROW", "agg_info", "map", "infoItem", "infoIndex", "undefined", "TotalFrequencyRow", "typeProp", "numCell1", "numCell2", "numCell3", "number_list", "for<PERSON>ach", "numItem", "numIndex", "text", "addUnit", "className", "count<PERSON>b<PERSON>", "endsWith", "substring", "length", "unit", "unitStyle", "fontSize"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/TotalFrequency.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource, toThousands } from '../utils/Util.js';\r\n\r\n/**\r\n * 総合度数コンテンツ<br>\r\n * propsは、「3.11総合度数コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module TotalFrequency\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst TotalFrequency = (props) => {\r\n  let cell1, cell2, cell3;\r\n  if (isValidSource(props) && props.agg_unit) {\r\n    cell1 = getCellFace(props.agg_unit[0], 'text-right col-start-2 justify');\r\n    cell2 = getCellFace(props.agg_unit[1], 'text-right col-start-2 justify');\r\n    cell3 = getCellFace(props.agg_unit[2], 'text-right col-start-2 justify');\r\n  }\r\n\r\n  const MAX_ROW = 5;\r\n\r\n  return (\r\n    <div>\r\n      <Title title={'119着信度数'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]\">\r\n          {props.agg_unit && (\r\n            <>\r\n              <div className=\"col-start-3 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell1} />\r\n              </div>\r\n              <div className=\"col-start-5 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell2} />\r\n              </div>\r\n              <div className=\"col-start-7 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell3} />\r\n              </div>\r\n            </>\r\n          )}\r\n          {props.agg_info &&\r\n            props.agg_info.map((infoItem, infoIndex) => {\r\n              if (infoIndex >= MAX_ROW) return undefined;\r\n\r\n              return <TotalFrequencyRow key={infoIndex} agg_info={infoItem} />;\r\n            })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst TotalFrequencyRow = (props) => {\r\n  let typeProp = getCellFace(props.agg_info, 'text-5xl justify');\r\n  let numCell1;\r\n  let numCell2;\r\n  let numCell3;\r\n\r\n  props.agg_info?.number_list?.forEach((numItem, numIndex) => {\r\n    if (numIndex === 0) {\r\n      numCell1 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-3'\r\n      );\r\n      numCell1.text = toThousands(numCell1.text);\r\n      addUnit(numCell1);\r\n    } else if (numIndex === 1) {\r\n      numCell2 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-5'\r\n      );\r\n      numCell2.text = toThousands(numCell2.text);\r\n      addUnit(numCell2);\r\n    } else if (numIndex === 2) {\r\n      numCell3 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-7'\r\n      );\r\n      numCell3.text = toThousands(numCell3.text);\r\n      addUnit(numCell3);\r\n    }\r\n  });\r\n\r\n  // props.agg_info.number_listのLength<3の場合に、Dummyの列情報を作る\r\n  if (!numCell1) {\r\n    numCell1 = { className: 'col-start-3' };\r\n  }\r\n  if (!numCell2) {\r\n    numCell2 = { className: 'col-start-5' };\r\n  }\r\n  if (!numCell3) {\r\n    numCell3 = { className: 'col-start-7' };\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Cell {...typeProp} />\r\n      <Cell {...numCell1} />\r\n      <Cell {...numCell2} />\r\n      <Cell {...numCell3} />\r\n    </>\r\n  );\r\n};\r\n\r\nfunction addUnit(countObj) {\r\n  if (countObj.text && countObj.text.endsWith('件')) {\r\n    countObj.text = countObj.text.substring(countObj.text.length - 1);\r\n  }\r\n\r\n  countObj.unit = '件';\r\n  countObj.unitStyle = { fontSize: '3.5rem' };\r\n}\r\n\r\nexport default TotalFrequency;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,eAAc,CAAG,QAAjBA,eAAiB,CAACC,KAAD,CAAW,CAChC,GAAIC,MAAJ,CAAWC,KAAX,CAAkBC,KAAlB,CACA,GAAIN,aAAa,CAACG,KAAD,CAAb,EAAwBA,KAAK,CAACI,QAAlC,CAA4C,CAC1CH,KAAK,CAAGL,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,CAAoB,gCAApB,CAAnB,CACAF,KAAK,CAAGN,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,CAAoB,gCAApB,CAAnB,CACAD,KAAK,CAAGP,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,CAAoB,gCAApB,CAAnB,CACD,CAED,GAAMC,QAAO,CAAG,CAAhB,CAEA,mBACE,oCACE,KAAC,KAAD,EAAO,KAAK,CAAE,SAAd,EADF,CAEGR,aAAa,CAACG,KAAD,CAAb,eACC,aAAK,SAAS,CAAC,gMAAf,WACGA,KAAK,CAACI,QAAN,eACC,wCACE,YAAK,SAAS,CAAC,0CAAf,uBACE,KAAC,IAAD,kBAAUH,KAAV,EADF,EADF,cAIE,YAAK,SAAS,CAAC,0CAAf,uBACE,KAAC,IAAD,kBAAUC,KAAV,EADF,EAJF,cAOE,YAAK,SAAS,CAAC,0CAAf,uBACE,KAAC,IAAD,kBAAUC,KAAV,EADF,EAPF,GAFJ,CAcGH,KAAK,CAACM,QAAN,EACCN,KAAK,CAACM,QAAN,CAAeC,GAAf,CAAmB,SAACC,QAAD,CAAWC,SAAX,CAAyB,CAC1C,GAAIA,SAAS,EAAIJ,OAAjB,CAA0B,MAAOK,UAAP,CAE1B,mBAAO,KAAC,iBAAD,EAAmC,QAAQ,CAAEF,QAA7C,EAAwBC,SAAxB,CAAP,CACD,CAJD,CAfJ,GAHJ,GADF,CA4BD,CAtCD,CAwCA,GAAME,kBAAiB,CAAG,QAApBA,kBAAoB,CAACX,KAAD,CAAW,2CACnC,GAAIY,SAAQ,CAAGhB,WAAW,CAACI,KAAK,CAACM,QAAP,CAAiB,kBAAjB,CAA1B,CACA,GAAIO,SAAJ,CACA,GAAIC,SAAJ,CACA,GAAIC,SAAJ,CAEA,iBAAAf,KAAK,CAACM,QAAN,iFAAgBU,WAAhB,sEAA6BC,OAA7B,CAAqC,SAACC,OAAD,CAAUC,QAAV,CAAuB,CAC1D,GAAIA,QAAQ,GAAK,CAAjB,CAAoB,CAClBN,QAAQ,CAAGjB,WAAW,CACpBsB,OADoB,CAEpB,6CAFoB,CAAtB,CAIAL,QAAQ,CAACO,IAAT,CAAgBtB,WAAW,CAACe,QAAQ,CAACO,IAAV,CAA3B,CACAC,OAAO,CAACR,QAAD,CAAP,CACD,CAPD,IAOO,IAAIM,QAAQ,GAAK,CAAjB,CAAoB,CACzBL,QAAQ,CAAGlB,WAAW,CACpBsB,OADoB,CAEpB,6CAFoB,CAAtB,CAIAJ,QAAQ,CAACM,IAAT,CAAgBtB,WAAW,CAACgB,QAAQ,CAACM,IAAV,CAA3B,CACAC,OAAO,CAACP,QAAD,CAAP,CACD,CAPM,IAOA,IAAIK,QAAQ,GAAK,CAAjB,CAAoB,CACzBJ,QAAQ,CAAGnB,WAAW,CACpBsB,OADoB,CAEpB,6CAFoB,CAAtB,CAIAH,QAAQ,CAACK,IAAT,CAAgBtB,WAAW,CAACiB,QAAQ,CAACK,IAAV,CAA3B,CACAC,OAAO,CAACN,QAAD,CAAP,CACD,CACF,CAvBD,EAyBA;AACA,GAAI,CAACF,QAAL,CAAe,CACbA,QAAQ,CAAG,CAAES,SAAS,CAAE,aAAb,CAAX,CACD,CACD,GAAI,CAACR,QAAL,CAAe,CACbA,QAAQ,CAAG,CAAEQ,SAAS,CAAE,aAAb,CAAX,CACD,CACD,GAAI,CAACP,QAAL,CAAe,CACbA,QAAQ,CAAG,CAAEO,SAAS,CAAE,aAAb,CAAX,CACD,CAED,mBACE,wCACE,KAAC,IAAD,kBAAUV,QAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,QAAV,EAFF,cAGE,KAAC,IAAD,kBAAUC,QAAV,EAHF,cAIE,KAAC,IAAD,kBAAUC,QAAV,EAJF,GADF,CAQD,CAlDD,CAoDA,QAASM,QAAT,CAAiBE,QAAjB,CAA2B,CACzB,GAAIA,QAAQ,CAACH,IAAT,EAAiBG,QAAQ,CAACH,IAAT,CAAcI,QAAd,CAAuB,GAAvB,CAArB,CAAkD,CAChDD,QAAQ,CAACH,IAAT,CAAgBG,QAAQ,CAACH,IAAT,CAAcK,SAAd,CAAwBF,QAAQ,CAACH,IAAT,CAAcM,MAAd,CAAuB,CAA/C,CAAhB,CACD,CAEDH,QAAQ,CAACI,IAAT,CAAgB,GAAhB,CACAJ,QAAQ,CAACK,SAAT,CAAqB,CAAEC,QAAQ,CAAE,QAAZ,CAArB,CACD,CAED,cAAe9B,eAAf"}, "metadata": {}, "sourceType": "module"}