{"ast": null, "code": "'use strict';\n\nexport default function parseProtocol(url) {\n  var match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}", "map": {"version": 3, "names": ["parseProtocol", "url", "match", "exec"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/parseProtocol.js"], "sourcesContent": ["'use strict';\n\nexport default function parseProtocol(url) {\n  const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}\n"], "mappings": "AAAA;;AAEA,eAAe,SAASA,aAAT,CAAuBC,GAAvB,EAA4B;EACzC,IAAMC,KAAK,GAAG,4BAA4BC,IAA5B,CAAiCF,GAAjC,CAAd;EACA,OAAOC,KAAK,IAAIA,KAAK,CAAC,CAAD,CAAd,IAAqB,EAA5B;AACD"}, "metadata": {}, "sourceType": "module"}