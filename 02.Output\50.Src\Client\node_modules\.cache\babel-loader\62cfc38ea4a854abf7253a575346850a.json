{"ast": null, "code": "var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call; // eslint-disable-next-line es-x/no-reflect -- safe\n\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "FunctionPrototype", "Function", "prototype", "apply", "call", "module", "exports", "Reflect", "bind", "arguments"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/core-js-pure/internals/function-apply.js"], "sourcesContent": ["var NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es-x/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAD,CAAzB;;AAEA,IAAIC,iBAAiB,GAAGC,QAAQ,CAACC,SAAjC;AACA,IAAIC,KAAK,GAAGH,iBAAiB,CAACG,KAA9B;AACA,IAAIC,IAAI,GAAGJ,iBAAiB,CAACI,IAA7B,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiB,OAAOC,OAAP,IAAkB,QAAlB,IAA8BA,OAAO,CAACJ,KAAtC,KAAgDL,WAAW,GAAGM,IAAI,CAACI,IAAL,CAAUL,KAAV,CAAH,GAAsB,YAAY;EAC5G,OAAOC,IAAI,CAACD,KAAL,CAAWA,KAAX,EAAkBM,SAAlB,CAAP;AACD,CAFgB,CAAjB"}, "metadata": {}, "sourceType": "script"}