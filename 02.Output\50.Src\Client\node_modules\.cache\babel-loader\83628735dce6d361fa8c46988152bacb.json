{"ast": null, "code": "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "require"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/react/jsx-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCC,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,2CAAD,CAAxB;AACD,CAFD,MAEO;EACLF,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,wCAAD,CAAxB;AACD"}, "metadata": {}, "sourceType": "script"}