{"ast": null, "code": "/** @license React v17.0.2\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar b = 60103,\n    c = 60106,\n    d = 60107,\n    e = 60108,\n    f = 60114,\n    g = 60109,\n    h = 60110,\n    k = 60112,\n    l = 60113,\n    m = 60120,\n    n = 60115,\n    p = 60116,\n    q = 60121,\n    r = 60122,\n    u = 60117,\n    v = 60129,\n    w = 60131;\n\nif (\"function\" === typeof Symbol && Symbol.for) {\n  var x = Symbol.for;\n  b = x(\"react.element\");\n  c = x(\"react.portal\");\n  d = x(\"react.fragment\");\n  e = x(\"react.strict_mode\");\n  f = x(\"react.profiler\");\n  g = x(\"react.provider\");\n  h = x(\"react.context\");\n  k = x(\"react.forward_ref\");\n  l = x(\"react.suspense\");\n  m = x(\"react.suspense_list\");\n  n = x(\"react.memo\");\n  p = x(\"react.lazy\");\n  q = x(\"react.block\");\n  r = x(\"react.server.block\");\n  u = x(\"react.fundamental\");\n  v = x(\"react.debug_trace_mode\");\n  w = x(\"react.legacy_hidden\");\n}\n\nfunction y(a) {\n  if (\"object\" === typeof a && null !== a) {\n    var t = a.$$typeof;\n\n    switch (t) {\n      case b:\n        switch (a = a.type, a) {\n          case d:\n          case f:\n          case e:\n          case l:\n          case m:\n            return a;\n\n          default:\n            switch (a = a && a.$$typeof, a) {\n              case h:\n              case k:\n              case p:\n              case n:\n              case g:\n                return a;\n\n              default:\n                return t;\n            }\n\n        }\n\n      case c:\n        return t;\n    }\n  }\n}\n\nvar z = g,\n    A = b,\n    B = k,\n    C = d,\n    D = p,\n    E = n,\n    F = c,\n    G = f,\n    H = e,\n    I = l;\nexports.ContextConsumer = h;\nexports.ContextProvider = z;\nexports.Element = A;\nexports.ForwardRef = B;\nexports.Fragment = C;\nexports.Lazy = D;\nexports.Memo = E;\nexports.Portal = F;\nexports.Profiler = G;\nexports.StrictMode = H;\nexports.Suspense = I;\n\nexports.isAsyncMode = function () {\n  return !1;\n};\n\nexports.isConcurrentMode = function () {\n  return !1;\n};\n\nexports.isContextConsumer = function (a) {\n  return y(a) === h;\n};\n\nexports.isContextProvider = function (a) {\n  return y(a) === g;\n};\n\nexports.isElement = function (a) {\n  return \"object\" === typeof a && null !== a && a.$$typeof === b;\n};\n\nexports.isForwardRef = function (a) {\n  return y(a) === k;\n};\n\nexports.isFragment = function (a) {\n  return y(a) === d;\n};\n\nexports.isLazy = function (a) {\n  return y(a) === p;\n};\n\nexports.isMemo = function (a) {\n  return y(a) === n;\n};\n\nexports.isPortal = function (a) {\n  return y(a) === c;\n};\n\nexports.isProfiler = function (a) {\n  return y(a) === f;\n};\n\nexports.isStrictMode = function (a) {\n  return y(a) === e;\n};\n\nexports.isSuspense = function (a) {\n  return y(a) === l;\n};\n\nexports.isValidElementType = function (a) {\n  return \"string\" === typeof a || \"function\" === typeof a || a === d || a === f || a === v || a === e || a === l || a === m || a === w || \"object\" === typeof a && null !== a && (a.$$typeof === p || a.$$typeof === n || a.$$typeof === g || a.$$typeof === h || a.$$typeof === k || a.$$typeof === u || a.$$typeof === q || a[0] === r) ? !0 : !1;\n};\n\nexports.typeOf = y;", "map": {"version": 3, "names": ["b", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "u", "v", "w", "Symbol", "for", "x", "y", "a", "t", "$$typeof", "type", "z", "A", "B", "C", "D", "E", "F", "G", "H", "I", "exports", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/react-is/cjs/react-is.production.min.js"], "sourcesContent": ["/** @license React v17.0.2\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var b=60103,c=60106,d=60107,e=60108,f=60114,g=60109,h=60110,k=60112,l=60113,m=60120,n=60115,p=60116,q=60121,r=60122,u=60117,v=60129,w=60131;\nif(\"function\"===typeof Symbol&&Symbol.for){var x=Symbol.for;b=x(\"react.element\");c=x(\"react.portal\");d=x(\"react.fragment\");e=x(\"react.strict_mode\");f=x(\"react.profiler\");g=x(\"react.provider\");h=x(\"react.context\");k=x(\"react.forward_ref\");l=x(\"react.suspense\");m=x(\"react.suspense_list\");n=x(\"react.memo\");p=x(\"react.lazy\");q=x(\"react.block\");r=x(\"react.server.block\");u=x(\"react.fundamental\");v=x(\"react.debug_trace_mode\");w=x(\"react.legacy_hidden\")}\nfunction y(a){if(\"object\"===typeof a&&null!==a){var t=a.$$typeof;switch(t){case b:switch(a=a.type,a){case d:case f:case e:case l:case m:return a;default:switch(a=a&&a.$$typeof,a){case h:case k:case p:case n:case g:return a;default:return t}}case c:return t}}}var z=g,A=b,B=k,C=d,D=p,E=n,F=c,G=f,H=e,I=l;exports.ContextConsumer=h;exports.ContextProvider=z;exports.Element=A;exports.ForwardRef=B;exports.Fragment=C;exports.Lazy=D;exports.Memo=E;exports.Portal=F;exports.Profiler=G;exports.StrictMode=H;\nexports.Suspense=I;exports.isAsyncMode=function(){return!1};exports.isConcurrentMode=function(){return!1};exports.isContextConsumer=function(a){return y(a)===h};exports.isContextProvider=function(a){return y(a)===g};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===b};exports.isForwardRef=function(a){return y(a)===k};exports.isFragment=function(a){return y(a)===d};exports.isLazy=function(a){return y(a)===p};exports.isMemo=function(a){return y(a)===n};\nexports.isPortal=function(a){return y(a)===c};exports.isProfiler=function(a){return y(a)===f};exports.isStrictMode=function(a){return y(a)===e};exports.isSuspense=function(a){return y(a)===l};exports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===d||a===f||a===v||a===e||a===l||a===m||a===w||\"object\"===typeof a&&null!==a&&(a.$$typeof===p||a.$$typeof===n||a.$$typeof===g||a.$$typeof===h||a.$$typeof===k||a.$$typeof===u||a.$$typeof===q||a[0]===r)?!0:!1};\nexports.typeOf=y;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAAa,IAAIA,CAAC,GAAC,KAAN;AAAA,IAAYC,CAAC,GAAC,KAAd;AAAA,IAAoBC,CAAC,GAAC,KAAtB;AAAA,IAA4BC,CAAC,GAAC,KAA9B;AAAA,IAAoCC,CAAC,GAAC,KAAtC;AAAA,IAA4CC,CAAC,GAAC,KAA9C;AAAA,IAAoDC,CAAC,GAAC,KAAtD;AAAA,IAA4DC,CAAC,GAAC,KAA9D;AAAA,IAAoEC,CAAC,GAAC,KAAtE;AAAA,IAA4EC,CAAC,GAAC,KAA9E;AAAA,IAAoFC,CAAC,GAAC,KAAtF;AAAA,IAA4FC,CAAC,GAAC,KAA9F;AAAA,IAAoGC,CAAC,GAAC,KAAtG;AAAA,IAA4GC,CAAC,GAAC,KAA9G;AAAA,IAAoHC,CAAC,GAAC,KAAtH;AAAA,IAA4HC,CAAC,GAAC,KAA9H;AAAA,IAAoIC,CAAC,GAAC,KAAtI;;AACb,IAAG,eAAa,OAAOC,MAApB,IAA4BA,MAAM,CAACC,GAAtC,EAA0C;EAAC,IAAIC,CAAC,GAACF,MAAM,CAACC,GAAb;EAAiBlB,CAAC,GAACmB,CAAC,CAAC,eAAD,CAAH;EAAqBlB,CAAC,GAACkB,CAAC,CAAC,cAAD,CAAH;EAAoBjB,CAAC,GAACiB,CAAC,CAAC,gBAAD,CAAH;EAAsBhB,CAAC,GAACgB,CAAC,CAAC,mBAAD,CAAH;EAAyBf,CAAC,GAACe,CAAC,CAAC,gBAAD,CAAH;EAAsBd,CAAC,GAACc,CAAC,CAAC,gBAAD,CAAH;EAAsBb,CAAC,GAACa,CAAC,CAAC,eAAD,CAAH;EAAqBZ,CAAC,GAACY,CAAC,CAAC,mBAAD,CAAH;EAAyBX,CAAC,GAACW,CAAC,CAAC,gBAAD,CAAH;EAAsBV,CAAC,GAACU,CAAC,CAAC,qBAAD,CAAH;EAA2BT,CAAC,GAACS,CAAC,CAAC,YAAD,CAAH;EAAkBR,CAAC,GAACQ,CAAC,CAAC,YAAD,CAAH;EAAkBP,CAAC,GAACO,CAAC,CAAC,aAAD,CAAH;EAAmBN,CAAC,GAACM,CAAC,CAAC,oBAAD,CAAH;EAA0BL,CAAC,GAACK,CAAC,CAAC,mBAAD,CAAH;EAAyBJ,CAAC,GAACI,CAAC,CAAC,wBAAD,CAAH;EAA8BH,CAAC,GAACG,CAAC,CAAC,qBAAD,CAAH;AAA2B;;AAClc,SAASC,CAAT,CAAWC,CAAX,EAAa;EAAC,IAAG,aAAW,OAAOA,CAAlB,IAAqB,SAAOA,CAA/B,EAAiC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,QAAR;;IAAiB,QAAOD,CAAP;MAAU,KAAKtB,CAAL;QAAO,QAAOqB,CAAC,GAACA,CAAC,CAACG,IAAJ,EAASH,CAAhB;UAAmB,KAAKnB,CAAL;UAAO,KAAKE,CAAL;UAAO,KAAKD,CAAL;UAAO,KAAKK,CAAL;UAAO,KAAKC,CAAL;YAAO,OAAOY,CAAP;;UAAS;YAAQ,QAAOA,CAAC,GAACA,CAAC,IAAEA,CAAC,CAACE,QAAP,EAAgBF,CAAvB;cAA0B,KAAKf,CAAL;cAAO,KAAKC,CAAL;cAAO,KAAKI,CAAL;cAAO,KAAKD,CAAL;cAAO,KAAKL,CAAL;gBAAO,OAAOgB,CAAP;;cAAS;gBAAQ,OAAOC,CAAP;YAA9E;;QAAvE;;MAA+J,KAAKrB,CAAL;QAAO,OAAOqB,CAAP;IAAvL;EAAiM;AAAC;;AAAA,IAAIG,CAAC,GAACpB,CAAN;AAAA,IAAQqB,CAAC,GAAC1B,CAAV;AAAA,IAAY2B,CAAC,GAACpB,CAAd;AAAA,IAAgBqB,CAAC,GAAC1B,CAAlB;AAAA,IAAoB2B,CAAC,GAAClB,CAAtB;AAAA,IAAwBmB,CAAC,GAACpB,CAA1B;AAAA,IAA4BqB,CAAC,GAAC9B,CAA9B;AAAA,IAAgC+B,CAAC,GAAC5B,CAAlC;AAAA,IAAoC6B,CAAC,GAAC9B,CAAtC;AAAA,IAAwC+B,CAAC,GAAC1B,CAA1C;AAA4C2B,OAAO,CAACC,eAAR,GAAwB9B,CAAxB;AAA0B6B,OAAO,CAACE,eAAR,GAAwBZ,CAAxB;AAA0BU,OAAO,CAACG,OAAR,GAAgBZ,CAAhB;AAAkBS,OAAO,CAACI,UAAR,GAAmBZ,CAAnB;AAAqBQ,OAAO,CAACK,QAAR,GAAiBZ,CAAjB;AAAmBO,OAAO,CAACM,IAAR,GAAaZ,CAAb;AAAeM,OAAO,CAACO,IAAR,GAAaZ,CAAb;AAAeK,OAAO,CAACQ,MAAR,GAAeZ,CAAf;AAAiBI,OAAO,CAACS,QAAR,GAAiBZ,CAAjB;AAAmBG,OAAO,CAACU,UAAR,GAAmBZ,CAAnB;AAC/dE,OAAO,CAACW,QAAR,GAAiBZ,CAAjB;;AAAmBC,OAAO,CAACY,WAAR,GAAoB,YAAU;EAAC,OAAM,CAAC,CAAP;AAAS,CAAxC;;AAAyCZ,OAAO,CAACa,gBAAR,GAAyB,YAAU;EAAC,OAAM,CAAC,CAAP;AAAS,CAA7C;;AAA8Cb,OAAO,CAACc,iBAAR,GAA0B,UAAS5B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOf,CAAd;AAAgB,CAAtD;;AAAuD6B,OAAO,CAACe,iBAAR,GAA0B,UAAS7B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOhB,CAAd;AAAgB,CAAtD;;AAAuD8B,OAAO,CAACgB,SAAR,GAAkB,UAAS9B,CAAT,EAAW;EAAC,OAAM,aAAW,OAAOA,CAAlB,IAAqB,SAAOA,CAA5B,IAA+BA,CAAC,CAACE,QAAF,KAAavB,CAAlD;AAAoD,CAAlF;;AAAmFmC,OAAO,CAACiB,YAAR,GAAqB,UAAS/B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOd,CAAd;AAAgB,CAAjD;;AAAkD4B,OAAO,CAACkB,UAAR,GAAmB,UAAShC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOnB,CAAd;AAAgB,CAA/C;;AAAgDiC,OAAO,CAACmB,MAAR,GAAe,UAASjC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOV,CAAd;AAAgB,CAA3C;;AAA4CwB,OAAO,CAACoB,MAAR,GAAe,UAASlC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOX,CAAd;AAAgB,CAA3C;;AACzbyB,OAAO,CAACqB,QAAR,GAAiB,UAASnC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOpB,CAAd;AAAgB,CAA7C;;AAA8CkC,OAAO,CAACsB,UAAR,GAAmB,UAASpC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOjB,CAAd;AAAgB,CAA/C;;AAAgD+B,OAAO,CAACuB,YAAR,GAAqB,UAASrC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOlB,CAAd;AAAgB,CAAjD;;AAAkDgC,OAAO,CAACwB,UAAR,GAAmB,UAAStC,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOb,CAAd;AAAgB,CAA/C;;AAAgD2B,OAAO,CAACyB,kBAAR,GAA2B,UAASvC,CAAT,EAAW;EAAC,OAAM,aAAW,OAAOA,CAAlB,IAAqB,eAAa,OAAOA,CAAzC,IAA4CA,CAAC,KAAGnB,CAAhD,IAAmDmB,CAAC,KAAGjB,CAAvD,IAA0DiB,CAAC,KAAGN,CAA9D,IAAiEM,CAAC,KAAGlB,CAArE,IAAwEkB,CAAC,KAAGb,CAA5E,IAA+Ea,CAAC,KAAGZ,CAAnF,IAAsFY,CAAC,KAAGL,CAA1F,IAA6F,aAAW,OAAOK,CAAlB,IAAqB,SAAOA,CAA5B,KAAgCA,CAAC,CAACE,QAAF,KAAaZ,CAAb,IAAgBU,CAAC,CAACE,QAAF,KAAab,CAA7B,IAAgCW,CAAC,CAACE,QAAF,KAAalB,CAA7C,IAAgDgB,CAAC,CAACE,QAAF,KAAajB,CAA7D,IAAgEe,CAAC,CAACE,QAAF,KAAahB,CAA7E,IAAgFc,CAAC,CAACE,QAAF,KAAaT,CAA7F,IAAgGO,CAAC,CAACE,QAAF,KAAaX,CAA7G,IAAgHS,CAAC,CAAC,CAAD,CAAD,KAAOR,CAAvJ,CAA7F,GAAuP,CAAC,CAAxP,GAA0P,CAAC,CAAjQ;AAAmQ,CAA1S;;AAChMsB,OAAO,CAAC0B,MAAR,GAAezC,CAAf"}, "metadata": {}, "sourceType": "script"}