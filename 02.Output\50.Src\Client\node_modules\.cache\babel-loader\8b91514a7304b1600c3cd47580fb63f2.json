{"ast": null, "code": "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useSessionStorageState = createUseStorageState(function () {\n  return isBrowser ? sessionStorage : undefined;\n});\nexport default useSessionStorageState;", "map": {"version": 3, "names": ["createUseStorageState", "<PERSON><PERSON><PERSON><PERSON>", "useSessionStorageState", "sessionStorage", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useSessionStorageState/index.js"], "sourcesContent": ["import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useSessionStorageState = createUseStorageState(function () {\n  return isBrowser ? sessionStorage : undefined;\n});\nexport default useSessionStorageState;"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,0BAAtC;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,IAAIC,sBAAsB,GAAGF,qBAAqB,CAAC,YAAY;EAC7D,OAAOC,SAAS,GAAGE,cAAH,GAAoBC,SAApC;AACD,CAFiD,CAAlD;AAGA,eAAeF,sBAAf"}, "metadata": {}, "sourceType": "module"}