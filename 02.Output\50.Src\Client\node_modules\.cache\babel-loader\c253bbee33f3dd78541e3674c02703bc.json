{"ast": null, "code": "var Symbol = require('./_Symbol');\n/** Used for built-in method references. */\n\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\n\nvar nativeObjectToString = objectProto.toString;\n/** Built-in value references. */\n\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\n\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n\n  return result;\n}\n\nmodule.exports = getRawTag;", "map": {"version": 3, "names": ["Symbol", "require", "objectProto", "Object", "prototype", "hasOwnProperty", "nativeObjectToString", "toString", "symToStringTag", "toStringTag", "undefined", "getRawTag", "value", "isOwn", "call", "tag", "unmasked", "e", "result", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getRawTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAD,CAApB;AAEA;;;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAIC,oBAAoB,GAAGJ,WAAW,CAACK,QAAvC;AAEA;;AACA,IAAIC,cAAc,GAAGR,MAAM,GAAGA,MAAM,CAACS,WAAV,GAAwBC,SAAnD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;EACxB,IAAIC,KAAK,GAAGR,cAAc,CAACS,IAAf,CAAoBF,KAApB,EAA2BJ,cAA3B,CAAZ;EAAA,IACIO,GAAG,GAAGH,KAAK,CAACJ,cAAD,CADf;;EAGA,IAAI;IACFI,KAAK,CAACJ,cAAD,CAAL,GAAwBE,SAAxB;IACA,IAAIM,QAAQ,GAAG,IAAf;EACD,CAHD,CAGE,OAAOC,CAAP,EAAU,CAAE;;EAEd,IAAIC,MAAM,GAAGZ,oBAAoB,CAACQ,IAArB,CAA0BF,KAA1B,CAAb;;EACA,IAAII,QAAJ,EAAc;IACZ,IAAIH,KAAJ,EAAW;MACTD,KAAK,CAACJ,cAAD,CAAL,GAAwBO,GAAxB;IACD,CAFD,MAEO;MACL,OAAOH,KAAK,CAACJ,cAAD,CAAZ;IACD;EACF;;EACD,OAAOU,MAAP;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBT,SAAjB"}, "metadata": {}, "sourceType": "script"}