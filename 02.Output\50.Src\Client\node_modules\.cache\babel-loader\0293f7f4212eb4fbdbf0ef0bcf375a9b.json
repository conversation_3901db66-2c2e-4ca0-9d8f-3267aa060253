{"ast": null, "code": "'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch (e) {// do nothing\n  }\n\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : level === 'log' ? function () {} : logObject.log;\n});\nmodule.exports = logObject;", "map": {"version": 3, "names": ["logObject", "for<PERSON>ach", "level", "levelExists", "global", "console", "apply", "e", "arguments", "log", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/log.js"], "sourcesContent": ["'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch(e) {\n    // do nothing\n  }\n\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : (level === 'log' ? function () {} : logObject.log);\n});\n\nmodule.exports = logObject;\n"], "mappings": "AAAA;;AAEA,IAAIA,SAAS,GAAG,EAAhB;AACA,CAAC,KAAD,EAAQ,OAAR,EAAiB,MAAjB,EAAyBC,OAAzB,CAAiC,UAAUC,KAAV,EAAiB;EAChD,IAAIC,WAAJ;;EAEA,IAAI;IACFA,WAAW,GAAGC,MAAM,CAACC,OAAP,IAAkBD,MAAM,CAACC,OAAP,CAAeH,KAAf,CAAlB,IAA2CE,MAAM,CAACC,OAAP,CAAeH,KAAf,EAAsBI,KAA/E;EACD,CAFD,CAEE,OAAMC,CAAN,EAAS,CACT;EACD;;EAEDP,SAAS,CAACE,KAAD,CAAT,GAAmBC,WAAW,GAAG,YAAY;IAC3C,OAAOC,MAAM,CAACC,OAAP,CAAeH,KAAf,EAAsBI,KAAtB,CAA4BF,MAAM,CAACC,OAAnC,EAA4CG,SAA5C,CAAP;EACD,CAF6B,GAEzBN,KAAK,KAAK,KAAV,GAAkB,YAAY,CAAE,CAAhC,GAAmCF,SAAS,CAACS,GAFlD;AAGD,CAZD;AAcAC,MAAM,CAACC,OAAP,GAAiBX,SAAjB"}, "metadata": {}, "sourceType": "script"}