{"ast": null, "code": "/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\nmodule.exports = stackHas;", "map": {"version": 3, "names": ["stackHas", "key", "__data__", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_stackHas.js"], "sourcesContent": ["/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\nmodule.exports = stackHas;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAT,CAAkBC,GAAlB,EAAuB;EACrB,OAAO,KAAKC,QAAL,CAAcC,GAAd,CAAkBF,GAAlB,CAAP;AACD;;AAEDG,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}