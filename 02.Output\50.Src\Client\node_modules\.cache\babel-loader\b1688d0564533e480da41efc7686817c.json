{"ast": null, "code": "/*!\n * JavaScript Cookie v2.2.1\n * https://github.com/js-cookie/js-cookie\n *\n * Copyright 2006, 2015 <PERSON> Brack\n * Released under the MIT license\n */\n;\n\n(function (factory) {\n  var registeredInModuleLoader;\n\n  if (typeof define === 'function' && define.amd) {\n    define(factory);\n    registeredInModuleLoader = true;\n  }\n\n  if (typeof exports === 'object') {\n    module.exports = factory();\n    registeredInModuleLoader = true;\n  }\n\n  if (!registeredInModuleLoader) {\n    var OldCookies = window.Cookies;\n    var api = window.Cookies = factory();\n\n    api.noConflict = function () {\n      window.Cookies = OldCookies;\n      return api;\n    };\n  }\n})(function () {\n  function extend() {\n    var i = 0;\n    var result = {};\n\n    for (; i < arguments.length; i++) {\n      var attributes = arguments[i];\n\n      for (var key in attributes) {\n        result[key] = attributes[key];\n      }\n    }\n\n    return result;\n  }\n\n  function decode(s) {\n    return s.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n  }\n\n  function init(converter) {\n    function api() {}\n\n    function set(key, value, attributes) {\n      if (typeof document === 'undefined') {\n        return;\n      }\n\n      attributes = extend({\n        path: '/'\n      }, api.defaults, attributes);\n\n      if (typeof attributes.expires === 'number') {\n        attributes.expires = new Date(new Date() * 1 + attributes.expires * 864e+5);\n      } // We're using \"expires\" because \"max-age\" is not supported by IE\n\n\n      attributes.expires = attributes.expires ? attributes.expires.toUTCString() : '';\n\n      try {\n        var result = JSON.stringify(value);\n\n        if (/^[\\{\\[]/.test(result)) {\n          value = result;\n        }\n      } catch (e) {}\n\n      value = converter.write ? converter.write(value, key) : encodeURIComponent(String(value)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent);\n      key = encodeURIComponent(String(key)).replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent).replace(/[\\(\\)]/g, escape);\n      var stringifiedAttributes = '';\n\n      for (var attributeName in attributes) {\n        if (!attributes[attributeName]) {\n          continue;\n        }\n\n        stringifiedAttributes += '; ' + attributeName;\n\n        if (attributes[attributeName] === true) {\n          continue;\n        } // Considers RFC 6265 section 5.2:\n        // ...\n        // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n        //     character:\n        // Consume the characters of the unparsed-attributes up to,\n        // not including, the first %x3B (\";\") character.\n        // ...\n\n\n        stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n      }\n\n      return document.cookie = key + '=' + value + stringifiedAttributes;\n    }\n\n    function get(key, json) {\n      if (typeof document === 'undefined') {\n        return;\n      }\n\n      var jar = {}; // To prevent the for loop in the first place assign an empty array\n      // in case there are no cookies at all.\n\n      var cookies = document.cookie ? document.cookie.split('; ') : [];\n      var i = 0;\n\n      for (; i < cookies.length; i++) {\n        var parts = cookies[i].split('=');\n        var cookie = parts.slice(1).join('=');\n\n        if (!json && cookie.charAt(0) === '\"') {\n          cookie = cookie.slice(1, -1);\n        }\n\n        try {\n          var name = decode(parts[0]);\n          cookie = (converter.read || converter)(cookie, name) || decode(cookie);\n\n          if (json) {\n            try {\n              cookie = JSON.parse(cookie);\n            } catch (e) {}\n          }\n\n          jar[name] = cookie;\n\n          if (key === name) {\n            break;\n          }\n        } catch (e) {}\n      }\n\n      return key ? jar[key] : jar;\n    }\n\n    api.set = set;\n\n    api.get = function (key) {\n      return get(key, false\n      /* read as raw */\n      );\n    };\n\n    api.getJSON = function (key) {\n      return get(key, true\n      /* read as json */\n      );\n    };\n\n    api.remove = function (key, attributes) {\n      set(key, '', extend(attributes, {\n        expires: -1\n      }));\n    };\n\n    api.defaults = {};\n    api.withConverter = init;\n    return api;\n  }\n\n  return init(function () {});\n});", "map": {"version": 3, "names": ["factory", "registeredInModuleLoader", "define", "amd", "exports", "module", "OldCookies", "window", "Cookies", "api", "noConflict", "extend", "i", "result", "arguments", "length", "attributes", "key", "decode", "s", "replace", "decodeURIComponent", "init", "converter", "set", "value", "document", "path", "defaults", "expires", "Date", "toUTCString", "JSON", "stringify", "test", "e", "write", "encodeURIComponent", "String", "escape", "stringifiedAttributes", "attributeName", "split", "cookie", "get", "json", "jar", "cookies", "parts", "slice", "join", "char<PERSON>t", "name", "read", "parse", "getJSON", "remove", "withConverter"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/js-cookie/src/js.cookie.js"], "sourcesContent": ["/*!\n * JavaScript Cookie v2.2.1\n * https://github.com/js-cookie/js-cookie\n *\n * Copyright 2006, 2015 <PERSON> & <PERSON> Brack\n * Released under the MIT license\n */\n;(function (factory) {\n\tvar registeredInModuleLoader;\n\tif (typeof define === 'function' && define.amd) {\n\t\tdefine(factory);\n\t\tregisteredInModuleLoader = true;\n\t}\n\tif (typeof exports === 'object') {\n\t\tmodule.exports = factory();\n\t\tregisteredInModuleLoader = true;\n\t}\n\tif (!registeredInModuleLoader) {\n\t\tvar OldCookies = window.Cookies;\n\t\tvar api = window.Cookies = factory();\n\t\tapi.noConflict = function () {\n\t\t\twindow.Cookies = OldCookies;\n\t\t\treturn api;\n\t\t};\n\t}\n}(function () {\n\tfunction extend () {\n\t\tvar i = 0;\n\t\tvar result = {};\n\t\tfor (; i < arguments.length; i++) {\n\t\t\tvar attributes = arguments[ i ];\n\t\t\tfor (var key in attributes) {\n\t\t\t\tresult[key] = attributes[key];\n\t\t\t}\n\t\t}\n\t\treturn result;\n\t}\n\n\tfunction decode (s) {\n\t\treturn s.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n\t}\n\n\tfunction init (converter) {\n\t\tfunction api() {}\n\n\t\tfunction set (key, value, attributes) {\n\t\t\tif (typeof document === 'undefined') {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tattributes = extend({\n\t\t\t\tpath: '/'\n\t\t\t}, api.defaults, attributes);\n\n\t\t\tif (typeof attributes.expires === 'number') {\n\t\t\t\tattributes.expires = new Date(new Date() * 1 + attributes.expires * 864e+5);\n\t\t\t}\n\n\t\t\t// We're using \"expires\" because \"max-age\" is not supported by IE\n\t\t\tattributes.expires = attributes.expires ? attributes.expires.toUTCString() : '';\n\n\t\t\ttry {\n\t\t\t\tvar result = JSON.stringify(value);\n\t\t\t\tif (/^[\\{\\[]/.test(result)) {\n\t\t\t\t\tvalue = result;\n\t\t\t\t}\n\t\t\t} catch (e) {}\n\n\t\t\tvalue = converter.write ?\n\t\t\t\tconverter.write(value, key) :\n\t\t\t\tencodeURIComponent(String(value))\n\t\t\t\t\t.replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent);\n\n\t\t\tkey = encodeURIComponent(String(key))\n\t\t\t\t.replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent)\n\t\t\t\t.replace(/[\\(\\)]/g, escape);\n\n\t\t\tvar stringifiedAttributes = '';\n\t\t\tfor (var attributeName in attributes) {\n\t\t\t\tif (!attributes[attributeName]) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tstringifiedAttributes += '; ' + attributeName;\n\t\t\t\tif (attributes[attributeName] === true) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Considers RFC 6265 section 5.2:\n\t\t\t\t// ...\n\t\t\t\t// 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n\t\t\t\t//     character:\n\t\t\t\t// Consume the characters of the unparsed-attributes up to,\n\t\t\t\t// not including, the first %x3B (\";\") character.\n\t\t\t\t// ...\n\t\t\t\tstringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n\t\t\t}\n\n\t\t\treturn (document.cookie = key + '=' + value + stringifiedAttributes);\n\t\t}\n\n\t\tfunction get (key, json) {\n\t\t\tif (typeof document === 'undefined') {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar jar = {};\n\t\t\t// To prevent the for loop in the first place assign an empty array\n\t\t\t// in case there are no cookies at all.\n\t\t\tvar cookies = document.cookie ? document.cookie.split('; ') : [];\n\t\t\tvar i = 0;\n\n\t\t\tfor (; i < cookies.length; i++) {\n\t\t\t\tvar parts = cookies[i].split('=');\n\t\t\t\tvar cookie = parts.slice(1).join('=');\n\n\t\t\t\tif (!json && cookie.charAt(0) === '\"') {\n\t\t\t\t\tcookie = cookie.slice(1, -1);\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tvar name = decode(parts[0]);\n\t\t\t\t\tcookie = (converter.read || converter)(cookie, name) ||\n\t\t\t\t\t\tdecode(cookie);\n\n\t\t\t\t\tif (json) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tcookie = JSON.parse(cookie);\n\t\t\t\t\t\t} catch (e) {}\n\t\t\t\t\t}\n\n\t\t\t\t\tjar[name] = cookie;\n\n\t\t\t\t\tif (key === name) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {}\n\t\t\t}\n\n\t\t\treturn key ? jar[key] : jar;\n\t\t}\n\n\t\tapi.set = set;\n\t\tapi.get = function (key) {\n\t\t\treturn get(key, false /* read as raw */);\n\t\t};\n\t\tapi.getJSON = function (key) {\n\t\t\treturn get(key, true /* read as json */);\n\t\t};\n\t\tapi.remove = function (key, attributes) {\n\t\t\tset(key, '', extend(attributes, {\n\t\t\t\texpires: -1\n\t\t\t}));\n\t\t};\n\n\t\tapi.defaults = {};\n\n\t\tapi.withConverter = init;\n\n\t\treturn api;\n\t}\n\n\treturn init(function () {});\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAAE,WAAUA,OAAV,EAAmB;EACpB,IAAIC,wBAAJ;;EACA,IAAI,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAA3C,EAAgD;IAC/CD,MAAM,CAACF,OAAD,CAAN;IACAC,wBAAwB,GAAG,IAA3B;EACA;;EACD,IAAI,OAAOG,OAAP,KAAmB,QAAvB,EAAiC;IAChCC,MAAM,CAACD,OAAP,GAAiBJ,OAAO,EAAxB;IACAC,wBAAwB,GAAG,IAA3B;EACA;;EACD,IAAI,CAACA,wBAAL,EAA+B;IAC9B,IAAIK,UAAU,GAAGC,MAAM,CAACC,OAAxB;IACA,IAAIC,GAAG,GAAGF,MAAM,CAACC,OAAP,GAAiBR,OAAO,EAAlC;;IACAS,GAAG,CAACC,UAAJ,GAAiB,YAAY;MAC5BH,MAAM,CAACC,OAAP,GAAiBF,UAAjB;MACA,OAAOG,GAAP;IACA,CAHD;EAIA;AACD,CAlBC,EAkBA,YAAY;EACb,SAASE,MAAT,GAAmB;IAClB,IAAIC,CAAC,GAAG,CAAR;IACA,IAAIC,MAAM,GAAG,EAAb;;IACA,OAAOD,CAAC,GAAGE,SAAS,CAACC,MAArB,EAA6BH,CAAC,EAA9B,EAAkC;MACjC,IAAII,UAAU,GAAGF,SAAS,CAAEF,CAAF,CAA1B;;MACA,KAAK,IAAIK,GAAT,IAAgBD,UAAhB,EAA4B;QAC3BH,MAAM,CAACI,GAAD,CAAN,GAAcD,UAAU,CAACC,GAAD,CAAxB;MACA;IACD;;IACD,OAAOJ,MAAP;EACA;;EAED,SAASK,MAAT,CAAiBC,CAAjB,EAAoB;IACnB,OAAOA,CAAC,CAACC,OAAF,CAAU,kBAAV,EAA8BC,kBAA9B,CAAP;EACA;;EAED,SAASC,IAAT,CAAeC,SAAf,EAA0B;IACzB,SAASd,GAAT,GAAe,CAAE;;IAEjB,SAASe,GAAT,CAAcP,GAAd,EAAmBQ,KAAnB,EAA0BT,UAA1B,EAAsC;MACrC,IAAI,OAAOU,QAAP,KAAoB,WAAxB,EAAqC;QACpC;MACA;;MAEDV,UAAU,GAAGL,MAAM,CAAC;QACnBgB,IAAI,EAAE;MADa,CAAD,EAEhBlB,GAAG,CAACmB,QAFY,EAEFZ,UAFE,CAAnB;;MAIA,IAAI,OAAOA,UAAU,CAACa,OAAlB,KAA8B,QAAlC,EAA4C;QAC3Cb,UAAU,CAACa,OAAX,GAAqB,IAAIC,IAAJ,CAAS,IAAIA,IAAJ,KAAa,CAAb,GAAiBd,UAAU,CAACa,OAAX,GAAqB,MAA/C,CAArB;MACA,CAXoC,CAarC;;;MACAb,UAAU,CAACa,OAAX,GAAqBb,UAAU,CAACa,OAAX,GAAqBb,UAAU,CAACa,OAAX,CAAmBE,WAAnB,EAArB,GAAwD,EAA7E;;MAEA,IAAI;QACH,IAAIlB,MAAM,GAAGmB,IAAI,CAACC,SAAL,CAAeR,KAAf,CAAb;;QACA,IAAI,UAAUS,IAAV,CAAerB,MAAf,CAAJ,EAA4B;UAC3BY,KAAK,GAAGZ,MAAR;QACA;MACD,CALD,CAKE,OAAOsB,CAAP,EAAU,CAAE;;MAEdV,KAAK,GAAGF,SAAS,CAACa,KAAV,GACPb,SAAS,CAACa,KAAV,CAAgBX,KAAhB,EAAuBR,GAAvB,CADO,GAEPoB,kBAAkB,CAACC,MAAM,CAACb,KAAD,CAAP,CAAlB,CACEL,OADF,CACU,2DADV,EACuEC,kBADvE,CAFD;MAKAJ,GAAG,GAAGoB,kBAAkB,CAACC,MAAM,CAACrB,GAAD,CAAP,CAAlB,CACJG,OADI,CACI,0BADJ,EACgCC,kBADhC,EAEJD,OAFI,CAEI,SAFJ,EAEemB,MAFf,CAAN;MAIA,IAAIC,qBAAqB,GAAG,EAA5B;;MACA,KAAK,IAAIC,aAAT,IAA0BzB,UAA1B,EAAsC;QACrC,IAAI,CAACA,UAAU,CAACyB,aAAD,CAAf,EAAgC;UAC/B;QACA;;QACDD,qBAAqB,IAAI,OAAOC,aAAhC;;QACA,IAAIzB,UAAU,CAACyB,aAAD,CAAV,KAA8B,IAAlC,EAAwC;UACvC;QACA,CAPoC,CASrC;QACA;QACA;QACA;QACA;QACA;QACA;;;QACAD,qBAAqB,IAAI,MAAMxB,UAAU,CAACyB,aAAD,CAAV,CAA0BC,KAA1B,CAAgC,GAAhC,EAAqC,CAArC,CAA/B;MACA;;MAED,OAAQhB,QAAQ,CAACiB,MAAT,GAAkB1B,GAAG,GAAG,GAAN,GAAYQ,KAAZ,GAAoBe,qBAA9C;IACA;;IAED,SAASI,GAAT,CAAc3B,GAAd,EAAmB4B,IAAnB,EAAyB;MACxB,IAAI,OAAOnB,QAAP,KAAoB,WAAxB,EAAqC;QACpC;MACA;;MAED,IAAIoB,GAAG,GAAG,EAAV,CALwB,CAMxB;MACA;;MACA,IAAIC,OAAO,GAAGrB,QAAQ,CAACiB,MAAT,GAAkBjB,QAAQ,CAACiB,MAAT,CAAgBD,KAAhB,CAAsB,IAAtB,CAAlB,GAAgD,EAA9D;MACA,IAAI9B,CAAC,GAAG,CAAR;;MAEA,OAAOA,CAAC,GAAGmC,OAAO,CAAChC,MAAnB,EAA2BH,CAAC,EAA5B,EAAgC;QAC/B,IAAIoC,KAAK,GAAGD,OAAO,CAACnC,CAAD,CAAP,CAAW8B,KAAX,CAAiB,GAAjB,CAAZ;QACA,IAAIC,MAAM,GAAGK,KAAK,CAACC,KAAN,CAAY,CAAZ,EAAeC,IAAf,CAAoB,GAApB,CAAb;;QAEA,IAAI,CAACL,IAAD,IAASF,MAAM,CAACQ,MAAP,CAAc,CAAd,MAAqB,GAAlC,EAAuC;UACtCR,MAAM,GAAGA,MAAM,CAACM,KAAP,CAAa,CAAb,EAAgB,CAAC,CAAjB,CAAT;QACA;;QAED,IAAI;UACH,IAAIG,IAAI,GAAGlC,MAAM,CAAC8B,KAAK,CAAC,CAAD,CAAN,CAAjB;UACAL,MAAM,GAAG,CAACpB,SAAS,CAAC8B,IAAV,IAAkB9B,SAAnB,EAA8BoB,MAA9B,EAAsCS,IAAtC,KACRlC,MAAM,CAACyB,MAAD,CADP;;UAGA,IAAIE,IAAJ,EAAU;YACT,IAAI;cACHF,MAAM,GAAGX,IAAI,CAACsB,KAAL,CAAWX,MAAX,CAAT;YACA,CAFD,CAEE,OAAOR,CAAP,EAAU,CAAE;UACd;;UAEDW,GAAG,CAACM,IAAD,CAAH,GAAYT,MAAZ;;UAEA,IAAI1B,GAAG,KAAKmC,IAAZ,EAAkB;YACjB;UACA;QACD,CAhBD,CAgBE,OAAOjB,CAAP,EAAU,CAAE;MACd;;MAED,OAAOlB,GAAG,GAAG6B,GAAG,CAAC7B,GAAD,CAAN,GAAc6B,GAAxB;IACA;;IAEDrC,GAAG,CAACe,GAAJ,GAAUA,GAAV;;IACAf,GAAG,CAACmC,GAAJ,GAAU,UAAU3B,GAAV,EAAe;MACxB,OAAO2B,GAAG,CAAC3B,GAAD,EAAM;MAAM;MAAZ,CAAV;IACA,CAFD;;IAGAR,GAAG,CAAC8C,OAAJ,GAAc,UAAUtC,GAAV,EAAe;MAC5B,OAAO2B,GAAG,CAAC3B,GAAD,EAAM;MAAK;MAAX,CAAV;IACA,CAFD;;IAGAR,GAAG,CAAC+C,MAAJ,GAAa,UAAUvC,GAAV,EAAeD,UAAf,EAA2B;MACvCQ,GAAG,CAACP,GAAD,EAAM,EAAN,EAAUN,MAAM,CAACK,UAAD,EAAa;QAC/Ba,OAAO,EAAE,CAAC;MADqB,CAAb,CAAhB,CAAH;IAGA,CAJD;;IAMApB,GAAG,CAACmB,QAAJ,GAAe,EAAf;IAEAnB,GAAG,CAACgD,aAAJ,GAAoBnC,IAApB;IAEA,OAAOb,GAAP;EACA;;EAED,OAAOa,IAAI,CAAC,YAAY,CAAE,CAAf,CAAX;AACA,CA3JC,CAAD"}, "metadata": {}, "sourceType": "script"}