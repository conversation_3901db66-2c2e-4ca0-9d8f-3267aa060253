{"ast": null, "code": "var getNative = require('./_getNative');\n/* Built-in method references that are verified to be native. */\n\n\nvar nativeCreate = getNative(Object, 'create');\nmodule.exports = nativeCreate;", "map": {"version": 3, "names": ["getNative", "require", "nativeCreate", "Object", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_nativeCreate.js"], "sourcesContent": ["var getNative = require('./_getNative');\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\n\nmodule.exports = nativeCreate;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAEA;;;AACA,IAAIC,YAAY,GAAGF,SAAS,CAACG,MAAD,EAAS,QAAT,CAA5B;AAEAC,MAAM,CAACC,OAAP,GAAiBH,YAAjB"}, "metadata": {}, "sourceType": "script"}