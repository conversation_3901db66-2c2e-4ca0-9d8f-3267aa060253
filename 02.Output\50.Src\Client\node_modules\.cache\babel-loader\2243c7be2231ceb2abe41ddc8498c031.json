{"ast": null, "code": "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\n\n/* eslint-disable require-jsdoc, valid-jsdoc */\nvar MapShim = function () {\n  if (typeof Map !== 'undefined') {\n    return Map;\n  }\n  /**\r\n   * Returns index in provided array that matches the specified key.\r\n   *\r\n   * @param {Array<Array>} arr\r\n   * @param {*} key\r\n   * @returns {number}\r\n   */\n\n\n  function getIndex(arr, key) {\n    var result = -1;\n    arr.some(function (entry, index) {\n      if (entry[0] === key) {\n        result = index;\n        return true;\n      }\n\n      return false;\n    });\n    return result;\n  }\n\n  return (\n    /** @class */\n    function () {\n      function class_1() {\n        this.__entries__ = [];\n      }\n\n      Object.defineProperty(class_1.prototype, \"size\", {\n        /**\r\n         * @returns {boolean}\r\n         */\n        get: function get() {\n          return this.__entries__.length;\n        },\n        enumerable: true,\n        configurable: true\n      });\n      /**\r\n       * @param {*} key\r\n       * @returns {*}\r\n       */\n\n      class_1.prototype.get = function (key) {\n        var index = getIndex(this.__entries__, key);\n        var entry = this.__entries__[index];\n        return entry && entry[1];\n      };\n      /**\r\n       * @param {*} key\r\n       * @param {*} value\r\n       * @returns {void}\r\n       */\n\n\n      class_1.prototype.set = function (key, value) {\n        var index = getIndex(this.__entries__, key);\n\n        if (~index) {\n          this.__entries__[index][1] = value;\n        } else {\n          this.__entries__.push([key, value]);\n        }\n      };\n      /**\r\n       * @param {*} key\r\n       * @returns {void}\r\n       */\n\n\n      class_1.prototype.delete = function (key) {\n        var entries = this.__entries__;\n        var index = getIndex(entries, key);\n\n        if (~index) {\n          entries.splice(index, 1);\n        }\n      };\n      /**\r\n       * @param {*} key\r\n       * @returns {void}\r\n       */\n\n\n      class_1.prototype.has = function (key) {\n        return !!~getIndex(this.__entries__, key);\n      };\n      /**\r\n       * @returns {void}\r\n       */\n\n\n      class_1.prototype.clear = function () {\n        this.__entries__.splice(0);\n      };\n      /**\r\n       * @param {Function} callback\r\n       * @param {*} [ctx=null]\r\n       * @returns {void}\r\n       */\n\n\n      class_1.prototype.forEach = function (callback, ctx) {\n        if (ctx === void 0) {\n          ctx = null;\n        }\n\n        for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\n          var entry = _a[_i];\n          callback.call(ctx, entry[1], entry[0]);\n        }\n      };\n\n      return class_1;\n    }()\n  );\n}();\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\n\n\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document; // Returns global object of a current environment.\n\nvar global$1 = function () {\n  if (typeof global !== 'undefined' && global.Math === Math) {\n    return global;\n  }\n\n  if (typeof self !== 'undefined' && self.Math === Math) {\n    return self;\n  }\n\n  if (typeof window !== 'undefined' && window.Math === Math) {\n    return window;\n  } // eslint-disable-next-line no-new-func\n\n\n  return Function('return this')();\n}();\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\n\n\nvar requestAnimationFrame$1 = function () {\n  if (typeof requestAnimationFrame === 'function') {\n    // It's required to use a bounded function because IE sometimes throws\n    // an \"Invalid calling object\" error if rAF is invoked without the global\n    // object on the left hand side.\n    return requestAnimationFrame.bind(global$1);\n  }\n\n  return function (callback) {\n    return setTimeout(function () {\n      return callback(Date.now());\n    }, 1000 / 60);\n  };\n}(); // Defines minimum timeout before adding a trailing call.\n\n\nvar trailingTimeout = 2;\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\n\nfunction throttle(callback, delay) {\n  var leadingCall = false,\n      trailingCall = false,\n      lastCallTime = 0;\n  /**\r\n   * Invokes the original callback function and schedules new invocation if\r\n   * the \"proxy\" was called during current request.\r\n   *\r\n   * @returns {void}\r\n   */\n\n  function resolvePending() {\n    if (leadingCall) {\n      leadingCall = false;\n      callback();\n    }\n\n    if (trailingCall) {\n      proxy();\n    }\n  }\n  /**\r\n   * Callback invoked after the specified delay. It will further postpone\r\n   * invocation of the original function delegating it to the\r\n   * requestAnimationFrame.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  function timeoutCallback() {\n    requestAnimationFrame$1(resolvePending);\n  }\n  /**\r\n   * Schedules invocation of the original function.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  function proxy() {\n    var timeStamp = Date.now();\n\n    if (leadingCall) {\n      // Reject immediately following calls.\n      if (timeStamp - lastCallTime < trailingTimeout) {\n        return;\n      } // Schedule new call to be in invoked when the pending one is resolved.\n      // This is important for \"transitions\" which never actually start\n      // immediately so there is a chance that we might miss one if change\n      // happens amids the pending invocation.\n\n\n      trailingCall = true;\n    } else {\n      leadingCall = true;\n      trailingCall = false;\n      setTimeout(timeoutCallback, delay);\n    }\n\n    lastCallTime = timeStamp;\n  }\n\n  return proxy;\n} // Minimum delay before invoking the update of observers.\n\n\nvar REFRESH_DELAY = 20; // A list of substrings of CSS properties used to find transition events that\n// might affect dimensions of observed elements.\n\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight']; // Check if MutationObserver is available.\n\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\n\nvar ResizeObserverController =\n/** @class */\nfunction () {\n  /**\r\n   * Creates a new instance of ResizeObserverController.\r\n   *\r\n   * @private\r\n   */\n  function ResizeObserverController() {\n    /**\r\n     * Indicates whether DOM listeners have been added.\r\n     *\r\n     * @private {boolean}\r\n     */\n    this.connected_ = false;\n    /**\r\n     * Tells that controller has subscribed for Mutation Events.\r\n     *\r\n     * @private {boolean}\r\n     */\n\n    this.mutationEventsAdded_ = false;\n    /**\r\n     * Keeps reference to the instance of MutationObserver.\r\n     *\r\n     * @private {MutationObserver}\r\n     */\n\n    this.mutationsObserver_ = null;\n    /**\r\n     * A list of connected observers.\r\n     *\r\n     * @private {Array<ResizeObserverSPI>}\r\n     */\n\n    this.observers_ = [];\n    this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\n    this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\n  }\n  /**\r\n   * Adds observer to observers list.\r\n   *\r\n   * @param {ResizeObserverSPI} observer - Observer to be added.\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.addObserver = function (observer) {\n    if (!~this.observers_.indexOf(observer)) {\n      this.observers_.push(observer);\n    } // Add listeners if they haven't been added yet.\n\n\n    if (!this.connected_) {\n      this.connect_();\n    }\n  };\n  /**\r\n   * Removes observer from observers list.\r\n   *\r\n   * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.removeObserver = function (observer) {\n    var observers = this.observers_;\n    var index = observers.indexOf(observer); // Remove observer if it's present in registry.\n\n    if (~index) {\n      observers.splice(index, 1);\n    } // Remove listeners if controller has no connected observers.\n\n\n    if (!observers.length && this.connected_) {\n      this.disconnect_();\n    }\n  };\n  /**\r\n   * Invokes the update of observers. It will continue running updates insofar\r\n   * it detects changes.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.refresh = function () {\n    var changesDetected = this.updateObservers_(); // Continue running updates if changes have been detected as there might\n    // be future ones caused by CSS transitions.\n\n    if (changesDetected) {\n      this.refresh();\n    }\n  };\n  /**\r\n   * Updates every observer from observers list and notifies them of queued\r\n   * entries.\r\n   *\r\n   * @private\r\n   * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n   *      dimensions of it's elements.\r\n   */\n\n\n  ResizeObserverController.prototype.updateObservers_ = function () {\n    // Collect observers that have active observations.\n    var activeObservers = this.observers_.filter(function (observer) {\n      return observer.gatherActive(), observer.hasActive();\n    }); // Deliver notifications in a separate cycle in order to avoid any\n    // collisions between observers, e.g. when multiple instances of\n    // ResizeObserver are tracking the same element and the callback of one\n    // of them changes content dimensions of the observed target. Sometimes\n    // this may result in notifications being blocked for the rest of observers.\n\n    activeObservers.forEach(function (observer) {\n      return observer.broadcastActive();\n    });\n    return activeObservers.length > 0;\n  };\n  /**\r\n   * Initializes DOM listeners.\r\n   *\r\n   * @private\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.connect_ = function () {\n    // Do nothing if running in a non-browser environment or if listeners\n    // have been already added.\n    if (!isBrowser || this.connected_) {\n      return;\n    } // Subscription to the \"Transitionend\" event is used as a workaround for\n    // delayed transitions. This way it's possible to capture at least the\n    // final state of an element.\n\n\n    document.addEventListener('transitionend', this.onTransitionEnd_);\n    window.addEventListener('resize', this.refresh);\n\n    if (mutationObserverSupported) {\n      this.mutationsObserver_ = new MutationObserver(this.refresh);\n      this.mutationsObserver_.observe(document, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    } else {\n      document.addEventListener('DOMSubtreeModified', this.refresh);\n      this.mutationEventsAdded_ = true;\n    }\n\n    this.connected_ = true;\n  };\n  /**\r\n   * Removes DOM listeners.\r\n   *\r\n   * @private\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.disconnect_ = function () {\n    // Do nothing if running in a non-browser environment or if listeners\n    // have been already removed.\n    if (!isBrowser || !this.connected_) {\n      return;\n    }\n\n    document.removeEventListener('transitionend', this.onTransitionEnd_);\n    window.removeEventListener('resize', this.refresh);\n\n    if (this.mutationsObserver_) {\n      this.mutationsObserver_.disconnect();\n    }\n\n    if (this.mutationEventsAdded_) {\n      document.removeEventListener('DOMSubtreeModified', this.refresh);\n    }\n\n    this.mutationsObserver_ = null;\n    this.mutationEventsAdded_ = false;\n    this.connected_ = false;\n  };\n  /**\r\n   * \"Transitionend\" event handler.\r\n   *\r\n   * @private\r\n   * @param {TransitionEvent} event\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\n    var _b = _a.propertyName,\n        propertyName = _b === void 0 ? '' : _b; // Detect whether transition may affect dimensions of an element.\n\n    var isReflowProperty = transitionKeys.some(function (key) {\n      return !!~propertyName.indexOf(key);\n    });\n\n    if (isReflowProperty) {\n      this.refresh();\n    }\n  };\n  /**\r\n   * Returns instance of the ResizeObserverController.\r\n   *\r\n   * @returns {ResizeObserverController}\r\n   */\n\n\n  ResizeObserverController.getInstance = function () {\n    if (!this.instance_) {\n      this.instance_ = new ResizeObserverController();\n    }\n\n    return this.instance_;\n  };\n  /**\r\n   * Holds reference to the controller's instance.\r\n   *\r\n   * @private {ResizeObserverController}\r\n   */\n\n\n  ResizeObserverController.instance_ = null;\n  return ResizeObserverController;\n}();\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\n\n\nvar defineConfigurable = function defineConfigurable(target, props) {\n  for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\n    var key = _a[_i];\n    Object.defineProperty(target, key, {\n      value: props[key],\n      enumerable: false,\n      writable: false,\n      configurable: true\n    });\n  }\n\n  return target;\n};\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\n\n\nvar getWindowOf = function getWindowOf(target) {\n  // Assume that the element is an instance of Node, which means that it\n  // has the \"ownerDocument\" property from which we can retrieve a\n  // corresponding global object.\n  var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView; // Return the local global object if it's not possible extract one from\n  // provided element.\n\n  return ownerGlobal || global$1;\n}; // Placeholder of an empty content rectangle.\n\n\nvar emptyRect = createRectInit(0, 0, 0, 0);\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\n\nfunction toFloat(value) {\n  return parseFloat(value) || 0;\n}\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\n\n\nfunction getBordersSize(styles) {\n  var positions = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    positions[_i - 1] = arguments[_i];\n  }\n\n  return positions.reduce(function (size, position) {\n    var value = styles['border-' + position + '-width'];\n    return size + toFloat(value);\n  }, 0);\n}\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\n\n\nfunction getPaddings(styles) {\n  var positions = ['top', 'right', 'bottom', 'left'];\n  var paddings = {};\n\n  for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\n    var position = positions_1[_i];\n    var value = styles['padding-' + position];\n    paddings[position] = toFloat(value);\n  }\n\n  return paddings;\n}\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\n\n\nfunction getSVGContentRect(target) {\n  var bbox = target.getBBox();\n  return createRectInit(0, 0, bbox.width, bbox.height);\n}\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\n\n\nfunction getHTMLElementContentRect(target) {\n  // Client width & height properties can't be\n  // used exclusively as they provide rounded values.\n  var clientWidth = target.clientWidth,\n      clientHeight = target.clientHeight; // By this condition we can catch all non-replaced inline, hidden and\n  // detached elements. Though elements with width & height properties less\n  // than 0.5 will be discarded as well.\n  //\n  // Without it we would need to implement separate methods for each of\n  // those cases and it's not possible to perform a precise and performance\n  // effective test for hidden elements. E.g. even jQuery's ':visible' filter\n  // gives wrong results for elements with width & height less than 0.5.\n\n  if (!clientWidth && !clientHeight) {\n    return emptyRect;\n  }\n\n  var styles = getWindowOf(target).getComputedStyle(target);\n  var paddings = getPaddings(styles);\n  var horizPad = paddings.left + paddings.right;\n  var vertPad = paddings.top + paddings.bottom; // Computed styles of width & height are being used because they are the\n  // only dimensions available to JS that contain non-rounded values. It could\n  // be possible to utilize the getBoundingClientRect if only it's data wasn't\n  // affected by CSS transformations let alone paddings, borders and scroll bars.\n\n  var width = toFloat(styles.width),\n      height = toFloat(styles.height); // Width & height include paddings and borders when the 'border-box' box\n  // model is applied (except for IE).\n\n  if (styles.boxSizing === 'border-box') {\n    // Following conditions are required to handle Internet Explorer which\n    // doesn't include paddings and borders to computed CSS dimensions.\n    //\n    // We can say that if CSS dimensions + paddings are equal to the \"client\"\n    // properties then it's either IE, and thus we don't need to subtract\n    // anything, or an element merely doesn't have paddings/borders styles.\n    if (Math.round(width + horizPad) !== clientWidth) {\n      width -= getBordersSize(styles, 'left', 'right') + horizPad;\n    }\n\n    if (Math.round(height + vertPad) !== clientHeight) {\n      height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\n    }\n  } // Following steps can't be applied to the document's root element as its\n  // client[Width/Height] properties represent viewport area of the window.\n  // Besides, it's as well not necessary as the <html> itself neither has\n  // rendered scroll bars nor it can be clipped.\n\n\n  if (!isDocumentElement(target)) {\n    // In some browsers (only in Firefox, actually) CSS width & height\n    // include scroll bars size which can be removed at this step as scroll\n    // bars are the only difference between rounded dimensions + paddings\n    // and \"client\" properties, though that is not always true in Chrome.\n    var vertScrollbar = Math.round(width + horizPad) - clientWidth;\n    var horizScrollbar = Math.round(height + vertPad) - clientHeight; // Chrome has a rather weird rounding of \"client\" properties.\n    // E.g. for an element with content width of 314.2px it sometimes gives\n    // the client width of 315px and for the width of 314.7px it may give\n    // 314px. And it doesn't happen all the time. So just ignore this delta\n    // as a non-relevant.\n\n    if (Math.abs(vertScrollbar) !== 1) {\n      width -= vertScrollbar;\n    }\n\n    if (Math.abs(horizScrollbar) !== 1) {\n      height -= horizScrollbar;\n    }\n  }\n\n  return createRectInit(paddings.left, paddings.top, width, height);\n}\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\n\n\nvar isSVGGraphicsElement = function () {\n  // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\n  // interface.\n  if (typeof SVGGraphicsElement !== 'undefined') {\n    return function (target) {\n      return target instanceof getWindowOf(target).SVGGraphicsElement;\n    };\n  } // If it's so, then check that element is at least an instance of the\n  // SVGElement and that it has the \"getBBox\" method.\n  // eslint-disable-next-line no-extra-parens\n\n\n  return function (target) {\n    return target instanceof getWindowOf(target).SVGElement && typeof target.getBBox === 'function';\n  };\n}();\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\n\n\nfunction isDocumentElement(target) {\n  return target === getWindowOf(target).document.documentElement;\n}\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\n\n\nfunction getContentRect(target) {\n  if (!isBrowser) {\n    return emptyRect;\n  }\n\n  if (isSVGGraphicsElement(target)) {\n    return getSVGContentRect(target);\n  }\n\n  return getHTMLElementContentRect(target);\n}\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\n\n\nfunction createReadOnlyRect(_a) {\n  var x = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height; // If DOMRectReadOnly is available use it as a prototype for the rectangle.\n\n  var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\n  var rect = Object.create(Constr.prototype); // Rectangle's properties are not writable and non-enumerable.\n\n  defineConfigurable(rect, {\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: height + y,\n    left: x\n  });\n  return rect;\n}\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\n\n\nfunction createRectInit(x, y, width, height) {\n  return {\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  };\n}\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\n\n\nvar ResizeObservation =\n/** @class */\nfunction () {\n  /**\r\n   * Creates an instance of ResizeObservation.\r\n   *\r\n   * @param {Element} target - Element to be observed.\r\n   */\n  function ResizeObservation(target) {\n    /**\r\n     * Broadcasted width of content rectangle.\r\n     *\r\n     * @type {number}\r\n     */\n    this.broadcastWidth = 0;\n    /**\r\n     * Broadcasted height of content rectangle.\r\n     *\r\n     * @type {number}\r\n     */\n\n    this.broadcastHeight = 0;\n    /**\r\n     * Reference to the last observed content rectangle.\r\n     *\r\n     * @private {DOMRectInit}\r\n     */\n\n    this.contentRect_ = createRectInit(0, 0, 0, 0);\n    this.target = target;\n  }\n  /**\r\n   * Updates content rectangle and tells whether it's width or height properties\r\n   * have changed since the last broadcast.\r\n   *\r\n   * @returns {boolean}\r\n   */\n\n\n  ResizeObservation.prototype.isActive = function () {\n    var rect = getContentRect(this.target);\n    this.contentRect_ = rect;\n    return rect.width !== this.broadcastWidth || rect.height !== this.broadcastHeight;\n  };\n  /**\r\n   * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n   * from the corresponding properties of the last observed content rectangle.\r\n   *\r\n   * @returns {DOMRectInit} Last observed content rectangle.\r\n   */\n\n\n  ResizeObservation.prototype.broadcastRect = function () {\n    var rect = this.contentRect_;\n    this.broadcastWidth = rect.width;\n    this.broadcastHeight = rect.height;\n    return rect;\n  };\n\n  return ResizeObservation;\n}();\n\nvar ResizeObserverEntry =\n/** @class */\nfunction () {\n  /**\r\n   * Creates an instance of ResizeObserverEntry.\r\n   *\r\n   * @param {Element} target - Element that is being observed.\r\n   * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n   */\n  function ResizeObserverEntry(target, rectInit) {\n    var contentRect = createReadOnlyRect(rectInit); // According to the specification following properties are not writable\n    // and are also not enumerable in the native implementation.\n    //\n    // Property accessors are not being used as they'd require to define a\n    // private WeakMap storage which may cause memory leaks in browsers that\n    // don't support this type of collections.\n\n    defineConfigurable(this, {\n      target: target,\n      contentRect: contentRect\n    });\n  }\n\n  return ResizeObserverEntry;\n}();\n\nvar ResizeObserverSPI =\n/** @class */\nfunction () {\n  /**\r\n   * Creates a new instance of ResizeObserver.\r\n   *\r\n   * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n   *      when one of the observed elements changes it's content dimensions.\r\n   * @param {ResizeObserverController} controller - Controller instance which\r\n   *      is responsible for the updates of observer.\r\n   * @param {ResizeObserver} callbackCtx - Reference to the public\r\n   *      ResizeObserver instance which will be passed to callback function.\r\n   */\n  function ResizeObserverSPI(callback, controller, callbackCtx) {\n    /**\r\n     * Collection of resize observations that have detected changes in dimensions\r\n     * of elements.\r\n     *\r\n     * @private {Array<ResizeObservation>}\r\n     */\n    this.activeObservations_ = [];\n    /**\r\n     * Registry of the ResizeObservation instances.\r\n     *\r\n     * @private {Map<Element, ResizeObservation>}\r\n     */\n\n    this.observations_ = new MapShim();\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('The callback provided as parameter 1 is not a function.');\n    }\n\n    this.callback_ = callback;\n    this.controller_ = controller;\n    this.callbackCtx_ = callbackCtx;\n  }\n  /**\r\n   * Starts observing provided element.\r\n   *\r\n   * @param {Element} target - Element to be observed.\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.observe = function (target) {\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    } // Do nothing if current environment doesn't have the Element interface.\n\n\n    if (typeof Element === 'undefined' || !(Element instanceof Object)) {\n      return;\n    }\n\n    if (!(target instanceof getWindowOf(target).Element)) {\n      throw new TypeError('parameter 1 is not of type \"Element\".');\n    }\n\n    var observations = this.observations_; // Do nothing if element is already being observed.\n\n    if (observations.has(target)) {\n      return;\n    }\n\n    observations.set(target, new ResizeObservation(target));\n    this.controller_.addObserver(this); // Force the update of observations.\n\n    this.controller_.refresh();\n  };\n  /**\r\n   * Stops observing provided element.\r\n   *\r\n   * @param {Element} target - Element to stop observing.\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.unobserve = function (target) {\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    } // Do nothing if current environment doesn't have the Element interface.\n\n\n    if (typeof Element === 'undefined' || !(Element instanceof Object)) {\n      return;\n    }\n\n    if (!(target instanceof getWindowOf(target).Element)) {\n      throw new TypeError('parameter 1 is not of type \"Element\".');\n    }\n\n    var observations = this.observations_; // Do nothing if element is not being observed.\n\n    if (!observations.has(target)) {\n      return;\n    }\n\n    observations.delete(target);\n\n    if (!observations.size) {\n      this.controller_.removeObserver(this);\n    }\n  };\n  /**\r\n   * Stops observing all elements.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.disconnect = function () {\n    this.clearActive();\n    this.observations_.clear();\n    this.controller_.removeObserver(this);\n  };\n  /**\r\n   * Collects observation instances the associated element of which has changed\r\n   * it's content rectangle.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.gatherActive = function () {\n    var _this = this;\n\n    this.clearActive();\n    this.observations_.forEach(function (observation) {\n      if (observation.isActive()) {\n        _this.activeObservations_.push(observation);\n      }\n    });\n  };\n  /**\r\n   * Invokes initial callback function with a list of ResizeObserverEntry\r\n   * instances collected from active resize observations.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.broadcastActive = function () {\n    // Do nothing if observer doesn't have active observations.\n    if (!this.hasActive()) {\n      return;\n    }\n\n    var ctx = this.callbackCtx_; // Create ResizeObserverEntry instance for every active observation.\n\n    var entries = this.activeObservations_.map(function (observation) {\n      return new ResizeObserverEntry(observation.target, observation.broadcastRect());\n    });\n    this.callback_.call(ctx, entries, ctx);\n    this.clearActive();\n  };\n  /**\r\n   * Clears the collection of active observations.\r\n   *\r\n   * @returns {void}\r\n   */\n\n\n  ResizeObserverSPI.prototype.clearActive = function () {\n    this.activeObservations_.splice(0);\n  };\n  /**\r\n   * Tells whether observer has active observations.\r\n   *\r\n   * @returns {boolean}\r\n   */\n\n\n  ResizeObserverSPI.prototype.hasActive = function () {\n    return this.activeObservations_.length > 0;\n  };\n\n  return ResizeObserverSPI;\n}(); // Registry of internal observers. If WeakMap is not available use current shim\n// for the Map collection as it has all required methods and because WeakMap\n// can't be fully polyfilled anyway.\n\n\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\n\nvar ResizeObserver =\n/** @class */\nfunction () {\n  /**\r\n   * Creates a new instance of ResizeObserver.\r\n   *\r\n   * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n   *      dimensions of the observed elements change.\r\n   */\n  function ResizeObserver(callback) {\n    if (!(this instanceof ResizeObserver)) {\n      throw new TypeError('Cannot call a class as a function.');\n    }\n\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    }\n\n    var controller = ResizeObserverController.getInstance();\n    var observer = new ResizeObserverSPI(callback, controller, this);\n    observers.set(this, observer);\n  }\n\n  return ResizeObserver;\n}(); // Expose public methods of ResizeObserver.\n\n\n['observe', 'unobserve', 'disconnect'].forEach(function (method) {\n  ResizeObserver.prototype[method] = function () {\n    var _a;\n\n    return (_a = observers.get(this))[method].apply(_a, arguments);\n  };\n});\n\nvar index = function () {\n  // Export existing implementation if available.\n  if (typeof global$1.ResizeObserver !== 'undefined') {\n    return global$1.ResizeObserver;\n  }\n\n  return ResizeObserver;\n}();\n\nexport default index;", "map": {"version": 3, "names": ["MapShim", "Map", "getIndex", "arr", "key", "result", "some", "entry", "index", "class_1", "__entries__", "Object", "defineProperty", "prototype", "get", "length", "enumerable", "configurable", "set", "value", "push", "delete", "entries", "splice", "has", "clear", "for<PERSON>ach", "callback", "ctx", "_i", "_a", "call", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "global$1", "global", "Math", "self", "Function", "requestAnimationFrame$1", "requestAnimationFrame", "bind", "setTimeout", "Date", "now", "trailingTimeout", "throttle", "delay", "leadingCall", "trailingCall", "lastCallTime", "resolvePending", "proxy", "timeout<PERSON><PERSON><PERSON>", "timeStamp", "REFRESH_DELAY", "<PERSON><PERSON><PERSON><PERSON>", "mutationObserverSupported", "MutationObserver", "ResizeObserverController", "connected_", "mutationEventsAdded_", "mutationsObserver_", "observers_", "onTransitionEnd_", "refresh", "addObserver", "observer", "indexOf", "connect_", "removeObserver", "observers", "disconnect_", "changesDetected", "updateObservers_", "activeObservers", "filter", "gatherActive", "hasActive", "broadcastActive", "addEventListener", "observe", "attributes", "childList", "characterData", "subtree", "removeEventListener", "disconnect", "_b", "propertyName", "isReflowProperty", "getInstance", "instance_", "defineConfigurable", "target", "props", "keys", "writable", "getWindowOf", "ownerGlobal", "ownerDocument", "defaultView", "emptyRect", "createRectInit", "toFloat", "parseFloat", "getBordersSize", "styles", "positions", "arguments", "reduce", "size", "position", "getPaddings", "paddings", "positions_1", "getSVGContentRect", "bbox", "getBBox", "width", "height", "getHTMLElementContentRect", "clientWidth", "clientHeight", "getComputedStyle", "horizPad", "left", "right", "vertPad", "top", "bottom", "boxSizing", "round", "isDocumentElement", "vertScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abs", "isSVGGraphicsElement", "SVGGraphicsElement", "SVGElement", "documentElement", "getContentRect", "createReadOnlyRect", "x", "y", "Constr", "DOMRectReadOnly", "rect", "create", "ResizeObservation", "broadcastWidth", "broadcastHeight", "contentRect_", "isActive", "broadcastRect", "ResizeObserverEntry", "rectInit", "contentRect", "ResizeObserverSPI", "controller", "callbackCtx", "activeObservations_", "observations_", "TypeError", "callback_", "controller_", "callbackCtx_", "Element", "observations", "unobserve", "clearActive", "_this", "observation", "map", "WeakMap", "ResizeObserver", "method", "apply"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js"], "sourcesContent": ["/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA,IAAIA,OAAO,GAAI,YAAY;EACvB,IAAI,OAAOC,GAAP,KAAe,WAAnB,EAAgC;IAC5B,OAAOA,GAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI,SAASC,QAAT,CAAkBC,GAAlB,EAAuBC,GAAvB,EAA4B;IACxB,IAAIC,MAAM,GAAG,CAAC,CAAd;IACAF,GAAG,CAACG,IAAJ,CAAS,UAAUC,KAAV,EAAiBC,KAAjB,EAAwB;MAC7B,IAAID,KAAK,CAAC,CAAD,CAAL,KAAaH,GAAjB,EAAsB;QAClBC,MAAM,GAAGG,KAAT;QACA,OAAO,IAAP;MACH;;MACD,OAAO,KAAP;IACH,CAND;IAOA,OAAOH,MAAP;EACH;;EACD;IAAO;IAAe,YAAY;MAC9B,SAASI,OAAT,GAAmB;QACf,KAAKC,WAAL,GAAmB,EAAnB;MACH;;MACDC,MAAM,CAACC,cAAP,CAAsBH,OAAO,CAACI,SAA9B,EAAyC,MAAzC,EAAiD;QAC7C;AACZ;AACA;QACYC,GAAG,EAAE,eAAY;UACb,OAAO,KAAKJ,WAAL,CAAiBK,MAAxB;QACH,CAN4C;QAO7CC,UAAU,EAAE,IAPiC;QAQ7CC,YAAY,EAAE;MAR+B,CAAjD;MAUA;AACR;AACA;AACA;;MACQR,OAAO,CAACI,SAAR,CAAkBC,GAAlB,GAAwB,UAAUV,GAAV,EAAe;QACnC,IAAII,KAAK,GAAGN,QAAQ,CAAC,KAAKQ,WAAN,EAAmBN,GAAnB,CAApB;QACA,IAAIG,KAAK,GAAG,KAAKG,WAAL,CAAiBF,KAAjB,CAAZ;QACA,OAAOD,KAAK,IAAIA,KAAK,CAAC,CAAD,CAArB;MACH,CAJD;MAKA;AACR;AACA;AACA;AACA;;;MACQE,OAAO,CAACI,SAAR,CAAkBK,GAAlB,GAAwB,UAAUd,GAAV,EAAee,KAAf,EAAsB;QAC1C,IAAIX,KAAK,GAAGN,QAAQ,CAAC,KAAKQ,WAAN,EAAmBN,GAAnB,CAApB;;QACA,IAAI,CAACI,KAAL,EAAY;UACR,KAAKE,WAAL,CAAiBF,KAAjB,EAAwB,CAAxB,IAA6BW,KAA7B;QACH,CAFD,MAGK;UACD,KAAKT,WAAL,CAAiBU,IAAjB,CAAsB,CAAChB,GAAD,EAAMe,KAAN,CAAtB;QACH;MACJ,CARD;MASA;AACR;AACA;AACA;;;MACQV,OAAO,CAACI,SAAR,CAAkBQ,MAAlB,GAA2B,UAAUjB,GAAV,EAAe;QACtC,IAAIkB,OAAO,GAAG,KAAKZ,WAAnB;QACA,IAAIF,KAAK,GAAGN,QAAQ,CAACoB,OAAD,EAAUlB,GAAV,CAApB;;QACA,IAAI,CAACI,KAAL,EAAY;UACRc,OAAO,CAACC,MAAR,CAAef,KAAf,EAAsB,CAAtB;QACH;MACJ,CAND;MAOA;AACR;AACA;AACA;;;MACQC,OAAO,CAACI,SAAR,CAAkBW,GAAlB,GAAwB,UAAUpB,GAAV,EAAe;QACnC,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,KAAKQ,WAAN,EAAmBN,GAAnB,CAAlB;MACH,CAFD;MAGA;AACR;AACA;;;MACQK,OAAO,CAACI,SAAR,CAAkBY,KAAlB,GAA0B,YAAY;QAClC,KAAKf,WAAL,CAAiBa,MAAjB,CAAwB,CAAxB;MACH,CAFD;MAGA;AACR;AACA;AACA;AACA;;;MACQd,OAAO,CAACI,SAAR,CAAkBa,OAAlB,GAA4B,UAAUC,QAAV,EAAoBC,GAApB,EAAyB;QACjD,IAAIA,GAAG,KAAK,KAAK,CAAjB,EAAoB;UAAEA,GAAG,GAAG,IAAN;QAAa;;QACnC,KAAK,IAAIC,EAAE,GAAG,CAAT,EAAYC,EAAE,GAAG,KAAKpB,WAA3B,EAAwCmB,EAAE,GAAGC,EAAE,CAACf,MAAhD,EAAwDc,EAAE,EAA1D,EAA8D;UAC1D,IAAItB,KAAK,GAAGuB,EAAE,CAACD,EAAD,CAAd;UACAF,QAAQ,CAACI,IAAT,CAAcH,GAAd,EAAmBrB,KAAK,CAAC,CAAD,CAAxB,EAA6BA,KAAK,CAAC,CAAD,CAAlC;QACH;MACJ,CAND;;MAOA,OAAOE,OAAP;IACH,CA1EqB;EAAtB;AA2EH,CAjGa,EAAd;AAmGA;AACA;AACA;;;AACA,IAAIuB,SAAS,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOC,QAAP,KAAoB,WAArD,IAAoED,MAAM,CAACC,QAAP,KAAoBA,QAAxG,C,CAEA;;AACA,IAAIC,QAAQ,GAAI,YAAY;EACxB,IAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,IAAP,KAAgBA,IAArD,EAA2D;IACvD,OAAOD,MAAP;EACH;;EACD,IAAI,OAAOE,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,CAACD,IAAL,KAAcA,IAAjD,EAAuD;IACnD,OAAOC,IAAP;EACH;;EACD,IAAI,OAAOL,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACI,IAAP,KAAgBA,IAArD,EAA2D;IACvD,OAAOJ,MAAP;EACH,CATuB,CAUxB;;;EACA,OAAOM,QAAQ,CAAC,aAAD,CAAR,EAAP;AACH,CAZc,EAAf;AAcA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIC,uBAAuB,GAAI,YAAY;EACvC,IAAI,OAAOC,qBAAP,KAAiC,UAArC,EAAiD;IAC7C;IACA;IACA;IACA,OAAOA,qBAAqB,CAACC,IAAtB,CAA2BP,QAA3B,CAAP;EACH;;EACD,OAAO,UAAUR,QAAV,EAAoB;IAAE,OAAOgB,UAAU,CAAC,YAAY;MAAE,OAAOhB,QAAQ,CAACiB,IAAI,CAACC,GAAL,EAAD,CAAf;IAA8B,CAA7C,EAA+C,OAAO,EAAtD,CAAjB;EAA6E,CAA1G;AACH,CAR6B,EAA9B,C,CAUA;;;AACA,IAAIC,eAAe,GAAG,CAAtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAmBpB,QAAnB,EAA6BqB,KAA7B,EAAoC;EAChC,IAAIC,WAAW,GAAG,KAAlB;EAAA,IAAyBC,YAAY,GAAG,KAAxC;EAAA,IAA+CC,YAAY,GAAG,CAA9D;EACA;AACJ;AACA;AACA;AACA;AACA;;EACI,SAASC,cAAT,GAA0B;IACtB,IAAIH,WAAJ,EAAiB;MACbA,WAAW,GAAG,KAAd;MACAtB,QAAQ;IACX;;IACD,IAAIuB,YAAJ,EAAkB;MACdG,KAAK;IACR;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI,SAASC,eAAT,GAA2B;IACvBd,uBAAuB,CAACY,cAAD,CAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI,SAASC,KAAT,GAAiB;IACb,IAAIE,SAAS,GAAGX,IAAI,CAACC,GAAL,EAAhB;;IACA,IAAII,WAAJ,EAAiB;MACb;MACA,IAAIM,SAAS,GAAGJ,YAAZ,GAA2BL,eAA/B,EAAgD;QAC5C;MACH,CAJY,CAKb;MACA;MACA;MACA;;;MACAI,YAAY,GAAG,IAAf;IACH,CAVD,MAWK;MACDD,WAAW,GAAG,IAAd;MACAC,YAAY,GAAG,KAAf;MACAP,UAAU,CAACW,eAAD,EAAkBN,KAAlB,CAAV;IACH;;IACDG,YAAY,GAAGI,SAAf;EACH;;EACD,OAAOF,KAAP;AACH,C,CAED;;;AACA,IAAIG,aAAa,GAAG,EAApB,C,CACA;AACA;;AACA,IAAIC,cAAc,GAAG,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,EAAmC,OAAnC,EAA4C,QAA5C,EAAsD,MAAtD,EAA8D,QAA9D,CAArB,C,CACA;;AACA,IAAIC,yBAAyB,GAAG,OAAOC,gBAAP,KAA4B,WAA5D;AACA;AACA;AACA;;AACA,IAAIC,wBAAwB;AAAG;AAAe,YAAY;EACtD;AACJ;AACA;AACA;AACA;EACI,SAASA,wBAAT,GAAoC;IAChC;AACR;AACA;AACA;AACA;IACQ,KAAKC,UAAL,GAAkB,KAAlB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,oBAAL,GAA4B,KAA5B;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,kBAAL,GAA0B,IAA1B;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,UAAL,GAAkB,EAAlB;IACA,KAAKC,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBvB,IAAtB,CAA2B,IAA3B,CAAxB;IACA,KAAKwB,OAAL,GAAenB,QAAQ,CAAC,KAAKmB,OAAL,CAAaxB,IAAb,CAAkB,IAAlB,CAAD,EAA0Bc,aAA1B,CAAvB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACII,wBAAwB,CAAC/C,SAAzB,CAAmCsD,WAAnC,GAAiD,UAAUC,QAAV,EAAoB;IACjE,IAAI,CAAC,CAAC,KAAKJ,UAAL,CAAgBK,OAAhB,CAAwBD,QAAxB,CAAN,EAAyC;MACrC,KAAKJ,UAAL,CAAgB5C,IAAhB,CAAqBgD,QAArB;IACH,CAHgE,CAIjE;;;IACA,IAAI,CAAC,KAAKP,UAAV,EAAsB;MAClB,KAAKS,QAAL;IACH;EACJ,CARD;EASA;AACJ;AACA;AACA;AACA;AACA;;;EACIV,wBAAwB,CAAC/C,SAAzB,CAAmC0D,cAAnC,GAAoD,UAAUH,QAAV,EAAoB;IACpE,IAAII,SAAS,GAAG,KAAKR,UAArB;IACA,IAAIxD,KAAK,GAAGgE,SAAS,CAACH,OAAV,CAAkBD,QAAlB,CAAZ,CAFoE,CAGpE;;IACA,IAAI,CAAC5D,KAAL,EAAY;MACRgE,SAAS,CAACjD,MAAV,CAAiBf,KAAjB,EAAwB,CAAxB;IACH,CANmE,CAOpE;;;IACA,IAAI,CAACgE,SAAS,CAACzD,MAAX,IAAqB,KAAK8C,UAA9B,EAA0C;MACtC,KAAKY,WAAL;IACH;EACJ,CAXD;EAYA;AACJ;AACA;AACA;AACA;AACA;;;EACIb,wBAAwB,CAAC/C,SAAzB,CAAmCqD,OAAnC,GAA6C,YAAY;IACrD,IAAIQ,eAAe,GAAG,KAAKC,gBAAL,EAAtB,CADqD,CAErD;IACA;;IACA,IAAID,eAAJ,EAAqB;MACjB,KAAKR,OAAL;IACH;EACJ,CAPD;EAQA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIN,wBAAwB,CAAC/C,SAAzB,CAAmC8D,gBAAnC,GAAsD,YAAY;IAC9D;IACA,IAAIC,eAAe,GAAG,KAAKZ,UAAL,CAAgBa,MAAhB,CAAuB,UAAUT,QAAV,EAAoB;MAC7D,OAAOA,QAAQ,CAACU,YAAT,IAAyBV,QAAQ,CAACW,SAAT,EAAhC;IACH,CAFqB,CAAtB,CAF8D,CAK9D;IACA;IACA;IACA;IACA;;IACAH,eAAe,CAAClD,OAAhB,CAAwB,UAAU0C,QAAV,EAAoB;MAAE,OAAOA,QAAQ,CAACY,eAAT,EAAP;IAAoC,CAAlF;IACA,OAAOJ,eAAe,CAAC7D,MAAhB,GAAyB,CAAhC;EACH,CAZD;EAaA;AACJ;AACA;AACA;AACA;AACA;;;EACI6C,wBAAwB,CAAC/C,SAAzB,CAAmCyD,QAAnC,GAA8C,YAAY;IACtD;IACA;IACA,IAAI,CAACtC,SAAD,IAAc,KAAK6B,UAAvB,EAAmC;MAC/B;IACH,CALqD,CAMtD;IACA;IACA;;;IACA3B,QAAQ,CAAC+C,gBAAT,CAA0B,eAA1B,EAA2C,KAAKhB,gBAAhD;IACAhC,MAAM,CAACgD,gBAAP,CAAwB,QAAxB,EAAkC,KAAKf,OAAvC;;IACA,IAAIR,yBAAJ,EAA+B;MAC3B,KAAKK,kBAAL,GAA0B,IAAIJ,gBAAJ,CAAqB,KAAKO,OAA1B,CAA1B;MACA,KAAKH,kBAAL,CAAwBmB,OAAxB,CAAgChD,QAAhC,EAA0C;QACtCiD,UAAU,EAAE,IAD0B;QAEtCC,SAAS,EAAE,IAF2B;QAGtCC,aAAa,EAAE,IAHuB;QAItCC,OAAO,EAAE;MAJ6B,CAA1C;IAMH,CARD,MASK;MACDpD,QAAQ,CAAC+C,gBAAT,CAA0B,oBAA1B,EAAgD,KAAKf,OAArD;MACA,KAAKJ,oBAAL,GAA4B,IAA5B;IACH;;IACD,KAAKD,UAAL,GAAkB,IAAlB;EACH,CAzBD;EA0BA;AACJ;AACA;AACA;AACA;AACA;;;EACID,wBAAwB,CAAC/C,SAAzB,CAAmC4D,WAAnC,GAAiD,YAAY;IACzD;IACA;IACA,IAAI,CAACzC,SAAD,IAAc,CAAC,KAAK6B,UAAxB,EAAoC;MAChC;IACH;;IACD3B,QAAQ,CAACqD,mBAAT,CAA6B,eAA7B,EAA8C,KAAKtB,gBAAnD;IACAhC,MAAM,CAACsD,mBAAP,CAA2B,QAA3B,EAAqC,KAAKrB,OAA1C;;IACA,IAAI,KAAKH,kBAAT,EAA6B;MACzB,KAAKA,kBAAL,CAAwByB,UAAxB;IACH;;IACD,IAAI,KAAK1B,oBAAT,EAA+B;MAC3B5B,QAAQ,CAACqD,mBAAT,CAA6B,oBAA7B,EAAmD,KAAKrB,OAAxD;IACH;;IACD,KAAKH,kBAAL,GAA0B,IAA1B;IACA,KAAKD,oBAAL,GAA4B,KAA5B;IACA,KAAKD,UAAL,GAAkB,KAAlB;EACH,CAjBD;EAkBA;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACID,wBAAwB,CAAC/C,SAAzB,CAAmCoD,gBAAnC,GAAsD,UAAUnC,EAAV,EAAc;IAChE,IAAI2D,EAAE,GAAG3D,EAAE,CAAC4D,YAAZ;IAAA,IAA0BA,YAAY,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EAA9D,CADgE,CAEhE;;IACA,IAAIE,gBAAgB,GAAGlC,cAAc,CAACnD,IAAf,CAAoB,UAAUF,GAAV,EAAe;MACtD,OAAO,CAAC,CAAC,CAACsF,YAAY,CAACrB,OAAb,CAAqBjE,GAArB,CAAV;IACH,CAFsB,CAAvB;;IAGA,IAAIuF,gBAAJ,EAAsB;MAClB,KAAKzB,OAAL;IACH;EACJ,CATD;EAUA;AACJ;AACA;AACA;AACA;;;EACIN,wBAAwB,CAACgC,WAAzB,GAAuC,YAAY;IAC/C,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,KAAKA,SAAL,GAAiB,IAAIjC,wBAAJ,EAAjB;IACH;;IACD,OAAO,KAAKiC,SAAZ;EACH,CALD;EAMA;AACJ;AACA;AACA;AACA;;;EACIjC,wBAAwB,CAACiC,SAAzB,GAAqC,IAArC;EACA,OAAOjC,wBAAP;AACH,CAjM6C,EAA9C;AAmMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIkC,kBAAkB,GAAI,SAAtBA,kBAAsB,CAAUC,MAAV,EAAkBC,KAAlB,EAAyB;EAC/C,KAAK,IAAInE,EAAE,GAAG,CAAT,EAAYC,EAAE,GAAGnB,MAAM,CAACsF,IAAP,CAAYD,KAAZ,CAAtB,EAA0CnE,EAAE,GAAGC,EAAE,CAACf,MAAlD,EAA0Dc,EAAE,EAA5D,EAAgE;IAC5D,IAAIzB,GAAG,GAAG0B,EAAE,CAACD,EAAD,CAAZ;IACAlB,MAAM,CAACC,cAAP,CAAsBmF,MAAtB,EAA8B3F,GAA9B,EAAmC;MAC/Be,KAAK,EAAE6E,KAAK,CAAC5F,GAAD,CADmB;MAE/BY,UAAU,EAAE,KAFmB;MAG/BkF,QAAQ,EAAE,KAHqB;MAI/BjF,YAAY,EAAE;IAJiB,CAAnC;EAMH;;EACD,OAAO8E,MAAP;AACH,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAII,WAAW,GAAI,SAAfA,WAAe,CAAUJ,MAAV,EAAkB;EACjC;EACA;EACA;EACA,IAAIK,WAAW,GAAGL,MAAM,IAAIA,MAAM,CAACM,aAAjB,IAAkCN,MAAM,CAACM,aAAP,CAAqBC,WAAzE,CAJiC,CAKjC;EACA;;EACA,OAAOF,WAAW,IAAIjE,QAAtB;AACH,CARD,C,CAUA;;;AACA,IAAIoE,SAAS,GAAGC,cAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAA9B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBtF,KAAjB,EAAwB;EACpB,OAAOuF,UAAU,CAACvF,KAAD,CAAV,IAAqB,CAA5B;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASwF,cAAT,CAAwBC,MAAxB,EAAgC;EAC5B,IAAIC,SAAS,GAAG,EAAhB;;EACA,KAAK,IAAIhF,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGiF,SAAS,CAAC/F,MAAhC,EAAwCc,EAAE,EAA1C,EAA8C;IAC1CgF,SAAS,CAAChF,EAAE,GAAG,CAAN,CAAT,GAAoBiF,SAAS,CAACjF,EAAD,CAA7B;EACH;;EACD,OAAOgF,SAAS,CAACE,MAAV,CAAiB,UAAUC,IAAV,EAAgBC,QAAhB,EAA0B;IAC9C,IAAI9F,KAAK,GAAGyF,MAAM,CAAC,YAAYK,QAAZ,GAAuB,QAAxB,CAAlB;IACA,OAAOD,IAAI,GAAGP,OAAO,CAACtF,KAAD,CAArB;EACH,CAHM,EAGJ,CAHI,CAAP;AAIH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS+F,WAAT,CAAqBN,MAArB,EAA6B;EACzB,IAAIC,SAAS,GAAG,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAAhB;EACA,IAAIM,QAAQ,GAAG,EAAf;;EACA,KAAK,IAAItF,EAAE,GAAG,CAAT,EAAYuF,WAAW,GAAGP,SAA/B,EAA0ChF,EAAE,GAAGuF,WAAW,CAACrG,MAA3D,EAAmEc,EAAE,EAArE,EAAyE;IACrE,IAAIoF,QAAQ,GAAGG,WAAW,CAACvF,EAAD,CAA1B;IACA,IAAIV,KAAK,GAAGyF,MAAM,CAAC,aAAaK,QAAd,CAAlB;IACAE,QAAQ,CAACF,QAAD,CAAR,GAAqBR,OAAO,CAACtF,KAAD,CAA5B;EACH;;EACD,OAAOgG,QAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,iBAAT,CAA2BtB,MAA3B,EAAmC;EAC/B,IAAIuB,IAAI,GAAGvB,MAAM,CAACwB,OAAP,EAAX;EACA,OAAOf,cAAc,CAAC,CAAD,EAAI,CAAJ,EAAOc,IAAI,CAACE,KAAZ,EAAmBF,IAAI,CAACG,MAAxB,CAArB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,yBAAT,CAAmC3B,MAAnC,EAA2C;EACvC;EACA;EACA,IAAI4B,WAAW,GAAG5B,MAAM,CAAC4B,WAAzB;EAAA,IAAsCC,YAAY,GAAG7B,MAAM,CAAC6B,YAA5D,CAHuC,CAIvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAI,CAACD,WAAD,IAAgB,CAACC,YAArB,EAAmC;IAC/B,OAAOrB,SAAP;EACH;;EACD,IAAIK,MAAM,GAAGT,WAAW,CAACJ,MAAD,CAAX,CAAoB8B,gBAApB,CAAqC9B,MAArC,CAAb;EACA,IAAIoB,QAAQ,GAAGD,WAAW,CAACN,MAAD,CAA1B;EACA,IAAIkB,QAAQ,GAAGX,QAAQ,CAACY,IAAT,GAAgBZ,QAAQ,CAACa,KAAxC;EACA,IAAIC,OAAO,GAAGd,QAAQ,CAACe,GAAT,GAAef,QAAQ,CAACgB,MAAtC,CAlBuC,CAmBvC;EACA;EACA;EACA;;EACA,IAAIX,KAAK,GAAGf,OAAO,CAACG,MAAM,CAACY,KAAR,CAAnB;EAAA,IAAmCC,MAAM,GAAGhB,OAAO,CAACG,MAAM,CAACa,MAAR,CAAnD,CAvBuC,CAwBvC;EACA;;EACA,IAAIb,MAAM,CAACwB,SAAP,KAAqB,YAAzB,EAAuC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA,IAAI/F,IAAI,CAACgG,KAAL,CAAWb,KAAK,GAAGM,QAAnB,MAAiCH,WAArC,EAAkD;MAC9CH,KAAK,IAAIb,cAAc,CAACC,MAAD,EAAS,MAAT,EAAiB,OAAjB,CAAd,GAA0CkB,QAAnD;IACH;;IACD,IAAIzF,IAAI,CAACgG,KAAL,CAAWZ,MAAM,GAAGQ,OAApB,MAAiCL,YAArC,EAAmD;MAC/CH,MAAM,IAAId,cAAc,CAACC,MAAD,EAAS,KAAT,EAAgB,QAAhB,CAAd,GAA0CqB,OAApD;IACH;EACJ,CAvCsC,CAwCvC;EACA;EACA;EACA;;;EACA,IAAI,CAACK,iBAAiB,CAACvC,MAAD,CAAtB,EAAgC;IAC5B;IACA;IACA;IACA;IACA,IAAIwC,aAAa,GAAGlG,IAAI,CAACgG,KAAL,CAAWb,KAAK,GAAGM,QAAnB,IAA+BH,WAAnD;IACA,IAAIa,cAAc,GAAGnG,IAAI,CAACgG,KAAL,CAAWZ,MAAM,GAAGQ,OAApB,IAA+BL,YAApD,CAN4B,CAO5B;IACA;IACA;IACA;IACA;;IACA,IAAIvF,IAAI,CAACoG,GAAL,CAASF,aAAT,MAA4B,CAAhC,EAAmC;MAC/Bf,KAAK,IAAIe,aAAT;IACH;;IACD,IAAIlG,IAAI,CAACoG,GAAL,CAASD,cAAT,MAA6B,CAAjC,EAAoC;MAChCf,MAAM,IAAIe,cAAV;IACH;EACJ;;EACD,OAAOhC,cAAc,CAACW,QAAQ,CAACY,IAAV,EAAgBZ,QAAQ,CAACe,GAAzB,EAA8BV,KAA9B,EAAqCC,MAArC,CAArB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIiB,oBAAoB,GAAI,YAAY;EACpC;EACA;EACA,IAAI,OAAOC,kBAAP,KAA8B,WAAlC,EAA+C;IAC3C,OAAO,UAAU5C,MAAV,EAAkB;MAAE,OAAOA,MAAM,YAAYI,WAAW,CAACJ,MAAD,CAAX,CAAoB4C,kBAA7C;IAAkE,CAA7F;EACH,CALmC,CAMpC;EACA;EACA;;;EACA,OAAO,UAAU5C,MAAV,EAAkB;IAAE,OAAQA,MAAM,YAAYI,WAAW,CAACJ,MAAD,CAAX,CAAoB6C,UAAtC,IAC/B,OAAO7C,MAAM,CAACwB,OAAd,KAA0B,UADH;EACiB,CAD5C;AAEH,CAX0B,EAA3B;AAYA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASe,iBAAT,CAA2BvC,MAA3B,EAAmC;EAC/B,OAAOA,MAAM,KAAKI,WAAW,CAACJ,MAAD,CAAX,CAAoB7D,QAApB,CAA6B2G,eAA/C;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,cAAT,CAAwB/C,MAAxB,EAAgC;EAC5B,IAAI,CAAC/D,SAAL,EAAgB;IACZ,OAAOuE,SAAP;EACH;;EACD,IAAImC,oBAAoB,CAAC3C,MAAD,CAAxB,EAAkC;IAC9B,OAAOsB,iBAAiB,CAACtB,MAAD,CAAxB;EACH;;EACD,OAAO2B,yBAAyB,CAAC3B,MAAD,CAAhC;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASgD,kBAAT,CAA4BjH,EAA5B,EAAgC;EAC5B,IAAIkH,CAAC,GAAGlH,EAAE,CAACkH,CAAX;EAAA,IAAcC,CAAC,GAAGnH,EAAE,CAACmH,CAArB;EAAA,IAAwBzB,KAAK,GAAG1F,EAAE,CAAC0F,KAAnC;EAAA,IAA0CC,MAAM,GAAG3F,EAAE,CAAC2F,MAAtD,CAD4B,CAE5B;;EACA,IAAIyB,MAAM,GAAG,OAAOC,eAAP,KAA2B,WAA3B,GAAyCA,eAAzC,GAA2DxI,MAAxE;EACA,IAAIyI,IAAI,GAAGzI,MAAM,CAAC0I,MAAP,CAAcH,MAAM,CAACrI,SAArB,CAAX,CAJ4B,CAK5B;;EACAiF,kBAAkB,CAACsD,IAAD,EAAO;IACrBJ,CAAC,EAAEA,CADkB;IACfC,CAAC,EAAEA,CADY;IACTzB,KAAK,EAAEA,KADE;IACKC,MAAM,EAAEA,MADb;IAErBS,GAAG,EAAEe,CAFgB;IAGrBjB,KAAK,EAAEgB,CAAC,GAAGxB,KAHU;IAIrBW,MAAM,EAAEV,MAAM,GAAGwB,CAJI;IAKrBlB,IAAI,EAAEiB;EALe,CAAP,CAAlB;EAOA,OAAOI,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS5C,cAAT,CAAwBwC,CAAxB,EAA2BC,CAA3B,EAA8BzB,KAA9B,EAAqCC,MAArC,EAA6C;EACzC,OAAO;IAAEuB,CAAC,EAAEA,CAAL;IAAQC,CAAC,EAAEA,CAAX;IAAczB,KAAK,EAAEA,KAArB;IAA4BC,MAAM,EAAEA;EAApC,CAAP;AACH;AAED;AACA;AACA;AACA;;;AACA,IAAI6B,iBAAiB;AAAG;AAAe,YAAY;EAC/C;AACJ;AACA;AACA;AACA;EACI,SAASA,iBAAT,CAA2BvD,MAA3B,EAAmC;IAC/B;AACR;AACA;AACA;AACA;IACQ,KAAKwD,cAAL,GAAsB,CAAtB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,eAAL,GAAuB,CAAvB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,YAAL,GAAoBjD,cAAc,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAlC;IACA,KAAKT,MAAL,GAAcA,MAAd;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIuD,iBAAiB,CAACzI,SAAlB,CAA4B6I,QAA5B,GAAuC,YAAY;IAC/C,IAAIN,IAAI,GAAGN,cAAc,CAAC,KAAK/C,MAAN,CAAzB;IACA,KAAK0D,YAAL,GAAoBL,IAApB;IACA,OAAQA,IAAI,CAAC5B,KAAL,KAAe,KAAK+B,cAApB,IACJH,IAAI,CAAC3B,MAAL,KAAgB,KAAK+B,eADzB;EAEH,CALD;EAMA;AACJ;AACA;AACA;AACA;AACA;;;EACIF,iBAAiB,CAACzI,SAAlB,CAA4B8I,aAA5B,GAA4C,YAAY;IACpD,IAAIP,IAAI,GAAG,KAAKK,YAAhB;IACA,KAAKF,cAAL,GAAsBH,IAAI,CAAC5B,KAA3B;IACA,KAAKgC,eAAL,GAAuBJ,IAAI,CAAC3B,MAA5B;IACA,OAAO2B,IAAP;EACH,CALD;;EAMA,OAAOE,iBAAP;AACH,CApDsC,EAAvC;;AAsDA,IAAIM,mBAAmB;AAAG;AAAe,YAAY;EACjD;AACJ;AACA;AACA;AACA;AACA;EACI,SAASA,mBAAT,CAA6B7D,MAA7B,EAAqC8D,QAArC,EAA+C;IAC3C,IAAIC,WAAW,GAAGf,kBAAkB,CAACc,QAAD,CAApC,CAD2C,CAE3C;IACA;IACA;IACA;IACA;IACA;;IACA/D,kBAAkB,CAAC,IAAD,EAAO;MAAEC,MAAM,EAAEA,MAAV;MAAkB+D,WAAW,EAAEA;IAA/B,CAAP,CAAlB;EACH;;EACD,OAAOF,mBAAP;AACH,CAlBwC,EAAzC;;AAoBA,IAAIG,iBAAiB;AAAG;AAAe,YAAY;EAC/C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,iBAAT,CAA2BpI,QAA3B,EAAqCqI,UAArC,EAAiDC,WAAjD,EAA8D;IAC1D;AACR;AACA;AACA;AACA;AACA;IACQ,KAAKC,mBAAL,GAA2B,EAA3B;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,aAAL,GAAqB,IAAInK,OAAJ,EAArB;;IACA,IAAI,OAAO2B,QAAP,KAAoB,UAAxB,EAAoC;MAChC,MAAM,IAAIyI,SAAJ,CAAc,yDAAd,CAAN;IACH;;IACD,KAAKC,SAAL,GAAiB1I,QAAjB;IACA,KAAK2I,WAAL,GAAmBN,UAAnB;IACA,KAAKO,YAAL,GAAoBN,WAApB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIF,iBAAiB,CAAClJ,SAAlB,CAA4BqE,OAA5B,GAAsC,UAAUa,MAAV,EAAkB;IACpD,IAAI,CAACe,SAAS,CAAC/F,MAAf,EAAuB;MACnB,MAAM,IAAIqJ,SAAJ,CAAc,0CAAd,CAAN;IACH,CAHmD,CAIpD;;;IACA,IAAI,OAAOI,OAAP,KAAmB,WAAnB,IAAkC,EAAEA,OAAO,YAAY7J,MAArB,CAAtC,EAAoE;MAChE;IACH;;IACD,IAAI,EAAEoF,MAAM,YAAYI,WAAW,CAACJ,MAAD,CAAX,CAAoByE,OAAxC,CAAJ,EAAsD;MAClD,MAAM,IAAIJ,SAAJ,CAAc,uCAAd,CAAN;IACH;;IACD,IAAIK,YAAY,GAAG,KAAKN,aAAxB,CAXoD,CAYpD;;IACA,IAAIM,YAAY,CAACjJ,GAAb,CAAiBuE,MAAjB,CAAJ,EAA8B;MAC1B;IACH;;IACD0E,YAAY,CAACvJ,GAAb,CAAiB6E,MAAjB,EAAyB,IAAIuD,iBAAJ,CAAsBvD,MAAtB,CAAzB;IACA,KAAKuE,WAAL,CAAiBnG,WAAjB,CAA6B,IAA7B,EAjBoD,CAkBpD;;IACA,KAAKmG,WAAL,CAAiBpG,OAAjB;EACH,CApBD;EAqBA;AACJ;AACA;AACA;AACA;AACA;;;EACI6F,iBAAiB,CAAClJ,SAAlB,CAA4B6J,SAA5B,GAAwC,UAAU3E,MAAV,EAAkB;IACtD,IAAI,CAACe,SAAS,CAAC/F,MAAf,EAAuB;MACnB,MAAM,IAAIqJ,SAAJ,CAAc,0CAAd,CAAN;IACH,CAHqD,CAItD;;;IACA,IAAI,OAAOI,OAAP,KAAmB,WAAnB,IAAkC,EAAEA,OAAO,YAAY7J,MAArB,CAAtC,EAAoE;MAChE;IACH;;IACD,IAAI,EAAEoF,MAAM,YAAYI,WAAW,CAACJ,MAAD,CAAX,CAAoByE,OAAxC,CAAJ,EAAsD;MAClD,MAAM,IAAIJ,SAAJ,CAAc,uCAAd,CAAN;IACH;;IACD,IAAIK,YAAY,GAAG,KAAKN,aAAxB,CAXsD,CAYtD;;IACA,IAAI,CAACM,YAAY,CAACjJ,GAAb,CAAiBuE,MAAjB,CAAL,EAA+B;MAC3B;IACH;;IACD0E,YAAY,CAACpJ,MAAb,CAAoB0E,MAApB;;IACA,IAAI,CAAC0E,YAAY,CAACzD,IAAlB,EAAwB;MACpB,KAAKsD,WAAL,CAAiB/F,cAAjB,CAAgC,IAAhC;IACH;EACJ,CApBD;EAqBA;AACJ;AACA;AACA;AACA;;;EACIwF,iBAAiB,CAAClJ,SAAlB,CAA4B2E,UAA5B,GAAyC,YAAY;IACjD,KAAKmF,WAAL;IACA,KAAKR,aAAL,CAAmB1I,KAAnB;IACA,KAAK6I,WAAL,CAAiB/F,cAAjB,CAAgC,IAAhC;EACH,CAJD;EAKA;AACJ;AACA;AACA;AACA;AACA;;;EACIwF,iBAAiB,CAAClJ,SAAlB,CAA4BiE,YAA5B,GAA2C,YAAY;IACnD,IAAI8F,KAAK,GAAG,IAAZ;;IACA,KAAKD,WAAL;IACA,KAAKR,aAAL,CAAmBzI,OAAnB,CAA2B,UAAUmJ,WAAV,EAAuB;MAC9C,IAAIA,WAAW,CAACnB,QAAZ,EAAJ,EAA4B;QACxBkB,KAAK,CAACV,mBAAN,CAA0B9I,IAA1B,CAA+ByJ,WAA/B;MACH;IACJ,CAJD;EAKH,CARD;EASA;AACJ;AACA;AACA;AACA;AACA;;;EACId,iBAAiB,CAAClJ,SAAlB,CAA4BmE,eAA5B,GAA8C,YAAY;IACtD;IACA,IAAI,CAAC,KAAKD,SAAL,EAAL,EAAuB;MACnB;IACH;;IACD,IAAInD,GAAG,GAAG,KAAK2I,YAAf,CALsD,CAMtD;;IACA,IAAIjJ,OAAO,GAAG,KAAK4I,mBAAL,CAAyBY,GAAzB,CAA6B,UAAUD,WAAV,EAAuB;MAC9D,OAAO,IAAIjB,mBAAJ,CAAwBiB,WAAW,CAAC9E,MAApC,EAA4C8E,WAAW,CAAClB,aAAZ,EAA5C,CAAP;IACH,CAFa,CAAd;IAGA,KAAKU,SAAL,CAAetI,IAAf,CAAoBH,GAApB,EAAyBN,OAAzB,EAAkCM,GAAlC;IACA,KAAK+I,WAAL;EACH,CAZD;EAaA;AACJ;AACA;AACA;AACA;;;EACIZ,iBAAiB,CAAClJ,SAAlB,CAA4B8J,WAA5B,GAA0C,YAAY;IAClD,KAAKT,mBAAL,CAAyB3I,MAAzB,CAAgC,CAAhC;EACH,CAFD;EAGA;AACJ;AACA;AACA;AACA;;;EACIwI,iBAAiB,CAAClJ,SAAlB,CAA4BkE,SAA5B,GAAwC,YAAY;IAChD,OAAO,KAAKmF,mBAAL,CAAyBnJ,MAAzB,GAAkC,CAAzC;EACH,CAFD;;EAGA,OAAOgJ,iBAAP;AACH,CAnJsC,EAAvC,C,CAqJA;AACA;AACA;;;AACA,IAAIvF,SAAS,GAAG,OAAOuG,OAAP,KAAmB,WAAnB,GAAiC,IAAIA,OAAJ,EAAjC,GAAiD,IAAI/K,OAAJ,EAAjE;AACA;AACA;AACA;AACA;;AACA,IAAIgL,cAAc;AAAG;AAAe,YAAY;EAC5C;AACJ;AACA;AACA;AACA;AACA;EACI,SAASA,cAAT,CAAwBrJ,QAAxB,EAAkC;IAC9B,IAAI,EAAE,gBAAgBqJ,cAAlB,CAAJ,EAAuC;MACnC,MAAM,IAAIZ,SAAJ,CAAc,oCAAd,CAAN;IACH;;IACD,IAAI,CAACtD,SAAS,CAAC/F,MAAf,EAAuB;MACnB,MAAM,IAAIqJ,SAAJ,CAAc,0CAAd,CAAN;IACH;;IACD,IAAIJ,UAAU,GAAGpG,wBAAwB,CAACgC,WAAzB,EAAjB;IACA,IAAIxB,QAAQ,GAAG,IAAI2F,iBAAJ,CAAsBpI,QAAtB,EAAgCqI,UAAhC,EAA4C,IAA5C,CAAf;IACAxF,SAAS,CAACtD,GAAV,CAAc,IAAd,EAAoBkD,QAApB;EACH;;EACD,OAAO4G,cAAP;AACH,CAnBmC,EAApC,C,CAoBA;;;AACA,CACI,SADJ,EAEI,WAFJ,EAGI,YAHJ,EAIEtJ,OAJF,CAIU,UAAUuJ,MAAV,EAAkB;EACxBD,cAAc,CAACnK,SAAf,CAAyBoK,MAAzB,IAAmC,YAAY;IAC3C,IAAInJ,EAAJ;;IACA,OAAO,CAACA,EAAE,GAAG0C,SAAS,CAAC1D,GAAV,CAAc,IAAd,CAAN,EAA2BmK,MAA3B,EAAmCC,KAAnC,CAAyCpJ,EAAzC,EAA6CgF,SAA7C,CAAP;EACH,CAHD;AAIH,CATD;;AAWA,IAAItG,KAAK,GAAI,YAAY;EACrB;EACA,IAAI,OAAO2B,QAAQ,CAAC6I,cAAhB,KAAmC,WAAvC,EAAoD;IAChD,OAAO7I,QAAQ,CAAC6I,cAAhB;EACH;;EACD,OAAOA,cAAP;AACH,CANW,EAAZ;;AAQA,eAAexK,KAAf"}, "metadata": {}, "sourceType": "module"}