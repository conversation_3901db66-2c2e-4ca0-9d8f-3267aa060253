{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useCallback, useRef, useState } from 'react';\n\nvar useDynamicList = function useDynamicList(initialList) {\n  if (initialList === void 0) {\n    initialList = [];\n  }\n\n  var counterRef = useRef(-1);\n  var keyList = useRef([]);\n  var setKey = useCallback(function (index) {\n    counterRef.current += 1;\n    keyList.current.splice(index, 0, counterRef.current);\n  }, []);\n\n  var _a = __read(useState(function () {\n    initialList.forEach(function (_, index) {\n      setKey(index);\n    });\n    return initialList;\n  }), 2),\n      list = _a[0],\n      setList = _a[1];\n\n  var resetList = useCallback(function (newList) {\n    keyList.current = [];\n    setList(function () {\n      newList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return newList;\n    });\n  }, []);\n  var insert = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n\n      temp.splice(index, 0, item);\n      setKey(index);\n      return temp;\n    });\n  }, []);\n  var getKey = useCallback(function (index) {\n    return keyList.current[index];\n  }, []);\n  var getIndex = useCallback(function (key) {\n    return keyList.current.findIndex(function (ele) {\n      return ele === key;\n    });\n  }, []);\n  var merge = useCallback(function (index, items) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n\n      items.forEach(function (_, i) {\n        setKey(index + i);\n      });\n      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));\n      return temp;\n    });\n  }, []);\n  var replace = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n\n      temp[index] = item;\n      return temp;\n    });\n  }, []);\n  var remove = useCallback(function (index) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n\n      temp.splice(index, 1); // remove keys if necessary\n\n      try {\n        keyList.current.splice(index, 1);\n      } catch (e) {\n        console.error(e);\n      }\n\n      return temp;\n    });\n  }, []);\n  var move = useCallback(function (oldIndex, newIndex) {\n    if (oldIndex === newIndex) {\n      return;\n    }\n\n    setList(function (l) {\n      var newList = __spreadArray([], __read(l), false);\n\n      var temp = newList.filter(function (_, index) {\n        return index !== oldIndex;\n      });\n      temp.splice(newIndex, 0, newList[oldIndex]); // move keys if necessary\n\n      try {\n        var keyTemp = keyList.current.filter(function (_, index) {\n          return index !== oldIndex;\n        });\n        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);\n        keyList.current = keyTemp;\n      } catch (e) {\n        console.error(e);\n      }\n\n      return temp;\n    });\n  }, []);\n  var push = useCallback(function (item) {\n    setList(function (l) {\n      setKey(l.length);\n      return l.concat([item]);\n    });\n  }, []);\n  var pop = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(0, keyList.current.length - 1);\n    } catch (e) {\n      console.error(e);\n    }\n\n    setList(function (l) {\n      return l.slice(0, l.length - 1);\n    });\n  }, []);\n  var unshift = useCallback(function (item) {\n    setList(function (l) {\n      setKey(0);\n      return [item].concat(l);\n    });\n  }, []);\n  var shift = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(1, keyList.current.length);\n    } catch (e) {\n      console.error(e);\n    }\n\n    setList(function (l) {\n      return l.slice(1, l.length);\n    });\n  }, []);\n  var sortList = useCallback(function (result) {\n    return result.map(function (item, index) {\n      return {\n        key: index,\n        item: item\n      };\n    }) // add index into obj\n    .sort(function (a, b) {\n      return getIndex(a.key) - getIndex(b.key);\n    }) // sort based on the index of table\n    .filter(function (item) {\n      return !!item.item;\n    }) // remove undefined(s)\n    .map(function (item) {\n      return item.item;\n    });\n  }, // retrive the data\n  []);\n  return {\n    list: list,\n    insert: insert,\n    merge: merge,\n    replace: replace,\n    remove: remove,\n    getKey: getKey,\n    getIndex: getIndex,\n    move: move,\n    push: push,\n    pop: pop,\n    unshift: unshift,\n    shift: shift,\n    sortList: sortList,\n    resetList: resetList\n  };\n};\n\nexport default useDynamicList;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useCallback", "useRef", "useState", "useDynamicList", "initialList", "counterRef", "keyList", "<PERSON><PERSON><PERSON>", "index", "current", "splice", "_a", "for<PERSON>ach", "_", "list", "setList", "resetList", "newList", "insert", "item", "temp", "<PERSON><PERSON><PERSON>", "getIndex", "key", "findIndex", "ele", "merge", "items", "apply", "replace", "remove", "console", "move", "oldIndex", "newIndex", "filter", "keyTemp", "pop", "unshift", "shift", "sortList", "result", "map", "sort", "a", "b"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useDynamicList/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useCallback, useRef, useState } from 'react';\nvar useDynamicList = function useDynamicList(initialList) {\n  if (initialList === void 0) {\n    initialList = [];\n  }\n  var counterRef = useRef(-1);\n  var keyList = useRef([]);\n  var setKey = useCallback(function (index) {\n    counterRef.current += 1;\n    keyList.current.splice(index, 0, counterRef.current);\n  }, []);\n  var _a = __read(useState(function () {\n      initialList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return initialList;\n    }), 2),\n    list = _a[0],\n    setList = _a[1];\n  var resetList = useCallback(function (newList) {\n    keyList.current = [];\n    setList(function () {\n      newList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return newList;\n    });\n  }, []);\n  var insert = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 0, item);\n      setKey(index);\n      return temp;\n    });\n  }, []);\n  var getKey = useCallback(function (index) {\n    return keyList.current[index];\n  }, []);\n  var getIndex = useCallback(function (key) {\n    return keyList.current.findIndex(function (ele) {\n      return ele === key;\n    });\n  }, []);\n  var merge = useCallback(function (index, items) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      items.forEach(function (_, i) {\n        setKey(index + i);\n      });\n      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));\n      return temp;\n    });\n  }, []);\n  var replace = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp[index] = item;\n      return temp;\n    });\n  }, []);\n  var remove = useCallback(function (index) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 1);\n      // remove keys if necessary\n      try {\n        keyList.current.splice(index, 1);\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var move = useCallback(function (oldIndex, newIndex) {\n    if (oldIndex === newIndex) {\n      return;\n    }\n    setList(function (l) {\n      var newList = __spreadArray([], __read(l), false);\n      var temp = newList.filter(function (_, index) {\n        return index !== oldIndex;\n      });\n      temp.splice(newIndex, 0, newList[oldIndex]);\n      // move keys if necessary\n      try {\n        var keyTemp = keyList.current.filter(function (_, index) {\n          return index !== oldIndex;\n        });\n        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);\n        keyList.current = keyTemp;\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var push = useCallback(function (item) {\n    setList(function (l) {\n      setKey(l.length);\n      return l.concat([item]);\n    });\n  }, []);\n  var pop = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(0, keyList.current.length - 1);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(0, l.length - 1);\n    });\n  }, []);\n  var unshift = useCallback(function (item) {\n    setList(function (l) {\n      setKey(0);\n      return [item].concat(l);\n    });\n  }, []);\n  var shift = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(1, keyList.current.length);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(1, l.length);\n    });\n  }, []);\n  var sortList = useCallback(function (result) {\n    return result.map(function (item, index) {\n      return {\n        key: index,\n        item: item\n      };\n    }) // add index into obj\n    .sort(function (a, b) {\n      return getIndex(a.key) - getIndex(b.key);\n    }) // sort based on the index of table\n    .filter(function (item) {\n      return !!item.item;\n    }) // remove undefined(s)\n    .map(function (item) {\n      return item.item;\n    });\n  },\n  // retrive the data\n  []);\n  return {\n    list: list,\n    insert: insert,\n    merge: merge,\n    replace: replace,\n    remove: remove,\n    getKey: getKey,\n    getIndex: getIndex,\n    move: move,\n    push: push,\n    pop: pop,\n    unshift: unshift,\n    shift: shift,\n    sortList: sortList,\n    resetList: resetList\n  };\n};\nexport default useDynamicList;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASS,WAAT,EAAsBC,MAAtB,EAA8BC,QAA9B,QAA8C,OAA9C;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,WAAxB,EAAqC;EACxD,IAAIA,WAAW,KAAK,KAAK,CAAzB,EAA4B;IAC1BA,WAAW,GAAG,EAAd;EACD;;EACD,IAAIC,UAAU,GAAGJ,MAAM,CAAC,CAAC,CAAF,CAAvB;EACA,IAAIK,OAAO,GAAGL,MAAM,CAAC,EAAD,CAApB;EACA,IAAIM,MAAM,GAAGP,WAAW,CAAC,UAAUQ,KAAV,EAAiB;IACxCH,UAAU,CAACI,OAAX,IAAsB,CAAtB;IACAH,OAAO,CAACG,OAAR,CAAgBC,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B,EAAiCH,UAAU,CAACI,OAA5C;EACD,CAHuB,EAGrB,EAHqB,CAAxB;;EAIA,IAAIE,EAAE,GAAGtC,MAAM,CAAC6B,QAAQ,CAAC,YAAY;IACjCE,WAAW,CAACQ,OAAZ,CAAoB,UAAUC,CAAV,EAAaL,KAAb,EAAoB;MACtCD,MAAM,CAACC,KAAD,CAAN;IACD,CAFD;IAGA,OAAOJ,WAAP;EACD,CALqB,CAAT,EAKT,CALS,CAAf;EAAA,IAMEU,IAAI,GAAGH,EAAE,CAAC,CAAD,CANX;EAAA,IAOEI,OAAO,GAAGJ,EAAE,CAAC,CAAD,CAPd;;EAQA,IAAIK,SAAS,GAAGhB,WAAW,CAAC,UAAUiB,OAAV,EAAmB;IAC7CX,OAAO,CAACG,OAAR,GAAkB,EAAlB;IACAM,OAAO,CAAC,YAAY;MAClBE,OAAO,CAACL,OAAR,CAAgB,UAAUC,CAAV,EAAaL,KAAb,EAAoB;QAClCD,MAAM,CAACC,KAAD,CAAN;MACD,CAFD;MAGA,OAAOS,OAAP;IACD,CALM,CAAP;EAMD,CAR0B,EAQxB,EARwB,CAA3B;EASA,IAAIC,MAAM,GAAGlB,WAAW,CAAC,UAAUQ,KAAV,EAAiBW,IAAjB,EAAuB;IAC9CJ,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,IAAIyB,IAAI,GAAG/B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsB,CAAD,CAAX,EAAgB,KAAhB,CAAxB;;MACAyB,IAAI,CAACV,MAAL,CAAYF,KAAZ,EAAmB,CAAnB,EAAsBW,IAAtB;MACAZ,MAAM,CAACC,KAAD,CAAN;MACA,OAAOY,IAAP;IACD,CALM,CAAP;EAMD,CAPuB,EAOrB,EAPqB,CAAxB;EAQA,IAAIC,MAAM,GAAGrB,WAAW,CAAC,UAAUQ,KAAV,EAAiB;IACxC,OAAOF,OAAO,CAACG,OAAR,CAAgBD,KAAhB,CAAP;EACD,CAFuB,EAErB,EAFqB,CAAxB;EAGA,IAAIc,QAAQ,GAAGtB,WAAW,CAAC,UAAUuB,GAAV,EAAe;IACxC,OAAOjB,OAAO,CAACG,OAAR,CAAgBe,SAAhB,CAA0B,UAAUC,GAAV,EAAe;MAC9C,OAAOA,GAAG,KAAKF,GAAf;IACD,CAFM,CAAP;EAGD,CAJyB,EAIvB,EAJuB,CAA1B;EAKA,IAAIG,KAAK,GAAG1B,WAAW,CAAC,UAAUQ,KAAV,EAAiBmB,KAAjB,EAAwB;IAC9CZ,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,IAAIyB,IAAI,GAAG/B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsB,CAAD,CAAX,EAAgB,KAAhB,CAAxB;;MACAgC,KAAK,CAACf,OAAN,CAAc,UAAUC,CAAV,EAAalC,CAAb,EAAgB;QAC5B4B,MAAM,CAACC,KAAK,GAAG7B,CAAT,CAAN;MACD,CAFD;MAGAyC,IAAI,CAACV,MAAL,CAAYkB,KAAZ,CAAkBR,IAAlB,EAAwB/B,aAAa,CAAC,CAACmB,KAAD,EAAQ,CAAR,CAAD,EAAanC,MAAM,CAACsD,KAAD,CAAnB,EAA4B,KAA5B,CAArC;MACA,OAAOP,IAAP;IACD,CAPM,CAAP;EAQD,CATsB,EASpB,EAToB,CAAvB;EAUA,IAAIS,OAAO,GAAG7B,WAAW,CAAC,UAAUQ,KAAV,EAAiBW,IAAjB,EAAuB;IAC/CJ,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,IAAIyB,IAAI,GAAG/B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsB,CAAD,CAAX,EAAgB,KAAhB,CAAxB;;MACAyB,IAAI,CAACZ,KAAD,CAAJ,GAAcW,IAAd;MACA,OAAOC,IAAP;IACD,CAJM,CAAP;EAKD,CANwB,EAMtB,EANsB,CAAzB;EAOA,IAAIU,MAAM,GAAG9B,WAAW,CAAC,UAAUQ,KAAV,EAAiB;IACxCO,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,IAAIyB,IAAI,GAAG/B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsB,CAAD,CAAX,EAAgB,KAAhB,CAAxB;;MACAyB,IAAI,CAACV,MAAL,CAAYF,KAAZ,EAAmB,CAAnB,EAFmB,CAGnB;;MACA,IAAI;QACFF,OAAO,CAACG,OAAR,CAAgBC,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;MACD,CAFD,CAEE,OAAOzB,CAAP,EAAU;QACVgD,OAAO,CAAC3C,KAAR,CAAcL,CAAd;MACD;;MACD,OAAOqC,IAAP;IACD,CAVM,CAAP;EAWD,CAZuB,EAYrB,EAZqB,CAAxB;EAaA,IAAIY,IAAI,GAAGhC,WAAW,CAAC,UAAUiC,QAAV,EAAoBC,QAApB,EAA8B;IACnD,IAAID,QAAQ,KAAKC,QAAjB,EAA2B;MACzB;IACD;;IACDnB,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,IAAIsB,OAAO,GAAG5B,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsB,CAAD,CAAX,EAAgB,KAAhB,CAA3B;;MACA,IAAIyB,IAAI,GAAGH,OAAO,CAACkB,MAAR,CAAe,UAAUtB,CAAV,EAAaL,KAAb,EAAoB;QAC5C,OAAOA,KAAK,KAAKyB,QAAjB;MACD,CAFU,CAAX;MAGAb,IAAI,CAACV,MAAL,CAAYwB,QAAZ,EAAsB,CAAtB,EAAyBjB,OAAO,CAACgB,QAAD,CAAhC,EALmB,CAMnB;;MACA,IAAI;QACF,IAAIG,OAAO,GAAG9B,OAAO,CAACG,OAAR,CAAgB0B,MAAhB,CAAuB,UAAUtB,CAAV,EAAaL,KAAb,EAAoB;UACvD,OAAOA,KAAK,KAAKyB,QAAjB;QACD,CAFa,CAAd;QAGAG,OAAO,CAAC1B,MAAR,CAAewB,QAAf,EAAyB,CAAzB,EAA4B5B,OAAO,CAACG,OAAR,CAAgBwB,QAAhB,CAA5B;QACA3B,OAAO,CAACG,OAAR,GAAkB2B,OAAlB;MACD,CAND,CAME,OAAOrD,CAAP,EAAU;QACVgD,OAAO,CAAC3C,KAAR,CAAcL,CAAd;MACD;;MACD,OAAOqC,IAAP;IACD,CAjBM,CAAP;EAkBD,CAtBqB,EAsBnB,EAtBmB,CAAtB;EAuBA,IAAIlC,IAAI,GAAGc,WAAW,CAAC,UAAUmB,IAAV,EAAgB;IACrCJ,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnBY,MAAM,CAACZ,CAAC,CAACD,MAAH,CAAN;MACA,OAAOC,CAAC,CAACI,MAAF,CAAS,CAACoB,IAAD,CAAT,CAAP;IACD,CAHM,CAAP;EAID,CALqB,EAKnB,EALmB,CAAtB;EAMA,IAAIkB,GAAG,GAAGrC,WAAW,CAAC,YAAY;IAChC;IACA,IAAI;MACFM,OAAO,CAACG,OAAR,GAAkBH,OAAO,CAACG,OAAR,CAAgBX,KAAhB,CAAsB,CAAtB,EAAyBQ,OAAO,CAACG,OAAR,CAAgBf,MAAhB,GAAyB,CAAlD,CAAlB;IACD,CAFD,CAEE,OAAOX,CAAP,EAAU;MACVgD,OAAO,CAAC3C,KAAR,CAAcL,CAAd;IACD;;IACDgC,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,OAAOA,CAAC,CAACG,KAAF,CAAQ,CAAR,EAAWH,CAAC,CAACD,MAAF,GAAW,CAAtB,CAAP;IACD,CAFM,CAAP;EAGD,CAVoB,EAUlB,EAVkB,CAArB;EAWA,IAAI4C,OAAO,GAAGtC,WAAW,CAAC,UAAUmB,IAAV,EAAgB;IACxCJ,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnBY,MAAM,CAAC,CAAD,CAAN;MACA,OAAO,CAACY,IAAD,EAAOpB,MAAP,CAAcJ,CAAd,CAAP;IACD,CAHM,CAAP;EAID,CALwB,EAKtB,EALsB,CAAzB;EAMA,IAAI4C,KAAK,GAAGvC,WAAW,CAAC,YAAY;IAClC;IACA,IAAI;MACFM,OAAO,CAACG,OAAR,GAAkBH,OAAO,CAACG,OAAR,CAAgBX,KAAhB,CAAsB,CAAtB,EAAyBQ,OAAO,CAACG,OAAR,CAAgBf,MAAzC,CAAlB;IACD,CAFD,CAEE,OAAOX,CAAP,EAAU;MACVgD,OAAO,CAAC3C,KAAR,CAAcL,CAAd;IACD;;IACDgC,OAAO,CAAC,UAAUpB,CAAV,EAAa;MACnB,OAAOA,CAAC,CAACG,KAAF,CAAQ,CAAR,EAAWH,CAAC,CAACD,MAAb,CAAP;IACD,CAFM,CAAP;EAGD,CAVsB,EAUpB,EAVoB,CAAvB;EAWA,IAAI8C,QAAQ,GAAGxC,WAAW,CAAC,UAAUyC,MAAV,EAAkB;IAC3C,OAAOA,MAAM,CAACC,GAAP,CAAW,UAAUvB,IAAV,EAAgBX,KAAhB,EAAuB;MACvC,OAAO;QACLe,GAAG,EAAEf,KADA;QAELW,IAAI,EAAEA;MAFD,CAAP;IAID,CALM,EAKJ;IALI,CAMNwB,IANM,CAMD,UAAUC,CAAV,EAAaC,CAAb,EAAgB;MACpB,OAAOvB,QAAQ,CAACsB,CAAC,CAACrB,GAAH,CAAR,GAAkBD,QAAQ,CAACuB,CAAC,CAACtB,GAAH,CAAjC;IACD,CARM,EAQJ;IARI,CASNY,MATM,CASC,UAAUhB,IAAV,EAAgB;MACtB,OAAO,CAAC,CAACA,IAAI,CAACA,IAAd;IACD,CAXM,EAWJ;IAXI,CAYNuB,GAZM,CAYF,UAAUvB,IAAV,EAAgB;MACnB,OAAOA,IAAI,CAACA,IAAZ;IACD,CAdM,CAAP;EAeD,CAhByB,EAiB1B;EACA,EAlB0B,CAA1B;EAmBA,OAAO;IACLL,IAAI,EAAEA,IADD;IAELI,MAAM,EAAEA,MAFH;IAGLQ,KAAK,EAAEA,KAHF;IAILG,OAAO,EAAEA,OAJJ;IAKLC,MAAM,EAAEA,MALH;IAMLT,MAAM,EAAEA,MANH;IAOLC,QAAQ,EAAEA,QAPL;IAQLU,IAAI,EAAEA,IARD;IASL9C,IAAI,EAAEA,IATD;IAULmD,GAAG,EAAEA,GAVA;IAWLC,OAAO,EAAEA,OAXJ;IAYLC,KAAK,EAAEA,KAZF;IAaLC,QAAQ,EAAEA,QAbL;IAcLxB,SAAS,EAAEA;EAdN,CAAP;AAgBD,CArKD;;AAsKA,eAAeb,cAAf"}, "metadata": {}, "sourceType": "module"}