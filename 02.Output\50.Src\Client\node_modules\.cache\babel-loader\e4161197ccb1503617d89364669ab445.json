{"ast": null, "code": "/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * There used to be a class with the same name in `@stomp/ng2-stompjs`, which has been replaced by\n * {@link RxStompConfig} and {@link InjectableRxStompConfig}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {}", "map": {"version": 3, "mappings": "AAWA;;;;;;;;;;AAUA,OAAM,MAAOA,WAAP,CAAkB", "names": ["StompConfig"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\stomp-config.ts"], "sourcesContent": ["import { StompHeaders } from './stomp-headers';\nimport {\n  ActivationState,\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  messageCallbackType,\n  wsErrorCallbackType,\n} from './types';\nimport { Versions } from './versions';\n\n/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * There used to be a class with the same name in `@stomp/ng2-stompjs`, which has been replaced by\n * {@link RxStompConfig} and {@link InjectableRxStompConfig}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {\n  /**\n   * See [Client#brokerURL]{@link Client#brokerURL}.\n   */\n  public brokerURL?: string;\n\n  /**\n   * See See [Client#stompVersions]{@link Client#stompVersions}.\n   */\n  public stompVersions?: Versions;\n\n  /**\n   * See [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   */\n  public webSocketFactory?: () => any;\n\n  /**\n   * See [Client#connectionTimeout]{@link Client#connectionTimeout}.\n   */\n  public connectionTimeout?: number;\n\n  /**\n   * See [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   */\n  public reconnectDelay?: number;\n\n  /**\n   * See [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}.\n   */\n  public heartbeatIncoming?: number;\n\n  /**\n   * See [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   */\n  public heartbeatOutgoing?: number;\n\n  /**\n   * See [Client#splitLargeFrames]{@link Client#splitLargeFrames}.\n   */\n  public splitLargeFrames?: boolean;\n\n  /**\n   * See [Client#forceBinaryWSFrames]{@link Client#forceBinaryWSFrames}.\n   */\n  public forceBinaryWSFrames?: boolean;\n\n  /**\n   * See [Client#appendMissingNULLonIncoming]{@link Client#appendMissingNULLonIncoming}.\n   */\n  public appendMissingNULLonIncoming?: boolean;\n\n  /**\n   * See [Client#maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n   */\n  public maxWebSocketChunkSize?: number;\n\n  /**\n   * See [Client#connectHeaders]{@link Client#connectHeaders}.\n   */\n  public connectHeaders?: StompHeaders;\n\n  /**\n   * See [Client#disconnectHeaders]{@link Client#disconnectHeaders}.\n   */\n  public disconnectHeaders?: StompHeaders;\n\n  /**\n   * See [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   */\n  public onUnhandledMessage?: messageCallbackType;\n\n  /**\n   * See [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   */\n  public onUnhandledReceipt?: frameCallbackType;\n\n  /**\n   * See [Client#onUnhandledFrame]{@link Client#onUnhandledFrame}.\n   */\n  public onUnhandledFrame?: frameCallbackType;\n\n  /**\n   * See [Client#beforeConnect]{@link Client#beforeConnect}.\n   */\n  public beforeConnect?: () => void | Promise<void>;\n\n  /**\n   * See [Client#onConnect]{@link Client#onConnect}.\n   */\n  public onConnect?: frameCallbackType;\n\n  /**\n   * See [Client#onDisconnect]{@link Client#onDisconnect}.\n   */\n  public onDisconnect?: frameCallbackType;\n\n  /**\n   * See [Client#onStompError]{@link Client#onStompError}.\n   */\n  public onStompError?: frameCallbackType;\n\n  /**\n   * See [Client#onWebSocketClose]{@link Client#onWebSocketClose}.\n   */\n  public onWebSocketClose?: closeEventCallbackType;\n\n  /**\n   * See [Client#onWebSocketError]{@link Client#onWebSocketError}.\n   */\n  public onWebSocketError?: wsErrorCallbackType;\n\n  /**\n   * See [Client#logRawCommunication]{@link Client#logRawCommunication}.\n   */\n  public logRawCommunication?: boolean;\n\n  /**\n   * See [Client#debug]{@link Client#debug}.\n   */\n  public debug?: debugFnType;\n\n  /**\n   * See [Client#discardWebsocketOnCommFailure]{@link Client#discardWebsocketOnCommFailure}.\n   */\n  public discardWebsocketOnCommFailure?: boolean;\n\n  /**\n   * See [Client#onChangeState]{@link Client#onChangeState}.\n   */\n  public onChangeState?: (state: ActivationState) => void;\n}\n"]}, "metadata": {}, "sourceType": "module"}