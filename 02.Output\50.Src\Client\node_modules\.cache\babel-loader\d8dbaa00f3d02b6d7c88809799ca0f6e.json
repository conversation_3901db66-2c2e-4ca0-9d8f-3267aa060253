{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n/** `Object#toString` result references. */\n\n\nvar argsTag = '[object Arguments]';\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\n\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\nmodule.exports = baseIsArguments;", "map": {"version": 3, "names": ["baseGetTag", "require", "isObjectLike", "argsTag", "baseIsArguments", "value", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseIsArguments.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]';\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\nmodule.exports = baseIsArguments;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,gBAAD,CAD1B;AAGA;;;AACA,IAAIE,OAAO,GAAG,oBAAd;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,eAAT,CAAyBC,KAAzB,EAAgC;EAC9B,OAAOH,YAAY,CAACG,KAAD,CAAZ,IAAuBL,UAAU,CAACK,KAAD,CAAV,IAAqBF,OAAnD;AACD;;AAEDG,MAAM,CAACC,OAAP,GAAiBH,eAAjB"}, "metadata": {}, "sourceType": "script"}