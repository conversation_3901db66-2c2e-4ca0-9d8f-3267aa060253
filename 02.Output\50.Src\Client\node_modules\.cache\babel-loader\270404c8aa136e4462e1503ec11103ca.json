{"ast": null, "code": "import _createForOfIteratorHelper from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js\";\nimport _slicedToArray from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\n\n/**\n * React Router DOM v6.3.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { useRef, useState, useLayoutEffect, createElement, forwardRef, useCallback, useMemo } from 'react';\nimport { createBrowserHistory, createHashHistory } from 'history';\nimport { Router, useHref, createPath, useLocation, useResolvedPath, useNavigate } from 'react-router';\nexport { MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, Routes, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, createPath, createRoutesFromChildren, generatePath, matchPath, matchRoutes, parsePath, renderMatches, resolvePath, useHref, useInRouterContext, useLocation, useMatch, useNavigate, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes } from 'react-router';\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar _excluded = [\"onClick\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\"],\n    _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"];\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} ////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\n\n\nfunction BrowserRouter(_ref) {\n  var basename = _ref.basename,\n      children = _ref.children,\n      window = _ref.window;\n  var historyRef = useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window: window\n    });\n  }\n\n  var history = historyRef.current;\n\n  var _useState = useState({\n    action: history.action,\n    location: history.location\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n\n  useLayoutEffect(function () {\n    return history.listen(setState);\n  }, [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\n\n\nfunction HashRouter(_ref2) {\n  var basename = _ref2.basename,\n      children = _ref2.children,\n      window = _ref2.window;\n  var historyRef = useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window: window\n    });\n  }\n\n  var history = historyRef.current;\n\n  var _useState3 = useState({\n    action: history.action,\n    location: history.location\n  }),\n      _useState4 = _slicedToArray(_useState3, 2),\n      state = _useState4[0],\n      setState = _useState4[1];\n\n  useLayoutEffect(function () {\n    return history.listen(setState);\n  }, [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\n\n\nfunction HistoryRouter(_ref3) {\n  var basename = _ref3.basename,\n      children = _ref3.children,\n      history = _ref3.history;\n\n  var _useState5 = useState({\n    action: history.action,\n    location: history.location\n  }),\n      _useState6 = _slicedToArray(_useState5, 2),\n      state = _useState6[0],\n      setState = _useState6[1];\n\n  useLayoutEffect(function () {\n    return history.listen(setState);\n  }, [history]);\n  return /*#__PURE__*/createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n/**\n * The public API for rendering a history-aware <a>.\n */\n\n\nvar Link = /*#__PURE__*/forwardRef(function LinkWithRef(_ref4, ref) {\n  var onClick = _ref4.onClick,\n      reloadDocument = _ref4.reloadDocument,\n      _ref4$replace = _ref4.replace,\n      replace = _ref4$replace === void 0 ? false : _ref4$replace,\n      state = _ref4.state,\n      target = _ref4.target,\n      to = _ref4.to,\n      rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n\n  var href = useHref(to);\n  var internalOnClick = useLinkClickHandler(to, {\n    replace: replace,\n    state: state,\n    target: target\n  });\n\n  function handleClick(event) {\n    if (onClick) onClick(event);\n\n    if (!event.defaultPrevented && !reloadDocument) {\n      internalOnClick(event);\n    }\n  }\n\n  return (\n    /*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    createElement(\"a\", _extends({}, rest, {\n      href: href,\n      onClick: handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\n\n\nvar NavLink = /*#__PURE__*/forwardRef(function NavLinkWithRef(_ref5, ref) {\n  var _ref5$ariaCurrent = _ref5[\"aria-current\"],\n      ariaCurrentProp = _ref5$ariaCurrent === void 0 ? \"page\" : _ref5$ariaCurrent,\n      _ref5$caseSensitive = _ref5.caseSensitive,\n      caseSensitive = _ref5$caseSensitive === void 0 ? false : _ref5$caseSensitive,\n      _ref5$className = _ref5.className,\n      classNameProp = _ref5$className === void 0 ? \"\" : _ref5$className,\n      _ref5$end = _ref5.end,\n      end = _ref5$end === void 0 ? false : _ref5$end,\n      styleProp = _ref5.style,\n      to = _ref5.to,\n      children = _ref5.children,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n\n  var location = useLocation();\n  var path = useResolvedPath(to);\n  var locationPathname = location.pathname;\n  var toPathname = path.pathname;\n\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    toPathname = toPathname.toLowerCase();\n  }\n\n  var isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  var ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  var className;\n\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive: isActive\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null].filter(Boolean).join(\" \");\n  }\n\n  var style = typeof styleProp === \"function\" ? styleProp({\n    isActive: isActive\n  }) : styleProp;\n  return /*#__PURE__*/createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive: isActive\n  }) : children);\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n} ////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\n\n\nfunction useLinkClickHandler(to, _temp) {\n  var _ref6 = _temp === void 0 ? {} : _temp,\n      target = _ref6.target,\n      replaceProp = _ref6.replace,\n      state = _ref6.state;\n\n  var navigate = useNavigate();\n  var location = useLocation();\n  var path = useResolvedPath(to);\n  return useCallback(function (event) {\n    if (event.button === 0 && ( // Ignore everything but left clicks\n    !target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n    ) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here.\n\n      var replace = !!replaceProp || createPath(location) === createPath(path);\n      navigate(to, {\n        replace: replace,\n        state: state\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  var defaultSearchParamsRef = useRef(createSearchParams(defaultInit));\n  var location = useLocation();\n  var searchParams = useMemo(function () {\n    var searchParams = createSearchParams(location.search);\n\n    var _iterator = _createForOfIteratorHelper(defaultSearchParamsRef.current.keys()),\n        _step;\n\n    try {\n      var _loop = function _loop() {\n        var key = _step.value;\n\n        if (!searchParams.has(key)) {\n          defaultSearchParamsRef.current.getAll(key).forEach(function (value) {\n            searchParams.append(key, value);\n          });\n        }\n      };\n\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        _loop();\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n\n    return searchParams;\n  }, [location.search]);\n  var navigate = useNavigate();\n  var setSearchParams = useCallback(function (nextInit, navigateOptions) {\n    navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n  }, [navigate]);\n  return [searchParams, setSearchParams];\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\n\n\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce(function (memo, key) {\n    var value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(function (v) {\n      return [key, v];\n    }) : [[key, value]]);\n  }, []));\n}\n\nexport { BrowserRouter, HashRouter, Link, NavLink, createSearchParams, HistoryRouter as unstable_HistoryRouter, useLinkClickHandler, useSearchParams };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,SAASA,OAAT,CAAiBC,IAAjB,EAAgCC,OAAhC,EAAuD;EACrD,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAARD,CAAaD,OAAbC;;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIE,KAAJ,CAAUH,OAAV,CAAN,CANE;IAAJ,EAQE,OAAOI,CAAP,EAAU;EACb;AACF,C,CAAA;AAkFD;AACA;;AAQA;AACA;AACA;;;AACO,SAASC,aAAT,OAIgB;EAAA,IAHrBC,QAGqB,QAHrBA,QAGqB;EAAA,IAFrBC,QAEqB,QAFrBA,QAEqB;EAAA,IADrBC,MACqB,QADrBA,MACqB;EACrB,IAAIC,UAAU,GAAGC,QAAjB;;EACA,IAAID,UAAU,CAACE,OAAXF,IAAsB,IAA1B,EAAgC;IAC9BA,UAAU,CAACE,OAAXF,GAAqBG,oBAAoB,CAAC;MAAEJ;IAAF,CAAD,CAAzCC;EACD;;EAED,IAAII,OAAO,GAAGJ,UAAU,CAACE,OAAzB;;EACA,gBAAwBD,SAAe;IACrCI,MAAM,EAAED,OAAO,CAACC,MADqB;IAErCC,QAAQ,EAAEF,OAAO,CAACE;EAFmB,CAAf,CAAxB;EAAA;EAAA,IAAKC,KAAL;EAAA,IAAYC,QAAZ;;EAKAP,gBAAsB;IAAA,OAAMG,OAAO,CAACK,MAARL,CAAeI,QAAfJ,CAAN;EAAA,CAAtB,EAAsD,CAACA,OAAD,CAAtD;EAEA,oBACEM,cAACC,MAADD;IACEb,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEQ,QAAQ,EAAEC,KAAK,CAACD,QAHlB;IAIEM,cAAc,EAAEL,KAAK,CAACF,MAJxB;IAKEQ,SAAS,EAAET;EALb,EADF;AASD;AAQD;AACA;AACA;AACA;;;AACO,SAASU,UAAT,QAAqE;EAAA,IAA/CjB,QAA+C,SAA/CA,QAA+C;EAAA,IAArCC,QAAqC,SAArCA,QAAqC;EAAA,IAA3BC,MAA2B,SAA3BA,MAA2B;EAC1E,IAAIC,UAAU,GAAGC,QAAjB;;EACA,IAAID,UAAU,CAACE,OAAXF,IAAsB,IAA1B,EAAgC;IAC9BA,UAAU,CAACE,OAAXF,GAAqBe,iBAAiB,CAAC;MAAEhB;IAAF,CAAD,CAAtCC;EACD;;EAED,IAAII,OAAO,GAAGJ,UAAU,CAACE,OAAzB;;EACA,iBAAwBD,SAAe;IACrCI,MAAM,EAAED,OAAO,CAACC,MADqB;IAErCC,QAAQ,EAAEF,OAAO,CAACE;EAFmB,CAAf,CAAxB;EAAA;EAAA,IAAKC,KAAL;EAAA,IAAYC,QAAZ;;EAKAP,gBAAsB;IAAA,OAAMG,OAAO,CAACK,MAARL,CAAeI,QAAfJ,CAAN;EAAA,CAAtB,EAAsD,CAACA,OAAD,CAAtD;EAEA,oBACEM,cAACC,MAADD;IACEb,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEQ,QAAQ,EAAEC,KAAK,CAACD,QAHlB;IAIEM,cAAc,EAAEL,KAAK,CAACF,MAJxB;IAKEQ,SAAS,EAAET;EALb,EADF;AASD;AAQD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASY,aAAT,QAA4E;EAAA,IAAnDnB,QAAmD,SAAnDA,QAAmD;EAAA,IAAzCC,QAAyC,SAAzCA,QAAyC;EAAA,IAA/BM,OAA+B,SAA/BA,OAA+B;;EAC1E,iBAA0BH,SAAe;IACvCI,MAAM,EAAED,OAAO,CAACC,MADuB;IAEvCC,QAAQ,EAAEF,OAAO,CAACE;EAFqB,CAAf,CAA1B;EAAA;EAAA,IAAOC,KAAP;EAAA,IAAcC,QAAd;;EAKAP,gBAAsB;IAAA,OAAMG,OAAO,CAACK,MAARL,CAAeI,QAAfJ,CAAN;EAAA,CAAtB,EAAsD,CAACA,OAAD,CAAtD;EAEA,oBACEM,cAACC,MAADD;IACEb,QAAQ,EAAEA,QADZ;IAEEC,QAAQ,EAAEA,QAFZ;IAGEQ,QAAQ,EAAEC,KAAK,CAACD,QAHlB;IAIEM,cAAc,EAAEL,KAAK,CAACF,MAJxB;IAKEQ,SAAS,EAAET;EALb,EADF;AASD;;AAED,2CAAa;EACXY,aAAa,CAACC,WAAdD,GAA4B,wBAA5BA;AACD;;AAID,SAASE,eAAT,CAAyBC,KAAzB,EAAkD;EAChD,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAND,IAAiBA,KAAK,CAACE,MAAvBF,IAAiCA,KAAK,CAACG,OAAvCH,IAAkDA,KAAK,CAACI,QAA1D,CAAR;AACD;AAUD;AACA;AACA;;;IACaC,IAAI,gBAAGvB,WAClB,SAASwB,WAAT,QAEEC,GAFF,EAGE;EAAA,IAFEC,OAEF,SAFEA,OAEF;EAAA,IAFWC,cAEX,SAFWA,cAEX;EAAA,0BAF2BC,OAE3B;EAAA,IAF2BA,OAE3B,8BAFqC,KAErC;EAAA,IAF4CtB,KAE5C,SAF4CA,KAE5C;EAAA,IAFmDuB,MAEnD,SAFmDA,MAEnD;EAAA,IAF2DC,EAE3D,SAF2DA,EAE3D;EAAA,IAFkEC,IAElE;;EACA,IAAIC,IAAI,GAAGC,OAAO,CAACH,EAAD,CAAlB;EACA,IAAII,eAAe,GAAGC,mBAAmB,CAACL,EAAD,EAAK;IAAEF,OAAF,EAAEA,OAAF;IAAWtB,KAAX,EAAWA,KAAX;IAAkBuB;EAAlB,CAAL,CAAzC;;EACA,SAASO,WAAT,CACElB,KADF,EAEE;IACA,IAAIQ,OAAJ,EAAaA,OAAO,CAACR,KAAD,CAAPQ;;IACb,IAAI,CAACR,KAAK,CAACmB,gBAAP,IAA2B,CAACV,cAAhC,EAAgD;MAC9CO,eAAe,CAAChB,KAAD,CAAfgB;IACD;EACF;;EAED;IAAA;IACE;IACAI,gCACMP,IADN;MAEEC,IAAI,EAAEA,IAFR;MAGEN,OAAO,EAAEU,WAHX;MAIEX,GAAG,EAAEA,GAJP;MAKEI,MAAM,EAAEA;IALV;EAFF;AAhBgB;;AA6BpB,2CAAa;EACXN,IAAI,CAACP,WAALO,GAAmB,MAAnBA;AACD;AAeD;AACA;AACA;;;IACagB,OAAO,gBAAGvC,WACrB,SAASwC,cAAT,QAWEf,GAXF,EAYE;EAAA,8BAVE,cAUF;EAAA,IAVkBgB,eAUlB,kCAVoC,MAUpC;EAAA,gCATEC,aASF;EAAA,IATEA,aASF,oCATkB,KASlB;EAAA,4BAREC,SAQF;EAAA,IARaC,aAQb,gCAR6B,EAQ7B;EAAA,sBAPEC,GAOF;EAAA,IAPEA,GAOF,0BAPQ,KAOR;EAAA,IANSC,SAMT,SANEC,KAMF;EAAA,IALEjB,EAKF,SALEA,EAKF;EAAA,IAJEjC,QAIF,SAJEA,QAIF;EAAA,IAHKkC,IAGL;;EACA,IAAI1B,QAAQ,GAAG2C,WAAW,EAA1B;EACA,IAAIC,IAAI,GAAGC,eAAe,CAACpB,EAAD,CAA1B;EAEA,IAAIqB,gBAAgB,GAAG9C,QAAQ,CAAC+C,QAAhC;EACA,IAAIC,UAAU,GAAGJ,IAAI,CAACG,QAAtB;;EACA,IAAI,CAACV,aAAL,EAAoB;IAClBS,gBAAgB,GAAGA,gBAAgB,CAACG,WAAjBH,EAAnBA;IACAE,UAAU,GAAGA,UAAU,CAACC,WAAXD,EAAbA;EACD;;EAED,IAAIE,QAAQ,GACVJ,gBAAgB,KAAKE,UAArBF,IACC,CAACN,GAAD,IACCM,gBAAgB,CAACK,UAAjBL,CAA4BE,UAA5BF,CADD,IAECA,gBAAgB,CAACM,MAAjBN,CAAwBE,UAAU,CAACK,MAAnCP,MAA+C,GAJnD;EAMA,IAAIQ,WAAW,GAAGJ,QAAQ,GAAGd,eAAH,GAAqBmB,SAA/C;EAEA,IAAIjB,SAAJ;;EACA,IAAI,OAAOC,aAAP,KAAyB,UAA7B,EAAyC;IACvCD,SAAS,GAAGC,aAAa,CAAC;MAAEW;IAAF,CAAD,CAAzBZ;EADF,OAEO;IACL;IACA;IACA;IACA;IACA;IACAA,SAAS,GAAG,CAACC,aAAD,EAAgBW,QAAQ,GAAG,QAAH,GAAc,IAAtC,EACTM,MADS,CACFC,OADE,EAETC,IAFS,CAEJ,GAFI,CAAZpB;EAGD;;EAED,IAAII,KAAK,GACP,OAAOD,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAAC;IAAES;EAAF,CAAD,CAA3C,GAA4DT,SAD9D;EAGA,oBACErC,cAACc,IAADd,eACMsB,IADN;IAEE,gBAAc4B,WAFhB;IAGEhB,SAAS,EAAEA,SAHb;IAIElB,GAAG,EAAEA,GAJP;IAKEsB,KAAK,EAAEA,KALT;IAMEjB,EAAE,EAAEA;EANN,IAQG,OAAOjC,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAAC;IAAE0D;EAAF,CAAD,CAAzC,GAA0D1D,QAR7DY,CADF;AAjDmB;;AAgEvB,2CAAa;EACX8B,OAAO,CAACvB,WAARuB,GAAsB,SAAtBA;AACD,C,CAAA;AAGD;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AACO,SAASJ,mBAAT,CACLL,EADK,SAW6C;EAAA,+BAD9C,EAC8C;EAAA,IARhDD,MAQgD,SARhDA,MAQgD;EAAA,IAPvCmC,WAOuC,SAPhDpC,OAOgD;EAAA,IANhDtB,KAMgD,SANhDA,KAMgD;;EAClD,IAAI2D,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAI7D,QAAQ,GAAG2C,WAAW,EAA1B;EACA,IAAIC,IAAI,GAAGC,eAAe,CAACpB,EAAD,CAA1B;EAEA,OAAO9B,YACJkB,eAAD,EAA4C;IAC1C,IACEA,KAAK,CAACiD,MAANjD,KAAiB,CAAjBA;IACC,CAACW,MAAD,IAAWA,MAAM,KAAK,OADvBX;IAEA,CAACD,eAAe,CAACC,KAAD,CAHlB;IAAA,EAIE;MACAA,KAAK,CAACkD,cAANlD,GADA;MAIA;;MACA,IAAIU,OAAO,GACT,CAAC,CAACoC,WAAF,IAAiBK,UAAU,CAAChE,QAAD,CAAVgE,KAAyBA,UAAU,CAACpB,IAAD,CADtD;MAGAgB,QAAQ,CAACnC,EAAD,EAAK;QAAEF,OAAF,EAAEA,OAAF;QAAWtB;MAAX,CAAL,CAAR2D;IACD;EAfE,GAiBL,CAAC5D,QAAD,EAAW4D,QAAX,EAAqBhB,IAArB,EAA2Be,WAA3B,EAAwC1D,KAAxC,EAA+CuB,MAA/C,EAAuDC,EAAvD,CAjBK,CAAP;AAmBD;AAED;AACA;AACA;AACA;;;AACO,SAASwC,eAAT,CAAyBC,WAAzB,EAA4D;EACjEC,+CAAO,CACL,OAAOC,eAAP,KAA2B,WADtB,EAEL,meAFK,CAAP;EAYA,IAAIC,sBAAsB,GAAG1E,OAAa2E,kBAAkB,CAACJ,WAAD,CAA/B,CAA7B;EAEA,IAAIlE,QAAQ,GAAG2C,WAAW,EAA1B;EACA,IAAI4B,YAAY,GAAG5E,QAAc,YAAM;IACrC,IAAI4E,YAAY,GAAGD,kBAAkB,CAACtE,QAAQ,CAACwE,MAAV,CAArC;;IADqC,2CAGrBH,sBAAsB,CAACzE,OAAvByE,CAA+BI,IAA/BJ,EAHqB;IAAA;;IAAA;MAAA;QAAA,IAG5BK,GAH4B;;QAInC,IAAI,CAACH,YAAY,CAACI,GAAbJ,CAAiBG,GAAjBH,CAAL,EAA4B;UAC1BF,sBAAsB,CAACzE,OAAvByE,CAA+BO,MAA/BP,CAAsCK,GAAtCL,EAA2CQ,OAA3CR,CAAoDS,eAAD,EAAW;YAC5DP,YAAY,CAACQ,MAAbR,CAAoBG,GAApBH,EAAyBO,KAAzBP;UADF;QAGD;MARkC;;MAGrC,oDAAuD;QAAA;MAMtD;IAToC;MAAA;IAAA;MAAA;IAAA;;IAWrC,OAAOA,YAAP;EAXiB,GAYhB,CAACvE,QAAQ,CAACwE,MAAV,CAZgB,CAAnB;EAcA,IAAIZ,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAImB,eAAe,GAAGrF,YACpB,UACEsF,QADF,EAEEC,eAFF,EAGK;IACHtB,QAAQ,CAAC,MAAMU,kBAAkB,CAACW,QAAD,CAAzB,EAAqCC,eAArC,CAARtB;EALkB,GAOpB,CAACA,QAAD,CAPoB,CAAtB;EAUA,OAAO,CAACW,YAAD,EAAeS,eAAf,CAAP;AACD;AAUD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAASV,kBAAT,CACLa,IADK,EAEY;EAAA,IADjBA,IACiB;IADjBA,IACiB,GADW,EAA5BA;EACiB;;EACjB,OAAO,IAAIf,eAAJ,CACL,OAAOe,IAAP,KAAgB,QAAhB,IACAC,KAAK,CAACC,OAAND,CAAcD,IAAdC,CADA,IAEAD,IAAI,YAAYf,eAFhB,GAGIe,IAHJ,GAIIG,MAAM,CAACb,IAAPa,CAAYH,IAAZG,EAAkBC,MAAlBD,CAAyB,UAACE,IAAD,EAAOd,GAAP,EAAe;IACtC,IAAII,KAAK,GAAGK,IAAI,CAACT,GAAD,CAAhB;IACA,OAAOc,IAAI,CAACC,MAALD,CACLJ,KAAK,CAACC,OAAND,CAAcN,KAAdM,IAAuBN,KAAK,CAACY,GAANZ,CAAWa,WAAD;MAAA,OAAO,CAACjB,GAAD,EAAMiB,CAAN,CAAP;IAAA,CAAVb,CAAvBM,GAAoD,CAAC,CAACV,GAAD,EAAMI,KAAN,CAAD,CAD/CU,CAAP;EAFF,GAKG,EALHF,CALC,CAAP;AAYD", "names": ["warning", "cond", "message", "console", "warn", "Error", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basename", "children", "window", "historyRef", "React", "current", "createBrowserHistory", "history", "action", "location", "state", "setState", "listen", "React.createElement", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createHashHistory", "HistoryRouter", "displayName", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "Link", "LinkWithRef", "ref", "onClick", "reloadDocument", "replace", "target", "to", "rest", "href", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "createElement", "NavLink", "NavLinkWithRef", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "styleProp", "style", "useLocation", "path", "useResolvedPath", "locationPathname", "pathname", "toPathname", "toLowerCase", "isActive", "startsWith", "char<PERSON>t", "length", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "replaceProp", "navigate", "useNavigate", "button", "preventDefault", "createPath", "useSearchParams", "defaultInit", "process", "URLSearchParams", "defaultSearchParamsRef", "createSearchParams", "searchParams", "search", "keys", "key", "has", "getAll", "for<PERSON>ach", "value", "append", "setSearchParams", "nextInit", "navigateOptions", "init", "Array", "isArray", "Object", "reduce", "memo", "concat", "map", "v"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\packages\\react-router-dom\\index.tsx"], "sourcesContent": ["/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type { BrowserHistory, HashHistory, History } from \"history\";\nimport { createBrowserHistory, createHashHistory } from \"history\";\nimport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  resolvePath,\n  renderMatches,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n} from \"react-router\";\nimport type { To } from \"react-router\";\n\nfunction warning(cond: boolean, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// RE-EXPORTS\n////////////////////////////////////////////////////////////////////////////////\n\n// Note: Keep in sync with react-router exports!\nexport {\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createRoutesFromChildren,\n  generatePath,\n  matchRoutes,\n  matchPath,\n  createPath,\n  parsePath,\n  renderMatches,\n  resolvePath,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigate,\n  useNavigationType,\n  useOutlet,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useOutletContext,\n};\n\nexport { NavigationType } from \"react-router\";\nexport type {\n  Hash,\n  Location,\n  Path,\n  To,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigator,\n  OutletProps,\n  Params,\n  PathMatch,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  Pathname,\n  Search,\n  RoutesProps,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n} from \"react-router\";\n\n////////////////////////////////////////////////////////////////////////////////\n// COMPONENTS\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nfunction isModifiedEvent(event: React.MouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  to: To;\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    { onClick, reloadDocument, replace = false, state, target, to, ...rest },\n    ref\n  ) {\n    let href = useHref(to);\n    let internalOnClick = useLinkClickHandler(to, { replace, state, target });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented && !reloadDocument) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={href}\n        onClick={handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: { isActive: boolean }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: { isActive: boolean }) => React.CSSProperties);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let location = useLocation();\n    let path = useResolvedPath(to);\n\n    let locationPathname = location.pathname;\n    let toPathname = path.pathname;\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [classNameProp, isActive ? \"active\" : null]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp({ isActive }) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\" ? children({ isActive }) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// HOOKS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to);\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (\n        event.button === 0 && // Ignore everything but left clicks\n        (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n        !isModifiedEvent(event) // Ignore clicks with modifier keys\n      ) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here.\n        let replace =\n          !!replaceProp || createPath(location) === createPath(path);\n\n        navigate(to, { replace, state });\n      }\n    },\n    [location, navigate, path, replaceProp, state, target, to]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(defaultInit?: URLSearchParamsInit) {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n\n  let location = useLocation();\n  let searchParams = React.useMemo(() => {\n    let searchParams = createSearchParams(location.search);\n\n    for (let key of defaultSearchParamsRef.current.keys()) {\n      if (!searchParams.has(key)) {\n        defaultSearchParamsRef.current.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    }\n\n    return searchParams;\n  }, [location.search]);\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback(\n    (\n      nextInit: URLSearchParamsInit,\n      navigateOptions?: { replace?: boolean; state?: any }\n    ) => {\n      navigate(\"?\" + createSearchParams(nextInit), navigateOptions);\n    },\n    [navigate]\n  );\n\n  return [searchParams, setSearchParams] as const;\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n"]}, "metadata": {}, "sourceType": "module"}