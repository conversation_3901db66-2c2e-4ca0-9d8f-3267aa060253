{"ast": null, "code": "'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' || typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function') {\n    return;\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}", "map": {"version": 3, "names": ["checkDCE", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "process", "env", "NODE_ENV", "Error", "err", "console", "error", "module", "exports", "require"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/react-dom/index.js"], "sourcesContent": ["'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "mappings": "AAAA;;AAEA,SAASA,QAAT,GAAoB;EAClB;EACA,IACE,OAAOC,8BAAP,KAA0C,WAA1C,IACA,OAAOA,8BAA8B,CAACD,QAAtC,KAAmD,UAFrD,EAGE;IACA;EACD;;EACD,IAAIE,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,IAAIC,KAAJ,CAAU,KAAV,CAAN;EACD;;EACD,IAAI;IACF;IACAJ,8BAA8B,CAACD,QAA/B,CAAwCA,QAAxC;EACD,CAHD,CAGE,OAAOM,GAAP,EAAY;IACZ;IACA;IACAC,OAAO,CAACC,KAAR,CAAcF,GAAd;EACD;AACF;;AAED,IAAIJ,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzC;EACA;EACAJ,QAAQ;EACRS,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,mCAAD,CAAxB;AACD,CALD,MAKO;EACLF,MAAM,CAACC,OAAP,GAAiBC,OAAO,CAAC,gCAAD,CAAxB;AACD"}, "metadata": {}, "sourceType": "script"}