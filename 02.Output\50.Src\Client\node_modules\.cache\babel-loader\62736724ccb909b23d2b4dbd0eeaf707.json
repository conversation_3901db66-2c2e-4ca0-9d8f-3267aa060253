{"ast": null, "code": "/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport var BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00'\n};", "map": {"version": 3, "mappings": "AAAA;;;;;;;AAOA,OAAO,IAAMA,IAAI,GAAG;EAClB;EACAC,EAAE,EAAE,MAFc;EAGlB;EACAC,IAAI,EAAE;AAJY,CAAb", "names": ["BYTE", "LF", "NULL"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\byte.ts"], "sourcesContent": ["/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00',\n};\n"]}, "metadata": {}, "sourceType": "module"}