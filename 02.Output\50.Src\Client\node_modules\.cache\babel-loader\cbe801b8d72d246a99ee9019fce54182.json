{"ast": null, "code": "var listeners = {};\n\nvar trigger = function trigger(key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\n\nvar subscribe = function subscribe(key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\n\nexport { trigger, subscribe };", "map": {"version": 3, "names": ["listeners", "trigger", "key", "data", "for<PERSON>ach", "item", "subscribe", "listener", "push", "unsubscribe", "index", "indexOf", "splice"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js"], "sourcesContent": ["var listeners = {};\nvar trigger = function trigger(key, data) {\n  if (listeners[key]) {\n    listeners[key].forEach(function (item) {\n      return item(data);\n    });\n  }\n};\nvar subscribe = function subscribe(key, listener) {\n  if (!listeners[key]) {\n    listeners[key] = [];\n  }\n  listeners[key].push(listener);\n  return function unsubscribe() {\n    var index = listeners[key].indexOf(listener);\n    listeners[key].splice(index, 1);\n  };\n};\nexport { trigger, subscribe };"], "mappings": "AAAA,IAAIA,SAAS,GAAG,EAAhB;;AACA,IAAIC,OAAO,GAAG,SAASA,OAAT,CAAiBC,GAAjB,EAAsBC,IAAtB,EAA4B;EACxC,IAAIH,SAAS,CAACE,GAAD,CAAb,EAAoB;IAClBF,SAAS,CAACE,GAAD,CAAT,CAAeE,OAAf,CAAuB,UAAUC,IAAV,EAAgB;MACrC,OAAOA,IAAI,CAACF,IAAD,CAAX;IACD,CAFD;EAGD;AACF,CAND;;AAOA,IAAIG,SAAS,GAAG,SAASA,SAAT,CAAmBJ,GAAnB,EAAwBK,QAAxB,EAAkC;EAChD,IAAI,CAACP,SAAS,CAACE,GAAD,CAAd,EAAqB;IACnBF,SAAS,CAACE,GAAD,CAAT,GAAiB,EAAjB;EACD;;EACDF,SAAS,CAACE,GAAD,CAAT,CAAeM,IAAf,CAAoBD,QAApB;EACA,OAAO,SAASE,WAAT,GAAuB;IAC5B,IAAIC,KAAK,GAAGV,SAAS,CAACE,GAAD,CAAT,CAAeS,OAAf,CAAuBJ,QAAvB,CAAZ;IACAP,SAAS,CAACE,GAAD,CAAT,CAAeU,MAAf,CAAsBF,KAAtB,EAA6B,CAA7B;EACD,CAHD;AAID,CATD;;AAUA,SAAST,OAAT,EAAkBK,SAAlB"}, "metadata": {}, "sourceType": "module"}