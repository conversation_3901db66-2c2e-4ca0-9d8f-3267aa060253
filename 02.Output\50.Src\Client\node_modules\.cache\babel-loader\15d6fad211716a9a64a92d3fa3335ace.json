{"ast": null, "code": "var DataView = require('./_DataView'),\n    Map = require('./_Map'),\n    Promise = require('./_Promise'),\n    Set = require('./_Set'),\n    WeakMap = require('./_WeakMap'),\n    baseGetTag = require('./_baseGetTag'),\n    toSource = require('./_toSource');\n/** `Object#toString` result references. */\n\n\nvar mapTag = '[object Map]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    setTag = '[object Set]',\n    weakMapTag = '[object WeakMap]';\nvar dataViewTag = '[object DataView]';\n/** Used to detect maps, sets, and weakmaps. */\n\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\n\nvar getTag = baseGetTag; // Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\n\nif (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map && getTag(new Map()) != mapTag || Promise && getTag(Promise.resolve()) != promiseTag || Set && getTag(new Set()) != setTag || WeakMap && getTag(new WeakMap()) != weakMapTag) {\n  getTag = function getTag(value) {\n    var result = baseGetTag(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : '';\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString:\n          return dataViewTag;\n\n        case mapCtorString:\n          return mapTag;\n\n        case promiseCtorString:\n          return promiseTag;\n\n        case setCtorString:\n          return setTag;\n\n        case weakMapCtorString:\n          return weakMapTag;\n      }\n    }\n\n    return result;\n  };\n}\n\nmodule.exports = getTag;", "map": {"version": 3, "names": ["DataView", "require", "Map", "Promise", "Set", "WeakMap", "baseGetTag", "toSource", "mapTag", "objectTag", "promiseTag", "setTag", "weakMapTag", "dataViewTag", "dataViewCtorString", "mapCtorString", "promiseCtorString", "setCtorString", "weakMapCtorString", "getTag", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resolve", "value", "result", "Ctor", "constructor", "undefined", "ctorString", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getTag.js"], "sourcesContent": ["var DataView = require('./_DataView'),\n    Map = require('./_Map'),\n    Promise = require('./_Promise'),\n    Set = require('./_Set'),\n    WeakMap = require('./_WeakMap'),\n    baseGetTag = require('./_baseGetTag'),\n    toSource = require('./_toSource');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    setTag = '[object Set]',\n    weakMapTag = '[object WeakMap]';\n\nvar dataViewTag = '[object DataView]';\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\n    (Map && getTag(new Map) != mapTag) ||\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\n    (Set && getTag(new Set) != setTag) ||\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\n  getTag = function(value) {\n    var result = baseGetTag(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : '';\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString: return dataViewTag;\n        case mapCtorString: return mapTag;\n        case promiseCtorString: return promiseTag;\n        case setCtorString: return setTag;\n        case weakMapCtorString: return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\n\nmodule.exports = getTag;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAD,CAAtB;AAAA,IACIC,GAAG,GAAGD,OAAO,CAAC,QAAD,CADjB;AAAA,IAEIE,OAAO,GAAGF,OAAO,CAAC,YAAD,CAFrB;AAAA,IAGIG,GAAG,GAAGH,OAAO,CAAC,QAAD,CAHjB;AAAA,IAIII,OAAO,GAAGJ,OAAO,CAAC,YAAD,CAJrB;AAAA,IAKIK,UAAU,GAAGL,OAAO,CAAC,eAAD,CALxB;AAAA,IAMIM,QAAQ,GAAGN,OAAO,CAAC,aAAD,CANtB;AAQA;;;AACA,IAAIO,MAAM,GAAG,cAAb;AAAA,IACIC,SAAS,GAAG,iBADhB;AAAA,IAEIC,UAAU,GAAG,kBAFjB;AAAA,IAGIC,MAAM,GAAG,cAHb;AAAA,IAIIC,UAAU,GAAG,kBAJjB;AAMA,IAAIC,WAAW,GAAG,mBAAlB;AAEA;;AACA,IAAIC,kBAAkB,GAAGP,QAAQ,CAACP,QAAD,CAAjC;AAAA,IACIe,aAAa,GAAGR,QAAQ,CAACL,GAAD,CAD5B;AAAA,IAEIc,iBAAiB,GAAGT,QAAQ,CAACJ,OAAD,CAFhC;AAAA,IAGIc,aAAa,GAAGV,QAAQ,CAACH,GAAD,CAH5B;AAAA,IAIIc,iBAAiB,GAAGX,QAAQ,CAACF,OAAD,CAJhC;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIc,MAAM,GAAGb,UAAb,C,CAEA;;AACA,IAAKN,QAAQ,IAAImB,MAAM,CAAC,IAAInB,QAAJ,CAAa,IAAIoB,WAAJ,CAAgB,CAAhB,CAAb,CAAD,CAAN,IAA4CP,WAAzD,IACCX,GAAG,IAAIiB,MAAM,CAAC,IAAIjB,GAAJ,EAAD,CAAN,IAAmBM,MAD3B,IAECL,OAAO,IAAIgB,MAAM,CAAChB,OAAO,CAACkB,OAAR,EAAD,CAAN,IAA6BX,UAFzC,IAGCN,GAAG,IAAIe,MAAM,CAAC,IAAIf,GAAJ,EAAD,CAAN,IAAmBO,MAH3B,IAICN,OAAO,IAAIc,MAAM,CAAC,IAAId,OAAJ,EAAD,CAAN,IAAuBO,UAJvC,EAIoD;EAClDO,MAAM,GAAG,gBAASG,KAAT,EAAgB;IACvB,IAAIC,MAAM,GAAGjB,UAAU,CAACgB,KAAD,CAAvB;IAAA,IACIE,IAAI,GAAGD,MAAM,IAAId,SAAV,GAAsBa,KAAK,CAACG,WAA5B,GAA0CC,SADrD;IAAA,IAEIC,UAAU,GAAGH,IAAI,GAAGjB,QAAQ,CAACiB,IAAD,CAAX,GAAoB,EAFzC;;IAIA,IAAIG,UAAJ,EAAgB;MACd,QAAQA,UAAR;QACE,KAAKb,kBAAL;UAAyB,OAAOD,WAAP;;QACzB,KAAKE,aAAL;UAAoB,OAAOP,MAAP;;QACpB,KAAKQ,iBAAL;UAAwB,OAAON,UAAP;;QACxB,KAAKO,aAAL;UAAoB,OAAON,MAAP;;QACpB,KAAKO,iBAAL;UAAwB,OAAON,UAAP;MAL1B;IAOD;;IACD,OAAOW,MAAP;EACD,CAfD;AAgBD;;AAEDK,MAAM,CAACC,OAAP,GAAiBV,MAAjB"}, "metadata": {}, "sourceType": "script"}