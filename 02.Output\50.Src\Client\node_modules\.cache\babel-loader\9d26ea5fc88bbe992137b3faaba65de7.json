{"ast": null, "code": "import _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport _assertThisInitialized from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\";\nimport _get from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/get.js\";\nimport _getPrototypeOf from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js\";\nimport _inherits from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/inherits.js\";\nimport _createSuper from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createSuper.js\";\nimport { Client } from '../client';\nimport { HeartbeatInfo } from './heartbeat-info';\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n */\n\nexport var CompatClient = /*#__PURE__*/function (_Client) {\n  _inherits(CompatClient, _Client);\n\n  var _super = _createSuper(CompatClient);\n\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  function CompatClient(webSocketFactory) {\n    var _this;\n\n    _classCallCheck(this, CompatClient);\n\n    _this = _super.call(this);\n    /**\n     * It is no op now. No longer needed. Large packets work out of the box.\n     */\n\n    _this.maxWebSocketFrameSize = 16 * 1024;\n    _this._heartbeatInfo = new HeartbeatInfo(_assertThisInitialized(_this));\n    _this.reconnect_delay = 0;\n    _this.webSocketFactory = webSocketFactory; // Default from previous version\n\n    _this.debug = function () {\n      var _console;\n\n      (_console = console).log.apply(_console, arguments);\n    };\n\n    return _this;\n  }\n\n  _createClass(CompatClient, [{\n    key: \"_parseConnect\",\n    value: function _parseConnect() {\n      var closeEventCallback;\n      var connectCallback;\n      var errorCallback;\n      var headers = {};\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      if (args.length < 2) {\n        throw new Error('Connect requires at least 2 arguments');\n      }\n\n      if (typeof args[1] === 'function') {\n        headers = args[0];\n        connectCallback = args[1];\n        errorCallback = args[2];\n        closeEventCallback = args[3];\n      } else {\n        switch (args.length) {\n          case 6:\n            headers.login = args[0];\n            headers.passcode = args[1];\n            connectCallback = args[2];\n            errorCallback = args[3];\n            closeEventCallback = args[4];\n            headers.host = args[5];\n            break;\n\n          default:\n            headers.login = args[0];\n            headers.passcode = args[1];\n            connectCallback = args[2];\n            errorCallback = args[3];\n            closeEventCallback = args[4];\n        }\n      }\n\n      return [headers, connectCallback, errorCallback, closeEventCallback];\n    }\n    /**\n     * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n     *\n     * **Deprecated**\n     *\n     * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n     * version with headers to pass your broker specific options.\n     *\n     * overloads:\n     * - connect(headers, connectCallback)\n     * - connect(headers, connectCallback, errorCallback)\n     * - connect(login, passcode, connectCallback)\n     * - connect(login, passcode, connectCallback, errorCallback)\n     * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n     * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n     *\n     * params:\n     * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n     * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n     * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n     * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n     * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n\n  }, {\n    key: \"connect\",\n    value: function connect() {\n      var out = this._parseConnect.apply(this, arguments);\n\n      if (out[0]) {\n        this.connectHeaders = out[0];\n      }\n\n      if (out[1]) {\n        this.onConnect = out[1];\n      }\n\n      if (out[2]) {\n        this.onStompError = out[2];\n      }\n\n      if (out[3]) {\n        this.onWebSocketClose = out[3];\n      }\n\n      _get(_getPrototypeOf(CompatClient.prototype), \"activate\", this).call(this);\n    }\n    /**\n     * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n     *\n     * **Deprecated**\n     *\n     * See:\n     * [Client#onDisconnect]{@link Client#onDisconnect}, and\n     * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n\n  }, {\n    key: \"disconnect\",\n    value: function disconnect(disconnectCallback) {\n      var headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n      if (disconnectCallback) {\n        this.onDisconnect = disconnectCallback;\n      }\n\n      this.disconnectHeaders = headers;\n\n      _get(_getPrototypeOf(CompatClient.prototype), \"deactivate\", this).call(this);\n    }\n    /**\n     * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n     *\n     * Send a message to a named destination. Refer to your STOMP broker documentation for types\n     * and naming of destinations. The headers will, typically, be available to the subscriber.\n     * However, there may be special purpose headers corresponding to your STOMP broker.\n     *\n     *  **Deprecated**, use [Client#publish]{@link Client#publish}\n     *\n     * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n     *\n     * ```javascript\n     *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n     *\n     *        // If you want to send a message with a body, you must also pass the headers argument.\n     *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n     * ```\n     *\n     * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n     */\n\n  }, {\n    key: \"send\",\n    value: function send(destination) {\n      var headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var body = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n      headers = Object.assign({}, headers);\n      var skipContentLengthHeader = headers['content-length'] === false;\n\n      if (skipContentLengthHeader) {\n        delete headers['content-length'];\n      }\n\n      this.publish({\n        destination: destination,\n        headers: headers,\n        body: body,\n        skipContentLengthHeader: skipContentLengthHeader\n      });\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"reconnect_delay\",\n    set: function set(value) {\n      this.reconnectDelay = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"ws\",\n    get: function get() {\n      return this.webSocket;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"version\",\n    get: function get() {\n      return this.connectedVersion;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"onreceive\",\n    get: function get() {\n      return this.onUnhandledMessage;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n     *\n     * **Deprecated**\n     */\n    ,\n    set: function set(value) {\n      this.onUnhandledMessage = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n     * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"onreceipt\",\n    get: function get() {\n      return this.onUnhandledReceipt;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n     *\n     * **Deprecated**\n     */\n    ,\n    set: function set(value) {\n      this.onUnhandledReceipt = value;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n     * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n     *\n     * **Deprecated**\n     */\n\n  }, {\n    key: \"heartbeat\",\n    get: function get() {\n      return this._heartbeatInfo;\n    }\n    /**\n     * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n     * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n     *\n     * **Deprecated**\n     */\n    ,\n    set: function set(value) {\n      this.heartbeatIncoming = value.incoming;\n      this.heartbeatOutgoing = value.outgoing;\n    }\n  }]);\n\n  return CompatClient;\n}(Client);", "map": {"version": 3, "mappings": ";;;;;;;AAAA,SAASA,MAAT,QAAuB,WAAvB;AAGA,SAASC,aAAT,QAA8B,kBAA9B;AAEA;;;;;;;;;;AASA,WAAaC,YAAb;EAAA;;EAAA;;EAME;;;;;;;;EAQA,sBAAYC,gBAAZ,EAAuC;IAAA;;IAAA;;IACrC;IAdF;;;;IAGO,8BAAgC,KAAK,IAArC;IAoOC,uBAAgC,IAAIF,aAAJ,+BAAhC;IAxNN,MAAKG,eAAL,GAAuB,CAAvB;IACA,MAAKD,gBAAL,GAAwBA,gBAAxB,CAHqC,CAIrC;;IACA,MAAKE,KAAL,GAAa,YAAsB;MAAA;;MACjC,mBAAO,EAACC,GAAR;IACD,CAFD;;IALqC;EAQtC;;EAtBH;IAAA;IAAA,OAwBU,yBAA4B;MAClC,IAAIC,kBAAJ;MACA,IAAIC,eAAJ;MACA,IAAIC,aAAJ;MACA,IAAIC,OAAO,GAAiB,EAA5B;;MAJkC,kCAAXC,IAAW;QAAXA,IAAW;MAAA;;MAKlC,IAAIA,IAAI,CAACC,MAAL,GAAc,CAAlB,EAAqB;QACnB,MAAM,IAAIC,KAAJ,CAAU,uCAAV,CAAN;MACD;;MACD,IAAI,OAAOF,IAAI,CAAC,CAAD,CAAX,KAAmB,UAAvB,EAAmC;QAChCD,OADgC,GAC+BC,IAD/B;QACvBH,eADuB,GAC+BG,IAD/B;QACNF,aADM,GAC+BE,IAD/B;QACSJ,kBADT,GAC+BI,IAD/B;MAElC,CAFD,MAEO;QACL,QAAQA,IAAI,CAACC,MAAb;UACE,KAAK,CAAL;YAEIF,OAAO,CAACI,KAFZ,GAQMH,IARN;YAGID,OAAO,CAACK,QAHZ,GAQMJ,IARN;YAIIH,eAJJ,GAQMG,IARN;YAKIF,aALJ,GAQME,IARN;YAMIJ,kBANJ,GAQMI,IARN;YAOID,OAAO,CAACM,IAPZ,GAQML,IARN;YASE;;UACF;YAEID,OAAO,CAACI,KAFZ,GAOMH,IAPN;YAGID,OAAO,CAACK,QAHZ,GAOMJ,IAPN;YAIIH,eAJJ,GAOMG,IAPN;YAKIF,aALJ,GAOME,IAPN;YAMIJ,kBANJ,GAOMI,IAPN;QAXF;MAoBD;;MAED,OAAO,CAACD,OAAD,EAAUF,eAAV,EAA2BC,aAA3B,EAA0CF,kBAA1C,CAAP;IACD;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA5DF;IAAA;IAAA,OAuFS,mBAAsB;MAC3B,IAAMU,GAAG,GAAG,KAAKC,aAAL,uBAAZ;;MAEA,IAAID,GAAG,CAAC,CAAD,CAAP,EAAY;QACV,KAAKE,cAAL,GAAsBF,GAAG,CAAC,CAAD,CAAzB;MACD;;MACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;QACV,KAAKG,SAAL,GAAiBH,GAAG,CAAC,CAAD,CAApB;MACD;;MACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;QACV,KAAKI,YAAL,GAAoBJ,GAAG,CAAC,CAAD,CAAvB;MACD;;MACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;QACV,KAAKK,gBAAL,GAAwBL,GAAG,CAAC,CAAD,CAA3B;MACD;;MAED;IACD;IAED;;;;;;;;;;;;EA1GF;IAAA;IAAA,OAqHS,oBACLM,kBADK,EAEqB;MAAA,IAA1Bb,OAA0B,uEAAF,EAAE;;MAE1B,IAAIa,kBAAJ,EAAwB;QACtB,KAAKC,YAAL,GAAoBD,kBAApB;MACD;;MACD,KAAKE,iBAAL,GAAyBf,OAAzB;;MAEA;IACD;IAED;;;;;;;;;;;;;;;;;;;;;EAjIF;IAAA;IAAA,OAqJS,cACLgB,WADK,EAGY;MAAA,IADjBhB,OACiB,uEADiB,EACjB;MAAA,IAAjBiB,IAAiB,uEAAF,EAAE;MAEjBjB,OAAO,GAAIkB,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BnB,OAA1B,CAAX;MAEA,IAAMoB,uBAAuB,GAAGpB,OAAO,CAAC,gBAAD,CAAP,KAA8B,KAA9D;;MACA,IAAIoB,uBAAJ,EAA6B;QAC3B,OAAOpB,OAAO,CAAC,gBAAD,CAAd;MACD;;MACD,KAAKqB,OAAL,CAAa;QACXL,WAAW,EAAXA,WADW;QAEXhB,OAAO,EAAEA,OAFE;QAGXiB,IAAI,EAAJA,IAHW;QAIXG,uBAAuB,EAAvBA;MAJW,CAAb;IAMD;IAED;;;;;;EAxKF;IAAA;IAAA,KA6KE,aAAoBE,KAApB,EAAiC;MAC/B,KAAKC,cAAL,GAAsBD,KAAtB;IACD;IAED;;;;;;EAjLF;IAAA;IAAA,KAsLE,eAAM;MACJ,OAAO,KAAKE,SAAZ;IACD;IAED;;;;;;EA1LF;IAAA;IAAA,KA+LE,eAAW;MACT,OAAO,KAAKC,gBAAZ;IACD;IAED;;;;;;EAnMF;IAAA;IAAA,KAwME,eAAa;MACX,OAAO,KAAKC,kBAAZ;IACD;IAED;;;;;IA5MF;IAAA,KAiNE,aAAcJ,KAAd,EAAwC;MACtC,KAAKI,kBAAL,GAA0BJ,KAA1B;IACD;IAED;;;;;;;EArNF;IAAA;IAAA,KA2NE,eAAa;MACX,OAAO,KAAKK,kBAAZ;IACD;IAED;;;;;IA/NF;IAAA,KAoOE,aAAcL,KAAd,EAAsC;MACpC,KAAKK,kBAAL,GAA0BL,KAA1B;IACD;IAID;;;;;;;EA1OF;IAAA;IAAA,KAgPE,eAAa;MACX,OAAO,KAAKM,cAAZ;IACD;IAED;;;;;;IApPF;IAAA,KA0PE,aAAcN,KAAd,EAA2D;MACzD,KAAKO,iBAAL,GAAyBP,KAAK,CAACQ,QAA/B;MACA,KAAKC,iBAAL,GAAyBT,KAAK,CAACU,QAA/B;IACD;EA7PH;;EAAA;AAAA,EAAkC1C,MAAlC", "names": ["Client", "HeartbeatInfo", "CompatClient", "webSocketFactory", "reconnect_delay", "debug", "log", "closeEventCallback", "connectCallback", "<PERSON><PERSON><PERSON><PERSON>", "headers", "args", "length", "Error", "login", "passcode", "host", "out", "_parseConnect", "connectHeaders", "onConnect", "onStompError", "onWebSocketClose", "disconnectCallback", "onDisconnect", "disconnectHeaders", "destination", "body", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publish", "value", "reconnectDelay", "webSocket", "connectedVersion", "onUnhandledMessage", "onUnhandledReceipt", "_heartbeatInfo", "heartbeatIncoming", "incoming", "heartbeatOutgoing", "outgoing"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\compatibility\\compat-client.ts"], "sourcesContent": ["import { Client } from '../client';\nimport { StompHeaders } from '../stomp-headers';\nimport { frameCallbackType, messageCallbackType } from '../types';\nimport { HeartbeatInfo } from './heartbeat-info';\n\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n */\nexport class CompatClient extends Client {\n  /**\n   * It is no op now. No longer needed. Large packets work out of the box.\n   */\n  public maxWebSocketFrameSize: number = 16 * 1024;\n\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory: () => any) {\n    super();\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory;\n    // Default from previous version\n    this.debug = (...message: any[]) => {\n      console.log(...message);\n    };\n  }\n\n  private _parseConnect(...args: any[]): any {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers: StompHeaders = {};\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n            headers.host,\n          ] = args;\n          break;\n        default:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n          ] = args;\n      }\n    }\n\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public connect(...args: any[]): void {\n    const out = this._parseConnect(...args);\n\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n\n    super.activate();\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public disconnect(\n    disconnectCallback?: any,\n    headers: StompHeaders = {}\n  ): void {\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n    this.disconnectHeaders = headers;\n\n    super.deactivate();\n  }\n\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public send(\n    destination: string,\n    headers: { [key: string]: any } = {},\n    body: string = ''\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    const skipContentLengthHeader = headers['content-length'] === false;\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n    this.publish({\n      destination,\n      headers: headers as StompHeaders,\n      body,\n      skipContentLengthHeader,\n    });\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n  set reconnect_delay(value: number) {\n    this.reconnectDelay = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n  get ws(): any {\n    return this.webSocket;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n  get version() {\n    return this.connectedVersion;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  get onreceive(): messageCallbackType {\n    return this.onUnhandledMessage;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  set onreceive(value: messageCallbackType) {\n    this.onUnhandledMessage = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n  get onreceipt(): frameCallbackType {\n    return this.onUnhandledReceipt;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n  set onreceipt(value: frameCallbackType) {\n    this.onUnhandledReceipt = value;\n  }\n\n  private _heartbeatInfo: HeartbeatInfo = new HeartbeatInfo(this);\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  set heartbeat(value: { incoming: number; outgoing: number }) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}