{"ast": null, "code": "var reportWebVitals=function reportWebVitals(onPerfEntry){if(onPerfEntry&&onPerfEntry instanceof Function){import('web-vitals').then(function(_ref){var getCLS=_ref.getCLS,getFID=_ref.getFID,getFCP=_ref.getFCP,getLCP=_ref.getLCP,getTTFB=_ref.getTTFB;getCLS(onPerfEntry);getFID(onPerfEntry);getFCP(onPerfEntry);getLCP(onPerfEntry);getTTFB(onPerfEntry);});}};export default reportWebVitals;", "map": {"version": 3, "names": ["reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/reportWebVitals.js"], "sourcesContent": ["const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n"], "mappings": "AAAA,GAAMA,gBAAe,CAAG,QAAlBA,gBAAkB,CAAAC,WAAW,CAAI,CACrC,GAAIA,WAAW,EAAIA,WAAW,WAAYC,SAA1C,CAAoD,CAClD,OAAO,YAAP,EAAqBC,IAArB,CAA0B,cAAiD,IAA9CC,OAA8C,MAA9CA,MAA8C,CAAtCC,MAAsC,MAAtCA,MAAsC,CAA9BC,MAA8B,MAA9BA,MAA8B,CAAtBC,MAAsB,MAAtBA,MAAsB,CAAdC,OAAc,MAAdA,OAAc,CACzEJ,MAAM,CAACH,WAAD,CAAN,CACAI,MAAM,CAACJ,WAAD,CAAN,CACAK,MAAM,CAACL,WAAD,CAAN,CACAM,MAAM,CAACN,WAAD,CAAN,CACAO,OAAO,CAACP,WAAD,CAAP,CACD,CAND,EAOD,CACF,CAVD,CAYA,cAAeD,gBAAf"}, "metadata": {}, "sourceType": "module"}