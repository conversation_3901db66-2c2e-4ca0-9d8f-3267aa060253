{"ast": null, "code": "var baseIsNative = require('./_baseIsNative'),\n    getValue = require('./_getValue');\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\n\n\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nmodule.exports = getNative;", "map": {"version": 3, "names": ["baseIsNative", "require", "getValue", "getNative", "object", "key", "value", "undefined", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getNative.js"], "sourcesContent": ["var baseIsNative = require('./_baseIsNative'),\n    getValue = require('./_getValue');\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nmodule.exports = getNative;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,aAAD,CADtB;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,SAAT,CAAmBC,MAAnB,EAA2BC,GAA3B,EAAgC;EAC9B,IAAIC,KAAK,GAAGJ,QAAQ,CAACE,MAAD,EAASC,GAAT,CAApB;EACA,OAAOL,YAAY,CAACM,KAAD,CAAZ,GAAsBA,KAAtB,GAA8BC,SAArC;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBN,SAAjB"}, "metadata": {}, "sourceType": "script"}