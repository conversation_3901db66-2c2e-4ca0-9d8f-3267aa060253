{"ast": null, "code": "import platform from './node/index.js';\nexport { platform as default };", "map": {"version": 3, "names": ["platform", "default"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\n\nexport {platform as default}\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,iBAArB;AAEA,SAAQA,QAAQ,IAAIC,OAApB"}, "metadata": {}, "sourceType": "module"}