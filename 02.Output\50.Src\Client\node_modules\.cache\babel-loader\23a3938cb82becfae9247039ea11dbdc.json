{"ast": null, "code": "'use strict';\n\nimport { VERSION } from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\nvar validators = {}; // eslint-disable-next-line func-names\n\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function (type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\nvar deprecatedWarnings = {};\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\n\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  } // eslint-disable-next-line func-names\n\n\n  return function (value, opt, opts) {\n    if (validator === false) {\n      throw new AxiosError(formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')), AxiosError.ERR_DEPRECATED);\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true; // eslint-disable-next-line no-console\n\n      console.warn(formatMessage(opt, ' has been deprecated since v' + version + ' and will be removed in the near future'));\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n\n  var keys = Object.keys(options);\n  var i = keys.length;\n\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n\n      continue;\n    }\n\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nexport default {\n  assertOptions: assertOptions,\n  validators: validators\n};", "map": {"version": 3, "names": ["VERSION", "AxiosError", "validators", "for<PERSON>ach", "type", "i", "validator", "thing", "deprecatedWarnings", "transitional", "version", "message", "formatMessage", "opt", "desc", "value", "opts", "ERR_DEPRECATED", "console", "warn", "assertOptions", "options", "schema", "allowUnknown", "ERR_BAD_OPTION_VALUE", "keys", "Object", "length", "result", "undefined", "ERR_BAD_OPTION"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/validator.js"], "sourcesContent": ["'use strict';\n\nimport {VERSION} from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\n\nconst validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach((type, i) => {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nconst deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return (value, opt, opts) => {\n    if (validator === false) {\n      throw new AxiosError(\n        formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')),\n        AxiosError.ERR_DEPRECATED\n      );\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  const keys = Object.keys(options);\n  let i = keys.length;\n  while (i-- > 0) {\n    const opt = keys[i];\n    const validator = schema[opt];\n    if (validator) {\n      const value = options[opt];\n      const result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nexport default {\n  assertOptions,\n  validators\n};\n"], "mappings": "AAAA;;AAEA,SAAQA,OAAR,QAAsB,gBAAtB;AACA,OAAOC,UAAP,MAAuB,uBAAvB;AAEA,IAAMC,UAAU,GAAG,EAAnB,C,CAEA;;AACA,CAAC,QAAD,EAAW,SAAX,EAAsB,QAAtB,EAAgC,UAAhC,EAA4C,QAA5C,EAAsD,QAAtD,EAAgEC,OAAhE,CAAwE,UAACC,IAAD,EAAOC,CAAP,EAAa;EACnFH,UAAU,CAACE,IAAD,CAAV,GAAmB,SAASE,SAAT,CAAmBC,KAAnB,EAA0B;IAC3C,OAAO,OAAOA,KAAP,KAAiBH,IAAjB,IAAyB,OAAOC,CAAC,GAAG,CAAJ,GAAQ,IAAR,GAAe,GAAtB,IAA6BD,IAA7D;EACD,CAFD;AAGD,CAJD;AAMA,IAAMI,kBAAkB,GAAG,EAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAN,UAAU,CAACO,YAAX,GAA0B,SAASA,YAAT,CAAsBH,SAAtB,EAAiCI,OAAjC,EAA0CC,OAA1C,EAAmD;EAC3E,SAASC,aAAT,CAAuBC,GAAvB,EAA4BC,IAA5B,EAAkC;IAChC,OAAO,aAAad,OAAb,GAAuB,0BAAvB,GAAoDa,GAApD,GAA0D,IAA1D,GAAiEC,IAAjE,IAAyEH,OAAO,GAAG,OAAOA,OAAV,GAAoB,EAApG,CAAP;EACD,CAH0E,CAK3E;;;EACA,OAAO,UAACI,KAAD,EAAQF,GAAR,EAAaG,IAAb,EAAsB;IAC3B,IAAIV,SAAS,KAAK,KAAlB,EAAyB;MACvB,MAAM,IAAIL,UAAJ,CACJW,aAAa,CAACC,GAAD,EAAM,uBAAuBH,OAAO,GAAG,SAASA,OAAZ,GAAsB,EAApD,CAAN,CADT,EAEJT,UAAU,CAACgB,cAFP,CAAN;IAID;;IAED,IAAIP,OAAO,IAAI,CAACF,kBAAkB,CAACK,GAAD,CAAlC,EAAyC;MACvCL,kBAAkB,CAACK,GAAD,CAAlB,GAA0B,IAA1B,CADuC,CAEvC;;MACAK,OAAO,CAACC,IAAR,CACEP,aAAa,CACXC,GADW,EAEX,iCAAiCH,OAAjC,GAA2C,yCAFhC,CADf;IAMD;;IAED,OAAOJ,SAAS,GAAGA,SAAS,CAACS,KAAD,EAAQF,GAAR,EAAaG,IAAb,CAAZ,GAAiC,IAAjD;EACD,CApBD;AAqBD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASI,aAAT,CAAuBC,OAAvB,EAAgCC,MAAhC,EAAwCC,YAAxC,EAAsD;EACpD,IAAI,OAAOF,OAAP,KAAmB,QAAvB,EAAiC;IAC/B,MAAM,IAAIpB,UAAJ,CAAe,2BAAf,EAA4CA,UAAU,CAACuB,oBAAvD,CAAN;EACD;;EACD,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYJ,OAAZ,CAAb;EACA,IAAIhB,CAAC,GAAGoB,IAAI,CAACE,MAAb;;EACA,OAAOtB,CAAC,KAAK,CAAb,EAAgB;IACd,IAAMQ,GAAG,GAAGY,IAAI,CAACpB,CAAD,CAAhB;IACA,IAAMC,SAAS,GAAGgB,MAAM,CAACT,GAAD,CAAxB;;IACA,IAAIP,SAAJ,EAAe;MACb,IAAMS,KAAK,GAAGM,OAAO,CAACR,GAAD,CAArB;MACA,IAAMe,MAAM,GAAGb,KAAK,KAAKc,SAAV,IAAuBvB,SAAS,CAACS,KAAD,EAAQF,GAAR,EAAaQ,OAAb,CAA/C;;MACA,IAAIO,MAAM,KAAK,IAAf,EAAqB;QACnB,MAAM,IAAI3B,UAAJ,CAAe,YAAYY,GAAZ,GAAkB,WAAlB,GAAgCe,MAA/C,EAAuD3B,UAAU,CAACuB,oBAAlE,CAAN;MACD;;MACD;IACD;;IACD,IAAID,YAAY,KAAK,IAArB,EAA2B;MACzB,MAAM,IAAItB,UAAJ,CAAe,oBAAoBY,GAAnC,EAAwCZ,UAAU,CAAC6B,cAAnD,CAAN;IACD;EACF;AACF;;AAED,eAAe;EACbV,aAAa,EAAbA,aADa;EAEblB,UAAU,EAAVA;AAFa,CAAf"}, "metadata": {}, "sourceType": "module"}