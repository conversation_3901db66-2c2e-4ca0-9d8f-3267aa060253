{"ast": null, "code": "import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n\n  if (!target) {\n    return defaultElement;\n  }\n\n  var targetElement;\n\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n\n  return targetElement;\n}", "map": {"version": 3, "names": ["isFunction", "<PERSON><PERSON><PERSON><PERSON>", "getTargetElement", "target", "defaultElement", "undefined", "targetElement", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/domTarget.js"], "sourcesContent": ["import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}"], "mappings": "AAAA,SAASA,UAAT,QAA2B,SAA3B;AACA,OAAOC,SAAP,MAAsB,aAAtB;AACA,OAAO,SAASC,gBAAT,CAA0BC,MAA1B,EAAkCC,cAAlC,EAAkD;EACvD,IAAI,CAACH,SAAL,EAAgB;IACd,OAAOI,SAAP;EACD;;EACD,IAAI,CAACF,MAAL,EAAa;IACX,OAAOC,cAAP;EACD;;EACD,IAAIE,aAAJ;;EACA,IAAIN,UAAU,CAACG,MAAD,CAAd,EAAwB;IACtBG,aAAa,GAAGH,MAAM,EAAtB;EACD,CAFD,MAEO,IAAI,aAAaA,MAAjB,EAAyB;IAC9BG,aAAa,GAAGH,MAAM,CAACI,OAAvB;EACD,CAFM,MAEA;IACLD,aAAa,GAAGH,MAAhB;EACD;;EACD,OAAOG,aAAP;AACD"}, "metadata": {}, "sourceType": "module"}