{"ast": null, "code": "var Symbol = require('./_Symbol'),\n    Uint8Array = require('./_Uint8Array'),\n    eq = require('./eq'),\n    equalArrays = require('./_equalArrays'),\n    mapToArray = require('./_mapToArray'),\n    setToArray = require('./_setToArray');\n/** Used to compose bitmasks for value comparisons. */\n\n\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n/** `Object#toString` result references. */\n\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]';\n/** Used to convert symbols to primitives and strings. */\n\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\n\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {\n        return false;\n      }\n\n      object = object.buffer;\n      other = other.buffer;\n\n    case arrayBufferTag:\n      if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n\n      return true;\n\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == other + '';\n\n    case mapTag:\n      var convert = mapToArray;\n\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n\n      if (object.size != other.size && !isPartial) {\n        return false;\n      } // Assume cyclic values are equal.\n\n\n      var stacked = stack.get(object);\n\n      if (stacked) {\n        return stacked == other;\n      }\n\n      bitmask |= COMPARE_UNORDERED_FLAG; // Recursively compare objects (susceptible to call stack limits).\n\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n\n  }\n\n  return false;\n}\n\nmodule.exports = equalByTag;", "map": {"version": 3, "names": ["Symbol", "require", "Uint8Array", "eq", "equalArrays", "mapToArray", "setToArray", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "boolTag", "dateTag", "errorTag", "mapTag", "numberTag", "regexpTag", "setTag", "stringTag", "symbolTag", "arrayBufferTag", "dataViewTag", "symbol<PERSON>roto", "prototype", "undefined", "symbolValueOf", "valueOf", "equalByTag", "object", "other", "tag", "bitmask", "customizer", "equalFunc", "stack", "byteLength", "byteOffset", "buffer", "name", "message", "convert", "isPartial", "size", "stacked", "get", "set", "result", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_equalByTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol'),\n    Uint8Array = require('./_Uint8Array'),\n    eq = require('./eq'),\n    equalArrays = require('./_equalArrays'),\n    mapToArray = require('./_mapToArray'),\n    setToArray = require('./_setToArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if ((object.byteLength != other.byteLength) ||\n          (object.byteOffset != other.byteOffset)) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n\n    case arrayBufferTag:\n      if ((object.byteLength != other.byteLength) ||\n          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n      return true;\n\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == (other + '');\n\n    case mapTag:\n      var convert = mapToArray;\n\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      // Assume cyclic values are equal.\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG;\n\n      // Recursively compare objects (susceptible to call stack limits).\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\n\nmodule.exports = equalByTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAD,CAApB;AAAA,IACIC,UAAU,GAAGD,OAAO,CAAC,eAAD,CADxB;AAAA,IAEIE,EAAE,GAAGF,OAAO,CAAC,MAAD,CAFhB;AAAA,IAGIG,WAAW,GAAGH,OAAO,CAAC,gBAAD,CAHzB;AAAA,IAIII,UAAU,GAAGJ,OAAO,CAAC,eAAD,CAJxB;AAAA,IAKIK,UAAU,GAAGL,OAAO,CAAC,eAAD,CALxB;AAOA;;;AACA,IAAIM,oBAAoB,GAAG,CAA3B;AAAA,IACIC,sBAAsB,GAAG,CAD7B;AAGA;;AACA,IAAIC,OAAO,GAAG,kBAAd;AAAA,IACIC,OAAO,GAAG,eADd;AAAA,IAEIC,QAAQ,GAAG,gBAFf;AAAA,IAGIC,MAAM,GAAG,cAHb;AAAA,IAIIC,SAAS,GAAG,iBAJhB;AAAA,IAKIC,SAAS,GAAG,iBALhB;AAAA,IAMIC,MAAM,GAAG,cANb;AAAA,IAOIC,SAAS,GAAG,iBAPhB;AAAA,IAQIC,SAAS,GAAG,iBARhB;AAUA,IAAIC,cAAc,GAAG,sBAArB;AAAA,IACIC,WAAW,GAAG,mBADlB;AAGA;;AACA,IAAIC,WAAW,GAAGpB,MAAM,GAAGA,MAAM,CAACqB,SAAV,GAAsBC,SAA9C;AAAA,IACIC,aAAa,GAAGH,WAAW,GAAGA,WAAW,CAACI,OAAf,GAAyBF,SADxD;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASG,UAAT,CAAoBC,MAApB,EAA4BC,KAA5B,EAAmCC,GAAnC,EAAwCC,OAAxC,EAAiDC,UAAjD,EAA6DC,SAA7D,EAAwEC,KAAxE,EAA+E;EAC7E,QAAQJ,GAAR;IACE,KAAKT,WAAL;MACE,IAAKO,MAAM,CAACO,UAAP,IAAqBN,KAAK,CAACM,UAA5B,IACCP,MAAM,CAACQ,UAAP,IAAqBP,KAAK,CAACO,UADhC,EAC6C;QAC3C,OAAO,KAAP;MACD;;MACDR,MAAM,GAAGA,MAAM,CAACS,MAAhB;MACAR,KAAK,GAAGA,KAAK,CAACQ,MAAd;;IAEF,KAAKjB,cAAL;MACE,IAAKQ,MAAM,CAACO,UAAP,IAAqBN,KAAK,CAACM,UAA5B,IACA,CAACF,SAAS,CAAC,IAAI7B,UAAJ,CAAewB,MAAf,CAAD,EAAyB,IAAIxB,UAAJ,CAAeyB,KAAf,CAAzB,CADd,EAC+D;QAC7D,OAAO,KAAP;MACD;;MACD,OAAO,IAAP;;IAEF,KAAKlB,OAAL;IACA,KAAKC,OAAL;IACA,KAAKG,SAAL;MACE;MACA;MACA,OAAOV,EAAE,CAAC,CAACuB,MAAF,EAAU,CAACC,KAAX,CAAT;;IAEF,KAAKhB,QAAL;MACE,OAAOe,MAAM,CAACU,IAAP,IAAeT,KAAK,CAACS,IAArB,IAA6BV,MAAM,CAACW,OAAP,IAAkBV,KAAK,CAACU,OAA5D;;IAEF,KAAKvB,SAAL;IACA,KAAKE,SAAL;MACE;MACA;MACA;MACA,OAAOU,MAAM,IAAKC,KAAK,GAAG,EAA1B;;IAEF,KAAKf,MAAL;MACE,IAAI0B,OAAO,GAAGjC,UAAd;;IAEF,KAAKU,MAAL;MACE,IAAIwB,SAAS,GAAGV,OAAO,GAAGtB,oBAA1B;MACA+B,OAAO,KAAKA,OAAO,GAAGhC,UAAf,CAAP;;MAEA,IAAIoB,MAAM,CAACc,IAAP,IAAeb,KAAK,CAACa,IAArB,IAA6B,CAACD,SAAlC,EAA6C;QAC3C,OAAO,KAAP;MACD,CANH,CAOE;;;MACA,IAAIE,OAAO,GAAGT,KAAK,CAACU,GAAN,CAAUhB,MAAV,CAAd;;MACA,IAAIe,OAAJ,EAAa;QACX,OAAOA,OAAO,IAAId,KAAlB;MACD;;MACDE,OAAO,IAAIrB,sBAAX,CAZF,CAcE;;MACAwB,KAAK,CAACW,GAAN,CAAUjB,MAAV,EAAkBC,KAAlB;MACA,IAAIiB,MAAM,GAAGxC,WAAW,CAACkC,OAAO,CAACZ,MAAD,CAAR,EAAkBY,OAAO,CAACX,KAAD,CAAzB,EAAkCE,OAAlC,EAA2CC,UAA3C,EAAuDC,SAAvD,EAAkEC,KAAlE,CAAxB;MACAA,KAAK,CAAC,QAAD,CAAL,CAAgBN,MAAhB;MACA,OAAOkB,MAAP;;IAEF,KAAK3B,SAAL;MACE,IAAIM,aAAJ,EAAmB;QACjB,OAAOA,aAAa,CAACsB,IAAd,CAAmBnB,MAAnB,KAA8BH,aAAa,CAACsB,IAAd,CAAmBlB,KAAnB,CAArC;MACD;;EA3DL;;EA6DA,OAAO,KAAP;AACD;;AAEDmB,MAAM,CAACC,OAAP,GAAiBtB,UAAjB"}, "metadata": {}, "sourceType": "script"}