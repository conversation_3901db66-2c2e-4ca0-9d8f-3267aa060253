{"ast": null, "code": "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react')) : typeof define === 'function' && define.amd ? define(['exports', 'react'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ReactErrorBoundary = {}, global.React));\n})(this, function (exports, React) {\n  'use strict';\n\n  function _interopNamespace(e) {\n    if (e && e.__esModule) return e;\n    var n = Object.create(null);\n\n    if (e) {\n      Object.keys(e).forEach(function (k) {\n        if (k !== 'default') {\n          var d = Object.getOwnPropertyDescriptor(e, k);\n          Object.defineProperty(n, k, d.get ? d : {\n            enumerable: true,\n            get: function get() {\n              return e[k];\n            }\n          });\n        }\n      });\n    }\n\n    n[\"default\"] = e;\n    return Object.freeze(n);\n  }\n\n  var React__namespace = /*#__PURE__*/_interopNamespace(React);\n\n  function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n      o.__proto__ = p;\n      return o;\n    };\n\n    return _setPrototypeOf(o, p);\n  }\n\n  function _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n\n    _setPrototypeOf(subClass, superClass);\n  }\n\n  var changedArray = function changedArray(a, b) {\n    if (a === void 0) {\n      a = [];\n    }\n\n    if (b === void 0) {\n      b = [];\n    }\n\n    return a.length !== b.length || a.some(function (item, index) {\n      return !Object.is(item, b[index]);\n    });\n  };\n\n  var initialState = {\n    error: null\n  };\n\n  var ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n    _inheritsLoose(ErrorBoundary, _React$Component);\n\n    function ErrorBoundary() {\n      var _this;\n\n      for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n        _args[_key] = arguments[_key];\n      }\n\n      _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n      _this.state = initialState;\n\n      _this.resetErrorBoundary = function () {\n        var _this$props;\n\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n\n        _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);\n\n        _this.reset();\n      };\n\n      return _this;\n    }\n\n    ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n      return {\n        error: error\n      };\n    };\n\n    var _proto = ErrorBoundary.prototype;\n\n    _proto.reset = function reset() {\n      this.setState(initialState);\n    };\n\n    _proto.componentDidCatch = function componentDidCatch(error, info) {\n      var _this$props$onError, _this$props2;\n\n      (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);\n    };\n\n    _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n      var error = this.state.error;\n      var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error\n      // happens to *also* be in the resetKeys array, we'd end up resetting\n      // the error boundary immediately. This would likely trigger a second\n      // error to be thrown.\n      // So we make sure that we don't check the resetKeys on the first call\n      // of cDU after the error is set\n\n      if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {\n        var _this$props$onResetKe, _this$props3;\n\n        (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);\n        this.reset();\n      }\n    };\n\n    _proto.render = function render() {\n      var error = this.state.error;\n      var _this$props4 = this.props,\n          fallbackRender = _this$props4.fallbackRender,\n          FallbackComponent = _this$props4.FallbackComponent,\n          fallback = _this$props4.fallback;\n\n      if (error !== null) {\n        var _props = {\n          error: error,\n          resetErrorBoundary: this.resetErrorBoundary\n        };\n\n        if ( /*#__PURE__*/React__namespace.isValidElement(fallback)) {\n          return fallback;\n        } else if (typeof fallbackRender === 'function') {\n          return fallbackRender(_props);\n        } else if (FallbackComponent) {\n          return /*#__PURE__*/React__namespace.createElement(FallbackComponent, _props);\n        } else {\n          throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');\n        }\n      }\n\n      return this.props.children;\n    };\n\n    return ErrorBoundary;\n  }(React__namespace.Component);\n\n  function withErrorBoundary(Component, errorBoundaryProps) {\n    var Wrapped = function Wrapped(props) {\n      return /*#__PURE__*/React__namespace.createElement(ErrorBoundary, errorBoundaryProps, /*#__PURE__*/React__namespace.createElement(Component, props));\n    }; // Format for display in DevTools\n\n\n    var name = Component.displayName || Component.name || 'Unknown';\n    Wrapped.displayName = \"withErrorBoundary(\" + name + \")\";\n    return Wrapped;\n  }\n\n  function useErrorHandler(givenError) {\n    var _React$useState = React__namespace.useState(null),\n        error = _React$useState[0],\n        setError = _React$useState[1];\n\n    if (givenError != null) throw givenError;\n    if (error != null) throw error;\n    return setError;\n  }\n  /*\n  eslint\n    @typescript-eslint/sort-type-union-intersection-members: \"off\",\n    @typescript-eslint/no-throw-literal: \"off\",\n    @typescript-eslint/prefer-nullish-coalescing: \"off\"\n  */\n\n\n  exports.ErrorBoundary = ErrorBoundary;\n  exports.useErrorHandler = useErrorHandler;\n  exports.withErrorBoundary = withErrorBoundary;\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n});", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAe,SAASA,eAAT,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;IAC5CF,eAAe,GAAGG,MAAM,CAACC,cAAPD,IAAyB,SAASH,eAAT,CAAyBC,CAAzB,EAA4BC,CAA5B,EAA+B;MACxED,CAAC,CAACI,SAAFJ,GAAcC,CAAdD;MACA,OAAOA,CAAP;IAFF;;IAKA,OAAOD,eAAe,CAACC,CAAD,EAAIC,CAAJ,CAAtB;EACD;;ECNc,SAASI,cAAT,CAAwBC,QAAxB,EAAkCC,UAAlC,EAA8C;IAC3DD,QAAQ,CAACE,SAATF,GAAqBJ,MAAM,CAACO,MAAPP,CAAcK,UAAU,CAACC,SAAzBN,CAArBI;IACAA,QAAQ,CAACE,SAATF,CAAmBI,WAAnBJ,GAAiCA,QAAjCA;;IACAH,eAAc,CAACG,QAAD,EAAWC,UAAX,CAAdJ;EACD;;ECHD,IAAMQ,YAAY,GAAG,SAAfA,YAAe,CAACC,CAAD,EAAyBC,CAAzB;IAAA,IAACD,CAAD;MAACA,CAAD,GAAqB,EAApBA;IAAD;;IAAA,IAAyBC,CAAzB;MAAyBA,CAAzB,GAA6C,EAApBA;IAAzB;;IAAA,OACnBD,CAAC,CAACE,MAAFF,KAAaC,CAAC,CAACC,MAAfF,IAAyBA,CAAC,CAACG,IAAFH,CAAO,UAACI,IAAD,EAAOC,KAAP;MAAA,OAAiB,CAACf,MAAM,CAACgB,EAAPhB,CAAUc,IAAVd,EAAgBW,CAAC,CAACI,KAAD,CAAjBf,CAAlB;IAAP,EADN;EAArB;;EAgEA,IAAMiB,YAAgC,GAAG;IAACC,KAAK,EAAE;EAAR,CAAzC;;MAEMC;;;;;;;;;;;YAQJC,QAAQH;;YACRI,qBAAqB,YAA6B;QAAA;;QAAA,mCAAzBC,IAAyB;UAAzBA,IAAyB,OAAzBA,GAAyBC,gBAAzBD;QAAyB;;QAChDE,MAAKC,KAAL,CAAWC,OAAX,yCAAKD,KAAL,EAAWC,OAAX,oBAAwBJ,IAAxB;;QACAE,MAAKG,KAAL;MACD;;;;;kBARMC,2BAAP,kCAAgCV,KAAhC,EAA8C;MAC5C,OAAO;QAACA,KAAK,EAALA;MAAD,CAAP;IACD;;;;WAQDS,yBAAQ;MACN,KAAKE,QAAL,CAAcZ,YAAd;IACD;;WAEDa,+CAAkBZ,KAAlB,EAAgCa,IAAhC,EAAuD;MAAA;;MACrD,4CAAKN,KAAL,EAAWO,OAAX,4DAAqBd,KAArB,EAA4Ba,IAA5B;IACD;;WAEDE,iDACEC,SADF,EAEEC,SAFF,EAGE;MACA,IAAOjB,KAAP,GAAgB,KAAKE,KAAL,CAATF,KAAP;MACA,IAAOkB,SAAP,GAAoB,KAAKX,KAAL,CAAbW,SAAP,CAFA;MAKA;MACA;MACA;MACA;MACA;;MAEA,IACElB,KAAK,KAAK,IAAVA,IACAiB,SAAS,CAACjB,KAAViB,KAAoB,IADpBjB,IAEAT,YAAY,CAACyB,SAAS,CAACE,SAAX,EAAsBA,SAAtB,CAHd,EAIE;QAAA;;QACA,8CAAKX,KAAL,EAAWY,iBAAX,8DAA+BH,SAAS,CAACE,SAAzC,EAAoDA,SAApD;QACA,KAAKT,KAAL;MACD;IACF;;WAEDW,2BAAS;MACP,IAAOpB,KAAP,GAAgB,KAAKE,KAAL,CAATF,KAAP;MAEA,mBAAsD,KAAKO,KAA3D;MAAA,IAAOc,cAAP,gBAAOA,cAAP;MAAA,IAAuBC,iBAAvB,gBAAuBA,iBAAvB;MAAA,IAA0CC,QAA1C,gBAA0CA,QAA1C;;MAEA,IAAIvB,KAAK,KAAK,IAAd,EAAoB;QAClB,IAAMO,MAAK,GAAG;UACZP,KAAK,EAALA,KADY;UAEZG,kBAAkB,EAAE,KAAKA;QAFb,CAAd;;QAIA,kBAAIqB,gBAAK,CAACC,cAAND,CAAqBD,QAArBC,CAAJ,EAAoC;UAClC,OAAOD,QAAP;QADF,OAEO,IAAI,OAAOF,cAAP,KAA0B,UAA9B,EAA0C;UAC/C,OAAOA,cAAc,CAACd,MAAD,CAArB;QADK,OAEA,IAAIe,iBAAJ,EAAuB;UAC5B,oBAAOE,+BAACF,iBAADE,EAAuBjB,MAAvBiB,CAAP;QADK,OAEA;UACL,MAAM,IAAIE,KAAJ,CACJ,4FADI,CAAN;QAGD;MACF;;MAED,OAAO,KAAKnB,KAAL,CAAWoB,QAAlB;IACD;;;GAtEG1B,CAAsBuB,gBAAK,CAACI,SAA5B3B;;EAyEN,SAAS4B,iBAAT,CACED,SADF,EAEEE,kBAFF,EAG0B;IACxB,IAAMC,OAA+B,GAAG,SAAlCA,OAAkC,QAAS;MAC/C,oBACEP,+BAACvB,aAADuB,EAAmBM,kBAAnBN,eACEA,+BAACI,SAADJ,EAAejB,KAAfiB,CADFA,CADF;IADF,EADwB;;;IAUxB,IAAMQ,IAAI,GAAGJ,SAAS,CAACK,WAAVL,IAAyBA,SAAS,CAACI,IAAnCJ,IAA2C,SAAxD;IACAG,OAAO,CAACE,WAARF,0BAA2CC,IAA3C;IAEA,OAAOD,OAAP;EACD;;EAED,SAASG,eAAT,CAAyBC,UAAzB,EAAyE;IACvE,sBAA0BX,gBAAK,CAACY,QAANZ,CAAwB,IAAxBA,CAA1B;IAAA,IAAOxB,KAAP;IAAA,IAAcqC,QAAd;;IACA,IAAIF,UAAU,IAAI,IAAlB,EAAwB,MAAMA,UAAN;IACxB,IAAInC,KAAK,IAAI,IAAb,EAAmB,MAAMA,KAAN;IACnB,OAAOqC,QAAP;EACD;EAWD", "names": ["_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "create", "constructor", "changedArray", "a", "b", "length", "some", "item", "index", "is", "initialState", "error", "Error<PERSON>ou<PERSON><PERSON>", "state", "resetErrorBoundary", "args", "arguments", "_this", "props", "onReset", "reset", "getDerivedStateFromError", "setState", "componentDidCatch", "info", "onError", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "onResetKeysChange", "render", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "React", "isValidElement", "Error", "children", "Component", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorBoundaryProps", "Wrapped", "name", "displayName", "useErrorHandler", "given<PERSON><PERSON><PERSON>", "useState", "setError"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-error-boundary\\node_modules\\@babel\\runtime\\helpers\\esm\\setPrototypeOf.js", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-error-boundary\\node_modules\\@babel\\runtime\\helpers\\esm\\inheritsLoose.js", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-error-boundary\\src\\index.tsx"], "sourcesContent": ["export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import * as React from 'react'\n\nconst changedArray = (a: Array<unknown> = [], b: Array<unknown> = []) =>\n  a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n\ninterface FallbackProps {\n  error: Error\n  resetErrorBoundary: (...args: Array<unknown>) => void\n}\n\ninterface ErrorBoundaryPropsWithComponent {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent: React.ComponentType<FallbackProps>\n  fallbackRender?: never\n}\n\ndeclare function FallbackRender(\n  props: FallbackProps,\n): React.ReactElement<\n  unknown,\n  string | React.FunctionComponent | typeof React.Component\n> | null\n\ninterface ErrorBoundaryPropsWithRender {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback?: never\n  FallbackComponent?: never\n  fallbackRender: typeof FallbackRender\n}\n\ninterface ErrorBoundaryPropsWithFallback {\n  onResetKeysChange?: (\n    prevResetKeys: Array<unknown> | undefined,\n    resetKeys: Array<unknown> | undefined,\n  ) => void\n  onReset?: (...args: Array<unknown>) => void\n  onError?: (error: Error, info: {componentStack: string}) => void\n  resetKeys?: Array<unknown>\n  fallback: React.ReactElement<\n    unknown,\n    string | React.FunctionComponent | typeof React.Component\n  > | null\n  FallbackComponent?: never\n  fallbackRender?: never\n}\n\ntype ErrorBoundaryProps =\n  | ErrorBoundaryPropsWithFallback\n  | ErrorBoundaryPropsWithComponent\n  | ErrorBoundaryPropsWithRender\n\ntype ErrorBoundaryState = {error: Error | null}\n\nconst initialState: ErrorBoundaryState = {error: null}\n\nclass ErrorBoundary extends React.Component<\n  React.PropsWithRef<React.PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  static getDerivedStateFromError(error: Error) {\n    return {error}\n  }\n\n  state = initialState\n  resetErrorBoundary = (...args: Array<unknown>) => {\n    this.props.onReset?.(...args)\n    this.reset()\n  }\n\n  reset() {\n    this.setState(initialState)\n  }\n\n  componentDidCatch(error: Error, info: React.ErrorInfo) {\n    this.props.onError?.(error, info)\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState,\n  ) {\n    const {error} = this.state\n    const {resetKeys} = this.props\n\n    // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (\n      error !== null &&\n      prevState.error !== null &&\n      changedArray(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onResetKeysChange?.(prevProps.resetKeys, resetKeys)\n      this.reset()\n    }\n  }\n\n  render() {\n    const {error} = this.state\n\n    const {fallbackRender, FallbackComponent, fallback} = this.props\n\n    if (error !== null) {\n      const props = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      }\n      if (React.isValidElement(fallback)) {\n        return fallback\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(props)\n      } else if (FallbackComponent) {\n        return <FallbackComponent {...props} />\n      } else {\n        throw new Error(\n          'react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop',\n        )\n      }\n    }\n\n    return this.props.children\n  }\n}\n\nfunction withErrorBoundary<P>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps: ErrorBoundaryProps,\n): React.ComponentType<P> {\n  const Wrapped: React.ComponentType<P> = props => {\n    return (\n      <ErrorBoundary {...errorBoundaryProps}>\n        <Component {...props} />\n      </ErrorBoundary>\n    )\n  }\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || 'Unknown'\n  Wrapped.displayName = `withErrorBoundary(${name})`\n\n  return Wrapped\n}\n\nfunction useErrorHandler(givenError?: unknown): (error: unknown) => void {\n  const [error, setError] = React.useState<unknown>(null)\n  if (givenError != null) throw givenError\n  if (error != null) throw error\n  return setError\n}\n\nexport {ErrorBoundary, withErrorBoundary, useErrorHandler}\nexport type {\n  FallbackProps,\n  ErrorBoundaryPropsWithComponent,\n  ErrorBoundaryPropsWithRender,\n  ErrorBoundaryPropsWithFallback,\n  ErrorBoundaryProps,\n}\n\n/*\neslint\n  @typescript-eslint/sort-type-union-intersection-members: \"off\",\n  @typescript-eslint/no-throw-literal: \"off\",\n  @typescript-eslint/prefer-nullish-coalescing: \"off\"\n*/\n"]}, "metadata": {}, "sourceType": "script"}