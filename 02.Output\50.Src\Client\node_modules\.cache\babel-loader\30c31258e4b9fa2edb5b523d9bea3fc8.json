{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport CanceledError from './CanceledError.js';\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\n\nvar CancelToken = /*#__PURE__*/function () {\n  function CancelToken(executor) {\n    _classCallCheck(this, CancelToken);\n\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n\n    var resolvePromise;\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n    var token = this; // eslint-disable-next-line func-names\n\n    this.promise.then(function (cancel) {\n      if (!token._listeners) return;\n      var i = token._listeners.length;\n\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n\n      token._listeners = null;\n    }); // eslint-disable-next-line func-names\n\n    this.promise.then = function (onfulfilled) {\n      var _resolve; // eslint-disable-next-line func-names\n\n\n      var promise = new Promise(function (resolve) {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n\n      return promise;\n    };\n\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n\n\n  _createClass(CancelToken, [{\n    key: \"throwIfRequested\",\n    value: function throwIfRequested() {\n      if (this.reason) {\n        throw this.reason;\n      }\n    }\n    /**\n     * Subscribe to the cancel signal\n     */\n\n  }, {\n    key: \"subscribe\",\n    value: function subscribe(listener) {\n      if (this.reason) {\n        listener(this.reason);\n        return;\n      }\n\n      if (this._listeners) {\n        this._listeners.push(listener);\n      } else {\n        this._listeners = [listener];\n      }\n    }\n    /**\n     * Unsubscribe from the cancel signal\n     */\n\n  }, {\n    key: \"unsubscribe\",\n    value: function unsubscribe(listener) {\n      if (!this._listeners) {\n        return;\n      }\n\n      var index = this._listeners.indexOf(listener);\n\n      if (index !== -1) {\n        this._listeners.splice(index, 1);\n      }\n    }\n    /**\n     * Returns an object that contains a new `CancelToken` and a function that, when called,\n     * cancels the `CancelToken`.\n     */\n\n  }], [{\n    key: \"source\",\n    value: function source() {\n      var cancel;\n      var token = new CancelToken(function executor(c) {\n        cancel = c;\n      });\n      return {\n        token: token,\n        cancel: cancel\n      };\n    }\n  }]);\n\n  return CancelToken;\n}();\n\nexport default CancelToken;", "map": {"version": 3, "names": ["CanceledError", "CancelToken", "executor", "TypeError", "resolvePromise", "promise", "Promise", "promiseExecutor", "resolve", "token", "then", "cancel", "_listeners", "i", "length", "onfulfilled", "_resolve", "subscribe", "reject", "unsubscribe", "message", "config", "request", "reason", "listener", "push", "index", "indexOf", "splice", "c"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/cancel/CancelToken.js"], "sourcesContent": ["'use strict';\n\nimport CanceledError from './CanceledError.js';\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\nclass CancelToken {\n  constructor(executor) {\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n\n    let resolvePromise;\n\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n\n    const token = this;\n\n    // eslint-disable-next-line func-names\n    this.promise.then(cancel => {\n      if (!token._listeners) return;\n\n      let i = token._listeners.length;\n\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n      token._listeners = null;\n    });\n\n    // eslint-disable-next-line func-names\n    this.promise.then = onfulfilled => {\n      let _resolve;\n      // eslint-disable-next-line func-names\n      const promise = new Promise(resolve => {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n\n      return promise;\n    };\n\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n  throwIfRequested() {\n    if (this.reason) {\n      throw this.reason;\n    }\n  }\n\n  /**\n   * Subscribe to the cancel signal\n   */\n\n  subscribe(listener) {\n    if (this.reason) {\n      listener(this.reason);\n      return;\n    }\n\n    if (this._listeners) {\n      this._listeners.push(listener);\n    } else {\n      this._listeners = [listener];\n    }\n  }\n\n  /**\n   * Unsubscribe from the cancel signal\n   */\n\n  unsubscribe(listener) {\n    if (!this._listeners) {\n      return;\n    }\n    const index = this._listeners.indexOf(listener);\n    if (index !== -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */\n  static source() {\n    let cancel;\n    const token = new CancelToken(function executor(c) {\n      cancel = c;\n    });\n    return {\n      token,\n      cancel\n    };\n  }\n}\n\nexport default CancelToken;\n"], "mappings": "AAAA;;;;AAEA,OAAOA,aAAP,MAA0B,oBAA1B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;IACMC,W;EACJ,qBAAYC,QAAZ,EAAsB;IAAA;;IACpB,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;MAClC,MAAM,IAAIC,SAAJ,CAAc,8BAAd,CAAN;IACD;;IAED,IAAIC,cAAJ;IAEA,KAAKC,OAAL,GAAe,IAAIC,OAAJ,CAAY,SAASC,eAAT,CAAyBC,OAAzB,EAAkC;MAC3DJ,cAAc,GAAGI,OAAjB;IACD,CAFc,CAAf;IAIA,IAAMC,KAAK,GAAG,IAAd,CAXoB,CAapB;;IACA,KAAKJ,OAAL,CAAaK,IAAb,CAAkB,UAAAC,MAAM,EAAI;MAC1B,IAAI,CAACF,KAAK,CAACG,UAAX,EAAuB;MAEvB,IAAIC,CAAC,GAAGJ,KAAK,CAACG,UAAN,CAAiBE,MAAzB;;MAEA,OAAOD,CAAC,KAAK,CAAb,EAAgB;QACdJ,KAAK,CAACG,UAAN,CAAiBC,CAAjB,EAAoBF,MAApB;MACD;;MACDF,KAAK,CAACG,UAAN,GAAmB,IAAnB;IACD,CATD,EAdoB,CAyBpB;;IACA,KAAKP,OAAL,CAAaK,IAAb,GAAoB,UAAAK,WAAW,EAAI;MACjC,IAAIC,QAAJ,CADiC,CAEjC;;;MACA,IAAMX,OAAO,GAAG,IAAIC,OAAJ,CAAY,UAAAE,OAAO,EAAI;QACrCC,KAAK,CAACQ,SAAN,CAAgBT,OAAhB;QACAQ,QAAQ,GAAGR,OAAX;MACD,CAHe,EAGbE,IAHa,CAGRK,WAHQ,CAAhB;;MAKAV,OAAO,CAACM,MAAR,GAAiB,SAASO,MAAT,GAAkB;QACjCT,KAAK,CAACU,WAAN,CAAkBH,QAAlB;MACD,CAFD;;MAIA,OAAOX,OAAP;IACD,CAbD;;IAeAH,QAAQ,CAAC,SAASS,MAAT,CAAgBS,OAAhB,EAAyBC,MAAzB,EAAiCC,OAAjC,EAA0C;MACjD,IAAIb,KAAK,CAACc,MAAV,EAAkB;QAChB;QACA;MACD;;MAEDd,KAAK,CAACc,MAAN,GAAe,IAAIvB,aAAJ,CAAkBoB,OAAlB,EAA2BC,MAA3B,EAAmCC,OAAnC,CAAf;MACAlB,cAAc,CAACK,KAAK,CAACc,MAAP,CAAd;IACD,CARO,CAAR;EASD;EAED;AACF;AACA;;;;;WACE,4BAAmB;MACjB,IAAI,KAAKA,MAAT,EAAiB;QACf,MAAM,KAAKA,MAAX;MACD;IACF;IAED;AACF;AACA;;;;WAEE,mBAAUC,QAAV,EAAoB;MAClB,IAAI,KAAKD,MAAT,EAAiB;QACfC,QAAQ,CAAC,KAAKD,MAAN,CAAR;QACA;MACD;;MAED,IAAI,KAAKX,UAAT,EAAqB;QACnB,KAAKA,UAAL,CAAgBa,IAAhB,CAAqBD,QAArB;MACD,CAFD,MAEO;QACL,KAAKZ,UAAL,GAAkB,CAACY,QAAD,CAAlB;MACD;IACF;IAED;AACF;AACA;;;;WAEE,qBAAYA,QAAZ,EAAsB;MACpB,IAAI,CAAC,KAAKZ,UAAV,EAAsB;QACpB;MACD;;MACD,IAAMc,KAAK,GAAG,KAAKd,UAAL,CAAgBe,OAAhB,CAAwBH,QAAxB,CAAd;;MACA,IAAIE,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChB,KAAKd,UAAL,CAAgBgB,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;MACD;IACF;IAED;AACF;AACA;AACA;;;;WACE,kBAAgB;MACd,IAAIf,MAAJ;MACA,IAAMF,KAAK,GAAG,IAAIR,WAAJ,CAAgB,SAASC,QAAT,CAAkB2B,CAAlB,EAAqB;QACjDlB,MAAM,GAAGkB,CAAT;MACD,CAFa,CAAd;MAGA,OAAO;QACLpB,KAAK,EAALA,KADK;QAELE,MAAM,EAANA;MAFK,CAAP;IAID;;;;;;AAGH,eAAeV,WAAf"}, "metadata": {}, "sourceType": "module"}