{"ast": null, "code": "'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}", "map": {"version": 3, "names": ["isCancel", "value", "__CANCEL__"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/cancel/isCancel.js"], "sourcesContent": ["'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}\n"], "mappings": "AAAA;;AAEA,eAAe,SAASA,QAAT,CAAkBC,KAAlB,EAAyB;EACtC,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACC,UAAjB,CAAR;AACD"}, "metadata": {}, "sourceType": "module"}