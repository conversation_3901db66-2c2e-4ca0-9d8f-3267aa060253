{"ast": null, "code": "var Stack = require('./_Stack'),\n    equalArrays = require('./_equalArrays'),\n    equalByTag = require('./_equalByTag'),\n    equalObjects = require('./_equalObjects'),\n    getTag = require('./_getTag'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isTypedArray = require('./isTypedArray');\n/** Used to compose bitmasks for value comparisons. */\n\n\nvar COMPARE_PARTIAL_FLAG = 1;\n/** `Object#toString` result references. */\n\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    objectTag = '[object Object]';\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\n\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n      othIsArr = isArray(other),\n      objTag = objIsArr ? arrayTag : getTag(object),\n      othTag = othIsArr ? arrayTag : getTag(other);\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n  var objIsObj = objTag == objectTag,\n      othIsObj = othTag == objectTag,\n      isSameTag = objTag == othTag;\n\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n\n    objIsArr = true;\n    objIsObj = false;\n  }\n\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack());\n    return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n          othUnwrapped = othIsWrapped ? other.value() : other;\n      stack || (stack = new Stack());\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n\n  if (!isSameTag) {\n    return false;\n  }\n\n  stack || (stack = new Stack());\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n\nmodule.exports = baseIsEqualDeep;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "require", "equalArrays", "equalByTag", "equalObjects", "getTag", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "isTypedArray", "COMPARE_PARTIAL_FLAG", "argsTag", "arrayTag", "objectTag", "objectProto", "Object", "prototype", "hasOwnProperty", "baseIsEqualDeep", "object", "other", "bitmask", "customizer", "equalFunc", "stack", "objIsArr", "othIsArr", "objTag", "othTag", "objIsObj", "othIsObj", "isSameTag", "objIsWrapped", "call", "othIsWrapped", "objUnwrapped", "value", "othUnwrapped", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseIsEqualDeep.js"], "sourcesContent": ["var Stack = require('./_Stack'),\n    equalArrays = require('./_equalArrays'),\n    equalByTag = require('./_equalByTag'),\n    equalObjects = require('./_equalObjects'),\n    getTag = require('./_getTag'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isTypedArray = require('./isTypedArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n      othIsArr = isArray(other),\n      objTag = objIsArr ? arrayTag : getTag(object),\n      othTag = othIsArr ? arrayTag : getTag(other);\n\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n\n  var objIsObj = objTag == objectTag,\n      othIsObj = othTag == objectTag,\n      isSameTag = objTag == othTag;\n\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack);\n    return (objIsArr || isTypedArray(object))\n      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)\n      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n          othUnwrapped = othIsWrapped ? other.value() : other;\n\n      stack || (stack = new Stack);\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack);\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n\nmodule.exports = baseIsEqualDeep;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAD,CAAnB;AAAA,IACIC,WAAW,GAAGD,OAAO,CAAC,gBAAD,CADzB;AAAA,IAEIE,UAAU,GAAGF,OAAO,CAAC,eAAD,CAFxB;AAAA,IAGIG,YAAY,GAAGH,OAAO,CAAC,iBAAD,CAH1B;AAAA,IAIII,MAAM,GAAGJ,OAAO,CAAC,WAAD,CAJpB;AAAA,IAKIK,OAAO,GAAGL,OAAO,CAAC,WAAD,CALrB;AAAA,IAMIM,QAAQ,GAAGN,OAAO,CAAC,YAAD,CANtB;AAAA,IAOIO,YAAY,GAAGP,OAAO,CAAC,gBAAD,CAP1B;AASA;;;AACA,IAAIQ,oBAAoB,GAAG,CAA3B;AAEA;;AACA,IAAIC,OAAO,GAAG,oBAAd;AAAA,IACIC,QAAQ,GAAG,gBADf;AAAA,IAEIC,SAAS,GAAG,iBAFhB;AAIA;;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,eAAT,CAAyBC,MAAzB,EAAiCC,KAAjC,EAAwCC,OAAxC,EAAiDC,UAAjD,EAA6DC,SAA7D,EAAwEC,KAAxE,EAA+E;EAC7E,IAAIC,QAAQ,GAAGlB,OAAO,CAACY,MAAD,CAAtB;EAAA,IACIO,QAAQ,GAAGnB,OAAO,CAACa,KAAD,CADtB;EAAA,IAEIO,MAAM,GAAGF,QAAQ,GAAGb,QAAH,GAAcN,MAAM,CAACa,MAAD,CAFzC;EAAA,IAGIS,MAAM,GAAGF,QAAQ,GAAGd,QAAH,GAAcN,MAAM,CAACc,KAAD,CAHzC;EAKAO,MAAM,GAAGA,MAAM,IAAIhB,OAAV,GAAoBE,SAApB,GAAgCc,MAAzC;EACAC,MAAM,GAAGA,MAAM,IAAIjB,OAAV,GAAoBE,SAApB,GAAgCe,MAAzC;EAEA,IAAIC,QAAQ,GAAGF,MAAM,IAAId,SAAzB;EAAA,IACIiB,QAAQ,GAAGF,MAAM,IAAIf,SADzB;EAAA,IAEIkB,SAAS,GAAGJ,MAAM,IAAIC,MAF1B;;EAIA,IAAIG,SAAS,IAAIvB,QAAQ,CAACW,MAAD,CAAzB,EAAmC;IACjC,IAAI,CAACX,QAAQ,CAACY,KAAD,CAAb,EAAsB;MACpB,OAAO,KAAP;IACD;;IACDK,QAAQ,GAAG,IAAX;IACAI,QAAQ,GAAG,KAAX;EACD;;EACD,IAAIE,SAAS,IAAI,CAACF,QAAlB,EAA4B;IAC1BL,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAJ,EAAb,CAAL;IACA,OAAQwB,QAAQ,IAAIhB,YAAY,CAACU,MAAD,CAAzB,GACHhB,WAAW,CAACgB,MAAD,EAASC,KAAT,EAAgBC,OAAhB,EAAyBC,UAAzB,EAAqCC,SAArC,EAAgDC,KAAhD,CADR,GAEHpB,UAAU,CAACe,MAAD,EAASC,KAAT,EAAgBO,MAAhB,EAAwBN,OAAxB,EAAiCC,UAAjC,EAA6CC,SAA7C,EAAwDC,KAAxD,CAFd;EAGD;;EACD,IAAI,EAAEH,OAAO,GAAGX,oBAAZ,CAAJ,EAAuC;IACrC,IAAIsB,YAAY,GAAGH,QAAQ,IAAIZ,cAAc,CAACgB,IAAf,CAAoBd,MAApB,EAA4B,aAA5B,CAA/B;IAAA,IACIe,YAAY,GAAGJ,QAAQ,IAAIb,cAAc,CAACgB,IAAf,CAAoBb,KAApB,EAA2B,aAA3B,CAD/B;;IAGA,IAAIY,YAAY,IAAIE,YAApB,EAAkC;MAChC,IAAIC,YAAY,GAAGH,YAAY,GAAGb,MAAM,CAACiB,KAAP,EAAH,GAAoBjB,MAAnD;MAAA,IACIkB,YAAY,GAAGH,YAAY,GAAGd,KAAK,CAACgB,KAAN,EAAH,GAAmBhB,KADlD;MAGAI,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAJ,EAAb,CAAL;MACA,OAAOsB,SAAS,CAACY,YAAD,EAAeE,YAAf,EAA6BhB,OAA7B,EAAsCC,UAAtC,EAAkDE,KAAlD,CAAhB;IACD;EACF;;EACD,IAAI,CAACO,SAAL,EAAgB;IACd,OAAO,KAAP;EACD;;EACDP,KAAK,KAAKA,KAAK,GAAG,IAAIvB,KAAJ,EAAb,CAAL;EACA,OAAOI,YAAY,CAACc,MAAD,EAASC,KAAT,EAAgBC,OAAhB,EAAyBC,UAAzB,EAAqCC,SAArC,EAAgDC,KAAhD,CAAnB;AACD;;AAEDc,MAAM,CAACC,OAAP,GAAiBrB,eAAjB"}, "metadata": {}, "sourceType": "script"}