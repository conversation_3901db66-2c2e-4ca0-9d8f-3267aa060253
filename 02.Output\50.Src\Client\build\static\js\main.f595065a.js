/*! For license information please see main.f595065a.js.LICENSE.txt */
!function(){var e={7472:function(e){e.exports="object"==typeof self?self.FormData:window.FormData},2110:function(e,t,n){"use strict";var r=n(8309),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r.isMemo(e)?i:s[e.$$typeof]||o}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=i;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var i=u(n);d&&(i=i.concat(d(n)));for(var s=l(t),m=l(n),v=0;v<i.length;++v){var g=i[v];if(!a[g]&&(!r||!r[g])&&(!m||!m[g])&&(!s||!s[g])){var y=f(n,g);try{c(t,g,y)}catch(b){}}}}return t}},746:function(e,t){"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,v=n?Symbol.for("react.lazy"):60116,g=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,_=n?Symbol.for("react.scope"):60119;function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case a:case s:case i:case p:return e;default:switch(e=e&&e.$$typeof){case c:case f:case v:case m:case l:return e;default:return t}}case o:return t}}}function w(e){return x(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=a,t.Lazy=v,t.Memo=m,t.Portal=o,t.Profiler=s,t.StrictMode=i,t.Suspense=p,t.isAsyncMode=function(e){return w(e)||x(e)===u},t.isConcurrentMode=w,t.isContextConsumer=function(e){return x(e)===c},t.isContextProvider=function(e){return x(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===f},t.isFragment=function(e){return x(e)===a},t.isLazy=function(e){return x(e)===v},t.isMemo=function(e){return x(e)===m},t.isPortal=function(e){return x(e)===o},t.isProfiler=function(e){return x(e)===s},t.isStrictMode=function(e){return x(e)===i},t.isSuspense=function(e){return x(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===s||e===i||e===p||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===m||e.$$typeof===l||e.$$typeof===c||e.$$typeof===f||e.$$typeof===y||e.$$typeof===b||e.$$typeof===_||e.$$typeof===g)},t.typeOf=x},8309:function(e,t,n){"use strict";e.exports=n(746)},2534:function(e){"function"===typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},908:function(e,t,n){var r=n(5385)(n(7009),"DataView");e.exports=r},9676:function(e,t,n){var r=n(5403),o=n(2747),a=n(6037),i=n(4154),s=n(7728);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=s,e.exports=l},8384:function(e,t,n){var r=n(3894),o=n(8699),a=n(4957),i=n(7184),s=n(7109);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=s,e.exports=l},5797:function(e,t,n){var r=n(5385)(n(7009),"Map");e.exports=r},8059:function(e,t,n){var r=n(4086),o=n(9255),a=n(9186),i=n(3423),s=n(3739);function l(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=i,l.prototype.set=s,e.exports=l},8319:function(e,t,n){var r=n(5385)(n(7009),"Promise");e.exports=r},3924:function(e,t,n){var r=n(5385)(n(7009),"Set");e.exports=r},692:function(e,t,n){var r=n(8059),o=n(5774),a=n(1596);function i(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}i.prototype.add=i.prototype.push=o,i.prototype.has=a,e.exports=i},2854:function(e,t,n){var r=n(8384),o=n(511),a=n(835),i=n(707),s=n(8832),l=n(5077);function c(e){var t=this.__data__=new r(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=a,c.prototype.get=i,c.prototype.has=s,c.prototype.set=l,e.exports=c},7197:function(e,t,n){var r=n(7009).Symbol;e.exports=r},6219:function(e,t,n){var r=n(7009).Uint8Array;e.exports=r},7091:function(e,t,n){var r=n(5385)(n(7009),"WeakMap");e.exports=r},4903:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}},7538:function(e,t,n){var r=n(6478),o=n(4963),a=n(3629),i=n(5174),s=n(6800),l=n(9102),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a(e),u=!n&&o(e),d=!n&&!u&&i(e),f=!n&&!u&&!d&&l(e),p=n||u||d||f,h=p?r(e.length,String):[],m=h.length;for(var v in e)!t&&!c.call(e,v)||p&&("length"==v||d&&("offset"==v||"parent"==v)||f&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||s(v,m))||h.push(v);return h}},1705:function(e){e.exports=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}},7897:function(e){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},7112:function(e,t,n){var r=n(9231);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},1986:function(e,t,n){var r=n(1705),o=n(3629);e.exports=function(e,t,n){var a=t(e);return o(e)?a:r(a,n(e))}},9066:function(e,t,n){var r=n(7197),o=n(1587),a=n(3581),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},4906:function(e,t,n){var r=n(9066),o=n(3141);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},1848:function(e,t,n){var r=n(3355),o=n(3141);e.exports=function e(t,n,a,i,s){return t===n||(null==t||null==n||!o(t)&&!o(n)?t!==t&&n!==n:r(t,n,a,i,e,s))}},3355:function(e,t,n){var r=n(2854),o=n(5305),a=n(2206),i=n(8078),s=n(8383),l=n(3629),c=n(5174),u=n(9102),d="[object Arguments]",f="[object Array]",p="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,m,v,g){var y=l(e),b=l(t),_=y?f:s(e),x=b?f:s(t),w=(_=_==d?p:_)==p,k=(x=x==d?p:x)==p,S=_==x;if(S&&c(e)){if(!c(t))return!1;y=!0,w=!1}if(S&&!w)return g||(g=new r),y||u(e)?o(e,t,n,m,v,g):a(e,t,_,n,m,v,g);if(!(1&n)){var E=w&&h.call(e,"__wrapped__"),C=k&&h.call(t,"__wrapped__");if(E||C){var N=E?e.value():e,j=C?t.value():t;return g||(g=new r),v(N,j,n,m,g)}}return!!S&&(g||(g=new r),i(e,t,n,m,v,g))}},6703:function(e,t,n){var r=n(4786),o=n(257),a=n(8092),i=n(7907),s=/^\[object .+?Constructor\]$/,l=Function.prototype,c=Object.prototype,u=l.toString,d=c.hasOwnProperty,f=RegExp("^"+u.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||o(e))&&(r(e)?f:s).test(i(e))}},8150:function(e,t,n){var r=n(9066),o=n(4635),a=n(3141),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[r(e)]}},3654:function(e,t,n){var r=n(2936),o=n(8836),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},6478:function(e){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},6194:function(e){e.exports=function(e){return function(t){return e(t)}}},75:function(e){e.exports=function(e,t){return e.has(t)}},5525:function(e,t,n){var r=n(7009)["__core-js_shared__"];e.exports=r},5305:function(e,t,n){var r=n(692),o=n(7897),a=n(75);e.exports=function(e,t,n,i,s,l){var c=1&n,u=e.length,d=t.length;if(u!=d&&!(c&&d>u))return!1;var f=l.get(e),p=l.get(t);if(f&&p)return f==t&&p==e;var h=-1,m=!0,v=2&n?new r:void 0;for(l.set(e,t),l.set(t,e);++h<u;){var g=e[h],y=t[h];if(i)var b=c?i(y,g,h,t,e,l):i(g,y,h,e,t,l);if(void 0!==b){if(b)continue;m=!1;break}if(v){if(!o(t,(function(e,t){if(!a(v,t)&&(g===e||s(g,e,n,i,l)))return v.push(t)}))){m=!1;break}}else if(g!==y&&!s(g,y,n,i,l)){m=!1;break}}return l.delete(e),l.delete(t),m}},2206:function(e,t,n){var r=n(7197),o=n(6219),a=n(9231),i=n(5305),s=n(234),l=n(2230),c=r?r.prototype:void 0,u=c?c.valueOf:void 0;e.exports=function(e,t,n,r,c,d,f){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!d(new o(e),new o(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=s;case"[object Set]":var h=1&r;if(p||(p=l),e.size!=t.size&&!h)return!1;var m=f.get(e);if(m)return m==t;r|=2,f.set(e,t);var v=i(p(e),p(t),r,c,d,f);return f.delete(e),v;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},8078:function(e,t,n){var r=n(8248),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,a,i,s){var l=1&n,c=r(e),u=c.length;if(u!=r(t).length&&!l)return!1;for(var d=u;d--;){var f=c[d];if(!(l?f in t:o.call(t,f)))return!1}var p=s.get(e),h=s.get(t);if(p&&h)return p==t&&h==e;var m=!0;s.set(e,t),s.set(t,e);for(var v=l;++d<u;){var g=e[f=c[d]],y=t[f];if(a)var b=l?a(y,g,f,t,e,s):a(g,y,f,e,t,s);if(!(void 0===b?g===y||i(g,y,n,a,s):b)){m=!1;break}v||(v="constructor"==f)}if(m&&!v){var _=e.constructor,x=t.constructor;_==x||!("constructor"in e)||!("constructor"in t)||"function"==typeof _&&_ instanceof _&&"function"==typeof x&&x instanceof x||(m=!1)}return s.delete(e),s.delete(t),m}},1032:function(e,t,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},8248:function(e,t,n){var r=n(1986),o=n(5918),a=n(2742);e.exports=function(e){return r(e,a,o)}},2799:function(e,t,n){var r=n(5964);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},5385:function(e,t,n){var r=n(6703),o=n(40);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},1587:function(e,t,n){var r=n(7197),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,s=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,s),n=e[s];try{e[s]=void 0;var r=!0}catch(l){}var o=i.call(e);return r&&(t?e[s]=n:delete e[s]),o}},5918:function(e,t,n){var r=n(4903),o=n(8174),a=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,s=i?function(e){return null==e?[]:(e=Object(e),r(i(e),(function(t){return a.call(e,t)})))}:o;e.exports=s},8383:function(e,t,n){var r=n(908),o=n(5797),a=n(8319),i=n(3924),s=n(7091),l=n(9066),c=n(7907),u="[object Map]",d="[object Promise]",f="[object Set]",p="[object WeakMap]",h="[object DataView]",m=c(r),v=c(o),g=c(a),y=c(i),b=c(s),_=l;(r&&_(new r(new ArrayBuffer(1)))!=h||o&&_(new o)!=u||a&&_(a.resolve())!=d||i&&_(new i)!=f||s&&_(new s)!=p)&&(_=function(e){var t=l(e),n="[object Object]"==t?e.constructor:void 0,r=n?c(n):"";if(r)switch(r){case m:return h;case v:return u;case g:return d;case y:return f;case b:return p}return t}),e.exports=_},40:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},5403:function(e,t,n){var r=n(9620);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},2747:function(e){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},6037:function(e,t,n){var r=n(9620),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(t,e)?t[e]:void 0}},4154:function(e,t,n){var r=n(9620),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:o.call(t,e)}},7728:function(e,t,n){var r=n(9620);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},6800:function(e){var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var r=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&t.test(e))&&e>-1&&e%1==0&&e<n}},5964:function(e){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},257:function(e,t,n){var r=n(5525),o=function(){var e=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},2936:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},3894:function(e){e.exports=function(){this.__data__=[],this.size=0}},8699:function(e,t,n){var r=n(7112),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0)&&(n==t.length-1?t.pop():o.call(t,n,1),--this.size,!0)}},4957:function(e,t,n){var r=n(7112);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},7184:function(e,t,n){var r=n(7112);e.exports=function(e){return r(this.__data__,e)>-1}},7109:function(e,t,n){var r=n(7112);e.exports=function(e,t){var n=this.__data__,o=r(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this}},4086:function(e,t,n){var r=n(9676),o=n(8384),a=n(5797);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(a||o),string:new r}}},9255:function(e,t,n){var r=n(2799);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},9186:function(e,t,n){var r=n(2799);e.exports=function(e){return r(this,e).get(e)}},3423:function(e,t,n){var r=n(2799);e.exports=function(e){return r(this,e).has(e)}},3739:function(e,t,n){var r=n(2799);e.exports=function(e,t){var n=r(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this}},234:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}},9620:function(e,t,n){var r=n(5385)(Object,"create");e.exports=r},8836:function(e,t,n){var r=n(2709)(Object.keys,Object);e.exports=r},9494:function(e,t,n){e=n.nmd(e);var r=n(1032),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&r.process,s=function(){try{var e=a&&a.require&&a.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(t){}}();e.exports=s},3581:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},2709:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},7009:function(e,t,n){var r=n(1032),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},5774:function(e){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},1596:function(e){e.exports=function(e){return this.__data__.has(e)}},2230:function(e){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}},511:function(e,t,n){var r=n(8384);e.exports=function(){this.__data__=new r,this.size=0}},835:function(e){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},707:function(e){e.exports=function(e){return this.__data__.get(e)}},8832:function(e){e.exports=function(e){return this.__data__.has(e)}},5077:function(e,t,n){var r=n(8384),o=n(5797),a=n(8059);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var i=n.__data__;if(!o||i.length<199)return i.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(i)}return n.set(e,t),this.size=n.size,this}},7907:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(n){}try{return e+""}catch(n){}}return""}},9231:function(e){e.exports=function(e,t){return e===t||e!==e&&t!==t}},4963:function(e,t,n){var r=n(4906),o=n(3141),a=Object.prototype,i=a.hasOwnProperty,s=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return o(e)&&i.call(e,"callee")&&!s.call(e,"callee")};e.exports=l},3629:function(e){var t=Array.isArray;e.exports=t},1473:function(e,t,n){var r=n(4786),o=n(4635);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},5174:function(e,t,n){e=n.nmd(e);var r=n(7009),o=n(9488),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,s=i&&i.exports===a?r.Buffer:void 0,l=(s?s.isBuffer:void 0)||o;e.exports=l},8111:function(e,t,n){var r=n(1848);e.exports=function(e,t){return r(e,t)}},4786:function(e,t,n){var r=n(9066),o=n(8092);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},4635:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},8092:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3141:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},9102:function(e,t,n){var r=n(8150),o=n(6194),a=n(9494),i=a&&a.isTypedArray,s=i?o(i):r;e.exports=s},2742:function(e,t,n){var r=n(7538),o=n(3654),a=n(1473);e.exports=function(e){return a(e)?r(e):o(e)}},8174:function(e){e.exports=function(){return[]}},9488:function(e){e.exports=function(){return!1}},888:function(e,t,n){"use strict";var r=n(9047);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,i){if(i!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},2007:function(e,t,n){e.exports=n(888)()},9047:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},6962:function(e,t){"use strict";var n=Object.prototype.hasOwnProperty;function r(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(t){return null}}function o(e){try{return encodeURIComponent(e)}catch(t){return null}}t.stringify=function(e,t){t=t||"";var r,a,i=[];for(a in"string"!==typeof t&&(t="?"),e)if(n.call(e,a)){if((r=e[a])||null!==r&&undefined!==r&&!isNaN(r)||(r=""),a=o(a),r=o(r),null===a||null===r)continue;i.push(a+"="+r)}return i.length?t+i.join("&"):""},t.parse=function(e){for(var t,n=/([^=?#&]+)=?([^&]*)/g,o={};t=n.exec(e);){var a=r(t[1]),i=r(t[2]);null===a||null===i||a in o||(o[a]=i)}return o}},4463:function(e,t,n){"use strict";var r=n(2791),o=n(5296);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,s={};function l(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(s[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){v[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){v[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){v[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){v[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){v[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=v.hasOwnProperty(t)?v[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var _=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),w=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),N=Symbol.for("react.context"),j=Symbol.for("react.forward_ref"),O=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var A=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var I=Symbol.iterator;function L(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=I&&e[I]||e["@@iterator"])?e:null}var D,F=Object.assign;function z(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var U=!1;function B(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var o=c.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,s=a.length-1;1<=i&&0<=s&&o[i]!==a[s];)s--;for(;1<=i&&0<=s;i--,s--)if(o[i]!==a[s]){if(1!==i||1!==s)do{if(i--,0>--s||o[i]!==a[s]){var l="\n"+o[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=i&&0<=s);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?z(e):""}function M(e){switch(e.tag){case 5:return z(e.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case w:return"Portal";case E:return"Profiler";case S:return"StrictMode";case O:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case j:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case P:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return W(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===S?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function G(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Q(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function J(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Y(e,t){J(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&G(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function ae(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ge=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _e=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,ke=null,Se=null;function Ee(e){if(e=_o(e)){if("function"!==typeof we)throw Error(a(280));var t=e.stateNode;t&&(t=wo(t),we(e.stateNode,e.type,t))}}function Ce(e){ke?Se?Se.push(e):Se=[e]:ke=e}function Ne(){if(ke){var e=ke,t=Se;if(Se=ke=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function je(e,t){return e(t)}function Oe(){}var Te=!1;function Pe(e,t,n){if(Te)return e(t,n);Te=!0;try{return je(e,t,n)}finally{Te=!1,(null!==ke||null!==Se)&&(Oe(),Ne())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wo(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Ae=!1;if(u)try{var Ie={};Object.defineProperty(Ie,"passive",{get:function(){Ae=!0}}),window.addEventListener("test",Ie,Ie),window.removeEventListener("test",Ie,Ie)}catch(ue){Ae=!1}function Le(e,t,n,r,o,a,i,s,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var De=!1,Fe=null,ze=!1,Ue=null,Be={onError:function(e){De=!0,Fe=e}};function Me(e,t,n,r,o,a,i,s,l){De=!1,Fe=null,Le.apply(Be,arguments)}function We(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(We(e)!==e)throw Error(a(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=We(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ve(o),e;if(i===r)return Ve(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Ke=o.unstable_scheduleCallback,Ge=o.unstable_cancelCallback,Qe=o.unstable_shouldYield,Xe=o.unstable_requestPaint,Je=o.unstable_now,Ye=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~o;0!==s?r=dt(s):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function _t(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,wt,kt,St,Et,Ct=!1,Nt=[],jt=null,Ot=null,Tt=null,Pt=new Map,Rt=new Map,At=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lt(e,t){switch(e){case"focusin":case"focusout":jt=null;break;case"dragenter":case"dragleave":Ot=null;break;case"mouseover":case"mouseout":Tt=null;break;case"pointerover":case"pointerout":Pt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Dt(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=_o(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Ft(e){var t=bo(e.target);if(null!==t){var n=We(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Et(e.priority,(function(){kt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function zt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=_o(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);_e=r,n.target.dispatchEvent(r),_e=null,t.shift()}return!0}function Ut(e,t,n){zt(e)&&n.delete(t)}function Bt(){Ct=!1,null!==jt&&zt(jt)&&(jt=null),null!==Ot&&zt(Ot)&&(Ot=null),null!==Tt&&zt(Tt)&&(Tt=null),Pt.forEach(Ut),Rt.forEach(Ut)}function Mt(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Bt)))}function Wt(e){function t(t){return Mt(t,e)}if(0<Nt.length){Mt(Nt[0],e);for(var n=1;n<Nt.length;n++){var r=Nt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==jt&&Mt(jt,e),null!==Ot&&Mt(Ot,e),null!==Tt&&Mt(Tt,e),Pt.forEach(t),Rt.forEach(t),n=0;n<At.length;n++)(r=At[n]).blockedOn===e&&(r.blockedOn=null);for(;0<At.length&&null===(n=At[0]).blockedOn;)Ft(n),null===n.blockedOn&&At.shift()}var Ht=_.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=1,Kt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function qt(e,t,n,r){var o=bt,a=Ht.transition;Ht.transition=null;try{bt=4,Kt(e,t,n,r)}finally{bt=o,Ht.transition=a}}function Kt(e,t,n,r){if(Vt){var o=Qt(e,t,n,r);if(null===o)Vr(e,t,r,Gt,n),Lt(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return jt=Dt(jt,e,t,n,r,o),!0;case"dragenter":return Ot=Dt(Ot,e,t,n,r,o),!0;case"mouseover":return Tt=Dt(Tt,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Pt.set(a,Dt(Pt.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Rt.set(a,Dt(Rt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Lt(e,r),4&t&&-1<It.indexOf(e)){for(;null!==o;){var a=_o(o);if(null!==a&&xt(a),null===(a=Qt(e,t,n,r))&&Vr(e,t,r,Gt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Vr(e,t,r,null,n)}}var Gt=null;function Qt(e,t,n,r){if(Gt=null,null!==(e=bo(e=xe(r))))if(null===(t=We(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Gt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ye()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Jt=null,Yt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Yt,r=n.length,o="value"in Jt?Jt.value:Jt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=on(cn),dn=F({},cn,{view:0,detail:0}),fn=on(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),hn=on(pn),mn=on(F({},pn,{dataTransfer:0})),vn=on(F({},dn,{relatedTarget:0})),gn=on(F({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=F({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(yn),_n=on(F({},cn,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function En(){return Sn}var Cn=F({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=on(Cn),jn=on(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),On=on(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Tn=on(F({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Pn=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=on(Pn),An=[9,13,27,32],In=u&&"CompositionEvent"in window,Ln=null;u&&"documentMode"in document&&(Ln=document.documentMode);var Dn=u&&"TextEvent"in window&&!Ln,Fn=u&&(!In||Ln&&8<Ln&&11>=Ln),zn=String.fromCharCode(32),Un=!1;function Bn(e,t){switch(e){case"keyup":return-1!==An.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Wn=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){Ce(r),0<(t=qr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Kn=null;function Gn(e){zr(e,0)}function Qn(e){if(K(xo(e)))return e}function Xn(e,t){if("change"===e)return t}var Jn=!1;if(u){var Yn;if(u){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Yn=Zn}else Yn=!1;Jn=Yn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Kn=qn=null)}function nr(e){if("value"===e.propertyName&&Qn(Kn)){var t=[];$n(t,Kn,e,xe(e)),Pe(Gn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Kn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Qn(Kn)}function ar(e,t){if("click"===e)return Qn(t)}function ir(e,t){if("input"===e||"change"===e)return Qn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!sr(e[o],t[o]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=G();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=G((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=ur(n,a);var i=ur(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=u&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,br=!1;function _r(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==G(r)||("selectionStart"in(r=vr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=qr(gr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},kr={},Sr={};function Er(e){if(kr[e])return kr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Sr)return kr[e]=n[t];return e}u&&(Sr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var Cr=Er("animationend"),Nr=Er("animationiteration"),jr=Er("animationstart"),Or=Er("transitionend"),Tr=new Map,Pr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Tr.set(e,t),l(t,[e])}for(var Ar=0;Ar<Pr.length;Ar++){var Ir=Pr[Ar];Rr(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Rr(Cr,"onAnimationEnd"),Rr(Nr,"onAnimationIteration"),Rr(jr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Or,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Lr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,s,l,c){if(Me.apply(this,arguments),De){if(!De)throw Error(a(198));var u=Fe;De=!1,Fe=null,ze||(ze=!0,Ue=u)}}(r,t,void 0,e),e.currentTarget=null}function zr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,c=s.currentTarget;if(s=s.listener,l!==a&&o.isPropagationStopped())break e;Fr(o,s,c),a=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,c=s.currentTarget,s=s.listener,l!==a&&o.isPropagationStopped())break e;Fr(o,s,c),a=l}}}if(ze)throw e=Ue,ze=!1,Ue=null,e}function Ur(e,t){var n=t[vo];void 0===n&&(n=t[vo]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Mr="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Mr]){e[Mr]=!0,i.forEach((function(t){"selectionchange"!==t&&(Dr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Mr]||(t[Mr]=!0,Br("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Xt(t)){case 1:var o=$t;break;case 4:o=qt;break;default:o=Kt}n=o.bind(null,t,n,e),o=void 0,!Ae||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Vr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===o||8===s.nodeType&&s.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var l=i.tag;if((3===l||4===l)&&((l=i.stateNode.containerInfo)===o||8===l.nodeType&&l.parentNode===o))return;i=i.return}for(;null!==s;){if(null===(i=bo(s)))return;if(5===(l=i.tag)||6===l){r=a=i;continue e}s=s.parentNode}}r=r.return}Pe((function(){var r=a,o=xe(n),i=[];e:{var s=Tr.get(e);if(void 0!==s){var l=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Nn;break;case"focusin":c="focus",l=vn;break;case"focusout":c="blur",l=vn;break;case"beforeblur":case"afterblur":l=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=On;break;case Cr:case Nr:case jr:l=gn;break;case Or:l=Tn;break;case"scroll":l=fn;break;case"wheel":l=Rn;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=jn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==s?s+"Capture":null:s;u=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Re(h,f))&&u.push($r(h,m,p)))),d)break;h=h.return}0<u.length&&(s=new l(s,c,null,n,o),i.push({event:s,listeners:u}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===_e||!(c=n.relatedTarget||n.fromElement)||!bo(c)&&!c[mo])&&(l||s)&&(s=o.window===o?o:(s=o.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(c=(c=n.relatedTarget||n.toElement)?bo(c):null)&&(c!==(d=We(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(l=null,c=r),l!==c)){if(u=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=jn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==l?s:xo(l),p=null==c?s:xo(c),(s=new u(m,h+"leave",l,n,o)).target=d,s.relatedTarget=p,m=null,bo(o)===r&&((u=new u(f,h+"enter",c,n,o)).target=p,u.relatedTarget=d,m=u),d=m,l&&c)e:{for(f=c,h=0,p=u=l;p;p=Kr(p))h++;for(p=0,m=f;m;m=Kr(m))p++;for(;0<h-p;)u=Kr(u),h--;for(;0<p-h;)f=Kr(f),p--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=Kr(u),f=Kr(f)}u=null}else u=null;null!==l&&Gr(i,s,l,u,!1),null!==c&&null!==d&&Gr(i,d,c,u,!0)}if("select"===(l=(s=r?xo(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var v=Xn;else if(Vn(s))if(Jn)v=ir;else{v=or;var g=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(v=ar);switch(v&&(v=v(e,r))?$n(i,v,n,o):(g&&g(e,s,r),"focusout"===e&&(g=s._wrapperState)&&g.controlled&&"number"===s.type&&ee(s,"number",s.value)),g=r?xo(r):window,e){case"focusin":(Vn(g)||"true"===g.contentEditable)&&(vr=g,gr=r,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,_r(i,n,o);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":_r(i,n,o)}var y;if(In)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Wn?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Wn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Wn&&(y=en()):(Yt="value"in(Jt=o)?Jt.value:Jt.textContent,Wn=!0)),0<(g=qr(r,b)).length&&(b=new _n(b,e,null,n,o),i.push({event:b,listeners:g}),y?b.data=y:null!==(y=Mn(n))&&(b.data=y))),(y=Dn?function(e,t){switch(e){case"compositionend":return Mn(t);case"keypress":return 32!==t.which?null:(Un=!0,zn);case"textInput":return(e=t.data)===zn&&Un?null:e;default:return null}}(e,n):function(e,t){if(Wn)return"compositionend"===e||!In&&Bn(e,t)?(e=en(),Zt=Yt=Jt=null,Wn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(o=new _n("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}zr(i,t)}))}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Re(e,n))&&r.unshift($r(e,a,o)),null!=(a=Re(e,t))&&r.push($r(e,a,o))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Gr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,c=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==c&&(s=c,o?null!=(l=Re(n,a))&&i.unshift($r(n,l,s)):o||null!=(l=Re(n,a))&&i.push($r(n,l,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Qr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Jr(e){return("string"===typeof e?e:""+e).replace(Qr,"\n").replace(Xr,"")}function Yr(e,t,n){if(t=Jr(t),Jr(e)!==t&&n)throw Error(a(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,ao="function"===typeof Promise?Promise:void 0,io="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ao?function(e){return ao.resolve(null).then(e).catch(so)}:ro;function so(e){setTimeout((function(){throw e}))}function lo(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Wt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Wt(t)}function co(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function uo(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,mo="__reactContainer$"+fo,vo="__reactEvents$"+fo,go="__reactListeners$"+fo,yo="__reactHandles$"+fo;function bo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mo]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=uo(e);null!==e;){if(n=e[po])return n;e=uo(e)}return t}n=(e=n).parentNode}return null}function _o(e){return!(e=e[po]||e[mo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function wo(e){return e[ho]||null}var ko=[],So=-1;function Eo(e){return{current:e}}function Co(e){0>So||(e.current=ko[So],ko[So]=null,So--)}function No(e,t){So++,ko[So]=e.current,e.current=t}var jo={},Oo=Eo(jo),To=Eo(!1),Po=jo;function Ro(e,t){var n=e.type.contextTypes;if(!n)return jo;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Ao(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Io(){Co(To),Co(Oo)}function Lo(e,t,n){if(Oo.current!==jo)throw Error(a(168));No(Oo,t),No(To,n)}function Do(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,H(e)||"Unknown",o));return F({},n,r)}function Fo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||jo,Po=Oo.current,No(Oo,e),No(To,To.current),!0}function zo(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Do(e,t,Po),r.__reactInternalMemoizedMergedChildContext=e,Co(To),Co(Oo),No(Oo,e)):Co(To),No(To,n)}var Uo=null,Bo=!1,Mo=!1;function Wo(e){null===Uo?Uo=[e]:Uo.push(e)}function Ho(){if(!Mo&&null!==Uo){Mo=!0;var e=0,t=bt;try{var n=Uo;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Uo=null,Bo=!1}catch(o){throw null!==Uo&&(Uo=Uo.slice(e+1)),Ke(Ze,Ho),o}finally{bt=t,Mo=!1}}return null}var Vo=[],$o=0,qo=null,Ko=0,Go=[],Qo=0,Xo=null,Jo=1,Yo="";function Zo(e,t){Vo[$o++]=Ko,Vo[$o++]=qo,qo=e,Ko=t}function ea(e,t,n){Go[Qo++]=Jo,Go[Qo++]=Yo,Go[Qo++]=Xo,Xo=e;var r=Jo;e=Yo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Jo=1<<32-it(t)+o|n<<o|r,Yo=a+e}else Jo=1<<a|n<<o|r,Yo=e}function ta(e){null!==e.return&&(Zo(e,1),ea(e,1,0))}function na(e){for(;e===qo;)qo=Vo[--$o],Vo[$o]=null,Ko=Vo[--$o],Vo[$o]=null;for(;e===Xo;)Xo=Go[--Qo],Go[Qo]=null,Yo=Go[--Qo],Go[Qo]=null,Jo=Go[--Qo],Go[Qo]=null}var ra=null,oa=null,aa=!1,ia=null;function sa(e,t){var n=Pc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function la(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=co(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Xo?{id:Jo,overflow:Yo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Pc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function ca(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ua(e){if(aa){var t=oa;if(t){var n=t;if(!la(e,t)){if(ca(e))throw Error(a(418));t=co(n.nextSibling);var r=ra;t&&la(e,t)?sa(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(ca(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function fa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(ca(e))throw pa(),Error(a(418));for(;t;)sa(e,t),t=co(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=co(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?co(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=oa;e;)e=co(e.nextSibling)}function ha(){oa=ra=null,aa=!1}function ma(e){null===ia?ia=[e]:ia.push(e)}var va=_.ReactCurrentBatchConfig;function ga(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}var ya=Eo(null),ba=null,_a=null,xa=null;function wa(){xa=_a=ba=null}function ka(e){var t=ya.current;Co(ya),e._currentValue=t}function Sa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ea(e,t){ba=e,xa=_a=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(_s=!0),e.firstContext=null)}function Ca(e){var t=e._currentValue;if(xa!==e)if(e={context:e,memoizedValue:t,next:null},null===_a){if(null===ba)throw Error(a(308));_a=e,ba.dependencies={lanes:0,firstContext:e}}else _a=_a.next=e;return t}var Na=null;function ja(e){null===Na?Na=[e]:Na.push(e)}function Oa(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,ja(t)):(n.next=o.next,o.next=n),t.interleaved=n,Ta(e,r)}function Ta(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Pa=!1;function Ra(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Aa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ia(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function La(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&jl)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Ta(e,n)}return null===(o=r.interleaved)?(t.next=t,ja(r)):(t.next=o.next,o.next=t),r.interleaved=t,Ta(e,n)}function Da(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Fa(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function za(e,t,n,r){var o=e.updateQueue;Pa=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,s=o.shared.pending;if(null!==s){o.shared.pending=null;var l=s,c=l.next;l.next=null,null===i?a=c:i.next=c,i=l;var u=e.alternate;null!==u&&((s=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===s?u.firstBaseUpdate=c:s.next=c,u.lastBaseUpdate=l))}if(null!==a){var d=o.baseState;for(i=0,u=c=l=null,s=a;;){var f=s.lane,p=s.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,m=s;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=F({},d,f);break e;case 2:Pa=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[s]:f.push(s))}else p={eventTime:p,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===u?(c=u=p,l=d):u=u.next=p,i|=f;if(null===(s=s.next)){if(null===(s=o.shared.pending))break;s=(f=s).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===u&&(l=d),o.baseState=l,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Dl|=i,e.lanes=i,e.memoizedState=d}}function Ua(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var Ba=(new r.Component).refs;function Ma(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var Wa={isMounted:function(e){return!!(e=e._reactInternals)&&We(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),a=Ia(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=La(e,a,o))&&(nc(t,e,o,r),Da(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),a=Ia(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=La(e,a,o))&&(nc(t,e,o,r),Da(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),o=Ia(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=La(e,o,r))&&(nc(t,e,r,n),Da(t,e,r))}};function Ha(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(o,a))}function Va(e,t,n){var r=!1,o=jo,a=t.contextType;return"object"===typeof a&&null!==a?a=Ca(a):(o=Ao(t)?Po:Oo.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ro(e,o):jo),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=Wa,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function $a(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Wa.enqueueReplaceState(t,t.state,null)}function qa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=Ba,Ra(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Ca(a):(a=Ao(t)?Po:Oo.current,o.context=Ro(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(Ma(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&Wa.enqueueReplaceState(o,o.state,null),za(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function Ka(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;t===Ba&&(t=o.refs={}),null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function Ga(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Qa(e){return(0,e._init)(e._payload)}function Xa(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Ac(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fc(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===R&&Qa(a)===t.type)?((r=o(t,n.props)).ref=Ka(e,t,n),r.return=e,r):((r=Ic(n.type,n.key,n.props,null,e.mode,r)).ref=Ka(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=zc(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Lc(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Ic(t.type,t.key,t.props,null,e.mode,n)).ref=Ka(e,null,t),n.return=e,n;case w:return(t=zc(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||L(t))return(t=Lc(t,e.mode,n,null)).return=e,t;Ga(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===o?c(e,t,n,r):null;case w:return n.key===o?u(e,t,n,r):null;case R:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||L(n))return null!==o?null:d(e,t,n,r,null);Ga(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case R:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||L(r))return d(t,e=e.get(n)||null,r,o,null);Ga(t,r)}return null}function m(o,a,s,l){for(var c=null,u=null,d=a,m=a=0,v=null;null!==d&&m<s.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var g=p(o,d,s[m],l);if(null===g){null===d&&(d=v);break}e&&d&&null===g.alternate&&t(o,d),a=i(g,a,m),null===u?c=g:u.sibling=g,u=g,d=v}if(m===s.length)return n(o,d),aa&&Zo(o,m),c;if(null===d){for(;m<s.length;m++)null!==(d=f(o,s[m],l))&&(a=i(d,a,m),null===u?c=d:u.sibling=d,u=d);return aa&&Zo(o,m),c}for(d=r(o,d);m<s.length;m++)null!==(v=h(d,o,m,s[m],l))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),a=i(v,a,m),null===u?c=v:u.sibling=v,u=v);return e&&d.forEach((function(e){return t(o,e)})),aa&&Zo(o,m),c}function v(o,s,l,c){var u=L(l);if("function"!==typeof u)throw Error(a(150));if(null==(l=u.call(l)))throw Error(a(151));for(var d=u=null,m=s,v=s=0,g=null,y=l.next();null!==m&&!y.done;v++,y=l.next()){m.index>v?(g=m,m=null):g=m.sibling;var b=p(o,m,y.value,c);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(o,m),s=i(b,s,v),null===d?u=b:d.sibling=b,d=b,m=g}if(y.done)return n(o,m),aa&&Zo(o,v),u;if(null===m){for(;!y.done;v++,y=l.next())null!==(y=f(o,y.value,c))&&(s=i(y,s,v),null===d?u=y:d.sibling=y,d=y);return aa&&Zo(o,v),u}for(m=r(o,m);!y.done;v++,y=l.next())null!==(y=h(m,o,v,y.value,c))&&(e&&null!==y.alternate&&m.delete(null===y.key?v:y.key),s=i(y,s,v),null===d?u=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(o,e)})),aa&&Zo(o,v),u}return function e(r,a,i,l){if("object"===typeof i&&null!==i&&i.type===k&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case x:e:{for(var c=i.key,u=a;null!==u;){if(u.key===c){if((c=i.type)===k){if(7===u.tag){n(r,u.sibling),(a=o(u,i.props.children)).return=r,r=a;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===R&&Qa(c)===u.type){n(r,u.sibling),(a=o(u,i.props)).ref=Ka(r,u,i),a.return=r,r=a;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===k?((a=Lc(i.props.children,r.mode,l,i.key)).return=r,r=a):((l=Ic(i.type,i.key,i.props,null,r.mode,l)).ref=Ka(r,a,i),l.return=r,r=l)}return s(r);case w:e:{for(u=i.key;null!==a;){if(a.key===u){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=zc(i,r.mode,l)).return=r,r=a}return s(r);case R:return e(r,a,(u=i._init)(i._payload),l)}if(te(i))return m(r,a,i,l);if(L(i))return v(r,a,i,l);Ga(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Fc(i,r.mode,l)).return=r,r=a),s(r)):n(r,a)}}var Ja=Xa(!0),Ya=Xa(!1),Za={},ei=Eo(Za),ti=Eo(Za),ni=Eo(Za);function ri(e){if(e===Za)throw Error(a(174));return e}function oi(e,t){switch(No(ni,t),No(ti,e),No(ei,Za),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Co(ei),No(ei,t)}function ai(){Co(ei),Co(ti),Co(ni)}function ii(e){ri(ni.current);var t=ri(ei.current),n=le(t,e.type);t!==n&&(No(ti,e),No(ei,n))}function si(e){ti.current===e&&(Co(ei),Co(ti))}var li=Eo(0);function ci(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ui=[];function di(){for(var e=0;e<ui.length;e++)ui[e]._workInProgressVersionPrimary=null;ui.length=0}var fi=_.ReactCurrentDispatcher,pi=_.ReactCurrentBatchConfig,hi=0,mi=null,vi=null,gi=null,yi=!1,bi=!1,_i=0,xi=0;function wi(){throw Error(a(321))}function ki(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function Si(e,t,n,r,o,i){if(hi=i,mi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,fi.current=null===e||null===e.memoizedState?ss:ls,e=n(r,o),bi){i=0;do{if(bi=!1,_i=0,25<=i)throw Error(a(301));i+=1,gi=vi=null,t.updateQueue=null,fi.current=cs,e=n(r,o)}while(bi)}if(fi.current=is,t=null!==vi&&null!==vi.next,hi=0,gi=vi=mi=null,yi=!1,t)throw Error(a(300));return e}function Ei(){var e=0!==_i;return _i=0,e}function Ci(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===gi?mi.memoizedState=gi=e:gi=gi.next=e,gi}function Ni(){if(null===vi){var e=mi.alternate;e=null!==e?e.memoizedState:null}else e=vi.next;var t=null===gi?mi.memoizedState:gi.next;if(null!==t)gi=t,vi=e;else{if(null===e)throw Error(a(310));e={memoizedState:(vi=e).memoizedState,baseState:vi.baseState,baseQueue:vi.baseQueue,queue:vi.queue,next:null},null===gi?mi.memoizedState=gi=e:gi=gi.next=e}return gi}function ji(e,t){return"function"===typeof t?t(e):t}function Oi(e){var t=Ni(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=vi,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var l=s=null,c=null,u=i;do{var d=u.lane;if((hi&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(l=c=f,s=r):c=c.next=f,mi.lanes|=d,Dl|=d}u=u.next}while(null!==u&&u!==i);null===c?s=r:c.next=l,sr(r,t.memoizedState)||(_s=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,mi.lanes|=i,Dl|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ti(e){var t=Ni(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var s=o=o.next;do{i=e(i,s.action),s=s.next}while(s!==o);sr(i,t.memoizedState)||(_s=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Pi(){}function Ri(e,t){var n=mi,r=Ni(),o=t(),i=!sr(r.memoizedState,o);if(i&&(r.memoizedState=o,_s=!0),r=r.queue,Vi(Li.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==gi&&1&gi.memoizedState.tag){if(n.flags|=2048,Ui(9,Ii.bind(null,n,r,o,t),void 0,null),null===Ol)throw Error(a(349));0!==(30&hi)||Ai(n,t,o)}return o}function Ai(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ii(e,t,n,r){t.value=n,t.getSnapshot=r,Di(t)&&Fi(e)}function Li(e,t,n){return n((function(){Di(t)&&Fi(e)}))}function Di(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Fi(e){var t=Ta(e,1);null!==t&&nc(t,e,1,-1)}function zi(e){var t=Ci();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ji,lastRenderedState:e},t.queue=e,e=e.dispatch=ns.bind(null,mi,e),[t.memoizedState,e]}function Ui(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=mi.updateQueue)?(t={lastEffect:null,stores:null},mi.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Bi(){return Ni().memoizedState}function Mi(e,t,n,r){var o=Ci();mi.flags|=e,o.memoizedState=Ui(1|t,n,void 0,void 0===r?null:r)}function Wi(e,t,n,r){var o=Ni();r=void 0===r?null:r;var a=void 0;if(null!==vi){var i=vi.memoizedState;if(a=i.destroy,null!==r&&ki(r,i.deps))return void(o.memoizedState=Ui(t,n,a,r))}mi.flags|=e,o.memoizedState=Ui(1|t,n,a,r)}function Hi(e,t){return Mi(8390656,8,e,t)}function Vi(e,t){return Wi(2048,8,e,t)}function $i(e,t){return Wi(4,2,e,t)}function qi(e,t){return Wi(4,4,e,t)}function Ki(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Gi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Wi(4,4,Ki.bind(null,t,e),n)}function Qi(){}function Xi(e,t){var n=Ni();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ki(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ji(e,t){var n=Ni();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ki(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Yi(e,t,n){return 0===(21&hi)?(e.baseState&&(e.baseState=!1,_s=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),mi.lanes|=n,Dl|=n,e.baseState=!0),t)}function Zi(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=pi.transition;pi.transition={};try{e(!1),t()}finally{bt=n,pi.transition=r}}function es(){return Ni().memoizedState}function ts(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rs(e))os(t,n);else if(null!==(n=Oa(e,t,n,r))){nc(n,e,r,ec()),as(n,t,r)}}function ns(e,t,n){var r=tc(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rs(e))os(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=a(i,n);if(o.hasEagerState=!0,o.eagerState=s,sr(s,i)){var l=t.interleaved;return null===l?(o.next=o,ja(t)):(o.next=l.next,l.next=o),void(t.interleaved=o)}}catch(c){}null!==(n=Oa(e,t,o,r))&&(nc(n,e,r,o=ec()),as(n,t,r))}}function rs(e){var t=e.alternate;return e===mi||null!==t&&t===mi}function os(e,t){bi=yi=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function as(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var is={readContext:Ca,useCallback:wi,useContext:wi,useEffect:wi,useImperativeHandle:wi,useInsertionEffect:wi,useLayoutEffect:wi,useMemo:wi,useReducer:wi,useRef:wi,useState:wi,useDebugValue:wi,useDeferredValue:wi,useTransition:wi,useMutableSource:wi,useSyncExternalStore:wi,useId:wi,unstable_isNewReconciler:!1},ss={readContext:Ca,useCallback:function(e,t){return Ci().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:Hi,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Mi(4194308,4,Ki.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Mi(4,2,e,t)},useMemo:function(e,t){var n=Ci();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ci();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ts.bind(null,mi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ci().memoizedState=e},useState:zi,useDebugValue:Qi,useDeferredValue:function(e){return Ci().memoizedState=e},useTransition:function(){var e=zi(!1),t=e[0];return e=Zi.bind(null,e[1]),Ci().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=mi,o=Ci();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Ol)throw Error(a(349));0!==(30&hi)||Ai(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Hi(Li.bind(null,r,i,e),[e]),r.flags|=2048,Ui(9,Ii.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ci(),t=Ol.identifierPrefix;if(aa){var n=Yo;t=":"+t+"R"+(n=(Jo&~(1<<32-it(Jo)-1)).toString(32)+n),0<(n=_i++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=xi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ls={readContext:Ca,useCallback:Xi,useContext:Ca,useEffect:Vi,useImperativeHandle:Gi,useInsertionEffect:$i,useLayoutEffect:qi,useMemo:Ji,useReducer:Oi,useRef:Bi,useState:function(){return Oi(ji)},useDebugValue:Qi,useDeferredValue:function(e){return Yi(Ni(),vi.memoizedState,e)},useTransition:function(){return[Oi(ji)[0],Ni().memoizedState]},useMutableSource:Pi,useSyncExternalStore:Ri,useId:es,unstable_isNewReconciler:!1},cs={readContext:Ca,useCallback:Xi,useContext:Ca,useEffect:Vi,useImperativeHandle:Gi,useInsertionEffect:$i,useLayoutEffect:qi,useMemo:Ji,useReducer:Ti,useRef:Bi,useState:function(){return Ti(ji)},useDebugValue:Qi,useDeferredValue:function(e){var t=Ni();return null===vi?t.memoizedState=e:Yi(t,vi.memoizedState,e)},useTransition:function(){return[Ti(ji)[0],Ni().memoizedState]},useMutableSource:Pi,useSyncExternalStore:Ri,useId:es,unstable_isNewReconciler:!1};function us(e,t){try{var n="",r=t;do{n+=M(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function ds(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fs(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var ps="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Ia(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vl||(Vl=!0,$l=r),fs(0,t)},n}function ms(e,t,n){(n=Ia(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){fs(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){fs(0,t),"function"!==typeof r&&(null===ql?ql=new Set([this]):ql.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function vs(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ps;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Ec.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ia(-1,1)).tag=2,La(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var bs=_.ReactCurrentOwner,_s=!1;function xs(e,t,n,r){t.child=null===e?Ya(t,null,n,r):Ja(t,e.child,n,r)}function ws(e,t,n,r,o){n=n.render;var a=t.ref;return Ea(t,o),r=Si(e,t,n,r,a,o),n=Ei(),null===e||_s?(aa&&n&&ta(t),t.flags|=1,xs(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vs(e,t,o))}function ks(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Rc(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Ic(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Ss(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(i,r)&&e.ref===t.ref)return Vs(e,t,o)}return t.flags|=1,(e=Ac(a,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(lr(a,r)&&e.ref===t.ref){if(_s=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,Vs(e,t,o);0!==(131072&e.flags)&&(_s=!0)}}return Ns(e,t,n,r,o)}function Es(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},No(Al,Rl),Rl|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,No(Al,Rl),Rl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,No(Al,Rl),Rl|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,No(Al,Rl),Rl|=r;return xs(e,t,o,n),t.child}function Cs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ns(e,t,n,r,o){var a=Ao(n)?Po:Oo.current;return a=Ro(t,a),Ea(t,o),n=Si(e,t,n,r,a,o),r=Ei(),null===e||_s?(aa&&r&&ta(t),t.flags|=1,xs(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Vs(e,t,o))}function js(e,t,n,r,o){if(Ao(n)){var a=!0;Fo(t)}else a=!1;if(Ea(t,o),null===t.stateNode)Hs(e,t),Va(t,n,r),qa(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var l=i.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ca(c):c=Ro(t,c=Ao(n)?Po:Oo.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==r||l!==c)&&$a(t,i,r,c),Pa=!1;var f=t.memoizedState;i.state=f,za(t,r,i,o),l=t.memoizedState,s!==r||f!==l||To.current||Pa?("function"===typeof u&&(Ma(t,n,u,r),l=t.memoizedState),(s=Pa||Ha(t,n,s,r,f,l,c))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=c,r=s):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Aa(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:ga(t.type,s),i.props=c,d=t.pendingProps,f=i.context,"object"===typeof(l=n.contextType)&&null!==l?l=Ca(l):l=Ro(t,l=Ao(n)?Po:Oo.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==d||f!==l)&&$a(t,i,r,l),Pa=!1,f=t.memoizedState,i.state=f,za(t,r,i,o);var h=t.memoizedState;s!==d||f!==h||To.current||Pa?("function"===typeof p&&(Ma(t,n,p,r),h=t.memoizedState),(c=Pa||Ha(t,n,c,r,f,h,l)||!1)?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,l),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=l,r=c):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Os(e,t,n,r,a,o)}function Os(e,t,n,r,o,a){Cs(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&zo(t,n,!1),Vs(e,t,a);r=t.stateNode,bs.current=t;var s=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=Ja(t,e.child,null,a),t.child=Ja(t,null,s,a)):xs(e,t,s,a),t.memoizedState=r.state,o&&zo(t,n,!0),t.child}function Ts(e){var t=e.stateNode;t.pendingContext?Lo(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Lo(0,t.context,!1),oi(e,t.containerInfo)}function Ps(e,t,n,r,o){return ha(),ma(o),t.flags|=256,xs(e,t,n,r),t.child}var Rs,As,Is,Ls={dehydrated:null,treeContext:null,retryLane:0};function Ds(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,n){var r,o=t.pendingProps,i=li.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),No(li,1&i),null===e)return ua(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=o.children,e=o.fallback,s?(o=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&o)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Dc(l,o,0,null),e=Lc(e,o,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ds(n),t.memoizedState=Ls,e):zs(t,l));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,s){if(n)return 256&t.flags?(t.flags&=-257,Us(e,t,s,r=ds(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Dc({mode:"visible",children:r.children},o,0,null),(i=Lc(i,o,s,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&Ja(t,e.child,null,s),t.child.memoizedState=Ds(s),t.memoizedState=Ls,i);if(0===(1&t.mode))return Us(e,t,s,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var l=r.dgst;return r=l,Us(e,t,s,r=ds(i=Error(a(419)),r,void 0))}if(l=0!==(s&e.childLanes),_s||l){if(null!==(r=Ol)){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|s))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Ta(e,o),nc(r,e,o,-1))}return mc(),Us(e,t,s,r=ds(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Nc.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=co(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(Go[Qo++]=Jo,Go[Qo++]=Yo,Go[Qo++]=Xo,Jo=e.id,Yo=e.overflow,Xo=t),(t=zs(t,r.children)).flags|=4096,t)}(e,t,l,o,r,i,n);if(s){s=o.fallback,l=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:o.children};return 0===(1&l)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=c,t.deletions=null):(o=Ac(i,c)).subtreeFlags=14680064&i.subtreeFlags,null!==r?s=Ac(r,s):(s=Lc(s,l,n,null)).flags|=2,s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,l=null===(l=e.child.memoizedState)?Ds(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Ls,o}return e=(s=e.child).sibling,o=Ac(s,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function zs(e,t){return(t=Dc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Us(e,t,n,r){return null!==r&&ma(r),Ja(t,e.child,null,n),(e=zs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Sa(e.return,t,n)}function Ms(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Ws(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(xs(e,t,r.children,n),0!==(2&(r=li.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(No(li,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ci(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ms(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ci(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ms(t,!0,n,null,a);break;case"together":Ms(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Dl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Ac(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ac(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $s(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ks(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qs(t),null;case 1:case 17:return Ao(t.type)&&Io(),qs(t),null;case 3:return r=t.stateNode,ai(),Co(To),Co(Oo),di(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ia&&(ic(ia),ia=null))),qs(t),null;case 5:si(t);var o=ri(ni.current);if(n=t.type,null!==e&&null!=t.stateNode)As(e,t,n,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return qs(t),null}if(e=ri(ei.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[po]=t,r[ho]=i,e=0!==(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(o=0;o<Lr.length;o++)Ur(Lr[o],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":X(r,i),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ur("invalid",r);break;case"textarea":oe(r,i),Ur("invalid",r)}for(var l in ye(n,i),o=null,i)if(i.hasOwnProperty(l)){var c=i[l];"children"===l?"string"===typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,c,e),o=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,c,e),o=["children",""+c]):s.hasOwnProperty(l)&&null!=c&&"onScroll"===l&&Ur("scroll",r)}switch(n){case"input":q(r),Z(r,i,!0);break;case"textarea":q(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[po]=t,e[ho]=r,Rs(e,t),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),o=r;break;case"iframe":case"object":case"embed":Ur("load",e),o=r;break;case"video":case"audio":for(o=0;o<Lr.length;o++)Ur(Lr[o],e);o=r;break;case"source":Ur("error",e),o=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),o=r;break;case"details":Ur("toggle",e),o=r;break;case"input":X(e,r),o=Q(e,r),Ur("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=F({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Ur("invalid",e)}for(i in ye(n,o),c=o)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?ve(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===i?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(s.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Ur("scroll",e):null!=u&&b(e,i,u,l))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qs(t),null;case 6:if(e&&null!=t.stateNode)Is(0,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=ri(ni.current),ri(ei.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Yr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Yr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return qs(t),null;case 13:if(Co(li),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!==(1&t.mode)&&0===(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[po]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qs(t),i=!1}else null!==ia&&(ic(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&li.current)?0===Il&&(Il=3):mc())),null!==t.updateQueue&&(t.flags|=4),qs(t),null);case 4:return ai(),null===e&&Wr(t.stateNode.containerInfo),qs(t),null;case 10:return ka(t.type._context),qs(t),null;case 19:if(Co(li),null===(i=t.memoizedState))return qs(t),null;if(r=0!==(128&t.flags),null===(l=i.rendering))if(r)$s(i,!1);else{if(0!==Il||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ci(e))){for(t.flags|=128,$s(i,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(l=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return No(li,1&li.current|2),t.child}e=e.sibling}null!==i.tail&&Je()>Wl&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ci(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$s(i,!0),null===i.tail&&"hidden"===i.tailMode&&!l.alternate&&!aa)return qs(t),null}else 2*Je()-i.renderingStartTime>Wl&&1073741824!==n&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=i.last)?n.sibling=l:t.child=l,i.last=l)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Je(),t.sibling=null,n=li.current,No(li,r?1&n|2:1&n),t):(qs(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rl)&&(qs(t),6&t.subtreeFlags&&(t.flags|=8192)):qs(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Gs(e,t){switch(na(t),t.tag){case 1:return Ao(t.type)&&Io(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ai(),Co(To),Co(Oo),di(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return si(t),null;case 13:if(Co(li),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Co(li),null;case 4:return ai(),null;case 10:return ka(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Rs=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},As=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,ri(ei.current);var a,i=null;switch(n){case"input":o=Q(e,o),r=Q(e,r),i=[];break;case"select":o=F({},o,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(u in ye(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u){var l=o[u];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(s.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(l=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(null!=c||null!=l))if("style"===u)if(l){for(a in l)!l.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&l[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,l=l?l.__html:void 0,null!=c&&l!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(s.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ur("scroll",e),i||l===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Is=function(e,t,n,r){n!==r&&(t.flags|=4)};var Qs=!1,Xs=!1,Js="function"===typeof WeakSet?WeakSet:Set,Ys=null;function Zs(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Sc(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Sc(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&el(t,n,a)}o=o.next}while(o!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ol(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[vo],delete t[go],delete t[yo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function il(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||il(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function cl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var ul=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(s){}switch(n.tag){case 5:Xs||Zs(n,t);case 6:var r=ul,o=dl;ul=null,fl(e,t,n),dl=o,null!==(ul=r)&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ul.removeChild(n.stateNode));break;case 18:null!==ul&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?lo(e.parentNode,n):1===e.nodeType&&lo(e,n),Wt(e)):lo(ul,n.stateNode));break;case 4:r=ul,o=dl,ul=n.stateNode.containerInfo,dl=!0,fl(e,t,n),ul=r,dl=o;break;case 0:case 11:case 14:case 15:if(!Xs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&el(n,t,i),o=o.next}while(o!==r)}fl(e,t,n);break;case 1:if(!Xs&&(Zs(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Sc(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Xs=(r=Xs)||null!==n.memoizedState,fl(e,t,n),Xs=r):fl(e,t,n);break;default:fl(e,t,n)}}function hl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Js),t.forEach((function(t){var r=jc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:ul=l.stateNode,dl=!1;break e;case 3:case 4:ul=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===ul)throw Error(a(160));pl(i,s,o),ul=null,dl=!1;var c=o.alternate;null!==c&&(c.return=null),o.return=null}catch(u){Sc(o,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vl(t,e),t=t.sibling}function vl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),gl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(v){Sc(e,e.return,v)}try{nl(5,e,e.return)}catch(v){Sc(e,e.return,v)}}break;case 1:ml(t,e),gl(e),512&r&&null!==n&&Zs(n,n.return);break;case 5:if(ml(t,e),gl(e),512&r&&null!==n&&Zs(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(v){Sc(e,e.return,v)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,s=null!==n?n.memoizedProps:i,l=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===l&&"radio"===i.type&&null!=i.name&&J(o,i),be(l,s);var u=be(l,i);for(s=0;s<c.length;s+=2){var d=c[s],f=c[s+1];"style"===d?ve(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):b(o,d,f,u)}switch(l){case"input":Y(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(v){Sc(e,e.return,v)}}break;case 6:if(ml(t,e),gl(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){Sc(e,e.return,v)}}break;case 3:if(ml(t,e),gl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Wt(t.containerInfo)}catch(v){Sc(e,e.return,v)}break;case 4:default:ml(t,e),gl(e);break;case 13:ml(t,e),gl(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Ml=Je())),4&r&&hl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xs=(u=Xs)||d,ml(t,e),Xs=u):ml(t,e),gl(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Ys=e,d=e.child;null!==d;){for(f=Ys=d;null!==Ys;){switch(h=(p=Ys).child,p.tag){case 0:case 11:case 14:case 15:nl(4,p,p.return);break;case 1:Zs(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Sc(r,n,v)}}break;case 5:Zs(p,p.return);break;case 22:if(null!==p.memoizedState){xl(f);continue}}null!==h?(h.return=p,Ys=h):xl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,u?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(l=f.stateNode,s=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,l.style.display=me("display",s))}catch(v){Sc(e,e.return,v)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Sc(e,e.return,v)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),gl(e),4&r&&hl(e);case 21:}}function gl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(il(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),cl(e,sl(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;ll(e,sl(e),i);break;default:throw Error(a(161))}}catch(s){Sc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yl(e,t,n){Ys=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Ys;){var o=Ys,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Qs;if(!i){var s=o.alternate,l=null!==s&&null!==s.memoizedState||Xs;s=Qs;var c=Xs;if(Qs=i,(Xs=l)&&!c)for(Ys=o;null!==Ys;)l=(i=Ys).child,22===i.tag&&null!==i.memoizedState?wl(o):null!==l?(l.return=i,Ys=l):wl(o);for(;null!==a;)Ys=a,bl(a,t,n),a=a.sibling;Ys=o,Qs=s,Xs=c}_l(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Ys=a):_l(e)}}function _l(e){for(;null!==Ys;){var t=Ys;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xs||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xs)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ga(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Ua(t,i,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ua(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Wt(f)}}}break;default:throw Error(a(163))}Xs||512&t.flags&&ol(t)}catch(p){Sc(t,t.return,p)}}if(t===e){Ys=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ys=n;break}Ys=t.return}}function xl(e){for(;null!==Ys;){var t=Ys;if(t===e){Ys=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ys=n;break}Ys=t.return}}function wl(e){for(;null!==Ys;){var t=Ys;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Sc(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(l){Sc(t,o,l)}}var a=t.return;try{ol(t)}catch(l){Sc(t,a,l)}break;case 5:var i=t.return;try{ol(t)}catch(l){Sc(t,i,l)}}}catch(l){Sc(t,t.return,l)}if(t===e){Ys=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Ys=s;break}Ys=t.return}}var kl,Sl=Math.ceil,El=_.ReactCurrentDispatcher,Cl=_.ReactCurrentOwner,Nl=_.ReactCurrentBatchConfig,jl=0,Ol=null,Tl=null,Pl=0,Rl=0,Al=Eo(0),Il=0,Ll=null,Dl=0,Fl=0,zl=0,Ul=null,Bl=null,Ml=0,Wl=1/0,Hl=null,Vl=!1,$l=null,ql=null,Kl=!1,Gl=null,Ql=0,Xl=0,Jl=null,Yl=-1,Zl=0;function ec(){return 0!==(6&jl)?Je():-1!==Yl?Yl:Yl=Je()}function tc(e){return 0===(1&e.mode)?1:0!==(2&jl)&&0!==Pl?Pl&-Pl:null!==va.transition?(0===Zl&&(Zl=mt()),Zl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nc(e,t,n,r){if(50<Xl)throw Xl=0,Jl=null,Error(a(185));gt(e,n,r),0!==(2&jl)&&e===Ol||(e===Ol&&(0===(2&jl)&&(Fl|=n),4===Il&&sc(e,Pl)),rc(e,r),1===n&&0===jl&&0===(1&t.mode)&&(Wl=Je()+500,Bo&&Ho()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),s=1<<i,l=o[i];-1===l?0!==(s&n)&&0===(s&r)||(o[i]=pt(s,t)):l<=t&&(e.expiredLanes|=s),a&=~s}}(e,t);var r=ft(e,e===Ol?Pl:0);if(0===r)null!==n&&Ge(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ge(n),1===t)0===e.tag?function(e){Bo=!0,Wo(e)}(lc.bind(null,e)):Wo(lc.bind(null,e)),io((function(){0===(6&jl)&&Ho()})),n=null;else{switch(_t(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Oc(n,oc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function oc(e,t){if(Yl=-1,Zl=0,0!==(6&jl))throw Error(a(327));var n=e.callbackNode;if(wc()&&e.callbackNode!==n)return null;var r=ft(e,e===Ol?Pl:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vc(e,r);else{t=r;var o=jl;jl|=2;var i=hc();for(Ol===e&&Pl===t||(Hl=null,Wl=Je()+500,fc(e,t));;)try{yc();break}catch(l){pc(e,l)}wa(),El.current=i,jl=o,null!==Tl?t=0:(Ol=null,Pl=0,t=Il)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=ac(e,o))),1===t)throw n=Ll,fc(e,0),sc(e,r),rc(e,Je()),n;if(6===t)sc(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!sr(a(),o))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=vc(e,r))&&(0!==(i=ht(e))&&(r=i,t=ac(e,i))),1===t))throw n=Ll,fc(e,0),sc(e,r),rc(e,Je()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:xc(e,Bl,Hl);break;case 3:if(sc(e,r),(130023424&r)===r&&10<(t=Ml+500-Je())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(xc.bind(null,e,Bl,Hl),t);break}xc(e,Bl,Hl);break;case 4:if(sc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-it(r);i=1<<s,(s=t[s])>o&&(o=s),r&=~i}if(r=o,10<(r=(120>(r=Je()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Sl(r/1960))-r)){e.timeoutHandle=ro(xc.bind(null,e,Bl,Hl),r);break}xc(e,Bl,Hl);break;default:throw Error(a(329))}}}return rc(e,Je()),e.callbackNode===n?oc.bind(null,e):null}function ac(e,t){var n=Ul;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=vc(e,t))&&(t=Bl,Bl=n,null!==t&&ic(t)),e}function ic(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function sc(e,t){for(t&=~zl,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function lc(e){if(0!==(6&jl))throw Error(a(327));wc();var t=ft(e,0);if(0===(1&t))return rc(e,Je()),null;var n=vc(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ac(e,r))}if(1===n)throw n=Ll,fc(e,0),sc(e,t),rc(e,Je()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xc(e,Bl,Hl),rc(e,Je()),null}function cc(e,t){var n=jl;jl|=1;try{return e(t)}finally{0===(jl=n)&&(Wl=Je()+500,Bo&&Ho())}}function uc(e){null!==Gl&&0===Gl.tag&&0===(6&jl)&&wc();var t=jl;jl|=1;var n=Nl.transition,r=bt;try{if(Nl.transition=null,bt=1,e)return e()}finally{bt=r,Nl.transition=n,0===(6&(jl=t))&&Ho()}}function dc(){Rl=Al.current,Co(Al)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Tl)for(n=Tl.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Io();break;case 3:ai(),Co(To),Co(Oo),di();break;case 5:si(r);break;case 4:ai();break;case 13:case 19:Co(li);break;case 10:ka(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Ol=e,Tl=e=Ac(e.current,null),Pl=Rl=t,Il=0,Ll=null,zl=Fl=Dl=0,Bl=Ul=null,null!==Na){for(t=0;t<Na.length;t++)if(null!==(r=(n=Na[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}Na=null}return e}function pc(e,t){for(;;){var n=Tl;try{if(wa(),fi.current=is,yi){for(var r=mi.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}yi=!1}if(hi=0,gi=vi=mi=null,bi=!1,_i=0,Cl.current=null,null===n||null===n.return){Il=1,Ll=t,Tl=null;break}e:{var i=e,s=n.return,l=n,c=t;if(t=Pl,l.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gs(s);if(null!==h){h.flags&=-257,ys(h,s,l,0,t),1&h.mode&&vs(i,u,t),c=u;var m=(t=h).updateQueue;if(null===m){var v=new Set;v.add(c),t.updateQueue=v}else m.add(c);break e}if(0===(1&t)){vs(i,u,t),mc();break e}c=Error(a(426))}else if(aa&&1&l.mode){var g=gs(s);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),ys(g,s,l,0,t),ma(us(c,l));break e}}i=c=us(c,l),4!==Il&&(Il=2),null===Ul?Ul=[i]:Ul.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Fa(i,hs(0,c,t));break e;case 1:l=c;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===ql||!ql.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Fa(i,ms(i,l,t));break e}}i=i.return}while(null!==i)}_c(n)}catch(_){t=_,Tl===n&&null!==n&&(Tl=n=n.return);continue}break}}function hc(){var e=El.current;return El.current=is,null===e?is:e}function mc(){0!==Il&&3!==Il&&2!==Il||(Il=4),null===Ol||0===(268435455&Dl)&&0===(268435455&Fl)||sc(Ol,Pl)}function vc(e,t){var n=jl;jl|=2;var r=hc();for(Ol===e&&Pl===t||(Hl=null,fc(e,t));;)try{gc();break}catch(o){pc(e,o)}if(wa(),jl=n,El.current=r,null!==Tl)throw Error(a(261));return Ol=null,Pl=0,Il}function gc(){for(;null!==Tl;)bc(Tl)}function yc(){for(;null!==Tl&&!Qe();)bc(Tl)}function bc(e){var t=kl(e.alternate,e,Rl);e.memoizedProps=e.pendingProps,null===t?_c(e):Tl=t,Cl.current=null}function _c(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ks(n,t,Rl)))return void(Tl=n)}else{if(null!==(n=Gs(n,t)))return n.flags&=32767,void(Tl=n);if(null===e)return Il=6,void(Tl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Tl=t);Tl=t=e}while(null!==t);0===Il&&(Il=5)}function xc(e,t,n){var r=bt,o=Nl.transition;try{Nl.transition=null,bt=1,function(e,t,n,r){do{wc()}while(null!==Gl);if(0!==(6&jl))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Ol&&(Tl=Ol=null,Pl=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Kl||(Kl=!0,Oc(tt,(function(){return wc(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Nl.transition,Nl.transition=null;var s=bt;bt=1;var l=jl;jl|=4,Cl.current=null,function(e,t){if(eo=Vt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(x){n=null;break e}var s=0,l=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(l=s+o),f!==i||0!==r&&3!==f.nodeType||(c=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++u===o&&(l=s),p===i&&++d===r&&(c=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Vt=!1,Ys=t;null!==Ys;)if(e=(t=Ys).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ys=e;else for(;null!==Ys;){t=Ys;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,g=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:ga(t.type,v),g);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var _=t.stateNode.containerInfo;1===_.nodeType?_.textContent="":9===_.nodeType&&_.documentElement&&_.removeChild(_.documentElement);break;default:throw Error(a(163))}}catch(x){Sc(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Ys=e;break}Ys=t.return}m=tl,tl=!1}(e,n),vl(n,e),hr(to),Vt=!!eo,to=eo=null,e.current=n,yl(n,e,o),Xe(),jl=l,bt=s,Nl.transition=i}else e.current=n;if(Kl&&(Kl=!1,Gl=e,Ql=o),0===(i=e.pendingLanes)&&(ql=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Je()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)r((o=t[n]).value,{componentStack:o.stack,digest:o.digest});if(Vl)throw Vl=!1,e=$l,$l=null,e;0!==(1&Ql)&&0!==e.tag&&wc(),0!==(1&(i=e.pendingLanes))?e===Jl?Xl++:(Xl=0,Jl=e):Xl=0,Ho()}(e,t,n,r)}finally{Nl.transition=o,bt=r}return null}function wc(){if(null!==Gl){var e=_t(Ql),t=Nl.transition,n=bt;try{if(Nl.transition=null,bt=16>e?16:e,null===Gl)var r=!1;else{if(e=Gl,Gl=null,Ql=0,0!==(6&jl))throw Error(a(331));var o=jl;for(jl|=4,Ys=e.current;null!==Ys;){var i=Ys,s=i.child;if(0!==(16&Ys.flags)){var l=i.deletions;if(null!==l){for(var c=0;c<l.length;c++){var u=l[c];for(Ys=u;null!==Ys;){var d=Ys;switch(d.tag){case 0:case 11:case 15:nl(8,d,i)}var f=d.child;if(null!==f)f.return=d,Ys=f;else for(;null!==Ys;){var p=(d=Ys).sibling,h=d.return;if(al(d),d===u){Ys=null;break}if(null!==p){p.return=h,Ys=p;break}Ys=h}}}var m=i.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var g=v.sibling;v.sibling=null,v=g}while(null!==v)}}Ys=i}}if(0!==(2064&i.subtreeFlags)&&null!==s)s.return=i,Ys=s;else e:for(;null!==Ys;){if(0!==(2048&(i=Ys).flags))switch(i.tag){case 0:case 11:case 15:nl(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Ys=y;break e}Ys=i.return}}var b=e.current;for(Ys=b;null!==Ys;){var _=(s=Ys).child;if(0!==(2064&s.subtreeFlags)&&null!==_)_.return=s,Ys=_;else e:for(s=b;null!==Ys;){if(0!==(2048&(l=Ys).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(w){Sc(l,l.return,w)}if(l===s){Ys=null;break e}var x=l.sibling;if(null!==x){x.return=l.return,Ys=x;break e}Ys=l.return}}if(jl=o,Ho(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(w){}r=!0}return r}finally{bt=n,Nl.transition=t}}return!1}function kc(e,t,n){e=La(e,t=hs(0,t=us(n,t),1),1),t=ec(),null!==e&&(gt(e,1,t),rc(e,t))}function Sc(e,t,n){if(3===e.tag)kc(e,e,n);else for(;null!==t;){if(3===t.tag){kc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===ql||!ql.has(r))){t=La(t,e=ms(t,e=us(n,e),1),1),e=ec(),null!==t&&(gt(t,1,e),rc(t,e));break}}t=t.return}}function Ec(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Ol===e&&(Pl&n)===n&&(4===Il||3===Il&&(130023424&Pl)===Pl&&500>Je()-Ml?fc(e,0):zl|=n),rc(e,t)}function Cc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Ta(e,t))&&(gt(e,t,n),rc(e,n))}function Nc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cc(e,n)}function jc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Cc(e,n)}function Oc(e,t){return Ke(e,t)}function Tc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pc(e,t,n,r){return new Tc(e,t,n,r)}function Rc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ac(e,t){var n=e.alternate;return null===n?((n=Pc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ic(e,t,n,r,o,i){var s=2;if(r=e,"function"===typeof e)Rc(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case k:return Lc(n.children,o,i,t);case S:s=8,o|=8;break;case E:return(e=Pc(12,n,t,2|o)).elementType=E,e.lanes=i,e;case O:return(e=Pc(13,n,t,o)).elementType=O,e.lanes=i,e;case T:return(e=Pc(19,n,t,o)).elementType=T,e.lanes=i,e;case A:return Dc(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:s=10;break e;case N:s=9;break e;case j:s=11;break e;case P:s=14;break e;case R:s=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Pc(s,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function Lc(e,t,n,r){return(e=Pc(7,e,r,t)).lanes=n,e}function Dc(e,t,n,r){return(e=Pc(22,e,r,t)).elementType=A,e.lanes=n,e.stateNode={isHidden:!1},e}function Fc(e,t,n){return(e=Pc(6,e,null,t)).lanes=n,e}function zc(e,t,n){return(t=Pc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uc(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Bc(e,t,n,r,o,a,i,s,l){return e=new Uc(e,t,n,s,l),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Pc(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ra(a),e}function Mc(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:w,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}function Wc(e){if(!e)return jo;e:{if(We(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ao(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Ao(n))return Do(e,n,t)}return t}function Hc(e,t,n,r,o,a,i,s,l){return(e=Bc(n,r,!0,e,0,a,0,s,l)).context=Wc(null),n=e.current,(a=Ia(r=ec(),o=tc(n))).callback=void 0!==t&&null!==t?t:null,La(n,a,o),e.current.lanes=o,gt(e,o,r),rc(e,r),e}function Vc(e,t,n,r){var o=t.current,a=ec(),i=tc(o);return n=Wc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ia(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=La(o,t,i))&&(nc(e,o,i,a),Da(e,o,i)),i}function $c(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Kc(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||To.current)_s=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return _s=!1,function(e,t,n){switch(t.tag){case 3:Ts(t),ha();break;case 5:ii(t);break;case 1:Ao(t.type)&&Fo(t);break;case 4:oi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;No(ya,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(No(li,1&li.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(No(li,1&li.current),null!==(e=Vs(e,t,n))?e.sibling:null);No(li,1&li.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Ws(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),No(li,li.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Vs(e,t,n)}(e,t,n);_s=0!==(131072&e.flags)}else _s=!1,aa&&0!==(1048576&t.flags)&&ea(t,Ko,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hs(e,t),e=t.pendingProps;var o=Ro(t,Oo.current);Ea(t,n),o=Si(null,t,r,e,o,n);var i=Ei();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ao(r)?(i=!0,Fo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Ra(t),o.updater=Wa,t.stateNode=o,o._reactInternals=t,qa(t,r,e,n),t=Os(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),xs(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Rc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===j)return 11;if(e===P)return 14}return 2}(r),e=ga(r,e),o){case 0:t=Ns(null,t,r,e,n);break e;case 1:t=js(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ga(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Ns(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 1:return r=t.type,o=t.pendingProps,js(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 3:e:{if(Ts(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,Aa(e,t),za(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Ps(e,t,r,n,o=us(Error(a(423)),t));break e}if(r!==o){t=Ps(e,t,r,n,o=us(Error(a(424)),t));break e}for(oa=co(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Ya(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===o){t=Vs(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return ii(t),null===e&&ua(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,s=o.children,no(r,o)?s=null:null!==i&&no(r,i)&&(t.flags|=32),Cs(e,t),xs(e,t,s,n),t.child;case 6:return null===e&&ua(t),null;case 13:return Fs(e,t,n);case 4:return oi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Ja(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,ws(e,t,r,o=t.elementType===r?o:ga(r,o),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,No(ya,r._currentValue),r._currentValue=s,null!==i)if(sr(i.value,s)){if(i.children===o.children&&!To.current){t=Vs(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var l=i.dependencies;if(null!==l){s=i.child;for(var c=l.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Ia(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),Sa(i.return,n,t),l.lanes|=n;break}c=c.next}}else if(10===i.tag)s=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(s=i.return))throw Error(a(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Sa(s,n,t),s=i.sibling}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===t){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}xs(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ea(t,n),r=r(o=Ca(o)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return o=ga(r=t.type,t.pendingProps),ks(e,t,r,o=ga(r.type,o),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ga(r,o),Hs(e,t),t.tag=1,Ao(r)?(e=!0,Fo(t)):e=!1,Ea(t,n),Va(t,r,o),qa(t,r,o,n),Os(null,t,r,!0,e,n);case 19:return Ws(e,t,n);case 22:return Es(e,t,n)}throw Error(a(156,t.tag))};var Gc="function"===typeof reportError?reportError:function(e){console.error(e)};function Qc(e){this._internalRoot=e}function Xc(e){this._internalRoot=e}function Jc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function eu(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var s=o;o=function(){var e=$c(i);s.call(e)}}Vc(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=$c(i);a.call(e)}}var i=Hc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=i,e[mo]=i.current,Wr(8===e.nodeType?e.parentNode:e),uc(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var s=r;r=function(){var e=$c(l);s.call(e)}}var l=Bc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=l,e[mo]=l.current,Wr(8===e.nodeType?e.parentNode:e),uc((function(){Vc(t,l,n,r)})),l}(n,t,e,o,r);return $c(i)}Xc.prototype.render=Qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Vc(e,t,null,null)},Xc.prototype.unmount=Qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Vc(null,e,null,null)})),t[mo]=null}},Xc.prototype.unstable_scheduleHydration=function(e){if(e){var t=St();e={blockedOn:null,target:e,priority:t};for(var n=0;n<At.length&&0!==t&&t<At[n].priority;n++);At.splice(n,0,e),0===n&&Ft(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Je()),0===(6&jl)&&(Wl=Je()+500,Ho()))}break;case 13:uc((function(){var t=Ta(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Kc(e,1)}},wt=function(e){if(13===e.tag){var t=Ta(e,134217728);if(null!==t)nc(t,e,134217728,ec());Kc(e,134217728)}},kt=function(e){if(13===e.tag){var t=tc(e),n=Ta(e,t);if(null!==n)nc(n,e,t,ec());Kc(e,t)}},St=function(){return bt},Et=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},we=function(e,t,n){switch(t){case"input":if(Y(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=wo(r);if(!o)throw Error(a(90));K(r),Y(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},je=cc,Oe=uc;var tu={usingClientEntryPoint:!1,Events:[_o,xo,wo,Ce,Ne,cc]},nu={findFiberByHostInstance:bo,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},ru={bundleType:nu.bundleType,version:nu.version,rendererPackageName:nu.rendererPackageName,rendererConfig:nu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:nu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ou=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ou.isDisabled&&ou.supportsFiber)try{ot=ou.inject(ru),at=ou}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Jc(t))throw Error(a(200));return Mc(e,t,null,n)},t.createRoot=function(e,t){if(!Jc(e))throw Error(a(299));var n=!1,r="",o=Gc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Bc(e,1,!1,null,0,n,0,r,o),e[mo]=t.current,Wr(8===e.nodeType?e.parentNode:e),new Qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Yc(t))throw Error(a(200));return eu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Jc(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",s=Gc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,o,0,i,s),e[mo]=t.current,Wr(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Xc(t)},t.render=function(e,t,n){if(!Yc(t))throw Error(a(200));return eu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yc(e))throw Error(a(40));return!!e._reactRootContainer&&(uc((function(){eu(null,null,e,!1,(function(){e._reactRootContainer=null,e[mo]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yc(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return eu(e,t,n,!1,r)},t.version="18.2.0-next-9e3b772b8-20220608"},1250:function(e,t,n){"use strict";var r=n(4164);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},4164:function(e,t,n){"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4463)},7470:function(e,t,n){!function(e,t){"use strict";function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var r=n(t);function o(e,t){return o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},o(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,o(e,t)}var i=function(e,t){return void 0===e&&(e=[]),void 0===t&&(t=[]),e.length!==t.length||e.some((function(e,n){return!Object.is(e,t[n])}))},s={error:null},l=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).state=s,t.resetErrorBoundary=function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];null==t.props.onReset||(e=t.props).onReset.apply(e,r),t.reset()},t}a(t,e),t.getDerivedStateFromError=function(e){return{error:e}};var n=t.prototype;return n.reset=function(){this.setState(s)},n.componentDidCatch=function(e,t){var n,r;null==(n=(r=this.props).onError)||n.call(r,e,t)},n.componentDidUpdate=function(e,t){var n,r,o=this.state.error,a=this.props.resetKeys;null!==o&&null!==t.error&&i(e.resetKeys,a)&&(null==(n=(r=this.props).onResetKeysChange)||n.call(r,e.resetKeys,a),this.reset())},n.render=function(){var e=this.state.error,t=this.props,n=t.fallbackRender,o=t.FallbackComponent,a=t.fallback;if(null!==e){var i={error:e,resetErrorBoundary:this.resetErrorBoundary};if(r.isValidElement(a))return a;if("function"===typeof n)return n(i);if(o)return r.createElement(o,i);throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop")}return this.props.children},t}(r.Component);function c(e,t){var n=function(n){return r.createElement(l,t,r.createElement(e,n))},o=e.displayName||e.name||"Unknown";return n.displayName="withErrorBoundary("+o+")",n}function u(e){var t=r.useState(null),n=t[0],o=t[1];if(null!=e)throw e;if(null!=n)throw n;return o}e.ErrorBoundary=l,e.useErrorHandler=u,e.withErrorBoundary=c,Object.defineProperty(e,"__esModule",{value:!0})}(t,n(2791))},1372:function(e,t){"use strict";var n=60103,r=60106,o=60107,a=60108,i=60114,s=60109,l=60110,c=60112,u=60113,d=60120,f=60115,p=60116,h=60121,m=60122,v=60117,g=60129,y=60131;if("function"===typeof Symbol&&Symbol.for){var b=Symbol.for;n=b("react.element"),r=b("react.portal"),o=b("react.fragment"),a=b("react.strict_mode"),i=b("react.profiler"),s=b("react.provider"),l=b("react.context"),c=b("react.forward_ref"),u=b("react.suspense"),d=b("react.suspense_list"),f=b("react.memo"),p=b("react.lazy"),h=b("react.block"),m=b("react.server.block"),v=b("react.fundamental"),g=b("react.debug_trace_mode"),y=b("react.legacy_hidden")}function _(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case i:case a:case u:case d:return e;default:switch(e=e&&e.$$typeof){case l:case c:case p:case f:case s:return e;default:return t}}case r:return t}}}t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===i||e===g||e===a||e===u||e===d||e===y||"object"===typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===f||e.$$typeof===s||e.$$typeof===l||e.$$typeof===c||e.$$typeof===v||e.$$typeof===h||e[0]===m)},t.typeOf=_},7441:function(e,t,n){"use strict";e.exports=n(1372)},6374:function(e,t,n){"use strict";var r=n(2791),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:s.current}}t.Fragment=a,t.jsx=c,t.jsxs=c},9117:function(e,t){"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var _=b.prototype=new y;_.constructor=b,m(_,g.prototype),_.isPureReactComponent=!0;var x=Array.isArray,w=Object.prototype.hasOwnProperty,k={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var o,a={},i=null,s=null;if(null!=t)for(o in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(i=""+t.key),t)w.call(t,o)&&!S.hasOwnProperty(o)&&(a[o]=t[o]);var l=arguments.length-2;if(1===l)a.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];a.children=c}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===a[o]&&(a[o]=l[o]);return{$$typeof:n,type:e,key:i,ref:s,props:a,_owner:k.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function j(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function O(e,t,o,a,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return i=i(l=e),e=""===a?"."+j(l,0):a,x(i)?(o="",null!=e&&(o=e.replace(N,"$&/")+"/"),O(i,t,o,"",(function(e){return e}))):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(N,"$&/")+"/")+e)),t.push(i)),1;if(l=0,a=""===a?".":a+":",x(e))for(var c=0;c<e.length;c++){var u=a+j(s=e[c],c);l+=O(s,t,o,u,i)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(s=e.next()).done;)l+=O(s=s.value,t,o,u=a+j(s,c++),i);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function T(e,t,n){if(null==e)return e;var r=[],o=0;return O(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},A={transition:null},I={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:A,ReactCurrentOwner:k};t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,s=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)w.call(t,c)&&!S.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];o.children=l}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=A.transition;A.transition={};try{e()}finally{A.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.2.0"},2791:function(e,t,n){"use strict";e.exports=n(9117)},184:function(e,t,n){"use strict";e.exports=n(6374)},3841:function(e){"use strict";e.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},6813:function(e,t){"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var s=2*(r+1)-1,l=e[s],c=s+1,u=e[c];if(0>a(l,n))c<o&&0>a(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[s]=n,r=s);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var c=[],u=[],d=1,f=null,p=3,h=!1,m=!1,v=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function _(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(v=!1,_(e),!m)if(null!==r(c))m=!0,A(w);else{var t=r(u);null!==t&&I(x,t.startTime-e)}}function w(e,n){m=!1,v&&(v=!1,y(C),C=-1),h=!0;var a=p;try{for(_(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!O());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(c)&&o(c),_(n)}else o(c);f=r(c)}if(null!==f)var l=!0;else{var d=r(u);null!==d&&I(x,d.startTime-n),l=!1}return l}finally{f=null,p=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,E=null,C=-1,N=5,j=-1;function O(){return!(t.unstable_now()-j<N)}function T(){if(null!==E){var e=t.unstable_now();j=e;var n=!0;try{n=E(!0,e)}finally{n?k():(S=!1,E=null)}}else S=!1}if("function"===typeof b)k=function(){b(T)};else if("undefined"!==typeof MessageChannel){var P=new MessageChannel,R=P.port2;P.port1.onmessage=T,k=function(){R.postMessage(null)}}else k=function(){g(T,0)};function A(e){E=e,S||(S=!0,k())}function I(e,n){C=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,A(w))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>i?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(v?(y(C),C=-1):v=!0,I(x,a-i))):(e.sortIndex=s,n(c,e),m||h||(m=!0,A(w))),e},t.unstable_shouldYield=O,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},5296:function(e,t,n){"use strict";e.exports=n(6813)},9613:function(e){e.exports=function(e,t,n,r){var o=n?n.call(r,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var a=Object.keys(e),i=Object.keys(t);if(a.length!==i.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<a.length;l++){var c=a[l];if(!s(c))return!1;var u=e[c],d=t[c];if(!1===(o=n?n.call(r,u,d,c):void 0)||void 0===o&&u!==d)return!1}return!0}},2506:function(e,t,n){"use strict";var r=n(9296);e.exports=n(1732)(r),"_sockjs_onload"in n.g&&setTimeout(n.g._sockjs_onload,1)},5002:function(e,t,n){"use strict";var r=n(2534),o=n(797);function a(){o.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}r(a,o),e.exports=a},1227:function(e,t,n){"use strict";var r=n(2534),o=n(9375);function a(){o.call(this)}r(a,o),a.prototype.removeAllListeners=function(e){e?delete this._listeners[e]:this._listeners={}},a.prototype.once=function(e,t){var n=this,r=!1;this.on(e,(function o(){n.removeListener(e,o),r||(r=!0,t.apply(this,arguments))}))},a.prototype.emit=function(){var e=arguments[0],t=this._listeners[e];if(t){for(var n=arguments.length,r=new Array(n-1),o=1;o<n;o++)r[o-1]=arguments[o];for(var a=0;a<t.length;a++)t[a].apply(this,r)}},a.prototype.on=a.prototype.addListener=o.prototype.addEventListener,a.prototype.removeListener=o.prototype.removeEventListener,e.exports.v=a},797:function(e){"use strict";function t(e){this.type=e}t.prototype.initEvent=function(e,t,n){return this.type=e,this.bubbles=t,this.cancelable=n,this.timeStamp=+new Date,this},t.prototype.stopPropagation=function(){},t.prototype.preventDefault=function(){},t.CAPTURING_PHASE=1,t.AT_TARGET=2,t.BUBBLING_PHASE=3,e.exports=t},9375:function(e){"use strict";function t(){this._listeners={}}t.prototype.addEventListener=function(e,t){e in this._listeners||(this._listeners[e]=[]);var n=this._listeners[e];-1===n.indexOf(t)&&(n=n.concat([t])),this._listeners[e]=n},t.prototype.removeEventListener=function(e,t){var n=this._listeners[e];if(n){var r=n.indexOf(t);-1===r||(n.length>1?this._listeners[e]=n.slice(0,r).concat(n.slice(r+1)):delete this._listeners[e])}},t.prototype.dispatchEvent=function(){var e=arguments[0],t=e.type,n=1===arguments.length?[e]:Array.apply(null,arguments);if(this["on"+t]&&this["on"+t].apply(this,n),t in this._listeners)for(var r=this._listeners[t],o=0;o<r.length;o++)r[o].apply(this,n)},e.exports=t},3231:function(e,t,n){"use strict";var r=n(2534),o=n(797);function a(e){o.call(this),this.initEvent("message",!1,!1),this.data=e}r(a,o),e.exports=a},8143:function(e,t,n){"use strict";var r=n(8136);function o(e){this._transport=e,e.on("message",this._transportMessage.bind(this)),e.on("close",this._transportClose.bind(this))}o.prototype._transportClose=function(e,t){r.postMessage("c",JSON.stringify([e,t]))},o.prototype._transportMessage=function(e){r.postMessage("t",e)},o.prototype._send=function(e){this._transport.send(e)},o.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},e.exports=o},7405:function(e,t,n){"use strict";var r=n(1793),o=n(2856),a=n(8143),i=n(7836),s=n(8136),l=n(7448);e.exports=function(e,t){var n,c={};t.forEach((function(e){e.facadeTransport&&(c[e.facadeTransport.transportName]=e.facadeTransport)})),c[i.transportName]=i,e.bootstrap_iframe=function(){var t;s.currentWindowId=l.hash.slice(1);o.attachEvent("message",(function(o){if(o.source===parent&&("undefined"===typeof n&&(n=o.origin),o.origin===n)){var i;try{i=JSON.parse(o.data)}catch(m){return void o.data}if(i.windowId===s.currentWindowId)switch(i.type){case"s":var u;try{u=JSON.parse(i.data)}catch(m){i.data;break}var d=u[0],f=u[1],p=u[2],h=u[3];if(d!==e.version)throw new Error('Incompatible SockJS! Main site uses: "'+d+'", the iframe: "'+e.version+'".');if(!r.isOriginEqual(p,l.href)||!r.isOriginEqual(h,l.href))throw new Error("Can't connect to different domain from within an iframe. ("+l.href+", "+p+", "+h+")");t=new a(new c[f](p,h));break;case"m":t._send(i.data);break;case"c":t&&t._close(),t=null}}})),s.postMessage("s")}}},6693:function(e,t,n){"use strict";var r=n(1227).v,o=n(2534),a=n(3617);function i(e,t){r.call(this);var n=this,o=+new Date;this.xo=new t("GET",e),this.xo.once("finish",(function(e,t){var r,i;if(200===e){if(i=+new Date-o,t)try{r=JSON.parse(t)}catch(s){}a.isObject(r)||(r={})}n.emit("finish",r,i),n.removeAllListeners()}))}o(i,r),i.prototype.close=function(){this.removeAllListeners(),this.xo.close()},e.exports=i},7836:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v,a=n(1494),i=n(6693);function s(e){var t=this;o.call(this),this.ir=new i(e,a),this.ir.once("finish",(function(e,n){t.ir=null,t.emit("message",JSON.stringify([e,n]))}))}r(s,o),s.transportName="iframe-info-receiver",s.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},e.exports=s},64:function(e,t,n){"use strict";var r=n(1227).v,o=n(2534),a=n(2856),i=n(192),s=n(7836);function l(e,t){var o=this;r.call(this);var l=function(){var n=o.ifr=new i(s.transportName,t,e);n.once("message",(function(e){if(e){var t;try{t=JSON.parse(e)}catch(a){return o.emit("finish"),void o.close()}var n=t[0],r=t[1];o.emit("finish",n,r)}o.close()})),n.once("close",(function(){o.emit("finish"),o.close()}))};n.g.document.body?l():a.attachEvent("load",l)}o(l,r),l.enabled=function(){return i.enabled()},l.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},e.exports=l},7996:function(e,t,n){"use strict";var r=n(1227).v,o=n(2534),a=n(1793),i=n(1212),s=n(6742),l=n(1494),c=n(7455),u=n(64),d=n(6693);function f(e,t){var n=this;r.call(this),setTimeout((function(){n.doXhr(e,t)}),0)}o(f,r),f._getReceiver=function(e,t,n){return n.sameOrigin?new d(t,l):s.enabled?new d(t,s):i.enabled&&n.sameScheme?new d(t,i):u.enabled()?new u(e,t):new d(t,c)},f.prototype.doXhr=function(e,t){var n=this,r=a.addPath(e,"/info");this.xo=f._getReceiver(e,r,t),this.timeoutRef=setTimeout((function(){n._cleanup(!1),n.emit("finish")}),f.timeout),this.xo.once("finish",(function(e,t){n._cleanup(!0),n.emit("finish",e,t)}))},f.prototype._cleanup=function(e){clearTimeout(this.timeoutRef),this.timeoutRef=null,!e&&this.xo&&this.xo.close(),this.xo=null},f.prototype.close=function(){this.removeAllListeners(),this._cleanup(!1)},f.timeout=8e3,e.exports=f},7448:function(e,t,n){"use strict";e.exports=n.g.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""}},1732:function(e,t,n){"use strict";n(7814);var r,o=n(5915),a=n(2534),i=n(1503),s=n(4419),l=n(1793),c=n(2856),u=n(5936),d=n(3617),f=n(5859),p=n(2159),h=n(797),m=n(9375),v=n(7448),g=n(5002),y=n(3231),b=n(7996);function _(e,t,n){if(!(this instanceof _))return new _(e,t,n);if(arguments.length<1)throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");m.call(this),this.readyState=_.CONNECTING,this.extensions="",this.protocol="",(n=n||{}).protocols_whitelist&&p.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=n.transports,this._transportOptions=n.transportOptions||{},this._timeout=n.timeout||0;var r=n.sessionId||8;if("function"===typeof r)this._generateSessionId=r;else{if("number"!==typeof r)throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._generateSessionId=function(){return i.string(r)}}this._server=n.server||i.numberString(1e3);var a=new o(e);if(!a.host||!a.protocol)throw new SyntaxError("The URL '"+e+"' is invalid");if(a.hash)throw new SyntaxError("The URL must not contain a fragment");if("http:"!==a.protocol&&"https:"!==a.protocol)throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+a.protocol+"' is not allowed.");var s="https:"===a.protocol;if("https:"===v.protocol&&!s&&!l.isLoopbackAddr(a.hostname))throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");t?Array.isArray(t)||(t=[t]):t=[];var c=t.sort();c.forEach((function(e,t){if(!e)throw new SyntaxError("The protocols entry '"+e+"' is invalid.");if(t<c.length-1&&e===c[t+1])throw new SyntaxError("The protocols entry '"+e+"' is duplicated.")}));var u=l.getOrigin(v.href);this._origin=u?u.toLowerCase():null,a.set("pathname",a.pathname.replace(/\/+$/,"")),this.url=a.href,this.url,this._urlInfo={nullOrigin:!f.hasDomain(),sameOrigin:l.isOriginEqual(this.url,v.href),sameScheme:l.isSchemeEqual(this.url,v.href)},this._ir=new b(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}function x(e){return 1e3===e||e>=3e3&&e<=4999}a(_,m),_.prototype.close=function(e,t){if(e&&!x(e))throw new Error("InvalidAccessError: Invalid code");if(t&&t.length>123)throw new SyntaxError("reason argument has an invalid length");if(this.readyState!==_.CLOSING&&this.readyState!==_.CLOSED){this._close(e||1e3,t||"Normal closure",!0)}},_.prototype.send=function(e){if("string"!==typeof e&&(e=""+e),this.readyState===_.CONNECTING)throw new Error("InvalidStateError: The connection has not been established yet");this.readyState===_.OPEN&&this._transport.send(s.quote(e))},_.version=n(3273),_.CONNECTING=0,_.OPEN=1,_.CLOSING=2,_.CLOSED=3,_.prototype._receiveInfo=function(e,t){if(this._ir=null,e){this._rto=this.countRTO(t),this._transUrl=e.base_url?e.base_url:this.url,e=d.extend(e,this._urlInfo);var n=r.filterToEnabled(this._transportsWhitelist,e);this._transports=n.main,this._transports.length,this._connect()}else this._close(1002,"Cannot connect to server")},_.prototype._connect=function(){for(var e=this._transports.shift();e;e=this._transports.shift()){if(e.transportName,e.needBody&&(!n.g.document.body||"undefined"!==typeof n.g.document.readyState&&"complete"!==n.g.document.readyState&&"interactive"!==n.g.document.readyState))return this._transports.unshift(e),void c.attachEvent("load",this._connect.bind(this));var t=Math.max(this._timeout,this._rto*e.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),t);var r=l.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),o=this._transportOptions[e.transportName],a=new e(r,this._transUrl,o);return a.on("message",this._transportMessage.bind(this)),a.once("close",this._transportClose.bind(this)),a.transportName=e.transportName,void(this._transport=a)}this._close(2e3,"All transports failed",!1)},_.prototype._transportTimeout=function(){this.readyState===_.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},_.prototype._transportMessage=function(e){var t,n=this,r=e.slice(0,1),o=e.slice(1);switch(r){case"o":return void this._open();case"h":return this.dispatchEvent(new h("heartbeat")),void this.transport}if(o)try{t=JSON.parse(o)}catch(a){}if("undefined"!==typeof t)switch(r){case"a":Array.isArray(t)&&t.forEach((function(e){n.transport,n.dispatchEvent(new y(e))}));break;case"m":this.transport,this.dispatchEvent(new y(t));break;case"c":Array.isArray(t)&&2===t.length&&this._close(t[0],t[1],!0)}},_.prototype._transportClose=function(e,t){this.transport,this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),x(e)||2e3===e||this.readyState!==_.CONNECTING?this._close(e,t):this._connect()},_.prototype._open=function(){this._transport&&this._transport.transportName,this.readyState,this.readyState===_.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=_.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new h("open")),this.transport):this._close(1006,"Server lost session")},_.prototype._close=function(e,t,n){this.transport,this.readyState;var r=!1;if(this._ir&&(r=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===_.CLOSED)throw new Error("InvalidStateError: SockJS has already been closed");this.readyState=_.CLOSING,setTimeout(function(){this.readyState=_.CLOSED,r&&this.dispatchEvent(new h("error"));var o=new g("close");o.wasClean=n||!1,o.code=e||1e3,o.reason=t,this.dispatchEvent(o),this.onmessage=this.onclose=this.onerror=null}.bind(this),0)},_.prototype.countRTO=function(e){return e>100?4*e:300+e},e.exports=function(e){return r=u(e),n(7405)(_,e),_}},7814:function(){"use strict";var e,t=Array.prototype,n=Object.prototype,r=Function.prototype,o=String.prototype,a=t.slice,i=n.toString,s=function(e){return"[object Function]"===n.toString.call(e)},l=function(e){return"[object String]"===i.call(e)},c=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch(e){return!1}}();e=c?function(e,t,n,r){!r&&t in e||Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(e,t,n,r){!r&&t in e||(e[t]=n)};var u=function(t,r,o){for(var a in r)n.hasOwnProperty.call(r,a)&&e(t,a,r[a],o)},d=function(e){if(null==e)throw new TypeError("can't convert "+e+" to object");return Object(e)};function f(e){var t=+e;return t!==t?t=0:0!==t&&t!==1/0&&t!==-1/0&&(t=(t>0||-1)*Math.floor(Math.abs(t))),t}function p(){}u(r,{bind:function(e){var t=this;if(!s(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var n=a.call(arguments,1),r=function(){if(this instanceof c){var r=t.apply(this,n.concat(a.call(arguments)));return Object(r)===r?r:this}return t.apply(e,n.concat(a.call(arguments)))},o=Math.max(0,t.length-n.length),i=[],l=0;l<o;l++)i.push("$"+l);var c=Function("binder","return function ("+i.join(",")+"){ return binder.apply(this, arguments); }")(r);return t.prototype&&(p.prototype=t.prototype,c.prototype=new p,p.prototype=null),c}}),u(Array,{isArray:function(e){return"[object Array]"===i.call(e)}});var h=Object("a"),m="a"!==h[0]||!(0 in h);u(t,{forEach:function(e){var t=d(this),n=m&&l(this)?this.split(""):t,r=arguments[1],o=-1,a=n.length>>>0;if(!s(e))throw new TypeError;for(;++o<a;)o in n&&e.call(r,n[o],o,t)}},!function(e){var t=!0,n=!0;return e&&(e.call("foo",(function(e,n,r){"object"!==typeof r&&(t=!1)})),e.call([1],(function(){n="string"===typeof this}),"x")),!!e&&t&&n}(t.forEach));var v=Array.prototype.indexOf&&-1!==[0,1].indexOf(1,2);u(t,{indexOf:function(e){var t=m&&l(this)?this.split(""):d(this),n=t.length>>>0;if(!n)return-1;var r=0;for(arguments.length>1&&(r=f(arguments[1])),r=r>=0?r:Math.max(0,n+r);r<n;r++)if(r in t&&t[r]===e)return r;return-1}},v);var g=o.split;2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||".".split(/()()/).length>1?function(){var e=void 0===/()??/.exec("")[1];o.split=function(n,r){var o=this;if(void 0===n&&0===r)return[];if("[object RegExp]"!==i.call(n))return g.call(this,n,r);var a,s,l,c,u=[],d=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.extended?"x":"")+(n.sticky?"y":""),f=0;for(n=new RegExp(n.source,d+"g"),o+="",e||(a=new RegExp("^"+n.source+"$(?!\\s)",d)),r=void 0===r?-1>>>0:r>>>0;(s=n.exec(o))&&!((l=s.index+s[0].length)>f&&(u.push(o.slice(f,s.index)),!e&&s.length>1&&s[0].replace(a,(function(){for(var e=1;e<arguments.length-2;e++)void 0===arguments[e]&&(s[e]=void 0)})),s.length>1&&s.index<o.length&&t.push.apply(u,s.slice(1)),c=s[0].length,f=l,u.length>=r));)n.lastIndex===s.index&&n.lastIndex++;return f===o.length?!c&&n.test("")||u.push(""):u.push(o.slice(f)),u.length>r?u.slice(0,r):u}}():"0".split(void 0,0).length&&(o.split=function(e,t){return void 0===e&&0===t?[]:g.call(this,e,t)});var y=o.substr,b="".substr&&"b"!=="0b".substr(-1);u(o,{substr:function(e,t){return y.call(this,e<0&&(e=this.length+e)<0?0:e,t)}},b)},9296:function(e,t,n){"use strict";e.exports=[n(5383),n(8605),n(1339),n(3759),n(4100)(n(3759)),n(9451),n(4100)(n(9451)),n(1383),n(4218),n(4100)(n(1383)),n(3639)]},700:function(e,t,n){"use strict";var r=n(1227).v,o=n(2534),a=n(2856),i=n(1793),s=n.g.XMLHttpRequest;function l(e,t,n,o){var a=this;r.call(this),setTimeout((function(){a._start(e,t,n,o)}),0)}o(l,r),l.prototype._start=function(e,t,n,r){var o=this;try{this.xhr=new s}catch(u){}if(!this.xhr)return this.emit("finish",0,"no xhr support"),void this._cleanup();t=i.addQuery(t,"t="+ +new Date),this.unloadRef=a.unloadAdd((function(){o._cleanup(!0)}));try{this.xhr.open(e,t,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){o.emit("finish",0,""),o._cleanup(!1)})}catch(d){return this.emit("finish",0,""),void this._cleanup(!1)}if(r&&r.noCredentials||!l.supportsCORS||(this.xhr.withCredentials=!0),r&&r.headers)for(var c in r.headers)this.xhr.setRequestHeader(c,r.headers[c]);this.xhr.onreadystatechange=function(){if(o.xhr){var e,t,n=o.xhr;switch(n.readyState,n.readyState){case 3:try{t=n.status,e=n.responseText}catch(d){}1223===t&&(t=204),200===t&&e&&e.length>0&&o.emit("chunk",t,e);break;case 4:1223===(t=n.status)&&(t=204),12005!==t&&12029!==t||(t=0),n.responseText,o.emit("finish",t,n.responseText),o._cleanup(!1)}}};try{o.xhr.send(n)}catch(d){o.emit("finish",0,""),o._cleanup(!1)}},l.prototype._cleanup=function(e){if(this.xhr){if(this.removeAllListeners(),a.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),e)try{this.xhr.abort()}catch(t){}this.unloadRef=this.xhr=null}},l.prototype.close=function(){this._cleanup(!0)},l.enabled=!!s;var c=["Active"].concat("Object").join("X");!l.enabled&&c in n.g&&(s=function(){try{return new n.g[c]("Microsoft.XMLHTTP")}catch(e){return null}},l.enabled=!!new s);var u=!1;try{u="withCredentials"in new s}catch(d){}l.supportsCORS=u,e.exports=l},9931:function(e,t,n){e.exports=n.g.EventSource},7501:function(e,t,n){"use strict";var r=n.g.WebSocket||n.g.MozWebSocket;e.exports=r?function(e){return new r(e)}:void 0},3759:function(e,t,n){"use strict";var r=n(2534),o=n(3475),a=n(6860),i=n(6742),s=n(9931);function l(e){if(!l.enabled())throw new Error("Transport created when disabled");o.call(this,e,"/eventsource",a,i)}r(l,o),l.enabled=function(){return!!s},l.transportName="eventsource",l.roundTrips=2,e.exports=l},9451:function(e,t,n){"use strict";var r=n(2534),o=n(6733),a=n(1494),i=n(3475);function s(e){if(!o.enabled)throw new Error("Transport created when disabled");i.call(this,e,"/htmlfile",o,a)}r(s,i),s.enabled=function(e){return o.enabled&&e.sameOrigin},s.transportName="htmlfile",s.roundTrips=2,e.exports=s},192:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v,a=n(3273),i=n(1793),s=n(8136),l=n(2856),c=n(1503);function u(e,t,n){if(!u.enabled())throw new Error("Transport created when disabled");o.call(this);var r=this;this.origin=i.getOrigin(n),this.baseUrl=n,this.transUrl=t,this.transport=e,this.windowId=c.string(8);var a=i.addPath(n,"/iframe.html")+"#"+this.windowId;this.iframeObj=s.createIframe(a,(function(e){r.emit("close",1006,"Unable to load an iframe ("+e+")"),r.close()})),this.onmessageCallback=this._message.bind(this),l.attachEvent("message",this.onmessageCallback)}r(u,o),u.prototype.close=function(){if(this.removeAllListeners(),this.iframeObj){l.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch(e){}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},u.prototype._message=function(e){if(e.data,!i.isOriginEqual(e.origin,this.origin))return e.origin,void this.origin;var t;try{t=JSON.parse(e.data)}catch(r){return void e.data}if(t.windowId!==this.windowId)return t.windowId,void this.windowId;switch(t.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([a,this.transport,this.transUrl,this.baseUrl]));break;case"t":this.emit("message",t.data);break;case"c":var n;try{n=JSON.parse(t.data)}catch(r){return void t.data}this.emit("close",n[0],n[1]),this.close()}},u.prototype.postMessage=function(e,t){this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:e,data:t||""}),this.origin)},u.prototype.send=function(e){this.postMessage("m",e)},u.enabled=function(){return s.iframeEnabled},u.transportName="iframe",u.roundTrips=2,e.exports=u},3639:function(e,t,n){"use strict";var r=n(2534),o=n(1153),a=n(1646),i=n(6173);function s(e){if(!s.enabled())throw new Error("Transport created when disabled");o.call(this,e,"/jsonp",i,a)}r(s,o),s.enabled=function(){return!!n.g.document},s.transportName="jsonp-polling",s.roundTrips=1,s.needBody=!0,e.exports=s},3475:function(e,t,n){"use strict";var r=n(2534),o=n(1793),a=n(1153);function i(e,t,n,r){a.call(this,e,t,function(e){return function(t,n,r){var a={};"string"===typeof n&&(a.headers={"Content-type":"text/plain"});var i=o.addPath(t,"/xhr_send"),s=new e("POST",i,n,a);return s.once("finish",(function(e){if(s=null,200!==e&&204!==e)return r(new Error("http status "+e));r()})),function(){s.close(),s=null;var e=new Error("Aborted");e.code=1e3,r(e)}}}(r),n,r)}r(i,a),e.exports=i},282:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v;function a(e,t){o.call(this),this.sendBuffer=[],this.sender=t,this.url=e}r(a,o),a.prototype.send=function(e){this.sendBuffer.push(e),this.sendStop||this.sendSchedule()},a.prototype.sendScheduleWait=function(){var e,t=this;this.sendStop=function(){t.sendStop=null,clearTimeout(e)},e=setTimeout((function(){t.sendStop=null,t.sendSchedule()}),25)},a.prototype.sendSchedule=function(){this.sendBuffer.length;var e=this;if(this.sendBuffer.length>0){var t="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,t,(function(t){e.sendStop=null,t?(e.emit("close",t.code||1006,"Sending error: "+t),e.close()):e.sendScheduleWait()})),this.sendBuffer=[]}},a.prototype._cleanup=function(){this.removeAllListeners()},a.prototype.close=function(){this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},e.exports=a},4100:function(e,t,n){"use strict";var r=n(2534),o=n(192),a=n(3617);e.exports=function(e){function t(t,n){o.call(this,e.transportName,t,n)}return r(t,o),t.enabled=function(t,r){if(!n.g.document)return!1;var i=a.extend({},r);return i.sameOrigin=!0,e.enabled(i)&&o.enabled()},t.transportName="iframe-"+e.transportName,t.needBody=!0,t.roundTrips=o.roundTrips+e.roundTrips-1,t.facadeTransport=e,t}},7973:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v;function a(e,t,n){o.call(this),this.Receiver=e,this.receiveUrl=t,this.AjaxObject=n,this._scheduleReceiver()}r(a,o),a.prototype._scheduleReceiver=function(){var e=this,t=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);t.on("message",(function(t){e.emit("message",t)})),t.once("close",(function(n,r){e.pollIsClosing,e.poll=t=null,e.pollIsClosing||("network"===r?e._scheduleReceiver():(e.emit("close",n||1006,r),e.removeAllListeners()))}))},a.prototype.abort=function(){this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},e.exports=a},1153:function(e,t,n){"use strict";var r=n(2534),o=n(1793),a=n(282),i=n(7973);function s(e,t,n,r,s){var l=o.addPath(e,t),c=this;a.call(this,e,n),this.poll=new i(r,l,s),this.poll.on("message",(function(e){c.emit("message",e)})),this.poll.once("close",(function(e,t){c.poll=null,c.emit("close",e,t),c.close()}))}r(s,a),s.prototype.close=function(){a.prototype.close.call(this),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},e.exports=s},6860:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v,a=n(9931);function i(e){o.call(this);var t=this,n=this.es=new a(e);n.onmessage=function(e){e.data,t.emit("message",decodeURI(e.data))},n.onerror=function(e){n.readyState;var r=2!==n.readyState?"network":"permanent";t._cleanup(),t._close(r)}}r(i,o),i.prototype.abort=function(){this._cleanup(),this._close("user")},i.prototype._cleanup=function(){var e=this.es;e&&(e.onmessage=e.onerror=null,e.close(),this.es=null)},i.prototype._close=function(e){var t=this;setTimeout((function(){t.emit("close",null,e),t.removeAllListeners()}),200)},e.exports=i},6733:function(e,t,n){"use strict";var r=n(2534),o=n(8136),a=n(1793),i=n(1227).v,s=n(1503);function l(e){i.call(this);var t=this;o.polluteGlobalNamespace(),this.id="a"+s.string(6),e=a.addQuery(e,"c="+decodeURIComponent(o.WPrefix+"."+this.id)),l.htmlfileEnabled;var r=l.htmlfileEnabled?o.createHtmlfile:o.createIframe;n.g[o.WPrefix][this.id]={start:function(){t.iframeObj.loaded()},message:function(e){t.emit("message",e)},stop:function(){t._cleanup(),t._close("network")}},this.iframeObj=r(e,(function(){t._cleanup(),t._close("permanent")}))}r(l,i),l.prototype.abort=function(){this._cleanup(),this._close("user")},l.prototype._cleanup=function(){this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete n.g[o.WPrefix][this.id]},l.prototype._close=function(e){this.emit("close",null,e),this.removeAllListeners()},l.htmlfileEnabled=!1;var c=["Active"].concat("Object").join("X");if(c in n.g)try{l.htmlfileEnabled=!!new n.g[c]("htmlfile")}catch(u){}l.enabled=l.htmlfileEnabled||o.iframeEnabled,e.exports=l},1646:function(e,t,n){"use strict";var r=n(8136),o=n(1503),a=n(5859),i=n(1793),s=n(2534),l=n(1227).v;function c(e){var t=this;l.call(this),r.polluteGlobalNamespace(),this.id="a"+o.string(6);var a=i.addQuery(e,"c="+encodeURIComponent(r.WPrefix+"."+this.id));n.g[r.WPrefix][this.id]=this._callback.bind(this),this._createScript(a),this.timeoutId=setTimeout((function(){t._abort(new Error("JSONP script loaded abnormally (timeout)"))}),c.timeout)}s(c,l),c.prototype.abort=function(){if(n.g[r.WPrefix][this.id]){var e=new Error("JSONP user aborted read");e.code=1e3,this._abort(e)}},c.timeout=35e3,c.scriptErrorTimeout=1e3,c.prototype._callback=function(e){this._cleanup(),this.aborting||(e&&this.emit("message",e),this.emit("close",null,"network"),this.removeAllListeners())},c.prototype._abort=function(e){this._cleanup(),this.aborting=!0,this.emit("close",e.code,e.message),this.removeAllListeners()},c.prototype._cleanup=function(){if(clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var e=this.script;e.parentNode.removeChild(e),e.onreadystatechange=e.onerror=e.onload=e.onclick=null,this.script=null}delete n.g[r.WPrefix][this.id]},c.prototype._scriptError=function(){var e=this;this.errorTimer||(this.errorTimer=setTimeout((function(){e.loadedOkay||e._abort(new Error("JSONP script loaded abnormally (onerror)"))}),c.scriptErrorTimeout))},c.prototype._createScript=function(e){var t,r=this,i=this.script=n.g.document.createElement("script");if(i.id="a"+o.string(8),i.src=e,i.type="text/javascript",i.charset="UTF-8",i.onerror=this._scriptError.bind(this),i.onload=function(){r._abort(new Error("JSONP script loaded abnormally (onload)"))},i.onreadystatechange=function(){if(i.readyState,/loaded|closed/.test(i.readyState)){if(i&&i.htmlFor&&i.onclick){r.loadedOkay=!0;try{i.onclick()}catch(e){}}i&&r._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"))}},"undefined"===typeof i.async&&n.g.document.attachEvent)if(a.isOpera())(t=this.script2=n.g.document.createElement("script")).text="try{var a = document.getElementById('"+i.id+"'); if(a)a.onerror();}catch(x){};",i.async=t.async=!1;else{try{i.htmlFor=i.id,i.event="onclick"}catch(l){}i.async=!0}"undefined"!==typeof i.async&&(i.async=!0);var s=n.g.document.getElementsByTagName("head")[0];s.insertBefore(i,s.firstChild),t&&s.insertBefore(t,s.firstChild)},e.exports=c},3138:function(e,t,n){"use strict";var r=n(2534),o=n(1227).v;function a(e,t){o.call(this);var n=this;this.bufferPosition=0,this.xo=new t("POST",e,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",(function(e,t){n._chunkHandler(e,t),n.xo=null;var r=200===e?"network":"permanent";n.emit("close",null,r),n._cleanup()}))}r(a,o),a.prototype._chunkHandler=function(e,t){if(200===e&&t)for(var n=-1;;this.bufferPosition+=n+1){var r=t.slice(this.bufferPosition);if(-1===(n=r.indexOf("\n")))break;var o=r.slice(0,n);o&&this.emit("message",o)}},a.prototype._cleanup=function(){this.removeAllListeners()},a.prototype.abort=function(){this.xo&&(this.xo.close(),this.emit("close",null,"user"),this.xo=null),this._cleanup()},e.exports=a},6173:function(e,t,n){"use strict";var r,o,a=n(1503),i=n(1793);e.exports=function(e,t,s){r||((r=n.g.document.createElement("form")).style.display="none",r.style.position="absolute",r.method="POST",r.enctype="application/x-www-form-urlencoded",r.acceptCharset="UTF-8",(o=n.g.document.createElement("textarea")).name="d",r.appendChild(o),n.g.document.body.appendChild(r));var l="a"+a.string(8);r.target=l,r.action=i.addQuery(i.addPath(e,"/jsonp_send"),"i="+l);var c=function(e){try{return n.g.document.createElement('<iframe name="'+e+'">')}catch(r){var t=n.g.document.createElement("iframe");return t.name=e,t}}(l);c.id=l,c.style.display="none",r.appendChild(c);try{o.value=t}catch(d){}r.submit();var u=function(e){c.onerror&&(c.onreadystatechange=c.onerror=c.onload=null,setTimeout((function(){c.parentNode.removeChild(c),c=null}),500),o.value="",s(e))};return c.onerror=function(){u()},c.onload=function(){u()},c.onreadystatechange=function(e){c.readyState,"complete"===c.readyState&&u()},function(){u(new Error("Aborted"))}}},1212:function(e,t,n){"use strict";var r=n(1227).v,o=n(2534),a=n(2856),i=n(5859),s=n(1793);function l(e,t,n){var o=this;r.call(this),setTimeout((function(){o._start(e,t,n)}),0)}o(l,r),l.prototype._start=function(e,t,r){var o=this,i=new n.g.XDomainRequest;t=s.addQuery(t,"t="+ +new Date),i.onerror=function(){o._error()},i.ontimeout=function(){o._error()},i.onprogress=function(){i.responseText,o.emit("chunk",200,i.responseText)},i.onload=function(){o.emit("finish",200,i.responseText),o._cleanup(!1)},this.xdr=i,this.unloadRef=a.unloadAdd((function(){o._cleanup(!0)}));try{this.xdr.open(e,t),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(r)}catch(l){this._error()}},l.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},l.prototype._cleanup=function(e){if(this.xdr){if(this.removeAllListeners(),a.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,e)try{this.xdr.abort()}catch(t){}this.unloadRef=this.xdr=null}},l.prototype.close=function(){this._cleanup(!0)},l.enabled=!(!n.g.XDomainRequest||!i.hasDomain()),e.exports=l},6742:function(e,t,n){"use strict";var r=n(2534),o=n(700);function a(e,t,n,r){o.call(this,e,t,n,r)}r(a,o),a.enabled=o.enabled&&o.supportsCORS,e.exports=a},7455:function(e,t,n){"use strict";var r=n(1227).v;function o(){var e=this;r.call(this),this.to=setTimeout((function(){e.emit("finish",200,"{}")}),o.timeout)}n(2534)(o,r),o.prototype.close=function(){clearTimeout(this.to)},o.timeout=2e3,e.exports=o},1494:function(e,t,n){"use strict";var r=n(2534),o=n(700);function a(e,t,n){o.call(this,e,t,n,{noCredentials:!0})}r(a,o),a.enabled=o.enabled,e.exports=a},5383:function(e,t,n){"use strict";var r=n(2856),o=n(1793),a=n(2534),i=n(1227).v,s=n(7501);function l(e,t,n){if(!l.enabled())throw new Error("Transport created when disabled");i.call(this);var a=this,c=o.addPath(e,"/websocket");c="https"===c.slice(0,5)?"wss"+c.slice(5):"ws"+c.slice(4),this.url=c,this.ws=new s(this.url,[],n),this.ws.onmessage=function(e){e.data,a.emit("message",e.data)},this.unloadRef=r.unloadAdd((function(){a.ws.close()})),this.ws.onclose=function(e){e.code,e.reason,a.emit("close",e.code,e.reason),a._cleanup()},this.ws.onerror=function(e){a.emit("close",1006,"WebSocket connection broken"),a._cleanup()}}a(l,i),l.prototype.send=function(e){var t="["+e+"]";this.ws.send(t)},l.prototype.close=function(){var e=this.ws;this._cleanup(),e&&e.close()},l.prototype._cleanup=function(){var e=this.ws;e&&(e.onmessage=e.onclose=e.onerror=null),r.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},l.enabled=function(){return!!s},l.transportName="websocket",l.roundTrips=2,e.exports=l},4218:function(e,t,n){"use strict";var r=n(2534),o=n(3475),a=n(1339),i=n(3138),s=n(1212);function l(e){if(!s.enabled)throw new Error("Transport created when disabled");o.call(this,e,"/xhr",i,s)}r(l,o),l.enabled=a.enabled,l.transportName="xdr-polling",l.roundTrips=2,e.exports=l},1339:function(e,t,n){"use strict";var r=n(2534),o=n(3475),a=n(3138),i=n(1212);function s(e){if(!i.enabled)throw new Error("Transport created when disabled");o.call(this,e,"/xhr_streaming",a,i)}r(s,o),s.enabled=function(e){return!e.cookie_needed&&!e.nullOrigin&&(i.enabled&&e.sameScheme)},s.transportName="xdr-streaming",s.roundTrips=2,e.exports=s},1383:function(e,t,n){"use strict";var r=n(2534),o=n(3475),a=n(3138),i=n(6742),s=n(1494);function l(e){if(!s.enabled&&!i.enabled)throw new Error("Transport created when disabled");o.call(this,e,"/xhr",a,i)}r(l,o),l.enabled=function(e){return!e.nullOrigin&&(!(!s.enabled||!e.sameOrigin)||i.enabled)},l.transportName="xhr-polling",l.roundTrips=2,e.exports=l},8605:function(e,t,n){"use strict";var r=n(2534),o=n(3475),a=n(3138),i=n(6742),s=n(1494),l=n(5859);function c(e){if(!s.enabled&&!i.enabled)throw new Error("Transport created when disabled");o.call(this,e,"/xhr_streaming",a,i)}r(c,o),c.enabled=function(e){return!e.nullOrigin&&(!l.isOpera()&&i.enabled)},c.transportName="xhr-streaming",c.roundTrips=2,c.needBody=!!n.g.document,e.exports=c},3306:function(e,t,n){"use strict";n.g.crypto&&n.g.crypto.getRandomValues?e.exports.randomBytes=function(e){var t=new Uint8Array(e);return n.g.crypto.getRandomValues(t),t}:e.exports.randomBytes=function(e){for(var t=new Array(e),n=0;n<e;n++)t[n]=Math.floor(256*Math.random());return t}},5859:function(e,t,n){"use strict";e.exports={isOpera:function(){return n.g.navigator&&/opera/i.test(n.g.navigator.userAgent)},isKonqueror:function(){return n.g.navigator&&/konqueror/i.test(n.g.navigator.userAgent)},hasDomain:function(){if(!n.g.document)return!0;try{return!!n.g.document.domain}catch(e){return!1}}}},4419:function(e){"use strict";var t,n=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;e.exports={quote:function(e){var r=JSON.stringify(e);return n.lastIndex=0,n.test(r)?(t||(t=function(e){var t,n={},r=[];for(t=0;t<65536;t++)r.push(String.fromCharCode(t));return e.lastIndex=0,r.join("").replace(e,(function(e){return n[e]="\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4),""})),e.lastIndex=0,n}(n)),r.replace(n,(function(e){return t[e]}))):r}}},2856:function(e,t,n){"use strict";var r=n(1503),o={},a=!1,i=n.g.chrome&&n.g.chrome.app&&n.g.chrome.app.runtime;e.exports={attachEvent:function(e,t){"undefined"!==typeof n.g.addEventListener?n.g.addEventListener(e,t,!1):n.g.document&&n.g.attachEvent&&(n.g.document.attachEvent("on"+e,t),n.g.attachEvent("on"+e,t))},detachEvent:function(e,t){"undefined"!==typeof n.g.addEventListener?n.g.removeEventListener(e,t,!1):n.g.document&&n.g.detachEvent&&(n.g.document.detachEvent("on"+e,t),n.g.detachEvent("on"+e,t))},unloadAdd:function(e){if(i)return null;var t=r.string(8);return o[t]=e,a&&setTimeout(this.triggerUnloadCallbacks,0),t},unloadDel:function(e){e in o&&delete o[e]},triggerUnloadCallbacks:function(){for(var e in o)o[e](),delete o[e]}};i||e.exports.attachEvent("unload",(function(){a||(a=!0,e.exports.triggerUnloadCallbacks())}))},8136:function(e,t,n){"use strict";var r=n(2856),o=n(5859);e.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){e.exports.WPrefix in n.g||(n.g[e.exports.WPrefix]={})},postMessage:function(t,r){n.g.parent!==n.g&&n.g.parent.postMessage(JSON.stringify({windowId:e.exports.currentWindowId,type:t,data:r||""}),"*")},createIframe:function(e,t){var o,a,i=n.g.document.createElement("iframe"),s=function(){clearTimeout(o);try{i.onload=null}catch(e){}i.onerror=null},l=function(){i&&(s(),setTimeout((function(){i&&i.parentNode.removeChild(i),i=null}),0),r.unloadDel(a))},c=function(e){i&&(l(),t(e))};return i.src=e,i.style.display="none",i.style.position="absolute",i.onerror=function(){c("onerror")},i.onload=function(){clearTimeout(o),o=setTimeout((function(){c("onload timeout")}),2e3)},n.g.document.body.appendChild(i),o=setTimeout((function(){c("timeout")}),15e3),a=r.unloadAdd(l),{post:function(e,t){setTimeout((function(){try{i&&i.contentWindow&&i.contentWindow.postMessage(e,t)}catch(n){}}),0)},cleanup:l,loaded:s}},createHtmlfile:function(t,o){var a,i,s,l=["Active"].concat("Object").join("X"),c=new n.g[l]("htmlfile"),u=function(){clearTimeout(a),s.onerror=null},d=function(){c&&(u(),r.unloadDel(i),s.parentNode.removeChild(s),s=c=null,CollectGarbage())},f=function(e){c&&(d(),o(e))};c.open(),c.write('<html><script>document.domain="'+n.g.document.domain+'";<\/script></html>'),c.close(),c.parentWindow[e.exports.WPrefix]=n.g[e.exports.WPrefix];var p=c.createElement("div");return c.body.appendChild(p),s=c.createElement("iframe"),p.appendChild(s),s.src=t,s.onerror=function(){f("onerror")},a=setTimeout((function(){f("timeout")}),15e3),i=r.unloadAdd(d),{post:function(e,t){try{setTimeout((function(){s&&s.contentWindow&&s.contentWindow.postMessage(e,t)}),0)}catch(n){}},cleanup:d,loaded:u}}},e.exports.iframeEnabled=!1,n.g.document&&(e.exports.iframeEnabled=("function"===typeof n.g.postMessage||"object"===typeof n.g.postMessage)&&!o.isKonqueror())},2159:function(e,t,n){"use strict";var r={};["log","debug","warn"].forEach((function(e){var t;try{t=n.g.console&&n.g.console[e]&&n.g.console[e].apply}catch(o){}r[e]=t?function(){return n.g.console[e].apply(n.g.console,arguments)}:"log"===e?function(){}:r.log})),e.exports=r},3617:function(e){"use strict";e.exports={isObject:function(e){var t=typeof e;return"function"===t||"object"===t&&!!e},extend:function(e){if(!this.isObject(e))return e;for(var t,n,r=1,o=arguments.length;r<o;r++)for(n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}}},1503:function(e,t,n){"use strict";var r=n(3306),o="abcdefghijklmnopqrstuvwxyz012345";e.exports={string:function(e){for(var t=o.length,n=r.randomBytes(e),a=[],i=0;i<e;i++)a.push(o.substr(n[i]%t,1));return a.join("")},number:function(e){return Math.floor(Math.random()*e)},numberString:function(e){var t=(""+(e-1)).length;return(new Array(t+1).join("0")+this.number(e)).slice(-t)}}},5936:function(e){"use strict";e.exports=function(e){return{filterToEnabled:function(t,n){var r={main:[],facade:[]};return t?"string"===typeof t&&(t=[t]):t=[],e.forEach((function(e){e&&("websocket"===e.transportName&&!1===n.websocket||(t.length&&-1===t.indexOf(e.transportName)?e.transportName:e.enabled(n)?(e.transportName,r.main.push(e),e.facadeTransport&&r.facade.push(e.facadeTransport)):e.transportName))})),r}}}},1793:function(e,t,n){"use strict";var r=n(5915);e.exports={getOrigin:function(e){if(!e)return null;var t=new r(e);if("file:"===t.protocol)return null;var n=t.port;return n||(n="https:"===t.protocol?"443":"80"),t.protocol+"//"+t.hostname+":"+n},isOriginEqual:function(e,t){var n=this.getOrigin(e)===this.getOrigin(t);return n},isSchemeEqual:function(e,t){return e.split(":")[0]===t.split(":")[0]},addPath:function(e,t){var n=e.split("?");return n[0]+t+(n[1]?"?"+n[1]:"")},addQuery:function(e,t){return e+(-1===e.indexOf("?")?"?"+t:"&"+t)},isLoopbackAddr:function(e){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(e)||/^\[::1\]$/.test(e)}}},3273:function(e){e.exports="1.6.1"},5915:function(e,t,n){"use strict";var r=n(3841),o=n(6962),a=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i=/[\n\r\t]/g,s=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,l=/:\d+$/,c=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,u=/^[a-zA-Z]:/;function d(e){return(e||"").toString().replace(a,"")}var f=[["#","hash"],["?","query"],function(e,t){return m(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],p={hash:1,query:1};function h(e){var t,r=("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:"undefined"!==typeof self?self:{}).location||{},o={},a=typeof(e=e||r);if("blob:"===e.protocol)o=new g(unescape(e.pathname),{});else if("string"===a)for(t in o=new g(e,{}),p)delete o[t];else if("object"===a){for(t in e)t in p||(o[t]=e[t]);void 0===o.slashes&&(o.slashes=s.test(e.href))}return o}function m(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function v(e,t){e=(e=d(e)).replace(i,""),t=t||{};var n,r=c.exec(e),o=r[1]?r[1].toLowerCase():"",a=!!r[2],s=!!r[3],l=0;return a?s?(n=r[2]+r[3]+r[4],l=r[2].length+r[3].length):(n=r[2]+r[4],l=r[2].length):s?(n=r[3]+r[4],l=r[3].length):n=r[4],"file:"===o?l>=2&&(n=n.slice(2)):m(o)?n=r[4]:o?a&&(n=n.slice(2)):l>=2&&m(t.protocol)&&(n=r[4]),{protocol:o,slashes:a||m(o),slashesCount:l,rest:n}}function g(e,t,n){if(e=(e=d(e)).replace(i,""),!(this instanceof g))return new g(e,t,n);var a,s,l,c,p,y,b=f.slice(),_=typeof t,x=this,w=0;for("object"!==_&&"string"!==_&&(n=t,t=null),n&&"function"!==typeof n&&(n=o.parse),a=!(s=v(e||"",t=h(t))).protocol&&!s.slashes,x.slashes=s.slashes||a&&t.slashes,x.protocol=s.protocol||t.protocol||"",e=s.rest,("file:"===s.protocol&&(2!==s.slashesCount||u.test(e))||!s.slashes&&(s.protocol||s.slashesCount<2||!m(x.protocol)))&&(b[3]=[/(.*)/,"pathname"]);w<b.length;w++)"function"!==typeof(c=b[w])?(l=c[0],y=c[1],l!==l?x[y]=e:"string"===typeof l?~(p="@"===l?e.lastIndexOf(l):e.indexOf(l))&&("number"===typeof c[2]?(x[y]=e.slice(0,p),e=e.slice(p+c[2])):(x[y]=e.slice(p),e=e.slice(0,p))):(p=l.exec(e))&&(x[y]=p[1],e=e.slice(0,p.index)),x[y]=x[y]||a&&c[3]&&t[y]||"",c[4]&&(x[y]=x[y].toLowerCase())):e=c(e,x);n&&(x.query=n(x.query)),a&&t.slashes&&"/"!==x.pathname.charAt(0)&&(""!==x.pathname||""!==t.pathname)&&(x.pathname=function(e,t){if(""===e)return t;for(var n=(t||"/").split("/").slice(0,-1).concat(e.split("/")),r=n.length,o=n[r-1],a=!1,i=0;r--;)"."===n[r]?n.splice(r,1):".."===n[r]?(n.splice(r,1),i++):i&&(0===r&&(a=!0),n.splice(r,1),i--);return a&&n.unshift(""),"."!==o&&".."!==o||n.push(""),n.join("/")}(x.pathname,t.pathname)),"/"!==x.pathname.charAt(0)&&m(x.protocol)&&(x.pathname="/"+x.pathname),r(x.port,x.protocol)||(x.host=x.hostname,x.port=""),x.username=x.password="",x.auth&&(~(p=x.auth.indexOf(":"))?(x.username=x.auth.slice(0,p),x.username=encodeURIComponent(decodeURIComponent(x.username)),x.password=x.auth.slice(p+1),x.password=encodeURIComponent(decodeURIComponent(x.password))):x.username=encodeURIComponent(decodeURIComponent(x.auth)),x.auth=x.password?x.username+":"+x.password:x.username),x.origin="file:"!==x.protocol&&m(x.protocol)&&x.host?x.protocol+"//"+x.host:"null",x.href=x.toString()}g.prototype={set:function(e,t,n){var a=this;switch(e){case"query":"string"===typeof t&&t.length&&(t=(n||o.parse)(t)),a[e]=t;break;case"port":a[e]=t,r(t,a.protocol)?t&&(a.host=a.hostname+":"+t):(a.host=a.hostname,a[e]="");break;case"hostname":a[e]=t,a.port&&(t+=":"+a.port),a.host=t;break;case"host":a[e]=t,l.test(t)?(t=t.split(":"),a.port=t.pop(),a.hostname=t.join(":")):(a.hostname=t,a.port="");break;case"protocol":a.protocol=t.toLowerCase(),a.slashes=!n;break;case"pathname":case"hash":if(t){var i="pathname"===e?"/":"#";a[e]=t.charAt(0)!==i?i+t:t}else a[e]=t;break;case"username":case"password":a[e]=encodeURIComponent(t);break;case"auth":var s=t.indexOf(":");~s?(a.username=t.slice(0,s),a.username=encodeURIComponent(decodeURIComponent(a.username)),a.password=t.slice(s+1),a.password=encodeURIComponent(decodeURIComponent(a.password))):a.username=encodeURIComponent(decodeURIComponent(t))}for(var c=0;c<f.length;c++){var u=f[c];u[4]&&(a[u[1]]=a[u[1]].toLowerCase())}return a.auth=a.password?a.username+":"+a.password:a.username,a.origin="file:"!==a.protocol&&m(a.protocol)&&a.host?a.protocol+"//"+a.host:"null",a.href=a.toString(),a},toString:function(e){e&&"function"===typeof e||(e=o.stringify);var t,n=this,r=n.host,a=n.protocol;a&&":"!==a.charAt(a.length-1)&&(a+=":");var i=a+(n.protocol&&n.slashes||m(n.protocol)?"//":"");return n.username?(i+=n.username,n.password&&(i+=":"+n.password),i+="@"):n.password?(i+=":"+n.password,i+="@"):"file:"!==n.protocol&&m(n.protocol)&&!r&&"/"!==n.pathname&&(i+="@"),(":"===r[r.length-1]||l.test(n.hostname)&&!n.port)&&(r+=":"),i+=r+n.pathname,(t="object"===typeof n.query?e(n.query):n.query)&&(i+="?"!==t.charAt(0)?"?"+t:t),n.hash&&(i+=n.hash),i}},g.extractProtocol=v,g.location=h,g.trimLeft=d,g.qs=o,e.exports=g}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=e,n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,r){return n.f[r](e,t),t}),[]))},n.u=function(e){return"static/js/"+e+".411d950c.chunk.js"},n.miniCssF=function(e){},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){var e={},t="contentapp:";n.l=function(r,o,a,i){if(e[r])e[r].push(o);else{var s,l;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+a),s.src=r),e[r]=[o];var f=function(t,n){s.onerror=s.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),o&&o.forEach((function(e){return e(n)})),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}}(),n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},n.p="/contentapp/",function(){var e={179:0};n.f.j=function(t,r){var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var a=new Promise((function(n,r){o=e[t]=[n,r]}));r.push(o[2]=a);var i=n.p+n.u(t),s=new Error;n.l(i,(function(r){if(n.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+a+": "+i+")",s.name="ChunkLoadError",s.type=a,s.request=i,o[1](s)}}),"chunk-"+t,t)}};var t=function(t,r){var o,a,i=r[0],s=r[1],l=r[2],c=0;if(i.some((function(t){return 0!==e[t]}))){for(o in s)n.o(s,o)&&(n.m[o]=s[o]);if(l)l(n)}for(t&&t(r);c<i.length;c++)a=i[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunkcontentapp=self.webpackChunkcontentapp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}(),n.nc=void 0,function(){"use strict";var e=n(2791),t=n(1250);function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){if(e){if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,s=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(l){s=!0,o=l}finally{try{i||null==n.return||n.return()}finally{if(s)throw o}}return a}}(e,t)||o(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||o(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var u=n(2506),d=n.n(u);function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(){p=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",i=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(j){s=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof d?t:d,a=Object.create(o.prototype),i=new E(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return N()}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var s=w(i,n);if(s){if(s===u)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var l=c(e,t,n);if("normal"===l.type){if(r=n.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(r="completed",n.method="throw",n.arg=l.arg)}}}(e,n,i),a}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(j){return{type:"throw",arg:j}}}e.wrap=l;var u={};function d(){}function h(){}function m(){}var v={};s(v,o,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(C([])));y&&y!==t&&n.call(y,o)&&(v=y);var b=m.prototype=d.prototype=Object.create(v);function _(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function r(o,a,i,s){var l=c(e[o],e,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==f(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){r("next",e,i,s)}),(function(e){r("throw",e,i,s)})):t.resolve(d).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,s)}))}s(l.arg)}var o;this._invoke=function(e,n){function a(){return new t((function(t,o){r(e,n,t,o)}))}return o=o?o.then(a,a):a()}}function w(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var r=c(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,u;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function C(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:N}}function N(){return{value:void 0,done:!0}}return h.prototype=m,s(b,"constructor",m),s(m,"constructor",h),h.displayName=s(m,i,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,s(e,i,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},_(x.prototype),s(x.prototype,a,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new x(l(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},_(b),s(b,i,"Generator"),s(b,o,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=C,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(s&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:C(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function v(e,t,n){return t&&m(e.prototype,t),n&&m(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var g="\n",y="\0";function b(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=o(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){l=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(l)throw i}}}}var _,x,w=function(){function e(t){h(this,e);var n=t.command,r=t.headers,o=t.body,a=t.binaryBody,i=t.escapeHeaderValues,s=t.skipContentLengthHeader;this.command=n,this.headers=Object.assign({},r||{}),a?(this._binaryBody=a,this.isBinaryBody=!0):(this._body=o||"",this.isBinaryBody=!1),this.escapeHeaderValues=i||!1,this.skipContentLengthHeader=s||!1}return v(e,[{key:"body",get:function(){return!this._body&&this.isBinaryBody&&(this._body=(new TextDecoder).decode(this._binaryBody)),this._body}},{key:"binaryBody",get:function(){return this._binaryBody||this.isBinaryBody||(this._binaryBody=(new TextEncoder).encode(this._body)),this._binaryBody}},{key:"toString",value:function(){return this.serializeCmdAndHeaders()}},{key:"serialize",value:function(){var t=this.serializeCmdAndHeaders();return this.isBinaryBody?e.toUnit8Array(t,this._binaryBody).buffer:t+this._body+y}},{key:"serializeCmdAndHeaders",value:function(){var t=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(var n=0,r=Object.keys(this.headers||{});n<r.length;n++){var o=r[n],a=this.headers[o];this.escapeHeaderValues&&"CONNECT"!==this.command&&"CONNECTED"!==this.command?t.push("".concat(o,":").concat(e.hdrValueEscape("".concat(a)))):t.push("".concat(o,":").concat(a))}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&t.push("content-length:".concat(this.bodyLength())),t.join(g)+g+g}},{key:"isBodyEmpty",value:function(){return 0===this.bodyLength()}},{key:"bodyLength",value:function(){var e=this.binaryBody;return e?e.length:0}}],[{key:"fromRawFrame",value:function(t,n){var r,o={},a=function(e){return e.replace(/^\s+|\s+$/g,"")},i=b(t.headers.reverse());try{for(i.s();!(r=i.n()).done;){var s=r.value,l=(s.indexOf(":"),a(s[0])),c=a(s[1]);n&&"CONNECT"!==t.command&&"CONNECTED"!==t.command&&(c=e.hdrValueUnEscape(c)),o[l]=c}}catch(u){i.e(u)}finally{i.f()}return new e({command:t.command,headers:o,binaryBody:t.binaryBody,escapeHeaderValues:n})}},{key:"sizeOfUTF8",value:function(e){return e?(new TextEncoder).encode(e).length:0}},{key:"toUnit8Array",value:function(e,t){var n=(new TextEncoder).encode(e),r=new Uint8Array([0]),o=new Uint8Array(n.length+t.length+r.length);return o.set(n),o.set(t,n.length),o.set(r,n.length+t.length),o}},{key:"marshall",value:function(t){return new e(t).serialize()}},{key:"hdrValueEscape",value:function(e){return e.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}},{key:"hdrValueUnEscape",value:function(e){return e.replace(/\\r/g,"\r").replace(/\\n/g,"\n").replace(/\\c/g,":").replace(/\\\\/g,"\\")}}]),e}(),k=function(){function e(t,n){h(this,e),this.onFrame=t,this.onIncomingPing=n,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}return v(e,[{key:"parseChunk",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t=e instanceof ArrayBuffer?new Uint8Array(e):this._encoder.encode(e),n&&0!==t[t.length-1]){var r=new Uint8Array(t.length+1);r.set(t,0),r[t.length]=0,t=r}for(var o=0;o<t.length;o++){var a=t[o];this._onByte(a)}}},{key:"_collectFrame",value:function(e){0!==e&&13!==e&&(10!==e?(this._onByte=this._collectCommand,this._reinjectByte(e)):this.onIncomingPing())}},{key:"_collectCommand",value:function(e){if(13!==e)return 10===e?(this._results.command=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}},{key:"_collectHeaders",value:function(e){13!==e&&(10!==e?(this._onByte=this._collectHeaderKey,this._reinjectByte(e)):this._setupCollectBody())}},{key:"_reinjectByte",value:function(e){this._onByte(e)}},{key:"_collectHeaderKey",value:function(e){if(58===e)return this._headerKey=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaderValue);this._consumeByte(e)}},{key:"_collectHeaderValue",value:function(e){if(13!==e)return 10===e?(this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,void(this._onByte=this._collectHeaders)):void this._consumeByte(e)}},{key:"_setupCollectBody",value:function(){var e=this._results.headers.filter((function(e){return"content-length"===e[0]}))[0];e?(this._bodyBytesRemaining=parseInt(e[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}},{key:"_collectBodyNullTerminated",value:function(e){0!==e?this._consumeByte(e):this._retrievedBody()}},{key:"_collectBodyFixedSize",value:function(e){0!==this._bodyBytesRemaining--?this._consumeByte(e):this._retrievedBody()}},{key:"_retrievedBody",value:function(){this._results.binaryBody=this._consumeTokenAsRaw(),this.onFrame(this._results),this._initState()}},{key:"_consumeByte",value:function(e){this._token.push(e)}},{key:"_consumeTokenAsUTF8",value:function(){return this._decoder.decode(this._consumeTokenAsRaw())}},{key:"_consumeTokenAsRaw",value:function(){var e=new Uint8Array(this._token);return this._token=[],e}},{key:"_initState",value:function(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}]),e}();!function(e){e[e.CONNECTING=0]="CONNECTING",e[e.OPEN=1]="OPEN",e[e.CLOSING=2]="CLOSING",e[e.CLOSED=3]="CLOSED"}(_||(_={})),function(e){e[e.ACTIVE=0]="ACTIVE",e[e.DEACTIVATING=1]="DEACTIVATING",e[e.INACTIVE=2]="INACTIVE"}(x||(x={}));var S=function(){function e(t){h(this,e),this.versions=t}return v(e,[{key:"supportedVersions",value:function(){return this.versions.join(",")}},{key:"protocolVersions",value:function(){return this.versions.map((function(e){return"v".concat(e.replace(".",""),".stomp")}))}}]),e}();S.V1_0="1.0",S.V1_1="1.1",S.V1_2="1.2",S.default=new S([S.V1_0,S.V1_1,S.V1_2]);var E=function(){function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};h(this,e),this._client=t,this._webSocket=n,this._serverFrameHandlers={CONNECTED:function(e){r.debug("connected to server ".concat(e.headers.server)),r._connected=!0,r._connectedVersion=e.headers.version,r._connectedVersion===S.V1_2&&(r._escapeHeaderValues=!0),r._setupHeartbeat(e.headers),r.onConnect(e)},MESSAGE:function(e){var t=e.headers.subscription,n=r._subscriptions[t]||r.onUnhandledMessage,o=e,a=r,i=r._connectedVersion===S.V1_2?o.headers.ack:o.headers["message-id"];o.ack=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return a.ack(i,t,e)},o.nack=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return a.nack(i,t,e)},n(o)},RECEIPT:function(e){var t=r._receiptWatchers[e.headers["receipt-id"]];t?(t(e),delete r._receiptWatchers[e.headers["receipt-id"]]):r.onUnhandledReceipt(e)},ERROR:function(e){r.onStompError(e)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.configure(o)}return v(e,[{key:"connectedVersion",get:function(){return this._connectedVersion}},{key:"connected",get:function(){return this._connected}},{key:"configure",value:function(e){Object.assign(this,e)}},{key:"start",value:function(){var e=this,t=new k((function(t){var n=w.fromRawFrame(t,e._escapeHeaderValues);e.logRawCommunication||e.debug("<<< ".concat(n)),(e._serverFrameHandlers[n.command]||e.onUnhandledFrame)(n)}),(function(){e.debug("<<< PONG")}));this._webSocket.onmessage=function(n){if(e.debug("Received data"),e._lastServerActivityTS=Date.now(),e.logRawCommunication){var r=n.data instanceof ArrayBuffer?(new TextDecoder).decode(n.data):n.data;e.debug("<<< ".concat(r))}t.parseChunk(n.data,e.appendMissingNULLonIncoming)},this._onclose=function(t){e.debug("Connection closed to ".concat(e._client.brokerURL)),e._cleanUp(),e.onWebSocketClose(t)},this._webSocket.onclose=this._onclose,this._webSocket.onerror=function(t){e.onWebSocketError(t)},this._webSocket.onopen=function(){var t=Object.assign({},e.connectHeaders);e.debug("Web Socket Opened..."),t["accept-version"]=e.stompVersions.supportedVersions(),t["heart-beat"]=[e.heartbeatOutgoing,e.heartbeatIncoming].join(","),e._transmit({command:"CONNECT",headers:t})}}},{key:"_setupHeartbeat",value:function(e){var t=this;if((e.version===S.V1_1||e.version===S.V1_2)&&e["heart-beat"]){var n=e["heart-beat"].split(",").map((function(e){return parseInt(e,10)})),r=a(n,2),o=r[0],i=r[1];if(0!==this.heartbeatOutgoing&&0!==i){var s=Math.max(this.heartbeatOutgoing,i);this.debug("send PING every ".concat(s,"ms")),this._pinger=setInterval((function(){t._webSocket.readyState===_.OPEN&&(t._webSocket.send(g),t.debug(">>> PING"))}),s)}if(0!==this.heartbeatIncoming&&0!==o){var l=Math.max(this.heartbeatIncoming,o);this.debug("check PONG every ".concat(l,"ms")),this._ponger=setInterval((function(){var e=Date.now()-t._lastServerActivityTS;e>2*l&&(t.debug("did not receive server activity for the last ".concat(e,"ms")),t._closeOrDiscardWebsocket())}),l)}}}},{key:"_closeOrDiscardWebsocket",value:function(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this._discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}},{key:"forceDisconnect",value:function(){this._webSocket&&(this._webSocket.readyState!==_.CONNECTING&&this._webSocket.readyState!==_.OPEN||this._closeOrDiscardWebsocket())}},{key:"_closeWebsocket",value:function(){this._webSocket.onmessage=function(){},this._webSocket.close()}},{key:"_discardWebsocket",value:function(){var e,t,n=this;this._webSocket.terminate||(e=this._webSocket,t=function(e){return n.debug(e)},e.terminate=function(){var e=function(){};this.onerror=e,this.onmessage=e,this.onopen=e;var n=new Date,r=this.onclose;this.onclose=function(e){var r=(new Date).getTime()-n.getTime();t("Discarded socket closed after ".concat(r,"ms, with code/reason: ").concat(e.code,"/").concat(e.reason))},this.close(),r.call(this,{code:4001,reason:"Heartbeat failure, discarding the socket",wasClean:!1})}),this._webSocket.terminate()}},{key:"_transmit",value:function(e){var t=e.command,n=e.headers,r=e.body,o=e.binaryBody,a=e.skipContentLengthHeader,i=new w({command:t,headers:n,body:r,binaryBody:o,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:a}),s=i.serialize();if(this.logRawCommunication?this.debug(">>> ".concat(s)):this.debug(">>> ".concat(i)),this.forceBinaryWSFrames&&"string"===typeof s&&(s=(new TextEncoder).encode(s)),"string"===typeof s&&this.splitLargeFrames)for(var l=s;l.length>0;){var c=l.substring(0,this.maxWebSocketChunkSize);l=l.substring(this.maxWebSocketChunkSize),this._webSocket.send(c),this.debug("chunk sent = ".concat(c.length,", remaining = ").concat(l.length))}else this._webSocket.send(s)}},{key:"dispose",value:function(){var e=this;if(this.connected)try{var t=Object.assign({},this.disconnectHeaders);t.receipt||(t.receipt="close-".concat(this._counter++)),this.watchForReceipt(t.receipt,(function(t){e._closeWebsocket(),e._cleanUp(),e.onDisconnect(t)})),this._transmit({command:"DISCONNECT",headers:t})}catch(n){this.debug("Ignoring error during disconnect ".concat(n))}else this._webSocket.readyState!==_.CONNECTING&&this._webSocket.readyState!==_.OPEN||this._closeWebsocket()}},{key:"_cleanUp",value:function(){this._connected=!1,this._pinger&&clearInterval(this._pinger),this._ponger&&clearInterval(this._ponger)}},{key:"publish",value:function(e){var t=e.destination,n=e.headers,r=e.body,o=e.binaryBody,a=e.skipContentLengthHeader,i=Object.assign({destination:t},n);this._transmit({command:"SEND",headers:i,body:r,binaryBody:o,skipContentLengthHeader:a})}},{key:"watchForReceipt",value:function(e,t){this._receiptWatchers[e]=t}},{key:"subscribe",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(n=Object.assign({},n)).id||(n.id="sub-".concat(this._counter++)),n.destination=e,this._subscriptions[n.id]=t,this._transmit({command:"SUBSCRIBE",headers:n});var r=this;return{id:n.id,unsubscribe:function(e){return r.unsubscribe(n.id,e)}}}},{key:"unsubscribe",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t=Object.assign({},t),delete this._subscriptions[e],t.id=e,this._transmit({command:"UNSUBSCRIBE",headers:t})}},{key:"begin",value:function(e){var t=e||"tx-".concat(this._counter++);this._transmit({command:"BEGIN",headers:{transaction:t}});var n=this;return{id:t,commit:function(){n.commit(t)},abort:function(){n.abort(t)}}}},{key:"commit",value:function(e){this._transmit({command:"COMMIT",headers:{transaction:e}})}},{key:"abort",value:function(e){this._transmit({command:"ABORT",headers:{transaction:e}})}},{key:"ack",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n=Object.assign({},n),this._connectedVersion===S.V1_2?n.id=e:n["message-id"]=e,n.subscription=t,this._transmit({command:"ACK",headers:n})}},{key:"nack",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n=Object.assign({},n),this._connectedVersion===S.V1_2?n.id=e:n["message-id"]=e,n.subscription=t,this._transmit({command:"NACK",headers:n})}}]),e}(),C=function(e,t,n,r){return new(n||(n=Promise))((function(o,a){function i(e){try{l(r.next(e))}catch(t){a(t)}}function s(e){try{l(r.throw(e))}catch(t){a(t)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,s)}l((r=r.apply(e,t||[])).next())}))},N=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,e),this.stompVersions=S.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8192,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.state=x.INACTIVE;var n=function(){};this.debug=n,this.beforeConnect=n,this.onConnect=n,this.onDisconnect=n,this.onUnhandledMessage=n,this.onUnhandledReceipt=n,this.onUnhandledFrame=n,this.onStompError=n,this.onWebSocketClose=n,this.onWebSocketError=n,this.logRawCommunication=!1,this.onChangeState=n,this.connectHeaders={},this._disconnectHeaders={},this.configure(t)}return v(e,[{key:"webSocket",get:function(){return this._stompHandler?this._stompHandler._webSocket:void 0}},{key:"disconnectHeaders",get:function(){return this._disconnectHeaders},set:function(e){this._disconnectHeaders=e,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}},{key:"connected",get:function(){return!!this._stompHandler&&this._stompHandler.connected}},{key:"connectedVersion",get:function(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}},{key:"active",get:function(){return this.state===x.ACTIVE}},{key:"_changeState",value:function(e){this.state=e,this.onChangeState(e)}},{key:"configure",value:function(e){Object.assign(this,e)}},{key:"activate",value:function(){if(this.state===x.DEACTIVATING)throw this.debug("Still DEACTIVATING, please await call to deactivate before trying to re-activate"),new Error("Still DEACTIVATING, can not activate now");this.active?this.debug("Already ACTIVE, ignoring request to activate"):(this._changeState(x.ACTIVE),this._connect())}},{key:"_connect",value:function(){return C(this,void 0,void 0,p().mark((function e(){var t,n=this;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.connected){e.next=3;break}return this.debug("STOMP: already connected, nothing to do"),e.abrupt("return");case 3:return e.next=5,this.beforeConnect();case 5:if(this.active){e.next=8;break}return this.debug("Client has been marked inactive, will not attempt to connect"),e.abrupt("return");case 8:this.connectionTimeout>0&&(this._connectionWatcher&&clearTimeout(this._connectionWatcher),this._connectionWatcher=setTimeout((function(){n.connected||(n.debug("Connection not established in ".concat(n.connectionTimeout,"ms, closing socket")),n.forceDisconnect())}),this.connectionTimeout)),this.debug("Opening Web Socket..."),t=this._createWebSocket(),this._stompHandler=new E(this,t,{debug:this.debug,stompVersions:this.stompVersions,connectHeaders:this.connectHeaders,disconnectHeaders:this._disconnectHeaders,heartbeatIncoming:this.heartbeatIncoming,heartbeatOutgoing:this.heartbeatOutgoing,splitLargeFrames:this.splitLargeFrames,maxWebSocketChunkSize:this.maxWebSocketChunkSize,forceBinaryWSFrames:this.forceBinaryWSFrames,logRawCommunication:this.logRawCommunication,appendMissingNULLonIncoming:this.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:this.discardWebsocketOnCommFailure,onConnect:function(e){if(n._connectionWatcher&&(clearTimeout(n._connectionWatcher),n._connectionWatcher=void 0),!n.active)return n.debug("STOMP got connected while deactivate was issued, will disconnect now"),void n._disposeStompHandler();n.onConnect(e)},onDisconnect:function(e){n.onDisconnect(e)},onStompError:function(e){n.onStompError(e)},onWebSocketClose:function(e){n._stompHandler=void 0,n.state===x.DEACTIVATING&&(n._resolveSocketClose(),n._resolveSocketClose=void 0,n._changeState(x.INACTIVE)),n.onWebSocketClose(e),n.active&&n._schedule_reconnect()},onWebSocketError:function(e){n.onWebSocketError(e)},onUnhandledMessage:function(e){n.onUnhandledMessage(e)},onUnhandledReceipt:function(e){n.onUnhandledReceipt(e)},onUnhandledFrame:function(e){n.onUnhandledFrame(e)}}),this._stompHandler.start();case 13:case"end":return e.stop()}}),e,this)})))}},{key:"_createWebSocket",value:function(){var e;return(e=this.webSocketFactory?this.webSocketFactory():new WebSocket(this.brokerURL,this.stompVersions.protocolVersions())).binaryType="arraybuffer",e}},{key:"_schedule_reconnect",value:function(){var e=this;this.reconnectDelay>0&&(this.debug("STOMP: scheduling reconnection in ".concat(this.reconnectDelay,"ms")),this._reconnector=setTimeout((function(){e._connect()}),this.reconnectDelay))}},{key:"deactivate",value:function(){return C(this,void 0,void 0,p().mark((function e(){var t,n=this;return p().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.state===x.ACTIVE){e.next=3;break}return this.debug("Already ".concat(x[this.state],", ignoring call to deactivate")),e.abrupt("return",Promise.resolve());case 3:if(this._changeState(x.DEACTIVATING),this._reconnector&&clearTimeout(this._reconnector),!this._stompHandler||this.webSocket.readyState===_.CLOSED){e.next=9;break}t=new Promise((function(e,t){n._resolveSocketClose=e})),e.next=11;break;case 9:return this._changeState(x.INACTIVE),e.abrupt("return",Promise.resolve());case 11:return this._disposeStompHandler(),e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this)})))}},{key:"forceDisconnect",value:function(){this._stompHandler&&this._stompHandler.forceDisconnect()}},{key:"_disposeStompHandler",value:function(){this._stompHandler&&(this._stompHandler.dispose(),this._stompHandler=null)}},{key:"publish",value:function(e){this._stompHandler.publish(e)}},{key:"watchForReceipt",value:function(e,t){this._stompHandler.watchForReceipt(e,t)}},{key:"subscribe",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._stompHandler.subscribe(e,t,n)}},{key:"unsubscribe",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._stompHandler.unsubscribe(e,t)}},{key:"begin",value:function(e){return this._stompHandler.begin(e)}},{key:"commit",value:function(e){this._stompHandler.commit(e)}},{key:"abort",value:function(e){this._stompHandler.abort(e)}},{key:"ack",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this._stompHandler.ack(e,t,n)}},{key:"nack",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this._stompHandler.nack(e,t,n)}}]),e}();var j=(0,e.createContext)(void 0),O=["url","children","stompClientOptions"];function T(t){var n=t.url,r=t.children,o=t.stompClientOptions,i=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(t,O);o&&(i=o);var s=a((0,e.useState)(void 0),2),l=s[0],u=s[1],f=(0,e.useRef)(new Map);(0,e.useEffect)((function(){var e=new N(i);return i.brokerURL||i.webSocketFactory||(e.webSocketFactory=function(){var e,t,r=new URL(n,null==(e=window)||null==(t=e.location)?void 0:t.href);if("http:"===r.protocol||"https:"===r.protocol)return new(d())(n);if("ws:"===r.protocol||"wss:"===r.protocol)return new WebSocket(n);throw new Error("Protocol not supported")}),e.onConnect=function(t){i.onConnect&&i.onConnect(t),f.current.forEach((function(t){t.subscription=e.subscribe(t.destination,t.callback,t.headers)})),u(e)},e.onWebSocketClose=function(e){i.onWebSocketClose&&i.onWebSocketClose(e),u(void 0)},i.onStompError||(e.onStompError=function(e){throw e}),e.activate(),function(){e.deactivate()}}),[n].concat(c(Object.values(i))));return e.createElement(j.Provider,{value:{client:l,subscribe:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=Math.random().toString(36).substr(2,9),o={destination:e,callback:t,headers:n};return f.current.set(r,o),l&&l.connected&&(o.subscription=l.subscribe(e,t,n)),function(){var e=f.current.get(r);e.subscription&&e.subscription.unsubscribe(),f.current.delete(r)}}}},r)}function P(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=(0,e.useContext)(j);if(void 0===o)throw new Error("There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs");var a=(0,e.useRef)(n),i=Array.isArray(t)?t:[t];a.current=n,(0,e.useEffect)((function(){var e=[];return i.forEach((function(t){return e.push(o.subscribe(t,(function(e){a.current(e)}),r))})),function(){e.forEach((function(e){e()}))}}),[Object.values(i).toString(),Object.values(r).toString()])}function R(){var t=(0,e.useContext)(j);if(void 0===t)throw new Error("There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs");return t.client}new Map;new Map;function A(e,t){if(e){var n=function(e){if(!e)return;return{text_color:F(e.text_color),background_color:F(e.background_color),text:e.display_text}}(e);return t&&(n.className=t),n}}function I(e){if(!e)return!1;var t=Object.entries(e);if(t.length<=0)return!1;for(var n=!1,r=0,o=t;r<o.length;r++){var i=a(o[r],2);i[0];i[1]&&(n=!0)}return!!n}function L(e,t,n){if(console.log("sendResultMsg start. id="+t),e){var r={id:t,result:n};e.publish({destination:"/app/wsresult",body:JSON.stringify(r)}),console.log("sendResultMsg end. id="+t)}}function D(e,t,n){var r="/monitor/"+e+"_"+t;return null!=n&&(r=r+"_"+n),r}function F(e){return null==e||e.startsWith("#")?e:"#"+e}function z(e){return F((null===e||void 0===e?void 0:e.text_color)||(null===e||void 0===e?void 0:e.display_color))}function U(e,t){if(e&&t){var n=c(e.matchAll(/(\d+)(\u6708|\u65e5|\u6642|\u5206)/g)),r="";return n.forEach((function(e){var n=e[1],o=e[2];r+="".concat(n,'<span style="font-size:').concat(t,'">').concat(o,"</span>")})),r}}function B(e){var t=e;return t&&!function(e){return/<[^>]+>/gi.test(e)}(t)&&(t=(t=t.replace(/ /g,"&nbsp;")).replace(/\u3000/g,"&nbsp;&nbsp;")),t}function M(e){return(e||0).toString().replace(/(\d)(?=(?:\d{3})+$)/g,"$1,")}var W=n(184),H=function(e){var t,n={};return e.text_color&&(n.color=F(e.text_color)),e.background_color&&(n.backgroundColor=F(e.background_color)),(0,W.jsx)("div",{className:null===e||void 0===e||null===(t=e.className)||void 0===t?void 0:t.trim(),style:n,children:e.children})};function V(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}var $=n(7441),q=n(9613),K=n.n(q);var G=function(e){function t(e,r,l,c,f){for(var p,h,m,v,_,w=0,k=0,S=0,E=0,C=0,R=0,I=m=p=0,D=0,F=0,z=0,U=0,B=l.length,M=B-1,W="",H="",V="",$="";D<B;){if(h=l.charCodeAt(D),D===M&&0!==k+E+S+w&&(0!==k&&(h=47===k?10:47),E=S=w=0,B++,M++),0===k+E+S+w){if(D===M&&(0<F&&(W=W.replace(d,"")),0<W.trim().length)){switch(h){case 32:case 9:case 59:case 13:case 10:break;default:W+=l.charAt(D)}h=59}switch(h){case 123:for(p=(W=W.trim()).charCodeAt(0),m=1,U=++D;D<B;){switch(h=l.charCodeAt(D)){case 123:m++;break;case 125:m--;break;case 47:switch(h=l.charCodeAt(D+1)){case 42:case 47:e:{for(I=D+1;I<M;++I)switch(l.charCodeAt(I)){case 47:if(42===h&&42===l.charCodeAt(I-1)&&D+2!==I){D=I+1;break e}break;case 10:if(47===h){D=I+1;break e}}D=I}}break;case 91:h++;case 40:h++;case 34:case 39:for(;D++<M&&l.charCodeAt(D)!==h;);}if(0===m)break;D++}if(m=l.substring(U,D),0===p&&(p=(W=W.replace(u,"").trim()).charCodeAt(0)),64===p){switch(0<F&&(W=W.replace(d,"")),h=W.charCodeAt(1)){case 100:case 109:case 115:case 45:F=r;break;default:F=P}if(U=(m=t(r,F,m,h,f+1)).length,0<A&&(_=s(3,m,F=n(P,W,z),r,j,N,U,h,f,c),W=F.join(""),void 0!==_&&0===(U=(m=_.trim()).length)&&(h=0,m="")),0<U)switch(h){case 115:W=W.replace(x,i);case 100:case 109:case 45:m=W+"{"+m+"}";break;case 107:m=(W=W.replace(g,"$1 $2"))+"{"+m+"}",m=1===T||2===T&&a("@"+m,3)?"@-webkit-"+m+"@"+m:"@"+m;break;default:m=W+m,112===c&&(H+=m,m="")}else m=""}else m=t(r,n(r,W,z),m,c,f+1);V+=m,m=z=F=I=p=0,W="",h=l.charCodeAt(++D);break;case 125:case 59:if(1<(U=(W=(0<F?W.replace(d,""):W).trim()).length))switch(0===I&&(p=W.charCodeAt(0),45===p||96<p&&123>p)&&(U=(W=W.replace(" ",":")).length),0<A&&void 0!==(_=s(1,W,r,e,j,N,H.length,c,f,c))&&0===(U=(W=_.trim()).length)&&(W="\0\0"),p=W.charCodeAt(0),h=W.charCodeAt(1),p){case 0:break;case 64:if(105===h||99===h){$+=W+l.charAt(D);break}default:58!==W.charCodeAt(U-1)&&(H+=o(W,p,h,W.charCodeAt(2)))}z=F=I=p=0,W="",h=l.charCodeAt(++D)}}switch(h){case 13:case 10:47===k?k=0:0===1+p&&107!==c&&0<W.length&&(F=1,W+="\0"),0<A*L&&s(0,W,r,e,j,N,H.length,c,f,c),N=1,j++;break;case 59:case 125:if(0===k+E+S+w){N++;break}default:switch(N++,v=l.charAt(D),h){case 9:case 32:if(0===E+w+k)switch(C){case 44:case 58:case 9:case 32:v="";break;default:32!==h&&(v=" ")}break;case 0:v="\\0";break;case 12:v="\\f";break;case 11:v="\\v";break;case 38:0===E+k+w&&(F=z=1,v="\f"+v);break;case 108:if(0===E+k+w+O&&0<I)switch(D-I){case 2:112===C&&58===l.charCodeAt(D-3)&&(O=C);case 8:111===R&&(O=R)}break;case 58:0===E+k+w&&(I=D);break;case 44:0===k+S+E+w&&(F=1,v+="\r");break;case 34:case 39:0===k&&(E=E===h?0:0===E?h:E);break;case 91:0===E+k+S&&w++;break;case 93:0===E+k+S&&w--;break;case 41:0===E+k+w&&S--;break;case 40:if(0===E+k+w){if(0===p)if(2*C+3*R===533);else p=1;S++}break;case 64:0===k+S+E+w+I+m&&(m=1);break;case 42:case 47:if(!(0<E+w+S))switch(k){case 0:switch(2*h+3*l.charCodeAt(D+1)){case 235:k=47;break;case 220:U=D,k=42}break;case 42:47===h&&42===C&&U+2!==D&&(33===l.charCodeAt(U+2)&&(H+=l.substring(U,D+1)),v="",k=0)}}0===k&&(W+=v)}R=C,C=h,D++}if(0<(U=H.length)){if(F=r,0<A&&(void 0!==(_=s(2,H,F,e,j,N,U,c,f,c))&&0===(H=_).length))return $+H+V;if(H=F.join(",")+"{"+H+"}",0!==T*O){switch(2!==T||a(H,2)||(O=0),O){case 111:H=H.replace(b,":-moz-$1")+H;break;case 112:H=H.replace(y,"::-webkit-input-$1")+H.replace(y,"::-moz-$1")+H.replace(y,":-ms-input-$1")+H}O=0}}return $+H+V}function n(e,t,n){var o=t.trim().split(m);t=o;var a=o.length,i=e.length;switch(i){case 0:case 1:var s=0;for(e=0===i?"":e[0]+" ";s<a;++s)t[s]=r(e,t[s],n).trim();break;default:var l=s=0;for(t=[];s<a;++s)for(var c=0;c<i;++c)t[l++]=r(e[c]+" ",o[s],n).trim()}return t}function r(e,t,n){var r=t.charCodeAt(0);switch(33>r&&(r=(t=t.trim()).charCodeAt(0)),r){case 38:return t.replace(v,"$1"+e.trim());case 58:return e.trim()+t.replace(v,"$1"+e.trim());default:if(0<1*n&&0<t.indexOf("\f"))return t.replace(v,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function o(e,t,n,r){var i=e+";",s=2*t+3*n+4*r;if(944===s){e=i.indexOf(":",9)+1;var l=i.substring(e,i.length-1).trim();return l=i.substring(0,e).trim()+l+";",1===T||2===T&&a(l,1)?"-webkit-"+l+l:l}if(0===T||2===T&&!a(i,1))return i;switch(s){case 1015:return 97===i.charCodeAt(10)?"-webkit-"+i+i:i;case 951:return 116===i.charCodeAt(3)?"-webkit-"+i+i:i;case 963:return 110===i.charCodeAt(5)?"-webkit-"+i+i:i;case 1009:if(100!==i.charCodeAt(4))break;case 969:case 942:return"-webkit-"+i+i;case 978:return"-webkit-"+i+"-moz-"+i+i;case 1019:case 983:return"-webkit-"+i+"-moz-"+i+"-ms-"+i+i;case 883:if(45===i.charCodeAt(8))return"-webkit-"+i+i;if(0<i.indexOf("image-set(",11))return i.replace(C,"$1-webkit-$2")+i;break;case 932:if(45===i.charCodeAt(4))switch(i.charCodeAt(5)){case 103:return"-webkit-box-"+i.replace("-grow","")+"-webkit-"+i+"-ms-"+i.replace("grow","positive")+i;case 115:return"-webkit-"+i+"-ms-"+i.replace("shrink","negative")+i;case 98:return"-webkit-"+i+"-ms-"+i.replace("basis","preferred-size")+i}return"-webkit-"+i+"-ms-"+i+i;case 964:return"-webkit-"+i+"-ms-flex-"+i+i;case 1023:if(99!==i.charCodeAt(8))break;return"-webkit-box-pack"+(l=i.substring(i.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+i+"-ms-flex-pack"+l+i;case 1005:return p.test(i)?i.replace(f,":-webkit-")+i.replace(f,":-moz-")+i:i;case 1e3:switch(t=(l=i.substring(13).trim()).indexOf("-")+1,l.charCodeAt(0)+l.charCodeAt(t)){case 226:l=i.replace(_,"tb");break;case 232:l=i.replace(_,"tb-rl");break;case 220:l=i.replace(_,"lr");break;default:return i}return"-webkit-"+i+"-ms-"+l+i;case 1017:if(-1===i.indexOf("sticky",9))break;case 975:switch(t=(i=e).length-10,s=(l=(33===i.charCodeAt(t)?i.substring(0,t):i).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|l.charCodeAt(7))){case 203:if(111>l.charCodeAt(8))break;case 115:i=i.replace(l,"-webkit-"+l)+";"+i;break;case 207:case 102:i=i.replace(l,"-webkit-"+(102<s?"inline-":"")+"box")+";"+i.replace(l,"-webkit-"+l)+";"+i.replace(l,"-ms-"+l+"box")+";"+i}return i+";";case 938:if(45===i.charCodeAt(5))switch(i.charCodeAt(6)){case 105:return l=i.replace("-items",""),"-webkit-"+i+"-webkit-box-"+l+"-ms-flex-"+l+i;case 115:return"-webkit-"+i+"-ms-flex-item-"+i.replace(k,"")+i;default:return"-webkit-"+i+"-ms-flex-line-pack"+i.replace("align-content","").replace(k,"")+i}break;case 973:case 989:if(45!==i.charCodeAt(3)||122===i.charCodeAt(4))break;case 931:case 953:if(!0===E.test(e))return 115===(l=e.substring(e.indexOf(":")+1)).charCodeAt(0)?o(e.replace("stretch","fill-available"),t,n,r).replace(":fill-available",":stretch"):i.replace(l,"-webkit-"+l)+i.replace(l,"-moz-"+l.replace("fill-",""))+i;break;case 962:if(i="-webkit-"+i+(102===i.charCodeAt(5)?"-ms-"+i:"")+i,211===n+r&&105===i.charCodeAt(13)&&0<i.indexOf("transform",10))return i.substring(0,i.indexOf(";",27)+1).replace(h,"$1-webkit-$2")+i}return i}function a(e,t){var n=e.indexOf(1===t?":":"{"),r=e.substring(0,3!==t?n:10);return n=e.substring(n+1,e.length-1),I(2!==t?r:r.replace(S,"$1"),n,t)}function i(e,t){var n=o(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return n!==t+";"?n.replace(w," or ($1)").substring(4):"("+t+")"}function s(e,t,n,r,o,a,i,s,l,u){for(var d,f=0,p=t;f<A;++f)switch(d=R[f].call(c,e,p,n,r,o,a,i,s,l,u)){case void 0:case!1:case!0:case null:break;default:p=d}if(p!==t)return p}function l(e){return void 0!==(e=e.prefix)&&(I=null,e?"function"!==typeof e?T=1:(T=2,I=e):T=0),l}function c(e,n){var r=e;if(33>r.charCodeAt(0)&&(r=r.trim()),r=[r],0<A){var o=s(-1,n,r,r,j,N,0,0,0,0);void 0!==o&&"string"===typeof o&&(n=o)}var a=t(P,r,n,0,0);return 0<A&&(void 0!==(o=s(-2,a,r,r,j,N,a.length,0,0,0))&&(a=o)),"",O=0,N=j=1,a}var u=/^\0+/g,d=/[\0\r\f]/g,f=/: */g,p=/zoo|gra/,h=/([,: ])(transform)/g,m=/,\r+?/g,v=/([\t\r\n ])*\f?&/g,g=/@(k\w+)\s*(\S*)\s*/,y=/::(place)/g,b=/:(read-only)/g,_=/[svh]\w+-[tblr]{2}/,x=/\(\s*(.*)\s*\)/g,w=/([\s\S]*?);/g,k=/-self|flex-/g,S=/[^]*?(:[rp][el]a[\w-]+)[^]*/,E=/stretch|:\s*\w+\-(?:conte|avail)/,C=/([^-])(image-set\()/,N=1,j=1,O=0,T=1,P=[],R=[],A=0,I=null,L=0;return c.use=function e(t){switch(t){case void 0:case null:A=R.length=0;break;default:if("function"===typeof t)R[A++]=t;else if("object"===typeof t)for(var n=0,r=t.length;n<r;++n)e(t[n]);else L=0|!!t}return e},c.set=l,void 0!==e&&l(e),c},Q={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var X=function(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}},J=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Y=X((function(e){return J.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),Z=n(2110),ee=n.n(Z);function te(){return(te=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var ne=function(e,t){for(var n=[e[0]],r=0,o=t.length;r<o;r+=1)n.push(t[r],e[r+1]);return n},re=function(e){return null!==e&&"object"==typeof e&&"[object Object]"===(e.toString?e.toString():Object.prototype.toString.call(e))&&!(0,$.typeOf)(e)},oe=Object.freeze([]),ae=Object.freeze({});function ie(e){return"function"==typeof e}function se(e){return e.displayName||e.name||"Component"}function le(e){return e&&"string"==typeof e.styledComponentId}var ce="undefined"!=typeof process&&({NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.SC_ATTR)||"data-styled",ue="undefined"!=typeof window&&"HTMLElement"in window,de=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"/contentapp",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_APP_URL:"/contentapp",REACT_APP_SERVER_CONTEXT:"/contentserver",REACT_APP_SERVER_URL:"http://*************:8080",REACT_APP_WEBSOCKET:"/ws"}.SC_DISABLE_SPEEDY));function fe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var pe=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,o=r;e>=o;)(o<<=1)<0&&fe(16,""+e);this.groupSizes=new Uint32Array(o),this.groupSizes.set(n),this.length=o;for(var a=r;a<o;a++)this.groupSizes[a]=0}for(var i=this.indexOfGroup(e+1),s=0,l=t.length;s<l;s++)this.tag.insertRule(i,t[s])&&(this.groupSizes[e]++,i++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var o=n;o<r;o++)this.tag.deleteRule(n)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),o=r+n,a=r;a<o;a++)t+=this.tag.getRule(a)+"/*!sc*/\n";return t},e}(),he=new Map,me=new Map,ve=1,ge=function(e){if(he.has(e))return he.get(e);for(;me.has(ve);)ve++;var t=ve++;return he.set(e,t),me.set(t,e),t},ye=function(e){return me.get(e)},be=function(e,t){t>=ve&&(ve=t+1),he.set(e,t),me.set(t,e)},_e="style["+ce+'][data-styled-version="5.3.6"]',xe=new RegExp("^"+ce+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),we=function(e,t,n){for(var r,o=n.split(","),a=0,i=o.length;a<i;a++)(r=o[a])&&e.registerName(t,r)},ke=function(e,t){for(var n=(t.textContent||"").split("/*!sc*/\n"),r=[],o=0,a=n.length;o<a;o++){var i=n[o].trim();if(i){var s=i.match(xe);if(s){var l=0|parseInt(s[1],10),c=s[2];0!==l&&(be(c,l),we(e,c,s[3]),e.getTag().insertRules(l,r)),r.length=0}else r.push(i)}}},Se=function(){return n.nc},Ee=function(e){var t=document.head,n=e||t,r=document.createElement("style"),o=function(e){for(var t=e.childNodes,n=t.length;n>=0;n--){var r=t[n];if(r&&1===r.nodeType&&r.hasAttribute(ce))return r}}(n),a=void 0!==o?o.nextSibling:null;r.setAttribute(ce,"active"),r.setAttribute("data-styled-version","5.3.6");var i=Se();return i&&r.setAttribute("nonce",i),n.insertBefore(r,a),r},Ce=function(){function e(e){var t=this.element=Ee(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var o=t[n];if(o.ownerNode===e)return o}fe(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),Ne=function(){function e(e){var t=this.element=Ee(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t),r=this.nodes[e];return this.element.insertBefore(n,r||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),je=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),Oe=ue,Te={isServer:!ue,useCSSOMInjection:!de},Pe=function(){function e(e,t,n){void 0===e&&(e=ae),void 0===t&&(t={}),this.options=te({},Te,{},e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&ue&&Oe&&(Oe=!1,function(e){for(var t=document.querySelectorAll(_e),n=0,r=t.length;n<r;n++){var o=t[n];o&&"active"!==o.getAttribute(ce)&&(ke(e,o),o.parentNode&&o.parentNode.removeChild(o))}}(this))}e.registerId=function(e){return ge(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(te({},this.options,{},t),this.gs,n&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(n=(t=this.options).isServer,r=t.useCSSOMInjection,o=t.target,e=n?new je(o):r?new Ce(o):new Ne(o),new pe(e)));var e,t,n,r,o},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(ge(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},t.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ge(e),n)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(ge(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),n=t.length,r="",o=0;o<n;o++){var a=ye(o);if(void 0!==a){var i=e.names.get(a),s=t.getGroup(o);if(i&&s&&i.size){var l=ce+".g"+o+'[id="'+a+'"]',c="";void 0!==i&&i.forEach((function(e){e.length>0&&(c+=e+",")})),r+=""+s+l+'{content:"'+c+'"}/*!sc*/\n'}}}return r}(this)},e}(),Re=/(a)(d)/gi,Ae=function(e){return String.fromCharCode(e+(e>25?39:97))};function Ie(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=Ae(t%52)+n;return(Ae(t%52)+n).replace(Re,"$1-$2")}var Le=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},De=function(e){return Le(5381,e)};function Fe(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(ie(n)&&!le(n))return!1}return!0}var ze=De("5.3.6"),Ue=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Fe(e),this.componentId=t,this.baseHash=Le(ze,t),this.baseStyle=n,Pe.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.componentId,o=[];if(this.baseStyle&&o.push(this.baseStyle.generateAndInjectStyles(e,t,n)),this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(r,this.staticRulesId))o.push(this.staticRulesId);else{var a=rt(this.rules,e,t,n).join(""),i=Ie(Le(this.baseHash,a)>>>0);if(!t.hasNameForId(r,i)){var s=n(a,"."+i,void 0,r);t.insertRules(r,i,s)}o.push(i),this.staticRulesId=i}else{for(var l=this.rules.length,c=Le(this.baseHash,n.hash),u="",d=0;d<l;d++){var f=this.rules[d];if("string"==typeof f)u+=f;else if(f){var p=rt(f,e,t,n),h=Array.isArray(p)?p.join(""):p;c=Le(c,h+d),u+=h}}if(u){var m=Ie(c>>>0);if(!t.hasNameForId(r,m)){var v=n(u,"."+m,void 0,r);t.insertRules(r,m,v)}o.push(m)}}return o.join(" ")},e}(),Be=/^\s*\/\/.*$/gm,Me=[":","[",".","#"];function We(e){var t,n,r,o,a=void 0===e?ae:e,i=a.options,s=void 0===i?ae:i,l=a.plugins,c=void 0===l?oe:l,u=new G(s),d=[],f=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(n,r,o,a,i,s,l,c,u,d){switch(n){case 1:if(0===u&&64===r.charCodeAt(0))return e(r+";"),"";break;case 2:if(0===c)return r+"/*|*/";break;case 3:switch(c){case 102:case 112:return e(o[0]+r),"";default:return r+(0===d?"/*|*/":"")}case-2:r.split("/*|*/}").forEach(t)}}}((function(e){d.push(e)})),p=function(e,r,a){return 0===r&&-1!==Me.indexOf(a[n.length])||a.match(o)?e:"."+t};function h(e,a,i,s){void 0===s&&(s="&");var l=e.replace(Be,""),c=a&&i?i+" "+a+" { "+l+" }":l;return t=s,n=a,r=new RegExp("\\"+n+"\\b","g"),o=new RegExp("(\\"+n+"\\b){2,}"),u(i||!a?"":a,c)}return u.use([].concat(c,[function(e,t,o){2===e&&o.length&&o[0].lastIndexOf(n)>0&&(o[0]=o[0].replace(r,p))},f,function(e){if(-2===e){var t=d;return d=[],t}}])),h.hash=c.length?c.reduce((function(e,t){return t.name||fe(15),Le(e,t.name)}),5381).toString():"",h}var He=e.createContext(),Ve=(He.Consumer,e.createContext()),$e=(Ve.Consumer,new Pe),qe=We();function Ke(){return(0,e.useContext)(He)||$e}function Ge(){return(0,e.useContext)(Ve)||qe}function Qe(t){var n=(0,e.useState)(t.stylisPlugins),r=n[0],o=n[1],a=Ke(),i=(0,e.useMemo)((function(){var e=a;return t.sheet?e=t.sheet:t.target&&(e=e.reconstructWithOptions({target:t.target},!1)),t.disableCSSOMInjection&&(e=e.reconstructWithOptions({useCSSOMInjection:!1})),e}),[t.disableCSSOMInjection,t.sheet,t.target]),s=(0,e.useMemo)((function(){return We({options:{prefix:!t.disableVendorPrefixes},plugins:r})}),[t.disableVendorPrefixes,r]);return(0,e.useEffect)((function(){K()(r,t.stylisPlugins)||o(t.stylisPlugins)}),[t.stylisPlugins]),e.createElement(He.Provider,{value:i},e.createElement(Ve.Provider,{value:s},t.children))}var Xe=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=qe);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.toString=function(){return fe(12,String(n.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=qe),this.name+e.hash},e}(),Je=/([A-Z])/,Ye=/([A-Z])/g,Ze=/^ms-/,et=function(e){return"-"+e.toLowerCase()};function tt(e){return Je.test(e)?e.replace(Ye,et).replace(Ze,"-ms-"):e}var nt=function(e){return null==e||!1===e||""===e};function rt(e,t,n,r){if(Array.isArray(e)){for(var o,a=[],i=0,s=e.length;i<s;i+=1)""!==(o=rt(e[i],t,n,r))&&(Array.isArray(o)?a.push.apply(a,o):a.push(o));return a}return nt(e)?"":le(e)?"."+e.styledComponentId:ie(e)?"function"!=typeof(l=e)||l.prototype&&l.prototype.isReactComponent||!t?e:rt(e(t),t,n,r):e instanceof Xe?n?(e.inject(n,r),e.getName(r)):e:re(e)?function e(t,n){var r,o,a=[];for(var i in t)t.hasOwnProperty(i)&&!nt(t[i])&&(Array.isArray(t[i])&&t[i].isCss||ie(t[i])?a.push(tt(i)+":",t[i],";"):re(t[i])?a.push.apply(a,e(t[i],i)):a.push(tt(i)+": "+(r=i,(null==(o=t[i])||"boolean"==typeof o||""===o?"":"number"!=typeof o||0===o||r in Q?String(o).trim():o+"px")+";")));return n?[n+" {"].concat(a,["}"]):a}(e):e.toString();var l}var ot=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function at(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return ie(e)||re(e)?ot(rt(ne(oe,[e].concat(n)))):0===n.length&&1===e.length&&"string"==typeof e[0]?e:ot(rt(ne(e,n)))}new Set;var it=function(e,t,n){return void 0===n&&(n=ae),e.theme!==n.theme&&e.theme||t||n.theme},st=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,lt=/(^-|-$)/g;function ct(e){return e.replace(st,"-").replace(lt,"")}var ut=function(e){return Ie(De(e)>>>0)};function dt(e){return"string"==typeof e&&!0}var ft=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},pt=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function ht(e,t,n){var r=e[n];ft(t)&&ft(r)?mt(r,t):e[n]=t}function mt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var o=0,a=n;o<a.length;o++){var i=a[o];if(ft(i))for(var s in i)pt(s)&&ht(e,i[s],s)}return e}var vt=e.createContext();vt.Consumer;var gt={};function yt(t,n,r){var o=le(t),a=!dt(t),i=n.attrs,s=void 0===i?oe:i,l=n.componentId,c=void 0===l?function(e,t){var n="string"!=typeof e?"sc":ct(e);gt[n]=(gt[n]||0)+1;var r=n+"-"+ut("5.3.6"+n+gt[n]);return t?t+"-"+r:r}(n.displayName,n.parentComponentId):l,u=n.displayName,d=void 0===u?function(e){return dt(e)?"styled."+e:"Styled("+se(e)+")"}(t):u,f=n.displayName&&n.componentId?ct(n.displayName)+"-"+n.componentId:n.componentId||c,p=o&&t.attrs?Array.prototype.concat(t.attrs,s).filter(Boolean):s,h=n.shouldForwardProp;o&&t.shouldForwardProp&&(h=n.shouldForwardProp?function(e,r,o){return t.shouldForwardProp(e,r,o)&&n.shouldForwardProp(e,r,o)}:t.shouldForwardProp);var m,v=new Ue(r,f,o?t.componentStyle:void 0),g=v.isStatic&&0===s.length,y=function(t,n){return function(t,n,r,o){var a=t.attrs,i=t.componentStyle,s=t.defaultProps,l=t.foldedComponentIds,c=t.shouldForwardProp,u=t.styledComponentId,d=t.target,f=function(e,t,n){void 0===e&&(e=ae);var r=te({},t,{theme:e}),o={};return n.forEach((function(e){var t,n,a,i=e;for(t in ie(i)&&(i=i(r)),i)r[t]=o[t]="className"===t?(n=o[t],a=i[t],n&&a?n+" "+a:n||a):i[t]})),[r,o]}(it(n,(0,e.useContext)(vt),s)||ae,n,a),p=f[0],h=f[1],m=function(e,t,n,r){var o=Ke(),a=Ge();return t?e.generateAndInjectStyles(ae,o,a):e.generateAndInjectStyles(n,o,a)}(i,o,p),v=r,g=h.$as||n.$as||h.as||n.as||d,y=dt(g),b=h!==n?te({},n,{},h):n,_={};for(var x in b)"$"!==x[0]&&"as"!==x&&("forwardedAs"===x?_.as=b[x]:(c?c(x,Y,g):!y||Y(x))&&(_[x]=b[x]));return n.style&&h.style!==n.style&&(_.style=te({},n.style,{},h.style)),_.className=Array.prototype.concat(l,u,m!==u?m:null,n.className,h.className).filter(Boolean).join(" "),_.ref=v,(0,e.createElement)(g,_)}(m,t,n,g)};return y.displayName=d,(m=e.forwardRef(y)).attrs=p,m.componentStyle=v,m.displayName=d,m.shouldForwardProp=h,m.foldedComponentIds=o?Array.prototype.concat(t.foldedComponentIds,t.styledComponentId):oe,m.styledComponentId=f,m.target=o?t.target:t,m.withComponent=function(e){var t=n.componentId,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(n,["componentId"]),a=t&&t+"-"+(dt(e)?e:ct(se(e)));return yt(e,te({},o,{attrs:p,componentId:a}),r)},Object.defineProperty(m,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=o?mt({},t.defaultProps,e):e}}),m.toString=function(){return"."+m.styledComponentId},a&&ee()(m,t,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),m}var bt=function(e){return function e(t,n,r){if(void 0===r&&(r=ae),!(0,$.isValidElementType)(n))return fe(1,String(n));var o=function(){return t(n,r,at.apply(void 0,arguments))};return o.withConfig=function(o){return e(t,n,te({},r,{},o))},o.attrs=function(o){return e(t,n,te({},r,{attrs:Array.prototype.concat(r.attrs,o).filter(Boolean)}))},o}(yt,e)};["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(e){bt[e]=bt(e)}));!function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=Fe(e),Pe.registerId(this.componentId+1)}var t=e.prototype;t.createStyles=function(e,t,n,r){var o=r(rt(this.rules,t,n,r).join(""),""),a=this.componentId+e;n.insertRules(a,a,o)},t.removeStyles=function(e,t){t.clearRules(this.componentId+e)},t.renderStyles=function(e,t,n,r){e>2&&Pe.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,r)}}();!function(){function t(){var t=this;this._emitSheetCSS=function(){var e=t.instance.toString();if(!e)return"";var n=Se();return"<style "+[n&&'nonce="'+n+'"',ce+'="true"','data-styled-version="5.3.6"'].filter(Boolean).join(" ")+">"+e+"</style>"},this.getStyleTags=function(){return t.sealed?fe(2):t._emitSheetCSS()},this.getStyleElement=function(){var n;if(t.sealed)return fe(2);var r=((n={})[ce]="",n["data-styled-version"]="5.3.6",n.dangerouslySetInnerHTML={__html:t.instance.toString()},n),o=Se();return o&&(r.nonce=o),[e.createElement("style",te({},r,{key:"sc-0-0"}))]},this.seal=function(){t.sealed=!0},this.instance=new Pe({isServer:!0}),this.sealed=!1}var n=t.prototype;n.collectStyles=function(t){return this.sealed?fe(2):e.createElement(Qe,{sheet:this.instance},t)},n.interleaveWithNodeStream=function(e){return fe(3)}}();var _t=bt;var xt=function(t){var n=(0,e.useRef)(t);return n.current=t,n};var wt,kt=function(t,n){var r=xt(t),o=(0,e.useRef)(null);return(0,e.useEffect)((function(){if(!("number"!==typeof n||n<0))return o.current=setTimeout((function(){r.current()}),n),function(){o.current&&clearTimeout(o.current)}}),[n]),(0,e.useCallback)((function(){o.current&&clearTimeout(o.current)}),[])},St=_t.span(wt||(wt=V(["\n  white-space: nowrap;\n  display: inline-block;\n  transform: scaleX(",");\n  translate: -","%;\n"])),(function(e){return e.scale}),(function(e){return e.translate}));var Et,Ct,Nt,jt,Ot=function(t){var n=a((0,e.useState)({scale:1,translate:0}),2),r=n[0],o=n[1],i=function(){var t=a((0,e.useState)([0,0]),2),n=t[0],r=t[1];return(0,e.useLayoutEffect)((function(){function e(){r([window.innerWidth,window.innerHeight])}return window.addEventListener("resize",e),e(),function(){return window.removeEventListener("resize",e)}}),[]),n}(),s=a(i,2),l=s[0],c=s[1],u=(0,e.useRef)(null);(0,e.useEffect)((function(){var e=u.current,t=e.offsetWidth,n=e.parentElement,r=t,a=null===n||void 0===n?void 0:n.offsetWidth,i=1;r&&a&&(i=a/r)<1&&o({scale:i,translate:(1-i)/2*100})}),[t.display_text,l,c]);var d=B((null===t||void 0===t?void 0:t.display_text)||(null===t||void 0===t?void 0:t.text));return(0,W.jsx)(St,{ref:u,dangerouslySetInnerHTML:{__html:d},scale:r.scale,translate:r.translate})},Tt=function(e){var t={};e.text_color&&(t.color=z(e)),e.background_color&&(t.backgroundColor=F(e.background_color));var n=e.className,r=B((null===e||void 0===e?void 0:e.display_text)||(null===e||void 0===e?void 0:e.text));return(0,W.jsxs)("div",{className:n,style:t,children:[e.scale&&(0,W.jsx)(Ot,l({},e)),!e.scale&&(0,W.jsx)("span",{dangerouslySetInnerHTML:{__html:r}}),e.unit&&(0,W.jsx)("span",{style:e.unitStyle,children:e.unit})]})},Pt=at(Ct||(Ct=V(["\n  "," ","s linear infinite none alternate\n"])),(function(e){return function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=at.apply(void 0,[e].concat(n)).join(""),a=ut(o);return new Xe(a,o)}(Et||(Et=V(["\n  0% {\n    color: ",";\n    background-color: ",";\n  }\n  100% {\n    color: ",";\n    background-color: ",";\n  }\n"])),z(e),F(e.background_color),Lt(e),F(e.lighting_background_color))}),(function(e){return e.blink_speed/1e3})),Rt=_t.div(Nt||(Nt=V(["\n  animation: ",";\n  animation-play-state: running;\n  animation-fill-mode: forwards;\n  position: relative;\n"])),Pt),At=_t.div.attrs((function(e){return{style:{color:z(e),backgroundColor:F(e.background_color)}}}))(jt||(jt=V(["\n  position: relative;\n"]))),It=function(e){var t,n,r,o=l(l({},e.showInfo),e.blink_setting);if("1"===e.lightingStatus)o.text_color=z(e.showInfo),o.background_color=F(null===(t=e.showInfo)||void 0===t?void 0:t.background_color);else if("2"===e.lightingStatus){var a;o.text_color=Lt(e),o.background_color=F(null===(a=e.blink_setting)||void 0===a?void 0:a.lighting_background_color)}return"3"===e.lightingStatus?(0,W.jsx)("div",{className:e.className,children:(0,W.jsx)(Rt,l(l({},o),{},{children:(0,W.jsx)(Tt,{display_text:null===(n=e.showInfo)||void 0===n?void 0:n.display_text})}))}):(0,W.jsx)("div",{className:e.className,children:(0,W.jsx)(At,l(l({},o),{},{children:(0,W.jsx)(Tt,{display_text:null===(r=e.showInfo)||void 0===r?void 0:r.display_text})}))})};function Lt(e){var t=(null===e||void 0===e?void 0:e.blink_setting)||(null===e||void 0===e?void 0:e.lighting_setting);return t?null!==t&&void 0!==t&&t.lighting_text_color?F(null===t||void 0===t?void 0:t.lighting_text_color):null!==t&&void 0!==t&&t.lighting_display_color?F(null===t||void 0===t?void 0:t.lighting_display_color):void 0:null!==e&&void 0!==e&&e.lighting_text_color?F(null===e||void 0===e?void 0:e.lighting_text_color):null!==e&&void 0!==e&&e.lighting_display_color?F(null===e||void 0===e?void 0:e.lighting_display_color):""}function Dt(e,t){var n,r,o,a;return e||(e={})," "===(null===(n=e)||void 0===n?void 0:n.display_text)?e:null!==(r=e)&&void 0!==r&&r.display_text?(null!==(o=e)&&void 0!==o&&o.text_color||!t||(e.text_color=t.text_color),null!==(a=e)&&void 0!==a&&a.background_color||!t||(e.background_color=t.background_color),e):(e.display_text=" ",e)}var Ft,zt=function(t){var n,r,o,i=a((0,e.useState)(!1),2),s=i[0],c=i[1],u=a((0,e.useState)(null),2),d=u[0],f=u[1],p=(0,e.useRef)(null);(0,e.useEffect)((function(){var e,n,r,o,a;(c(!1),p.current&&console.log("BlinkBlock timer is running."),"3"===(null===t||void 0===t||null===(e=t.blink_setting)||void 0===e?void 0:e.lighting_status)&&(null===t||void 0===t||null===(n=t.blink_setting)||void 0===n?void 0:n.blink_speed)>0&&(null===t||void 0===t||null===(r=t.blink_setting)||void 0===r?void 0:r.blink_time)>0)&&(console.log("BlinkBlock setDelay. oldDelay: "+d+". newDelay:"+1e3*(null===t||void 0===t||null===(o=t.blink_setting)||void 0===o?void 0:o.blink_time)),f(1e3*(null===t||void 0===t||null===(a=t.blink_setting)||void 0===a?void 0:a.blink_time)))}),[null===t||void 0===t?void 0:t.blink_setting]),p.current=kt((function(){c(!0),f(null),console.log("BlinkBlock useTimeout. With delay: "+d)}),d),(0,e.useEffect)((function(){return function(){p.current&&(console.log("BlinkBlock clear timer when unmouting."),p.current())}}),[]);var h=null===t||void 0===t||null===(n=t.blink_setting)||void 0===n?void 0:n.lighting_status;return s&&(console.log("BlinkBlock blinkTimeout true. lightingStatus: "+h),h="1"),((null===t||void 0===t||null===(r=t.blink_setting)||void 0===r?void 0:r.blink_speed)<=0||(null===t||void 0===t||null===(o=t.blink_setting)||void 0===o?void 0:o.blink_time)<=0)&&(h="1"),(0,W.jsx)(W.Fragment,{children:Array.isArray(t.block)&&t.block.map((function(e,n){return(0,W.jsx)(It,l(l({},e),{},{lightingStatus:h,blink_setting:null===t||void 0===t?void 0:t.blink_setting}),n)}))})},Ut=32,Bt=function(e){var t;t=e.showDelopy?"grid-cols-vehicle-deploy":"grid-cols-vehicle-nodeploy";var n=A(e,"".concat("col-span-full"," flex flex-col items-center"));return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("div",{className:"grid ".concat(t),children:(0,W.jsx)(H,l(l({},n),{},{children:e.display_text&&(0,W.jsx)("span",{children:e.display_text})}))}),e.car_name.map((function(t,n){return(0,W.jsx)(Mt,l(l({},e),{},{index:n}),n)}))]})},Mt=function(e){var t,n,r,o,a;e.showDelopy&&e.deployment&&e.lighting_setting&&(t=e.deployment[e.index]),e.car_name&&e.lighting_setting&&(n=e.car_name[e.index]),e.town_name&&e.lighting_setting&&(r=e.town_name[e.index]),e.disaster_type&&e.lighting_setting&&(o=e.disaster_type[e.index]),e.avm_dynamic_state&&e.lighting_setting&&(a=e.avm_dynamic_state[e.index]);var i=1;e.lighting_setting&&e.lighting_setting[e.index]&&(i=e.lighting_setting[e.index].lighting_status);var s=e.car_name[e.index];s||(s=e.town_name[e.index]),s||(s=e.disaster_type[e.index]),s||(s=e.avm_dynamic_state[e.index]),s||(s=e.deployment[e.index]),t=Dt(t,s),n=Dt(n,s),r=Dt(r,s),o=Dt(o,s),a=Dt(a,s);var c,u=l({},t),d=l({},t),f=l({},n),p=l({},r),h=l({},o),m=l({},a);u.display_text=" ",d.display_text=" ",f.display_text=" ",p.display_text=" ",h.display_text=" ",m.display_text=" ",3!==i&&(u.background_color=void 0,d.background_color=void 0,f.background_color=void 0,p.background_color=void 0,h.background_color=void 0,m.background_color=void 0);var v=[];return e.showDelopy?(c="grid-cols-vehicle-deploy",e.startRow+e.index<=16&&v.push({showInfo:u,className:"col-span-1"}),v.push({showInfo:t,className:"col-span-1 col-start-2"}),v.push({showInfo:d,className:"col-span-1"}),v.push({showInfo:n,className:"col-span-4 col-start-4"}),v.push({showInfo:f,className:"col-span-1"}),v.push({showInfo:r,className:"col-span-6 col-start-9"}),v.push({showInfo:p,className:"col-span-1"}),v.push({showInfo:o,className:"col-span-2 col-start-16"}),v.push({showInfo:h,className:"col-span-1"}),v.push({showInfo:a,className:"col-span-2 col-start-19"}),e.startRow+e.index>16&&v.push({showInfo:m,className:"col-span-1"})):(c="grid-cols-vehicle-nodeploy",v.push({showInfo:d,className:"col-span-1"}),v.push({showInfo:n,className:"col-span-4 col-start-2"}),v.push({showInfo:f,className:"col-span-1"}),v.push({showInfo:r,className:"col-span-6 col-start-7"}),v.push({showInfo:p,className:"col-span-1"}),v.push({showInfo:o,className:"col-span-2 col-start-14"}),v.push({showInfo:h,className:"col-span-1"}),v.push({showInfo:a,className:"col-span-2 col-start-17"}),e.startRow+e.index>16&&v.push({showInfo:m,className:"col-span-1"})),(0,W.jsxs)(W.Fragment,{children:[e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(c),children:(0,W.jsx)(zt,{block:v,blink_setting:e.lighting_setting[e.index]})}),!e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(c),children:(0,W.jsx)(zt,{block:v,blink_setting:e.lighting_setting[e.index]})})]})},Wt=function(e){if(I(e)){var t=1===e.is_deployment,n=0,r=[];if(e.title_name){var o,a=b(e.title_name);try{for(a.s();!(o=a.n()).done;){var i=o.value;if(!i.car_name)return;var s=i.car_name.length+1;if(n+s<=Ut)r.push(i),n+=s;else if(n<=30){var c,u,d,f,p,h,m=(s=Ut-n)-1,v=l({},i);v.deployment=null===(c=v.deployment)||void 0===c?void 0:c.slice(0,m),v.car_name=null===(u=v.car_name)||void 0===u?void 0:u.slice(0,m),v.town_name=null===(d=v.town_name)||void 0===d?void 0:d.slice(0,m),v.disaster_type=null===(f=v.disaster_type)||void 0===f?void 0:f.slice(0,m),v.avm_dynamic_state=null===(p=v.avm_dynamic_state)||void 0===p?void 0:p.slice(0,m),v.lighting_setting=null===(h=v.lighting_setting)||void 0===h?void 0:h.slice(0,m),r.push(v),n+=s}}}catch(y){a.e(y)}finally{a.f()}var g=1;return(0,W.jsx)(W.Fragment,{children:I(e)&&(0,W.jsx)("div",{className:"grid grid-cols-2 grid-rows-16 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]",children:r.map((function(e,n){var r,o=g;return g=g+(null===e||void 0===e||null===(r=e.car_name)||void 0===r?void 0:r.length)+1,(0,W.jsx)(Bt,l(l({},e),{},{index:n,showDelopy:t,startRow:o}),n)}))})})}}},Ht=function(e){return 1!==e.sourceDispPattern?"grid-cols-2 grid-rows-16 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]":"left"==e.column_position?"grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pr-[16px]":"right"==e.column_position?"grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pl-[16px]":"grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]"},Vt=function(e){var t;t=e.showDelopy?"grid-cols-quarter-vehicle-deploy":"grid-cols-quarter-vehicle-nodeploy";var n=A(e.title,"".concat("col-span-full"," flex flex-col items-center"));return(0,W.jsxs)(W.Fragment,{children:[e.title&&(0,W.jsx)("div",{className:"grid ".concat(t),children:(0,W.jsx)(H,l(l({},n),{},{children:(0,W.jsx)("span",{children:e.title.display_text})}))}),!e.title&&(0,W.jsx)($t,l({},e))]})},$t=function(e){var t,n,r,o,a;e.showDelopy&&e.deployment&&(t=e.deployment),e.car_name&&(n=e.car_name),e.town_name&&(r=e.town_name),e.disaster_type&&(o=e.disaster_type),e.avm_dynamic_state&&(a=e.avm_dynamic_state);var i=e.lighting_setting?e.lighting_setting.lighting_status:1,s=[e.car_name,e.town_name,e.disaster_type,e.avm_dynamic_state,e.deployment].find((function(e){return e}));t=Dt(t,s),n=Dt(n,s),r=Dt(r,s),o=Dt(o,s),a=Dt(a,s);var c,u=l({},t),d=l({},t),f=l({},n),p=l({},r),h=l({},o),m=l({},a);u.display_text=" ",d.display_text=" ",f.display_text=" ",p.display_text=" ",h.display_text=" ",m.display_text=" ",3!==i&&(u.background_color=void 0,d.background_color=void 0,f.background_color=void 0,p.background_color=void 0,h.background_color=void 0,m.background_color=void 0);var v=[];return e.showDelopy?(c="grid-cols-quarter-vehicle-deploy","left"==e.columnPosition&&v.push({showInfo:u,className:"col-span-1"}),v.push({showInfo:t,className:"col-span-1 col-start-2"}),v.push({showInfo:d,className:"col-span-1"}),v.push({showInfo:n,className:"col-span-4 col-start-4"}),v.push({showInfo:f,className:"col-span-1"}),v.push({showInfo:r,className:"col-span-6 col-start-9"}),v.push({showInfo:p,className:"col-span-1"}),v.push({showInfo:o,className:"col-span-2 col-start-16"}),v.push({showInfo:h,className:"col-span-1"}),v.push({showInfo:a,className:"col-span-2 col-start-19"}),"right"==e.columnPosition&&v.push({showInfo:m,className:"col-span-1"})):(c="grid-cols-quarter-vehicle-nodeploy",v.push({showInfo:d,className:"col-span-1"}),v.push({showInfo:n,className:"col-span-4 col-start-2"}),v.push({showInfo:f,className:"col-span-1"}),v.push({showInfo:r,className:"col-span-6 col-start-7"}),v.push({showInfo:p,className:"col-span-1"}),v.push({showInfo:o,className:"col-span-2 col-start-14"}),v.push({showInfo:h,className:"col-span-1"}),v.push({showInfo:a,className:"col-span-2 col-start-17"}),"right"==e.columnPosition&&v.push({showInfo:m,className:"col-span-1"})),(0,W.jsxs)(W.Fragment,{children:[e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(c),children:(0,W.jsx)(zt,{block:v,blink_setting:e.lighting_setting})}),!e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(c),children:(0,W.jsx)(zt,{block:v,blink_setting:e.lighting_setting})})]})},qt=function(e){if(I(e)&&e.items){var t=1===e.is_deployment,n=e.column_position,r=Ht(e);return(0,W.jsx)(W.Fragment,{children:I(e)&&(0,W.jsx)("div",{className:"grid ".concat(r),children:e.items.map((function(e,r){return(0,W.jsx)(Vt,l(l({},e),{},{showDelopy:t,columnPosition:n}))}))})})}},Kt=function(e){var t={fontSize:e.fontSize||"3.5rem"};return(0,W.jsx)("div",{className:"flex flex-col items-center bg-white text-black",style:t,children:(0,W.jsx)("div",{className:"leading-none",children:e.title})})},Gt=function(e){var t=A(e.deployment?e.deployment[e.index]:null,"col-span-2"),n=A(e.car_name?e.car_name[e.index]:null,"col-span-4 col-start-5"),r=A(e.arrow?e.arrow[e.index]:null,"col-span-1 col-start-11"),o=A(e.move_car_name?e.move_car_name[e.index]:null,"col-span-4 col-start-14"),a=A(e.car_type?e.car_type[e.index]:null,"col-span-4 col-start-20");return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(Tt,l({},n)),(0,W.jsx)(Tt,l({},r)),(0,W.jsx)(Tt,l({},o)),(0,W.jsx)(Tt,l({},a))]})},Qt=function(e){return(0,W.jsxs)("div",{className:"text-6xl",children:[(0,W.jsx)(Kt,{title:"\u914d\u5099\u72b6\u6cc1"}),I(e)&&(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]",children:e.car_name.map((function(t,n){return(0,W.jsx)(Gt,l(l({},e),{},{index:n}),n)}))})]})},Xt=n(8111),Jt=n.n(Xt),Yt=(Ft=e.useEffect,function(t,n){var r,o,a=(0,e.useRef)(),i=(0,e.useRef)(0);void 0!==n&&(r=n,o=a.current,void 0===r&&(r=[]),void 0===o&&(o=[]),Jt()(r,o))||(a.current=n,i.current+=1),Ft(t,[i.current])});function Zt(e,t){if(e.length<t)for(var n=t-e.length,r=0;r<n;r++)e.push({display_text:"\u3000"});return e}var en=function(t){var n,r,o=null===t||void 0===t?void 0:t.max;(null===t||void 0===t||null===(n=t.change_setting)||void 0===n?void 0:n.change_size)>o&&(t.change_setting.change_size=o);var i,s=null===t||void 0===t?void 0:t.display_data;o&&(s=Zt(s=null===t||void 0===t||null===(i=t.display_data)||void 0===i?void 0:i.slice(0,o),o));var c=a((0,e.useState)({subArray:s,changeSize:0}),2),u=c[0],d=c[1],f=(0,e.useRef)(u);if(Yt((function(){var e,n=null;if(function(e){var t,n;return!!e&&((null===e||void 0===e?void 0:e.display_data)&&(null===e||void 0===e?void 0:e.display_data.length)>(null===e||void 0===e?void 0:e.max)&&(null===e||void 0===e?void 0:e.change_setting)&&(null===e||void 0===e||null===(t=e.change_setting)||void 0===t?void 0:t.change_time)>0&&(null===e||void 0===e||null===(n=e.change_setting)||void 0===n?void 0:n.change_size)>0)}(t))n=setInterval((function(){var e,n,r,a,i,s;f.current.changeSize+o>=(null===t||void 0===t||null===(e=t.display_data)||void 0===e?void 0:e.length)?a=0:a=f.current.changeSize+(null===t||void 0===t||null===(i=t.change_setting)||void 0===i?void 0:i.change_size);a+o>=(null===t||void 0===t||null===(n=t.display_data)||void 0===n?void 0:n.length)&&(a=(null===t||void 0===t||null===(s=t.display_data)||void 0===s?void 0:s.length)-o);var l={changeSize:a,subArray:null===t||void 0===t||null===(r=t.display_data)||void 0===r?void 0:r.slice(a,a+o)};d(l),f.current=l}),1e3*(null===t||void 0===t||null===(e=t.change_setting)||void 0===e?void 0:e.change_time));else if(null!==t&&void 0!==t&&t.display_data){var r,a={changeSize:0,subArray:null===t||void 0===t||null===(r=t.display_data)||void 0===r?void 0:r.slice(0,0+o)};a.subArray=Zt(a.subArray,o),d(a),f.current=a}if(n)return function(){return clearInterval(n)}}),[null===t||void 0===t?void 0:t.change_setting,null===t||void 0===t?void 0:t.display_data]),t)return(0,W.jsx)("div",{className:"grid ".concat(null===t||void 0===t?void 0:t.gridLevelProps),children:null===(r=u.subArray)||void 0===r?void 0:r.map((function(e,n){if(Array.isArray(e))return(0,W.jsx)("span",{children:" \u914d\u5217\u8868\u793a\u3092\u30b5\u30dd\u30fc\u30c8\u3057\u307e\u305b\u3093 "});var r,o=n%(null===t||void 0===t||null===(r=t.cellLevelProps)||void 0===r?void 0:r.length),a={text_color:e.text_color,text:e.display_text,background_color:e.background_color,className:null===t||void 0===t?void 0:t.cellLevelProps[o]};return(0,W.jsx)(Tt,l({},a),n)}))});console.error("ArrayScroll: props is null.")},tn=function(e){var t,n,r,o,a,i,s,c,u,d,f;if(I(e)){var p=e.disaster_class;o={backgroundColor:F(p.background_color)},t=A(p.disaster_type,"col-span-4"),n=A(p.case_no,"col-span-7 col-start-6"),r=A(p.fire_station_name,"col-span-4 col-start-14"),a=A(e.town_name,"col-span-6 w-fit"),i=A(e.target_name,"col-span-9 col-start-8 w-fit"),s=A(e.awareness_time,"col-span-8 col-start-2 w-fit"),c=A(e.command_time,"col-span-8 col-start-12 w-fit"),u=A(e.latest_dynamic_state_time,"col-span-15 col-start-2 text-center"),d=A(e.disaster_dynamic_state,"col-span-4 col-start-6 text-center"),f={backgroundColor:F(e.disaster_dynamic_state.background_color)}}return(0,W.jsx)(W.Fragment,{children:I(e)&&(0,W.jsxs)("div",{className:"leading-[1] text-6xl",children:[(0,W.jsxs)("div",{style:o,className:"border-transparent border-x-[1rem] grid grid-cols-[repeat(4,7rem)_minmax(0.25rem,1fr)_repeat(7,7rem)_minmax(0.25rem,1fr)_repeat(4,7rem)] text-7xl",children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(Tt,l({},n)),(0,W.jsx)(Tt,l({},r))]}),(0,W.jsxs)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-16 text-7xl mt-[3.1rem]",children:[(0,W.jsx)(Tt,l({},a)),(0,W.jsx)(Tt,l({},i))]}),(0,W.jsxs)("div",{className:"grid grid-cols-[minmax(3.5rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(3.5rem,2fr)] text-6xl auto-cols-fr my-[2.9rem]",children:[(0,W.jsx)(Tt,l({},s)),(0,W.jsx)(Tt,l({},c))]}),(0,W.jsx)(en,{max:12,gridLevelProps:"grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr text-6xl leading-[1] gap-y-[1.2rem] gap-x-[1.2rem]",cellLevelProps:["col-span-4 col-start-2","col-span-4","col-span-4","col-span-4"],display_data:e.car_name.display_data,change_setting:e.car_name.change_setting}),!e.transport_hospital&&(0,W.jsx)("div",{className:"grid grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] leading-[1] my-[1.9rem]",children:(0,W.jsx)(Tt,l({},u))}),e.transport_hospital&&(0,W.jsx)("div",{className:"leading-[1.5]",children:(0,W.jsx)(en,{max:1,gridLevelProps:"grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] text-6xl leading-[1] my-[1.9rem]",cellLevelProps:["col-span-15 col-start-2 w-fit"],display_data:e.transport_hospital.display_data,change_setting:e.transport_hospital.change_setting})}),(0,W.jsx)("div",{style:f,className:"grid grid-cols-14 text-8xl auto-cols-fr leading-[1]",children:(0,W.jsx)(Tt,l({},d))})]})})},nn=function(e){var t,n,r,o,a,i,s,c,u,d,f=null===e||void 0===e?void 0:e.item,p=null===f||void 0===f?void 0:f.disaster_class;return c={backgroundColor:F(null===p||void 0===p?void 0:p.background_color)},a=A(null===p||void 0===p?void 0:p.disaster_type,"col-span-4"),i=A(null===p||void 0===p?void 0:p.case_no,"col-span-7 col-start-6"),s=A(null===p||void 0===p?void 0:p.fire_station_name,"col-span-4 col-start-14"),u=A(null===f||void 0===f?void 0:f.awareness_time,"col-span-8 col-start-2 w-fit"),d=A(null===f||void 0===f?void 0:f.town_name,"col-span-10 col-start-10 w-fit"),(0,W.jsxs)("div",{className:"leading-[1]",children:[(0,W.jsxs)("div",{style:c,className:"border-transparent border-x-[1rem] grid grid-cols-19 auto-cols-fr text-6xl",children:[(0,W.jsx)(Tt,l({},a)),(0,W.jsx)(Tt,l({},i)),(0,W.jsx)(Tt,l({},s))]}),(0,W.jsxs)("div",{className:"grid grid-cols-[minmax(6rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(6rem,2fr)] auto-cols-fr my-[3.1rem] text-5xl",children:[(0,W.jsx)(Tt,l({},u)),(0,W.jsx)(Tt,l({},d))]}),(0,W.jsx)(en,{max:8,gridLevelProps:"grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr mb-[3rem] gap-y-[1.2rem] gap-x-[1.2rem] text-6xl",cellLevelProps:["col-span-4 col-start-2","col-span-4","col-span-4","col-span-4"],display_data:null===e||void 0===e||null===(t=e.item)||void 0===t||null===(n=t.car_name)||void 0===n?void 0:n.display_data,change_setting:null===e||void 0===e||null===(r=e.item)||void 0===r||null===(o=r.car_name)||void 0===o?void 0:o.change_setting})]})},rn=function(e){return(0,W.jsx)(W.Fragment,{children:I(e)&&e.case.map((function(e,t){if(!(t>=2))return(0,W.jsx)(nn,{item:e,index:t},t)}))})},on=function(e){var t,n,r,o,a,i,s=e.item,c=s.disaster_class;return o={backgroundColor:F(c.background_color)},t=A(c.disaster_type,"col-span-4"),n=A(c.case_no,"col-span-7 col-start-6"),r=A(c.fire_station_name,"col-span-4 col-start-14"),a=A(s.awareness_time,"col-span-8 col-start-2 w-fit"),i=A(null===s||void 0===s?void 0:s.town_name,"col-span-10 col-start-11 w-fit"),(0,W.jsxs)("div",{className:"text-6xl leading-[1]",children:[(0,W.jsxs)("div",{style:o,className:"border-transparent border-x-[1rem] grid grid-cols-19 leading-none auto-cols-fr",children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(Tt,l({},n)),(0,W.jsx)(Tt,l({},r))]}),(0,W.jsxs)("div",{className:"grid grid-cols-21 text-6xl auto-cols-fr my-[2.35rem]",children:[(0,W.jsx)(Tt,l({},a)),(0,W.jsx)(Tt,l({},i))]})]})},an=function(e){return(0,W.jsx)(W.Fragment,{children:I(e)&&e.case.map((function(e,t){if(!(t>=4))return(0,W.jsx)(on,{item:e,index:t},t)}))})};var sn=function(e){return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Kt,{title:"\u7740\u4fe1\u72b6\u6cc1"}),(0,W.jsx)("div",{className:"mx-[2rem]",children:(0,W.jsx)("div",{className:"grid grid-cols-[repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)] items-center leading-[1]",children:I(e)&&e.line_name.map((function(e,t){if(!(t>=28)){if(!e.display_text)return"";var n,r,o="text-6.5xl";switch(t%4){case 1:n="col-start-6";break;case 2:n="col-start-11";break;case 3:n="col-start-16";break;default:n="col-start-1"}(r=e.display_text,/^[A-Za-z]+$/.test(r))&&(o="text-4.5xl");var a=[{showInfo:e,className:"my-[1.5rem] w-fit"}];return(0,W.jsx)("div",{className:"".concat("col-span-4"," ").concat(n," ").concat(o),children:(0,W.jsx)(zt,{block:a,index:t,blink_setting:e.blink_setting},t)},t)}}))})})]})},ln=function(e){var t,n=A(e,"col-span-4 w-fit");return e.incoming_call?e.incoming_call.length<6&&(t=cn(6-e.incoming_call.length)):t=cn(6),(0,W.jsxs)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-14 grid-rows-1 auto-cols-fr",children:[(0,W.jsx)(Tt,l({},n)),(0,W.jsx)("div",{className:"col-start-9 col-span-6",children:(0,W.jsxs)("div",{className:"flex flex-row",children:[e.incoming_call&&e.incoming_call.map((function(e,t){if(!(t>=6)){var n=l({},e);n.display_text="\u25a0";var r=[{showInfo:n}];return(0,W.jsx)(zt,{block:r,index:t,blink_setting:e.blink_setting},t)}})),t&&t.map((function(e,t){return(0,W.jsx)("div",{className:"text-[#404040]",children:e},t)}))]})})]})};function cn(e){for(var t=[],n=0;n<e;n++)t.push("\u25a0");return t}var un=function(e){return(0,W.jsx)("div",{className:"text-4xl leading-[1]",children:I(e)&&(0,W.jsx)("div",{className:"grid grid-cols-2 grid-rows-16 grid-flow-col gap-[0.1rem] gap-x-[1rem] auto-cols-fr mt-5",children:e.line_name.map((function(e,t){if(!(t>=32))return(0,W.jsx)(ln,l({},e),t)}))})})},dn=function(e){if(!e||!e.display_text)return null;var t="/images/".concat({"\u5317":"North.png","\u5317_\u5317\u6771":"North_Northeast.png","\u5317\u6771":"Northeast.png","\u5317\u6771_\u6771":"Northeast_East.png","\u6771":"East.png","\u6771_\u5357\u6771":"East_Southeast.png","\u5357\u6771":"Southeast.png","\u5357\u6771_\u5357":"Southeast_South.png","\u5357":"South.png","\u5357_\u5357\u897f":"South_Southwest.png","\u5357\u897f":"Southwest.png","\u5357\u897f_\u897f":"Southwest_West.png","\u897f":"West.png","\u897f_\u5317\u897f":"West_Northwest.png","\u5317\u897f":"Northwest.png","\u5317\u897f_\u5317":"Northwest_North.png"}[e.display_text.trim()]);return(0,W.jsx)("img",{className:"min-w-full",src:"/contentapp"+t,alt:e.display_text})},fn=function(e){var t,n={text_color:"#080808",background_color:"#fff",text:e.weather,className:"col-start-1 col-span-4 justify"},r=A(e.detail,"col-span-3 col-start-6 place-self-end w-fit");return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},n)),(0,W.jsx)(Tt,l({},r)),(null===(t=e.detail)||void 0===t?void 0:t.display_text)&&(0,W.jsx)(H,l(l({},{text_color:"#fff",background_color:"#000",className:"col-start-10 col-span-2"}),{},{children:(0,W.jsx)("span",{dangerouslySetInnerHTML:{__html:{"\u5e73\u5747\u98a8\u901f":"m/s","\u98a8\u5411":"","\u6700\u5927\u98a8\u901f":"m/s","\u6c17\u6e29":"&#8451;","\u96e8\u91cf":"mm","\u5b9f\u52b9\u6e7f\u5ea6":"%","\u76f8\u5bfe\u6e7f\u5ea6":"%","\u6c17\u5727":"hPa","\u89b3\u6e2c\u6642\u523b":"","\u5929\u5019":""}[e.weather]}})}))]})},pn=function(e){return e.barTitle=null==e.barTitle?"\u6c17\u8c61\u72b6\u6cc1":e.barTitle,console.log("props.barTitle: ".concat(e.barTitle)),(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Kt,{title:e.barTitle}),I(e)&&(0,W.jsxs)("div",{className:"flex flex-row",children:[(0,W.jsx)("div",{className:"basis-[54.25%] grid grid-cols-1",children:(0,W.jsx)("div",{className:"p-[3.6rem] mt-[.7rem]",children:(0,W.jsx)(dn,l({},e.wind_direction))})}),(0,W.jsx)("div",{className:"basis-[45.75%] ml-[2rem]",children:(0,W.jsxs)("div",{className:"grid grid-cols-[repeat(4,5.1rem)_3.1rem_repeat(3,5.5rem)_3.1rem_repeat(2,5rem)] grid-rows-8 gap-y-[2.3rem] mt-[3.8rem] mb-[3rem] text-5xl leading-[1] mr-[3rem]",children:[(0,W.jsx)(fn,{weather:"\u6700\u5927\u98a8\u901f",detail:e.wind_speed_max}),(0,W.jsx)(fn,{weather:"\u5e73\u5747\u98a8\u901f",detail:e.wind_speed}),(0,W.jsx)(fn,{weather:"\u6c17\u6e29",detail:e.temperature}),(0,W.jsx)(fn,{weather:"\u96e8\u91cf",detail:e.rainfall}),(0,W.jsx)(fn,{weather:"\u5b9f\u52b9\u6e7f\u5ea6",detail:e.effective_humidity}),(0,W.jsx)(fn,{weather:"\u76f8\u5bfe\u6e7f\u5ea6",detail:e.relative_humidity}),(0,W.jsx)(fn,{weather:"\u6c17\u5727",detail:e.atmospheric_pressure}),(0,W.jsx)(fn,{weather:"\u5929\u5019",detail:e.weather})]})})]})]})},hn=function(e){var t,n,r,o,a,i=A(e.agg_info,"text-5xl justify");return null===(t=e.agg_info)||void 0===t||null===(n=t.number_list)||void 0===n||n.forEach((function(e,t){0===t?((r=A(e,"justify-self-end w-fit text-6xl col-start-3")).text=M(r.text),mn(r)):1===t?((o=A(e,"justify-self-end w-fit text-6xl col-start-5")).text=M(o.text),mn(o)):2===t&&((a=A(e,"justify-self-end w-fit text-6xl col-start-7")).text=M(a.text),mn(a))})),r||(r={className:"col-start-3"}),o||(o={className:"col-start-5"}),a||(a={className:"col-start-7"}),(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},i)),(0,W.jsx)(Tt,l({},r)),(0,W.jsx)(Tt,l({},o)),(0,W.jsx)(Tt,l({},a))]})};function mn(e){e.text&&e.text.endsWith("\u4ef6")&&(e.text=e.text.substring(e.text.length-1)),e.unit="\u4ef6",e.unitStyle={fontSize:"3.5rem"}}var vn,gn,yn=function(e){var t,n,r;I(e)&&e.agg_unit&&(t=A(e.agg_unit[0],"text-right col-start-2 justify"),n=A(e.agg_unit[1],"text-right col-start-2 justify"),r=A(e.agg_unit[2],"text-right col-start-2 justify"));return(0,W.jsxs)("div",{children:[(0,W.jsx)(Kt,{title:"119\u7740\u4fe1\u5ea6\u6570"}),I(e)&&(0,W.jsxs)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]",children:[e.agg_unit&&(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("div",{className:"col-start-3 grid grid-cols-[1fr,13.5rem]",children:(0,W.jsx)(Tt,l({},t))}),(0,W.jsx)("div",{className:"col-start-5 grid grid-cols-[1fr,13.5rem]",children:(0,W.jsx)(Tt,l({},n))}),(0,W.jsx)("div",{className:"col-start-7 grid grid-cols-[1fr,13.5rem]",children:(0,W.jsx)(Tt,l({},r))})]}),e.agg_info&&e.agg_info.map((function(e,t){if(!(t>=5))return(0,W.jsx)(hn,{agg_info:e},t)}))]})]})};function bn(t){var n={contentWidth:0,boxWidth:0,duration:t.duration},r=a((0,e.useState)(n),2),o=r[0],i=r[1],s=a((0,e.useState)(!1),2),l=s[0],c=s[1],u=(0,e.useRef)(null);if((0,e.useEffect)((function(){var e=!1,t=u.current,n=t.offsetWidth,r=t.parentElement;i({contentWidth:n,boxWidth:r.offsetWidth,duration:Math.ceil(n/r.offsetWidth*1e4)}),r&&n>r.offsetWidth&&(e=!0),c(e)}),[t.content]),l){var d="marquee_".concat(o.contentWidth);return(0,W.jsx)("div",{className:"marquee_box",children:(0,W.jsx)(_n,{ref:u,animationName:d,duration:o.duration,contentWidth:o.contentWidth,boxWidth:o.boxWidth,color:t.color,background_color:t.background_color,children:t.content})})}return(0,W.jsx)("div",{className:"marquee_box",children:(0,W.jsx)(xn,{ref:u,color:t.color,background_color:t.background_color,children:t.content})})}var _n=_t.p(vn||(vn=V(["\n  color: ",";\n  background-color: ",";\n  position: relative;\n  will-change: transform;\n  animation: "," ","ms\n    linear infinite running both normal;\n\n  @keyframes "," {\n    0%,5% {\n      transform: translateX(0px);\n    }\n    100% {\n      transform: translateX(\n        -","px\n      );\n    }\n  }\n"])),(function(e){return e.color}),(function(e){return e.background_color}),(function(e){return e.animationName}),(function(e){return e.duration}),(function(e){return e.animationName}),(function(e){return e.contentWidth})),xn=_t.p(gn||(gn=V(["\n  position: relative;\n  color: ",";\n  background-color: ",";\n"])),(function(e){return e.color}),(function(e){return e.background_color}));bn.defaultProps={content:"",duration:3};var wn=bn,kn=function(e){if(e.issued_ts[e.index]&&e.combined_forecast_warning&&e.combined_forecast_warning[e.index]){var t=A(e.issued_ts[e.index],"text-4xl my-4"),n=l({},t),r={background_color:t.background_color,className:"".concat(t.className," col-start-5"),text:" "},o=function(e){if(e){var t=c(e.matchAll(/(\d+\u6708\d+\u65e5)(\d+\u6642\d+\u5206)/g));return Array.isArray(t)&&t.length>=1?{date:t[0][1],time:t[0][2]}:void 0}}(t.text);o?(t.text=U(o.date,"3rem"),t.className="col-span-4 ".concat(t.className),n.text=U(o.time,"3rem"),n.className="col-start-6 col-span-4 ".concat(n.className)):(t.text=U(t.text,"3rem"),t.className="col-span-9 ".concat(t.className));var a=A(e.combined_forecast_warning[e.index],"col-span-9 col-start-11 text-7xl"),i={className:a.className,text_color:a.text_color};return(0,W.jsxs)(W.Fragment,{children:[!o&&(0,W.jsx)(Tt,l({},t)),o&&(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(Tt,l({},r)),(0,W.jsx)(Tt,l({},n))]}),(0,W.jsx)(H,l(l({},i),{},{children:(0,W.jsx)("div",{className:"text-8xl",children:(0,W.jsx)(wn,{content:a.text,background_color:a.background_color})})}))]})}},Sn=function(e){var t;return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Kt,{title:"\u8b66\u5831\u30fb\u6ce8\u610f\u5831"}),I(e)&&(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid leading-[1]\r grid-cols-[repeat(4,4.5rem)_1.2rem_repeat(4,4.5rem)_minmax(0.25rem,1fr)_repeat(9,1fr)] items-end gap-y-[4rem] mt-[3rem]",children:null===(t=e.issued_ts)||void 0===t?void 0:t.map((function(t,n){if(!(n>=5))return(0,W.jsx)(kn,l(l({},e),{},{index:n}),n)}))})]})},En=function(e){if(e.official_position_name[e.index]&&e.attendance_dynamic_state_name&&e.attendance_dynamic_state_name[e.index]){var t=A(e.official_position_name[e.index],"col-span-6 w-fit"),n=A(e.attendance_dynamic_state_name[e.index],"col-span-2 col-start-8 w-fit");return(0,W.jsxs)("div",{className:"grid grid-cols-9 grid-rows-1 auto-cols-fr",children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(Tt,l({},n))]})}},Cn=function(e){var t;return(0,W.jsxs)("div",{className:"text-6xl leading-tight",children:[(0,W.jsx)(Kt,{title:"\u51fa\u9000\u72b6\u6cc1"}),I(e)&&(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-2 grid-rows-7 grid-flow-col gap-4 auto-cols-fr leading-[1] gap-y-[2.7rem] gap-x-[7rem] mt-[3rem]",children:null===(t=e.official_position_name)||void 0===t?void 0:t.map((function(t,n){if(!(n>=14))return(0,W.jsx)(En,l(l({},e),{},{index:n}),n)}))})]})},Nn=function(e){var t,n,r=null===e||void 0===e?void 0:e.item;if(r){var o=A(r,"col-span-2");if((null===(t=r.medical_institution_name)||void 0===t?void 0:t.length)>1||(null===(n=r.medical_institution_telephone_no)||void 0===n?void 0:n.length)>1){var a,i,s,c,u="";if(null===(a=r.medical_institution_name)||void 0===a||a.forEach((function(e,t){u=r.medical_institution_telephone_no[t]&&r.medical_institution_telephone_no[t].display_text?"".concat(u," ").concat(e.display_text," ").concat(r.medical_institution_telephone_no[t].display_text):"".concat(u," ").concat(e.display_text)})),(null===(i=r.medical_institution_name)||void 0===i?void 0:i.length)<(null===(s=r.medical_institution_telephone_no)||void 0===s?void 0:s.length))for(var d=(null===(f=r.medical_institution_name)||void 0===f?void 0:f.length)||0;d<(null===(p=r.medical_institution_telephone_no)||void 0===p?void 0:p.length);d++){var f,p,h=r.medical_institution_telephone_no[d];h.display_text&&(u="".concat(u," ").concat(h.display_text))}(c=r.medical_institution_telephone_no&&r.medical_institution_telephone_no[0]?A(r.medical_institution_telephone_no[0],"col-span-13 col-start-4 text-7xl"):A(r.medical_institution_name[0],"col-span-13 col-start-4 text-7xl")).text=u;var m={className:c.className,text_color:c.text_color};return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},o)),(0,W.jsx)(H,l(l({},m),{},{children:(0,W.jsx)(wn,{content:c.text,background_color:c.background_color})}))]})}var v,g,y={className:"col-span-5 col-start-4"},b={className:"col-span-7 col-start-10"};1===(null===(v=r.medical_institution_name)||void 0===v?void 0:v.length)&&(y=A(r.medical_institution_name[0],"col-span-5 col-start-4")),1===(null===(g=r.medical_institution_telephone_no)||void 0===g?void 0:g.length)&&(b=A(r.medical_institution_telephone_no[0],"col-span-7 col-start-10 text-7xl w-fit"));var _={className:y.className,text_color:y.text_color};return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},o)),(0,W.jsx)(H,l(l({},_),{},{children:(0,W.jsx)(wn,{content:y.text,background_color:y.background_color})})),(0,W.jsx)(Tt,l({},b))]})}},jn=function(e){return(0,W.jsxs)("div",{className:"text-8xl",children:[(0,W.jsx)(Kt,{title:"\u5f53\u756a\u75c5\u9662"}),I(e)&&(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-[repeat(2,8rem)_minmax(0.25rem,1fr)_repeat(5,8rem)_minmax(0.25rem,1fr)_repeat(7,7rem)] leading-[1]  gap-y-[2.3rem] mt-[1rem] items-end",children:e.medical_subject.map((function(e,t){if(!(t>=6))return(0,W.jsx)(Nn,{item:e},t)}))})]})},On=function(e){if(e.schedule_ts[e.index]){var t=A(e.schedule_ts[e.index],"col-span-9 text-5xl self-end");t.text=U(t.text,"3rem");var n,r={className:"col-span-10 col-start-11"};return e.schedule_content&&e.schedule_content[e.index]&&(r=A(e.schedule_content[e.index],"col-span-10 col-start-11 text-7xl self-end")),n={className:r.className,text_color:r.text_color},(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Tt,l({},t)),(0,W.jsx)(H,l(l({},n),{},{children:(0,W.jsx)(wn,{content:r.text,background_color:r.background_color})}))]})}},Tn=function(e){var t;return(0,W.jsxs)("div",{className:"text-4xl",children:[(0,W.jsx)(Kt,{title:"\u4e88\u5b9a"}),I(e)&&(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid grid-row-6 grid-cols-[repeat(9,4.7rem)_minmax(0.25rem,1fr)_repeat(10,7.5rem)] leading-[1] gap-y-[3.5rem] mt-[2rem] items-end",children:null===(t=e.schedule_ts)||void 0===t?void 0:t.map((function(t,n){if(!(n>=6))return(0,W.jsx)(On,l(l({},e),{},{index:n}),n)}))})]})},Pn=function(t){var n;return(0,W.jsxs)("div",{className:"text-7xl",children:[(0,W.jsx)(Kt,{title:"\u5f15\u7d99\u4e8b\u9805"}),(0,W.jsx)("div",{className:"border-transparent border-x-[1rem] grid grid-cols-1 leading-[1] gap-y-[3.2rem] mt-[3rem]",children:I(t)&&(null===(n=t.handover_listing)||void 0===n?void 0:n.map((function(t,n){if(!(n>=6)){var r=A(t),o={className:r.className,text_color:r.text_color};return(0,e.createElement)(H,l(l({},o),{},{key:n}),(0,W.jsx)(wn,{content:t.display_text,background_color:r.background_color}))}})))})]})},Rn=function(e){var t,n="";e.index%2===1&&(n="border-l-2"),t=e.index%2==0?"grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)]":"grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)_1rem]";var r=A(e,"col-span-full justify-self-center text-[4.2rem] w-fit");return(0,W.jsxs)("div",{className:"grid content-start ".concat(t," ").concat(n," gap-y-10 pt-2"),children:[(0,W.jsx)(Tt,l({},r)),e.outgoing_call_move_station_name&&e.outgoing_call_move_station_name.map((function(t,n){if(!(n>=2))return(0,W.jsx)(An,l(l({},e),{},{index:n,rightPadding:e.index%2==1}),n)}))]})},An=function(e){var t=e.outgoing_call_move_station_name[e.index],n=l({},t);n.display_text=" ";var r=l({},n),o={},a={};e.incoming_call_move_station_name&&e.incoming_call_move_station_name[e.index]&&((a=l({},o=e.incoming_call_move_station_name[e.index])).display_text=" ");var i={};e.incoming_call_ts&&e.incoming_call_ts[e.index]&&(i=e.incoming_call_ts[e.index]);var s=l({},i);s.display_text=" ";var c=[];return c.push({showInfo:r,className:"col-span-1"}),c.push({showInfo:t,className:"col-span-4 col-start-2"}),c.push({showInfo:n,className:"col-span-1"}),c.push({showInfo:o,className:"col-span-3 col-start-7"}),c.push({showInfo:a,className:"col-span-1"}),c.push({showInfo:i,className:"col-span-5 col-start-11"}),e.rightPadding&&c.push({showInfo:s,className:"col-span-1"}),(0,W.jsx)(zt,{block:c,blink_setting:e.blink_setting?e.blink_setting[e.index]:{}})},In=function(e){return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(Kt,{title:"\u30c7\u30b8\u30bf\u30eb\u7121\u7dda\u901a\u4fe1\u72b6\u6cc1"}),I(e)&&(0,W.jsx)("div",{className:"grid min-h-full grid-rows-2 grid-cols-2 place-content-stretch text-5xl leading-[1]",children:e.wireless_channel_name.map((function(e,t){if(!(t>=4))return(0,W.jsx)(Rn,l(l({},e),{},{index:t}),t)}))})]})};var Ln,Dn,Fn,zn=function(e){if(I(e)){var t=e.title,n=e.dispatch_num,r={backgroundColor:F(null===t||void 0===t?void 0:t.background_color)},o=A(t,"mt-[2.3rem] text-10xl col-start-2 col-span-5  justify-self-center"),a=A(n,"mt-[1.5rem] col-start-2 col-span-4 justify-self-end");return a.text=function(e){if(null==e)return e;return e.endsWith("\u53f0")?e.substring(0,e.length-1):e}(a.text),(0,W.jsx)("div",{style:r,className:"h-full w-full",children:(0,W.jsxs)("div",{className:"grid grid-cols-1 grid-rows-2 place-items-center leading-[1] gap-y-[3rem]",children:[(0,W.jsx)("div",{className:"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(5,10rem)_minmax(0.25rem,2fr)]",children:(0,W.jsx)(Tt,l({},o))}),(0,W.jsx)("div",{className:"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(4,13rem)_minmax(0.25rem,2fr)] text-[23rem]",children:(0,W.jsxs)(H,l(l({},a),{},{children:[a.text,(0,W.jsx)("span",{className:"text-12xl",children:"\u53f0"})]}))})]})})}},Un=function(e){var t;t=e.showDelopy?"grid-cols-extended-vehicle-deploy":"grid-cols-extended-vehicle-nodeploy";var n=A(e,"".concat("col-span-full"," flex flex-col items-center"));return(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("div",{className:"grid ".concat(t),children:(0,W.jsx)(H,l(l({},n),{},{children:e.display_text&&(0,W.jsx)("span",{children:e.display_text})}))}),e.car_name.map((function(t,n){return(0,W.jsx)(Bn,l(l({},e),{},{index:n}),n)}))]})},Bn=function(e){var t={display_text:"",text_color:"",background_color:""},n={display_text:"",text_color:"",background_color:""},r={display_text:"",text_color:"",background_color:""},o={display_text:"",text_color:"",background_color:""},a={display_text:"",text_color:"",background_color:""},i={display_text:"",text_color:"",background_color:""},s={display_text:"",text_color:"",background_color:""},l={display_text:"",text_color:"",background_color:""},c={display_text:"",text_color:"",background_color:""},u={display_text:"",text_color:"",background_color:""},d={display_text:"",text_color:"",background_color:""};e.deployment&&e.deployment[e.index]&&(s=e.deployment[e.index]),e.car_name&&e.car_name[e.index]&&(l=e.car_name[e.index]),e.town_name&&e.town_name[e.index]&&(c=e.town_name[e.index]),e.disaster_type&&e.disaster_type[e.index]&&(u=e.disaster_type[e.index]),e.avm_dynamic_state&&e.avm_dynamic_state[e.index]&&(d=e.avm_dynamic_state[e.index]);var f,p=e.car_name[e.index];p||(p=e.town_name[e.index]),p||(p=e.disaster_type[e.index]),p||(p=e.avm_dynamic_state[e.index]),p||(p=e.deployment[e.index]),s=Dt(s,p),l=Dt(l,p),c=Dt(c,p),u=Dt(u,p),d=Dt(d,p),t=Dt(t,p),n=Dt(n,p),r=Dt(r,p),o=Dt(o,p),a=Dt(a,p),i=Dt(i,p);var h=[];return e.showDelopy?(f="grid-cols-extended-vehicle-deploy",e.startRow+e.index<=25&&h.push({showInfo:t,className:"col-span-1"}),h.push({showInfo:s,className:"col-span-1 col-start-2"}),h.push({showInfo:n,className:"col-span-1"}),h.push({showInfo:l,className:"col-span-4 col-start-4"}),h.push({showInfo:r,className:"col-span-1"}),h.push({showInfo:c,className:"col-span-6 col-start-9"}),h.push({showInfo:o,className:"col-span-1"}),h.push({showInfo:u,className:"col-span-2 col-start-16"}),h.push({showInfo:a,className:"col-span-1"}),h.push({showInfo:d,className:"col-span-2 col-start-19"}),e.startRow+e.index<=25&&h.push({showInfo:i,className:"col-span-1"})):(f="grid-cols-extended-vehicle-nodeploy",e.startRow+e.index<=25&&h.push({showInfo:t,className:"col-span-1"}),h.push({showInfo:l,className:"col-span-4 col-start-2"}),h.push({showInfo:n,className:"col-span-1"}),h.push({showInfo:c,className:"col-span-6 col-start-7"}),h.push({showInfo:r,className:"col-span-1"}),h.push({showInfo:u,className:"col-span-2 col-start-14"}),h.push({showInfo:o,className:"col-span-1"}),h.push({showInfo:d,className:"col-span-2 col-start-17"}),e.startRow+e.index<=25&&h.push({showInfo:a,className:"col-span-1"})),(0,W.jsxs)(W.Fragment,{children:[e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(f),children:(0,W.jsx)(zt,{block:h,blink_setting:e.lighting_setting[e.index]})}),!e.showDelopy&&(0,W.jsx)("div",{className:"grid ".concat(f),children:(0,W.jsx)(zt,{block:h,blink_setting:e.lighting_setting[e.index]})})]})},Mn=function(e){if(I(e)){var t=1===e.is_deployment,n=0,r=[];if(e.title_name){var o,a=b(e.title_name);try{for(a.s();!(o=a.n()).done;){var i=o.value;if(!i.car_name)return;var s=i.car_name.length+1;if(n+s<=50){var c={display_text:i.display_text,text_color:i.text_color,background_color:i.background_color,car_name:i.car_name,town_name:i.town_name,disaster_type:i.disaster_type,avm_dynamic_state:i.avm_dynamic_state,deployment:i.deployment,lighting_setting:i.lighting_setting};r.push(c),n+=s}}}catch(d){a.e(d)}finally{a.f()}var u=1;return(0,W.jsx)(W.Fragment,{children:I(e)&&(0,W.jsx)("div",{className:"grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.4rem]",children:r.map((function(e,n){var r,o=u;return u=u+(null===e||void 0===e||null===(r=e.car_name)||void 0===r?void 0:r.length)+1,(0,W.jsx)(Un,l(l({},e),{},{index:n,showDelopy:t,startRow:o}),n)}))})})}}},Wn=function(e){var t=A(e,"ml-[9rem] mt-[2rem] text-10xl text-[#40ffff]"),n=A(e,"mt-[1.5rem] text-[23rem] text-[#40ffff]");return(0,W.jsx)(W.Fragment,{children:I(e)&&(0,W.jsxs)("div",{className:"grid grid-cols-1 grid-rows-2 place-items-center auto-cols-fr leading-[1] gap-y-[3rem]",children:[(0,W.jsx)("div",{className:"grid auto-cols-max",children:(0,W.jsxs)(H,l(l({},t),{},{children:[e.year,(0,W.jsx)("span",{className:"text-6xl mx-[1rem]",children:"\u5e74"}),Number(e.month)<10&&(0,W.jsx)(W.Fragment,{children:(0,W.jsx)("span",{className:"text-black",children:"0"})}),e.month,(0,W.jsx)("span",{className:"text-6xl mx-[1rem]",children:"\u6708"}),Number(e.day)<10&&(0,W.jsx)(W.Fragment,{children:(0,W.jsx)("span",{className:"text-black",children:"0"})}),e.day,(0,W.jsx)("span",{className:"text-6xl ml-[1rem]",children:"\u65e5"}),(0,W.jsx)("span",{children:"\uff08"}),e.weekday,(0,W.jsx)("span",{children:"\uff09"})]}))}),(0,W.jsxs)(H,l(l({},n),{},{children:[Number(e.hour)<10&&(0,W.jsx)(W.Fragment,{children:(0,W.jsx)("span",{className:"text-black",children:"0"})}),e.hour,(0,W.jsx)("span",{className:"text-12xl mx-[1rem]",children:"\u6642"}),e.minute,(0,W.jsx)("span",{className:"text-12xl ml-[1rem]",children:"\u5206"})]}))]})})},Hn=function(e){return(0,W.jsx)("div",{className:"flex flex-col items-center",children:(0,W.jsx)("div",{className:"m-10 text-6xl text-[#39e2e2]"})})},Vn=function(t){var n=a((0,e.useState)(),2),r=n[0],o=n[1];P(D(t.displayNo,t.splitNo,t.detailSplitNo)+"/setContent",(function(e){console.info("receiveContent: "+e.body);var n=JSON.parse(e.body);if(n.sourceData&&"string"===typeof n.sourceData&&(n.sourceData=JSON.parse(n.sourceData)),n)switch(n.detailSplitNo){case 1:case 3:case 9:case 11:1===t.detailSplitNo&&o((function(e){return l(l({},e),n)}));break;case 4:case 6:case 12:case 14:2===t.detailSplitNo&&o((function(e){return l(l({},e),n)}));break;case 5:case 7:case 13:case 15:3===t.detailSplitNo&&o((function(e){return l(l({},e),n)}));break;case 0:case 2:case 8:case 10:0===t.detailSplitNo&&o((function(e){return l(l({},e),n)}))}}));var i=R();if((0,e.useEffect)((function(){null!==r&&void 0!==r&&r.id&&L(i,r.id,0)}),[null===r||void 0===r?void 0:r.id,i]),r&&null!==r&&void 0!==r&&r.id&&console.log("TaskID: ".concat(null===r||void 0===r?void 0:r.id)),r&&r.sourceNo>=0){var s=r.sourceData;switch(s.barTitle=r.sourceName,s.sourceDispPattern=r.sourceDispPattern,r.sourceNo){case 1:return 1==s.sourceDispPattern?(0,W.jsx)(qt,l({},s)):(0,W.jsx)(Wt,l({},s));case 2:return(0,W.jsx)(Qt,l({},s));case 3:return(0,W.jsx)(tn,l({},s));case 4:return(0,W.jsx)(rn,l({},s));case 5:return(0,W.jsx)(an,l({},s));case 6:return(0,W.jsx)(Wn,l({},s));case 7:return(0,W.jsx)(sn,l({},s));case 8:return(0,W.jsx)(un,l({},s));case 9:return(0,W.jsx)(pn,l({},s));case 10:return(0,W.jsx)(yn,l({},s));case 11:return(0,W.jsx)(Sn,l({},s));case 12:return(0,W.jsx)(Cn,l({},s));case 13:return(0,W.jsx)(jn,l({},s));case 14:return(0,W.jsx)(Tn,l({},s));case 15:return(0,W.jsx)(Pn,l({},s));case 16:return(0,W.jsx)(In,l({},s));case 17:return(0,W.jsx)(zn,l({},s));case 18:return(0,W.jsx)(Mn,l({},s));default:return(0,W.jsx)(Hn,{})}}return(0,W.jsx)(Hn,{})},$n=n(2007),qn=n.n($n),Kn={displayNo:qn().number,splitNo:qn().number},Gn=new Audio;Gn.addEventListener("ended",(function(){console.log("soundPlayEnd: ended")}),!1);var Qn=function(t){var n=a((0,e.useState)(),2),r=n[0],o=n[1];P(D(t.displayNo,t.splitNo)+"/setSound",(function(e){console.info("receiveSound: "+e.body);var t=JSON.parse(e.body);if(t.sourceData&&"string"===typeof t.sourceData&&(t.sourceData=JSON.parse(t.sourceData)),t){Fn=0,Gn.pause(),Gn.currentTime=0,Gn.loop=!1;var n=t.soundNo,r="".concat("/sounds").concat("/sound").concat(n).concat(".mp3");Ln=0,Dn=t.repeatCount,Gn.loop=Dn>0,console.info("receiveSound: Repeat="+Dn+", "+r),Gn.src="/contentapp"+r,Gn.play(),Gn.onerror=function(){console.log("sound play error: "+Gn.error.message),Gn.loop=!1,Fn=-1,o((function(e){return l(l({},e),t)}))},Gn.onplaying=function(){Ln++,console.log("sound playing count("+Ln+")"),Ln<=Dn?console.log("sound repeat"):(Gn.loop=!1,console.log("sound repeat end ")),Fn=0,o((function(e){return l(l({},e),t)}))}}else console.info("receiveSound: command none")}));var i=R();(0,e.useEffect)((function(){null!==r&&void 0!==r&&r.id&&L(i,r.id,Fn)}),[null===r||void 0===r?void 0:r.id,i])};Qn.propTypes=Kn;var Xn,Jn=Qn;function Yn(){return Yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yn.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(Xn||(Xn={}));var Zn=function(e){return e};var er="beforeunload",tr="popstate";function nr(e){e.preventDefault(),e.returnValue=""}function rr(){var e=[];return{get length(){return e.length},push:function(t){return e.push(t),function(){e=e.filter((function(e){return e!==t}))}},call:function(t){e.forEach((function(e){return e&&e(t)}))}}}function or(){return Math.random().toString(36).substr(2,8)}function ar(e){var t=e.pathname,n=void 0===t?"/":t,r=e.search,o=void 0===r?"":r,a=e.hash,i=void 0===a?"":a;return o&&"?"!==o&&(n+="?"===o.charAt(0)?o:"?"+o),i&&"#"!==i&&(n+="#"===i.charAt(0)?i:"#"+i),n}function ir(e){var t={};if(e){var n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));var r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}var sr=(0,e.createContext)(null);var lr=(0,e.createContext)(null);var cr=(0,e.createContext)({outlet:null,matches:[]});function ur(e,t){if(!e)throw new Error(t)}function dr(e,t,n){void 0===n&&(n="/");var r=br(("string"===typeof t?ir(t):t).pathname||"/",n);if(null==r)return null;var o=fr(e);!function(e){e.sort((function(e,t){return e.score!==t.score?t.score-e.score:function(e,t){var n=e.length===t.length&&e.slice(0,-1).every((function(e,n){return e===t[n]}));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((function(e){return e.childrenIndex})),t.routesMeta.map((function(e){return e.childrenIndex})))}))}(o);for(var a=null,i=0;null==a&&i<o.length;++i)a=vr(o[i],r);return a}function fr(e,t,n,r){return void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r=""),e.forEach((function(e,o){var a={relativePath:e.path||"",caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};a.relativePath.startsWith("/")&&(a.relativePath.startsWith(r)||ur(!1),a.relativePath=a.relativePath.slice(r.length));var i=_r([r,a.relativePath]),s=n.concat(a);e.children&&e.children.length>0&&(!0===e.index&&ur(!1),fr(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:mr(i,e.index),routesMeta:s})})),t}var pr=/^:\w+$/,hr=function(e){return"*"===e};function mr(e,t){var n=e.split("/"),r=n.length;return n.some(hr)&&(r+=-2),t&&(r+=2),n.filter((function(e){return!hr(e)})).reduce((function(e,t){return e+(pr.test(t)?3:""===t?1:10)}),r)}function vr(e,t){for(var n=e.routesMeta,r={},o="/",a=[],i=0;i<n.length;++i){var s=n[i],l=i===n.length-1,c="/"===o?t:t.slice(o.length)||"/",u=gr({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},c);if(!u)return null;Object.assign(r,u.params);var d=s.route;a.push({params:r,pathname:_r([o,u.pathname]),pathnameBase:xr(_r([o,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(o=_r([o,u.pathnameBase]))}return a}function gr(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});var n=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);var r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^$?{}|()[\]]/g,"\\$&").replace(/:(\w+)/g,(function(e,t){return r.push(t),"([^\\/]+)"}));e.endsWith("*")?(r.push("*"),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):o+=n?"\\/*$":"(?:(?=[.~-]|%[0-9A-F]{2})|\\b|\\/|$)";return[new RegExp(o,t?void 0:"i"),r]}(e.path,e.caseSensitive,e.end),r=a(n,2),o=r[0],i=r[1],s=t.match(o);if(!s)return null;var l=s[0],c=l.replace(/(.)\/+$/,"$1"),u=s.slice(1),d=i.reduce((function(e,t,n){if("*"===t){var r=u[n]||"";c=l.slice(0,l.length-r.length).replace(/(.)\/+$/,"$1")}return e[t]=function(e,t){try{return decodeURIComponent(e)}catch(n){return e}}(u[n]||""),e}),{});return{params:d,pathname:l,pathnameBase:c,pattern:e}}function yr(e,t,n){var r,o="string"===typeof e?ir(e):e,a=""===e||""===o.pathname?"/":o.pathname;if(null==a)r=n;else{var i=t.length-1;if(a.startsWith("..")){for(var s=a.split("/");".."===s[0];)s.shift(),i-=1;o.pathname=s.join("/")}r=i>=0?t[i]:"/"}var l=function(e,t){void 0===t&&(t="/");var n="string"===typeof e?ir(e):e,r=n.pathname,o=n.search,a=void 0===o?"":o,i=n.hash,s=void 0===i?"":i,l=r?r.startsWith("/")?r:function(e,t){var n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((function(e){".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(r,t):t;return{pathname:l,search:wr(a),hash:kr(s)}}(o,r);return a&&"/"!==a&&a.endsWith("/")&&!l.pathname.endsWith("/")&&(l.pathname+="/"),l}function br(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;var n=e.charAt(t.length);return n&&"/"!==n?null:e.slice(t.length)||"/"}var _r=function(e){return e.join("/").replace(/\/\/+/g,"/")},xr=function(e){return e.replace(/\/+$/,"").replace(/^\/*/,"/")},wr=function(e){return e&&"?"!==e?e.startsWith("?")?e:"?"+e:""},kr=function(e){return e&&"#"!==e?e.startsWith("#")?e:"#"+e:""};function Sr(){return null!=(0,e.useContext)(lr)}function Er(){return Sr()||ur(!1),(0,e.useContext)(lr).location}function Cr(){Sr()||ur(!1);var t=(0,e.useContext)(sr),n=t.basename,r=t.navigator,o=(0,e.useContext)(cr).matches,a=Er().pathname,i=JSON.stringify(o.map((function(e){return e.pathnameBase}))),s=(0,e.useRef)(!1);return(0,e.useEffect)((function(){s.current=!0})),(0,e.useCallback)((function(e,t){if(void 0===t&&(t={}),s.current)if("number"!==typeof e){var o=yr(e,JSON.parse(i),a);"/"!==n&&(o.pathname=_r([n,o.pathname])),(t.replace?r.replace:r.push)(o,t.state)}else r.go(e)}),[n,r,i,a])}function Nr(t,n){return void 0===n&&(n=[]),null==t?null:t.reduceRight((function(r,o,a){return(0,e.createElement)(cr.Provider,{children:void 0!==o.route.element?o.route.element:r,value:{outlet:r,matches:n.concat(t.slice(0,a+1))}})}),null)}function jr(e){ur(!1)}function Or(t){var n=t.basename,r=void 0===n?"/":n,o=t.children,a=void 0===o?null:o,i=t.location,s=t.navigationType,l=void 0===s?Xn.Pop:s,c=t.navigator,u=t.static,d=void 0!==u&&u;Sr()&&ur(!1);var f=xr(r),p=(0,e.useMemo)((function(){return{basename:f,navigator:c,static:d}}),[f,c,d]);"string"===typeof i&&(i=ir(i));var h=i,m=h.pathname,v=void 0===m?"/":m,g=h.search,y=void 0===g?"":g,b=h.hash,_=void 0===b?"":b,x=h.state,w=void 0===x?null:x,k=h.key,S=void 0===k?"default":k,E=(0,e.useMemo)((function(){var e=br(v,f);return null==e?null:{pathname:e,search:y,hash:_,state:w,key:S}}),[f,v,y,_,w,S]);return null==E?null:(0,e.createElement)(sr.Provider,{value:p},(0,e.createElement)(lr.Provider,{children:a,value:{location:E,navigationType:l}}))}function Tr(t){var n=t.children,r=t.location;return function(t,n){Sr()||ur(!1);var r,o=(0,e.useContext)(cr).matches,a=o[o.length-1],i=a?a.params:{},s=(a&&a.pathname,a?a.pathnameBase:"/"),l=(a&&a.route,Er());if(n){var c,u="string"===typeof n?ir(n):n;"/"===s||(null==(c=u.pathname)?void 0:c.startsWith(s))||ur(!1),r=u}else r=l;var d=r.pathname||"/",f=dr(t,{pathname:"/"===s?d:d.slice(s.length)||"/"});return Nr(f&&f.map((function(e){return Object.assign({},e,{params:Object.assign({},i,e.params),pathname:_r([s,e.pathname]),pathnameBase:"/"===e.pathnameBase?s:_r([s,e.pathnameBase])})})),o)}(Pr(n),r)}function Pr(t){var n=[];return e.Children.forEach(t,(function(t){if((0,e.isValidElement)(t))if(t.type!==e.Fragment){t.type!==jr&&ur(!1);var r={caseSensitive:t.props.caseSensitive,element:t.props.element,index:t.props.index,path:t.props.path};t.props.children&&(r.children=Pr(t.props.children)),n.push(r)}else n.push.apply(n,Pr(t.props.children))})),n}function Rr(t){var n=t.basename,r=t.children,o=t.window,i=(0,e.useRef)();null==i.current&&(i.current=function(e){void 0===e&&(e={});var t=e.window,n=void 0===t?document.defaultView:t,r=n.history;function o(){var e=n.location,t=e.pathname,o=e.search,a=e.hash,i=r.state||{};return[i.idx,Zn({pathname:t,search:o,hash:a,state:i.usr||null,key:i.key||"default"})]}var a=null;n.addEventListener(tr,(function(){if(a)d.call(a),a=null;else{var e=Xn.Pop,t=o(),n=t[0],r=t[1];if(d.length){if(null!=n){var i=l-n;i&&(a={action:e,location:r,retry:function(){g(-1*i)}},g(i))}}else v(e)}}));var i=Xn.Pop,s=o(),l=s[0],c=s[1],u=rr(),d=rr();function f(e){return"string"===typeof e?e:ar(e)}function p(e,t){return void 0===t&&(t=null),Zn(Yn({pathname:c.pathname,hash:"",search:""},"string"===typeof e?ir(e):e,{state:t,key:or()}))}function h(e,t){return[{usr:e.state,key:e.key,idx:t},f(e)]}function m(e,t,n){return!d.length||(d.call({action:e,location:t,retry:n}),!1)}function v(e){i=e;var t=o();l=t[0],c=t[1],u.call({action:i,location:c})}function g(e){r.go(e)}null==l&&(l=0,r.replaceState(Yn({},r.state,{idx:l}),""));var y={get action(){return i},get location(){return c},createHref:f,push:function e(t,o){var a=Xn.Push,i=p(t,o);if(m(a,i,(function(){e(t,o)}))){var s=h(i,l+1),c=s[0],u=s[1];try{r.pushState(c,"",u)}catch(d){n.location.assign(u)}v(a)}},replace:function e(t,n){var o=Xn.Replace,a=p(t,n);if(m(o,a,(function(){e(t,n)}))){var i=h(a,l),s=i[0],c=i[1];r.replaceState(s,"",c),v(o)}},go:g,back:function(){g(-1)},forward:function(){g(1)},listen:function(e){return u.push(e)},block:function(e){var t=d.push(e);return 1===d.length&&n.addEventListener(er,nr),function(){t(),d.length||n.removeEventListener(er,nr)}}};return y}({window:o}));var s=i.current,l=a((0,e.useState)({action:s.action,location:s.location}),2),c=l[0],u=l[1];return(0,e.useLayoutEffect)((function(){return s.listen(u)}),[s]),(0,e.createElement)(Or,{basename:n,children:r,location:c.location,navigationType:c.action,navigator:s})}function Ar(e){return void 0===e&&(e=""),new URLSearchParams("string"===typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((function(t,n){var r=e[n];return t.concat(Array.isArray(r)?r.map((function(e){return[n,e]})):[[n,r]])}),[]))}var Ir=function(e){var t=e.title||"\u30a8\u30e9\u30fc\u767a\u751f";return(0,W.jsx)(W.Fragment,{children:(0,W.jsxs)("div",{role:"alert",children:[(0,W.jsx)("div",{className:"bg-red-500 text-white font-bold rounded-t px-4 py-2 text-2xl",children:t}),(0,W.jsx)("div",{className:"border border-t-0 border-red-400 rounded-b bg-red-100 px-4 py-3 text-red-700 text-4xl",children:(0,W.jsx)("p",{children:e.msg})})]})})};function Lr(e){var t=e||16;document.getElementsByTagName("html")[0].style["font-size"]=t+"px"}var Dr=function(){var t=a((0,e.useState)({rowNum:1,colNum:1}),2),n=t[0],r=t[1],o=a(function(t){var n=(0,e.useRef)(Ar(t)),r=Er(),o=(0,e.useMemo)((function(){var e,t=Ar(r.search),o=b(n.current.keys());try{var a=function(){var r=e.value;t.has(r)||n.current.getAll(r).forEach((function(e){t.append(r,e)}))};for(o.s();!(e=o.n()).done;)a()}catch(i){o.e(i)}finally{o.f()}return t}),[r.search]),a=Cr();return[o,(0,e.useCallback)((function(e,t){a("?"+Ar(e),t)}),[a])]}(),2),i=o[0],s=(o[1],i.get("display_split_no")),l=i.get("display_no");(0,e.useEffect)((function(){document.title="\u8868\u793a\u76e4\u756a\u53f7\uff1a"+l+", \u9762\u756a\u53f7\uff1a"+s}),[l,s]);var c={displayNo:l,splitNo:s};P(D(l,s)+"/setControl",(function(e){console.info("receiveControl: "+e.body);var t=JSON.parse(e.body);t&&(1===t.rowNum&&1===t.colNum?Lr(16):Lr(8),r(t))}),c);var u=R();if((0,e.useEffect)((function(){null!==n&&void 0!==n&&n.id&&L(u,n.id,0)}),[null===n||void 0===n?void 0:n.id,u]),null==s||null==l)return(0,W.jsx)(Ir,{msg:"\u8868\u793a\u76e4\u756a\u53f7\u3001\u53ca\u3073\u9762\u756a\u53f7\u3092\u8a2d\u5b9a\u3057\u3066\u304f\u3060\u3055\u3044\u3002\u4f8b\uff1ahttp://operationcenter/contentapp/?display_no=0&display_split_no=0"});s=Number(s),l=Number(l);var d="absolute h-[1px] w-[3px] bg-white";return(0,W.jsxs)("div",{className:"static",children:[(0,W.jsx)("div",{className:"".concat(d," left-0 top-0")}),(0,W.jsx)("div",{className:"".concat(d," top-0 right-0")}),(0,W.jsx)("div",{className:"".concat(d," bottom-0 left-0")}),(0,W.jsx)("div",{className:"".concat(d," bottom-0 right-0")}),2===n.rowNum&&2===n.colNum&&(0,W.jsxs)("div",{className:"bg-black grid min-h-screen grid-rows-2 grid-cols-2 place-content-stretch select-none overflow-hidden",children:[(0,W.jsx)("div",{className:"flex flex-col text-white w-full h-full border-r-[1px] border-b-[1px] border-white p-[1px]",children:(0,W.jsx)(Vn,{displayNo:l,splitNo:s,detailSplitNo:0})}),(0,W.jsx)("div",{className:"flex flex-col text-white w-full h-full border-l-[1px] border-b-[1px] border-white p-[1px]",children:(0,W.jsx)(Vn,{displayNo:l,splitNo:s,detailSplitNo:1})}),(0,W.jsx)("div",{className:"flex flex-col text-white w-full h-full border-r-[1px] border-t-[1px] border-white p-[1px]",children:(0,W.jsx)(Vn,{displayNo:l,splitNo:s,detailSplitNo:2})}),(0,W.jsx)("div",{className:"flex flex-col text-white w-full h-full border-l-[1px] border-t-[1px] border-white p-[1px]",children:(0,W.jsx)(Vn,{displayNo:l,splitNo:s,detailSplitNo:3})})]}),1===n.rowNum&&1===n.colNum&&(0,W.jsx)("div",{className:"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-stretch select-none overflow-hidden",children:(0,W.jsx)("div",{className:"flex flex-col text-white w-full h-full p-[1px]",children:(0,W.jsx)(Vn,{displayNo:l,splitNo:s,detailSplitNo:0})})}),(0,W.jsx)("div",{className:"bg-black",children:(0,W.jsx)("div",{className:"bg-black text-white",children:(0,W.jsx)(Jn,{displayNo:l,splitNo:s})})})]})};var Fr=function(){var e="ServerUrl".concat("/contentapp","?display_no=xx&display_split_no=xx \u306e\u3088\u3046\u306a\u30a2\u30c9\u30ec\u30b9\u3067\u3001\u8868\u793a\u76e4ID\u3068\u30e2\u30cb\u30bf\u30fcID\u3092\u63d0\u4f9b\u3057\u3066\u3001\u30a2\u30af\u30bb\u30b9\u3057\u3066\u304f\u3060\u3055\u3044\u3002");return(0,W.jsx)("div",{className:"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center",children:(0,W.jsxs)("div",{className:"flex flex-col items-center text-green-400",children:[(0,W.jsx)("div",{className:"m-10 text-4xl",children:"\u5b58\u5728\u3057\u306a\u3044\u30da\u30fc\u30b8\u3067\u3059\u3002"}),(0,W.jsx)("div",{className:"m-10 text-2xl",children:e})]})})},zr=function(){return(0,W.jsx)("div",{className:"App",children:(0,W.jsx)("header",{className:"App-header",children:(0,W.jsxs)(Tr,{children:[(0,W.jsx)(jr,{index:!0,element:(0,W.jsx)(Dr,{})}),(0,W.jsx)(jr,{path:"/",element:(0,W.jsx)(Dr,{})}),(0,W.jsx)(jr,{path:"*",element:(0,W.jsx)(Fr,{})})]})})})},Ur=function(e){e&&e instanceof Function&&n.e(787).then(n.bind(n,787)).then((function(t){var n=t.getCLS,r=t.getFID,o=t.getFCP,a=t.getLCP,i=t.getTTFB;n(e),r(e),o(e),a(e),i(e)}))},Br=n(7470);function Mr(e,t){return function(){return e.apply(t,arguments)}}var Wr,Hr=Object.prototype.toString,Vr=Object.getPrototypeOf,$r=(Wr=Object.create(null),function(e){var t=Hr.call(e);return Wr[t]||(Wr[t]=t.slice(8,-1).toLowerCase())}),qr=function(e){return e=e.toLowerCase(),function(t){return $r(t)===e}},Kr=function(e){return function(t){return typeof t===e}},Gr=Array.isArray,Qr=Kr("undefined");var Xr=qr("ArrayBuffer");var Jr=Kr("string"),Yr=Kr("function"),Zr=Kr("number"),eo=function(e){return null!==e&&"object"===typeof e},to=function(e){if("object"!==$r(e))return!1;var t=Vr(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},no=qr("Date"),ro=qr("File"),oo=qr("Blob"),ao=qr("FileList"),io=qr("URLSearchParams");function so(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=o.allOwnKeys,i=void 0!==a&&a;if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Gr(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{var s,l=i?Object.getOwnPropertyNames(e):Object.keys(e),c=l.length;for(n=0;n<c;n++)s=l[n],t.call(null,e[s],s,e)}}var lo,co=(lo="undefined"!==typeof Uint8Array&&Vr(Uint8Array),function(e){return lo&&e instanceof lo}),uo=qr("HTMLFormElement"),fo=function(e){var t=Object.prototype.hasOwnProperty;return function(e,n){return t.call(e,n)}}(),po=qr("RegExp"),ho=function(e,t){var n=Object.getOwnPropertyDescriptors(e),r={};so(n,(function(n,o){!1!==t(n,o,e)&&(r[o]=n)})),Object.defineProperties(e,r)},mo={isArray:Gr,isArrayBuffer:Xr,isBuffer:function(e){return null!==e&&!Qr(e)&&null!==e.constructor&&!Qr(e.constructor)&&Yr(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:function(e){var t="[object FormData]";return e&&("function"===typeof FormData&&e instanceof FormData||Hr.call(e)===t||Yr(e.toString)&&e.toString()===t)},isArrayBufferView:function(e){return"undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Xr(e.buffer)},isString:Jr,isNumber:Zr,isBoolean:function(e){return!0===e||!1===e},isObject:eo,isPlainObject:to,isUndefined:Qr,isDate:no,isFile:ro,isBlob:oo,isRegExp:po,isFunction:Yr,isStream:function(e){return eo(e)&&Yr(e.pipe)},isURLSearchParams:io,isTypedArray:co,isFileList:ao,forEach:so,merge:function e(){for(var t={},n=function(n,r){to(t[r])&&to(n)?t[r]=e(t[r],n):to(n)?t[r]=e({},n):Gr(n)?t[r]=n.slice():t[r]=n},r=0,o=arguments.length;r<o;r++)arguments[r]&&so(arguments[r],n);return t},extend:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=r.allOwnKeys;return so(t,(function(t,r){n&&Yr(t)?e[r]=Mr(t,n):e[r]=t}),{allOwnKeys:o}),e},trim:function(e){return e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e},inherits:function(e,t,n,r){e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:function(e,t,n,r){var o,a,i,s={};if(t=t||{},null==e)return t;do{for(a=(o=Object.getOwnPropertyNames(e)).length;a-- >0;)i=o[a],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&Vr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:$r,kindOfTest:qr,endsWith:function(e,t,n){e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;var r=e.indexOf(t,n);return-1!==r&&r===n},toArray:function(e){if(!e)return null;if(Gr(e))return e;var t=e.length;if(!Zr(t))return null;for(var n=new Array(t);t-- >0;)n[t]=e[t];return n},forEachEntry:function(e,t){for(var n,r=(e&&e[Symbol.iterator]).call(e);(n=r.next())&&!n.done;){var o=n.value;t.call(e,o[0],o[1])}},matchAll:function(e,t){for(var n,r=[];null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:uo,hasOwnProperty:fo,hasOwnProp:fo,reduceDescriptors:ho,freezeMethods:function(e){ho(e,(function(t,n){var r=e[n];Yr(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=function(){throw Error("Can not read-only method '"+n+"'")}))}))},toObjectSet:function(e,t){var n={},r=function(e){e.forEach((function(e){n[e]=!0}))};return Gr(e)?r(e):r(String(e).split(t)),n},toCamelCase:function(e){return e.toLowerCase().replace(/[_-\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n}))},noop:function(){},toFiniteNumber:function(e,t){return e=+e,Number.isFinite(e)?e:t}};function vo(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}mo.inherits(vo,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var go=vo.prototype,yo={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((function(e){yo[e]={value:e}})),Object.defineProperties(vo,yo),Object.defineProperty(go,"isAxiosError",{value:!0}),vo.from=function(e,t,n,r,o,a){var i=Object.create(go);return mo.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(function(e){return"isAxiosError"!==e})),vo.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};var bo=vo,_o=n(7472);function xo(e){return mo.isPlainObject(e)||mo.isArray(e)}function wo(e){return mo.endsWith(e,"[]")?e.slice(0,-2):e}function ko(e,t,n){return e?e.concat(t).map((function(e,t){return e=wo(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}var So=mo.toFlatObject(mo,{},null,(function(e){return/^is[A-Z]/.test(e)}));var Eo=function(e,t,n){if(!mo.isObject(e))throw new TypeError("target must be an object");t=t||new(_o||FormData);var r,o=(n=mo.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!mo.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,i=n.dots,s=n.indexes,l=(n.Blob||"undefined"!==typeof Blob&&Blob)&&((r=t)&&mo.isFunction(r.append)&&"FormData"===r[Symbol.toStringTag]&&r[Symbol.iterator]);if(!mo.isFunction(a))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(mo.isDate(e))return e.toISOString();if(!l&&mo.isBlob(e))throw new bo("Blob is not supported. Use a Buffer instead.");return mo.isArrayBuffer(e)||mo.isTypedArray(e)?l&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,r){var a=e;if(e&&!r&&"object"===typeof e)if(mo.endsWith(n,"{}"))n=o?n:n.slice(0,-2),e=JSON.stringify(e);else if(mo.isArray(e)&&function(e){return mo.isArray(e)&&!e.some(xo)}(e)||mo.isFileList(e)||mo.endsWith(n,"[]")&&(a=mo.toArray(e)))return n=wo(n),a.forEach((function(e,r){!mo.isUndefined(e)&&null!==e&&t.append(!0===s?ko([n],r,i):null===s?n:n+"[]",c(e))})),!1;return!!xo(e)||(t.append(ko(r,n,i),c(e)),!1)}var d=[],f=Object.assign(So,{defaultVisitor:u,convertValue:c,isVisitable:xo});if(!mo.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!mo.isUndefined(n)){if(-1!==d.indexOf(n))throw Error("Circular reference detected in "+r.join("."));d.push(n),mo.forEach(n,(function(n,o){!0===(!(mo.isUndefined(n)||null===n)&&a.call(t,n,mo.isString(o)?o.trim():o,r,f))&&e(n,r?r.concat(o):[o])})),d.pop()}}(e),t};function Co(e){var t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function No(e,t){this._pairs=[],e&&Eo(e,this,t)}var jo=No.prototype;jo.append=function(e,t){this._pairs.push([e,t])},jo.toString=function(e){var t=e?function(t){return e.call(this,t,Co)}:Co;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};var Oo=No;function To(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Po(e,t,n){if(!t)return e;var r,o=n&&n.encode||To,a=n&&n.serialize;if(r=a?a(t,n):mo.isURLSearchParams(t)?t.toString():new Oo(t,n).toString(o)){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e}var Ro=function(){function e(){h(this,e),this.handlers=[]}return v(e,[{key:"use",value:function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}},{key:"eject",value:function(e){this.handlers[e]&&(this.handlers[e]=null)}},{key:"clear",value:function(){this.handlers&&(this.handlers=[])}},{key:"forEach",value:function(e){mo.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}]),e}(),Ao={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Io="undefined"!==typeof URLSearchParams?URLSearchParams:Oo,Lo=FormData,Do=function(){var e;return("undefined"===typeof navigator||"ReactNative"!==(e=navigator.product)&&"NativeScript"!==e&&"NS"!==e)&&("undefined"!==typeof window&&"undefined"!==typeof document)}(),Fo={isBrowser:!0,classes:{URLSearchParams:Io,FormData:Lo,Blob:Blob},isStandardBrowserEnv:Do,protocols:["http","https","file","blob","url","data"]};var zo=function(e){function t(e,n,r,o){var a=e[o++],i=Number.isFinite(+a),s=o>=e.length;return a=!a&&mo.isArray(r)?r.length:a,s?(mo.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!i):(r[a]&&mo.isObject(r[a])||(r[a]=[]),t(e,n,r[a],o)&&mo.isArray(r[a])&&(r[a]=function(e){var t,n,r={},o=Object.keys(e),a=o.length;for(t=0;t<a;t++)r[n=o[t]]=e[n];return r}(r[a])),!i)}if(mo.isFormData(e)&&mo.isFunction(e.entries)){var n={};return mo.forEachEntry(e,(function(e,r){t(function(e){return mo.matchAll(/\w+|\[(\w*)]/g,e).map((function(e){return"[]"===e[0]?"":e[1]||e[0]}))}(e),r,n,0)})),n}return null};var Uo=Fo.isStandardBrowserEnv?{write:function(e,t,n,r,o,a){var i=[];i.push(e+"="+encodeURIComponent(t)),mo.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),mo.isString(r)&&i.push("path="+r),mo.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}};function Bo(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}var Mo=Fo.isStandardBrowserEnv?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=mo.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0};function Wo(e,t,n){bo.call(this,null==e?"canceled":e,bo.ERR_CANCELED,t,n),this.name="CanceledError"}mo.inherits(Wo,bo,{__CANCEL__:!0});var Ho=Wo;var Vo=mo.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),$o=Symbol("internals"),qo=Symbol("defaults");function Ko(e){return e&&String(e).trim().toLowerCase()}function Go(e){return!1===e||null==e?e:mo.isArray(e)?e.map(Go):String(e)}function Qo(e,t,n,r){return mo.isFunction(r)?r.call(this,t,n):mo.isString(t)?mo.isString(r)?-1!==t.indexOf(r):mo.isRegExp(r)?r.test(t):void 0:void 0}function Xo(e,t){t=t.toLowerCase();for(var n,r=Object.keys(e),o=r.length;o-- >0;)if(t===(n=r[o]).toLowerCase())return n;return null}function Jo(e,t){e&&this.set(e),this[qo]=t||null}Object.assign(Jo.prototype,{set:function(e,t,n){var r=this;function o(e,t,n){var o=Ko(t);if(!o)throw new Error("header name must be a non-empty string");var a=Xo(r,o);(!a||!0===n||!1!==r[a]&&!1!==n)&&(r[a||t]=Go(e))}return mo.isPlainObject(e)?mo.forEach(e,(function(e,n){o(e,n,t)})):o(t,e,n),this},get:function(e,t){if(e=Ko(e)){var n=Xo(this,e);if(n){var r=this[n];if(!t)return r;if(!0===t)return function(e){for(var t,n=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;t=r.exec(e);)n[t[1]]=t[2];return n}(r);if(mo.isFunction(t))return t.call(this,r,n);if(mo.isRegExp(t))return t.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}},has:function(e,t){if(e=Ko(e)){var n=Xo(this,e);return!(!n||t&&!Qo(0,this[n],n,t))}return!1},delete:function(e,t){var n=this,r=!1;function o(e){if(e=Ko(e)){var o=Xo(n,e);!o||t&&!Qo(0,n[o],o,t)||(delete n[o],r=!0)}}return mo.isArray(e)?e.forEach(o):o(e),r},clear:function(){return Object.keys(this).forEach(this.delete.bind(this))},normalize:function(e){var t=this,n={};return mo.forEach(this,(function(r,o){var a=Xo(n,o);if(a)return t[a]=Go(r),void delete t[o];var i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n}))}(o):String(o).trim();i!==o&&delete t[o],t[i]=Go(r),n[i]=!0})),this},toJSON:function(e){var t=Object.create(null);return mo.forEach(Object.assign({},this[qo]||null,this),(function(n,r){null!=n&&!1!==n&&(t[r]=e&&mo.isArray(n)?n.join(", "):n)})),t}}),Object.assign(Jo,{from:function(e){return mo.isString(e)?new this(function(e){var t,n,r,o={};return e&&e.split("\n").forEach((function(e){r=e.indexOf(":"),t=e.substring(0,r).trim().toLowerCase(),n=e.substring(r+1).trim(),!t||o[t]&&Vo[t]||("set-cookie"===t?o[t]?o[t].push(n):o[t]=[n]:o[t]=o[t]?o[t]+", "+n:n)})),o}(e)):e instanceof this?e:new this(e)},accessor:function(e){var t=(this[$o]=this[$o]={accessors:{}}).accessors,n=this.prototype;function r(e){var r=Ko(e);t[r]||(!function(e,t){var n=mo.toCamelCase(" "+t);["get","set","has"].forEach((function(r){Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return mo.isArray(e)?e.forEach(r):r(e),this}}),Jo.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent"]),mo.freezeMethods(Jo.prototype),mo.freezeMethods(Jo);var Yo=Jo;var Zo=function(e,t){e=e||10;var n,r=new Array(e),o=new Array(e),a=0,i=0;return t=void 0!==t?t:1e3,function(s){var l=Date.now(),c=o[i];n||(n=l),r[a]=s,o[a]=l;for(var u=i,d=0;u!==a;)d+=r[u++],u%=e;if((a=(a+1)%e)===i&&(i=(i+1)%e),!(l-n<t)){var f=c&&l-c;return f?Math.round(1e3*d/f):void 0}}};function ea(e,t){var n=0,r=Zo(50,250);return function(o){var a=o.loaded,i=o.lengthComputable?o.total:void 0,s=a-n,l=r(s);n=a;var c={loaded:a,total:i,progress:i?a/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&a<=i?(i-a)/l:void 0};c[t?"download":"upload"]=!0,e(c)}}function ta(e){return new Promise((function(t,n){var r,o=e.data,a=Yo.from(e.headers).normalize(),i=e.responseType;function s(){e.cancelToken&&e.cancelToken.unsubscribe(r),e.signal&&e.signal.removeEventListener("abort",r)}mo.isFormData(o)&&Fo.isStandardBrowserEnv&&a.setContentType(!1);var l=new XMLHttpRequest;if(e.auth){var c=e.auth.username||"",u=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";a.set("Authorization","Basic "+btoa(c+":"+u))}var d=Bo(e.baseURL,e.url);function f(){if(l){var r=Yo.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders());!function(e,t,n){var r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new bo("Request failed with status code "+n.status,[bo.ERR_BAD_REQUEST,bo.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),s()}),(function(e){n(e),s()}),{data:i&&"text"!==i&&"json"!==i?l.response:l.responseText,status:l.status,statusText:l.statusText,headers:r,config:e,request:l}),l=null}}if(l.open(e.method.toUpperCase(),Po(d,e.params,e.paramsSerializer),!0),l.timeout=e.timeout,"onloadend"in l?l.onloadend=f:l.onreadystatechange=function(){l&&4===l.readyState&&(0!==l.status||l.responseURL&&0===l.responseURL.indexOf("file:"))&&setTimeout(f)},l.onabort=function(){l&&(n(new bo("Request aborted",bo.ECONNABORTED,e,l)),l=null)},l.onerror=function(){n(new bo("Network Error",bo.ERR_NETWORK,e,l)),l=null},l.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",r=e.transitional||Ao;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new bo(t,r.clarifyTimeoutError?bo.ETIMEDOUT:bo.ECONNABORTED,e,l)),l=null},Fo.isStandardBrowserEnv){var p=(e.withCredentials||Mo(d))&&e.xsrfCookieName&&Uo.read(e.xsrfCookieName);p&&a.set(e.xsrfHeaderName,p)}void 0===o&&a.setContentType(null),"setRequestHeader"in l&&mo.forEach(a.toJSON(),(function(e,t){l.setRequestHeader(t,e)})),mo.isUndefined(e.withCredentials)||(l.withCredentials=!!e.withCredentials),i&&"json"!==i&&(l.responseType=e.responseType),"function"===typeof e.onDownloadProgress&&l.addEventListener("progress",ea(e.onDownloadProgress,!0)),"function"===typeof e.onUploadProgress&&l.upload&&l.upload.addEventListener("progress",ea(e.onUploadProgress)),(e.cancelToken||e.signal)&&(r=function(t){l&&(n(!t||t.type?new Ho(null,e,l):t),l.abort(),l=null)},e.cancelToken&&e.cancelToken.subscribe(r),e.signal&&(e.signal.aborted?r():e.signal.addEventListener("abort",r)));var h=function(e){var t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(d);h&&-1===Fo.protocols.indexOf(h)?n(new bo("Unsupported protocol "+h+":",bo.ERR_BAD_REQUEST,e)):l.send(o||null)}))}var na={http:ta,xhr:ta},ra=function(e){if(mo.isString(e)){var t=na[e];if(!e)throw Error(mo.hasOwnProp(e)?"Adapter '".concat(e,"' is not available in the build"):"Can not resolve adapter '".concat(e,"'"));return t}if(!mo.isFunction(e))throw new TypeError("adapter is not a function");return e},oa={"Content-Type":"application/x-www-form-urlencoded"};var aa={transitional:Ao,adapter:function(){var e;return"undefined"!==typeof XMLHttpRequest?e=ra("xhr"):"undefined"!==typeof process&&"process"===mo.kindOf(process)&&(e=ra("http")),e}(),transformRequest:[function(e,t){var n,r=t.getContentType()||"",o=r.indexOf("application/json")>-1,a=mo.isObject(e);if(a&&mo.isHTMLForm(e)&&(e=new FormData(e)),mo.isFormData(e))return o&&o?JSON.stringify(zo(e)):e;if(mo.isArrayBuffer(e)||mo.isBuffer(e)||mo.isStream(e)||mo.isFile(e)||mo.isBlob(e))return e;if(mo.isArrayBufferView(e))return e.buffer;if(mo.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(a){if(r.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Eo(e,new Fo.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Fo.isNode&&mo.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((n=mo.isFileList(e))||r.indexOf("multipart/form-data")>-1){var i=this.env&&this.env.FormData;return Eo(n?{"files[]":e}:e,i&&new i,this.formSerializer)}}return a||o?(t.setContentType("application/json",!1),function(e,t,n){if(mo.isString(e))try{return(t||JSON.parse)(e),mo.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||aa.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(e&&mo.isString(e)&&(n&&!this.responseType||r)){var o=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(a){if(o){if("SyntaxError"===a.name)throw bo.from(a,bo.ERR_BAD_RESPONSE,this,null,this.response);throw a}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Fo.classes.FormData,Blob:Fo.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};mo.forEach(["delete","get","head"],(function(e){aa.headers[e]={}})),mo.forEach(["post","put","patch"],(function(e){aa.headers[e]=mo.merge(oa)}));var ia=aa;function sa(e,t){var n=this||ia,r=t||n,o=Yo.from(r.headers),a=r.data;return mo.forEach(e,(function(e){a=e.call(n,a,o.normalize(),t?t.status:void 0)})),o.normalize(),a}function la(e){return!(!e||!e.__CANCEL__)}function ca(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ho}function ua(e){return ca(e),e.headers=Yo.from(e.headers),e.data=sa.call(e,e.transformRequest),(e.adapter||ia.adapter)(e).then((function(t){return ca(e),t.data=sa.call(e,e.transformResponse,t),t.headers=Yo.from(t.headers),t}),(function(t){return la(t)||(ca(e),t&&t.response&&(t.response.data=sa.call(e,e.transformResponse,t.response),t.response.headers=Yo.from(t.response.headers))),Promise.reject(t)}))}function da(e,t){t=t||{};var n={};function r(e,t){return mo.isPlainObject(e)&&mo.isPlainObject(t)?mo.merge(e,t):mo.isPlainObject(t)?mo.merge({},t):mo.isArray(t)?t.slice():t}function o(n){return mo.isUndefined(t[n])?mo.isUndefined(e[n])?void 0:r(void 0,e[n]):r(e[n],t[n])}function a(e){if(!mo.isUndefined(t[e]))return r(void 0,t[e])}function i(n){return mo.isUndefined(t[n])?mo.isUndefined(e[n])?void 0:r(void 0,e[n]):r(void 0,t[n])}function s(n){return n in t?r(e[n],t[n]):n in e?r(void 0,e[n]):void 0}var l={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s};return mo.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=l[e]||o,r=t(e);mo.isUndefined(r)&&t!==s||(n[e]=r)})),n}var fa="1.1.3",pa={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){pa[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var ha={};pa.transitional=function(e,t,n){function r(e,t){return"[Axios v1.1.3] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,o,a){if(!1===e)throw new bo(r(o," has been removed"+(t?" in "+t:"")),bo.ERR_DEPRECATED);return t&&!ha[o]&&(ha[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}};var ma={assertOptions:function(e,t,n){if("object"!==typeof e)throw new bo("options must be an object",bo.ERR_BAD_OPTION_VALUE);for(var r=Object.keys(e),o=r.length;o-- >0;){var a=r[o],i=t[a];if(i){var s=e[a],l=void 0===s||i(s,a,e);if(!0!==l)throw new bo("option "+a+" must be "+l,bo.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new bo("Unknown option "+a,bo.ERR_BAD_OPTION)}},validators:pa},va=ma.validators,ga=function(){function e(t){h(this,e),this.defaults=t,this.interceptors={request:new Ro,response:new Ro}}return v(e,[{key:"request",value:function(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{};var n=t=da(this.defaults,t),r=n.transitional,o=n.paramsSerializer;void 0!==r&&ma.assertOptions(r,{silentJSONParsing:va.transitional(va.boolean),forcedJSONParsing:va.transitional(va.boolean),clarifyTimeoutError:va.transitional(va.boolean)},!1),void 0!==o&&ma.assertOptions(o,{encode:va.function,serialize:va.function},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();var a=t.headers&&mo.merge(t.headers.common,t.headers[t.method]);a&&mo.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),t.headers=new Yo(t.headers,a);var i=[],s=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));var l,c=[];this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));var u,d=0;if(!s){var f=[ua.bind(this),void 0];for(f.unshift.apply(f,i),f.push.apply(f,c),u=f.length,l=Promise.resolve(t);d<u;)l=l.then(f[d++],f[d++]);return l}u=i.length;var p=t;for(d=0;d<u;){var h=i[d++],m=i[d++];try{p=h(p)}catch(v){m.call(this,v);break}}try{l=ua.call(this,p)}catch(v){return Promise.reject(v)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}},{key:"getUri",value:function(e){return Po(Bo((e=da(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}]),e}();mo.forEach(["delete","get","head","options"],(function(e){ga.prototype[e]=function(t,n){return this.request(da(n||{},{method:e,url:t,data:(n||{}).data}))}})),mo.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(da(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ga.prototype[e]=t(),ga.prototype[e+"Form"]=t(!0)}));var ya=ga,ba=function(){function e(t){if(h(this,e),"function"!==typeof t)throw new TypeError("executor must be a function.");var n;this.promise=new Promise((function(e){n=e}));var r=this;this.promise.then((function(e){if(r._listeners){for(var t=r._listeners.length;t-- >0;)r._listeners[t](e);r._listeners=null}})),this.promise.then=function(e){var t,n=new Promise((function(e){r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},t((function(e,t,o){r.reason||(r.reason=new Ho(e,t,o),n(r.reason))}))}return v(e,[{key:"throwIfRequested",value:function(){if(this.reason)throw this.reason}},{key:"subscribe",value:function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}},{key:"unsubscribe",value:function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}}}],[{key:"source",value:function(){var t;return{token:new e((function(e){t=e})),cancel:t}}}]),e}(),_a=ba;var xa=function e(t){var n=new ya(t),r=Mr(ya.prototype.request,n);return mo.extend(r,ya.prototype,n,{allOwnKeys:!0}),mo.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(da(t,n))},r}(ia);xa.Axios=ya,xa.CanceledError=Ho,xa.CancelToken=_a,xa.isCancel=la,xa.VERSION=fa,xa.toFormData=Eo,xa.AxiosError=bo,xa.Cancel=xa.CanceledError,xa.all=function(e){return Promise.all(e)},xa.spread=function(e){return function(t){return e.apply(null,t)}},xa.isAxiosError=function(e){return mo.isObject(e)&&!0===e.isAxiosError},xa.formToJSON=function(e){return zo(mo.isHTMLForm(e)?new FormData(e):e)};var wa=xa,ka=(wa.Axios,wa.AxiosError,wa.CanceledError,wa.isCancel,wa.CancelToken,wa.VERSION,wa.all,wa.Cancel,wa.isAxiosError,wa.spread,wa.toFormData,wa),Sa=t.createRoot(document.getElementById("root")),Ea="/contentserver";Ea&&!Ea.startsWith("/")&&(Ea="/".concat(Ea));var Ca="/ws";Ca&&!Ca.startsWith("/")&&(Ca="/".concat(Ca));var Na=function(){return{reconnectDelay:5e3,onConnect:function(){console.log("Connected to WebSocket")},onDisconnect:function(){console.log("Disconnected from WebSocket, will attempt to reconnect")},onWebSocketClose:function(){console.log("WebSocket closed, will attempt to reconnect")}}},ja=function(){var e=Er(),t=new URLSearchParams(e.search).get("server_url");if(null==t){var n=window.location.hostname,r=window.location.port,o=window.location.protocol;t="".concat(o,"//").concat(n,":").concat(r)}return(0,W.jsx)(Br.ErrorBoundary,{FallbackComponent:Oa,onError:function(e,n){var r={name:null===e||void 0===e?void 0:e.name,message:null===e||void 0===e?void 0:e.messsage,stack:null===e||void 0===e?void 0:e.stack,componentStack:null===n||void 0===n?void 0:n.componentStack};ka({headers:{"Content-Type":"application/json"},method:"post",url:"".concat(t).concat(Ea,"/saveFrontError"),data:r}).then((function(e){e&&200===e.status?console.log("Upload error success."):console.log("Upload error fail.")}))},onReset:function(){},children:(0,W.jsx)(T,{url:"".concat(t).concat(Ea).concat(Ca),config:Na,children:(0,W.jsx)(zr,{})})})};function Oa(e){var t=e.error,n=e.resetErrorBoundary;return(0,W.jsx)("div",{className:"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center text-3xl",children:(0,W.jsxs)("div",{className:"flex flex-col items-center text-green-400",children:[(0,W.jsx)("p",{children:"\u30a8\u30e9\u30fc\u767a\u751f: "}),(0,W.jsx)("pre",{children:t.message}),(0,W.jsx)("br",{}),(0,W.jsx)("br",{}),(0,W.jsx)("button",{className:"text-red-800",onClick:n,children:"\u3053\u3053\u3092\u30af\u30ea\u30c3\u30af\u3057\u3066\u3001\u3082\u3046\u4e00\u5ea6\u8a66\u3057\u3066\u304f\u3060\u3055\u3044"})]})})}Sa.render((0,W.jsx)(Rr,{basename:"/contentapp",children:(0,W.jsx)(ja,{})})),Ur()}()}();
//# sourceMappingURL=main.f595065a.js.map