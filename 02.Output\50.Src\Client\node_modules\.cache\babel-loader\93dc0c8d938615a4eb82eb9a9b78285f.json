{"ast": null, "code": "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nmodule.exports = setToArray;", "map": {"version": 3, "names": ["setToArray", "set", "index", "result", "Array", "size", "for<PERSON>ach", "value", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_setToArray.js"], "sourcesContent": ["/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nmodule.exports = setToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAT,CAAoBC,GAApB,EAAyB;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAL,CADlB;EAGAJ,GAAG,CAACK,OAAJ,CAAY,UAASC,KAAT,EAAgB;IAC1BJ,MAAM,CAAC,EAAED,KAAH,CAAN,GAAkBK,KAAlB;EACD,CAFD;EAGA,OAAOJ,MAAP;AACD;;AAEDK,MAAM,CAACC,OAAP,GAAiBT,UAAjB"}, "metadata": {}, "sourceType": "script"}