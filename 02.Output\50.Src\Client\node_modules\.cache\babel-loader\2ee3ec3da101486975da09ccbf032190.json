{"ast": null, "code": "var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default isBrowser;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "createElement"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/isBrowser.js"], "sourcesContent": ["var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default isBrowser;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,QAAxC,IAAoDD,MAAM,CAACC,QAAP,CAAgBC,aAAtE,CAAjB;AACA,eAAeH,SAAf"}, "metadata": {}, "sourceType": "module"}