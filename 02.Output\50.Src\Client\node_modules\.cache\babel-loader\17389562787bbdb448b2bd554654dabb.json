{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    AjaxBasedTransport = require('./lib/ajax-based'),\n    XhrReceiver = require('./receiver/xhr'),\n    XHRCorsObject = require('./sender/xhr-cors'),\n    XHRLocalObject = require('./sender/xhr-local');\n\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrPollingTransport, AjaxBasedTransport);\n\nXhrPollingTransport.enabled = function (info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XHRCorsObject", "XHRLocalObject", "XhrPollingTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/xhr-polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  ;\n\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrPollingTransport, AjaxBasedTransport);\n\nXhrPollingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\n\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,kBAAkB,GAAGD,OAAO,CAAC,kBAAD,CADhC;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAAA,IAGIG,aAAa,GAAGH,OAAO,CAAC,mBAAD,CAH3B;AAAA,IAIII,cAAc,GAAGJ,OAAO,CAAC,oBAAD,CAJ5B;;AAOA,SAASK,mBAAT,CAA6BC,QAA7B,EAAuC;EACrC,IAAI,CAACF,cAAc,CAACG,OAAhB,IAA2B,CAACJ,aAAa,CAACI,OAA9C,EAAuD;IACrD,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDP,kBAAkB,CAACQ,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,MAAxC,EAAgDJ,WAAhD,EAA6DC,aAA7D;AACD;;AAEDJ,QAAQ,CAACM,mBAAD,EAAsBJ,kBAAtB,CAAR;;AAEAI,mBAAmB,CAACE,OAApB,GAA8B,UAASG,IAAT,EAAe;EAC3C,IAAIA,IAAI,CAACC,UAAT,EAAqB;IACnB,OAAO,KAAP;EACD;;EAED,IAAIP,cAAc,CAACG,OAAf,IAA0BG,IAAI,CAACE,UAAnC,EAA+C;IAC7C,OAAO,IAAP;EACD;;EACD,OAAOT,aAAa,CAACI,OAArB;AACD,CATD;;AAWAF,mBAAmB,CAACQ,aAApB,GAAoC,aAApC;AACAR,mBAAmB,CAACS,UAApB,GAAiC,CAAjC,C,CAAoC;;AAEpCC,MAAM,CAACC,OAAP,GAAiBX,mBAAjB"}, "metadata": {}, "sourceType": "script"}