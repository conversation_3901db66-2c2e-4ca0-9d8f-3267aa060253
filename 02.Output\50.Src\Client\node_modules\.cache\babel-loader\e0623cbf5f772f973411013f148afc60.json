{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{getCellFace,isValidSource}from'../utils/Util.js';import CellBox from'./elements/CellBox';import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";/**\r\n * 時刻コンテンツB<br>\r\n * propsは、「3.28時刻同期」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Now\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */var Now=function Now(props){// Propsのtext_colorが優先で、なければ、text-[#40ffff]が効く\nvar cell1Props=getCellFace(props,'ml-[9rem] mt-[2rem] text-10xl text-[#40ffff]');var cell2Props=getCellFace(props,'mt-[1.5rem] text-[23rem] text-[#40ffff]');return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 grid-rows-2 place-items-center auto-cols-fr leading-[1] gap-y-[3rem]\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid auto-cols-max\",children:/*#__PURE__*/_jsxs(CellBox,_objectSpread(_objectSpread({},cell1Props),{},{children:[props.year,/*#__PURE__*/_jsx(\"span\",{className:\"text-6xl mx-[1rem]\",children:\"\\u5E74\"}),Number(props.month)<10&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"span\",{className:\"text-black\",children:\"0\"})}),props.month,/*#__PURE__*/_jsx(\"span\",{className:\"text-6xl mx-[1rem]\",children:\"\\u6708\"}),Number(props.day)<10&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"span\",{className:\"text-black\",children:\"0\"})}),props.day,/*#__PURE__*/_jsx(\"span\",{className:\"text-6xl ml-[1rem]\",children:\"\\u65E5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\uFF08\"}),props.weekday,/*#__PURE__*/_jsx(\"span\",{children:\"\\uFF09\"})]}))}),/*#__PURE__*/_jsxs(CellBox,_objectSpread(_objectSpread({},cell2Props),{},{children:[Number(props.hour)<10&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(\"span\",{className:\"text-black\",children:\"0\"})}),props.hour,/*#__PURE__*/_jsx(\"span\",{className:\"text-12xl mx-[1rem]\",children:\"\\u6642\"}),props.minute,/*#__PURE__*/_jsx(\"span\",{className:\"text-12xl ml-[1rem]\",children:\"\\u5206\"})]}))]})});};export default Now;", "map": {"version": 3, "names": ["React", "getCellFace", "isValidSource", "CellBox", "Now", "props", "cell1Props", "cell2Props", "year", "Number", "month", "day", "weekday", "hour", "minute"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Now.js"], "sourcesContent": ["import React from 'react';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport CellBox from './elements/CellBox';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  year: PropTypes.string.isRequired,\r\n  month: PropTypes.string.isRequired,\r\n  day: PropTypes.string.isRequired,\r\n  weekday: PropTypes.string.isRequired,\r\n  hour: PropTypes.string.isRequired,\r\n  minute: PropTypes.string.isRequired,\r\n  text_color: PropTypes.string,\r\n  background_color: PropTypes.string,\r\n};\r\n\r\n/**\r\n * 時刻コンテンツB<br>\r\n * propsは、「3.28時刻同期」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Now\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Now = (props) => {\r\n  // Propsのtext_colorが優先で、なければ、text-[#40ffff]が効く\r\n  let cell1Props = getCellFace(props, 'ml-[9rem] mt-[2rem] text-10xl text-[#40ffff]');\r\n  let cell2Props = getCellFace(props, 'mt-[1.5rem] text-[23rem] text-[#40ffff]');\r\n\r\n  return (\r\n    <>\r\n      {isValidSource(props) && (\r\n        <div className=\"grid grid-cols-1 grid-rows-2 place-items-center auto-cols-fr leading-[1] gap-y-[3rem]\">\r\n\t\t   <div className=\"grid auto-cols-max\">\r\n          <CellBox {...cell1Props}>\r\n            {props.year}\r\n            <span className=\"text-6xl mx-[1rem]\">年</span>\r\n            { Number(props.month) < 10 && (\r\n              <>\r\n                <span className=\"text-black\">0</span>\r\n              </>\r\n            )}\r\n            {props.month}\r\n            <span className=\"text-6xl mx-[1rem]\">月</span>\r\n            { Number(props.day) < 10 && (\r\n              <>\r\n                <span className=\"text-black\">0</span>\r\n              </>\r\n            )}\r\n            {props.day}\r\n            <span className=\"text-6xl ml-[1rem]\">日</span>\r\n            <span>（</span>\r\n            {props.weekday}\r\n            <span>）</span>\r\n          </CellBox>\r\n\t\t  </div> \r\n          <CellBox {...cell2Props}>\r\n            { Number(props.hour) < 10 && (\r\n              <>\r\n                <span className=\"text-black\">0</span>\r\n              </>\r\n            )}\r\n            {props.hour}\r\n            <span className=\"text-12xl mx-[1rem]\">時</span>\r\n            {props.minute}\r\n            <span className=\"text-12xl ml-[1rem]\">分</span>\r\n          </CellBox>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nNow.propTypes = propTypes;\r\nexport default Now;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,QAAP,KAAoB,oBAApB,C,6IAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,IAAG,CAAG,QAANA,IAAM,CAACC,KAAD,CAAW,CACrB;AACA,GAAIC,WAAU,CAAGL,WAAW,CAACI,KAAD,CAAQ,8CAAR,CAA5B,CACA,GAAIE,WAAU,CAAGN,WAAW,CAACI,KAAD,CAAQ,yCAAR,CAA5B,CAEA,mBACE,yBACGH,aAAa,CAACG,KAAD,CAAb,eACC,aAAK,SAAS,CAAC,uFAAf,wBACH,YAAK,SAAS,CAAC,oBAAf,uBACK,MAAC,OAAD,gCAAaC,UAAb,gBACGD,KAAK,CAACG,IADT,cAEE,aAAM,SAAS,CAAC,oBAAhB,oBAFF,CAGIC,MAAM,CAACJ,KAAK,CAACK,KAAP,CAAN,CAAsB,EAAtB,eACA,sCACE,aAAM,SAAS,CAAC,YAAhB,eADF,EAJJ,CAQGL,KAAK,CAACK,KART,cASE,aAAM,SAAS,CAAC,oBAAhB,oBATF,CAUID,MAAM,CAACJ,KAAK,CAACM,GAAP,CAAN,CAAoB,EAApB,eACA,sCACE,aAAM,SAAS,CAAC,YAAhB,eADF,EAXJ,CAeGN,KAAK,CAACM,GAfT,cAgBE,aAAM,SAAS,CAAC,oBAAhB,oBAhBF,cAiBE,gCAjBF,CAkBGN,KAAK,CAACO,OAlBT,cAmBE,gCAnBF,IADL,EADG,cAwBE,MAAC,OAAD,gCAAaL,UAAb,gBACIE,MAAM,CAACJ,KAAK,CAACQ,IAAP,CAAN,CAAqB,EAArB,eACA,sCACE,aAAM,SAAS,CAAC,YAAhB,eADF,EAFJ,CAMGR,KAAK,CAACQ,IANT,cAOE,aAAM,SAAS,CAAC,qBAAhB,oBAPF,CAQGR,KAAK,CAACS,MART,cASE,aAAM,SAAS,CAAC,qBAAhB,oBATF,IAxBF,GAFJ,EADF,CA0CD,CA/CD,CAkDA,cAAeV,IAAf"}, "metadata": {}, "sourceType": "module"}