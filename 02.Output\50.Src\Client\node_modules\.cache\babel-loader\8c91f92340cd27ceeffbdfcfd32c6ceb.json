{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport throttle from 'lodash/throttle';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\n\nfunction useThrottleFn(fn, options) {\n  var _a;\n\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useThrottleFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var throttled = useMemo(function () {\n    return throttle(function () {\n      var args = [];\n\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    throttled.cancel();\n  });\n  return {\n    run: throttled,\n    cancel: throttled.cancel,\n    flush: throttled.flush\n  };\n}\n\nexport default useThrottleFn;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "throttle", "useMemo", "useLatest", "useUnmount", "isFunction", "isDev", "useThrottleFn", "fn", "options", "_a", "console", "fnRef", "wait", "throttled", "args", "_i", "current", "apply", "cancel", "run", "flush"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useThrottleFn/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport throttle from 'lodash/throttle';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useThrottleFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useThrottleFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var throttled = useMemo(function () {\n    return throttle(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    throttled.cancel();\n  });\n  return {\n    run: throttled,\n    cancel: throttled.cancel,\n    flush: throttled.flush\n  };\n}\nexport default useThrottleFn;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,OAAOS,QAAP,MAAqB,iBAArB;AACA,SAASC,OAAT,QAAwB,OAAxB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,KAAP,MAAkB,gBAAlB;;AACA,SAASC,aAAT,CAAuBC,EAAvB,EAA2BC,OAA3B,EAAoC;EAClC,IAAIC,EAAJ;;EACA,IAAIJ,KAAJ,EAAW;IACT,IAAI,CAACD,UAAU,CAACG,EAAD,CAAf,EAAqB;MACnBG,OAAO,CAACtB,KAAR,CAAc,uDAAuDW,MAAvD,CAA8D,OAAOQ,EAArE,CAAd;IACD;EACF;;EACD,IAAII,KAAK,GAAGT,SAAS,CAACK,EAAD,CAArB;EACA,IAAIK,IAAI,GAAG,CAACH,EAAE,GAAGD,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACI,IAAhE,MAA0E,IAA1E,IAAkFH,EAAE,KAAK,KAAK,CAA9F,GAAkGA,EAAlG,GAAuG,IAAlH;EACA,IAAII,SAAS,GAAGZ,OAAO,CAAC,YAAY;IAClC,OAAOD,QAAQ,CAAC,YAAY;MAC1B,IAAIc,IAAI,GAAG,EAAX;;MACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGtB,SAAS,CAACC,MAAhC,EAAwCqB,EAAE,EAA1C,EAA8C;QAC5CD,IAAI,CAACC,EAAD,CAAJ,GAAWtB,SAAS,CAACsB,EAAD,CAApB;MACD;;MACD,OAAOJ,KAAK,CAACK,OAAN,CAAcC,KAAd,CAAoBN,KAApB,EAA2BtB,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACyC,IAAD,CAAX,EAAmB,KAAnB,CAAxC,CAAP;IACD,CANc,EAMZF,IANY,EAMNJ,OANM,CAAf;EAOD,CARsB,EAQpB,EARoB,CAAvB;EASAL,UAAU,CAAC,YAAY;IACrBU,SAAS,CAACK,MAAV;EACD,CAFS,CAAV;EAGA,OAAO;IACLC,GAAG,EAAEN,SADA;IAELK,MAAM,EAAEL,SAAS,CAACK,MAFb;IAGLE,KAAK,EAAEP,SAAS,CAACO;EAHZ,CAAP;AAKD;;AACD,eAAed,aAAf"}, "metadata": {}, "sourceType": "module"}