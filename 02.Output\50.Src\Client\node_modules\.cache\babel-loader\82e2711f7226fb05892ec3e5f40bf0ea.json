{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    XhrDriver = require('../driver/xhr');\n\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\n\ninherits(XHRCorsObject, XhrDriver);\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\nmodule.exports = XHRCorsObject;", "map": {"version": 3, "names": ["inherits", "require", "XhrDriver", "XHRCorsObject", "method", "url", "payload", "opts", "call", "enabled", "supportsCORS", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/sender/xhr-cors.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\n\ninherits(XHRCorsObject, XhrDriver);\n\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\n\nmodule.exports = XHRCorsObject;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,eAAD,CADvB;;AAIA,SAASE,aAAT,CAAuBC,MAAvB,EAA+BC,GAA/B,EAAoCC,OAApC,EAA6CC,IAA7C,EAAmD;EACjDL,SAAS,CAACM,IAAV,CAAe,IAAf,EAAqBJ,MAArB,EAA6BC,GAA7B,EAAkCC,OAAlC,EAA2CC,IAA3C;AACD;;AAEDP,QAAQ,CAACG,aAAD,EAAgBD,SAAhB,CAAR;AAEAC,aAAa,CAACM,OAAd,GAAwBP,SAAS,CAACO,OAAV,IAAqBP,SAAS,CAACQ,YAAvD;AAEAC,MAAM,CAACC,OAAP,GAAiBT,aAAjB"}, "metadata": {}, "sourceType": "script"}