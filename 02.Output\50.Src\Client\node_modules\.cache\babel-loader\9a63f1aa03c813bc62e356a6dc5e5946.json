{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\Deployment.js\";\nimport React from 'react';\nimport Title from './elements/Title';\nimport Cell from './elements/Cell';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\n/**\r\n * 配備状況コンテンツ<br>\r\n * propsは、「3.4配備状況コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Deployment\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\n\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Deployment = props => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-6xl\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      title: '配備状況'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), isValidSource(props) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]\",\n      children: // 車両名称の配列を軸で、行を作る\n      props.car_name.map((item, index) => {\n        return /*#__PURE__*/_jsxDEV(DeploymentRow, { ...props,\n          index: index\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 22\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n\n_c = Deployment;\n\nconst DeploymentRow = props => {\n  let cell1 = getCellFace(props.deployment ? props.deployment[props.index] : null, 'col-span-2');\n  let cell2 = getCellFace(props.car_name ? props.car_name[props.index] : null, 'col-span-4 col-start-5');\n  let cell3 = getCellFace(props.arrow ? props.arrow[props.index] : null, 'col-span-1 col-start-11');\n  let cell4 = getCellFace(props.move_car_name ? props.move_car_name[props.index] : null, 'col-span-4 col-start-14');\n  let cell5 = getCellFace(props.car_type ? props.car_type[props.index] : null, 'col-span-4 col-start-20');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Cell, { ...cell1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...cell2\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...cell3\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...cell4\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...cell5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n_c2 = DeploymentRow;\nexport default Deployment;\n\nvar _c, _c2;\n\n$RefreshReg$(_c, \"Deployment\");\n$RefreshReg$(_c2, \"DeploymentRow\");", "map": {"version": 3, "names": ["React", "Title", "Cell", "getCellFace", "isValidSource", "Deployment", "props", "car_name", "map", "item", "index", "DeploymentRow", "cell1", "deployment", "cell2", "cell3", "arrow", "cell4", "move_car_name", "cell5", "car_type"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Deployment.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 配備状況コンテンツ<br>\r\n * propsは、「3.4配備状況コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Deployment\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Deployment = (props) => {\r\n  return (\r\n    <div className=\"text-6xl\">\r\n      <Title title={'配備状況'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]\">\r\n          {\r\n            // 車両名称の配列を軸で、行を作る\r\n            props.car_name.map((item, index) => {\r\n              return <DeploymentRow key={index} {...props} index={index} />;\r\n            })\r\n          }\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DeploymentRow = (props) => {\r\n  let cell1 = getCellFace(\r\n    props.deployment ? props.deployment[props.index] : null,\r\n    'col-span-2'\r\n  );\r\n  let cell2 = getCellFace(\r\n    props.car_name ? props.car_name[props.index] : null,\r\n    'col-span-4 col-start-5'\r\n  );\r\n  let cell3 = getCellFace(\r\n    props.arrow ? props.arrow[props.index] : null,\r\n    'col-span-1 col-start-11'\r\n  );\r\n  let cell4 = getCellFace(\r\n    props.move_car_name ? props.move_car_name[props.index] : null,\r\n    'col-span-4 col-start-14'\r\n  );\r\n  let cell5 = getCellFace(\r\n    props.car_type ? props.car_type[props.index] : null,\r\n    'col-span-4 col-start-20'\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <Cell {...cell1} />\r\n      <Cell {...cell2} />\r\n      <Cell {...cell3} />\r\n      <Cell {...cell4} />\r\n      <Cell {...cell5} />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Deployment;\r\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,KAAP,MAAkB,kBAAlB;AACA,OAAOC,IAAP,MAAiB,iBAAjB;AACA,SAASC,WAAT,EAAsBC,aAAtB,QAA2C,kBAA3C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AACA,MAAMC,UAAU,GAAIC,KAAD,IAAW;EAC5B,oBACE;IAAK,SAAS,EAAC,UAAf;IAAA,wBACE,QAAC,KAAD;MAAO,KAAK,EAAE;IAAd;MAAA;MAAA;MAAA;IAAA,QADF,EAEGF,aAAa,CAACE,KAAD,CAAb,iBACC;MAAK,SAAS,EAAC,0FAAf;MAAA,UAEI;MACAA,KAAK,CAACC,QAAN,CAAeC,GAAf,CAAmB,CAACC,IAAD,EAAOC,KAAP,KAAiB;QAClC,oBAAO,QAAC,aAAD,OAA+BJ,KAA/B;UAAsC,KAAK,EAAEI;QAA7C,GAAoBA,KAApB;UAAA;UAAA;UAAA;QAAA,QAAP;MACD,CAFD;IAHJ;MAAA;MAAA;MAAA;IAAA,QAHJ;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAeD,CAhBD;;KAAML,U;;AAkBN,MAAMM,aAAa,GAAIL,KAAD,IAAW;EAC/B,IAAIM,KAAK,GAAGT,WAAW,CACrBG,KAAK,CAACO,UAAN,GAAmBP,KAAK,CAACO,UAAN,CAAiBP,KAAK,CAACI,KAAvB,CAAnB,GAAmD,IAD9B,EAErB,YAFqB,CAAvB;EAIA,IAAII,KAAK,GAAGX,WAAW,CACrBG,KAAK,CAACC,QAAN,GAAiBD,KAAK,CAACC,QAAN,CAAeD,KAAK,CAACI,KAArB,CAAjB,GAA+C,IAD1B,EAErB,wBAFqB,CAAvB;EAIA,IAAIK,KAAK,GAAGZ,WAAW,CACrBG,KAAK,CAACU,KAAN,GAAcV,KAAK,CAACU,KAAN,CAAYV,KAAK,CAACI,KAAlB,CAAd,GAAyC,IADpB,EAErB,yBAFqB,CAAvB;EAIA,IAAIO,KAAK,GAAGd,WAAW,CACrBG,KAAK,CAACY,aAAN,GAAsBZ,KAAK,CAACY,aAAN,CAAoBZ,KAAK,CAACI,KAA1B,CAAtB,GAAyD,IADpC,EAErB,yBAFqB,CAAvB;EAIA,IAAIS,KAAK,GAAGhB,WAAW,CACrBG,KAAK,CAACc,QAAN,GAAiBd,KAAK,CAACc,QAAN,CAAed,KAAK,CAACI,KAArB,CAAjB,GAA+C,IAD1B,EAErB,yBAFqB,CAAvB;EAKA,oBACE;IAAA,wBACE,QAAC,IAAD,OAAUE;IAAV;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,IAAD,OAAUE;IAAV;MAAA;MAAA;MAAA;IAAA,QAFF,eAGE,QAAC,IAAD,OAAUC;IAAV;MAAA;MAAA;MAAA;IAAA,QAHF,eAIE,QAAC,IAAD,OAAUE;IAAV;MAAA;MAAA;MAAA;IAAA,QAJF,eAKE,QAAC,IAAD,OAAUE;IAAV;MAAA;MAAA;MAAA;IAAA,QALF;EAAA,gBADF;AASD,CA/BD;;MAAMR,a;AAiCN,eAAeN,UAAf"}, "metadata": {}, "sourceType": "module"}