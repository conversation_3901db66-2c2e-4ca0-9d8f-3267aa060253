{"ast": null, "code": "import _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\n\n/**\n * @internal\n */\nvar NULL = 0;\n/**\n * @internal\n */\n\nvar LF = 10;\n/**\n * @internal\n */\n\nvar CR = 13;\n/**\n * @internal\n */\n\nvar COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\n\nexport var Parser = /*#__PURE__*/function () {\n  function Parser(onFrame, onIncomingPing) {\n    _classCallCheck(this, Parser);\n\n    this.onFrame = onFrame;\n    this.onIncomingPing = onIncomingPing;\n    this._encoder = new TextEncoder();\n    this._decoder = new TextDecoder();\n    this._token = [];\n\n    this._initState();\n  }\n\n  _createClass(Parser, [{\n    key: \"parseChunk\",\n    value: function parseChunk(segment) {\n      var appendMissingNULLonIncoming = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var chunk;\n\n      if (segment instanceof ArrayBuffer) {\n        chunk = new Uint8Array(segment);\n      } else {\n        chunk = this._encoder.encode(segment);\n      } // See https://github.com/stomp-js/stompjs/issues/89\n      // Remove when underlying issue is fixed.\n      //\n      // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n\n\n      if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n        var chunkWithNull = new Uint8Array(chunk.length + 1);\n        chunkWithNull.set(chunk, 0);\n        chunkWithNull[chunk.length] = 0;\n        chunk = chunkWithNull;\n      } // tslint:disable-next-line:prefer-for-of\n\n\n      for (var i = 0; i < chunk.length; i++) {\n        var byte = chunk[i];\n\n        this._onByte(byte);\n      }\n    } // The following implements a simple Rec Descent Parser.\n    // The grammar is simple and just one byte tells what should be the next state\n\n  }, {\n    key: \"_collectFrame\",\n    value: function _collectFrame(byte) {\n      if (byte === NULL) {\n        // Ignore\n        return;\n      }\n\n      if (byte === CR) {\n        // Ignore CR\n        return;\n      }\n\n      if (byte === LF) {\n        // Incoming Ping\n        this.onIncomingPing();\n        return;\n      }\n\n      this._onByte = this._collectCommand;\n\n      this._reinjectByte(byte);\n    }\n  }, {\n    key: \"_collectCommand\",\n    value: function _collectCommand(byte) {\n      if (byte === CR) {\n        // Ignore CR\n        return;\n      }\n\n      if (byte === LF) {\n        this._results.command = this._consumeTokenAsUTF8();\n        this._onByte = this._collectHeaders;\n        return;\n      }\n\n      this._consumeByte(byte);\n    }\n  }, {\n    key: \"_collectHeaders\",\n    value: function _collectHeaders(byte) {\n      if (byte === CR) {\n        // Ignore CR\n        return;\n      }\n\n      if (byte === LF) {\n        this._setupCollectBody();\n\n        return;\n      }\n\n      this._onByte = this._collectHeaderKey;\n\n      this._reinjectByte(byte);\n    }\n  }, {\n    key: \"_reinjectByte\",\n    value: function _reinjectByte(byte) {\n      this._onByte(byte);\n    }\n  }, {\n    key: \"_collectHeaderKey\",\n    value: function _collectHeaderKey(byte) {\n      if (byte === COLON) {\n        this._headerKey = this._consumeTokenAsUTF8();\n        this._onByte = this._collectHeaderValue;\n        return;\n      }\n\n      this._consumeByte(byte);\n    }\n  }, {\n    key: \"_collectHeaderValue\",\n    value: function _collectHeaderValue(byte) {\n      if (byte === CR) {\n        // Ignore CR\n        return;\n      }\n\n      if (byte === LF) {\n        this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n\n        this._headerKey = undefined;\n        this._onByte = this._collectHeaders;\n        return;\n      }\n\n      this._consumeByte(byte);\n    }\n  }, {\n    key: \"_setupCollectBody\",\n    value: function _setupCollectBody() {\n      var contentLengthHeader = this._results.headers.filter(function (header) {\n        return header[0] === 'content-length';\n      })[0];\n\n      if (contentLengthHeader) {\n        this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n        this._onByte = this._collectBodyFixedSize;\n      } else {\n        this._onByte = this._collectBodyNullTerminated;\n      }\n    }\n  }, {\n    key: \"_collectBodyNullTerminated\",\n    value: function _collectBodyNullTerminated(byte) {\n      if (byte === NULL) {\n        this._retrievedBody();\n\n        return;\n      }\n\n      this._consumeByte(byte);\n    }\n  }, {\n    key: \"_collectBodyFixedSize\",\n    value: function _collectBodyFixedSize(byte) {\n      // It is post decrement, so that we discard the trailing NULL octet\n      if (this._bodyBytesRemaining-- === 0) {\n        this._retrievedBody();\n\n        return;\n      }\n\n      this._consumeByte(byte);\n    }\n  }, {\n    key: \"_retrievedBody\",\n    value: function _retrievedBody() {\n      this._results.binaryBody = this._consumeTokenAsRaw();\n      this.onFrame(this._results);\n\n      this._initState();\n    } // Rec Descent Parser helpers\n\n  }, {\n    key: \"_consumeByte\",\n    value: function _consumeByte(byte) {\n      this._token.push(byte);\n    }\n  }, {\n    key: \"_consumeTokenAsUTF8\",\n    value: function _consumeTokenAsUTF8() {\n      return this._decoder.decode(this._consumeTokenAsRaw());\n    }\n  }, {\n    key: \"_consumeTokenAsRaw\",\n    value: function _consumeTokenAsRaw() {\n      var rawResult = new Uint8Array(this._token);\n      this._token = [];\n      return rawResult;\n    }\n  }, {\n    key: \"_initState\",\n    value: function _initState() {\n      this._results = {\n        command: undefined,\n        headers: [],\n        binaryBody: undefined\n      };\n      this._token = [];\n      this._headerKey = undefined;\n      this._onByte = this._collectFrame;\n    }\n  }]);\n\n  return Parser;\n}();", "map": {"version": 3, "mappings": ";;;AAEA;;;AAGA,IAAMA,IAAI,GAAG,CAAb;AACA;;;;AAGA,IAAMC,EAAE,GAAG,EAAX;AACA;;;;AAGA,IAAMC,EAAE,GAAG,EAAX;AACA;;;;AAGA,IAAMC,KAAK,GAAG,EAAd;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,WAAaC,MAAb;EAYE,gBACSC,OADT,EAESC,cAFT,EAEmC;IAAA;;IAD1B;IACA;IAbQ,gBAAW,IAAIC,WAAJ,EAAX;IACA,gBAAW,IAAIC,WAAJ,EAAX;IAIT,cAAmB,EAAnB;;IAUN,KAAKC,UAAL;EACD;;EAjBH;IAAA;IAAA,OAmBS,oBACLC,OADK,EAEuC;MAAA,IAA5CC,2BAA4C,uEAAL,KAAK;MAE5C,IAAIC,KAAJ;;MAEA,IAAIF,OAAO,YAAYG,WAAvB,EAAoC;QAClCD,KAAK,GAAG,IAAIE,UAAJ,CAAeJ,OAAf,CAAR;MACD,CAFD,MAEO;QACLE,KAAK,GAAG,KAAKG,QAAL,CAAcC,MAAd,CAAqBN,OAArB,CAAR;MACD,CAR2C,CAU5C;MACA;MACA;MACA;;;MACA,IAAIC,2BAA2B,IAAIC,KAAK,CAACA,KAAK,CAACK,MAAN,GAAe,CAAhB,CAAL,KAA4B,CAA/D,EAAkE;QAChE,IAAMC,aAAa,GAAG,IAAIJ,UAAJ,CAAeF,KAAK,CAACK,MAAN,GAAe,CAA9B,CAAtB;QACAC,aAAa,CAACC,GAAd,CAAkBP,KAAlB,EAAyB,CAAzB;QACAM,aAAa,CAACN,KAAK,CAACK,MAAP,CAAb,GAA8B,CAA9B;QACAL,KAAK,GAAGM,aAAR;MACD,CAnB2C,CAqB5C;;;MACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAAK,CAACK,MAA1B,EAAkCG,CAAC,EAAnC,EAAuC;QACrC,IAAMC,IAAI,GAAGT,KAAK,CAACQ,CAAD,CAAlB;;QACA,KAAKE,OAAL,CAAaD,IAAb;MACD;IACF,CA/CH,CAiDE;IACA;;EAlDF;IAAA;IAAA,OAoDU,uBAAcA,IAAd,EAA0B;MAChC,IAAIA,IAAI,KAAKrB,IAAb,EAAmB;QACjB;QACA;MACD;;MACD,IAAIqB,IAAI,KAAKnB,EAAb,EAAiB;QACf;QACA;MACD;;MACD,IAAImB,IAAI,KAAKpB,EAAb,EAAiB;QACf;QACA,KAAKK,cAAL;QACA;MACD;;MAED,KAAKgB,OAAL,GAAe,KAAKC,eAApB;;MACA,KAAKC,aAAL,CAAmBH,IAAnB;IACD;EArEH;IAAA;IAAA,OAuEU,yBAAgBA,IAAhB,EAA4B;MAClC,IAAIA,IAAI,KAAKnB,EAAb,EAAiB;QACf;QACA;MACD;;MACD,IAAImB,IAAI,KAAKpB,EAAb,EAAiB;QACf,KAAKwB,QAAL,CAAcC,OAAd,GAAwB,KAAKC,mBAAL,EAAxB;QACA,KAAKL,OAAL,GAAe,KAAKM,eAApB;QACA;MACD;;MAED,KAAKC,YAAL,CAAkBR,IAAlB;IACD;EAnFH;IAAA;IAAA,OAqFU,yBAAgBA,IAAhB,EAA4B;MAClC,IAAIA,IAAI,KAAKnB,EAAb,EAAiB;QACf;QACA;MACD;;MACD,IAAImB,IAAI,KAAKpB,EAAb,EAAiB;QACf,KAAK6B,iBAAL;;QACA;MACD;;MACD,KAAKR,OAAL,GAAe,KAAKS,iBAApB;;MACA,KAAKP,aAAL,CAAmBH,IAAnB;IACD;EAhGH;IAAA;IAAA,OAkGU,uBAAcA,IAAd,EAA0B;MAChC,KAAKC,OAAL,CAAaD,IAAb;IACD;EApGH;IAAA;IAAA,OAsGU,2BAAkBA,IAAlB,EAA8B;MACpC,IAAIA,IAAI,KAAKlB,KAAb,EAAoB;QAClB,KAAK6B,UAAL,GAAkB,KAAKL,mBAAL,EAAlB;QACA,KAAKL,OAAL,GAAe,KAAKW,mBAApB;QACA;MACD;;MACD,KAAKJ,YAAL,CAAkBR,IAAlB;IACD;EA7GH;IAAA;IAAA,OA+GU,6BAAoBA,IAApB,EAAgC;MACtC,IAAIA,IAAI,KAAKnB,EAAb,EAAiB;QACf;QACA;MACD;;MACD,IAAImB,IAAI,KAAKpB,EAAb,EAAiB;QACf,KAAKwB,QAAL,CAAcS,OAAd,CAAsBC,IAAtB,CAA2B,CAAC,KAAKH,UAAN,EAAkB,KAAKL,mBAAL,EAAlB,CAA3B;;QACA,KAAKK,UAAL,GAAkBI,SAAlB;QACA,KAAKd,OAAL,GAAe,KAAKM,eAApB;QACA;MACD;;MACD,KAAKC,YAAL,CAAkBR,IAAlB;IACD;EA3HH;IAAA;IAAA,OA6HU,6BAAiB;MACvB,IAAMgB,mBAAmB,GAAG,KAAKZ,QAAL,CAAcS,OAAd,CAAsBI,MAAtB,CAC1B,UAACC,MAAD,EAA6B;QAC3B,OAAOA,MAAM,CAAC,CAAD,CAAN,KAAc,gBAArB;MACD,CAHyB,EAI1B,CAJ0B,CAA5B;;MAMA,IAAIF,mBAAJ,EAAyB;QACvB,KAAKG,mBAAL,GAA2BC,QAAQ,CAACJ,mBAAmB,CAAC,CAAD,CAApB,EAAyB,EAAzB,CAAnC;QACA,KAAKf,OAAL,GAAe,KAAKoB,qBAApB;MACD,CAHD,MAGO;QACL,KAAKpB,OAAL,GAAe,KAAKqB,0BAApB;MACD;IACF;EA1IH;IAAA;IAAA,OA4IU,oCAA2BtB,IAA3B,EAAuC;MAC7C,IAAIA,IAAI,KAAKrB,IAAb,EAAmB;QACjB,KAAK4C,cAAL;;QACA;MACD;;MACD,KAAKf,YAAL,CAAkBR,IAAlB;IACD;EAlJH;IAAA;IAAA,OAoJU,+BAAsBA,IAAtB,EAAkC;MACxC;MACA,IAAI,KAAKmB,mBAAL,OAA+B,CAAnC,EAAsC;QACpC,KAAKI,cAAL;;QACA;MACD;;MACD,KAAKf,YAAL,CAAkBR,IAAlB;IACD;EA3JH;IAAA;IAAA,OA6JU,0BAAc;MACpB,KAAKI,QAAL,CAAcoB,UAAd,GAA2B,KAAKC,kBAAL,EAA3B;MAEA,KAAKzC,OAAL,CAAa,KAAKoB,QAAlB;;MAEA,KAAKhB,UAAL;IACD,CAnKH,CAqKE;;EArKF;IAAA;IAAA,OAuKU,sBAAaY,IAAb,EAAyB;MAC/B,KAAK0B,MAAL,CAAYZ,IAAZ,CAAiBd,IAAjB;IACD;EAzKH;IAAA;IAAA,OA2KU,+BAAmB;MACzB,OAAO,KAAK2B,QAAL,CAAcC,MAAd,CAAqB,KAAKH,kBAAL,EAArB,CAAP;IACD;EA7KH;IAAA;IAAA,OA+KU,8BAAkB;MACxB,IAAMI,SAAS,GAAG,IAAIpC,UAAJ,CAAe,KAAKiC,MAApB,CAAlB;MACA,KAAKA,MAAL,GAAc,EAAd;MACA,OAAOG,SAAP;IACD;EAnLH;IAAA;IAAA,OAqLU,sBAAU;MAChB,KAAKzB,QAAL,GAAgB;QACdC,OAAO,EAAEU,SADK;QAEdF,OAAO,EAAE,EAFK;QAGdW,UAAU,EAAET;MAHE,CAAhB;MAMA,KAAKW,MAAL,GAAc,EAAd;MACA,KAAKf,UAAL,GAAkBI,SAAlB;MAEA,KAAKd,OAAL,GAAe,KAAK6B,aAApB;IACD;EAhMH;;EAAA;AAAA", "names": ["NULL", "LF", "CR", "COLON", "<PERSON><PERSON><PERSON>", "onFrame", "onIncomingPing", "TextEncoder", "TextDecoder", "_initState", "segment", "appendMissingNULLonIncoming", "chunk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "_encoder", "encode", "length", "chunkWithNull", "set", "i", "byte", "_onByte", "_collectCommand", "_reinjectByte", "_results", "command", "_consumeTokenAsUTF8", "_collectHeaders", "_consumeByte", "_setupCollectBody", "_collect<PERSON><PERSON><PERSON><PERSON>ey", "_<PERSON><PERSON><PERSON>", "_collectHeaderValue", "headers", "push", "undefined", "contentLengthHeader", "filter", "header", "_bodyBytesRemaining", "parseInt", "_collectBodyFixedSize", "_collectBodyNullTerminated", "_retrievedBody", "binaryBody", "_consumeTokenAsRaw", "_token", "_decoder", "decode", "rawResult", "_collectFrame"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\parser.ts"], "sourcesContent": ["import { IRawFrameType } from './types';\n\n/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  private readonly _encoder = new TextEncoder();\n  private readonly _decoder = new TextDecoder();\n\n  private _results: IRawFrameType;\n\n  private _token: number[] = [];\n  private _headerKey: string;\n  private _bodyBytesRemaining: number;\n\n  private _onByte: (byte: number) => void;\n\n  public constructor(\n    public onFrame: (rawFrame: IRawFrameType) => void,\n    public onIncomingPing: () => void\n  ) {\n    this._initState();\n  }\n\n  public parseChunk(\n    segment: string | ArrayBuffer,\n    appendMissingNULLonIncoming: boolean = false\n  ) {\n    let chunk: Uint8Array;\n\n    if (segment instanceof ArrayBuffer) {\n      chunk = new Uint8Array(segment);\n    } else {\n      chunk = this._encoder.encode(segment);\n    }\n\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n\n  private _collectFrame(byte: number): void {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n\n  private _collectCommand(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaders(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n\n  private _reinjectByte(byte: number) {\n    this._onByte(byte);\n  }\n\n  private _collectHeaderKey(byte: number): void {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaderValue(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(\n      (header: [string, string]) => {\n        return header[0] === 'content-length';\n      }\n    )[0];\n\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n\n  private _collectBodyNullTerminated(byte: number): void {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectBodyFixedSize(byte: number): void {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if (this._bodyBytesRemaining-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n\n    this.onFrame(this._results);\n\n    this._initState();\n  }\n\n  // Rec Descent Parser helpers\n\n  private _consumeByte(byte: number) {\n    this._token.push(byte);\n  }\n\n  private _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n\n  private _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n\n  private _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined,\n    };\n\n    this._token = [];\n    this._headerKey = undefined;\n\n    this._onByte = this._collectFrame;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}