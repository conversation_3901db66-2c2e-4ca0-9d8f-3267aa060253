{"ast": null, "code": "'use strict'; // Some extra characters that Chrome gets wrong, and substitutes with\n// something else on the wire.\n// eslint-disable-next-line no-control-regex, no-misleading-character-class\n\nvar extraEscapable = /[\\x00-\\x1f\\ud800-\\udfff\\ufffe\\uffff\\u0300-\\u0333\\u033d-\\u0346\\u034a-\\u034c\\u0350-\\u0352\\u0357-\\u0358\\u035c-\\u0362\\u0374\\u037e\\u0387\\u0591-\\u05af\\u05c4\\u0610-\\u0617\\u0653-\\u0654\\u0657-\\u065b\\u065d-\\u065e\\u06df-\\u06e2\\u06eb-\\u06ec\\u0730\\u0732-\\u0733\\u0735-\\u0736\\u073a\\u073d\\u073f-\\u0741\\u0743\\u0745\\u0747\\u07eb-\\u07f1\\u0951\\u0958-\\u095f\\u09dc-\\u09dd\\u09df\\u0a33\\u0a36\\u0a59-\\u0a5b\\u0a5e\\u0b5c-\\u0b5d\\u0e38-\\u0e39\\u0f43\\u0f4d\\u0f52\\u0f57\\u0f5c\\u0f69\\u0f72-\\u0f76\\u0f78\\u0f80-\\u0f83\\u0f93\\u0f9d\\u0fa2\\u0fa7\\u0fac\\u0fb9\\u1939-\\u193a\\u1a17\\u1b6b\\u1cda-\\u1cdb\\u1dc0-\\u1dcf\\u1dfc\\u1dfe\\u1f71\\u1f73\\u1f75\\u1f77\\u1f79\\u1f7b\\u1f7d\\u1fbb\\u1fbe\\u1fc9\\u1fcb\\u1fd3\\u1fdb\\u1fe3\\u1feb\\u1fee-\\u1fef\\u1ff9\\u1ffb\\u1ffd\\u2000-\\u2001\\u20d0-\\u20d1\\u20d4-\\u20d7\\u20e7-\\u20e9\\u2126\\u212a-\\u212b\\u2329-\\u232a\\u2adc\\u302b-\\u302c\\uaab2-\\uaab3\\uf900-\\ufa0d\\ufa10\\ufa12\\ufa15-\\ufa1e\\ufa20\\ufa22\\ufa25-\\ufa26\\ufa2a-\\ufa2d\\ufa30-\\ufa6d\\ufa70-\\ufad9\\ufb1d\\ufb1f\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufb4e\\ufff0-\\uffff]/g,\n    extraLookup; // This may be quite slow, so let's delay until user actually uses bad\n// characters.\n\nvar unrollLookup = function unrollLookup(escapable) {\n  var i;\n  var unrolled = {};\n  var c = [];\n\n  for (i = 0; i < 65536; i++) {\n    c.push(String.fromCharCode(i));\n  }\n\n  escapable.lastIndex = 0;\n  c.join('').replace(escapable, function (a) {\n    unrolled[a] = \"\\\\u\" + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n    return '';\n  });\n  escapable.lastIndex = 0;\n  return unrolled;\n}; // Quote string, also taking care of unicode characters that browsers\n// often break. Especially, take care of unicode surrogates:\n// http://en.wikipedia.org/wiki/Mapping_of_Unicode_characters#Surrogates\n\n\nmodule.exports = {\n  quote: function quote(string) {\n    var quoted = JSON.stringify(string); // In most cases this should be very fast and good enough.\n\n    extraEscapable.lastIndex = 0;\n\n    if (!extraEscapable.test(quoted)) {\n      return quoted;\n    }\n\n    if (!extraLookup) {\n      extraLookup = unrollLookup(extraEscapable);\n    }\n\n    return quoted.replace(extraEscapable, function (a) {\n      return extraLookup[a];\n    });\n  }\n};", "map": {"version": 3, "names": ["extraEscapable", "extraLookup", "unrollLookup", "escapable", "i", "unrolled", "c", "push", "String", "fromCharCode", "lastIndex", "join", "replace", "a", "charCodeAt", "toString", "slice", "module", "exports", "quote", "string", "quoted", "JSON", "stringify", "test"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/escape.js"], "sourcesContent": ["'use strict';\n\n// Some extra characters that Chrome gets wrong, and substitutes with\n// something else on the wire.\n// eslint-disable-next-line no-control-regex, no-misleading-character-class\nvar extraEscapable = /[\\x00-\\x1f\\ud800-\\udfff\\ufffe\\uffff\\u0300-\\u0333\\u033d-\\u0346\\u034a-\\u034c\\u0350-\\u0352\\u0357-\\u0358\\u035c-\\u0362\\u0374\\u037e\\u0387\\u0591-\\u05af\\u05c4\\u0610-\\u0617\\u0653-\\u0654\\u0657-\\u065b\\u065d-\\u065e\\u06df-\\u06e2\\u06eb-\\u06ec\\u0730\\u0732-\\u0733\\u0735-\\u0736\\u073a\\u073d\\u073f-\\u0741\\u0743\\u0745\\u0747\\u07eb-\\u07f1\\u0951\\u0958-\\u095f\\u09dc-\\u09dd\\u09df\\u0a33\\u0a36\\u0a59-\\u0a5b\\u0a5e\\u0b5c-\\u0b5d\\u0e38-\\u0e39\\u0f43\\u0f4d\\u0f52\\u0f57\\u0f5c\\u0f69\\u0f72-\\u0f76\\u0f78\\u0f80-\\u0f83\\u0f93\\u0f9d\\u0fa2\\u0fa7\\u0fac\\u0fb9\\u1939-\\u193a\\u1a17\\u1b6b\\u1cda-\\u1cdb\\u1dc0-\\u1dcf\\u1dfc\\u1dfe\\u1f71\\u1f73\\u1f75\\u1f77\\u1f79\\u1f7b\\u1f7d\\u1fbb\\u1fbe\\u1fc9\\u1fcb\\u1fd3\\u1fdb\\u1fe3\\u1feb\\u1fee-\\u1fef\\u1ff9\\u1ffb\\u1ffd\\u2000-\\u2001\\u20d0-\\u20d1\\u20d4-\\u20d7\\u20e7-\\u20e9\\u2126\\u212a-\\u212b\\u2329-\\u232a\\u2adc\\u302b-\\u302c\\uaab2-\\uaab3\\uf900-\\ufa0d\\ufa10\\ufa12\\ufa15-\\ufa1e\\ufa20\\ufa22\\ufa25-\\ufa26\\ufa2a-\\ufa2d\\ufa30-\\ufa6d\\ufa70-\\ufad9\\ufb1d\\ufb1f\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufb4e\\ufff0-\\uffff]/g\n  , extraLookup;\n\n// This may be quite slow, so let's delay until user actually uses bad\n// characters.\nvar unrollLookup = function(escapable) {\n  var i;\n  var unrolled = {};\n  var c = [];\n  for (i = 0; i < 65536; i++) {\n    c.push( String.fromCharCode(i) );\n  }\n  escapable.lastIndex = 0;\n  c.join('').replace(escapable, function(a) {\n    unrolled[ a ] = '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n    return '';\n  });\n  escapable.lastIndex = 0;\n  return unrolled;\n};\n\n// Quote string, also taking care of unicode characters that browsers\n// often break. Especially, take care of unicode surrogates:\n// http://en.wikipedia.org/wiki/Mapping_of_Unicode_characters#Surrogates\nmodule.exports = {\n  quote: function(string) {\n    var quoted = JSON.stringify(string);\n\n    // In most cases this should be very fast and good enough.\n    extraEscapable.lastIndex = 0;\n    if (!extraEscapable.test(quoted)) {\n      return quoted;\n    }\n\n    if (!extraLookup) {\n      extraLookup = unrollLookup(extraEscapable);\n    }\n\n    return quoted.replace(extraEscapable, function(a) {\n      return extraLookup[a];\n    });\n  }\n};\n"], "mappings": "AAAA,a,CAEA;AACA;AACA;;AACA,IAAIA,cAAc,GAAG,y/BAArB;AAAA,IACIC,WADJ,C,CAGA;AACA;;AACA,IAAIC,YAAY,GAAG,SAAfA,YAAe,CAASC,SAAT,EAAoB;EACrC,IAAIC,CAAJ;EACA,IAAIC,QAAQ,GAAG,EAAf;EACA,IAAIC,CAAC,GAAG,EAAR;;EACA,KAAKF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,KAAhB,EAAuBA,CAAC,EAAxB,EAA4B;IAC1BE,CAAC,CAACC,IAAF,CAAQC,MAAM,CAACC,YAAP,CAAoBL,CAApB,CAAR;EACD;;EACDD,SAAS,CAACO,SAAV,GAAsB,CAAtB;EACAJ,CAAC,CAACK,IAAF,CAAO,EAAP,EAAWC,OAAX,CAAmBT,SAAnB,EAA8B,UAASU,CAAT,EAAY;IACxCR,QAAQ,CAAEQ,CAAF,CAAR,GAAgB,QAAQ,CAAC,SAASA,CAAC,CAACC,UAAF,CAAa,CAAb,EAAgBC,QAAhB,CAAyB,EAAzB,CAAV,EAAwCC,KAAxC,CAA8C,CAAC,CAA/C,CAAxB;IACA,OAAO,EAAP;EACD,CAHD;EAIAb,SAAS,CAACO,SAAV,GAAsB,CAAtB;EACA,OAAOL,QAAP;AACD,CAdD,C,CAgBA;AACA;AACA;;;AACAY,MAAM,CAACC,OAAP,GAAiB;EACfC,KAAK,EAAE,eAASC,MAAT,EAAiB;IACtB,IAAIC,MAAM,GAAGC,IAAI,CAACC,SAAL,CAAeH,MAAf,CAAb,CADsB,CAGtB;;IACApB,cAAc,CAACU,SAAf,GAA2B,CAA3B;;IACA,IAAI,CAACV,cAAc,CAACwB,IAAf,CAAoBH,MAApB,CAAL,EAAkC;MAChC,OAAOA,MAAP;IACD;;IAED,IAAI,CAACpB,WAAL,EAAkB;MAChBA,WAAW,GAAGC,YAAY,CAACF,cAAD,CAA1B;IACD;;IAED,OAAOqB,MAAM,CAACT,OAAP,CAAeZ,cAAf,EAA+B,UAASa,CAAT,EAAY;MAChD,OAAOZ,WAAW,CAACY,CAAD,CAAlB;IACD,CAFM,CAAP;EAGD;AAjBc,CAAjB"}, "metadata": {}, "sourceType": "script"}