{"ast": null, "code": "var isFunction = require('./isFunction'),\n    isMasked = require('./_isMasked'),\n    isObject = require('./isObject'),\n    toSource = require('./_toSource');\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\n\n\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n/** Used to detect host constructors (Safari). */\n\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n/** Used for built-in method references. */\n\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n/** Used to resolve the decompiled source of functions. */\n\nvar funcToString = funcProto.toString;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/** Used to detect if a method is native. */\n\nvar reIsNative = RegExp('^' + funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&').replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$');\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\n\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nmodule.exports = baseIsNative;", "map": {"version": 3, "names": ["isFunction", "require", "isMasked", "isObject", "toSource", "reRegExpChar", "reIsHostCtor", "funcProto", "Function", "prototype", "objectProto", "Object", "funcToString", "toString", "hasOwnProperty", "reIsNative", "RegExp", "call", "replace", "baseIsNative", "value", "pattern", "test", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseIsNative.js"], "sourcesContent": ["var isFunction = require('./isFunction'),\n    isMasked = require('./_isMasked'),\n    isObject = require('./isObject'),\n    toSource = require('./_toSource');\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nmodule.exports = baseIsNative;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,cAAD,CAAxB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,aAAD,CADtB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,YAAD,CAFtB;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,aAAD,CAHtB;AAKA;AACA;AACA;AACA;;;AACA,IAAII,YAAY,GAAG,qBAAnB;AAEA;;AACA,IAAIC,YAAY,GAAG,6BAAnB;AAEA;;AACA,IAAIC,SAAS,GAAGC,QAAQ,CAACC,SAAzB;AAAA,IACIC,WAAW,GAAGC,MAAM,CAACF,SADzB;AAGA;;AACA,IAAIG,YAAY,GAAGL,SAAS,CAACM,QAA7B;AAEA;;AACA,IAAIC,cAAc,GAAGJ,WAAW,CAACI,cAAjC;AAEA;;AACA,IAAIC,UAAU,GAAGC,MAAM,CAAC,MACtBJ,YAAY,CAACK,IAAb,CAAkBH,cAAlB,EAAkCI,OAAlC,CAA0Cb,YAA1C,EAAwD,MAAxD,EACCa,OADD,CACS,wDADT,EACmE,OADnE,CADsB,GAEwD,GAFzD,CAAvB;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,YAAT,CAAsBC,KAAtB,EAA6B;EAC3B,IAAI,CAACjB,QAAQ,CAACiB,KAAD,CAAT,IAAoBlB,QAAQ,CAACkB,KAAD,CAAhC,EAAyC;IACvC,OAAO,KAAP;EACD;;EACD,IAAIC,OAAO,GAAGrB,UAAU,CAACoB,KAAD,CAAV,GAAoBL,UAApB,GAAiCT,YAA/C;EACA,OAAOe,OAAO,CAACC,IAAR,CAAalB,QAAQ,CAACgB,KAAD,CAArB,CAAP;AACD;;AAEDG,MAAM,CAACC,OAAP,GAAiBL,YAAjB"}, "metadata": {}, "sourceType": "script"}