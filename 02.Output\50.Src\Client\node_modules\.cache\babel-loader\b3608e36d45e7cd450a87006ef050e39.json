{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nimport { useEffect, useRef } from 'react';\nexport default function useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n\n    prevProps.current = props;\n  });\n}", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "useEffect", "useRef", "useWhyDidYouUpdate", "componentName", "props", "prevProps", "current", "allKeys", "keys", "changedProps_1", "for<PERSON>ach", "key", "is", "from", "to", "console", "log"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useWhyDidYouUpdate/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nimport { useEffect, useRef } from 'react';\nexport default function useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n    prevProps.current = props;\n  });\n}"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,SAASO,SAAT,EAAoBC,MAApB,QAAkC,OAAlC;AACA,eAAe,SAASC,kBAAT,CAA4BC,aAA5B,EAA2CC,KAA3C,EAAkD;EAC/D,IAAIC,SAAS,GAAGJ,MAAM,CAAC,EAAD,CAAtB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAIK,SAAS,CAACC,OAAd,EAAuB;MACrB,IAAIC,OAAO,GAAGpB,MAAM,CAACqB,IAAP,CAAYtB,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKmB,SAAS,CAACC,OAAf,CAAT,EAAkCF,KAAlC,CAApB,CAAd;MACA,IAAIK,cAAc,GAAG,EAArB;MACAF,OAAO,CAACG,OAAR,CAAgB,UAAUC,GAAV,EAAe;QAC7B,IAAI,CAACxB,MAAM,CAACyB,EAAP,CAAUP,SAAS,CAACC,OAAV,CAAkBK,GAAlB,CAAV,EAAkCP,KAAK,CAACO,GAAD,CAAvC,CAAL,EAAoD;UAClDF,cAAc,CAACE,GAAD,CAAd,GAAsB;YACpBE,IAAI,EAAER,SAAS,CAACC,OAAV,CAAkBK,GAAlB,CADc;YAEpBG,EAAE,EAAEV,KAAK,CAACO,GAAD;UAFW,CAAtB;QAID;MACF,CAPD;;MAQA,IAAIxB,MAAM,CAACqB,IAAP,CAAYC,cAAZ,EAA4Bf,MAAhC,EAAwC;QACtCqB,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCb,aAApC,EAAmDM,cAAnD;MACD;IACF;;IACDJ,SAAS,CAACC,OAAV,GAAoBF,KAApB;EACD,CAjBQ,CAAT;AAkBD"}, "metadata": {}, "sourceType": "module"}