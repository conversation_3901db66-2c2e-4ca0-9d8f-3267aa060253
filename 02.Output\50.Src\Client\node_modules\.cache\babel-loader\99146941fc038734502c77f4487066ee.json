{"ast": null, "code": "var root = require('./_root');\n/** Built-in value references. */\n\n\nvar Uint8Array = root.Uint8Array;\nmodule.exports = Uint8Array;", "map": {"version": 3, "names": ["root", "require", "Uint8Array", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_Uint8Array.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Built-in value references. */\nvar Uint8Array = root.Uint8Array;\n\nmodule.exports = Uint8Array;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAD,CAAlB;AAEA;;;AACA,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAtB;AAEAC,MAAM,CAACC,OAAP,GAAiBF,UAAjB"}, "metadata": {}, "sourceType": "script"}