{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\n\nfunction emptyFunctionWithReset() {}\n\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function () {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n\n    var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n    err.name = 'Invariant Violation';\n    throw err;\n  }\n\n  ;\n  shim.isRequired = shim;\n\n  function getShim() {\n    return shim;\n  }\n\n  ; // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n  ReactPropTypes.PropTypes = ReactPropTypes;\n  return ReactPropTypes;\n};", "map": {"version": 3, "names": ["ReactPropTypesSecret", "require", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "module", "exports", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "Error", "name", "isRequired", "getShim", "ReactPropTypes", "array", "bigint", "bool", "func", "number", "object", "string", "symbol", "any", "arrayOf", "element", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/prop-types/factoryWithThrowingShims.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,4BAAD,CAAlC;;AAEA,SAASC,aAAT,GAAyB,CAAE;;AAC3B,SAASC,sBAAT,GAAkC,CAAE;;AACpCA,sBAAsB,CAACC,iBAAvB,GAA2CF,aAA3C;;AAEAG,MAAM,CAACC,OAAP,GAAiB,YAAW;EAC1B,SAASC,IAAT,CAAcC,KAAd,EAAqBC,QAArB,EAA+BC,aAA/B,EAA8CC,QAA9C,EAAwDC,YAAxD,EAAsEC,MAAtE,EAA8E;IAC5E,IAAIA,MAAM,KAAKb,oBAAf,EAAqC;MACnC;MACA;IACD;;IACD,IAAIc,GAAG,GAAG,IAAIC,KAAJ,CACR,yFACA,+CADA,GAEA,gDAHQ,CAAV;IAKAD,GAAG,CAACE,IAAJ,GAAW,qBAAX;IACA,MAAMF,GAAN;EACD;;EAAA;EACDP,IAAI,CAACU,UAAL,GAAkBV,IAAlB;;EACA,SAASW,OAAT,GAAmB;IACjB,OAAOX,IAAP;EACD;;EAAA,CAjByB,CAkB1B;EACA;;EACA,IAAIY,cAAc,GAAG;IACnBC,KAAK,EAAEb,IADY;IAEnBc,MAAM,EAAEd,IAFW;IAGnBe,IAAI,EAAEf,IAHa;IAInBgB,IAAI,EAAEhB,IAJa;IAKnBiB,MAAM,EAAEjB,IALW;IAMnBkB,MAAM,EAAElB,IANW;IAOnBmB,MAAM,EAAEnB,IAPW;IAQnBoB,MAAM,EAAEpB,IARW;IAUnBqB,GAAG,EAAErB,IAVc;IAWnBsB,OAAO,EAAEX,OAXU;IAYnBY,OAAO,EAAEvB,IAZU;IAanBwB,WAAW,EAAExB,IAbM;IAcnByB,UAAU,EAAEd,OAdO;IAenBe,IAAI,EAAE1B,IAfa;IAgBnB2B,QAAQ,EAAEhB,OAhBS;IAiBnBiB,KAAK,EAAEjB,OAjBY;IAkBnBkB,SAAS,EAAElB,OAlBQ;IAmBnBmB,KAAK,EAAEnB,OAnBY;IAoBnBoB,KAAK,EAAEpB,OApBY;IAsBnBqB,cAAc,EAAEpC,sBAtBG;IAuBnBC,iBAAiB,EAAEF;EAvBA,CAArB;EA0BAiB,cAAc,CAACqB,SAAf,GAA2BrB,cAA3B;EAEA,OAAOA,cAAP;AACD,CAjDD"}, "metadata": {}, "sourceType": "script"}