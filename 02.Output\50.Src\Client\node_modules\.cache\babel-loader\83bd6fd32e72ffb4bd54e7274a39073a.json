{"ast": null, "code": "/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n\n  return array;\n}\n\nmodule.exports = arrayPush;", "map": {"version": 3, "names": ["arrayPush", "array", "values", "index", "length", "offset", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_arrayPush.js"], "sourcesContent": ["/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\nmodule.exports = arrayPush;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,KAAnB,EAA0BC,MAA1B,EAAkC;EAChC,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGF,MAAM,CAACE,MADpB;EAAA,IAEIC,MAAM,GAAGJ,KAAK,CAACG,MAFnB;;EAIA,OAAO,EAAED,KAAF,GAAUC,MAAjB,EAAyB;IACvBH,KAAK,CAACI,MAAM,GAAGF,KAAV,CAAL,GAAwBD,MAAM,CAACC,KAAD,CAA9B;EACD;;EACD,OAAOF,KAAP;AACD;;AAEDK,MAAM,CAACC,OAAP,GAAiBP,SAAjB"}, "metadata": {}, "sourceType": "script"}