{"ast": null, "code": "import { useRef } from 'react';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\nimport { isObject } from '../utils'; // k:v 原对象:代理过的对象\n\nvar proxyMap = new WeakMap(); // k:v 代理过的对象:原对象\n\nvar rawMap = new WeakMap();\n\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal); // 添加缓存 防止重新构建proxy\n\n  if (existingProxy) {\n    return existingProxy;\n  } // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n\n\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n\n  var proxy = new Proxy(initialVal, {\n    get: function get(target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      return isObject(res) ? observer(res, cb) : Reflect.get(target, key);\n    },\n    set: function set(target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function deleteProperty(target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\n\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\n\nexport default useReactive;", "map": {"version": 3, "names": ["useRef", "useCreation", "useUpdate", "isObject", "proxyMap", "WeakMap", "rawMap", "observer", "initialVal", "cb", "existingProxy", "get", "has", "proxy", "Proxy", "target", "key", "receiver", "res", "Reflect", "set", "val", "ret", "deleteProperty", "useReactive", "initialState", "update", "stateRef", "state", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useReactive/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\nimport { isObject } from '../utils';\n// k:v 原对象:代理过的对象\nvar proxyMap = new WeakMap();\n// k:v 代理过的对象:原对象\nvar rawMap = new WeakMap();\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal);\n  // 添加缓存 防止重新构建proxy\n  if (existingProxy) {\n    return existingProxy;\n  }\n  // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n  var proxy = new Proxy(initialVal, {\n    get: function get(target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      return isObject(res) ? observer(res, cb) : Reflect.get(target, key);\n    },\n    set: function set(target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function deleteProperty(target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\nexport default useReactive;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,WAAP,MAAwB,gBAAxB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,UAAzB,C,CACA;;AACA,IAAIC,QAAQ,GAAG,IAAIC,OAAJ,EAAf,C,CACA;;AACA,IAAIC,MAAM,GAAG,IAAID,OAAJ,EAAb;;AACA,SAASE,QAAT,CAAkBC,UAAlB,EAA8BC,EAA9B,EAAkC;EAChC,IAAIC,aAAa,GAAGN,QAAQ,CAACO,GAAT,CAAaH,UAAb,CAApB,CADgC,CAEhC;;EACA,IAAIE,aAAJ,EAAmB;IACjB,OAAOA,aAAP;EACD,CAL+B,CAMhC;EACA;;;EACA,IAAIJ,MAAM,CAACM,GAAP,CAAWJ,UAAX,CAAJ,EAA4B;IAC1B,OAAOA,UAAP;EACD;;EACD,IAAIK,KAAK,GAAG,IAAIC,KAAJ,CAAUN,UAAV,EAAsB;IAChCG,GAAG,EAAE,SAASA,GAAT,CAAaI,MAAb,EAAqBC,GAArB,EAA0BC,QAA1B,EAAoC;MACvC,IAAIC,GAAG,GAAGC,OAAO,CAACR,GAAR,CAAYI,MAAZ,EAAoBC,GAApB,EAAyBC,QAAzB,CAAV;MACA,OAAOd,QAAQ,CAACe,GAAD,CAAR,GAAgBX,QAAQ,CAACW,GAAD,EAAMT,EAAN,CAAxB,GAAoCU,OAAO,CAACR,GAAR,CAAYI,MAAZ,EAAoBC,GAApB,CAA3C;IACD,CAJ+B;IAKhCI,GAAG,EAAE,SAASA,GAAT,CAAaL,MAAb,EAAqBC,GAArB,EAA0BK,GAA1B,EAA+B;MAClC,IAAIC,GAAG,GAAGH,OAAO,CAACC,GAAR,CAAYL,MAAZ,EAAoBC,GAApB,EAAyBK,GAAzB,CAAV;MACAZ,EAAE;MACF,OAAOa,GAAP;IACD,CAT+B;IAUhCC,cAAc,EAAE,SAASA,cAAT,CAAwBR,MAAxB,EAAgCC,GAAhC,EAAqC;MACnD,IAAIM,GAAG,GAAGH,OAAO,CAACI,cAAR,CAAuBR,MAAvB,EAA+BC,GAA/B,CAAV;MACAP,EAAE;MACF,OAAOa,GAAP;IACD;EAd+B,CAAtB,CAAZ;EAgBAlB,QAAQ,CAACgB,GAAT,CAAaZ,UAAb,EAAyBK,KAAzB;EACAP,MAAM,CAACc,GAAP,CAAWP,KAAX,EAAkBL,UAAlB;EACA,OAAOK,KAAP;AACD;;AACD,SAASW,WAAT,CAAqBC,YAArB,EAAmC;EACjC,IAAIC,MAAM,GAAGxB,SAAS,EAAtB;EACA,IAAIyB,QAAQ,GAAG3B,MAAM,CAACyB,YAAD,CAArB;EACA,IAAIG,KAAK,GAAG3B,WAAW,CAAC,YAAY;IAClC,OAAOM,QAAQ,CAACoB,QAAQ,CAACE,OAAV,EAAmB,YAAY;MAC5CH,MAAM;IACP,CAFc,CAAf;EAGD,CAJsB,EAIpB,EAJoB,CAAvB;EAKA,OAAOE,KAAP;AACD;;AACD,eAAeJ,WAAf"}, "metadata": {}, "sourceType": "module"}