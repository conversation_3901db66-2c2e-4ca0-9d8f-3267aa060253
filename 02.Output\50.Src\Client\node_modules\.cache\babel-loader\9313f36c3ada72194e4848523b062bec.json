{"ast": null, "code": "var ListCache = require('./_ListCache'),\n    Map = require('./_Map'),\n    MapCache = require('./_MapCache');\n/** Used as the size to enable large array optimizations. */\n\n\nvar LARGE_ARRAY_SIZE = 200;\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\n\nfunction stackSet(key, value) {\n  var data = this.__data__;\n\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n\n    if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n\n    data = this.__data__ = new MapCache(pairs);\n  }\n\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\nmodule.exports = stackSet;", "map": {"version": 3, "names": ["ListCache", "require", "Map", "MapCache", "LARGE_ARRAY_SIZE", "stackSet", "key", "value", "data", "__data__", "pairs", "length", "push", "size", "set", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_stackSet.js"], "sourcesContent": ["var ListCache = require('./_ListCache'),\n    Map = require('./_Map'),\n    MapCache = require('./_MapCache');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\nmodule.exports = stackSet;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,GAAG,GAAGD,OAAO,CAAC,QAAD,CADjB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,aAAD,CAFtB;AAIA;;;AACA,IAAIG,gBAAgB,GAAG,GAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,GAAlB,EAAuBC,KAAvB,EAA8B;EAC5B,IAAIC,IAAI,GAAG,KAAKC,QAAhB;;EACA,IAAID,IAAI,YAAYR,SAApB,EAA+B;IAC7B,IAAIU,KAAK,GAAGF,IAAI,CAACC,QAAjB;;IACA,IAAI,CAACP,GAAD,IAASQ,KAAK,CAACC,MAAN,GAAeP,gBAAgB,GAAG,CAA/C,EAAmD;MACjDM,KAAK,CAACE,IAAN,CAAW,CAACN,GAAD,EAAMC,KAAN,CAAX;MACA,KAAKM,IAAL,GAAY,EAAEL,IAAI,CAACK,IAAnB;MACA,OAAO,IAAP;IACD;;IACDL,IAAI,GAAG,KAAKC,QAAL,GAAgB,IAAIN,QAAJ,CAAaO,KAAb,CAAvB;EACD;;EACDF,IAAI,CAACM,GAAL,CAASR,GAAT,EAAcC,KAAd;EACA,KAAKM,IAAL,GAAYL,IAAI,CAACK,IAAjB;EACA,OAAO,IAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBX,QAAjB"}, "metadata": {}, "sourceType": "script"}