{"ast": null, "code": "import { useRef } from 'react';\n\nvar useRetryPlugin = function useRetryPlugin(fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n      retryCount = _a.retryCount;\n  var timerRef = useRef();\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n\n  if (!retryCount) {\n    return {};\n  }\n\n  return {\n    onBefore: function onBefore() {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n\n      triggerByRetry.current = false;\n\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function onSuccess() {\n      countRef.current = 0;\n    },\n    onError: function onError() {\n      countRef.current += 1;\n\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function onCancel() {\n      countRef.current = 0;\n\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\n\nexport default useRetryPlugin;", "map": {"version": 3, "names": ["useRef", "useRetryPlugin", "fetchInstance", "_a", "retryInterval", "retryCount", "timerRef", "countRef", "triggerByRetry", "onBefore", "current", "clearTimeout", "onSuccess", "onError", "timeout", "Math", "min", "pow", "setTimeout", "refresh", "onCancel"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nvar useRetryPlugin = function useRetryPlugin(fetchInstance, _a) {\n  var retryInterval = _a.retryInterval,\n    retryCount = _a.retryCount;\n  var timerRef = useRef();\n  var countRef = useRef(0);\n  var triggerByRetry = useRef(false);\n  if (!retryCount) {\n    return {};\n  }\n  return {\n    onBefore: function onBefore() {\n      if (!triggerByRetry.current) {\n        countRef.current = 0;\n      }\n      triggerByRetry.current = false;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    },\n    onSuccess: function onSuccess() {\n      countRef.current = 0;\n    },\n    onError: function onError() {\n      countRef.current += 1;\n      if (retryCount === -1 || countRef.current <= retryCount) {\n        // Exponential backoff\n        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1000 * Math.pow(2, countRef.current), 30000);\n        timerRef.current = setTimeout(function () {\n          triggerByRetry.current = true;\n          fetchInstance.refresh();\n        }, timeout);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function onCancel() {\n      countRef.current = 0;\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    }\n  };\n};\nexport default useRetryPlugin;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,aAAxB,EAAuCC,EAAvC,EAA2C;EAC9D,IAAIC,aAAa,GAAGD,EAAE,CAACC,aAAvB;EAAA,IACEC,UAAU,GAAGF,EAAE,CAACE,UADlB;EAEA,IAAIC,QAAQ,GAAGN,MAAM,EAArB;EACA,IAAIO,QAAQ,GAAGP,MAAM,CAAC,CAAD,CAArB;EACA,IAAIQ,cAAc,GAAGR,MAAM,CAAC,KAAD,CAA3B;;EACA,IAAI,CAACK,UAAL,EAAiB;IACf,OAAO,EAAP;EACD;;EACD,OAAO;IACLI,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5B,IAAI,CAACD,cAAc,CAACE,OAApB,EAA6B;QAC3BH,QAAQ,CAACG,OAAT,GAAmB,CAAnB;MACD;;MACDF,cAAc,CAACE,OAAf,GAAyB,KAAzB;;MACA,IAAIJ,QAAQ,CAACI,OAAb,EAAsB;QACpBC,YAAY,CAACL,QAAQ,CAACI,OAAV,CAAZ;MACD;IACF,CATI;IAULE,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9BL,QAAQ,CAACG,OAAT,GAAmB,CAAnB;IACD,CAZI;IAaLG,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1BN,QAAQ,CAACG,OAAT,IAAoB,CAApB;;MACA,IAAIL,UAAU,KAAK,CAAC,CAAhB,IAAqBE,QAAQ,CAACG,OAAT,IAAoBL,UAA7C,EAAyD;QACvD;QACA,IAAIS,OAAO,GAAGV,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqDA,aAArD,GAAqEW,IAAI,CAACC,GAAL,CAAS,OAAOD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYV,QAAQ,CAACG,OAArB,CAAhB,EAA+C,KAA/C,CAAnF;QACAJ,QAAQ,CAACI,OAAT,GAAmBQ,UAAU,CAAC,YAAY;UACxCV,cAAc,CAACE,OAAf,GAAyB,IAAzB;UACAR,aAAa,CAACiB,OAAd;QACD,CAH4B,EAG1BL,OAH0B,CAA7B;MAID,CAPD,MAOO;QACLP,QAAQ,CAACG,OAAT,GAAmB,CAAnB;MACD;IACF,CAzBI;IA0BLU,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5Bb,QAAQ,CAACG,OAAT,GAAmB,CAAnB;;MACA,IAAIJ,QAAQ,CAACI,OAAb,EAAsB;QACpBC,YAAY,CAACL,QAAQ,CAACI,OAAV,CAAZ;MACD;IACF;EA/BI,CAAP;AAiCD,CA1CD;;AA2CA,eAAeT,cAAf"}, "metadata": {}, "sourceType": "module"}