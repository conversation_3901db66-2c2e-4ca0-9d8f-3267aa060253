{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{getHexColor,getTextOrDisplayColor,replaceWithHtmlSpace}from'../../utils/Util.js';import ScaleBlock from'./ScaleBlock';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";/**\r\n * 色/背景色/文字列の情報を従い、表示する\r\n * @module Cell\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */var Cell=function Cell(props){var style={};if(props.text_color){style.color=getTextOrDisplayColor(props);}if(props.background_color){style.backgroundColor=getHexColor(props.background_color);}var className=props.className;var refinedText=replaceWithHtmlSpace((props===null||props===void 0?void 0:props.display_text)||(props===null||props===void 0?void 0:props.text));return/*#__PURE__*/_jsxs(\"div\",{className:className,style:style,children:[props.scale&&/*#__PURE__*/_jsx(ScaleBlock,_objectSpread({},props)),!props.scale&&/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:refinedText}}),// Scaleの計算は、Unitの文字を含めてない。画面Layoutによって、このUnitを含めて、ロジックを見直す必要\nprops.unit&&/*#__PURE__*/_jsx(\"span\",{style:props.unitStyle,children:props.unit})]});};export default Cell;", "map": {"version": 3, "names": ["React", "getHexColor", "getTextOrDisplayColor", "replaceWithHtmlSpace", "ScaleBlock", "Cell", "props", "style", "text_color", "color", "background_color", "backgroundColor", "className", "refinedText", "display_text", "text", "scale", "__html", "unit", "unitStyle"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/Cell.js"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n  getHexColor,\r\n  getTextOrDisplayColor,\r\n  replaceWithHtmlSpace,\r\n} from '../../utils/Util.js';\r\nimport PropTypes from 'prop-types';\r\nimport ScaleBlock from './ScaleBlock';\r\n\r\nconst propTypes = {\r\n  scale: PropTypes.bool,\r\n  unit: PropTypes.string,\r\n  unitStyle: PropTypes.object,\r\n  className: PropTypes.string,\r\n  display_text: PropTypes.string,\r\n  text: PropTypes.string,\r\n  text_color: PropTypes.string,\r\n  background_color: PropTypes.string,\r\n};\r\n\r\n/**\r\n * 色/背景色/文字列の情報を従い、表示する\r\n * @module Cell\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\r\nconst Cell = (props) => {\r\n  const style = {};\r\n  if (props.text_color) {\r\n    style.color = getTextOrDisplayColor(props);\r\n  }\r\n\r\n  if (props.background_color) {\r\n    style.backgroundColor = getHexColor(props.background_color);\r\n  }\r\n\r\n  let className = props.className;\r\n  let refinedText = replaceWithHtmlSpace(props?.display_text || props?.text);\r\n\r\n  return (\r\n    <div className={className} style={style}>\r\n      {props.scale && <ScaleBlock {...props}></ScaleBlock>}\r\n      {!props.scale && (\r\n        <span dangerouslySetInnerHTML={{ __html: refinedText }}></span>\r\n      )}\r\n      {\r\n        // Scaleの計算は、Unitの文字を含めてない。画面Layoutによって、このUnitを含めて、ロジックを見直す必要\r\n        props.unit && <span style={props.unitStyle}>{props.unit}</span>\r\n      }\r\n    </div>\r\n  );\r\n};\r\n\r\nCell.propTypes = propTypes;\r\nexport default Cell;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,OACEC,WADF,CAEEC,qBAFF,CAGEC,oBAHF,KAIO,qBAJP,CAMA,MAAOC,WAAP,KAAuB,cAAvB,C,wFAaA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,KAAI,CAAG,QAAPA,KAAO,CAACC,KAAD,CAAW,CACtB,GAAMC,MAAK,CAAG,EAAd,CACA,GAAID,KAAK,CAACE,UAAV,CAAsB,CACpBD,KAAK,CAACE,KAAN,CAAcP,qBAAqB,CAACI,KAAD,CAAnC,CACD,CAED,GAAIA,KAAK,CAACI,gBAAV,CAA4B,CAC1BH,KAAK,CAACI,eAAN,CAAwBV,WAAW,CAACK,KAAK,CAACI,gBAAP,CAAnC,CACD,CAED,GAAIE,UAAS,CAAGN,KAAK,CAACM,SAAtB,CACA,GAAIC,YAAW,CAAGV,oBAAoB,CAAC,CAAAG,KAAK,OAAL,EAAAA,KAAK,SAAL,QAAAA,KAAK,CAAEQ,YAAP,IAAuBR,KAAvB,SAAuBA,KAAvB,iBAAuBA,KAAK,CAAES,IAA9B,CAAD,CAAtC,CAEA,mBACE,aAAK,SAAS,CAAEH,SAAhB,CAA2B,KAAK,CAAEL,KAAlC,WACGD,KAAK,CAACU,KAAN,eAAe,KAAC,UAAD,kBAAgBV,KAAhB,EADlB,CAEG,CAACA,KAAK,CAACU,KAAP,eACC,aAAM,uBAAuB,CAAE,CAAEC,MAAM,CAAEJ,WAAV,CAA/B,EAHJ,CAMI;AACAP,KAAK,CAACY,IAAN,eAAc,aAAM,KAAK,CAAEZ,KAAK,CAACa,SAAnB,UAA+Bb,KAAK,CAACY,IAArC,EAPlB,GADF,CAYD,CAzBD,CA4BA,cAAeb,KAAf"}, "metadata": {}, "sourceType": "module"}