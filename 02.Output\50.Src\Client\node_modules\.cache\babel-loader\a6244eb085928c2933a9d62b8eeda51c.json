{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\n\nfunction useEventTarget(options) {\n  var _a = options || {},\n      initialValue = _a.initialValue,\n      transformer = _a.transformer;\n\n  var _b = __read(useState(initialValue), 2),\n      value = _b[0],\n      setValue = _b[1];\n\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    } // no transformer => U and T should be the same\n\n\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\n\nexport default useEventTarget;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useCallback", "useState", "useLatest", "isFunction", "useEventTarget", "options", "_a", "initialValue", "transformer", "_b", "setValue", "transformerRef", "reset", "onChange", "_value", "target", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useEventTarget/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nfunction useEventTarget(options) {\n  var _a = options || {},\n    initialValue = _a.initialValue,\n    transformer = _a.transformer;\n  var _b = __read(useState(initialValue), 2),\n    value = _b[0],\n    setValue = _b[1];\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    }\n    // no transformer => U and T should be the same\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\nexport default useEventTarget;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,WAAT,EAAsBC,QAAtB,QAAsC,OAAtC;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,UAAT,QAA2B,UAA3B;;AACA,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;EAC/B,IAAIC,EAAE,GAAGD,OAAO,IAAI,EAApB;EAAA,IACEE,YAAY,GAAGD,EAAE,CAACC,YADpB;EAAA,IAEEC,WAAW,GAAGF,EAAE,CAACE,WAFnB;;EAGA,IAAIC,EAAE,GAAGzB,MAAM,CAACiB,QAAQ,CAACM,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACET,KAAK,GAAGW,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEC,QAAQ,GAAGD,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIE,cAAc,GAAGT,SAAS,CAACM,WAAD,CAA9B;EACA,IAAII,KAAK,GAAGZ,WAAW,CAAC,YAAY;IAClC,OAAOU,QAAQ,CAACH,YAAD,CAAf;EACD,CAFsB,EAEpB,EAFoB,CAAvB;EAGA,IAAIM,QAAQ,GAAGb,WAAW,CAAC,UAAUN,CAAV,EAAa;IACtC,IAAIoB,MAAM,GAAGpB,CAAC,CAACqB,MAAF,CAASjB,KAAtB;;IACA,IAAIK,UAAU,CAACQ,cAAc,CAACK,OAAhB,CAAd,EAAwC;MACtC,OAAON,QAAQ,CAACC,cAAc,CAACK,OAAf,CAAuBF,MAAvB,CAAD,CAAf;IACD,CAJqC,CAKtC;;;IACA,OAAOJ,QAAQ,CAACI,MAAD,CAAf;EACD,CAPyB,EAOvB,EAPuB,CAA1B;EAQA,OAAO,CAAChB,KAAD,EAAQ;IACbe,QAAQ,EAAEA,QADG;IAEbD,KAAK,EAAEA;EAFM,CAAR,CAAP;AAID;;AACD,eAAeR,cAAf"}, "metadata": {}, "sourceType": "module"}