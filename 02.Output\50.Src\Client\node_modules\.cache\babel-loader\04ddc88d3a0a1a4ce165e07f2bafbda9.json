{"ast": null, "code": "'use strict';\n\nimport bind from './helpers/bind.js'; // utils is a library of generic helper functions non-specific to axios\n\nvar toString = Object.prototype.toString;\nvar getPrototypeOf = Object.getPrototypeOf;\n\nvar kindOf = function (cache) {\n  return function (thing) {\n    var str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n  };\n}(Object.create(null));\n\nvar kindOfTest = function kindOfTest(type) {\n  type = type.toLowerCase();\n  return function (thing) {\n    return kindOf(thing) === type;\n  };\n};\n\nvar typeOfTest = function typeOfTest(type) {\n  return function (thing) {\n    return typeof thing === type;\n  };\n};\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */\n\n\nvar isArray = Array.isArray;\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */\n\nvar isUndefined = typeOfTest('undefined');\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\n\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\n\n\nvar isArrayBuffer = kindOfTest('ArrayBuffer');\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\n\nfunction isArrayBufferView(val) {\n  var result;\n\n  if (typeof ArrayBuffer !== 'undefined' && ArrayBuffer.isView) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = val && val.buffer && isArrayBuffer(val.buffer);\n  }\n\n  return result;\n}\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */\n\n\nvar isString = typeOfTest('string');\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\n\nvar isFunction = typeOfTest('function');\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */\n\nvar isNumber = typeOfTest('number');\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */\n\nvar isObject = function isObject(thing) {\n  return thing !== null && typeof thing === 'object';\n};\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */\n\n\nvar isBoolean = function isBoolean(thing) {\n  return thing === true || thing === false;\n};\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */\n\n\nvar isPlainObject = function isPlainObject(val) {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n\n  var prototype = getPrototypeOf(val);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);\n};\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */\n\n\nvar isDate = kindOfTest('Date');\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\n\nvar isFile = kindOfTest('File');\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */\n\nvar isBlob = kindOfTest('Blob');\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\n\nvar isFileList = kindOfTest('FileList');\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */\n\nvar isStream = function isStream(val) {\n  return isObject(val) && isFunction(val.pipe);\n};\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */\n\n\nvar isFormData = function isFormData(thing) {\n  var pattern = '[object FormData]';\n  return thing && (typeof FormData === 'function' && thing instanceof FormData || toString.call(thing) === pattern || isFunction(thing.toString) && thing.toString() === pattern);\n};\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\n\n\nvar isURLSearchParams = kindOfTest('URLSearchParams');\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */\n\nvar trim = function trim(str) {\n  return str.trim ? str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n};\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {void}\n */\n\n\nfunction forEach(obj, fn) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref$allOwnKeys = _ref.allOwnKeys,\n      allOwnKeys = _ref$allOwnKeys === void 0 ? false : _ref$allOwnKeys;\n\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  var i;\n  var l; // Force an array if not already something iterable\n\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    var keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n    var len = keys.length;\n    var key;\n\n    for (i = 0; i < len; i++) {\n      key = keys[i];\n      fn.call(null, obj[key], key, obj);\n    }\n  }\n}\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */\n\n\nfunction\n  /* obj1, obj2, obj3, ... */\nmerge() {\n  var result = {};\n\n  var assignValue = function assignValue(val, key) {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  };\n\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    arguments[i] && forEach(arguments[i], assignValue);\n  }\n\n  return result;\n}\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */\n\n\nvar extend = function extend(a, b, thisArg) {\n  var _ref2 = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {},\n      allOwnKeys = _ref2.allOwnKeys;\n\n  forEach(b, function (val, key) {\n    if (thisArg && isFunction(val)) {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  }, {\n    allOwnKeys: allOwnKeys\n  });\n  return a;\n};\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */\n\n\nvar stripBOM = function stripBOM(content) {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n\n  return content;\n};\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */\n\n\nvar inherits = function inherits(constructor, superConstructor, props, descriptors) {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  Object.defineProperty(constructor, 'super', {\n    value: superConstructor.prototype\n  });\n  props && Object.assign(constructor.prototype, props);\n};\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */\n\n\nvar toFlatObject = function toFlatObject(sourceObj, destObj, filter, propFilter) {\n  var props;\n  var i;\n  var prop;\n  var merged = {};\n  destObj = destObj || {}; // eslint-disable-next-line no-eq-null,eqeqeq\n\n  if (sourceObj == null) return destObj;\n\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n\n    while (i-- > 0) {\n      prop = props[i];\n\n      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n\n    sourceObj = filter !== false && getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n\n  return destObj;\n};\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */\n\n\nvar endsWith = function endsWith(str, searchString, position) {\n  str = String(str);\n\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n\n  position -= searchString.length;\n  var lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n};\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */\n\n\nvar toArray = function toArray(thing) {\n  if (!thing) return null;\n  if (isArray(thing)) return thing;\n  var i = thing.length;\n  if (!isNumber(i)) return null;\n  var arr = new Array(i);\n\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n\n  return arr;\n};\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */\n// eslint-disable-next-line func-names\n\n\nvar isTypedArray = function (TypedArray) {\n  // eslint-disable-next-line func-names\n  return function (thing) {\n    return TypedArray && thing instanceof TypedArray;\n  };\n}(typeof Uint8Array !== 'undefined' && getPrototypeOf(Uint8Array));\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */\n\n\nvar forEachEntry = function forEachEntry(obj, fn) {\n  var generator = obj && obj[Symbol.iterator];\n  var iterator = generator.call(obj);\n  var result;\n\n  while ((result = iterator.next()) && !result.done) {\n    var pair = result.value;\n    fn.call(obj, pair[0], pair[1]);\n  }\n};\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */\n\n\nvar matchAll = function matchAll(regExp, str) {\n  var matches;\n  var arr = [];\n\n  while ((matches = regExp.exec(str)) !== null) {\n    arr.push(matches);\n  }\n\n  return arr;\n};\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */\n\n\nvar isHTMLForm = kindOfTest('HTMLFormElement');\n\nvar toCamelCase = function toCamelCase(str) {\n  return str.toLowerCase().replace(/[_-\\s]([a-z\\d])(\\w*)/g, function replacer(m, p1, p2) {\n    return p1.toUpperCase() + p2;\n  });\n};\n/* Creating a function that will check if an object has a property. */\n\n\nvar hasOwnProperty = function (_ref3) {\n  var hasOwnProperty = _ref3.hasOwnProperty;\n  return function (obj, prop) {\n    return hasOwnProperty.call(obj, prop);\n  };\n}(Object.prototype);\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */\n\n\nvar isRegExp = kindOfTest('RegExp');\n\nvar reduceDescriptors = function reduceDescriptors(obj, reducer) {\n  var descriptors = Object.getOwnPropertyDescriptors(obj);\n  var reducedDescriptors = {};\n  forEach(descriptors, function (descriptor, name) {\n    if (reducer(descriptor, name, obj) !== false) {\n      reducedDescriptors[name] = descriptor;\n    }\n  });\n  Object.defineProperties(obj, reducedDescriptors);\n};\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */\n\n\nvar freezeMethods = function freezeMethods(obj) {\n  reduceDescriptors(obj, function (descriptor, name) {\n    var value = obj[name];\n    if (!isFunction(value)) return;\n    descriptor.enumerable = false;\n\n    if ('writable' in descriptor) {\n      descriptor.writable = false;\n      return;\n    }\n\n    if (!descriptor.set) {\n      descriptor.set = function () {\n        throw Error('Can not read-only method \\'' + name + '\\'');\n      };\n    }\n  });\n};\n\nvar toObjectSet = function toObjectSet(arrayOrString, delimiter) {\n  var obj = {};\n\n  var define = function define(arr) {\n    arr.forEach(function (value) {\n      obj[value] = true;\n    });\n  };\n\n  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n  return obj;\n};\n\nvar noop = function noop() {};\n\nvar toFiniteNumber = function toFiniteNumber(value, defaultValue) {\n  value = +value;\n  return Number.isFinite(value) ? value : defaultValue;\n};\n\nexport default {\n  isArray: isArray,\n  isArrayBuffer: isArrayBuffer,\n  isBuffer: isBuffer,\n  isFormData: isFormData,\n  isArrayBufferView: isArrayBufferView,\n  isString: isString,\n  isNumber: isNumber,\n  isBoolean: isBoolean,\n  isObject: isObject,\n  isPlainObject: isPlainObject,\n  isUndefined: isUndefined,\n  isDate: isDate,\n  isFile: isFile,\n  isBlob: isBlob,\n  isRegExp: isRegExp,\n  isFunction: isFunction,\n  isStream: isStream,\n  isURLSearchParams: isURLSearchParams,\n  isTypedArray: isTypedArray,\n  isFileList: isFileList,\n  forEach: forEach,\n  merge: merge,\n  extend: extend,\n  trim: trim,\n  stripBOM: stripBOM,\n  inherits: inherits,\n  toFlatObject: toFlatObject,\n  kindOf: kindOf,\n  kindOfTest: kindOfTest,\n  endsWith: endsWith,\n  toArray: toArray,\n  forEachEntry: forEachEntry,\n  matchAll: matchAll,\n  isHTMLForm: isHTMLForm,\n  hasOwnProperty: hasOwnProperty,\n  hasOwnProp: hasOwnProperty,\n  // an alias to avoid ESLint no-prototype-builtins detection\n  reduceDescriptors: reduceDescriptors,\n  freezeMethods: freezeMethods,\n  toObjectSet: toObjectSet,\n  toCamelCase: toCamelCase,\n  noop: noop,\n  toFiniteNumber: toFiniteNumber\n};", "map": {"version": 3, "names": ["bind", "toString", "Object", "prototype", "getPrototypeOf", "kindOf", "cache", "thing", "str", "call", "slice", "toLowerCase", "create", "kindOfTest", "type", "typeOfTest", "isArray", "Array", "isUndefined", "<PERSON><PERSON><PERSON><PERSON>", "val", "constructor", "isFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArrayBuffer<PERSON>iew", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "isString", "isNumber", "isObject", "isBoolean", "isPlainObject", "Symbol", "toStringTag", "iterator", "isDate", "isFile", "isBlob", "isFileList", "isStream", "pipe", "isFormData", "pattern", "FormData", "isURLSearchParams", "trim", "replace", "for<PERSON>ach", "obj", "fn", "allOwnKeys", "i", "l", "length", "keys", "getOwnPropertyNames", "len", "key", "merge", "assignValue", "arguments", "extend", "a", "b", "thisArg", "stripBOM", "content", "charCodeAt", "inherits", "superConstructor", "props", "descriptors", "defineProperty", "value", "assign", "toFlatObject", "sourceObj", "destObj", "filter", "propFilter", "prop", "merged", "endsWith", "searchString", "position", "String", "undefined", "lastIndex", "indexOf", "toArray", "arr", "isTypedArray", "TypedArray", "Uint8Array", "forEachEntry", "generator", "next", "done", "pair", "matchAll", "regExp", "matches", "exec", "push", "isHTMLForm", "toCamelCase", "replacer", "m", "p1", "p2", "toUpperCase", "hasOwnProperty", "isRegExp", "reduceDescriptors", "reducer", "getOwnPropertyDescriptors", "reducedDescriptors", "descriptor", "name", "defineProperties", "freezeMethods", "enumerable", "writable", "set", "Error", "toObjectSet", "arrayOrString", "delimiter", "define", "split", "noop", "toFiniteNumber", "defaultValue", "Number", "isFinite", "hasOwnProp"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/utils.js"], "sourcesContent": ["'use strict';\n\nimport bind from './helpers/bind.js';\n\n// utils is a library of generic helper functions non-specific to axios\n\nconst {toString} = Object.prototype;\nconst {getPrototypeOf} = Object;\n\nconst kindOf = (cache => thing => {\n    const str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n})(Object.create(null));\n\nconst kindOfTest = (type) => {\n  type = type.toLowerCase();\n  return (thing) => kindOf(thing) === type\n}\n\nconst typeOfTest = type => thing => typeof thing === type;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */\nconst {isArray} = Array;\n\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nconst isUndefined = typeOfTest('undefined');\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nconst isArrayBuffer = kindOfTest('ArrayBuffer');\n\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  let result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (isArrayBuffer(val.buffer));\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */\nconst isString = typeOfTest('string');\n\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nconst isFunction = typeOfTest('function');\n\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */\nconst isNumber = typeOfTest('number');\n\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */\nconst isObject = (thing) => thing !== null && typeof thing === 'object';\n\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */\nconst isBoolean = thing => thing === true || thing === false;\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */\nconst isPlainObject = (val) => {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n\n  const prototype = getPrototypeOf(val);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */\nconst isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nconst isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nconst isStream = (val) => isObject(val) && isFunction(val.pipe);\n\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nconst isFormData = (thing) => {\n  const pattern = '[object FormData]';\n  return thing && (\n    (typeof FormData === 'function' && thing instanceof FormData) ||\n    toString.call(thing) === pattern ||\n    (isFunction(thing.toString) && thing.toString() === pattern)\n  );\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nconst isURLSearchParams = kindOfTest('URLSearchParams');\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */\nconst trim = (str) => str.trim ?\n  str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {void}\n */\nfunction forEach(obj, fn, {allOwnKeys = false} = {}) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  let i;\n  let l;\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n    const len = keys.length;\n    let key;\n\n    for (i = 0; i < len; i++) {\n      key = keys[i];\n      fn.call(null, obj[key], key, obj);\n    }\n  }\n}\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  const result = {};\n  const assignValue = (val, key) => {\n    if (isPlainObject(result[key]) && isPlainObject(val)) {\n      result[key] = merge(result[key], val);\n    } else if (isPlainObject(val)) {\n      result[key] = merge({}, val);\n    } else if (isArray(val)) {\n      result[key] = val.slice();\n    } else {\n      result[key] = val;\n    }\n  }\n\n  for (let i = 0, l = arguments.length; i < l; i++) {\n    arguments[i] && forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */\nconst extend = (a, b, thisArg, {allOwnKeys}= {}) => {\n  forEach(b, (val, key) => {\n    if (thisArg && isFunction(val)) {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  }, {allOwnKeys});\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */\nconst stripBOM = (content) => {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */\nconst inherits = (constructor, superConstructor, props, descriptors) => {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  Object.defineProperty(constructor, 'super', {\n    value: superConstructor.prototype\n  });\n  props && Object.assign(constructor.prototype, props);\n}\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */\nconst toFlatObject = (sourceObj, destObj, filter, propFilter) => {\n  let props;\n  let i;\n  let prop;\n  const merged = {};\n\n  destObj = destObj || {};\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  if (sourceObj == null) return destObj;\n\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = filter !== false && getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n\n  return destObj;\n}\n\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */\nconst endsWith = (str, searchString, position) => {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  const lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n}\n\n\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */\nconst toArray = (thing) => {\n  if (!thing) return null;\n  if (isArray(thing)) return thing;\n  let i = thing.length;\n  if (!isNumber(i)) return null;\n  const arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n}\n\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */\n// eslint-disable-next-line func-names\nconst isTypedArray = (TypedArray => {\n  // eslint-disable-next-line func-names\n  return thing => {\n    return TypedArray && thing instanceof TypedArray;\n  };\n})(typeof Uint8Array !== 'undefined' && getPrototypeOf(Uint8Array));\n\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */\nconst forEachEntry = (obj, fn) => {\n  const generator = obj && obj[Symbol.iterator];\n\n  const iterator = generator.call(obj);\n\n  let result;\n\n  while ((result = iterator.next()) && !result.done) {\n    const pair = result.value;\n    fn.call(obj, pair[0], pair[1]);\n  }\n}\n\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */\nconst matchAll = (regExp, str) => {\n  let matches;\n  const arr = [];\n\n  while ((matches = regExp.exec(str)) !== null) {\n    arr.push(matches);\n  }\n\n  return arr;\n}\n\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */\nconst isHTMLForm = kindOfTest('HTMLFormElement');\n\nconst toCamelCase = str => {\n  return str.toLowerCase().replace(/[_-\\s]([a-z\\d])(\\w*)/g,\n    function replacer(m, p1, p2) {\n      return p1.toUpperCase() + p2;\n    }\n  );\n};\n\n/* Creating a function that will check if an object has a property. */\nconst hasOwnProperty = (({hasOwnProperty}) => (obj, prop) => hasOwnProperty.call(obj, prop))(Object.prototype);\n\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */\nconst isRegExp = kindOfTest('RegExp');\n\nconst reduceDescriptors = (obj, reducer) => {\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  const reducedDescriptors = {};\n\n  forEach(descriptors, (descriptor, name) => {\n    if (reducer(descriptor, name, obj) !== false) {\n      reducedDescriptors[name] = descriptor;\n    }\n  });\n\n  Object.defineProperties(obj, reducedDescriptors);\n}\n\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */\n\nconst freezeMethods = (obj) => {\n  reduceDescriptors(obj, (descriptor, name) => {\n    const value = obj[name];\n\n    if (!isFunction(value)) return;\n\n    descriptor.enumerable = false;\n\n    if ('writable' in descriptor) {\n      descriptor.writable = false;\n      return;\n    }\n\n    if (!descriptor.set) {\n      descriptor.set = () => {\n        throw Error('Can not read-only method \\'' + name + '\\'');\n      };\n    }\n  });\n}\n\nconst toObjectSet = (arrayOrString, delimiter) => {\n  const obj = {};\n\n  const define = (arr) => {\n    arr.forEach(value => {\n      obj[value] = true;\n    });\n  }\n\n  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n\n  return obj;\n}\n\nconst noop = () => {}\n\nconst toFiniteNumber = (value, defaultValue) => {\n  value = +value;\n  return Number.isFinite(value) ? value : defaultValue;\n}\n\nexport default {\n  isArray,\n  isArrayBuffer,\n  isBuffer,\n  isFormData,\n  isArrayBufferView,\n  isString,\n  isNumber,\n  isBoolean,\n  isObject,\n  isPlainObject,\n  isUndefined,\n  isDate,\n  isFile,\n  isBlob,\n  isRegExp,\n  isFunction,\n  isStream,\n  isURLSearchParams,\n  isTypedArray,\n  isFileList,\n  forEach,\n  merge,\n  extend,\n  trim,\n  stripBOM,\n  inherits,\n  toFlatObject,\n  kindOf,\n  kindOfTest,\n  endsWith,\n  toArray,\n  forEachEntry,\n  matchAll,\n  isHTMLForm,\n  hasOwnProperty,\n  hasOwnProp: hasOwnProperty, // an alias to avoid ESLint no-prototype-builtins detection\n  reduceDescriptors,\n  freezeMethods,\n  toObjectSet,\n  toCamelCase,\n  noop,\n  toFiniteNumber\n};\n"], "mappings": "AAAA;;AAEA,OAAOA,IAAP,MAAiB,mBAAjB,C,CAEA;;AAEA,IAAOC,QAAP,GAAmBC,MAAM,CAACC,SAA1B,CAAOF,QAAP;AACA,IAAOG,cAAP,GAAyBF,MAAzB,CAAOE,cAAP;;AAEA,IAAMC,MAAM,GAAI,UAAAC,KAAK;EAAA,OAAI,UAAAC,KAAK,EAAI;IAC9B,IAAMC,GAAG,GAAGP,QAAQ,CAACQ,IAAT,CAAcF,KAAd,CAAZ;IACA,OAAOD,KAAK,CAACE,GAAD,CAAL,KAAeF,KAAK,CAACE,GAAD,CAAL,GAAaA,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,EAAiBC,WAAjB,EAA5B,CAAP;EACH,CAHoB;AAAA,CAAN,CAGZT,MAAM,CAACU,MAAP,CAAc,IAAd,CAHY,CAAf;;AAKA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACC,IAAD,EAAU;EAC3BA,IAAI,GAAGA,IAAI,CAACH,WAAL,EAAP;EACA,OAAO,UAACJ,KAAD;IAAA,OAAWF,MAAM,CAACE,KAAD,CAAN,KAAkBO,IAA7B;EAAA,CAAP;AACD,CAHD;;AAKA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAAAD,IAAI;EAAA,OAAI,UAAAP,KAAK;IAAA,OAAI,OAAOA,KAAP,KAAiBO,IAArB;EAAA,CAAT;AAAA,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAOE,OAAP,GAAkBC,KAAlB,CAAOD,OAAP;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAME,WAAW,GAAGH,UAAU,CAAC,WAAD,CAA9B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASI,QAAT,CAAkBC,GAAlB,EAAuB;EACrB,OAAOA,GAAG,KAAK,IAAR,IAAgB,CAACF,WAAW,CAACE,GAAD,CAA5B,IAAqCA,GAAG,CAACC,WAAJ,KAAoB,IAAzD,IAAiE,CAACH,WAAW,CAACE,GAAG,CAACC,WAAL,CAA7E,IACFC,UAAU,CAACF,GAAG,CAACC,WAAJ,CAAgBF,QAAjB,CADR,IACsCC,GAAG,CAACC,WAAJ,CAAgBF,QAAhB,CAAyBC,GAAzB,CAD7C;AAED;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMG,aAAa,GAAGV,UAAU,CAAC,aAAD,CAAhC;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASW,iBAAT,CAA2BJ,GAA3B,EAAgC;EAC9B,IAAIK,MAAJ;;EACA,IAAK,OAAOC,WAAP,KAAuB,WAAxB,IAAyCA,WAAW,CAACC,MAAzD,EAAkE;IAChEF,MAAM,GAAGC,WAAW,CAACC,MAAZ,CAAmBP,GAAnB,CAAT;EACD,CAFD,MAEO;IACLK,MAAM,GAAIL,GAAD,IAAUA,GAAG,CAACQ,MAAd,IAA0BL,aAAa,CAACH,GAAG,CAACQ,MAAL,CAAhD;EACD;;EACD,OAAOH,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMI,QAAQ,GAAGd,UAAU,CAAC,QAAD,CAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMO,UAAU,GAAGP,UAAU,CAAC,UAAD,CAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMe,QAAQ,GAAGf,UAAU,CAAC,QAAD,CAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMgB,QAAQ,GAAG,SAAXA,QAAW,CAACxB,KAAD;EAAA,OAAWA,KAAK,KAAK,IAAV,IAAkB,OAAOA,KAAP,KAAiB,QAA9C;AAAA,CAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMyB,SAAS,GAAG,SAAZA,SAAY,CAAAzB,KAAK;EAAA,OAAIA,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAhC;AAAA,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAM0B,aAAa,GAAG,SAAhBA,aAAgB,CAACb,GAAD,EAAS;EAC7B,IAAIf,MAAM,CAACe,GAAD,CAAN,KAAgB,QAApB,EAA8B;IAC5B,OAAO,KAAP;EACD;;EAED,IAAMjB,SAAS,GAAGC,cAAc,CAACgB,GAAD,CAAhC;EACA,OAAO,CAACjB,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAKD,MAAM,CAACC,SAA3C,IAAwDD,MAAM,CAACE,cAAP,CAAsBD,SAAtB,MAAqC,IAA9F,KAAuG,EAAE+B,MAAM,CAACC,WAAP,IAAsBf,GAAxB,CAAvG,IAAuI,EAAEc,MAAM,CAACE,QAAP,IAAmBhB,GAArB,CAA9I;AACD,CAPD;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMiB,MAAM,GAAGxB,UAAU,CAAC,MAAD,CAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMyB,MAAM,GAAGzB,UAAU,CAAC,MAAD,CAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAM0B,MAAM,GAAG1B,UAAU,CAAC,MAAD,CAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAM2B,UAAU,GAAG3B,UAAU,CAAC,UAAD,CAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAM4B,QAAQ,GAAG,SAAXA,QAAW,CAACrB,GAAD;EAAA,OAASW,QAAQ,CAACX,GAAD,CAAR,IAAiBE,UAAU,CAACF,GAAG,CAACsB,IAAL,CAApC;AAAA,CAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAACpC,KAAD,EAAW;EAC5B,IAAMqC,OAAO,GAAG,mBAAhB;EACA,OAAOrC,KAAK,KACT,OAAOsC,QAAP,KAAoB,UAApB,IAAkCtC,KAAK,YAAYsC,QAApD,IACA5C,QAAQ,CAACQ,IAAT,CAAcF,KAAd,MAAyBqC,OADzB,IAECtB,UAAU,CAACf,KAAK,CAACN,QAAP,CAAV,IAA8BM,KAAK,CAACN,QAAN,OAAqB2C,OAH1C,CAAZ;AAKD,CAPD;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAME,iBAAiB,GAAGjC,UAAU,CAAC,iBAAD,CAApC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMkC,IAAI,GAAG,SAAPA,IAAO,CAACvC,GAAD;EAAA,OAASA,GAAG,CAACuC,IAAJ,GACpBvC,GAAG,CAACuC,IAAJ,EADoB,GACPvC,GAAG,CAACwC,OAAJ,CAAY,oCAAZ,EAAkD,EAAlD,CADF;AAAA,CAAb;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,OAAT,CAAiBC,GAAjB,EAAsBC,EAAtB,EAAqD;EAAA,+EAAJ,EAAI;EAAA,2BAA1BC,UAA0B;EAAA,IAA1BA,UAA0B,gCAAb,KAAa;;EACnD;EACA,IAAIF,GAAG,KAAK,IAAR,IAAgB,OAAOA,GAAP,KAAe,WAAnC,EAAgD;IAC9C;EACD;;EAED,IAAIG,CAAJ;EACA,IAAIC,CAAJ,CAPmD,CASnD;;EACA,IAAI,OAAOJ,GAAP,KAAe,QAAnB,EAA6B;IAC3B;IACAA,GAAG,GAAG,CAACA,GAAD,CAAN;EACD;;EAED,IAAIlC,OAAO,CAACkC,GAAD,CAAX,EAAkB;IAChB;IACA,KAAKG,CAAC,GAAG,CAAJ,EAAOC,CAAC,GAAGJ,GAAG,CAACK,MAApB,EAA4BF,CAAC,GAAGC,CAAhC,EAAmCD,CAAC,EAApC,EAAwC;MACtCF,EAAE,CAAC1C,IAAH,CAAQ,IAAR,EAAcyC,GAAG,CAACG,CAAD,CAAjB,EAAsBA,CAAtB,EAAyBH,GAAzB;IACD;EACF,CALD,MAKO;IACL;IACA,IAAMM,IAAI,GAAGJ,UAAU,GAAGlD,MAAM,CAACuD,mBAAP,CAA2BP,GAA3B,CAAH,GAAqChD,MAAM,CAACsD,IAAP,CAAYN,GAAZ,CAA5D;IACA,IAAMQ,GAAG,GAAGF,IAAI,CAACD,MAAjB;IACA,IAAII,GAAJ;;IAEA,KAAKN,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGK,GAAhB,EAAqBL,CAAC,EAAtB,EAA0B;MACxBM,GAAG,GAAGH,IAAI,CAACH,CAAD,CAAV;MACAF,EAAE,CAAC1C,IAAH,CAAQ,IAAR,EAAcyC,GAAG,CAACS,GAAD,CAAjB,EAAwBA,GAAxB,EAA6BT,GAA7B;IACD;EACF;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA;EAAe;AAANU,KAAT,GAA4C;EAC1C,IAAMnC,MAAM,GAAG,EAAf;;EACA,IAAMoC,WAAW,GAAG,SAAdA,WAAc,CAACzC,GAAD,EAAMuC,GAAN,EAAc;IAChC,IAAI1B,aAAa,CAACR,MAAM,CAACkC,GAAD,CAAP,CAAb,IAA8B1B,aAAa,CAACb,GAAD,CAA/C,EAAsD;MACpDK,MAAM,CAACkC,GAAD,CAAN,GAAcC,KAAK,CAACnC,MAAM,CAACkC,GAAD,CAAP,EAAcvC,GAAd,CAAnB;IACD,CAFD,MAEO,IAAIa,aAAa,CAACb,GAAD,CAAjB,EAAwB;MAC7BK,MAAM,CAACkC,GAAD,CAAN,GAAcC,KAAK,CAAC,EAAD,EAAKxC,GAAL,CAAnB;IACD,CAFM,MAEA,IAAIJ,OAAO,CAACI,GAAD,CAAX,EAAkB;MACvBK,MAAM,CAACkC,GAAD,CAAN,GAAcvC,GAAG,CAACV,KAAJ,EAAd;IACD,CAFM,MAEA;MACLe,MAAM,CAACkC,GAAD,CAAN,GAAcvC,GAAd;IACD;EACF,CAVD;;EAYA,KAAK,IAAIiC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGQ,SAAS,CAACP,MAA9B,EAAsCF,CAAC,GAAGC,CAA1C,EAA6CD,CAAC,EAA9C,EAAkD;IAChDS,SAAS,CAACT,CAAD,CAAT,IAAgBJ,OAAO,CAACa,SAAS,CAACT,CAAD,CAAV,EAAeQ,WAAf,CAAvB;EACD;;EACD,OAAOpC,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMsC,MAAM,GAAG,SAATA,MAAS,CAACC,CAAD,EAAIC,CAAJ,EAAOC,OAAP,EAAqC;EAAA,gFAAP,EAAO;EAAA,IAApBd,UAAoB,SAApBA,UAAoB;;EAClDH,OAAO,CAACgB,CAAD,EAAI,UAAC7C,GAAD,EAAMuC,GAAN,EAAc;IACvB,IAAIO,OAAO,IAAI5C,UAAU,CAACF,GAAD,CAAzB,EAAgC;MAC9B4C,CAAC,CAACL,GAAD,CAAD,GAAS3D,IAAI,CAACoB,GAAD,EAAM8C,OAAN,CAAb;IACD,CAFD,MAEO;MACLF,CAAC,CAACL,GAAD,CAAD,GAASvC,GAAT;IACD;EACF,CANM,EAMJ;IAACgC,UAAU,EAAVA;EAAD,CANI,CAAP;EAOA,OAAOY,CAAP;AACD,CATD;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMG,QAAQ,GAAG,SAAXA,QAAW,CAACC,OAAD,EAAa;EAC5B,IAAIA,OAAO,CAACC,UAAR,CAAmB,CAAnB,MAA0B,MAA9B,EAAsC;IACpCD,OAAO,GAAGA,OAAO,CAAC1D,KAAR,CAAc,CAAd,CAAV;EACD;;EACD,OAAO0D,OAAP;AACD,CALD;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAME,QAAQ,GAAG,SAAXA,QAAW,CAACjD,WAAD,EAAckD,gBAAd,EAAgCC,KAAhC,EAAuCC,WAAvC,EAAuD;EACtEpD,WAAW,CAAClB,SAAZ,GAAwBD,MAAM,CAACU,MAAP,CAAc2D,gBAAgB,CAACpE,SAA/B,EAA0CsE,WAA1C,CAAxB;EACApD,WAAW,CAAClB,SAAZ,CAAsBkB,WAAtB,GAAoCA,WAApC;EACAnB,MAAM,CAACwE,cAAP,CAAsBrD,WAAtB,EAAmC,OAAnC,EAA4C;IAC1CsD,KAAK,EAAEJ,gBAAgB,CAACpE;EADkB,CAA5C;EAGAqE,KAAK,IAAItE,MAAM,CAAC0E,MAAP,CAAcvD,WAAW,CAAClB,SAA1B,EAAqCqE,KAArC,CAAT;AACD,CAPD;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMK,YAAY,GAAG,SAAfA,YAAe,CAACC,SAAD,EAAYC,OAAZ,EAAqBC,MAArB,EAA6BC,UAA7B,EAA4C;EAC/D,IAAIT,KAAJ;EACA,IAAInB,CAAJ;EACA,IAAI6B,IAAJ;EACA,IAAMC,MAAM,GAAG,EAAf;EAEAJ,OAAO,GAAGA,OAAO,IAAI,EAArB,CAN+D,CAO/D;;EACA,IAAID,SAAS,IAAI,IAAjB,EAAuB,OAAOC,OAAP;;EAEvB,GAAG;IACDP,KAAK,GAAGtE,MAAM,CAACuD,mBAAP,CAA2BqB,SAA3B,CAAR;IACAzB,CAAC,GAAGmB,KAAK,CAACjB,MAAV;;IACA,OAAOF,CAAC,KAAK,CAAb,EAAgB;MACd6B,IAAI,GAAGV,KAAK,CAACnB,CAAD,CAAZ;;MACA,IAAI,CAAC,CAAC4B,UAAD,IAAeA,UAAU,CAACC,IAAD,EAAOJ,SAAP,EAAkBC,OAAlB,CAA1B,KAAyD,CAACI,MAAM,CAACD,IAAD,CAApE,EAA4E;QAC1EH,OAAO,CAACG,IAAD,CAAP,GAAgBJ,SAAS,CAACI,IAAD,CAAzB;QACAC,MAAM,CAACD,IAAD,CAAN,GAAe,IAAf;MACD;IACF;;IACDJ,SAAS,GAAGE,MAAM,KAAK,KAAX,IAAoB5E,cAAc,CAAC0E,SAAD,CAA9C;EACD,CAXD,QAWSA,SAAS,KAAK,CAACE,MAAD,IAAWA,MAAM,CAACF,SAAD,EAAYC,OAAZ,CAAtB,CAAT,IAAwDD,SAAS,KAAK5E,MAAM,CAACC,SAXtF;;EAaA,OAAO4E,OAAP;AACD,CAxBD;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMK,QAAQ,GAAG,SAAXA,QAAW,CAAC5E,GAAD,EAAM6E,YAAN,EAAoBC,QAApB,EAAiC;EAChD9E,GAAG,GAAG+E,MAAM,CAAC/E,GAAD,CAAZ;;EACA,IAAI8E,QAAQ,KAAKE,SAAb,IAA0BF,QAAQ,GAAG9E,GAAG,CAAC+C,MAA7C,EAAqD;IACnD+B,QAAQ,GAAG9E,GAAG,CAAC+C,MAAf;EACD;;EACD+B,QAAQ,IAAID,YAAY,CAAC9B,MAAzB;EACA,IAAMkC,SAAS,GAAGjF,GAAG,CAACkF,OAAJ,CAAYL,YAAZ,EAA0BC,QAA1B,CAAlB;EACA,OAAOG,SAAS,KAAK,CAAC,CAAf,IAAoBA,SAAS,KAAKH,QAAzC;AACD,CARD;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMK,OAAO,GAAG,SAAVA,OAAU,CAACpF,KAAD,EAAW;EACzB,IAAI,CAACA,KAAL,EAAY,OAAO,IAAP;EACZ,IAAIS,OAAO,CAACT,KAAD,CAAX,EAAoB,OAAOA,KAAP;EACpB,IAAI8C,CAAC,GAAG9C,KAAK,CAACgD,MAAd;EACA,IAAI,CAACzB,QAAQ,CAACuB,CAAD,CAAb,EAAkB,OAAO,IAAP;EAClB,IAAMuC,GAAG,GAAG,IAAI3E,KAAJ,CAAUoC,CAAV,CAAZ;;EACA,OAAOA,CAAC,KAAK,CAAb,EAAgB;IACduC,GAAG,CAACvC,CAAD,CAAH,GAAS9C,KAAK,CAAC8C,CAAD,CAAd;EACD;;EACD,OAAOuC,GAAP;AACD,CAVD;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMC,YAAY,GAAI,UAAAC,UAAU,EAAI;EAClC;EACA,OAAO,UAAAvF,KAAK,EAAI;IACd,OAAOuF,UAAU,IAAIvF,KAAK,YAAYuF,UAAtC;EACD,CAFD;AAGD,CALoB,CAKlB,OAAOC,UAAP,KAAsB,WAAtB,IAAqC3F,cAAc,CAAC2F,UAAD,CALjC,CAArB;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAAC9C,GAAD,EAAMC,EAAN,EAAa;EAChC,IAAM8C,SAAS,GAAG/C,GAAG,IAAIA,GAAG,CAAChB,MAAM,CAACE,QAAR,CAA5B;EAEA,IAAMA,QAAQ,GAAG6D,SAAS,CAACxF,IAAV,CAAeyC,GAAf,CAAjB;EAEA,IAAIzB,MAAJ;;EAEA,OAAO,CAACA,MAAM,GAAGW,QAAQ,CAAC8D,IAAT,EAAV,KAA8B,CAACzE,MAAM,CAAC0E,IAA7C,EAAmD;IACjD,IAAMC,IAAI,GAAG3E,MAAM,CAACkD,KAApB;IACAxB,EAAE,CAAC1C,IAAH,CAAQyC,GAAR,EAAakD,IAAI,CAAC,CAAD,CAAjB,EAAsBA,IAAI,CAAC,CAAD,CAA1B;EACD;AACF,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,CAACC,MAAD,EAAS9F,GAAT,EAAiB;EAChC,IAAI+F,OAAJ;EACA,IAAMX,GAAG,GAAG,EAAZ;;EAEA,OAAO,CAACW,OAAO,GAAGD,MAAM,CAACE,IAAP,CAAYhG,GAAZ,CAAX,MAAiC,IAAxC,EAA8C;IAC5CoF,GAAG,CAACa,IAAJ,CAASF,OAAT;EACD;;EAED,OAAOX,GAAP;AACD,CATD;AAWA;;;AACA,IAAMc,UAAU,GAAG7F,UAAU,CAAC,iBAAD,CAA7B;;AAEA,IAAM8F,WAAW,GAAG,SAAdA,WAAc,CAAAnG,GAAG,EAAI;EACzB,OAAOA,GAAG,CAACG,WAAJ,GAAkBqC,OAAlB,CAA0B,uBAA1B,EACL,SAAS4D,QAAT,CAAkBC,CAAlB,EAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;IAC3B,OAAOD,EAAE,CAACE,WAAH,KAAmBD,EAA1B;EACD,CAHI,CAAP;AAKD,CAND;AAQA;;;AACA,IAAME,cAAc,GAAI;EAAA,IAAEA,cAAF,SAAEA,cAAF;EAAA,OAAsB,UAAC/D,GAAD,EAAMgC,IAAN;IAAA,OAAe+B,cAAc,CAACxG,IAAf,CAAoByC,GAApB,EAAyBgC,IAAzB,CAAf;EAAA,CAAtB;AAAA,CAAD,CAAsEhF,MAAM,CAACC,SAA7E,CAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAM+G,QAAQ,GAAGrG,UAAU,CAAC,QAAD,CAA3B;;AAEA,IAAMsG,iBAAiB,GAAG,SAApBA,iBAAoB,CAACjE,GAAD,EAAMkE,OAAN,EAAkB;EAC1C,IAAM3C,WAAW,GAAGvE,MAAM,CAACmH,yBAAP,CAAiCnE,GAAjC,CAApB;EACA,IAAMoE,kBAAkB,GAAG,EAA3B;EAEArE,OAAO,CAACwB,WAAD,EAAc,UAAC8C,UAAD,EAAaC,IAAb,EAAsB;IACzC,IAAIJ,OAAO,CAACG,UAAD,EAAaC,IAAb,EAAmBtE,GAAnB,CAAP,KAAmC,KAAvC,EAA8C;MAC5CoE,kBAAkB,CAACE,IAAD,CAAlB,GAA2BD,UAA3B;IACD;EACF,CAJM,CAAP;EAMArH,MAAM,CAACuH,gBAAP,CAAwBvE,GAAxB,EAA6BoE,kBAA7B;AACD,CAXD;AAaA;AACA;AACA;AACA;;;AAEA,IAAMI,aAAa,GAAG,SAAhBA,aAAgB,CAACxE,GAAD,EAAS;EAC7BiE,iBAAiB,CAACjE,GAAD,EAAM,UAACqE,UAAD,EAAaC,IAAb,EAAsB;IAC3C,IAAM7C,KAAK,GAAGzB,GAAG,CAACsE,IAAD,CAAjB;IAEA,IAAI,CAAClG,UAAU,CAACqD,KAAD,CAAf,EAAwB;IAExB4C,UAAU,CAACI,UAAX,GAAwB,KAAxB;;IAEA,IAAI,cAAcJ,UAAlB,EAA8B;MAC5BA,UAAU,CAACK,QAAX,GAAsB,KAAtB;MACA;IACD;;IAED,IAAI,CAACL,UAAU,CAACM,GAAhB,EAAqB;MACnBN,UAAU,CAACM,GAAX,GAAiB,YAAM;QACrB,MAAMC,KAAK,CAAC,gCAAgCN,IAAhC,GAAuC,IAAxC,CAAX;MACD,CAFD;IAGD;EACF,CAjBgB,CAAjB;AAkBD,CAnBD;;AAqBA,IAAMO,WAAW,GAAG,SAAdA,WAAc,CAACC,aAAD,EAAgBC,SAAhB,EAA8B;EAChD,IAAM/E,GAAG,GAAG,EAAZ;;EAEA,IAAMgF,MAAM,GAAG,SAATA,MAAS,CAACtC,GAAD,EAAS;IACtBA,GAAG,CAAC3C,OAAJ,CAAY,UAAA0B,KAAK,EAAI;MACnBzB,GAAG,CAACyB,KAAD,CAAH,GAAa,IAAb;IACD,CAFD;EAGD,CAJD;;EAMA3D,OAAO,CAACgH,aAAD,CAAP,GAAyBE,MAAM,CAACF,aAAD,CAA/B,GAAiDE,MAAM,CAAC3C,MAAM,CAACyC,aAAD,CAAN,CAAsBG,KAAtB,CAA4BF,SAA5B,CAAD,CAAvD;EAEA,OAAO/E,GAAP;AACD,CAZD;;AAcA,IAAMkF,IAAI,GAAG,SAAPA,IAAO,GAAM,CAAE,CAArB;;AAEA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAC1D,KAAD,EAAQ2D,YAAR,EAAyB;EAC9C3D,KAAK,GAAG,CAACA,KAAT;EACA,OAAO4D,MAAM,CAACC,QAAP,CAAgB7D,KAAhB,IAAyBA,KAAzB,GAAiC2D,YAAxC;AACD,CAHD;;AAKA,eAAe;EACbtH,OAAO,EAAPA,OADa;EAEbO,aAAa,EAAbA,aAFa;EAGbJ,QAAQ,EAARA,QAHa;EAIbwB,UAAU,EAAVA,UAJa;EAKbnB,iBAAiB,EAAjBA,iBALa;EAMbK,QAAQ,EAARA,QANa;EAObC,QAAQ,EAARA,QAPa;EAQbE,SAAS,EAATA,SARa;EASbD,QAAQ,EAARA,QATa;EAUbE,aAAa,EAAbA,aAVa;EAWbf,WAAW,EAAXA,WAXa;EAYbmB,MAAM,EAANA,MAZa;EAabC,MAAM,EAANA,MAba;EAcbC,MAAM,EAANA,MAda;EAeb2E,QAAQ,EAARA,QAfa;EAgBb5F,UAAU,EAAVA,UAhBa;EAiBbmB,QAAQ,EAARA,QAjBa;EAkBbK,iBAAiB,EAAjBA,iBAlBa;EAmBb+C,YAAY,EAAZA,YAnBa;EAoBbrD,UAAU,EAAVA,UApBa;EAqBbS,OAAO,EAAPA,OArBa;EAsBbW,KAAK,EAALA,KAtBa;EAuBbG,MAAM,EAANA,MAvBa;EAwBbhB,IAAI,EAAJA,IAxBa;EAyBboB,QAAQ,EAARA,QAzBa;EA0BbG,QAAQ,EAARA,QA1Ba;EA2BbO,YAAY,EAAZA,YA3Ba;EA4BbxE,MAAM,EAANA,MA5Ba;EA6BbQ,UAAU,EAAVA,UA7Ba;EA8BbuE,QAAQ,EAARA,QA9Ba;EA+BbO,OAAO,EAAPA,OA/Ba;EAgCbK,YAAY,EAAZA,YAhCa;EAiCbK,QAAQ,EAARA,QAjCa;EAkCbK,UAAU,EAAVA,UAlCa;EAmCbO,cAAc,EAAdA,cAnCa;EAoCbwB,UAAU,EAAExB,cApCC;EAoCe;EAC5BE,iBAAiB,EAAjBA,iBArCa;EAsCbO,aAAa,EAAbA,aAtCa;EAuCbK,WAAW,EAAXA,WAvCa;EAwCbpB,WAAW,EAAXA,WAxCa;EAyCbyB,IAAI,EAAJA,IAzCa;EA0CbC,cAAc,EAAdA;AA1Ca,CAAf"}, "metadata": {}, "sourceType": "module"}