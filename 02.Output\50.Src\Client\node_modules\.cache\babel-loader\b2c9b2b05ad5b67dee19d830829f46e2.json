{"ast": null, "code": "var baseTrim = require('./_baseTrim'),\n    isObject = require('./isObject'),\n    isSymbol = require('./isSymbol');\n/** Used as references for various `Number` constants. */\n\n\nvar NAN = 0 / 0;\n/** Used to detect bad signed hexadecimal string values. */\n\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n/** Used to detect binary string values. */\n\nvar reIsBinary = /^0b[01]+$/i;\n/** Used to detect octal string values. */\n\nvar reIsOctal = /^0o[0-7]+$/i;\n/** Built-in method references without a dependency on `root`. */\n\nvar freeParseInt = parseInt;\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\n\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n\n  if (isSymbol(value)) {\n    return NAN;\n  }\n\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? other + '' : other;\n  }\n\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;\n}\n\nmodule.exports = toNumber;", "map": {"version": 3, "names": ["baseTrim", "require", "isObject", "isSymbol", "NAN", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "parseInt", "toNumber", "value", "other", "valueOf", "isBinary", "test", "slice", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/toNumber.js"], "sourcesContent": ["var baseTrim = require('./_baseTrim'),\n    isObject = require('./isObject'),\n    isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = toNumber;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAD,CAAtB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,YAAD,CADtB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,YAAD,CAFtB;AAIA;;;AACA,IAAIG,GAAG,GAAG,IAAI,CAAd;AAEA;;AACA,IAAIC,UAAU,GAAG,oBAAjB;AAEA;;AACA,IAAIC,UAAU,GAAG,YAAjB;AAEA;;AACA,IAAIC,SAAS,GAAG,aAAhB;AAEA;;AACA,IAAIC,YAAY,GAAGC,QAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;EACvB,IAAI,OAAOA,KAAP,IAAgB,QAApB,EAA8B;IAC5B,OAAOA,KAAP;EACD;;EACD,IAAIR,QAAQ,CAACQ,KAAD,CAAZ,EAAqB;IACnB,OAAOP,GAAP;EACD;;EACD,IAAIF,QAAQ,CAACS,KAAD,CAAZ,EAAqB;IACnB,IAAIC,KAAK,GAAG,OAAOD,KAAK,CAACE,OAAb,IAAwB,UAAxB,GAAqCF,KAAK,CAACE,OAAN,EAArC,GAAuDF,KAAnE;IACAA,KAAK,GAAGT,QAAQ,CAACU,KAAD,CAAR,GAAmBA,KAAK,GAAG,EAA3B,GAAiCA,KAAzC;EACD;;EACD,IAAI,OAAOD,KAAP,IAAgB,QAApB,EAA8B;IAC5B,OAAOA,KAAK,KAAK,CAAV,GAAcA,KAAd,GAAsB,CAACA,KAA9B;EACD;;EACDA,KAAK,GAAGX,QAAQ,CAACW,KAAD,CAAhB;EACA,IAAIG,QAAQ,GAAGR,UAAU,CAACS,IAAX,CAAgBJ,KAAhB,CAAf;EACA,OAAQG,QAAQ,IAAIP,SAAS,CAACQ,IAAV,CAAeJ,KAAf,CAAb,GACHH,YAAY,CAACG,KAAK,CAACK,KAAN,CAAY,CAAZ,CAAD,EAAiBF,QAAQ,GAAG,CAAH,GAAO,CAAhC,CADT,GAEFT,UAAU,CAACU,IAAX,CAAgBJ,KAAhB,IAAyBP,GAAzB,GAA+B,CAACO,KAFrC;AAGD;;AAEDM,MAAM,CAACC,OAAP,GAAiBR,QAAjB"}, "metadata": {}, "sourceType": "script"}