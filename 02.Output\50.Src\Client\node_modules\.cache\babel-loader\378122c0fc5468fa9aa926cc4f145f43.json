{"ast": null, "code": "'use strict';\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\n\nmodule.exports = function (availableTransports) {\n  return {\n    filterToEnabled: function filterToEnabled(transportsWhitelist, info) {\n      var transports = {\n        main: [],\n        facade: []\n      };\n\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n\n      availableTransports.forEach(function (trans) {\n        if (!trans) {\n          return;\n        }\n\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n\n        if (transportsWhitelist.length && transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};", "map": {"version": 3, "names": ["debug", "process", "env", "NODE_ENV", "require", "module", "exports", "availableTransports", "filterToEnabled", "transports<PERSON><PERSON><PERSON><PERSON>", "info", "transports", "main", "facade", "for<PERSON>ach", "trans", "transportName", "websocket", "length", "indexOf", "enabled", "push", "facadeTransport"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/transport.js"], "sourcesContent": ["'use strict';\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\n\nmodule.exports = function(availableTransports) {\n  return {\n    filterToEnabled: function(transportsWhitelist, info) {\n      var transports = {\n        main: []\n      , facade: []\n      };\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n\n      availableTransports.forEach(function(trans) {\n        if (!trans) {\n          return;\n        }\n\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n\n        if (transportsWhitelist.length &&\n            transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGI,OAAO,CAAC,OAAD,CAAP,CAAiB,+BAAjB,CAAR;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiB,UAASC,mBAAT,EAA8B;EAC7C,OAAO;IACLC,eAAe,EAAE,yBAASC,mBAAT,EAA8BC,IAA9B,EAAoC;MACnD,IAAIC,UAAU,GAAG;QACfC,IAAI,EAAE,EADS;QAEfC,MAAM,EAAE;MAFO,CAAjB;;MAIA,IAAI,CAACJ,mBAAL,EAA0B;QACxBA,mBAAmB,GAAG,EAAtB;MACD,CAFD,MAEO,IAAI,OAAOA,mBAAP,KAA+B,QAAnC,EAA6C;QAClDA,mBAAmB,GAAG,CAACA,mBAAD,CAAtB;MACD;;MAEDF,mBAAmB,CAACO,OAApB,CAA4B,UAASC,KAAT,EAAgB;QAC1C,IAAI,CAACA,KAAL,EAAY;UACV;QACD;;QAED,IAAIA,KAAK,CAACC,aAAN,KAAwB,WAAxB,IAAuCN,IAAI,CAACO,SAAL,KAAmB,KAA9D,EAAqE;UACnEjB,KAAK,CAAC,sBAAD,EAAyB,WAAzB,CAAL;UACA;QACD;;QAED,IAAIS,mBAAmB,CAACS,MAApB,IACAT,mBAAmB,CAACU,OAApB,CAA4BJ,KAAK,CAACC,aAAlC,MAAqD,CAAC,CAD1D,EAC6D;UAC3DhB,KAAK,CAAC,kBAAD,EAAqBe,KAAK,CAACC,aAA3B,CAAL;UACA;QACD;;QAED,IAAID,KAAK,CAACK,OAAN,CAAcV,IAAd,CAAJ,EAAyB;UACvBV,KAAK,CAAC,SAAD,EAAYe,KAAK,CAACC,aAAlB,CAAL;UACAL,UAAU,CAACC,IAAX,CAAgBS,IAAhB,CAAqBN,KAArB;;UACA,IAAIA,KAAK,CAACO,eAAV,EAA2B;YACzBX,UAAU,CAACE,MAAX,CAAkBQ,IAAlB,CAAuBN,KAAK,CAACO,eAA7B;UACD;QACF,CAND,MAMO;UACLtB,KAAK,CAAC,UAAD,EAAae,KAAK,CAACC,aAAnB,CAAL;QACD;MACF,CAzBD;MA0BA,OAAOL,UAAP;IACD;EAvCI,CAAP;AAyCD,CA1CD"}, "metadata": {}, "sourceType": "script"}