{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits'),\n    objectUtils = require('./utils/object');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\n\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n  this.xo.once('finish', function (status, text) {\n    var info, rtt;\n\n    if (status === 200) {\n      rtt = +new Date() - t0;\n\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\n\ninherits(InfoAjax, EventEmitter);\n\nInfoAjax.prototype.close = function () {\n  this.removeAllListeners();\n  this.xo.close();\n};\n\nmodule.exports = InfoAjax;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "objectUtils", "debug", "process", "env", "NODE_ENV", "InfoAjax", "url", "AjaxObject", "call", "self", "t0", "Date", "xo", "once", "status", "text", "info", "rtt", "JSON", "parse", "e", "isObject", "emit", "removeAllListeners", "prototype", "close", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/info-ajax.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , objectUtils = require('./utils/object')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\n\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n\n  this.xo.once('finish', function(status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = (+new Date()) - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\n\ninherits(InfoAjax, EventEmitter);\n\nInfoAjax.prototype.close = function() {\n  this.removeAllListeners();\n  this.xo.close();\n};\n\nmodule.exports = InfoAjax;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;;AAKA,IAAIG,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAD,CAAP,CAAiB,yBAAjB,CAAR;AACD;;AAED,SAASO,QAAT,CAAkBC,GAAlB,EAAuBC,UAAvB,EAAmC;EACjCV,YAAY,CAACW,IAAb,CAAkB,IAAlB;EAEA,IAAIC,IAAI,GAAG,IAAX;EACA,IAAIC,EAAE,GAAG,CAAC,IAAIC,IAAJ,EAAV;EACA,KAAKC,EAAL,GAAU,IAAIL,UAAJ,CAAe,KAAf,EAAsBD,GAAtB,CAAV;EAEA,KAAKM,EAAL,CAAQC,IAAR,CAAa,QAAb,EAAuB,UAASC,MAAT,EAAiBC,IAAjB,EAAuB;IAC5C,IAAIC,IAAJ,EAAUC,GAAV;;IACA,IAAIH,MAAM,KAAK,GAAf,EAAoB;MAClBG,GAAG,GAAI,CAAC,IAAIN,IAAJ,EAAF,GAAgBD,EAAtB;;MACA,IAAIK,IAAJ,EAAU;QACR,IAAI;UACFC,IAAI,GAAGE,IAAI,CAACC,KAAL,CAAWJ,IAAX,CAAP;QACD,CAFD,CAEE,OAAOK,CAAP,EAAU;UACVnB,KAAK,CAAC,UAAD,EAAac,IAAb,CAAL;QACD;MACF;;MAED,IAAI,CAACf,WAAW,CAACqB,QAAZ,CAAqBL,IAArB,CAAL,EAAiC;QAC/BA,IAAI,GAAG,EAAP;MACD;IACF;;IACDP,IAAI,CAACa,IAAL,CAAU,QAAV,EAAoBN,IAApB,EAA0BC,GAA1B;IACAR,IAAI,CAACc,kBAAL;EACD,CAlBD;AAmBD;;AAEDxB,QAAQ,CAACM,QAAD,EAAWR,YAAX,CAAR;;AAEAQ,QAAQ,CAACmB,SAAT,CAAmBC,KAAnB,GAA2B,YAAW;EACpC,KAAKF,kBAAL;EACA,KAAKX,EAAL,CAAQa,KAAR;AACD,CAHD;;AAKAC,MAAM,CAACC,OAAP,GAAiBtB,QAAjB"}, "metadata": {}, "sourceType": "script"}