{"ast": null, "code": "'use strict';\n\nvar crypto = require('crypto'); // This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\n\n\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function (length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n\n    return ret.join('');\n  },\n  number: function (max) {\n    return Math.floor(Math.random() * max);\n  },\n  numberString: function (max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};", "map": {"version": 3, "names": ["crypto", "require", "_randomStringChars", "module", "exports", "string", "length", "max", "bytes", "randomBytes", "ret", "i", "push", "substr", "join", "number", "Math", "floor", "random", "numberString", "t", "p", "Array", "slice"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/random.js"], "sourcesContent": ["'use strict';\n\nvar crypto = require('crypto');\n\n// This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function(length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n    return ret.join('');\n  }\n\n, number: function(max) {\n    return Math.floor(Math.random() * max);\n  }\n\n, numberString: function(max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,QAAD,CAApB,C,CAEA;AACA;;;AACA,IAAIC,kBAAkB,GAAG,kCAAzB;AACAC,MAAM,CAACC,OAAP,GAAiB;EACfC,MAAM,EAAE,UAASC,MAAT,EAAiB;IACvB,IAAIC,GAAG,GAAGL,kBAAkB,CAACI,MAA7B;IACA,IAAIE,KAAK,GAAGR,MAAM,CAACS,WAAP,CAAmBH,MAAnB,CAAZ;IACA,IAAII,GAAG,GAAG,EAAV;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,MAApB,EAA4BK,CAAC,EAA7B,EAAiC;MAC/BD,GAAG,CAACE,IAAJ,CAASV,kBAAkB,CAACW,MAAnB,CAA0BL,KAAK,CAACG,CAAD,CAAL,GAAWJ,GAArC,EAA0C,CAA1C,CAAT;IACD;;IACD,OAAOG,GAAG,CAACI,IAAJ,CAAS,EAAT,CAAP;EACD,CATc;EAWfC,MAAM,EAAE,UAASR,GAAT,EAAc;IACpB,OAAOS,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBX,GAA3B,CAAP;EACD,CAbc;EAefY,YAAY,EAAE,UAASZ,GAAT,EAAc;IAC1B,IAAIa,CAAC,GAAG,CAAC,MAAMb,GAAG,GAAG,CAAZ,CAAD,EAAiBD,MAAzB;IACA,IAAIe,CAAC,GAAG,IAAIC,KAAJ,CAAUF,CAAC,GAAG,CAAd,EAAiBN,IAAjB,CAAsB,GAAtB,CAAR;IACA,OAAO,CAACO,CAAC,GAAG,KAAKN,MAAL,CAAYR,GAAZ,CAAL,EAAuBgB,KAAvB,CAA6B,CAACH,CAA9B,CAAP;EACD;AAnBc,CAAjB"}, "metadata": {}, "sourceType": "script"}