{"ast": null, "code": "'use strict';\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\n\nfunction EventTarget() {\n  this._listeners = {};\n}\n\nEventTarget.prototype.addEventListener = function (eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n\n  var arr = this._listeners[eventType]; // #4\n\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n\n  this._listeners[eventType] = arr;\n};\n\nEventTarget.prototype.removeEventListener = function (eventType, listener) {\n  var arr = this._listeners[eventType];\n\n  if (!arr) {\n    return;\n  }\n\n  var idx = arr.indexOf(listener);\n\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n\n    return;\n  }\n};\n\nEventTarget.prototype.dispatchEvent = function () {\n  var event = arguments[0];\n  var t = event.type; // equivalent of Array.prototype.slice.call(arguments, 0);\n\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments); // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\n\nmodule.exports = EventTarget;", "map": {"version": 3, "names": ["EventTarget", "_listeners", "prototype", "addEventListener", "eventType", "listener", "arr", "indexOf", "concat", "removeEventListener", "idx", "length", "slice", "dispatchEvent", "event", "arguments", "t", "type", "args", "Array", "apply", "listeners", "i", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/event/eventtarget.js"], "sourcesContent": ["'use strict';\n\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\n\nfunction EventTarget() {\n  this._listeners = {};\n}\n\nEventTarget.prototype.addEventListener = function(eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n  var arr = this._listeners[eventType];\n  // #4\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n  this._listeners[eventType] = arr;\n};\n\nEventTarget.prototype.removeEventListener = function(eventType, listener) {\n  var arr = this._listeners[eventType];\n  if (!arr) {\n    return;\n  }\n  var idx = arr.indexOf(listener);\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n    return;\n  }\n};\n\nEventTarget.prototype.dispatchEvent = function() {\n  var event = arguments[0];\n  var t = event.type;\n  // equivalent of Array.prototype.slice.call(arguments, 0);\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments);\n  // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\n\nmodule.exports = EventTarget;\n"], "mappings": "AAAA;AAEA;AACA;AACA;;AAEA,SAASA,WAAT,GAAuB;EACrB,KAAKC,UAAL,GAAkB,EAAlB;AACD;;AAEDD,WAAW,CAACE,SAAZ,CAAsBC,gBAAtB,GAAyC,UAASC,SAAT,EAAoBC,QAApB,EAA8B;EACrE,IAAI,EAAED,SAAS,IAAI,KAAKH,UAApB,CAAJ,EAAqC;IACnC,KAAKA,UAAL,CAAgBG,SAAhB,IAA6B,EAA7B;EACD;;EACD,IAAIE,GAAG,GAAG,KAAKL,UAAL,CAAgBG,SAAhB,CAAV,CAJqE,CAKrE;;EACA,IAAIE,GAAG,CAACC,OAAJ,CAAYF,QAAZ,MAA0B,CAAC,CAA/B,EAAkC;IAChC;IACAC,GAAG,GAAGA,GAAG,CAACE,MAAJ,CAAW,CAACH,QAAD,CAAX,CAAN;EACD;;EACD,KAAKJ,UAAL,CAAgBG,SAAhB,IAA6BE,GAA7B;AACD,CAXD;;AAaAN,WAAW,CAACE,SAAZ,CAAsBO,mBAAtB,GAA4C,UAASL,SAAT,EAAoBC,QAApB,EAA8B;EACxE,IAAIC,GAAG,GAAG,KAAKL,UAAL,CAAgBG,SAAhB,CAAV;;EACA,IAAI,CAACE,GAAL,EAAU;IACR;EACD;;EACD,IAAII,GAAG,GAAGJ,GAAG,CAACC,OAAJ,CAAYF,QAAZ,CAAV;;EACA,IAAIK,GAAG,KAAK,CAAC,CAAb,EAAgB;IACd,IAAIJ,GAAG,CAACK,MAAJ,GAAa,CAAjB,EAAoB;MAClB;MACA,KAAKV,UAAL,CAAgBG,SAAhB,IAA6BE,GAAG,CAACM,KAAJ,CAAU,CAAV,EAAaF,GAAb,EAAkBF,MAAlB,CAAyBF,GAAG,CAACM,KAAJ,CAAUF,GAAG,GAAG,CAAhB,CAAzB,CAA7B;IACD,CAHD,MAGO;MACL,OAAO,KAAKT,UAAL,CAAgBG,SAAhB,CAAP;IACD;;IACD;EACD;AACF,CAfD;;AAiBAJ,WAAW,CAACE,SAAZ,CAAsBW,aAAtB,GAAsC,YAAW;EAC/C,IAAIC,KAAK,GAAGC,SAAS,CAAC,CAAD,CAArB;EACA,IAAIC,CAAC,GAAGF,KAAK,CAACG,IAAd,CAF+C,CAG/C;;EACA,IAAIC,IAAI,GAAGH,SAAS,CAACJ,MAAV,KAAqB,CAArB,GAAyB,CAACG,KAAD,CAAzB,GAAmCK,KAAK,CAACC,KAAN,CAAY,IAAZ,EAAkBL,SAAlB,CAA9C,CAJ+C,CAK/C;EACA;EACA;EACA;;EACA,IAAI,KAAK,OAAOC,CAAZ,CAAJ,EAAoB;IAClB,KAAK,OAAOA,CAAZ,EAAeI,KAAf,CAAqB,IAArB,EAA2BF,IAA3B;EACD;;EACD,IAAIF,CAAC,IAAI,KAAKf,UAAd,EAA0B;IACxB;IACA,IAAIoB,SAAS,GAAG,KAAKpB,UAAL,CAAgBe,CAAhB,CAAhB;;IACA,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,SAAS,CAACV,MAA9B,EAAsCW,CAAC,EAAvC,EAA2C;MACzCD,SAAS,CAACC,CAAD,CAAT,CAAaF,KAAb,CAAmB,IAAnB,EAAyBF,IAAzB;IACD;EACF;AACF,CAnBD;;AAqBAK,MAAM,CAACC,OAAP,GAAiBxB,WAAjB"}, "metadata": {}, "sourceType": "script"}