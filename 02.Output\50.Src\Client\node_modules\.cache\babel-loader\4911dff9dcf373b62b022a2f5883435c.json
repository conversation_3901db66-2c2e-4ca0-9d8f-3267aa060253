{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits');\n\nfunction\n  /* method, url, payload, opts */\nXHRFake() {\n  var self = this;\n  EventEmitter.call(this);\n  this.to = setTimeout(function () {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\n\ninherits(XHRFake, EventEmitter);\n\nXHRFake.prototype.close = function () {\n  clearTimeout(this.to);\n};\n\nXHRFake.timeout = 2000;\nmodule.exports = XHRFake;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "XHRFake", "self", "call", "to", "setTimeout", "emit", "timeout", "prototype", "close", "clearTimeout", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/sender/xhr-fake.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  ;\n\nfunction XHRFake(/* method, url, payload, opts */) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.to = setTimeout(function() {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\n\ninherits(XHRFake, EventEmitter);\n\nXHRFake.prototype.close = function() {\n  clearTimeout(this.to);\n};\n\nXHRFake.timeout = 2000;\n\nmodule.exports = XHRFake;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;;AAIA;EAAiB;AAARE,OAAT,GAAmD;EACjD,IAAIC,IAAI,GAAG,IAAX;EACAJ,YAAY,CAACK,IAAb,CAAkB,IAAlB;EAEA,KAAKC,EAAL,GAAUC,UAAU,CAAC,YAAW;IAC9BH,IAAI,CAACI,IAAL,CAAU,QAAV,EAAoB,GAApB,EAAyB,IAAzB;EACD,CAFmB,EAEjBL,OAAO,CAACM,OAFS,CAApB;AAGD;;AAEDP,QAAQ,CAACC,OAAD,EAAUH,YAAV,CAAR;;AAEAG,OAAO,CAACO,SAAR,CAAkBC,KAAlB,GAA0B,YAAW;EACnCC,YAAY,CAAC,KAAKN,EAAN,CAAZ;AACD,CAFD;;AAIAH,OAAO,CAACM,OAAR,GAAkB,IAAlB;AAEAI,MAAM,CAACC,OAAP,GAAiBX,OAAjB"}, "metadata": {}, "sourceType": "script"}