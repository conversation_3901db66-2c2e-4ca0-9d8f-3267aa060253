{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\n\nvar checkIfAllInShadow = function checkIfAllInShadow(targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) return false;\n    if (targetElement.getRootNode() instanceof ShadowRoot) return true;\n  });\n};\n\nvar getShadow = function getShadow(node) {\n  if (!node) {\n    return document;\n  }\n\n  return node.getRootNode();\n};\n\nvar getDocumentOrShadow = function getDocumentOrShadow(target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n\n  var targets = Array.isArray(target) ? target : [target];\n\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n\n  return document;\n};\n\nexport default getDocumentOrShadow;", "map": {"version": 3, "names": ["getTargetElement", "checkIfAllInShadow", "targets", "every", "item", "targetElement", "getRootNode", "ShadowRoot", "getShadow", "node", "document", "getDocumentOrShadow", "target", "Array", "isArray"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/getDocumentOrShadow.js"], "sourcesContent": ["import { getTargetElement } from '../utils/domTarget';\nvar checkIfAllInShadow = function checkIfAllInShadow(targets) {\n  return targets.every(function (item) {\n    var targetElement = getTargetElement(item);\n    if (!targetElement) return false;\n    if (targetElement.getRootNode() instanceof ShadowRoot) return true;\n  });\n};\nvar getShadow = function getShadow(node) {\n  if (!node) {\n    return document;\n  }\n  return node.getRootNode();\n};\nvar getDocumentOrShadow = function getDocumentOrShadow(target) {\n  if (!target || !document.getRootNode) {\n    return document;\n  }\n  var targets = Array.isArray(target) ? target : [target];\n  if (checkIfAllInShadow(targets)) {\n    return getShadow(getTargetElement(targets[0]));\n  }\n  return document;\n};\nexport default getDocumentOrShadow;"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;;AACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAT,CAA4BC,OAA5B,EAAqC;EAC5D,OAAOA,OAAO,CAACC,KAAR,CAAc,UAAUC,IAAV,EAAgB;IACnC,IAAIC,aAAa,GAAGL,gBAAgB,CAACI,IAAD,CAApC;IACA,IAAI,CAACC,aAAL,EAAoB,OAAO,KAAP;IACpB,IAAIA,aAAa,CAACC,WAAd,cAAuCC,UAA3C,EAAuD,OAAO,IAAP;EACxD,CAJM,CAAP;AAKD,CAND;;AAOA,IAAIC,SAAS,GAAG,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;EACvC,IAAI,CAACA,IAAL,EAAW;IACT,OAAOC,QAAP;EACD;;EACD,OAAOD,IAAI,CAACH,WAAL,EAAP;AACD,CALD;;AAMA,IAAIK,mBAAmB,GAAG,SAASA,mBAAT,CAA6BC,MAA7B,EAAqC;EAC7D,IAAI,CAACA,MAAD,IAAW,CAACF,QAAQ,CAACJ,WAAzB,EAAsC;IACpC,OAAOI,QAAP;EACD;;EACD,IAAIR,OAAO,GAAGW,KAAK,CAACC,OAAN,CAAcF,MAAd,IAAwBA,MAAxB,GAAiC,CAACA,MAAD,CAA/C;;EACA,IAAIX,kBAAkB,CAACC,OAAD,CAAtB,EAAiC;IAC/B,OAAOM,SAAS,CAACR,gBAAgB,CAACE,OAAO,CAAC,CAAD,CAAR,CAAjB,CAAhB;EACD;;EACD,OAAOQ,QAAP;AACD,CATD;;AAUA,eAAeC,mBAAf"}, "metadata": {}, "sourceType": "module"}