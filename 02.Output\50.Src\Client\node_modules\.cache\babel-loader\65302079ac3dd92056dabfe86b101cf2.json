{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    XhrDriver = require('../driver/xhr');\n\nfunction XHRLocalObject(method, url, payload\n/*, opts */\n) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\n\ninherits(XHRLocalObject, XhrDriver);\nXHRLocalObject.enabled = XhrDriver.enabled;\nmodule.exports = XHRLocalObject;", "map": {"version": 3, "names": ["inherits", "require", "XhrDriver", "XHRLocalObject", "method", "url", "payload", "call", "noCredentials", "enabled", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/sender/xhr-local.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\n\ninherits(XHRLocalObject, XhrDriver);\n\nXHRLocalObject.enabled = XhrDriver.enabled;\n\nmodule.exports = XHRLocalObject;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,eAAD,CADvB;;AAIA,SAASE,cAAT,CAAwBC,MAAxB,EAAgCC,GAAhC,EAAqCC;AAAQ;AAA7C,EAA0D;EACxDJ,SAAS,CAACK,IAAV,CAAe,IAAf,EAAqBH,MAArB,EAA6BC,GAA7B,EAAkCC,OAAlC,EAA2C;IACzCE,aAAa,EAAE;EAD0B,CAA3C;AAGD;;AAEDR,QAAQ,CAACG,cAAD,EAAiBD,SAAjB,CAAR;AAEAC,cAAc,CAACM,OAAf,GAAyBP,SAAS,CAACO,OAAnC;AAEAC,MAAM,CAACC,OAAP,GAAiBR,cAAjB"}, "metadata": {}, "sourceType": "script"}