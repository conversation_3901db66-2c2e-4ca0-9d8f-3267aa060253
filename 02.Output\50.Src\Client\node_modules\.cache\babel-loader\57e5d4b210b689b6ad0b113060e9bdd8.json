{"ast": null, "code": "import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\n\nvar usePollingPlugin = function usePollingPlugin(fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n      _b = _a.pollingWhenHidden,\n      pollingWhenHidden = _b === void 0 ? true : _b,\n      _c = _a.pollingErrorRetryCount,\n      pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef();\n  var unsubscribeRef = useRef();\n  var countRef = useRef(0);\n\n  var stopPolling = function stopPolling() {\n    var _a;\n\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n\n  if (!pollingInterval) {\n    return {};\n  }\n\n  return {\n    onBefore: function onBefore() {\n      stopPolling();\n    },\n    onError: function onError() {\n      countRef.current += 1;\n    },\n    onSuccess: function onSuccess() {\n      countRef.current = 0;\n    },\n    onFinally: function onFinally() {\n      if (pollingErrorRetryCount === -1 || // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function onCancel() {\n      stopPolling();\n    }\n  };\n};\n\nexport default usePollingPlugin;", "map": {"version": 3, "names": ["useRef", "useUpdateEffect", "isDocumentVisible", "subscribeReVisible", "usePollingPlugin", "fetchInstance", "_a", "pollingInterval", "_b", "pollingWhenHidden", "_c", "pollingErrorRetryCount", "timerRef", "unsubscribeRef", "countRef", "stopPolling", "current", "clearTimeout", "call", "onBefore", "onError", "onSuccess", "onFinally", "setTimeout", "refresh", "onCancel"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\nvar usePollingPlugin = function usePollingPlugin(fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n    _b = _a.pollingWhenHidden,\n    pollingWhenHidden = _b === void 0 ? true : _b,\n    _c = _a.pollingErrorRetryCount,\n    pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef();\n  var unsubscribeRef = useRef();\n  var countRef = useRef(0);\n  var stopPolling = function stopPolling() {\n    var _a;\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n  if (!pollingInterval) {\n    return {};\n  }\n  return {\n    onBefore: function onBefore() {\n      stopPolling();\n    },\n    onError: function onError() {\n      countRef.current += 1;\n    },\n    onSuccess: function onSuccess() {\n      countRef.current = 0;\n    },\n    onFinally: function onFinally() {\n      if (pollingErrorRetryCount === -1 ||\n      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function onCancel() {\n      stopPolling();\n    }\n  };\n};\nexport default usePollingPlugin;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,eAAP,MAA4B,0BAA5B;AACA,OAAOC,iBAAP,MAA8B,4BAA9B;AACA,OAAOC,kBAAP,MAA+B,6BAA/B;;AACA,IAAIC,gBAAgB,GAAG,SAASA,gBAAT,CAA0BC,aAA1B,EAAyCC,EAAzC,EAA6C;EAClE,IAAIC,eAAe,GAAGD,EAAE,CAACC,eAAzB;EAAA,IACEC,EAAE,GAAGF,EAAE,CAACG,iBADV;EAAA,IAEEA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EAF7C;EAAA,IAGEE,EAAE,GAAGJ,EAAE,CAACK,sBAHV;EAAA,IAIEA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAC,CAAjB,GAAqBA,EAJhD;EAKA,IAAIE,QAAQ,GAAGZ,MAAM,EAArB;EACA,IAAIa,cAAc,GAAGb,MAAM,EAA3B;EACA,IAAIc,QAAQ,GAAGd,MAAM,CAAC,CAAD,CAArB;;EACA,IAAIe,WAAW,GAAG,SAASA,WAAT,GAAuB;IACvC,IAAIT,EAAJ;;IACA,IAAIM,QAAQ,CAACI,OAAb,EAAsB;MACpBC,YAAY,CAACL,QAAQ,CAACI,OAAV,CAAZ;IACD;;IACD,CAACV,EAAE,GAAGO,cAAc,CAACG,OAArB,MAAkC,IAAlC,IAA0CV,EAAE,KAAK,KAAK,CAAtD,GAA0D,KAAK,CAA/D,GAAmEA,EAAE,CAACY,IAAH,CAAQL,cAAR,CAAnE;EACD,CAND;;EAOAZ,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACM,eAAL,EAAsB;MACpBQ,WAAW;IACZ;EACF,CAJc,EAIZ,CAACR,eAAD,CAJY,CAAf;;EAKA,IAAI,CAACA,eAAL,EAAsB;IACpB,OAAO,EAAP;EACD;;EACD,OAAO;IACLY,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5BJ,WAAW;IACZ,CAHI;IAILK,OAAO,EAAE,SAASA,OAAT,GAAmB;MAC1BN,QAAQ,CAACE,OAAT,IAAoB,CAApB;IACD,CANI;IAOLK,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9BP,QAAQ,CAACE,OAAT,GAAmB,CAAnB;IACD,CATI;IAULM,SAAS,EAAE,SAASA,SAAT,GAAqB;MAC9B,IAAIX,sBAAsB,KAAK,CAAC,CAA5B,IACJ;MACAA,sBAAsB,KAAK,CAAC,CAA5B,IAAiCG,QAAQ,CAACE,OAAT,IAAoBL,sBAFrD,EAE6E;QAC3EC,QAAQ,CAACI,OAAT,GAAmBO,UAAU,CAAC,YAAY;UACxC;UACA,IAAI,CAACd,iBAAD,IAAsB,CAACP,iBAAiB,EAA5C,EAAgD;YAC9CW,cAAc,CAACG,OAAf,GAAyBb,kBAAkB,CAAC,YAAY;cACtDE,aAAa,CAACmB,OAAd;YACD,CAF0C,CAA3C;UAGD,CAJD,MAIO;YACLnB,aAAa,CAACmB,OAAd;UACD;QACF,CAT4B,EAS1BjB,eAT0B,CAA7B;MAUD,CAbD,MAaO;QACLO,QAAQ,CAACE,OAAT,GAAmB,CAAnB;MACD;IACF,CA3BI;IA4BLS,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5BV,WAAW;IACZ;EA9BI,CAAP;AAgCD,CAxDD;;AAyDA,eAAeX,gBAAf"}, "metadata": {}, "sourceType": "module"}