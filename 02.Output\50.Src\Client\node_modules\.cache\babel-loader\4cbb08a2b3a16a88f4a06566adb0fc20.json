{"ast": null, "code": "var isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nexport default isDev;", "map": {"version": 3, "names": ["isDev", "process", "env", "NODE_ENV"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/isDev.js"], "sourcesContent": ["var isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nexport default isDev;"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,aAAzB,IAA0CF,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,MAA/E;AACA,eAAeH,KAAf"}, "metadata": {}, "sourceType": "module"}