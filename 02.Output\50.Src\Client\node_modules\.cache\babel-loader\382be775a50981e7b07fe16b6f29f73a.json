{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    AjaxBasedTransport = require('./lib/ajax-based'),\n    XhrReceiver = require('./receiver/xhr'),\n    XDRObject = require('./sender/xdr'); // According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\n\ninherits(XdrStreamingTransport, AjaxBasedTransport);\n\nXdrStreamingTransport.enabled = function (info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n\n  return XDRObject.enabled && info.sameScheme;\n};\n\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XhrReceiver", "XDRObject", "XdrStreamingTransport", "transUrl", "enabled", "Error", "call", "info", "cookie_needed", "<PERSON><PERSON><PERSON><PERSON>", "sameScheme", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/xdr-streaming.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\n\ninherits(XdrStreamingTransport, AjaxBasedTransport);\n\nXdrStreamingTransport.enabled = function(info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\n\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,kBAAkB,GAAGD,OAAO,CAAC,kBAAD,CADhC;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAAA,IAGIG,SAAS,GAAGH,OAAO,CAAC,cAAD,CAHvB,C,CAMA;AACA;AACA;;;AAEA,SAASI,qBAAT,CAA+BC,QAA/B,EAAyC;EACvC,IAAI,CAACF,SAAS,CAACG,OAAf,EAAwB;IACtB,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDN,kBAAkB,CAACO,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,gBAAxC,EAA0DH,WAA1D,EAAuEC,SAAvE;AACD;;AAEDJ,QAAQ,CAACK,qBAAD,EAAwBH,kBAAxB,CAAR;;AAEAG,qBAAqB,CAACE,OAAtB,GAAgC,UAASG,IAAT,EAAe;EAC7C,IAAIA,IAAI,CAACC,aAAL,IAAsBD,IAAI,CAACE,UAA/B,EAA2C;IACzC,OAAO,KAAP;EACD;;EACD,OAAOR,SAAS,CAACG,OAAV,IAAqBG,IAAI,CAACG,UAAjC;AACD,CALD;;AAOAR,qBAAqB,CAACS,aAAtB,GAAsC,eAAtC;AACAT,qBAAqB,CAACU,UAAtB,GAAmC,CAAnC,C,CAAsC;;AAEtCC,MAAM,CAACC,OAAP,GAAiBZ,qBAAjB"}, "metadata": {}, "sourceType": "script"}