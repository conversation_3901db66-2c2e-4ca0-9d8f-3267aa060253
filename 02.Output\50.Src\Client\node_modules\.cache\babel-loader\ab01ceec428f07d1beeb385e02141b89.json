{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\n\nvar useDrop = function useDrop(target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var optionsRef = useLatest(options); // https://stackoverflow.com/a/26459269\n\n  var dragEnterTarget = useRef();\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n\n    var onData = function onData(dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n\n        try {\n          data = JSON.parse(dom);\n        } catch (e) {\n          data = dom;\n        }\n\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n\n    var onDragEnter = function onDragEnter(event) {\n      var _a, _b;\n\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n\n    var onDragOver = function onDragOver(event) {\n      var _a, _b;\n\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n\n    var onDragLeave = function onDragLeave(event) {\n      var _a, _b;\n\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n\n    var onDrop = function onDrop(event) {\n      var _a, _b;\n\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n\n    var onPaste = function onPaste(event) {\n      var _a, _b;\n\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\n\nexport default useDrop;", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "useEffectWithTarget", "useRef", "useDrop", "target", "options", "optionsRef", "dragEnterTarget", "targetElement", "addEventListener", "onData", "dataTransfer", "event", "uri", "getData", "dom", "current", "onDom", "data", "JSON", "parse", "e", "onUri", "files", "length", "onFiles", "Array", "from", "items", "onText", "getAsString", "text", "onDragEnter", "_a", "_b", "preventDefault", "stopPropagation", "call", "onDragOver", "onDragLeave", "onDrop", "onPaste", "clipboardData", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useDrop/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\nvar useDrop = function useDrop(target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  // https://stackoverflow.com/a/26459269\n  var dragEnterTarget = useRef();\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onData = function onData(dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n        try {\n          data = JSON.parse(dom);\n        } catch (e) {\n          data = dom;\n        }\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n    var onDragEnter = function onDragEnter(event) {\n      var _a, _b;\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragOver = function onDragOver(event) {\n      var _a, _b;\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragLeave = function onDragLeave(event) {\n      var _a, _b;\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n    var onDrop = function onDrop(event) {\n      var _a, _b;\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onPaste = function onPaste(event) {\n      var _a, _b;\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\nexport default useDrop;"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;AACA,SAASC,MAAT,QAAuB,OAAvB;;AACA,IAAIC,OAAO,GAAG,SAASA,OAAT,CAAiBC,MAAjB,EAAyBC,OAAzB,EAAkC;EAC9C,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,UAAU,GAAGP,SAAS,CAACM,OAAD,CAA1B,CAJ8C,CAK9C;;EACA,IAAIE,eAAe,GAAGL,MAAM,EAA5B;EACAD,mBAAmB,CAAC,YAAY;IAC9B,IAAIO,aAAa,GAAGR,gBAAgB,CAACI,MAAD,CAApC;;IACA,IAAI,EAAEI,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACC,gBAA9E,CAAJ,EAAqG;MACnG;IACD;;IACD,IAAIC,MAAM,GAAG,SAASA,MAAT,CAAgBC,YAAhB,EAA8BC,KAA9B,EAAqC;MAChD,IAAIC,GAAG,GAAGF,YAAY,CAACG,OAAb,CAAqB,eAArB,CAAV;MACA,IAAIC,GAAG,GAAGJ,YAAY,CAACG,OAAb,CAAqB,QAArB,CAAV;;MACA,IAAIC,GAAG,IAAIT,UAAU,CAACU,OAAX,CAAmBC,KAA9B,EAAqC;QACnC,IAAIC,IAAI,GAAGH,GAAX;;QACA,IAAI;UACFG,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWL,GAAX,CAAP;QACD,CAFD,CAEE,OAAOM,CAAP,EAAU;UACVH,IAAI,GAAGH,GAAP;QACD;;QACDT,UAAU,CAACU,OAAX,CAAmBC,KAAnB,CAAyBC,IAAzB,EAA+BN,KAA/B;QACA;MACD;;MACD,IAAIC,GAAG,IAAIP,UAAU,CAACU,OAAX,CAAmBM,KAA9B,EAAqC;QACnChB,UAAU,CAACU,OAAX,CAAmBM,KAAnB,CAAyBT,GAAzB,EAA8BD,KAA9B;QACA;MACD;;MACD,IAAID,YAAY,CAACY,KAAb,IAAsBZ,YAAY,CAACY,KAAb,CAAmBC,MAAzC,IAAmDlB,UAAU,CAACU,OAAX,CAAmBS,OAA1E,EAAmF;QACjFnB,UAAU,CAACU,OAAX,CAAmBS,OAAnB,CAA2BC,KAAK,CAACC,IAAN,CAAWhB,YAAY,CAACY,KAAxB,CAA3B,EAA2DX,KAA3D;QACA;MACD;;MACD,IAAID,YAAY,CAACiB,KAAb,IAAsBjB,YAAY,CAACiB,KAAb,CAAmBJ,MAAzC,IAAmDlB,UAAU,CAACU,OAAX,CAAmBa,MAA1E,EAAkF;QAChFlB,YAAY,CAACiB,KAAb,CAAmB,CAAnB,EAAsBE,WAAtB,CAAkC,UAAUC,IAAV,EAAgB;UAChDzB,UAAU,CAACU,OAAX,CAAmBa,MAAnB,CAA0BE,IAA1B,EAAgCnB,KAAhC;QACD,CAFD;MAGD;IACF,CA1BD;;IA2BA,IAAIoB,WAAW,GAAG,SAASA,WAAT,CAAqBpB,KAArB,EAA4B;MAC5C,IAAIqB,EAAJ,EAAQC,EAAR;;MACAtB,KAAK,CAACuB,cAAN;MACAvB,KAAK,CAACwB,eAAN;MACA7B,eAAe,CAACS,OAAhB,GAA0BJ,KAAK,CAACR,MAAhC;MACA,CAAC8B,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAjB,EAA0BgB,WAAhC,MAAiD,IAAjD,IAAyDE,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAACG,IAAH,CAAQJ,EAAR,EAAYrB,KAAZ,CAAlF;IACD,CAND;;IAOA,IAAI0B,UAAU,GAAG,SAASA,UAAT,CAAoB1B,KAApB,EAA2B;MAC1C,IAAIqB,EAAJ,EAAQC,EAAR;;MACAtB,KAAK,CAACuB,cAAN;MACA,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAjB,EAA0BsB,UAAhC,MAAgD,IAAhD,IAAwDJ,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACG,IAAH,CAAQJ,EAAR,EAAYrB,KAAZ,CAAjF;IACD,CAJD;;IAKA,IAAI2B,WAAW,GAAG,SAASA,WAAT,CAAqB3B,KAArB,EAA4B;MAC5C,IAAIqB,EAAJ,EAAQC,EAAR;;MACA,IAAItB,KAAK,CAACR,MAAN,KAAiBG,eAAe,CAACS,OAArC,EAA8C;QAC5C,CAACkB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAjB,EAA0BuB,WAAhC,MAAiD,IAAjD,IAAyDL,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAACG,IAAH,CAAQJ,EAAR,EAAYrB,KAAZ,CAAlF;MACD;IACF,CALD;;IAMA,IAAI4B,MAAM,GAAG,SAASA,MAAT,CAAgB5B,KAAhB,EAAuB;MAClC,IAAIqB,EAAJ,EAAQC,EAAR;;MACAtB,KAAK,CAACuB,cAAN;MACAzB,MAAM,CAACE,KAAK,CAACD,YAAP,EAAqBC,KAArB,CAAN;MACA,CAACsB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAjB,EAA0BwB,MAAhC,MAA4C,IAA5C,IAAoDN,EAAE,KAAK,KAAK,CAAhE,GAAoE,KAAK,CAAzE,GAA6EA,EAAE,CAACG,IAAH,CAAQJ,EAAR,EAAYrB,KAAZ,CAA7E;IACD,CALD;;IAMA,IAAI6B,OAAO,GAAG,SAASA,OAAT,CAAiB7B,KAAjB,EAAwB;MACpC,IAAIqB,EAAJ,EAAQC,EAAR;;MACAxB,MAAM,CAACE,KAAK,CAAC8B,aAAP,EAAsB9B,KAAtB,CAAN;MACA,CAACsB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAjB,EAA0ByB,OAAhC,MAA6C,IAA7C,IAAqDP,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACG,IAAH,CAAQJ,EAAR,EAAYrB,KAAZ,CAA9E;IACD,CAJD;;IAKAJ,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4CuB,WAA5C;IACAxB,aAAa,CAACC,gBAAd,CAA+B,UAA/B,EAA2C6B,UAA3C;IACA9B,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4C8B,WAA5C;IACA/B,aAAa,CAACC,gBAAd,CAA+B,MAA/B,EAAuC+B,MAAvC;IACAhC,aAAa,CAACC,gBAAd,CAA+B,OAA/B,EAAwCgC,OAAxC;IACA,OAAO,YAAY;MACjBjC,aAAa,CAACmC,mBAAd,CAAkC,WAAlC,EAA+CX,WAA/C;MACAxB,aAAa,CAACmC,mBAAd,CAAkC,UAAlC,EAA8CL,UAA9C;MACA9B,aAAa,CAACmC,mBAAd,CAAkC,WAAlC,EAA+CJ,WAA/C;MACA/B,aAAa,CAACmC,mBAAd,CAAkC,MAAlC,EAA0CH,MAA1C;MACAhC,aAAa,CAACmC,mBAAd,CAAkC,OAAlC,EAA2CF,OAA3C;IACD,CAND;EAOD,CAzEkB,EAyEhB,EAzEgB,EAyEZrC,MAzEY,CAAnB;AA0ED,CAjFD;;AAkFA,eAAeD,OAAf"}, "metadata": {}, "sourceType": "module"}