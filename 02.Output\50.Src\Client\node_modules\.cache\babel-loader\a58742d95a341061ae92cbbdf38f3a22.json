{"ast": null, "code": "import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\n\nvar useMount = function useMount(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\n\nexport default useMount;", "map": {"version": 3, "names": ["useEffect", "isFunction", "isDev", "useMount", "fn", "console", "error", "concat"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useMount/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useMount = function useMount(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\nexport default useMount;"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,KAAP,MAAkB,gBAAlB;;AACA,IAAIC,QAAQ,GAAG,SAASA,QAAT,CAAkBC,EAAlB,EAAsB;EACnC,IAAIF,KAAJ,EAAW;IACT,IAAI,CAACD,UAAU,CAACG,EAAD,CAAf,EAAqB;MACnBC,OAAO,CAACC,KAAR,CAAc,iEAAiEC,MAAjE,CAAwE,OAAOH,EAA/E,EAAmF,KAAnF,CAAd;IACD;EACF;;EACDJ,SAAS,CAAC,YAAY;IACpBI,EAAE,KAAK,IAAP,IAAeA,EAAE,KAAK,KAAK,CAA3B,GAA+B,KAAK,CAApC,GAAwCA,EAAE,EAA1C;EACD,CAFQ,EAEN,EAFM,CAAT;AAGD,CATD;;AAUA,eAAeD,QAAf"}, "metadata": {}, "sourceType": "module"}