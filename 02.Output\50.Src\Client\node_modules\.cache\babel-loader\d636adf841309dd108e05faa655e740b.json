{"ast": null, "code": "/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean' ? value !== '__proto__' : value === null;\n}\n\nmodule.exports = isKeyable;", "map": {"version": 3, "names": ["isKeyable", "value", "type", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_isKeyable.js"], "sourcesContent": ["/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\nmodule.exports = isKeyable;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,KAAnB,EAA0B;EACxB,IAAIC,IAAI,GAAG,OAAOD,KAAlB;EACA,OAAQC,IAAI,IAAI,QAAR,IAAoBA,IAAI,IAAI,QAA5B,IAAwCA,IAAI,IAAI,QAAhD,IAA4DA,IAAI,IAAI,SAArE,GACFD,KAAK,KAAK,WADR,GAEFA,KAAK,KAAK,IAFf;AAGD;;AAEDE,MAAM,CAACC,OAAP,GAAiBJ,SAAjB"}, "metadata": {}, "sourceType": "script"}