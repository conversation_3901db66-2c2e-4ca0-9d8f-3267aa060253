{"ast": null, "code": "var getNative = require('./_getNative'),\n    root = require('./_root');\n/* Built-in method references that are verified to be native. */\n\n\nvar WeakMap = getNative(root, 'WeakMap');\nmodule.exports = WeakMap;", "map": {"version": 3, "names": ["getNative", "require", "root", "WeakMap", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_WeakMap.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\n\nmodule.exports = WeakMap;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,IAAI,GAAGD,OAAO,CAAC,SAAD,CADlB;AAGA;;;AACA,IAAIE,OAAO,GAAGH,SAAS,CAACE,IAAD,EAAO,SAAP,CAAvB;AAEAE,MAAM,CAACC,OAAP,GAAiBF,OAAjB"}, "metadata": {}, "sourceType": "script"}