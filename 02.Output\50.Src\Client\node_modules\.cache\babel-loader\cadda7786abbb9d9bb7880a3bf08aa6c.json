{"ast": null, "code": "'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};", "map": {"version": 3, "names": ["silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/defaults/transitional.js"], "sourcesContent": ["'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};\n"], "mappings": "AAAA;;AAEA,eAAe;EACbA,iBAAiB,EAAE,IADN;EAEbC,iBAAiB,EAAE,IAFN;EAGbC,mBAAmB,EAAE;AAHR,CAAf"}, "metadata": {}, "sourceType": "module"}