{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n      isFocusWithin = _a[0],\n      setIsFocusWithin = _a[1];\n\n  var _b = options || {},\n      onFocus = _b.onFocus,\n      onBlur = _b.onBlur,\n      onChange = _b.onChange;\n\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useEventListener", "useFocusWithin", "target", "options", "_a", "isFocusWithin", "setIsFocusWithin", "_b", "onFocus", "onBlur", "onChange", "currentTarget", "contains", "relatedTarget"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useFocusWithin/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n    isFocusWithin = _a[0],\n    setIsFocusWithin = _a[1];\n  var _b = options || {},\n    onFocus = _b.onFocus,\n    onBlur = _b.onBlur,\n    onChange = _b.onChange;\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,eAAe,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,OAAhC,EAAyC;EACtD,IAAIC,EAAE,GAAGrB,MAAM,CAACgB,QAAQ,CAAC,KAAD,CAAT,EAAkB,CAAlB,CAAf;EAAA,IACEM,aAAa,GAAGD,EAAE,CAAC,CAAD,CADpB;EAAA,IAEEE,gBAAgB,GAAGF,EAAE,CAAC,CAAD,CAFvB;;EAGA,IAAIG,EAAE,GAAGJ,OAAO,IAAI,EAApB;EAAA,IACEK,OAAO,GAAGD,EAAE,CAACC,OADf;EAAA,IAEEC,MAAM,GAAGF,EAAE,CAACE,MAFd;EAAA,IAGEC,QAAQ,GAAGH,EAAE,CAACG,QAHhB;;EAIAV,gBAAgB,CAAC,SAAD,EAAY,UAAUP,CAAV,EAAa;IACvC,IAAI,CAACY,aAAL,EAAoB;MAClBG,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACf,CAAD,CAAzD;MACAiB,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC,IAAD,CAA5D;MACAJ,gBAAgB,CAAC,IAAD,CAAhB;IACD;EACF,CANe,EAMb;IACDJ,MAAM,EAAEA;EADP,CANa,CAAhB;EASAF,gBAAgB,CAAC,UAAD,EAAa,UAAUP,CAAV,EAAa;IACxC,IAAIW,EAAJ,EAAQG,EAAR;;IACA,IAAIF,aAAa,IAAI,EAAE,CAACE,EAAE,GAAG,CAACH,EAAE,GAAGX,CAAC,CAACkB,aAAR,MAA2B,IAA3B,IAAmCP,EAAE,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,EAAE,CAACQ,QAArE,MAAmF,IAAnF,IAA2FL,EAAE,KAAK,KAAK,CAAvG,GAA2G,KAAK,CAAhH,GAAoHA,EAAE,CAACjB,IAAH,CAAQc,EAAR,EAAYX,CAAC,CAACoB,aAAd,CAAtH,CAArB,EAA0K;MACxKJ,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAChB,CAAD,CAAtD;MACAiB,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC,KAAD,CAA5D;MACAJ,gBAAgB,CAAC,KAAD,CAAhB;IACD;EACF,CAPe,EAOb;IACDJ,MAAM,EAAEA;EADP,CAPa,CAAhB;EAUA,OAAOG,aAAP;AACD"}, "metadata": {}, "sourceType": "module"}