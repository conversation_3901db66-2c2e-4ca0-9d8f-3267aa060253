{"ast": null, "code": "'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\n\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError();\n  }\n}\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\n\n\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n  config.headers = AxiosHeaders.from(config.headers); // Transform request data\n\n  config.data = transformData.call(config, config.transformRequest);\n  var adapter = config.adapter || defaults.adapter;\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config); // Transform response data\n\n    response.data = transformData.call(config, config.transformResponse, response);\n    response.headers = AxiosHeaders.from(response.headers);\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config); // Transform response data\n\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(config, config.transformResponse, reason.response);\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n}", "map": {"version": 3, "names": ["transformData", "isCancel", "defaults", "CanceledError", "AxiosHeaders", "throwIfCancellationRequested", "config", "cancelToken", "throwIfRequested", "signal", "aborted", "dispatchRequest", "headers", "from", "data", "call", "transformRequest", "adapter", "then", "onAdapterResolution", "response", "transformResponse", "onAdapterRejection", "reason", "Promise", "reject"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/dispatchRequest.js"], "sourcesContent": ["'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError();\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  config.headers = AxiosHeaders.from(config.headers);\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.transformRequest\n  );\n\n  const adapter = config.adapter || defaults.adapter;\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      config.transformResponse,\n      response\n    );\n\n    response.headers = AxiosHeaders.from(response.headers);\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          config.transformResponse,\n          reason.response\n        );\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,aAAP,MAA0B,oBAA1B;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAOC,aAAP,MAA0B,4BAA1B;AACA,OAAOC,YAAP,MAAyB,yBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,4BAAT,CAAsCC,MAAtC,EAA8C;EAC5C,IAAIA,MAAM,CAACC,WAAX,EAAwB;IACtBD,MAAM,CAACC,WAAP,CAAmBC,gBAAnB;EACD;;EAED,IAAIF,MAAM,CAACG,MAAP,IAAiBH,MAAM,CAACG,MAAP,CAAcC,OAAnC,EAA4C;IAC1C,MAAM,IAAIP,aAAJ,EAAN;EACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,eAAe,SAASQ,eAAT,CAAyBL,MAAzB,EAAiC;EAC9CD,4BAA4B,CAACC,MAAD,CAA5B;EAEAA,MAAM,CAACM,OAAP,GAAiBR,YAAY,CAACS,IAAb,CAAkBP,MAAM,CAACM,OAAzB,CAAjB,CAH8C,CAK9C;;EACAN,MAAM,CAACQ,IAAP,GAAcd,aAAa,CAACe,IAAd,CACZT,MADY,EAEZA,MAAM,CAACU,gBAFK,CAAd;EAKA,IAAMC,OAAO,GAAGX,MAAM,CAACW,OAAP,IAAkBf,QAAQ,CAACe,OAA3C;EAEA,OAAOA,OAAO,CAACX,MAAD,CAAP,CAAgBY,IAAhB,CAAqB,SAASC,mBAAT,CAA6BC,QAA7B,EAAuC;IACjEf,4BAA4B,CAACC,MAAD,CAA5B,CADiE,CAGjE;;IACAc,QAAQ,CAACN,IAAT,GAAgBd,aAAa,CAACe,IAAd,CACdT,MADc,EAEdA,MAAM,CAACe,iBAFO,EAGdD,QAHc,CAAhB;IAMAA,QAAQ,CAACR,OAAT,GAAmBR,YAAY,CAACS,IAAb,CAAkBO,QAAQ,CAACR,OAA3B,CAAnB;IAEA,OAAOQ,QAAP;EACD,CAbM,EAaJ,SAASE,kBAAT,CAA4BC,MAA5B,EAAoC;IACrC,IAAI,CAACtB,QAAQ,CAACsB,MAAD,CAAb,EAAuB;MACrBlB,4BAA4B,CAACC,MAAD,CAA5B,CADqB,CAGrB;;MACA,IAAIiB,MAAM,IAAIA,MAAM,CAACH,QAArB,EAA+B;QAC7BG,MAAM,CAACH,QAAP,CAAgBN,IAAhB,GAAuBd,aAAa,CAACe,IAAd,CACrBT,MADqB,EAErBA,MAAM,CAACe,iBAFc,EAGrBE,MAAM,CAACH,QAHc,CAAvB;QAKAG,MAAM,CAACH,QAAP,CAAgBR,OAAhB,GAA0BR,YAAY,CAACS,IAAb,CAAkBU,MAAM,CAACH,QAAP,CAAgBR,OAAlC,CAA1B;MACD;IACF;;IAED,OAAOY,OAAO,CAACC,MAAR,CAAeF,MAAf,CAAP;EACD,CA7BM,CAAP;AA8BD"}, "metadata": {}, "sourceType": "module"}