{"ast": null, "code": "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar aCallable = require('../internals/a-callable');\n\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar bind = uncurryThis(uncurryThis.bind); // optional / simple context binding\n\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : NATIVE_BIND ? bind(fn, that) : function\n    /* ...args */\n  () {\n    return fn.apply(that, arguments);\n  };\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "aCallable", "NATIVE_BIND", "bind", "module", "exports", "fn", "that", "undefined", "apply", "arguments"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/core-js-pure/internals/function-bind-context.js"], "sourcesContent": ["var uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : NATIVE_BIND ? bind(fn, that) : function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAD,CAAzB;;AACA,IAAIC,SAAS,GAAGD,OAAO,CAAC,yBAAD,CAAvB;;AACA,IAAIE,WAAW,GAAGF,OAAO,CAAC,mCAAD,CAAzB;;AAEA,IAAIG,IAAI,GAAGJ,WAAW,CAACA,WAAW,CAACI,IAAb,CAAtB,C,CAEA;;AACAC,MAAM,CAACC,OAAP,GAAiB,UAAUC,EAAV,EAAcC,IAAd,EAAoB;EACnCN,SAAS,CAACK,EAAD,CAAT;EACA,OAAOC,IAAI,KAAKC,SAAT,GAAqBF,EAArB,GAA0BJ,WAAW,GAAGC,IAAI,CAACG,EAAD,EAAKC,IAAL,CAAP,GAAoB;IAAU;EAAV,GAAyB;IACvF,OAAOD,EAAE,CAACG,KAAH,CAASF,IAAT,EAAeG,SAAf,CAAP;EACD,CAFD;AAGD,CALD"}, "metadata": {}, "sourceType": "script"}