{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventTarget = require('./eventtarget');\n\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\n\ninherits(EventEmitter, EventTarget);\n\nEventEmitter.prototype.removeAllListeners = function (type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\n\nEventEmitter.prototype.once = function (type, listener) {\n  var self = this,\n      fired = false;\n\n  function g() {\n    self.removeListener(type, g);\n\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n\n  this.on(type, g);\n};\n\nEventEmitter.prototype.emit = function () {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n\n  if (!listeners) {\n    return;\n  } // equivalent of Array.prototype.slice.call(arguments, 1);\n\n\n  var l = arguments.length;\n  var args = new Array(l - 1);\n\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\nmodule.exports.EventEmitter = EventEmitter;", "map": {"version": 3, "names": ["inherits", "require", "EventTarget", "EventEmitter", "call", "prototype", "removeAllListeners", "type", "_listeners", "once", "listener", "self", "fired", "g", "removeListener", "apply", "arguments", "on", "emit", "listeners", "l", "length", "args", "Array", "ai", "i", "addListener", "addEventListener", "removeEventListener", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/event/emitter.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventTarget = require('./eventtarget')\n  ;\n\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\n\ninherits(EventEmitter, EventTarget);\n\nEventEmitter.prototype.removeAllListeners = function(type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\n\nEventEmitter.prototype.once = function(type, listener) {\n  var self = this\n    , fired = false;\n\n  function g() {\n    self.removeListener(type, g);\n\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n\n  this.on(type, g);\n};\n\nEventEmitter.prototype.emit = function() {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\n\nmodule.exports.EventEmitter = EventEmitter;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,WAAW,GAAGD,OAAO,CAAC,eAAD,CADzB;;AAIA,SAASE,YAAT,GAAwB;EACtBD,WAAW,CAACE,IAAZ,CAAiB,IAAjB;AACD;;AAEDJ,QAAQ,CAACG,YAAD,EAAeD,WAAf,CAAR;;AAEAC,YAAY,CAACE,SAAb,CAAuBC,kBAAvB,GAA4C,UAASC,IAAT,EAAe;EACzD,IAAIA,IAAJ,EAAU;IACR,OAAO,KAAKC,UAAL,CAAgBD,IAAhB,CAAP;EACD,CAFD,MAEO;IACL,KAAKC,UAAL,GAAkB,EAAlB;EACD;AACF,CAND;;AAQAL,YAAY,CAACE,SAAb,CAAuBI,IAAvB,GAA8B,UAASF,IAAT,EAAeG,QAAf,EAAyB;EACrD,IAAIC,IAAI,GAAG,IAAX;EAAA,IACIC,KAAK,GAAG,KADZ;;EAGA,SAASC,CAAT,GAAa;IACXF,IAAI,CAACG,cAAL,CAAoBP,IAApB,EAA0BM,CAA1B;;IAEA,IAAI,CAACD,KAAL,EAAY;MACVA,KAAK,GAAG,IAAR;MACAF,QAAQ,CAACK,KAAT,CAAe,IAAf,EAAqBC,SAArB;IACD;EACF;;EAED,KAAKC,EAAL,CAAQV,IAAR,EAAcM,CAAd;AACD,CAdD;;AAgBAV,YAAY,CAACE,SAAb,CAAuBa,IAAvB,GAA8B,YAAW;EACvC,IAAIX,IAAI,GAAGS,SAAS,CAAC,CAAD,CAApB;EACA,IAAIG,SAAS,GAAG,KAAKX,UAAL,CAAgBD,IAAhB,CAAhB;;EACA,IAAI,CAACY,SAAL,EAAgB;IACd;EACD,CALsC,CAMvC;;;EACA,IAAIC,CAAC,GAAGJ,SAAS,CAACK,MAAlB;EACA,IAAIC,IAAI,GAAG,IAAIC,KAAJ,CAAUH,CAAC,GAAG,CAAd,CAAX;;EACA,KAAK,IAAII,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGJ,CAAtB,EAAyBI,EAAE,EAA3B,EAA+B;IAC7BF,IAAI,CAACE,EAAE,GAAG,CAAN,CAAJ,GAAeR,SAAS,CAACQ,EAAD,CAAxB;EACD;;EACD,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,SAAS,CAACE,MAA9B,EAAsCI,CAAC,EAAvC,EAA2C;IACzCN,SAAS,CAACM,CAAD,CAAT,CAAaV,KAAb,CAAmB,IAAnB,EAAyBO,IAAzB;EACD;AACF,CAfD;;AAiBAnB,YAAY,CAACE,SAAb,CAAuBY,EAAvB,GAA4Bd,YAAY,CAACE,SAAb,CAAuBqB,WAAvB,GAAqCxB,WAAW,CAACG,SAAZ,CAAsBsB,gBAAvF;AACAxB,YAAY,CAACE,SAAb,CAAuBS,cAAvB,GAAwCZ,WAAW,CAACG,SAAZ,CAAsBuB,mBAA9D;AAEAC,MAAM,CAACC,OAAP,CAAe3B,YAAf,GAA8BA,YAA9B"}, "metadata": {}, "sourceType": "script"}