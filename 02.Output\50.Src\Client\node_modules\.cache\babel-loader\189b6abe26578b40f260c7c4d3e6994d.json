{"ast": null, "code": "/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nmodule.exports = getValue;", "map": {"version": 3, "names": ["getValue", "object", "key", "undefined", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getValue.js"], "sourcesContent": ["/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nmodule.exports = getValue;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAT,CAAkBC,MAAlB,EAA0BC,GAA1B,EAA+B;EAC7B,OAAOD,MAAM,IAAI,IAAV,GAAiBE,SAAjB,GAA6BF,MAAM,CAACC,GAAD,CAA1C;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}