{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource,getHexColor}from'../utils/Util.js';/**\r\n * 救急車稼働率コンテンツ<br>\r\n * propsは、「3.18救急車稼働率コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module AmbulanceRate\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var AmbulanceRate=function AmbulanceRate(props){if(!isValidSource(props)){return;}var title=props.title;var dispatch_num=props.dispatch_num;var totalStyle={backgroundColor:getHexColor(title===null||title===void 0?void 0:title.background_color)};var row1Cell1Props=getCellFace(title,'mt-[2.3rem] text-10xl col-start-2 col-span-5  justify-self-center');var row2Cell1Props=getCellFace(dispatch_num,'mt-[1.5rem] col-start-2 col-span-4 justify-self-end');row2Cell1Props.text=detachUnit(row2Cell1Props.text);return/*#__PURE__*/_jsx(\"div\",{style:totalStyle,className:\"h-full w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 grid-rows-2 place-items-center leading-[1] gap-y-[3rem]\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(5,10rem)_minmax(0.25rem,2fr)]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell1Props))}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(4,13rem)_minmax(0.25rem,2fr)] text-[23rem]\",children:/*#__PURE__*/_jsxs(CellBox,_objectSpread(_objectSpread({},row2Cell1Props),{},{children:[row2Cell1Props.text,/*#__PURE__*/_jsx(\"span\",{className:\"text-12xl\",children:\"\\u53F0\"})]}))})]})});};/**\r\n * 単位の「台」なくして返す\r\n *\r\n * @export\r\n * @param {*} num\r\n * @return {*}　「台」なくす文字列\r\n */function detachUnit(num){if(num==null){return num;}if(num.endsWith('台')){return num.substring(0,num.length-1);}else{return num;}}export default AmbulanceRate;", "map": {"version": 3, "names": ["React", "Cell", "CellBox", "getCellFace", "isValidSource", "getHexColor", "AmbulanceRate", "props", "title", "dispatch_num", "totalStyle", "backgroundColor", "background_color", "row1Cell1Props", "row2Cell1Props", "text", "detachUnit", "num", "endsWith", "substring", "length"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/AmbulanceRate.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';\r\n\r\n/**\r\n * 救急車稼働率コンテンツ<br>\r\n * propsは、「3.18救急車稼働率コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module AmbulanceRate\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst AmbulanceRate = (props) => {\r\n  if (!isValidSource(props)) {\r\n    return;\r\n  }\r\n\r\n  const title = props.title;\r\n  const dispatch_num = props.dispatch_num;\r\n\r\n  let totalStyle = { backgroundColor: getHexColor(title?.background_color) };\r\n\r\n  let row1Cell1Props = getCellFace(title, 'mt-[2.3rem] text-10xl col-start-2 col-span-5  justify-self-center');\r\n  let row2Cell1Props = getCellFace(\r\n    dispatch_num,\r\n    'mt-[1.5rem] col-start-2 col-span-4 justify-self-end'\r\n  );\r\n  row2Cell1Props.text = detachUnit(row2Cell1Props.text);\r\n\r\n  return (\r\n    <div style={totalStyle} className=\"h-full w-full\">\r\n      <div className=\"grid grid-cols-1 grid-rows-2 place-items-center leading-[1] gap-y-[3rem]\">\r\n\t\t<div className=\"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(5,10rem)_minmax(0.25rem,2fr)]\">\r\n\t\t  <Cell {...row1Cell1Props} />\r\n\t\t</div>\r\n        <div className=\"grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(4,13rem)_minmax(0.25rem,2fr)] text-[23rem]\">\r\n          <CellBox {...row2Cell1Props} >\r\n            {row2Cell1Props.text}\r\n            <span className=\"text-12xl\">台</span>\r\n          </CellBox>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * 単位の「台」なくして返す\r\n *\r\n * @export\r\n * @param {*} num\r\n * @return {*}　「台」なくす文字列\r\n */\r\nfunction detachUnit(num) {\r\n  if (num == null) {\r\n    return num;\r\n  }\r\n\r\n  if (num.endsWith('台')) {\r\n    return num.substring(0, num.length - 1);\r\n  } else {\r\n    return num;\r\n  }\r\n}\r\n\r\nexport default AmbulanceRate;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,wFACA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACC,KAAD,CAAW,CAC/B,GAAI,CAACH,aAAa,CAACG,KAAD,CAAlB,CAA2B,CACzB,OACD,CAED,GAAMC,MAAK,CAAGD,KAAK,CAACC,KAApB,CACA,GAAMC,aAAY,CAAGF,KAAK,CAACE,YAA3B,CAEA,GAAIC,WAAU,CAAG,CAAEC,eAAe,CAAEN,WAAW,CAACG,KAAD,SAACA,KAAD,iBAACA,KAAK,CAAEI,gBAAR,CAA9B,CAAjB,CAEA,GAAIC,eAAc,CAAGV,WAAW,CAACK,KAAD,CAAQ,mEAAR,CAAhC,CACA,GAAIM,eAAc,CAAGX,WAAW,CAC9BM,YAD8B,CAE9B,qDAF8B,CAAhC,CAIAK,cAAc,CAACC,IAAf,CAAsBC,UAAU,CAACF,cAAc,CAACC,IAAhB,CAAhC,CAEA,mBACE,YAAK,KAAK,CAAEL,UAAZ,CAAwB,SAAS,CAAC,eAAlC,uBACE,aAAK,SAAS,CAAC,0EAAf,wBACJ,YAAK,SAAS,CAAC,+EAAf,uBACE,KAAC,IAAD,kBAAUG,cAAV,EADF,EADI,cAIE,YAAK,SAAS,CAAC,4FAAf,uBACE,MAAC,OAAD,gCAAaC,cAAb,gBACGA,cAAc,CAACC,IADlB,cAEE,aAAM,SAAS,CAAC,WAAhB,oBAFF,IADF,EAJF,GADF,EADF,CAeD,CAhCD,CAkCA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,QAASC,WAAT,CAAoBC,GAApB,CAAyB,CACvB,GAAIA,GAAG,EAAI,IAAX,CAAiB,CACf,MAAOA,IAAP,CACD,CAED,GAAIA,GAAG,CAACC,QAAJ,CAAa,GAAb,CAAJ,CAAuB,CACrB,MAAOD,IAAG,CAACE,SAAJ,CAAc,CAAd,CAAiBF,GAAG,CAACG,MAAJ,CAAa,CAA9B,CAAP,CACD,CAFD,IAEO,CACL,MAAOH,IAAP,CACD,CACF,CAED,cAAeX,cAAf"}, "metadata": {}, "sourceType": "module"}