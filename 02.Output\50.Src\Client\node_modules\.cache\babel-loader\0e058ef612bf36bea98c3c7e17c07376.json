{"ast": null, "code": "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nvar adapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n};\nexport default {\n  getAdapter: function getAdapter(nameOrAdapter) {\n    if (utils.isString(nameOrAdapter)) {\n      var adapter = adapters[nameOrAdapter];\n\n      if (!nameOrAdapter) {\n        throw Error(utils.hasOwnProp(nameOrAdapter) ? \"Adapter '\".concat(nameOrAdapter, \"' is not available in the build\") : \"Can not resolve adapter '\".concat(nameOrAdapter, \"'\"));\n      }\n\n      return adapter;\n    }\n\n    if (!utils.isFunction(nameOrAdapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n\n    return nameOrAdapter;\n  },\n  adapters: adapters\n};", "map": {"version": 3, "names": ["utils", "httpAdapter", "xhrAdapter", "adapters", "http", "xhr", "getAdapter", "nameOrAdapter", "isString", "adapter", "Error", "hasOwnProp", "isFunction", "TypeError"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/adapters/index.js"], "sourcesContent": ["import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\n\nconst adapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter\n}\n\nexport default {\n  getAdapter: (nameOrAdapter) => {\n    if(utils.isString(nameOrAdapter)){\n      const adapter = adapters[nameOrAdapter];\n\n      if (!nameOrAdapter) {\n        throw Error(\n          utils.hasOwnProp(nameOrAdapter) ?\n            `Adapter '${nameOrAdapter}' is not available in the build` :\n            `Can not resolve adapter '${nameOrAdapter}'`\n        );\n      }\n\n      return adapter\n    }\n\n    if (!utils.isFunction(nameOrAdapter)) {\n      throw new TypeError('adapter is not a function');\n    }\n\n    return nameOrAdapter;\n  },\n  adapters\n}\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,WAAP,MAAwB,WAAxB;AACA,OAAOC,UAAP,MAAuB,UAAvB;AAEA,IAAMC,QAAQ,GAAG;EACfC,IAAI,EAAEH,WADS;EAEfI,GAAG,EAAEH;AAFU,CAAjB;AAKA,eAAe;EACbI,UAAU,EAAE,oBAACC,aAAD,EAAmB;IAC7B,IAAGP,KAAK,CAACQ,QAAN,CAAeD,aAAf,CAAH,EAAiC;MAC/B,IAAME,OAAO,GAAGN,QAAQ,CAACI,aAAD,CAAxB;;MAEA,IAAI,CAACA,aAAL,EAAoB;QAClB,MAAMG,KAAK,CACTV,KAAK,CAACW,UAAN,CAAiBJ,aAAjB,uBACcA,aADd,0EAE8BA,aAF9B,MADS,CAAX;MAKD;;MAED,OAAOE,OAAP;IACD;;IAED,IAAI,CAACT,KAAK,CAACY,UAAN,CAAiBL,aAAjB,CAAL,EAAsC;MACpC,MAAM,IAAIM,SAAJ,CAAc,2BAAd,CAAN;IACD;;IAED,OAAON,aAAP;EACD,CArBY;EAsBbJ,QAAQ,EAARA;AAtBa,CAAf"}, "metadata": {}, "sourceType": "module"}