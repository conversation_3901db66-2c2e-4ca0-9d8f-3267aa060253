{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n    isObject = require('./isObject');\n/** `Object#toString` result references. */\n\n\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\n\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  } // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n\n\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nmodule.exports = isFunction;", "map": {"version": 3, "names": ["baseGetTag", "require", "isObject", "asyncTag", "funcTag", "genTag", "proxyTag", "isFunction", "value", "tag", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/isFunction.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObject = require('./isObject');\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nmodule.exports = isFunction;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,YAAD,CADtB;AAGA;;;AACA,IAAIE,QAAQ,GAAG,wBAAf;AAAA,IACIC,OAAO,GAAG,mBADd;AAAA,IAEIC,MAAM,GAAG,4BAFb;AAAA,IAGIC,QAAQ,GAAG,gBAHf;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,UAAT,CAAoBC,KAApB,EAA2B;EACzB,IAAI,CAACN,QAAQ,CAACM,KAAD,CAAb,EAAsB;IACpB,OAAO,KAAP;EACD,CAHwB,CAIzB;EACA;;;EACA,IAAIC,GAAG,GAAGT,UAAU,CAACQ,KAAD,CAApB;EACA,OAAOC,GAAG,IAAIL,OAAP,IAAkBK,GAAG,IAAIJ,MAAzB,IAAmCI,GAAG,IAAIN,QAA1C,IAAsDM,GAAG,IAAIH,QAApE;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiBJ,UAAjB"}, "metadata": {}, "sourceType": "script"}