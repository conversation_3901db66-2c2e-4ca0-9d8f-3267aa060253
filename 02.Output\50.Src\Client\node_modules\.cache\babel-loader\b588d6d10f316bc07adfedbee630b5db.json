{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\n\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = new Error().stack;\n  }\n\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  response && (this.response = response);\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code,\n      status: this.response && this.response.status ? this.response.status : null\n    };\n  }\n});\nvar prototype = AxiosError.prototype;\nvar descriptors = {};\n['ERR_BAD_OPTION_VALUE', 'ERR_BAD_OPTION', 'ECONNABORTED', 'ETIMEDOUT', 'ERR_NETWORK', 'ERR_FR_TOO_MANY_REDIRECTS', 'ERR_DEPRECATED', 'ERR_BAD_RESPONSE', 'ERR_BAD_REQUEST', 'ERR_CANCELED', 'ERR_NOT_SUPPORT', 'ERR_INVALID_URL' // eslint-disable-next-line func-names\n].forEach(function (code) {\n  descriptors[code] = {\n    value: code\n  };\n});\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {\n  value: true\n}); // eslint-disable-next-line func-names\n\nAxiosError.from = function (error, code, config, request, response, customProps) {\n  var axiosError = Object.create(prototype);\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, function (prop) {\n    return prop !== 'isAxiosError';\n  });\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n  axiosError.cause = error;\n  axiosError.name = error.name;\n  customProps && Object.assign(axiosError, customProps);\n  return axiosError;\n};\n\nexport default AxiosError;", "map": {"version": 3, "names": ["utils", "AxiosError", "message", "code", "config", "request", "response", "Error", "call", "captureStackTrace", "constructor", "stack", "name", "inherits", "toJSON", "description", "number", "fileName", "lineNumber", "columnNumber", "status", "prototype", "descriptors", "for<PERSON>ach", "value", "Object", "defineProperties", "defineProperty", "from", "error", "customProps", "axiosError", "create", "toFlatObject", "filter", "obj", "prop", "cause", "assign"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/AxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = (new Error()).stack;\n  }\n\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  response && (this.response = response);\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code,\n      status: this.response && this.response.status ? this.response.status : null\n    };\n  }\n});\n\nconst prototype = AxiosError.prototype;\nconst descriptors = {};\n\n[\n  'ERR_BAD_OPTION_VALUE',\n  'ERR_BAD_OPTION',\n  'ECONNABORTED',\n  'ETIMEDOUT',\n  'ERR_NETWORK',\n  'ERR_FR_TOO_MANY_REDIRECTS',\n  'ERR_DEPRECATED',\n  'ERR_BAD_RESPONSE',\n  'ERR_BAD_REQUEST',\n  'ERR_CANCELED',\n  'ERR_NOT_SUPPORT',\n  'ERR_INVALID_URL'\n// eslint-disable-next-line func-names\n].forEach(code => {\n  descriptors[code] = {value: code};\n});\n\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {value: true});\n\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps) => {\n  const axiosError = Object.create(prototype);\n\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, prop => {\n    return prop !== 'isAxiosError';\n  });\n\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n\n  axiosError.cause = error;\n\n  axiosError.name = error.name;\n\n  customProps && Object.assign(axiosError, customProps);\n\n  return axiosError;\n};\n\nexport default AxiosError;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,IAA7B,EAAmCC,MAAnC,EAA2CC,OAA3C,EAAoDC,QAApD,EAA8D;EAC5DC,KAAK,CAACC,IAAN,CAAW,IAAX;;EAEA,IAAID,KAAK,CAACE,iBAAV,EAA6B;IAC3BF,KAAK,CAACE,iBAAN,CAAwB,IAAxB,EAA8B,KAAKC,WAAnC;EACD,CAFD,MAEO;IACL,KAAKC,KAAL,GAAc,IAAIJ,KAAJ,EAAD,CAAcI,KAA3B;EACD;;EAED,KAAKT,OAAL,GAAeA,OAAf;EACA,KAAKU,IAAL,GAAY,YAAZ;EACAT,IAAI,KAAK,KAAKA,IAAL,GAAYA,IAAjB,CAAJ;EACAC,MAAM,KAAK,KAAKA,MAAL,GAAcA,MAAnB,CAAN;EACAC,OAAO,KAAK,KAAKA,OAAL,GAAeA,OAApB,CAAP;EACAC,QAAQ,KAAK,KAAKA,QAAL,GAAgBA,QAArB,CAAR;AACD;;AAEDN,KAAK,CAACa,QAAN,CAAeZ,UAAf,EAA2BM,KAA3B,EAAkC;EAChCO,MAAM,EAAE,SAASA,MAAT,GAAkB;IACxB,OAAO;MACL;MACAZ,OAAO,EAAE,KAAKA,OAFT;MAGLU,IAAI,EAAE,KAAKA,IAHN;MAIL;MACAG,WAAW,EAAE,KAAKA,WALb;MAMLC,MAAM,EAAE,KAAKA,MANR;MAOL;MACAC,QAAQ,EAAE,KAAKA,QARV;MASLC,UAAU,EAAE,KAAKA,UATZ;MAULC,YAAY,EAAE,KAAKA,YAVd;MAWLR,KAAK,EAAE,KAAKA,KAXP;MAYL;MACAP,MAAM,EAAE,KAAKA,MAbR;MAcLD,IAAI,EAAE,KAAKA,IAdN;MAeLiB,MAAM,EAAE,KAAKd,QAAL,IAAiB,KAAKA,QAAL,CAAcc,MAA/B,GAAwC,KAAKd,QAAL,CAAcc,MAAtD,GAA+D;IAflE,CAAP;EAiBD;AAnB+B,CAAlC;AAsBA,IAAMC,SAAS,GAAGpB,UAAU,CAACoB,SAA7B;AACA,IAAMC,WAAW,GAAG,EAApB;AAEA,CACE,sBADF,EAEE,gBAFF,EAGE,cAHF,EAIE,WAJF,EAKE,aALF,EAME,2BANF,EAOE,gBAPF,EAQE,kBARF,EASE,iBATF,EAUE,cAVF,EAWE,iBAXF,EAYE,iBAZF,CAaA;AAbA,EAcEC,OAdF,CAcU,UAAApB,IAAI,EAAI;EAChBmB,WAAW,CAACnB,IAAD,CAAX,GAAoB;IAACqB,KAAK,EAAErB;EAAR,CAApB;AACD,CAhBD;AAkBAsB,MAAM,CAACC,gBAAP,CAAwBzB,UAAxB,EAAoCqB,WAApC;AACAG,MAAM,CAACE,cAAP,CAAsBN,SAAtB,EAAiC,cAAjC,EAAiD;EAACG,KAAK,EAAE;AAAR,CAAjD,E,CAEA;;AACAvB,UAAU,CAAC2B,IAAX,GAAkB,UAACC,KAAD,EAAQ1B,IAAR,EAAcC,MAAd,EAAsBC,OAAtB,EAA+BC,QAA/B,EAAyCwB,WAAzC,EAAyD;EACzE,IAAMC,UAAU,GAAGN,MAAM,CAACO,MAAP,CAAcX,SAAd,CAAnB;EAEArB,KAAK,CAACiC,YAAN,CAAmBJ,KAAnB,EAA0BE,UAA1B,EAAsC,SAASG,MAAT,CAAgBC,GAAhB,EAAqB;IACzD,OAAOA,GAAG,KAAK5B,KAAK,CAACc,SAArB;EACD,CAFD,EAEG,UAAAe,IAAI,EAAI;IACT,OAAOA,IAAI,KAAK,cAAhB;EACD,CAJD;EAMAnC,UAAU,CAACO,IAAX,CAAgBuB,UAAhB,EAA4BF,KAAK,CAAC3B,OAAlC,EAA2CC,IAA3C,EAAiDC,MAAjD,EAAyDC,OAAzD,EAAkEC,QAAlE;EAEAyB,UAAU,CAACM,KAAX,GAAmBR,KAAnB;EAEAE,UAAU,CAACnB,IAAX,GAAkBiB,KAAK,CAACjB,IAAxB;EAEAkB,WAAW,IAAIL,MAAM,CAACa,MAAP,CAAcP,UAAd,EAA0BD,WAA1B,CAAf;EAEA,OAAOC,UAAP;AACD,CAlBD;;AAoBA,eAAe9B,UAAf"}, "metadata": {}, "sourceType": "module"}