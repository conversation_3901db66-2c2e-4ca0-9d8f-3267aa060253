{"ast": null, "code": "var getMapData = require('./_getMapData');\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\n\n\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = mapCacheDelete;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheDelete", "key", "result", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapCacheDelete.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = mapCacheDelete;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,cAAT,CAAwBC,GAAxB,EAA6B;EAC3B,IAAIC,MAAM,GAAGJ,UAAU,CAAC,IAAD,EAAOG,GAAP,CAAV,CAAsB,QAAtB,EAAgCA,GAAhC,CAAb;EACA,KAAKE,IAAL,IAAaD,MAAM,GAAG,CAAH,GAAO,CAA1B;EACA,OAAOA,MAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBL,cAAjB"}, "metadata": {}, "sourceType": "script"}