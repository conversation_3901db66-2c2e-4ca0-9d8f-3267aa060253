{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\n\nvar dumpIndex = function dumpIndex(step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n\n  if (index < 0) {\n    index = 0;\n  }\n\n  return index;\n};\n\nvar split = function split(step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\n\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n\n  var _a = __read(useState({\n    present: initialValue,\n    past: [],\n    future: []\n  }), 2),\n      history = _a[0],\n      setHistory = _a[1];\n\n  var present = history.present,\n      past = history.past,\n      future = history.future;\n  var initialValueRef = useRef(initialValue);\n\n  var reset = function reset() {\n    var params = [];\n\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n\n  var updateValue = function updateValue(val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength); // maximum number of records exceeded\n\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n\n  var _forward = function _forward(step) {\n    if (step === void 0) {\n      step = 1;\n    }\n\n    if (future.length === 0) {\n      return;\n    }\n\n    var _a = split(step, future),\n        _before = _a._before,\n        _current = _a._current,\n        _after = _a._after;\n\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n\n  var _backward = function _backward(step) {\n    if (step === void 0) {\n      step = -1;\n    }\n\n    if (past.length === 0) {\n      return;\n    }\n\n    var _a = split(step, past),\n        _before = _a._before,\n        _current = _a._current,\n        _after = _a._after;\n\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n\n  var go = function go(step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n\n    if (stepNum === 0) {\n      return;\n    }\n\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n\n    _backward(stepNum);\n  };\n\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useRef", "useState", "useMemoizedFn", "isNumber", "dumpIndex", "step", "arr", "index", "split", "targetArr", "_current", "_before", "_after", "useHistoryTravel", "initialValue", "max<PERSON><PERSON><PERSON>", "_a", "present", "past", "future", "history", "setHistory", "initialValueRef", "reset", "params", "_i", "_initial", "current", "updateValue", "val", "_past", "maxLength<PERSON>um", "Number", "splice", "_forward", "_backward", "go", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "back", "forward"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useHistoryTravel/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar dumpIndex = function dumpIndex(step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n  if (index < 0) {\n    index = 0;\n  }\n  return index;\n};\nvar split = function split(step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n  var _a = __read(useState({\n      present: initialValue,\n      past: [],\n      future: []\n    }), 2),\n    history = _a[0],\n    setHistory = _a[1];\n  var present = history.present,\n    past = history.past,\n    future = history.future;\n  var initialValueRef = useRef(initialValue);\n  var reset = function reset() {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n  var updateValue = function updateValue(val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);\n    // maximum number of records exceeded\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n  var _forward = function _forward(step) {\n    if (step === void 0) {\n      step = 1;\n    }\n    if (future.length === 0) {\n      return;\n    }\n    var _a = split(step, future),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n  var _backward = function _backward(step) {\n    if (step === void 0) {\n      step = -1;\n    }\n    if (past.length === 0) {\n      return;\n    }\n    var _a = split(step, past),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n  var go = function go(step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n    if (stepNum === 0) {\n      return;\n    }\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n    _backward(stepNum);\n  };\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASS,MAAT,EAAiBC,QAAjB,QAAiC,OAAjC;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,IAAIC,SAAS,GAAG,SAASA,SAAT,CAAmBC,IAAnB,EAAyBC,GAAzB,EAA8B;EAC5C,IAAIC,KAAK,GAAGF,IAAI,GAAG,CAAP,GAAWA,IAAI,GAAG,CAAlB,CAAoB;EAApB,EACVC,GAAG,CAACZ,MAAJ,GAAaW,IADf,CAD4C,CAEvB;;EACrB,IAAIE,KAAK,IAAID,GAAG,CAACZ,MAAJ,GAAa,CAA1B,EAA6B;IAC3Ba,KAAK,GAAGD,GAAG,CAACZ,MAAJ,GAAa,CAArB;EACD;;EACD,IAAIa,KAAK,GAAG,CAAZ,EAAe;IACbA,KAAK,GAAG,CAAR;EACD;;EACD,OAAOA,KAAP;AACD,CAVD;;AAWA,IAAIC,KAAK,GAAG,SAASA,KAAT,CAAeH,IAAf,EAAqBI,SAArB,EAAgC;EAC1C,IAAIF,KAAK,GAAGH,SAAS,CAACC,IAAD,EAAOI,SAAP,CAArB;EACA,OAAO;IACLC,QAAQ,EAAED,SAAS,CAACF,KAAD,CADd;IAELI,OAAO,EAAEF,SAAS,CAACX,KAAV,CAAgB,CAAhB,EAAmBS,KAAnB,CAFJ;IAGLK,MAAM,EAAEH,SAAS,CAACX,KAAV,CAAgBS,KAAK,GAAG,CAAxB;EAHH,CAAP;AAKD,CAPD;;AAQA,eAAe,SAASM,gBAAT,CAA0BC,YAA1B,EAAwCC,SAAxC,EAAmD;EAChE,IAAIA,SAAS,KAAK,KAAK,CAAvB,EAA0B;IACxBA,SAAS,GAAG,CAAZ;EACD;;EACD,IAAIC,EAAE,GAAG3C,MAAM,CAAC4B,QAAQ,CAAC;IACrBgB,OAAO,EAAEH,YADY;IAErBI,IAAI,EAAE,EAFe;IAGrBC,MAAM,EAAE;EAHa,CAAD,CAAT,EAIT,CAJS,CAAf;EAAA,IAKEC,OAAO,GAAGJ,EAAE,CAAC,CAAD,CALd;EAAA,IAMEK,UAAU,GAAGL,EAAE,CAAC,CAAD,CANjB;;EAOA,IAAIC,OAAO,GAAGG,OAAO,CAACH,OAAtB;EAAA,IACEC,IAAI,GAAGE,OAAO,CAACF,IADjB;EAAA,IAEEC,MAAM,GAAGC,OAAO,CAACD,MAFnB;EAGA,IAAIG,eAAe,GAAGtB,MAAM,CAACc,YAAD,CAA5B;;EACA,IAAIS,KAAK,GAAG,SAASA,KAAT,GAAiB;IAC3B,IAAIC,MAAM,GAAG,EAAb;;IACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGhC,SAAS,CAACC,MAAhC,EAAwC+B,EAAE,EAA1C,EAA8C;MAC5CD,MAAM,CAACC,EAAD,CAAN,GAAahC,SAAS,CAACgC,EAAD,CAAtB;IACD;;IACD,IAAIC,QAAQ,GAAGF,MAAM,CAAC9B,MAAP,GAAgB,CAAhB,GAAoB8B,MAAM,CAAC,CAAD,CAA1B,GAAgCF,eAAe,CAACK,OAA/D;;IACAL,eAAe,CAACK,OAAhB,GAA0BD,QAA1B;IACAL,UAAU,CAAC;MACTJ,OAAO,EAAES,QADA;MAETP,MAAM,EAAE,EAFC;MAGTD,IAAI,EAAE;IAHG,CAAD,CAAV;EAKD,CAZD;;EAaA,IAAIU,WAAW,GAAG,SAASA,WAAT,CAAqBC,GAArB,EAA0B;IAC1C,IAAIC,KAAK,GAAGzC,aAAa,CAACA,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAAC6C,IAAD,CAAX,EAAmB,KAAnB,CAAd,EAAyC,CAACD,OAAD,CAAzC,EAAoD,KAApD,CAAzB;;IACA,IAAIc,YAAY,GAAG5B,QAAQ,CAACY,SAAD,CAAR,GAAsBA,SAAtB,GAAkCiB,MAAM,CAACjB,SAAD,CAA3D,CAF0C,CAG1C;;IACA,IAAIgB,YAAY,GAAG,CAAf,IAAoBD,KAAK,CAACpC,MAAN,GAAeqC,YAAvC,EAAqD;MACnD;MACAD,KAAK,CAACG,MAAN,CAAa,CAAb,EAAgB,CAAhB;IACD;;IACDZ,UAAU,CAAC;MACTJ,OAAO,EAAEY,GADA;MAETV,MAAM,EAAE,EAFC;MAGTD,IAAI,EAAEY;IAHG,CAAD,CAAV;EAKD,CAbD;;EAcA,IAAII,QAAQ,GAAG,SAASA,QAAT,CAAkB7B,IAAlB,EAAwB;IACrC,IAAIA,IAAI,KAAK,KAAK,CAAlB,EAAqB;MACnBA,IAAI,GAAG,CAAP;IACD;;IACD,IAAIc,MAAM,CAACzB,MAAP,KAAkB,CAAtB,EAAyB;MACvB;IACD;;IACD,IAAIsB,EAAE,GAAGR,KAAK,CAACH,IAAD,EAAOc,MAAP,CAAd;IAAA,IACER,OAAO,GAAGK,EAAE,CAACL,OADf;IAAA,IAEED,QAAQ,GAAGM,EAAE,CAACN,QAFhB;IAAA,IAGEE,MAAM,GAAGI,EAAE,CAACJ,MAHd;;IAIAS,UAAU,CAAC;MACTH,IAAI,EAAE7B,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAAC6C,IAAD,CAAX,EAAmB,KAAnB,CAAd,EAAyC,CAACD,OAAD,CAAzC,EAAoD,KAApD,CAAd,EAA0E5C,MAAM,CAACsC,OAAD,CAAhF,EAA2F,KAA3F,CADV;MAETM,OAAO,EAAEP,QAFA;MAGTS,MAAM,EAAEP;IAHC,CAAD,CAAV;EAKD,CAhBD;;EAiBA,IAAIuB,SAAS,GAAG,SAASA,SAAT,CAAmB9B,IAAnB,EAAyB;IACvC,IAAIA,IAAI,KAAK,KAAK,CAAlB,EAAqB;MACnBA,IAAI,GAAG,CAAC,CAAR;IACD;;IACD,IAAIa,IAAI,CAACxB,MAAL,KAAgB,CAApB,EAAuB;MACrB;IACD;;IACD,IAAIsB,EAAE,GAAGR,KAAK,CAACH,IAAD,EAAOa,IAAP,CAAd;IAAA,IACEP,OAAO,GAAGK,EAAE,CAACL,OADf;IAAA,IAEED,QAAQ,GAAGM,EAAE,CAACN,QAFhB;IAAA,IAGEE,MAAM,GAAGI,EAAE,CAACJ,MAHd;;IAIAS,UAAU,CAAC;MACTH,IAAI,EAAEP,OADG;MAETM,OAAO,EAAEP,QAFA;MAGTS,MAAM,EAAE9B,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACuC,MAAD,CAAX,EAAqB,KAArB,CAAd,EAA2C,CAACK,OAAD,CAA3C,EAAsD,KAAtD,CAAd,EAA4E5C,MAAM,CAAC8C,MAAD,CAAlF,EAA4F,KAA5F;IAHZ,CAAD,CAAV;EAKD,CAhBD;;EAiBA,IAAIiB,EAAE,GAAG,SAASA,EAAT,CAAY/B,IAAZ,EAAkB;IACzB,IAAIgC,OAAO,GAAGlC,QAAQ,CAACE,IAAD,CAAR,GAAiBA,IAAjB,GAAwB2B,MAAM,CAAC3B,IAAD,CAA5C;;IACA,IAAIgC,OAAO,KAAK,CAAhB,EAAmB;MACjB;IACD;;IACD,IAAIA,OAAO,GAAG,CAAd,EAAiB;MACf,OAAOH,QAAQ,CAACG,OAAD,CAAf;IACD;;IACDF,SAAS,CAACE,OAAD,CAAT;EACD,CATD;;EAUA,OAAO;IACLlD,KAAK,EAAE8B,OADF;IAELqB,UAAU,EAAEpB,IAAI,CAACxB,MAFZ;IAGL6C,aAAa,EAAEpB,MAAM,CAACzB,MAHjB;IAIL8C,QAAQ,EAAEtC,aAAa,CAAC0B,WAAD,CAJlB;IAKLQ,EAAE,EAAElC,aAAa,CAACkC,EAAD,CALZ;IAMLK,IAAI,EAAEvC,aAAa,CAAC,YAAY;MAC9BkC,EAAE,CAAC,CAAC,CAAF,CAAF;IACD,CAFkB,CANd;IASLM,OAAO,EAAExC,aAAa,CAAC,YAAY;MACjCkC,EAAE,CAAC,CAAD,CAAF;IACD,CAFqB,CATjB;IAYLb,KAAK,EAAErB,aAAa,CAACqB,KAAD;EAZf,CAAP;AAcD"}, "metadata": {}, "sourceType": "module"}