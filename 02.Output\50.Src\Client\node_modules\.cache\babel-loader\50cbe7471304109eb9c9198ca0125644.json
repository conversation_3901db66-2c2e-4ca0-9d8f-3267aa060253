{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits'),\n    utils = require('./utils/event'),\n    IframeTransport = require('./transport/iframe'),\n    InfoReceiverIframe = require('./info-iframe-receiver');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\n\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n\n  var go = function go() {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n    ifr.once('message', function (msg) {\n      if (msg) {\n        var d;\n\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n\n        var info = d[0],\n            rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n\n      self.close();\n    });\n    ifr.once('close', function () {\n      self.emit('finish');\n      self.close();\n    });\n  }; // TODO this seems the same as the 'needBody' from transports\n\n\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\n\ninherits(InfoIframe, EventEmitter);\n\nInfoIframe.enabled = function () {\n  return IframeTransport.enabled();\n};\n\nInfoIframe.prototype.close = function () {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n\n  this.removeAllListeners();\n  this.ifr = null;\n};\n\nmodule.exports = InfoIframe;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "utils", "IframeTransport", "InfoReceiverIframe", "debug", "process", "env", "NODE_ENV", "InfoIframe", "baseUrl", "url", "self", "call", "go", "ifr", "transportName", "once", "msg", "d", "JSON", "parse", "e", "emit", "close", "info", "rtt", "global", "document", "body", "attachEvent", "enabled", "prototype", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/info-iframe.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('./utils/event')\n  , IframeTransport = require('./transport/iframe')\n  , InfoReceiverIframe = require('./info-iframe-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\n\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n\n  var go = function() {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n\n    ifr.once('message', function(msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n\n        var info = d[0], rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n\n    ifr.once('close', function() {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\n\ninherits(InfoIframe, EventEmitter);\n\nInfoIframe.enabled = function() {\n  return IframeTransport.enabled();\n};\n\nInfoIframe.prototype.close = function() {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\n\nmodule.exports = InfoIframe;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIE,KAAK,GAAGF,OAAO,CAAC,eAAD,CAFnB;AAAA,IAGIG,eAAe,GAAGH,OAAO,CAAC,oBAAD,CAH7B;AAAA,IAIII,kBAAkB,GAAGJ,OAAO,CAAC,wBAAD,CAJhC;;AAOA,IAAIK,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAD,CAAP,CAAiB,2BAAjB,CAAR;AACD;;AAED,SAASS,UAAT,CAAoBC,OAApB,EAA6BC,GAA7B,EAAkC;EAChC,IAAIC,IAAI,GAAG,IAAX;EACAb,YAAY,CAACc,IAAb,CAAkB,IAAlB;;EAEA,IAAIC,EAAE,GAAG,SAALA,EAAK,GAAW;IAClB,IAAIC,GAAG,GAAGH,IAAI,CAACG,GAAL,GAAW,IAAIZ,eAAJ,CAAoBC,kBAAkB,CAACY,aAAvC,EAAsDL,GAAtD,EAA2DD,OAA3D,CAArB;IAEAK,GAAG,CAACE,IAAJ,CAAS,SAAT,EAAoB,UAASC,GAAT,EAAc;MAChC,IAAIA,GAAJ,EAAS;QACP,IAAIC,CAAJ;;QACA,IAAI;UACFA,CAAC,GAAGC,IAAI,CAACC,KAAL,CAAWH,GAAX,CAAJ;QACD,CAFD,CAEE,OAAOI,CAAP,EAAU;UACVjB,KAAK,CAAC,UAAD,EAAaa,GAAb,CAAL;UACAN,IAAI,CAACW,IAAL,CAAU,QAAV;UACAX,IAAI,CAACY,KAAL;UACA;QACD;;QAED,IAAIC,IAAI,GAAGN,CAAC,CAAC,CAAD,CAAZ;QAAA,IAAiBO,GAAG,GAAGP,CAAC,CAAC,CAAD,CAAxB;QACAP,IAAI,CAACW,IAAL,CAAU,QAAV,EAAoBE,IAApB,EAA0BC,GAA1B;MACD;;MACDd,IAAI,CAACY,KAAL;IACD,CAhBD;IAkBAT,GAAG,CAACE,IAAJ,CAAS,OAAT,EAAkB,YAAW;MAC3BL,IAAI,CAACW,IAAL,CAAU,QAAV;MACAX,IAAI,CAACY,KAAL;IACD,CAHD;EAID,CAzBD,CAJgC,CA+BhC;;;EACA,IAAI,CAACG,MAAM,CAACC,QAAP,CAAgBC,IAArB,EAA2B;IACzB3B,KAAK,CAAC4B,WAAN,CAAkB,MAAlB,EAA0BhB,EAA1B;EACD,CAFD,MAEO;IACLA,EAAE;EACH;AACF;;AAEDb,QAAQ,CAACQ,UAAD,EAAaV,YAAb,CAAR;;AAEAU,UAAU,CAACsB,OAAX,GAAqB,YAAW;EAC9B,OAAO5B,eAAe,CAAC4B,OAAhB,EAAP;AACD,CAFD;;AAIAtB,UAAU,CAACuB,SAAX,CAAqBR,KAArB,GAA6B,YAAW;EACtC,IAAI,KAAKT,GAAT,EAAc;IACZ,KAAKA,GAAL,CAASS,KAAT;EACD;;EACD,KAAKS,kBAAL;EACA,KAAKlB,GAAL,GAAW,IAAX;AACD,CAND;;AAQAmB,MAAM,CAACC,OAAP,GAAiB1B,UAAjB"}, "metadata": {}, "sourceType": "script"}