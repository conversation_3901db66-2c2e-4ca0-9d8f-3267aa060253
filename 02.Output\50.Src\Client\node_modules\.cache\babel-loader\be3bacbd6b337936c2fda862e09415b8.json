{"ast": null, "code": "var listCacheClear = require('./_listCacheClear'),\n    listCacheDelete = require('./_listCacheDelete'),\n    listCacheGet = require('./_listCacheGet'),\n    listCacheHas = require('./_listCacheHas'),\n    listCacheSet = require('./_listCacheSet');\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\n\n\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n  this.clear();\n\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n} // Add methods to `ListCache`.\n\n\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\nmodule.exports = ListCache;", "map": {"version": 3, "names": ["listCacheClear", "require", "listCacheDelete", "listCacheGet", "listCacheHas", "listCacheSet", "ListCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_ListCache.js"], "sourcesContent": ["var listCacheClear = require('./_listCacheClear'),\n    listCacheDelete = require('./_listCacheDelete'),\n    listCacheGet = require('./_listCacheGet'),\n    listCacheHas = require('./_listCacheHas'),\n    listCacheSet = require('./_listCacheSet');\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\nmodule.exports = ListCache;\n"], "mappings": "AAAA,IAAIA,cAAc,GAAGC,OAAO,CAAC,mBAAD,CAA5B;AAAA,IACIC,eAAe,GAAGD,OAAO,CAAC,oBAAD,CAD7B;AAAA,IAEIE,YAAY,GAAGF,OAAO,CAAC,iBAAD,CAF1B;AAAA,IAGIG,YAAY,GAAGH,OAAO,CAAC,iBAAD,CAH1B;AAAA,IAIII,YAAY,GAAGJ,OAAO,CAAC,iBAAD,CAJ1B;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,SAAT,CAAmBC,OAAnB,EAA4B;EAC1B,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGF,OAAO,IAAI,IAAX,GAAkB,CAAlB,GAAsBA,OAAO,CAACE,MAD3C;EAGA,KAAKC,KAAL;;EACA,OAAO,EAAEF,KAAF,GAAUC,MAAjB,EAAyB;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAD,CAAnB;IACA,KAAKI,GAAL,CAASD,KAAK,CAAC,CAAD,CAAd,EAAmBA,KAAK,CAAC,CAAD,CAAxB;EACD;AACF,C,CAED;;;AACAL,SAAS,CAACO,SAAV,CAAoBH,KAApB,GAA4BV,cAA5B;AACAM,SAAS,CAACO,SAAV,CAAoB,QAApB,IAAgCX,eAAhC;AACAI,SAAS,CAACO,SAAV,CAAoBC,GAApB,GAA0BX,YAA1B;AACAG,SAAS,CAACO,SAAV,CAAoBE,GAApB,GAA0BX,YAA1B;AACAE,SAAS,CAACO,SAAV,CAAoBD,GAApB,GAA0BP,YAA1B;AAEAW,MAAM,CAACC,OAAP,GAAiBX,SAAjB"}, "metadata": {}, "sourceType": "script"}