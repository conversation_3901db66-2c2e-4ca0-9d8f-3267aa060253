{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\n\nexport default function transformData(fns, response) {\n  var config = this || defaults;\n  var context = response || config;\n  var headers = AxiosHeaders.from(context.headers);\n  var data = context.data;\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n  headers.normalize();\n  return data;\n}", "map": {"version": 3, "names": ["utils", "defaults", "AxiosHeaders", "transformData", "fns", "response", "config", "context", "headers", "from", "data", "for<PERSON>ach", "transform", "fn", "call", "normalize", "status", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/transformData.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\nexport default function transformData(fns, response) {\n  const config = this || defaults;\n  const context = response || config;\n  const headers = AxiosHeaders.from(context.headers);\n  let data = context.data;\n\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n\n  headers.normalize();\n\n  return data;\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAOC,YAAP,MAAyB,yBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,aAAT,CAAuBC,GAAvB,EAA4BC,QAA5B,EAAsC;EACnD,IAAMC,MAAM,GAAG,QAAQL,QAAvB;EACA,IAAMM,OAAO,GAAGF,QAAQ,IAAIC,MAA5B;EACA,IAAME,OAAO,GAAGN,YAAY,CAACO,IAAb,CAAkBF,OAAO,CAACC,OAA1B,CAAhB;EACA,IAAIE,IAAI,GAAGH,OAAO,CAACG,IAAnB;EAEAV,KAAK,CAACW,OAAN,CAAcP,GAAd,EAAmB,SAASQ,SAAT,CAAmBC,EAAnB,EAAuB;IACxCH,IAAI,GAAGG,EAAE,CAACC,IAAH,CAAQR,MAAR,EAAgBI,IAAhB,EAAsBF,OAAO,CAACO,SAAR,EAAtB,EAA2CV,QAAQ,GAAGA,QAAQ,CAACW,MAAZ,GAAqBC,SAAxE,CAAP;EACD,CAFD;EAIAT,OAAO,CAACO,SAAR;EAEA,OAAOL,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}