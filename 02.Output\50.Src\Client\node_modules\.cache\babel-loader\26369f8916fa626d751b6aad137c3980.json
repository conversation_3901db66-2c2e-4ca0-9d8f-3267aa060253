{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import TextScroll from'./elements/TextScroll';import Cell from'./elements/Cell';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource,formateDatetimeText,splitDateAndTime}from'../utils/Util.js';/**\r\n * 予警報コンテンツ<br>\r\n * propsは、「3.12予警報コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Alarm\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var Alarm=function Alarm(props){var _props$issued_ts;var MAX_ROW=5;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Title,{title:'警報・注意報'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid leading-[1]\\r grid-cols-[repeat(4,4.5rem)_1.2rem_repeat(4,4.5rem)_minmax(0.25rem,1fr)_repeat(9,1fr)] items-end gap-y-[4rem] mt-[3rem]\",children:(_props$issued_ts=props.issued_ts)===null||_props$issued_ts===void 0?void 0:_props$issued_ts.map(function(item,index){//・1コンテンツあたり最大5件の情報を表示可能。\nif(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(AlarmRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})})]});};var AlarmRow=function AlarmRow(props){if(!props.issued_ts[props.index]||!props.combined_forecast_warning||!props.combined_forecast_warning[props.index]){return;}var cellDateProps=getCellFace(props.issued_ts[props.index],'text-4xl my-4');var cellTimeProps=_objectSpread({},cellDateProps);var cell2DateTimeSeperator={background_color:cellDateProps.background_color,className:\"\".concat(cellDateProps.className,\" col-start-5\"),text:' '};var splitedDateAndTime=splitDateAndTime(cellDateProps.text);if(splitedDateAndTime){cellDateProps.text=formateDatetimeText(splitedDateAndTime.date,'3rem');cellDateProps.className=\"col-span-4 \".concat(cellDateProps.className);cellTimeProps.text=formateDatetimeText(splitedDateAndTime.time,'3rem');cellTimeProps.className=\"col-start-6 col-span-4 \".concat(cellTimeProps.className);}else{cellDateProps.text=formateDatetimeText(cellDateProps.text,'3rem');cellDateProps.className=\"col-span-9 \".concat(cellDateProps.className);}var cellPropAlarm=getCellFace(props.combined_forecast_warning[props.index],'col-span-9 col-start-11 text-7xl');var cellPropAlarmFrame={className:cellPropAlarm.className,text_color:cellPropAlarm.text_color};return/*#__PURE__*/_jsxs(_Fragment,{children:[!splitedDateAndTime&&/*#__PURE__*/_jsx(Cell,_objectSpread({},cellDateProps)),splitedDateAndTime&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cellDateProps)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell2DateTimeSeperator)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cellTimeProps))]}),/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},cellPropAlarmFrame),{},{children:/*#__PURE__*/_jsx(\"div\",{className:\"text-8xl\",children:/*#__PURE__*/_jsx(TextScroll,{content:cellPropAlarm.text,background_color:cellPropAlarm.background_color})})}))]});};export default Alarm;", "map": {"version": 3, "names": ["React", "Title", "TextScroll", "Cell", "CellBox", "getCellFace", "isValidSource", "formateDatetimeText", "splitDateAndTime", "Alarm", "props", "MAX_ROW", "issued_ts", "map", "item", "index", "undefined", "AlarmRow", "combined_forecast_warning", "cellDateProps", "cellTimeProps", "cell2DateTimeSeperator", "background_color", "className", "text", "splitedDateAndTime", "date", "time", "cellPropAlarm", "cellPropAlarmFrame", "text_color"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Alarm.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport TextScroll from './elements/TextScroll';\r\nimport Cell from './elements/Cell';\r\nimport CellBox from './elements/CellBox';\r\nimport {\r\n  getCellFace,\r\n  isValidSource,\r\n  formateDatetimeText,\r\n  splitDateAndTime,\r\n} from '../utils/Util.js';\r\n\r\n/**\r\n * 予警報コンテンツ<br>\r\n * propsは、「3.12予警報コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Alarm\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Alarm = (props) => {\r\n  const MAX_ROW = 5;\r\n  return (\r\n    <>\r\n      <Title title={'警報・注意報'} />\r\n      {isValidSource(props) && (\r\n        <div\r\n          className=\"border-transparent border-x-[1rem] grid leading-[1]\r\n          grid-cols-[repeat(4,4.5rem)_1.2rem_repeat(4,4.5rem)_minmax(0.25rem,1fr)_repeat(9,1fr)] items-end gap-y-[4rem] mt-[3rem]\"\r\n        >\r\n          {props.issued_ts?.map((item, index) => {\r\n            //・1コンテンツあたり最大5件の情報を表示可能。\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <AlarmRow key={index} {...props} index={index} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nconst AlarmRow = (props) => {\r\n  if (\r\n    !props.issued_ts[props.index] ||\r\n    !props.combined_forecast_warning ||\r\n    !props.combined_forecast_warning[props.index]\r\n  ) {\r\n    return;\r\n  }\r\n\r\n  let cellDateProps = getCellFace(\r\n    props.issued_ts[props.index],\r\n    'text-4xl my-4'\r\n  );\r\n  let cellTimeProps = { ...cellDateProps };\r\n\r\n  let cell2DateTimeSeperator = {\r\n    background_color: cellDateProps.background_color,\r\n    className: `${cellDateProps.className} col-start-5`,\r\n    text: ' ',\r\n  };\r\n\r\n  let splitedDateAndTime = splitDateAndTime(cellDateProps.text);\r\n  if (splitedDateAndTime) {\r\n    cellDateProps.text = formateDatetimeText(splitedDateAndTime.date, '3rem');\r\n    cellDateProps.className = `col-span-4 ${cellDateProps.className}`;\r\n\r\n    cellTimeProps.text = formateDatetimeText(splitedDateAndTime.time, '3rem');\r\n    cellTimeProps.className = `col-start-6 col-span-4 ${cellTimeProps.className}`;\r\n  } else {\r\n    cellDateProps.text = formateDatetimeText(cellDateProps.text, '3rem');\r\n    cellDateProps.className = `col-span-9 ${cellDateProps.className}`;\r\n  }\r\n\r\n  let cellPropAlarm = getCellFace(\r\n    props.combined_forecast_warning[props.index],\r\n    'col-span-9 col-start-11 text-7xl'\r\n  );\r\n\r\n  let cellPropAlarmFrame = {\r\n    className: cellPropAlarm.className,\r\n    text_color: cellPropAlarm.text_color,\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {!splitedDateAndTime && <Cell {...cellDateProps} />}\r\n      {splitedDateAndTime && (\r\n        <>\r\n          <Cell {...cellDateProps} />\r\n          <Cell {...cell2DateTimeSeperator} />\r\n          <Cell {...cellTimeProps} />\r\n        </>\r\n      )}\r\n      <CellBox {...cellPropAlarmFrame}>\r\n        <div className=\"text-8xl\">\r\n          <TextScroll\r\n            content={cellPropAlarm.text}\r\n            background_color={cellPropAlarm.background_color}\r\n          />\r\n        </div>\r\n      </CellBox>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Alarm;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OACEC,WADF,CAEEC,aAFF,CAGEC,mBAHF,CAIEC,gBAJF,KAKO,kBALP,CAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,MAAK,CAAG,QAARA,MAAQ,CAACC,KAAD,CAAW,sBACvB,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,wCACE,KAAC,KAAD,EAAO,KAAK,CAAE,QAAd,EADF,CAEGL,aAAa,CAACI,KAAD,CAAb,eACC,YACE,SAAS,CAAC,+KADZ,4BAIGA,KAAK,CAACE,SAJT,2CAIG,iBAAiBC,GAAjB,CAAqB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACrC;AACA,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,QAAD,gCAA0BN,KAA1B,MAAiC,KAAK,CAAEK,KAAxC,GAAeA,KAAf,CAAP,CACD,CALA,CAJH,EAHJ,GADF,CAkBD,CApBD,CAsBA,GAAME,SAAQ,CAAG,QAAXA,SAAW,CAACP,KAAD,CAAW,CAC1B,GACE,CAACA,KAAK,CAACE,SAAN,CAAgBF,KAAK,CAACK,KAAtB,CAAD,EACA,CAACL,KAAK,CAACQ,yBADP,EAEA,CAACR,KAAK,CAACQ,yBAAN,CAAgCR,KAAK,CAACK,KAAtC,CAHH,CAIE,CACA,OACD,CAED,GAAII,cAAa,CAAGd,WAAW,CAC7BK,KAAK,CAACE,SAAN,CAAgBF,KAAK,CAACK,KAAtB,CAD6B,CAE7B,eAF6B,CAA/B,CAIA,GAAIK,cAAa,kBAAQD,aAAR,CAAjB,CAEA,GAAIE,uBAAsB,CAAG,CAC3BC,gBAAgB,CAAEH,aAAa,CAACG,gBADL,CAE3BC,SAAS,WAAKJ,aAAa,CAACI,SAAnB,gBAFkB,CAG3BC,IAAI,CAAE,GAHqB,CAA7B,CAMA,GAAIC,mBAAkB,CAAGjB,gBAAgB,CAACW,aAAa,CAACK,IAAf,CAAzC,CACA,GAAIC,kBAAJ,CAAwB,CACtBN,aAAa,CAACK,IAAd,CAAqBjB,mBAAmB,CAACkB,kBAAkB,CAACC,IAApB,CAA0B,MAA1B,CAAxC,CACAP,aAAa,CAACI,SAAd,sBAAwCJ,aAAa,CAACI,SAAtD,EAEAH,aAAa,CAACI,IAAd,CAAqBjB,mBAAmB,CAACkB,kBAAkB,CAACE,IAApB,CAA0B,MAA1B,CAAxC,CACAP,aAAa,CAACG,SAAd,kCAAoDH,aAAa,CAACG,SAAlE,EACD,CAND,IAMO,CACLJ,aAAa,CAACK,IAAd,CAAqBjB,mBAAmB,CAACY,aAAa,CAACK,IAAf,CAAqB,MAArB,CAAxC,CACAL,aAAa,CAACI,SAAd,sBAAwCJ,aAAa,CAACI,SAAtD,EACD,CAED,GAAIK,cAAa,CAAGvB,WAAW,CAC7BK,KAAK,CAACQ,yBAAN,CAAgCR,KAAK,CAACK,KAAtC,CAD6B,CAE7B,kCAF6B,CAA/B,CAKA,GAAIc,mBAAkB,CAAG,CACvBN,SAAS,CAAEK,aAAa,CAACL,SADF,CAEvBO,UAAU,CAAEF,aAAa,CAACE,UAFH,CAAzB,CAKA,mBACE,2BACG,CAACL,kBAAD,eAAuB,KAAC,IAAD,kBAAUN,aAAV,EAD1B,CAEGM,kBAAkB,eACjB,wCACE,KAAC,IAAD,kBAAUN,aAAV,EADF,cAEE,KAAC,IAAD,kBAAUE,sBAAV,EAFF,cAGE,KAAC,IAAD,kBAAUD,aAAV,EAHF,GAHJ,cASE,KAAC,OAAD,gCAAaS,kBAAb,4BACE,YAAK,SAAS,CAAC,UAAf,uBACE,KAAC,UAAD,EACE,OAAO,CAAED,aAAa,CAACJ,IADzB,CAEE,gBAAgB,CAAEI,aAAa,CAACN,gBAFlC,EADF,EADF,GATF,GADF,CAoBD,CA/DD,CAiEA,cAAeb,MAAf"}, "metadata": {}, "sourceType": "module"}