{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nimport { StompHandler } from './stomp-handler';\nimport { ActivationState, StompSocketState } from './types';\nimport { Versions } from './versions';\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\n\nexport class Client {\n  /**\n   * Create an instance.\n   */\n  constructor() {\n    let conf = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    /**\n     * STOMP versions to attempt during STOMP handshake. By default versions `1.0`, `1.1`, and `1.2` are attempted.\n     *\n     * Example:\n     * ```javascript\n     *        // Try only versions 1.0 and 1.1\n     *        client.stompVersions = new Versions(['1.0', '1.1'])\n     * ```\n     */\n    this.stompVersions = Versions.default;\n    /**\n     * Will retry if Stomp connection is not established in specified milliseconds.\n     * Default 0, which implies wait for ever.\n     */\n\n    this.connectionTimeout = 0;\n    /**\n     *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n     */\n\n    this.reconnectDelay = 5000;\n    /**\n     * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n\n    this.heartbeatIncoming = 10000;\n    /**\n     * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n     */\n\n    this.heartbeatOutgoing = 10000;\n    /**\n     * This switches on a non standard behavior while sending WebSocket packets.\n     * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n     * Only Java Spring brokers seems to use this mode.\n     *\n     * WebSockets, by itself, split large (text) packets,\n     * so it is not needed with a truly compliant STOMP/WebSocket broker.\n     * Actually setting it for such broker will cause large messages to fail.\n     *\n     * `false` by default.\n     *\n     * Binary frames are never split.\n     */\n\n    this.splitLargeFrames = false;\n    /**\n     * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n     * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n     */\n\n    this.maxWebSocketChunkSize = 8 * 1024;\n    /**\n     * Usually the\n     * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n     * is automatically decided by type of the payload.\n     * Default is `false`, which should work with all compliant brokers.\n     *\n     * Set this flag to force binary frames.\n     */\n\n    this.forceBinaryWSFrames = false;\n    /**\n     * A bug in ReactNative chops a string on occurrence of a NULL.\n     * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n     * This makes incoming WebSocket messages invalid STOMP packets.\n     * Setting this flag attempts to reverse the damage by appending a NULL.\n     * If the broker splits a large message into multiple WebSocket messages,\n     * this flag will cause data loss and abnormal termination of connection.\n     *\n     * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n     */\n\n    this.appendMissingNULLonIncoming = false;\n    /**\n     * Activation state.\n     *\n     * It will usually be ACTIVE or INACTIVE.\n     * When deactivating it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n     */\n\n    this.state = ActivationState.INACTIVE; // Dummy callbacks\n\n    const noOp = () => {};\n\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp; // These parameters would typically get proper values before connect is called\n\n    this.connectHeaders = {};\n    this._disconnectHeaders = {}; // Apply configuration\n\n    this.configure(conf);\n  }\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n\n\n  get webSocket() {\n    return this._stompHandler ? this._stompHandler._webSocket : undefined;\n  }\n  /**\n   * Disconnection headers.\n   */\n\n\n  get disconnectHeaders() {\n    return this._disconnectHeaders;\n  }\n\n  set disconnectHeaders(value) {\n    this._disconnectHeaders = value;\n\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  /**\n   * `true` if there is a active connection with STOMP Broker\n   */\n\n\n  get connected() {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n\n\n  get connectedVersion() {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n\n\n  get active() {\n    return this.state === ActivationState.ACTIVE;\n  }\n\n  _changeState(state) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n  /**\n   * Update configuration.\n   */\n\n\n  configure(conf) {\n    // bulk assign all properties to this\n    Object.assign(this, conf);\n  }\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect.\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n\n\n  activate() {\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Still DEACTIVATING, please await call to deactivate before trying to re-activate');\n      throw new Error('Still DEACTIVATING, can not activate now');\n    }\n\n    if (this.active) {\n      this.debug('Already ACTIVE, ignoring request to activate');\n      return;\n    }\n\n    this._changeState(ActivationState.ACTIVE);\n\n    this._connect();\n  }\n\n  _connect() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.connected) {\n        this.debug('STOMP: already connected, nothing to do');\n        return;\n      }\n\n      yield this.beforeConnect();\n\n      if (!this.active) {\n        this.debug('Client has been marked inactive, will not attempt to connect');\n        return;\n      } // setup connection watcher\n\n\n      if (this.connectionTimeout > 0) {\n        // clear first\n        if (this._connectionWatcher) {\n          clearTimeout(this._connectionWatcher);\n        }\n\n        this._connectionWatcher = setTimeout(() => {\n          if (this.connected) {\n            return;\n          } // Connection not established, close the underlying socket\n          // a reconnection will be attempted\n\n\n          this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`);\n          this.forceDisconnect();\n        }, this.connectionTimeout);\n      }\n\n      this.debug('Opening Web Socket...'); // Get the actual WebSocket (or a similar object)\n\n      const webSocket = this._createWebSocket();\n\n      this._stompHandler = new StompHandler(this, webSocket, {\n        debug: this.debug,\n        stompVersions: this.stompVersions,\n        connectHeaders: this.connectHeaders,\n        disconnectHeaders: this._disconnectHeaders,\n        heartbeatIncoming: this.heartbeatIncoming,\n        heartbeatOutgoing: this.heartbeatOutgoing,\n        splitLargeFrames: this.splitLargeFrames,\n        maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n        forceBinaryWSFrames: this.forceBinaryWSFrames,\n        logRawCommunication: this.logRawCommunication,\n        appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n        discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n        onConnect: frame => {\n          // Successfully connected, stop the connection watcher\n          if (this._connectionWatcher) {\n            clearTimeout(this._connectionWatcher);\n            this._connectionWatcher = undefined;\n          }\n\n          if (!this.active) {\n            this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n\n            this._disposeStompHandler();\n\n            return;\n          }\n\n          this.onConnect(frame);\n        },\n        onDisconnect: frame => {\n          this.onDisconnect(frame);\n        },\n        onStompError: frame => {\n          this.onStompError(frame);\n        },\n        onWebSocketClose: evt => {\n          this._stompHandler = undefined; // a new one will be created in case of a reconnect\n\n          if (this.state === ActivationState.DEACTIVATING) {\n            // Mark deactivation complete\n            this._resolveSocketClose();\n\n            this._resolveSocketClose = undefined;\n\n            this._changeState(ActivationState.INACTIVE);\n          }\n\n          this.onWebSocketClose(evt); // The callback is called before attempting to reconnect, this would allow the client\n          // to be `deactivated` in the callback.\n\n          if (this.active) {\n            this._schedule_reconnect();\n          }\n        },\n        onWebSocketError: evt => {\n          this.onWebSocketError(evt);\n        },\n        onUnhandledMessage: message => {\n          this.onUnhandledMessage(message);\n        },\n        onUnhandledReceipt: frame => {\n          this.onUnhandledReceipt(frame);\n        },\n        onUnhandledFrame: frame => {\n          this.onUnhandledFrame(frame);\n        }\n      });\n\n      this._stompHandler.start();\n    });\n  }\n\n  _createWebSocket() {\n    let webSocket;\n\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else {\n      webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n    }\n\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n\n  _schedule_reconnect() {\n    if (this.reconnectDelay > 0) {\n      this.debug(`STOMP: scheduling reconnection in ${this.reconnectDelay}ms`);\n      this._reconnector = setTimeout(() => {\n        this._connect();\n      }, this.reconnectDelay);\n    }\n  }\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if underlying STOMP connection was connected.\n   *\n   * This call is async, it will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after underlying websocket is properly disposed.\n   *\n   * To reactivate you can call [Client#activate]{@link Client#activate}.\n   */\n\n\n  deactivate() {\n    return __awaiter(this, void 0, void 0, function* () {\n      let retPromise;\n\n      if (this.state !== ActivationState.ACTIVE) {\n        this.debug(`Already ${ActivationState[this.state]}, ignoring call to deactivate`);\n        return Promise.resolve();\n      }\n\n      this._changeState(ActivationState.DEACTIVATING); // Clear if a reconnection was scheduled\n\n\n      if (this._reconnector) {\n        clearTimeout(this._reconnector);\n      }\n\n      if (this._stompHandler && this.webSocket.readyState !== StompSocketState.CLOSED) {\n        // we need to wait for underlying websocket to close\n        retPromise = new Promise((resolve, reject) => {\n          this._resolveSocketClose = resolve;\n        });\n      } else {\n        // indicate that auto reconnect loop should terminate\n        this._changeState(ActivationState.INACTIVE);\n\n        return Promise.resolve();\n      }\n\n      this._disposeStompHandler();\n\n      return retPromise;\n    });\n  }\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different than a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n\n\n  forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n\n  _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n\n      this._stompHandler = null;\n    }\n  }\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body use binaryBody parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect if message body has NULL octet(s)\n   * and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n\n\n  publish(params) {\n    this._stompHandler.publish(params);\n  }\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use. Typically a sequence, a UUID, a\n   * random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based in the value of the receipt-id.\n   *\n   * This method allow watching for a receipt and invoke the callback\n   * when corresponding receipt has been received.\n   *\n   * The actual {@link FrameImpl} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n\n\n  watchForReceipt(receiptId, callback) {\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each received message with\n   * the {@link IMessage} as argument.\n   *\n   * Note: The library will generate an unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the headers argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n\n\n  subscribe(destination, callback) {\n    let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: http://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n\n\n  unsubscribe(id) {\n    let headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    this._stompHandler.unsubscribe(id, headers);\n  }\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n\n\n  begin(transactionId) {\n    return this._stompHandler.begin(transactionId);\n  }\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n\n\n  commit(transactionId) {\n    this._stompHandler.commit(transactionId);\n  }\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n\n\n  abort(transactionId) {\n    this._stompHandler.abort(transactionId);\n  }\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n\n\n  ack(messageId, subscriptionId) {\n    let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n\n\n  nack(messageId, subscriptionId) {\n    let headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n\n}", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAASA,YAAT,QAA6B,iBAA7B;AAGA,SACEC,eADF,EAQEC,gBARF,QAUO,SAVP;AAWA,SAASC,QAAT,QAAyB,YAAzB;AAUA;;;;;;AAKA,OAAM,MAAOC,MAAP,CAAa;EAwTjB;;;EAGAC,cAAkC;IAAA,IAAtBC,IAAsB,uEAAF,EAAE;;IA9SlC;;;;;;;;;IASO,qBAAgBH,QAAQ,CAACI,OAAzB;IAyBP;;;;;IAIO,yBAA4B,CAA5B;IAIP;;;;IAGO,sBAAyB,IAAzB;IAEP;;;;IAGO,yBAA4B,KAA5B;IAEP;;;;IAGO,yBAA4B,KAA5B;IAEP;;;;;;;;;;;;;;IAaO,wBAA4B,KAA5B;IAEP;;;;;IAIO,6BAAgC,IAAI,IAApC;IAEP;;;;;;;;;IAQO,2BAA+B,KAA/B;IAEP;;;;;;;;;;;IAUO,mCAAuC,KAAvC;IA+LP;;;;;;;IAMO,aAAyBN,eAAe,CAACO,QAAzC,CAO2B,CAChC;;IACA,MAAMC,IAAI,GAAG,MAAK,CAAG,CAArB;;IACA,KAAKC,KAAL,GAAaD,IAAb;IACA,KAAKE,aAAL,GAAqBF,IAArB;IACA,KAAKG,SAAL,GAAiBH,IAAjB;IACA,KAAKI,YAAL,GAAoBJ,IAApB;IACA,KAAKK,kBAAL,GAA0BL,IAA1B;IACA,KAAKM,kBAAL,GAA0BN,IAA1B;IACA,KAAKO,gBAAL,GAAwBP,IAAxB;IACA,KAAKQ,YAAL,GAAoBR,IAApB;IACA,KAAKS,gBAAL,GAAwBT,IAAxB;IACA,KAAKU,gBAAL,GAAwBV,IAAxB;IACA,KAAKW,mBAAL,GAA2B,KAA3B;IACA,KAAKC,aAAL,GAAqBZ,IAArB,CAdgC,CAgBhC;;IACA,KAAKa,cAAL,GAAsB,EAAtB;IACA,KAAKC,kBAAL,GAA0B,EAA1B,CAlBgC,CAoBhC;;IACA,KAAKC,SAAL,CAAelB,IAAf;EACD;EAhOD;;;;;EAGa,IAATmB,SAAS;IACX,OAAO,KAAKC,aAAL,GAAqB,KAAKA,aAAL,CAAmBC,UAAxC,GAAqDC,SAA5D;EACD;EASD;;;;;EAGqB,IAAjBC,iBAAiB;IACnB,OAAO,KAAKN,kBAAZ;EACD;;EAEoB,IAAjBM,iBAAiB,CAACC,KAAD,EAAoB;IACvC,KAAKP,kBAAL,GAA0BO,KAA1B;;IACA,IAAI,KAAKJ,aAAT,EAAwB;MACtB,KAAKA,aAAL,CAAmBG,iBAAnB,GAAuC,KAAKN,kBAA5C;IACD;EACF;EA+BD;;;;;EAGa,IAATQ,SAAS;IACX,OAAO,CAAC,CAAC,KAAKL,aAAP,IAAwB,KAAKA,aAAL,CAAmBK,SAAlD;EACD;EAgGD;;;;;EAGoB,IAAhBC,gBAAgB;IAClB,OAAO,KAAKN,aAAL,GAAqB,KAAKA,aAAL,CAAmBM,gBAAxC,GAA2DJ,SAAlE;EACD;EAID;;;;;EAGU,IAANK,MAAM;IACR,OAAO,KAAKC,KAAL,KAAejC,eAAe,CAACkC,MAAtC;EACD;;EASOC,YAAY,CAACF,KAAD,EAAuB;IACzC,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKb,aAAL,CAAmBa,KAAnB;EACD;EA0CD;;;;;EAGOV,SAAS,CAAClB,IAAD,EAAkB;IAChC;IACC+B,MAAc,CAACC,MAAf,CAAsB,IAAtB,EAA4BhC,IAA5B;EACF;EAED;;;;;;;;;EAOOiC,QAAQ;IACb,IAAI,KAAKL,KAAL,KAAejC,eAAe,CAACuC,YAAnC,EAAiD;MAC/C,KAAK9B,KAAL,CACE,kFADF;MAGA,MAAM,IAAI+B,KAAJ,CAAU,0CAAV,CAAN;IACD;;IAED,IAAI,KAAKR,MAAT,EAAiB;MACf,KAAKvB,KAAL,CAAW,8CAAX;MACA;IACD;;IAED,KAAK0B,YAAL,CAAkBnC,eAAe,CAACkC,MAAlC;;IAEA,KAAKO,QAAL;EACD;;EAEaA,QAAQ;;MACpB,IAAI,KAAKX,SAAT,EAAoB;QAClB,KAAKrB,KAAL,CAAW,yCAAX;QACA;MACD;;MAED,MAAM,KAAKC,aAAL,EAAN;;MAEA,IAAI,CAAC,KAAKsB,MAAV,EAAkB;QAChB,KAAKvB,KAAL,CACE,8DADF;QAGA;MACD,EAED;;;MACA,IAAI,KAAKiC,iBAAL,GAAyB,CAA7B,EAAgC;QAC9B;QACA,IAAI,KAAKC,kBAAT,EAA6B;UAC3BC,YAAY,CAAC,KAAKD,kBAAN,CAAZ;QACD;;QACD,KAAKA,kBAAL,GAA0BE,UAAU,CAAC,MAAK;UACxC,IAAI,KAAKf,SAAT,EAAoB;YAClB;UACD,CAHuC,CAIxC;UACA;;;UACA,KAAKrB,KAAL,CACE,iCAAiC,KAAKiC,iBAAiB,oBADzD;UAGA,KAAKI,eAAL;QACD,CAVmC,EAUjC,KAAKJ,iBAV4B,CAApC;MAWD;;MAED,KAAKjC,KAAL,CAAW,uBAAX,GAEA;;MACA,MAAMe,SAAS,GAAG,KAAKuB,gBAAL,EAAlB;;MAEA,KAAKtB,aAAL,GAAqB,IAAI1B,YAAJ,CAAiB,IAAjB,EAAuByB,SAAvB,EAAkC;QACrDf,KAAK,EAAE,KAAKA,KADyC;QAErDuC,aAAa,EAAE,KAAKA,aAFiC;QAGrD3B,cAAc,EAAE,KAAKA,cAHgC;QAIrDO,iBAAiB,EAAE,KAAKN,kBAJ6B;QAKrD2B,iBAAiB,EAAE,KAAKA,iBAL6B;QAMrDC,iBAAiB,EAAE,KAAKA,iBAN6B;QAOrDC,gBAAgB,EAAE,KAAKA,gBAP8B;QAQrDC,qBAAqB,EAAE,KAAKA,qBARyB;QASrDC,mBAAmB,EAAE,KAAKA,mBAT2B;QAUrDlC,mBAAmB,EAAE,KAAKA,mBAV2B;QAWrDmC,2BAA2B,EAAE,KAAKA,2BAXmB;QAYrDC,6BAA6B,EAAE,KAAKA,6BAZiB;QAcrD5C,SAAS,EAAE6C,KAAK,IAAG;UACjB;UACA,IAAI,KAAKb,kBAAT,EAA6B;YAC3BC,YAAY,CAAC,KAAKD,kBAAN,CAAZ;YACA,KAAKA,kBAAL,GAA0BhB,SAA1B;UACD;;UAED,IAAI,CAAC,KAAKK,MAAV,EAAkB;YAChB,KAAKvB,KAAL,CACE,sEADF;;YAGA,KAAKgD,oBAAL;;YACA;UACD;;UACD,KAAK9C,SAAL,CAAe6C,KAAf;QACD,CA7BoD;QA8BrD5C,YAAY,EAAE4C,KAAK,IAAG;UACpB,KAAK5C,YAAL,CAAkB4C,KAAlB;QACD,CAhCoD;QAiCrDxC,YAAY,EAAEwC,KAAK,IAAG;UACpB,KAAKxC,YAAL,CAAkBwC,KAAlB;QACD,CAnCoD;QAoCrDvC,gBAAgB,EAAEyC,GAAG,IAAG;UACtB,KAAKjC,aAAL,GAAqBE,SAArB,CADsB,CACU;;UAEhC,IAAI,KAAKM,KAAL,KAAejC,eAAe,CAACuC,YAAnC,EAAiD;YAC/C;YACA,KAAKoB,mBAAL;;YACA,KAAKA,mBAAL,GAA2BhC,SAA3B;;YACA,KAAKQ,YAAL,CAAkBnC,eAAe,CAACO,QAAlC;UACD;;UAED,KAAKU,gBAAL,CAAsByC,GAAtB,EAVsB,CAWtB;UACA;;UACA,IAAI,KAAK1B,MAAT,EAAiB;YACf,KAAK4B,mBAAL;UACD;QACF,CApDoD;QAqDrD1C,gBAAgB,EAAEwC,GAAG,IAAG;UACtB,KAAKxC,gBAAL,CAAsBwC,GAAtB;QACD,CAvDoD;QAwDrD7C,kBAAkB,EAAEgD,OAAO,IAAG;UAC5B,KAAKhD,kBAAL,CAAwBgD,OAAxB;QACD,CA1DoD;QA2DrD/C,kBAAkB,EAAE0C,KAAK,IAAG;UAC1B,KAAK1C,kBAAL,CAAwB0C,KAAxB;QACD,CA7DoD;QA8DrDzC,gBAAgB,EAAEyC,KAAK,IAAG;UACxB,KAAKzC,gBAAL,CAAsByC,KAAtB;QACD;MAhEoD,CAAlC,CAArB;;MAmEA,KAAK/B,aAAL,CAAmBqC,KAAnB;IACD;EAAA;;EAEOf,gBAAgB;IACtB,IAAIvB,SAAJ;;IAEA,IAAI,KAAKuC,gBAAT,EAA2B;MACzBvC,SAAS,GAAG,KAAKuC,gBAAL,EAAZ;IACD,CAFD,MAEO;MACLvC,SAAS,GAAG,IAAIwC,SAAJ,CACV,KAAKC,SADK,EAEV,KAAKjB,aAAL,CAAmBkB,gBAAnB,EAFU,CAAZ;IAID;;IACD1C,SAAS,CAAC2C,UAAV,GAAuB,aAAvB;IACA,OAAO3C,SAAP;EACD;;EAEOoC,mBAAmB;IACzB,IAAI,KAAKQ,cAAL,GAAsB,CAA1B,EAA6B;MAC3B,KAAK3D,KAAL,CAAW,qCAAqC,KAAK2D,cAAc,IAAnE;MAEA,KAAKC,YAAL,GAAoBxB,UAAU,CAAC,MAAK;QAClC,KAAKJ,QAAL;MACD,CAF6B,EAE3B,KAAK2B,cAFsB,CAA9B;IAGD;EACF;EAED;;;;;;;;;;;EASaE,UAAU;;MACrB,IAAIC,UAAJ;;MAEA,IAAI,KAAKtC,KAAL,KAAejC,eAAe,CAACkC,MAAnC,EAA2C;QACzC,KAAKzB,KAAL,CACE,WAAWT,eAAe,CAAC,KAAKiC,KAAN,CAAY,+BADxC;QAGA,OAAOuC,OAAO,CAACC,OAAR,EAAP;MACD;;MAED,KAAKtC,YAAL,CAAkBnC,eAAe,CAACuC,YAAlC,GAEA;;;MACA,IAAI,KAAK8B,YAAT,EAAuB;QACrBzB,YAAY,CAAC,KAAKyB,YAAN,CAAZ;MACD;;MAED,IACE,KAAK5C,aAAL,IACA,KAAKD,SAAL,CAAekD,UAAf,KAA8BzE,gBAAgB,CAAC0E,MAFjD,EAGE;QACA;QACAJ,UAAU,GAAG,IAAIC,OAAJ,CAAkB,CAACC,OAAD,EAAUG,MAAV,KAAoB;UACjD,KAAKjB,mBAAL,GAA2Bc,OAA3B;QACD,CAFY,CAAb;MAGD,CARD,MAQO;QACL;QACA,KAAKtC,YAAL,CAAkBnC,eAAe,CAACO,QAAlC;;QACA,OAAOiE,OAAO,CAACC,OAAR,EAAP;MACD;;MAED,KAAKhB,oBAAL;;MAEA,OAAOc,UAAP;IACD;EAAA;EAED;;;;;;;;EAMOzB,eAAe;IACpB,IAAI,KAAKrB,aAAT,EAAwB;MACtB,KAAKA,aAAL,CAAmBqB,eAAnB;IACD;EACF;;EAEOW,oBAAoB;IAC1B;IACA,IAAI,KAAKhC,aAAT,EAAwB;MACtB,KAAKA,aAAL,CAAmBoD,OAAnB;;MACA,KAAKpD,aAAL,GAAqB,IAArB;IACD;EACF;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoCOqD,OAAO,CAACC,MAAD,EAAuB;IACnC,KAAKtD,aAAL,CAAmBqD,OAAnB,CAA2BC,MAA3B;EACD;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmCOC,eAAe,CAACC,SAAD,EAAoBC,QAApB,EAA+C;IACnE,KAAKzD,aAAL,CAAmBuD,eAAnB,CAAmCC,SAAnC,EAA8CC,QAA9C;EACD;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;EAwBOC,SAAS,CACdC,WADc,EAEdF,QAFc,EAGY;IAAA,IAA1BG,OAA0B,uEAAF,EAAE;IAE1B,OAAO,KAAK5D,aAAL,CAAmB0D,SAAnB,CAA6BC,WAA7B,EAA0CF,QAA1C,EAAoDG,OAApD,CAAP;EACD;EAED;;;;;;;;;;;;;;EAYOC,WAAW,CAACC,EAAD,EAAuC;IAAA,IAA1BF,OAA0B,uEAAF,EAAE;;IACvD,KAAK5D,aAAL,CAAmB6D,WAAnB,CAA+BC,EAA/B,EAAmCF,OAAnC;EACD;EAED;;;;;;;;EAMOG,KAAK,CAACC,aAAD,EAAuB;IACjC,OAAO,KAAKhE,aAAL,CAAmB+D,KAAnB,CAAyBC,aAAzB,CAAP;EACD;EAED;;;;;;;;;;;;;;EAYOC,MAAM,CAACD,aAAD,EAAsB;IACjC,KAAKhE,aAAL,CAAmBiE,MAAnB,CAA0BD,aAA1B;EACD;EAED;;;;;;;;;;;;;EAWOE,KAAK,CAACF,aAAD,EAAsB;IAChC,KAAKhE,aAAL,CAAmBkE,KAAnB,CAAyBF,aAAzB;EACD;EAED;;;;;;;;;;;;;;;EAaOG,GAAG,CACRC,SADQ,EAERC,cAFQ,EAGkB;IAAA,IAA1BT,OAA0B,uEAAF,EAAE;;IAE1B,KAAK5D,aAAL,CAAmBmE,GAAnB,CAAuBC,SAAvB,EAAkCC,cAAlC,EAAkDT,OAAlD;EACD;EAED;;;;;;;;;;;;;;;EAaOU,IAAI,CACTF,SADS,EAETC,cAFS,EAGiB;IAAA,IAA1BT,OAA0B,uEAAF,EAAE;;IAE1B,KAAK5D,aAAL,CAAmBsE,IAAnB,CAAwBF,SAAxB,EAAmCC,cAAnC,EAAmDT,OAAnD;EACD;;AA3wBgB", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ActivationState", "StompSocketState", "Versions", "Client", "constructor", "conf", "default", "INACTIVE", "noOp", "debug", "beforeConnect", "onConnect", "onDisconnect", "onUnhandledMessage", "onUnhandledReceipt", "onUnhandledFrame", "onStompError", "onWebSocketClose", "onWebSocketError", "logRawCommunication", "onChangeState", "connectHeaders", "_disconnectHeaders", "configure", "webSocket", "_stomp<PERSON><PERSON><PERSON>", "_webSocket", "undefined", "disconnectHeaders", "value", "connected", "connectedVersion", "active", "state", "ACTIVE", "_changeState", "Object", "assign", "activate", "DEACTIVATING", "Error", "_connect", "connectionTimeout", "_connectionWatcher", "clearTimeout", "setTimeout", "forceDisconnect", "_createWebSocket", "stompV<PERSON><PERSON>", "heartbeatIncoming", "heartbeatOutgoing", "splitLargeFrames", "maxWebSocketChunkSize", "forceBinaryWSFrames", "appendMissingNULLonIncoming", "discardWebsocketOnCommFailure", "frame", "_dispose<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evt", "_resolveSocketClose", "_schedule_reconnect", "message", "start", "webSocketFactory", "WebSocket", "brokerURL", "protocolVersions", "binaryType", "reconnectDelay", "_reconnector", "deactivate", "retPromise", "Promise", "resolve", "readyState", "CLOSED", "reject", "dispose", "publish", "params", "watchForReceipt", "receiptId", "callback", "subscribe", "destination", "headers", "unsubscribe", "id", "begin", "transactionId", "commit", "abort", "ack", "messageId", "subscriptionId", "nack"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\client.ts"], "sourcesContent": ["import { ITransaction } from './i-transaction';\nimport { StompConfig } from './stomp-config';\nimport { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler';\nimport { StompHeaders } from './stomp-headers';\nimport { StompSubscription } from './stomp-subscription';\nimport {\n  ActivationState,\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  messageCallbackType,\n  StompSocketState,\n  wsErrorCallbackType,\n} from './types';\nimport { Versions } from './versions';\n\n/**\n * @internal\n */\ndeclare const WebSocket: {\n  prototype: IStompSocket;\n  new (url: string, protocols?: string | string[]): IStompSocket;\n};\n\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * The URL for the STOMP broker to connect to.\n   * Typically like `\"ws://broker.329broker.com:15674/ws\"` or `\"wss://broker.329broker.com:15674/ws\"`.\n   *\n   * Only one of this or [Client#webSocketFactory]{@link Client#webSocketFactory} need to be set.\n   * If both are set, [Client#webSocketFactory]{@link Client#webSocketFactory} will be used.\n   *\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   */\n  public brokerURL: string;\n\n  /**\n   * STOMP versions to attempt during STOMP handshake. By default versions `1.0`, `1.1`, and `1.2` are attempted.\n   *\n   * Example:\n   * ```javascript\n   *        // Try only versions 1.0 and 1.1\n   *        client.stompVersions = new Versions(['1.0', '1.1'])\n   * ```\n   */\n  public stompVersions = Versions.default;\n\n  /**\n   * This function should return a WebSocket or a similar (e.g. SockJS) object.\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   * If your STOMP Broker supports WebSockets, prefer setting [Client#brokerURL]{@link Client#brokerURL}.\n   *\n   * If both this and [Client#brokerURL]{@link Client#brokerURL} are set, this will be used.\n   *\n   * Example:\n   * ```javascript\n   *        // use a WebSocket\n   *        client.webSocketFactory= function () {\n   *          return new WebSocket(\"wss://broker.329broker.com:15674/ws\");\n   *        };\n   *\n   *        // Typical usage with SockJS\n   *        client.webSocketFactory= function () {\n   *          return new SockJS(\"http://broker.329broker.com/stomp\");\n   *        };\n   * ```\n   */\n  public webSocketFactory: () => IStompSocket;\n\n  /**\n   * Will retry if Stomp connection is not established in specified milliseconds.\n   * Default 0, which implies wait for ever.\n   */\n  public connectionTimeout: number = 0;\n\n  private _connectionWatcher: number; // Timer\n\n  /**\n   *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n   */\n  public reconnectDelay: number = 5000;\n\n  /**\n   * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatIncoming: number = 10000;\n\n  /**\n   * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatOutgoing: number = 10000;\n\n  /**\n   * This switches on a non standard behavior while sending WebSocket packets.\n   * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n   * Only Java Spring brokers seems to use this mode.\n   *\n   * WebSockets, by itself, split large (text) packets,\n   * so it is not needed with a truly compliant STOMP/WebSocket broker.\n   * Actually setting it for such broker will cause large messages to fail.\n   *\n   * `false` by default.\n   *\n   * Binary frames are never split.\n   */\n  public splitLargeFrames: boolean = false;\n\n  /**\n   * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n   * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n   */\n  public maxWebSocketChunkSize: number = 8 * 1024;\n\n  /**\n   * Usually the\n   * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n   * is automatically decided by type of the payload.\n   * Default is `false`, which should work with all compliant brokers.\n   *\n   * Set this flag to force binary frames.\n   */\n  public forceBinaryWSFrames: boolean = false;\n\n  /**\n   * A bug in ReactNative chops a string on occurrence of a NULL.\n   * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n   * This makes incoming WebSocket messages invalid STOMP packets.\n   * Setting this flag attempts to reverse the damage by appending a NULL.\n   * If the broker splits a large message into multiple WebSocket messages,\n   * this flag will cause data loss and abnormal termination of connection.\n   *\n   * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n   */\n  public appendMissingNULLonIncoming: boolean = false;\n\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket(): IStompSocket {\n    return this._stompHandler ? this._stompHandler._webSocket : undefined;\n  }\n\n  /**\n   * Connection headers, important keys - `login`, `passcode`, `host`.\n   * Though STOMP 1.2 standard marks these keys to be present, check your broker documentation for\n   * details specific to your broker.\n   */\n  public connectHeaders: StompHeaders;\n\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders(): StompHeaders {\n    return this._disconnectHeaders;\n  }\n\n  set disconnectHeaders(value: StompHeaders) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  private _disconnectHeaders: StompHeaders;\n\n  /**\n   * This function will be called for any unhandled messages.\n   * It is useful for receiving messages sent to RabbitMQ temporary queues.\n   *\n   * It can also get invoked with stray messages while the server is processing\n   * a request to [Client#unsubscribe]{@link Client#unsubscribe}\n   * from an endpoint.\n   *\n   * The actual {@link IMessage} will be passed as parameter to the callback.\n   */\n  public onUnhandledMessage: messageCallbackType;\n\n  /**\n   * STOMP brokers can be requested to notify when an operation is actually completed.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}. See\n   * [Client#watchForReceipt]{@link Client#watchForReceipt} for examples.\n   *\n   * The actual {@link FrameImpl} will be passed as parameter to the callback.\n   */\n  public onUnhandledReceipt: frameCallbackType;\n\n  /**\n   * Will be invoked if {@link FrameImpl} of unknown type is received from the STOMP broker.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onUnhandledFrame: frameCallbackType;\n\n  /**\n   * `true` if there is a active connection with STOMP Broker\n   */\n  get connected(): boolean {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n\n  /**\n   * Callback, invoked on before a connection connection to the STOMP broker.\n   *\n   * You can change options on the client, which will impact the immediate connect.\n   * It is valid to call [Client#decativate]{@link Client#deactivate} in this callback.\n   *\n   * As of version 5.1, this callback can be\n   * [async](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/async_function)\n   * (i.e., it can return a\n   * [Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)).\n   * In that case connect will be called only after the Promise is resolved.\n   * This can be used to reliably fetch credentials, access token etc. from some other service\n   * in an asynchronous way.\n   */\n  public beforeConnect: () => void | Promise<void>;\n\n  /**\n   * Callback, invoked on every successful connection to the STOMP broker.\n   *\n   * The actual {@link FrameImpl} will be passed as parameter to the callback.\n   * Sometimes clients will like to use headers from this frame.\n   */\n  public onConnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on every successful disconnection from the STOMP broker. It will not be invoked if\n   * the STOMP broker disconnected due to an error.\n   *\n   * The actual Receipt {@link FrameImpl} acknowledging the DISCONNECT will be passed as parameter to the callback.\n   *\n   * The way STOMP protocol is designed, the connection may close/terminate without the client\n   * receiving the Receipt {@link FrameImpl} acknowledging the DISCONNECT.\n   * You might find [Client#onWebSocketClose]{@link Client#onWebSocketClose} more appropriate to watch\n   * STOMP broker disconnects.\n   */\n  public onDisconnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on an ERROR frame received from the STOMP Broker.\n   * A compliant STOMP Broker will close the connection after this type of frame.\n   * Please check broker specific documentation for exact behavior.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onStompError: frameCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket is closed.\n   *\n   * Actual [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketClose: closeEventCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket raises an error.\n   *\n   * Actual [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketError: wsErrorCallbackType;\n\n  /**\n   * Set it to log the actual raw communication with the broker.\n   * When unset, it logs headers of the parsed frames.\n   *\n   * Change in this effects from next broker reconnect.\n   *\n   * **Caution: this assumes that frames only have valid UTF8 strings.**\n   */\n  public logRawCommunication: boolean;\n\n  /**\n   * By default, debug messages are discarded. To log to `console` following can be used:\n   *\n   * ```javascript\n   *        client.debug = function(str) {\n   *          console.log(str);\n   *        };\n   * ```\n   *\n   * Currently this method does not support levels of log. Be aware that the output can be quite verbose\n   * and may contain sensitive information (like passwords, tokens etc.).\n   */\n  public debug: debugFnType;\n\n  /**\n   * Browsers do not immediately close WebSockets when `.close` is issued.\n   * This may cause reconnection to take a longer on certain type of failures.\n   * In case of incoming heartbeat failure, this experimental flag instructs the library\n   * to discard the socket immediately (even before it is actually closed).\n   */\n  public discardWebsocketOnCommFailure: boolean;\n\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion(): string {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n\n  private _stompHandler: StompHandler;\n\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active(): boolean {\n    return this.state === ActivationState.ACTIVE;\n  }\n\n  /**\n   * It will be called on state change.\n   *\n   * When deactivating it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public onChangeState: (state: ActivationState) => void;\n\n  private _changeState(state: ActivationState) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n\n  // This will mark deactivate to complete, to be called after Websocket is closed\n  private _resolveSocketClose: (value?: PromiseLike<void> | void) => void;\n\n  /**\n   * Activation state.\n   *\n   * It will usually be ACTIVE or INACTIVE.\n   * When deactivating it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public state: ActivationState = ActivationState.INACTIVE;\n\n  private _reconnector: any;\n\n  /**\n   * Create an instance.\n   */\n  constructor(conf: StompConfig = {}) {\n    // Dummy callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n\n    // Apply configuration\n    this.configure(conf);\n  }\n\n  /**\n   * Update configuration.\n   */\n  public configure(conf: StompConfig): void {\n    // bulk assign all properties to this\n    (Object as any).assign(this, conf);\n  }\n\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect.\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  public activate(): void {\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug(\n        'Still DEACTIVATING, please await call to deactivate before trying to re-activate'\n      );\n      throw new Error('Still DEACTIVATING, can not activate now');\n    }\n\n    if (this.active) {\n      this.debug('Already ACTIVE, ignoring request to activate');\n      return;\n    }\n\n    this._changeState(ActivationState.ACTIVE);\n\n    this._connect();\n  }\n\n  private async _connect(): Promise<void> {\n    if (this.connected) {\n      this.debug('STOMP: already connected, nothing to do');\n      return;\n    }\n\n    await this.beforeConnect();\n\n    if (!this.active) {\n      this.debug(\n        'Client has been marked inactive, will not attempt to connect'\n      );\n      return;\n    }\n\n    // setup connection watcher\n    if (this.connectionTimeout > 0) {\n      // clear first\n      if (this._connectionWatcher) {\n        clearTimeout(this._connectionWatcher);\n      }\n      this._connectionWatcher = setTimeout(() => {\n        if (this.connected) {\n          return;\n        }\n        // Connection not established, close the underlying socket\n        // a reconnection will be attempted\n        this.debug(\n          `Connection not established in ${this.connectionTimeout}ms, closing socket`\n        );\n        this.forceDisconnect();\n      }, this.connectionTimeout);\n    }\n\n    this.debug('Opening Web Socket...');\n\n    // Get the actual WebSocket (or a similar object)\n    const webSocket = this._createWebSocket();\n\n    this._stompHandler = new StompHandler(this, webSocket, {\n      debug: this.debug,\n      stompVersions: this.stompVersions,\n      connectHeaders: this.connectHeaders,\n      disconnectHeaders: this._disconnectHeaders,\n      heartbeatIncoming: this.heartbeatIncoming,\n      heartbeatOutgoing: this.heartbeatOutgoing,\n      splitLargeFrames: this.splitLargeFrames,\n      maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n      forceBinaryWSFrames: this.forceBinaryWSFrames,\n      logRawCommunication: this.logRawCommunication,\n      appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n      discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n\n      onConnect: frame => {\n        // Successfully connected, stop the connection watcher\n        if (this._connectionWatcher) {\n          clearTimeout(this._connectionWatcher);\n          this._connectionWatcher = undefined;\n        }\n\n        if (!this.active) {\n          this.debug(\n            'STOMP got connected while deactivate was issued, will disconnect now'\n          );\n          this._disposeStompHandler();\n          return;\n        }\n        this.onConnect(frame);\n      },\n      onDisconnect: frame => {\n        this.onDisconnect(frame);\n      },\n      onStompError: frame => {\n        this.onStompError(frame);\n      },\n      onWebSocketClose: evt => {\n        this._stompHandler = undefined; // a new one will be created in case of a reconnect\n\n        if (this.state === ActivationState.DEACTIVATING) {\n          // Mark deactivation complete\n          this._resolveSocketClose();\n          this._resolveSocketClose = undefined;\n          this._changeState(ActivationState.INACTIVE);\n        }\n\n        this.onWebSocketClose(evt);\n        // The callback is called before attempting to reconnect, this would allow the client\n        // to be `deactivated` in the callback.\n        if (this.active) {\n          this._schedule_reconnect();\n        }\n      },\n      onWebSocketError: evt => {\n        this.onWebSocketError(evt);\n      },\n      onUnhandledMessage: message => {\n        this.onUnhandledMessage(message);\n      },\n      onUnhandledReceipt: frame => {\n        this.onUnhandledReceipt(frame);\n      },\n      onUnhandledFrame: frame => {\n        this.onUnhandledFrame(frame);\n      },\n    });\n\n    this._stompHandler.start();\n  }\n\n  private _createWebSocket(): IStompSocket {\n    let webSocket: IStompSocket;\n\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else {\n      webSocket = new WebSocket(\n        this.brokerURL,\n        this.stompVersions.protocolVersions()\n      );\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n\n  private _schedule_reconnect(): void {\n    if (this.reconnectDelay > 0) {\n      this.debug(`STOMP: scheduling reconnection in ${this.reconnectDelay}ms`);\n\n      this._reconnector = setTimeout(() => {\n        this._connect();\n      }, this.reconnectDelay);\n    }\n  }\n\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if underlying STOMP connection was connected.\n   *\n   * This call is async, it will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after underlying websocket is properly disposed.\n   *\n   * To reactivate you can call [Client#activate]{@link Client#activate}.\n   */\n  public async deactivate(): Promise<void> {\n    let retPromise: Promise<void>;\n\n    if (this.state !== ActivationState.ACTIVE) {\n      this.debug(\n        `Already ${ActivationState[this.state]}, ignoring call to deactivate`\n      );\n      return Promise.resolve();\n    }\n\n    this._changeState(ActivationState.DEACTIVATING);\n\n    // Clear if a reconnection was scheduled\n    if (this._reconnector) {\n      clearTimeout(this._reconnector);\n    }\n\n    if (\n      this._stompHandler &&\n      this.webSocket.readyState !== StompSocketState.CLOSED\n    ) {\n      // we need to wait for underlying websocket to close\n      retPromise = new Promise<void>((resolve, reject) => {\n        this._resolveSocketClose = resolve;\n      });\n    } else {\n      // indicate that auto reconnect loop should terminate\n      this._changeState(ActivationState.INACTIVE);\n      return Promise.resolve();\n    }\n\n    this._disposeStompHandler();\n\n    return retPromise;\n  }\n\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different than a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  public forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n\n  private _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n      this._stompHandler = null;\n    }\n  }\n\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body use binaryBody parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect if message body has NULL octet(s)\n   * and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  public publish(params: IPublishParams) {\n    this._stompHandler.publish(params);\n  }\n\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use. Typically a sequence, a UUID, a\n   * random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based in the value of the receipt-id.\n   *\n   * This method allow watching for a receipt and invoke the callback\n   * when corresponding receipt has been received.\n   *\n   * The actual {@link FrameImpl} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each received message with\n   * the {@link IMessage} as argument.\n   *\n   * Note: The library will generate an unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the headers argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {}\n  ): StompSubscription {\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: http://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    this._stompHandler.unsubscribe(id, headers);\n  }\n\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  public begin(transactionId?: string): ITransaction {\n    return this._stompHandler.begin(transactionId);\n  }\n\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  public commit(transactionId: string): void {\n    this._stompHandler.commit(transactionId);\n  }\n\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  public abort(transactionId: string): void {\n    this._stompHandler.abort(transactionId);\n  }\n\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}