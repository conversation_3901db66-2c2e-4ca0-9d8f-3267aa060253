{"ast": null, "code": "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n\n      var TempCtor = function TempCtor() {};\n\n      TempCtor.prototype = superCtor.prototype;\n      ctor.prototype = new TempCtor();\n      ctor.prototype.constructor = ctor;\n    }\n  };\n}", "map": {"version": 3, "names": ["Object", "create", "module", "exports", "inherits", "ctor", "superCtor", "super_", "prototype", "constructor", "value", "enumerable", "writable", "configurable", "TempCtor"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/inherits/inherits_browser.js"], "sourcesContent": ["if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n"], "mappings": "AAAA,IAAI,OAAOA,MAAM,CAACC,MAAd,KAAyB,UAA7B,EAAyC;EACvC;EACAC,MAAM,CAACC,OAAP,GAAiB,SAASC,QAAT,CAAkBC,IAAlB,EAAwBC,SAAxB,EAAmC;IAClD,IAAIA,SAAJ,EAAe;MACbD,IAAI,CAACE,MAAL,GAAcD,SAAd;MACAD,IAAI,CAACG,SAAL,GAAiBR,MAAM,CAACC,MAAP,CAAcK,SAAS,CAACE,SAAxB,EAAmC;QAClDC,WAAW,EAAE;UACXC,KAAK,EAAEL,IADI;UAEXM,UAAU,EAAE,KAFD;UAGXC,QAAQ,EAAE,IAHC;UAIXC,YAAY,EAAE;QAJH;MADqC,CAAnC,CAAjB;IAQD;EACF,CAZD;AAaD,CAfD,MAeO;EACL;EACAX,MAAM,CAACC,OAAP,GAAiB,SAASC,QAAT,CAAkBC,IAAlB,EAAwBC,SAAxB,EAAmC;IAClD,IAAIA,SAAJ,EAAe;MACbD,IAAI,CAACE,MAAL,GAAcD,SAAd;;MACA,IAAIQ,QAAQ,GAAG,SAAXA,QAAW,GAAY,CAAE,CAA7B;;MACAA,QAAQ,CAACN,SAAT,GAAqBF,SAAS,CAACE,SAA/B;MACAH,IAAI,CAACG,SAAL,GAAiB,IAAIM,QAAJ,EAAjB;MACAT,IAAI,CAACG,SAAL,CAAeC,WAAf,GAA6BJ,IAA7B;IACD;EACF,CARD;AASD"}, "metadata": {}, "sourceType": "script"}