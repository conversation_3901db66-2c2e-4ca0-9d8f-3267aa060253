{"ast": null, "code": "import { useLayoutEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useLayoutEffect);\nexport default useEffectWithTarget;", "map": {"version": 3, "names": ["useLayoutEffect", "createEffectWithTarget", "useEffectWithTarget"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useLayoutEffect);\nexport default useEffectWithTarget;"], "mappings": "AAAA,SAASA,eAAT,QAAgC,OAAhC;AACA,OAAOC,sBAAP,MAAmC,0BAAnC;AACA,IAAIC,mBAAmB,GAAGD,sBAAsB,CAACD,eAAD,CAAhD;AACA,eAAeE,mBAAf"}, "metadata": {}, "sourceType": "module"}