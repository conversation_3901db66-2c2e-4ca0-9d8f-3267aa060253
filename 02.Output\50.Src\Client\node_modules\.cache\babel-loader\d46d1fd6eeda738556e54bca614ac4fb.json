{"ast": null, "code": "/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\n\nconst LF = 10;\n/**\n * @internal\n */\n\nconst CR = 13;\n/**\n * @internal\n */\n\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\n\nexport class Parser {\n  constructor(onFrame, onIncomingPing) {\n    this.onFrame = onFrame;\n    this.onIncomingPing = onIncomingPing;\n    this._encoder = new TextEncoder();\n    this._decoder = new TextDecoder();\n    this._token = [];\n\n    this._initState();\n  }\n\n  parseChunk(segment) {\n    let appendMissingNULLonIncoming = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let chunk;\n\n    if (segment instanceof ArrayBuffer) {\n      chunk = new Uint8Array(segment);\n    } else {\n      chunk = this._encoder.encode(segment);\n    } // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n\n\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    } // tslint:disable-next-line:prefer-for-of\n\n\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n\n      this._onByte(byte);\n    }\n  } // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n\n\n  _collectFrame(byte) {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n\n    this._onByte = this._collectCommand;\n\n    this._reinjectByte(byte);\n  }\n\n  _collectCommand(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  _collectHeaders(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n\n    if (byte === LF) {\n      this._setupCollectBody();\n\n      return;\n    }\n\n    this._onByte = this._collectHeaderKey;\n\n    this._reinjectByte(byte);\n  }\n\n  _reinjectByte(byte) {\n    this._onByte(byte);\n  }\n\n  _collectHeaderKey(byte) {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  _collectHeaderValue(byte) {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n\n    if (byte === LF) {\n      this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(header => {\n      return header[0] === 'content-length';\n    })[0];\n\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n\n  _collectBodyNullTerminated(byte) {\n    if (byte === NULL) {\n      this._retrievedBody();\n\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  _collectBodyFixedSize(byte) {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if (this._bodyBytesRemaining-- === 0) {\n      this._retrievedBody();\n\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n    this.onFrame(this._results);\n\n    this._initState();\n  } // Rec Descent Parser helpers\n\n\n  _consumeByte(byte) {\n    this._token.push(byte);\n  }\n\n  _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n\n  _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n\n  _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined\n    };\n    this._token = [];\n    this._headerKey = undefined;\n    this._onByte = this._collectFrame;\n  }\n\n}", "map": {"version": 3, "mappings": "AAEA;;;AAGA,MAAMA,IAAI,GAAG,CAAb;AACA;;;;AAGA,MAAMC,EAAE,GAAG,EAAX;AACA;;;;AAGA,MAAMC,EAAE,GAAG,EAAX;AACA;;;;AAGA,MAAMC,KAAK,GAAG,EAAd;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,OAAM,MAAOC,MAAP,CAAa;EAYjBC,YACSC,OADT,EAESC,cAFT,EAEmC;IAD1B;IACA;IAbQ,gBAAW,IAAIC,WAAJ,EAAX;IACA,gBAAW,IAAIC,WAAJ,EAAX;IAIT,cAAmB,EAAnB;;IAUN,KAAKC,UAAL;EACD;;EAEMC,UAAU,CACfC,OADe,EAE6B;IAAA,IAA5CC,2BAA4C,uEAAL,KAAK;IAE5C,IAAIC,KAAJ;;IAEA,IAAIF,OAAO,YAAYG,WAAvB,EAAoC;MAClCD,KAAK,GAAG,IAAIE,UAAJ,CAAeJ,OAAf,CAAR;IACD,CAFD,MAEO;MACLE,KAAK,GAAG,KAAKG,QAAL,CAAcC,MAAd,CAAqBN,OAArB,CAAR;IACD,CAR2C,CAU5C;IACA;IACA;IACA;;;IACA,IAAIC,2BAA2B,IAAIC,KAAK,CAACA,KAAK,CAACK,MAAN,GAAe,CAAhB,CAAL,KAA4B,CAA/D,EAAkE;MAChE,MAAMC,aAAa,GAAG,IAAIJ,UAAJ,CAAeF,KAAK,CAACK,MAAN,GAAe,CAA9B,CAAtB;MACAC,aAAa,CAACC,GAAd,CAAkBP,KAAlB,EAAyB,CAAzB;MACAM,aAAa,CAACN,KAAK,CAACK,MAAP,CAAb,GAA8B,CAA9B;MACAL,KAAK,GAAGM,aAAR;IACD,CAnB2C,CAqB5C;;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAAK,CAACK,MAA1B,EAAkCG,CAAC,EAAnC,EAAuC;MACrC,MAAMC,IAAI,GAAGT,KAAK,CAACQ,CAAD,CAAlB;;MACA,KAAKE,OAAL,CAAaD,IAAb;IACD;EACF,CA/CgB,CAiDjB;EACA;;;EAEQE,aAAa,CAACF,IAAD,EAAa;IAChC,IAAIA,IAAI,KAAKvB,IAAb,EAAmB;MACjB;MACA;IACD;;IACD,IAAIuB,IAAI,KAAKrB,EAAb,EAAiB;MACf;MACA;IACD;;IACD,IAAIqB,IAAI,KAAKtB,EAAb,EAAiB;MACf;MACA,KAAKM,cAAL;MACA;IACD;;IAED,KAAKiB,OAAL,GAAe,KAAKE,eAApB;;IACA,KAAKC,aAAL,CAAmBJ,IAAnB;EACD;;EAEOG,eAAe,CAACH,IAAD,EAAa;IAClC,IAAIA,IAAI,KAAKrB,EAAb,EAAiB;MACf;MACA;IACD;;IACD,IAAIqB,IAAI,KAAKtB,EAAb,EAAiB;MACf,KAAK2B,QAAL,CAAcC,OAAd,GAAwB,KAAKC,mBAAL,EAAxB;MACA,KAAKN,OAAL,GAAe,KAAKO,eAApB;MACA;IACD;;IAED,KAAKC,YAAL,CAAkBT,IAAlB;EACD;;EAEOQ,eAAe,CAACR,IAAD,EAAa;IAClC,IAAIA,IAAI,KAAKrB,EAAb,EAAiB;MACf;MACA;IACD;;IACD,IAAIqB,IAAI,KAAKtB,EAAb,EAAiB;MACf,KAAKgC,iBAAL;;MACA;IACD;;IACD,KAAKT,OAAL,GAAe,KAAKU,iBAApB;;IACA,KAAKP,aAAL,CAAmBJ,IAAnB;EACD;;EAEOI,aAAa,CAACJ,IAAD,EAAa;IAChC,KAAKC,OAAL,CAAaD,IAAb;EACD;;EAEOW,iBAAiB,CAACX,IAAD,EAAa;IACpC,IAAIA,IAAI,KAAKpB,KAAb,EAAoB;MAClB,KAAKgC,UAAL,GAAkB,KAAKL,mBAAL,EAAlB;MACA,KAAKN,OAAL,GAAe,KAAKY,mBAApB;MACA;IACD;;IACD,KAAKJ,YAAL,CAAkBT,IAAlB;EACD;;EAEOa,mBAAmB,CAACb,IAAD,EAAa;IACtC,IAAIA,IAAI,KAAKrB,EAAb,EAAiB;MACf;MACA;IACD;;IACD,IAAIqB,IAAI,KAAKtB,EAAb,EAAiB;MACf,KAAK2B,QAAL,CAAcS,OAAd,CAAsBC,IAAtB,CAA2B,CAAC,KAAKH,UAAN,EAAkB,KAAKL,mBAAL,EAAlB,CAA3B;;MACA,KAAKK,UAAL,GAAkBI,SAAlB;MACA,KAAKf,OAAL,GAAe,KAAKO,eAApB;MACA;IACD;;IACD,KAAKC,YAAL,CAAkBT,IAAlB;EACD;;EAEOU,iBAAiB;IACvB,MAAMO,mBAAmB,GAAG,KAAKZ,QAAL,CAAcS,OAAd,CAAsBI,MAAtB,CACzBC,MAAD,IAA6B;MAC3B,OAAOA,MAAM,CAAC,CAAD,CAAN,KAAc,gBAArB;IACD,CAHyB,EAI1B,CAJ0B,CAA5B;;IAMA,IAAIF,mBAAJ,EAAyB;MACvB,KAAKG,mBAAL,GAA2BC,QAAQ,CAACJ,mBAAmB,CAAC,CAAD,CAApB,EAAyB,EAAzB,CAAnC;MACA,KAAKhB,OAAL,GAAe,KAAKqB,qBAApB;IACD,CAHD,MAGO;MACL,KAAKrB,OAAL,GAAe,KAAKsB,0BAApB;IACD;EACF;;EAEOA,0BAA0B,CAACvB,IAAD,EAAa;IAC7C,IAAIA,IAAI,KAAKvB,IAAb,EAAmB;MACjB,KAAK+C,cAAL;;MACA;IACD;;IACD,KAAKf,YAAL,CAAkBT,IAAlB;EACD;;EAEOsB,qBAAqB,CAACtB,IAAD,EAAa;IACxC;IACA,IAAI,KAAKoB,mBAAL,OAA+B,CAAnC,EAAsC;MACpC,KAAKI,cAAL;;MACA;IACD;;IACD,KAAKf,YAAL,CAAkBT,IAAlB;EACD;;EAEOwB,cAAc;IACpB,KAAKnB,QAAL,CAAcoB,UAAd,GAA2B,KAAKC,kBAAL,EAA3B;IAEA,KAAK3C,OAAL,CAAa,KAAKsB,QAAlB;;IAEA,KAAKlB,UAAL;EACD,CAnKgB,CAqKjB;;;EAEQsB,YAAY,CAACT,IAAD,EAAa;IAC/B,KAAK2B,MAAL,CAAYZ,IAAZ,CAAiBf,IAAjB;EACD;;EAEOO,mBAAmB;IACzB,OAAO,KAAKqB,QAAL,CAAcC,MAAd,CAAqB,KAAKH,kBAAL,EAArB,CAAP;EACD;;EAEOA,kBAAkB;IACxB,MAAMI,SAAS,GAAG,IAAIrC,UAAJ,CAAe,KAAKkC,MAApB,CAAlB;IACA,KAAKA,MAAL,GAAc,EAAd;IACA,OAAOG,SAAP;EACD;;EAEO3C,UAAU;IAChB,KAAKkB,QAAL,GAAgB;MACdC,OAAO,EAAEU,SADK;MAEdF,OAAO,EAAE,EAFK;MAGdW,UAAU,EAAET;IAHE,CAAhB;IAMA,KAAKW,MAAL,GAAc,EAAd;IACA,KAAKf,UAAL,GAAkBI,SAAlB;IAEA,KAAKf,OAAL,GAAe,KAAKC,aAApB;EACD;;AAhMgB", "names": ["NULL", "LF", "CR", "COLON", "<PERSON><PERSON><PERSON>", "constructor", "onFrame", "onIncomingPing", "TextEncoder", "TextDecoder", "_initState", "parseChunk", "segment", "appendMissingNULLonIncoming", "chunk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "_encoder", "encode", "length", "chunkWithNull", "set", "i", "byte", "_onByte", "_collectFrame", "_collectCommand", "_reinjectByte", "_results", "command", "_consumeTokenAsUTF8", "_collectHeaders", "_consumeByte", "_setupCollectBody", "_collect<PERSON><PERSON><PERSON><PERSON>ey", "_<PERSON><PERSON><PERSON>", "_collectHeaderValue", "headers", "push", "undefined", "contentLengthHeader", "filter", "header", "_bodyBytesRemaining", "parseInt", "_collectBodyFixedSize", "_collectBodyNullTerminated", "_retrievedBody", "binaryBody", "_consumeTokenAsRaw", "_token", "_decoder", "decode", "rawResult"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\parser.ts"], "sourcesContent": ["import { IRawFrameType } from './types';\n\n/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  private readonly _encoder = new TextEncoder();\n  private readonly _decoder = new TextDecoder();\n\n  private _results: IRawFrameType;\n\n  private _token: number[] = [];\n  private _headerKey: string;\n  private _bodyBytesRemaining: number;\n\n  private _onByte: (byte: number) => void;\n\n  public constructor(\n    public onFrame: (rawFrame: IRawFrameType) => void,\n    public onIncomingPing: () => void\n  ) {\n    this._initState();\n  }\n\n  public parseChunk(\n    segment: string | ArrayBuffer,\n    appendMissingNULLonIncoming: boolean = false\n  ) {\n    let chunk: Uint8Array;\n\n    if (segment instanceof ArrayBuffer) {\n      chunk = new Uint8Array(segment);\n    } else {\n      chunk = this._encoder.encode(segment);\n    }\n\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n\n  private _collectFrame(byte: number): void {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n\n  private _collectCommand(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaders(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n\n  private _reinjectByte(byte: number) {\n    this._onByte(byte);\n  }\n\n  private _collectHeaderKey(byte: number): void {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaderValue(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([this._headerKey, this._consumeTokenAsUTF8()]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(\n      (header: [string, string]) => {\n        return header[0] === 'content-length';\n      }\n    )[0];\n\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n\n  private _collectBodyNullTerminated(byte: number): void {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectBodyFixedSize(byte: number): void {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if (this._bodyBytesRemaining-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n\n    this.onFrame(this._results);\n\n    this._initState();\n  }\n\n  // Rec Descent Parser helpers\n\n  private _consumeByte(byte: number) {\n    this._token.push(byte);\n  }\n\n  private _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n\n  private _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n\n  private _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined,\n    };\n\n    this._token = [];\n    this._headerKey = undefined;\n\n    this._onByte = this._collectFrame;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}