{"ast": null, "code": "var trimmedEndIndex = require('./_trimmedEndIndex');\n/** Used to match leading whitespace. */\n\n\nvar reTrimStart = /^\\s+/;\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\n\nfunction baseTrim(string) {\n  return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '') : string;\n}\n\nmodule.exports = baseTrim;", "map": {"version": 3, "names": ["trimmedEndIndex", "require", "reTrimStart", "baseTrim", "string", "slice", "replace", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseTrim.js"], "sourcesContent": ["var trimmedEndIndex = require('./_trimmedEndIndex');\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nmodule.exports = baseTrim;\n"], "mappings": "AAAA,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAD,CAA7B;AAEA;;;AACA,IAAIC,WAAW,GAAG,MAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,MAAlB,EAA0B;EACxB,OAAOA,MAAM,GACTA,MAAM,CAACC,KAAP,CAAa,CAAb,EAAgBL,eAAe,CAACI,MAAD,CAAf,GAA0B,CAA1C,EAA6CE,OAA7C,CAAqDJ,WAArD,EAAkE,EAAlE,CADS,GAETE,MAFJ;AAGD;;AAEDG,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}