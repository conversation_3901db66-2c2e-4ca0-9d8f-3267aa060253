{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\n\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n\n    return source;\n  } // eslint-disable-next-line consistent-return\n\n\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  } // eslint-disable-next-line consistent-return\n\n\n  function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    }\n  } // eslint-disable-next-line consistent-return\n\n\n  function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  } // eslint-disable-next-line consistent-return\n\n\n  function mergeDirectKeys(prop) {\n    if (prop in config2) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  var mergeMap = {\n    'url': valueFromConfig2,\n    'method': valueFromConfig2,\n    'data': valueFromConfig2,\n    'baseURL': defaultToConfig2,\n    'transformRequest': defaultToConfig2,\n    'transformResponse': defaultToConfig2,\n    'paramsSerializer': defaultToConfig2,\n    'timeout': defaultToConfig2,\n    'timeoutMessage': defaultToConfig2,\n    'withCredentials': defaultToConfig2,\n    'adapter': defaultToConfig2,\n    'responseType': defaultToConfig2,\n    'xsrfCookieName': defaultToConfig2,\n    'xsrfHeaderName': defaultToConfig2,\n    'onUploadProgress': defaultToConfig2,\n    'onDownloadProgress': defaultToConfig2,\n    'decompress': defaultToConfig2,\n    'maxContentLength': defaultToConfig2,\n    'maxBodyLength': defaultToConfig2,\n    'beforeRedirect': defaultToConfig2,\n    'transport': defaultToConfig2,\n    'httpAgent': defaultToConfig2,\n    'httpsAgent': defaultToConfig2,\n    'cancelToken': defaultToConfig2,\n    'socketPath': defaultToConfig2,\n    'responseEncoding': defaultToConfig2,\n    'validateStatus': mergeDirectKeys\n  };\n  utils.forEach(Object.keys(config1).concat(Object.keys(config2)), function computeConfigValue(prop) {\n    var merge = mergeMap[prop] || mergeDeepProperties;\n    var configValue = merge(prop);\n    utils.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n  });\n  return config;\n}", "map": {"version": 3, "names": ["utils", "mergeConfig", "config1", "config2", "config", "getMergedValue", "target", "source", "isPlainObject", "merge", "isArray", "slice", "mergeDeepProperties", "prop", "isUndefined", "undefined", "valueFromConfig2", "defaultToConfig2", "mergeDirectKeys", "mergeMap", "for<PERSON>ach", "Object", "keys", "concat", "computeConfigValue", "config<PERSON><PERSON><PERSON>"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(prop) {\n    if (prop in config2) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  const mergeMap = {\n    'url': valueFromConfig2,\n    'method': valueFromConfig2,\n    'data': valueFromConfig2,\n    'baseURL': defaultToConfig2,\n    'transformRequest': defaultToConfig2,\n    'transformResponse': defaultToConfig2,\n    'paramsSerializer': defaultToConfig2,\n    'timeout': defaultToConfig2,\n    'timeoutMessage': defaultToConfig2,\n    'withCredentials': defaultToConfig2,\n    'adapter': defaultToConfig2,\n    'responseType': defaultToConfig2,\n    'xsrfCookieName': defaultToConfig2,\n    'xsrfHeaderName': defaultToConfig2,\n    'onUploadProgress': defaultToConfig2,\n    'onDownloadProgress': defaultToConfig2,\n    'decompress': defaultToConfig2,\n    'maxContentLength': defaultToConfig2,\n    'maxBodyLength': defaultToConfig2,\n    'beforeRedirect': defaultToConfig2,\n    'transport': defaultToConfig2,\n    'httpAgent': defaultToConfig2,\n    'httpsAgent': defaultToConfig2,\n    'cancelToken': defaultToConfig2,\n    'socketPath': defaultToConfig2,\n    'responseEncoding': defaultToConfig2,\n    'validateStatus': mergeDirectKeys\n  };\n\n  utils.forEach(Object.keys(config1).concat(Object.keys(config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n}\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,WAAT,CAAqBC,OAArB,EAA8BC,OAA9B,EAAuC;EACpD;EACAA,OAAO,GAAGA,OAAO,IAAI,EAArB;EACA,IAAMC,MAAM,GAAG,EAAf;;EAEA,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,MAAhC,EAAwC;IACtC,IAAIP,KAAK,CAACQ,aAAN,CAAoBF,MAApB,KAA+BN,KAAK,CAACQ,aAAN,CAAoBD,MAApB,CAAnC,EAAgE;MAC9D,OAAOP,KAAK,CAACS,KAAN,CAAYH,MAAZ,EAAoBC,MAApB,CAAP;IACD,CAFD,MAEO,IAAIP,KAAK,CAACQ,aAAN,CAAoBD,MAApB,CAAJ,EAAiC;MACtC,OAAOP,KAAK,CAACS,KAAN,CAAY,EAAZ,EAAgBF,MAAhB,CAAP;IACD,CAFM,MAEA,IAAIP,KAAK,CAACU,OAAN,CAAcH,MAAd,CAAJ,EAA2B;MAChC,OAAOA,MAAM,CAACI,KAAP,EAAP;IACD;;IACD,OAAOJ,MAAP;EACD,CAdmD,CAgBpD;;;EACA,SAASK,mBAAT,CAA6BC,IAA7B,EAAmC;IACjC,IAAI,CAACb,KAAK,CAACc,WAAN,CAAkBX,OAAO,CAACU,IAAD,CAAzB,CAAL,EAAuC;MACrC,OAAOR,cAAc,CAACH,OAAO,CAACW,IAAD,CAAR,EAAgBV,OAAO,CAACU,IAAD,CAAvB,CAArB;IACD,CAFD,MAEO,IAAI,CAACb,KAAK,CAACc,WAAN,CAAkBZ,OAAO,CAACW,IAAD,CAAzB,CAAL,EAAuC;MAC5C,OAAOR,cAAc,CAACU,SAAD,EAAYb,OAAO,CAACW,IAAD,CAAnB,CAArB;IACD;EACF,CAvBmD,CAyBpD;;;EACA,SAASG,gBAAT,CAA0BH,IAA1B,EAAgC;IAC9B,IAAI,CAACb,KAAK,CAACc,WAAN,CAAkBX,OAAO,CAACU,IAAD,CAAzB,CAAL,EAAuC;MACrC,OAAOR,cAAc,CAACU,SAAD,EAAYZ,OAAO,CAACU,IAAD,CAAnB,CAArB;IACD;EACF,CA9BmD,CAgCpD;;;EACA,SAASI,gBAAT,CAA0BJ,IAA1B,EAAgC;IAC9B,IAAI,CAACb,KAAK,CAACc,WAAN,CAAkBX,OAAO,CAACU,IAAD,CAAzB,CAAL,EAAuC;MACrC,OAAOR,cAAc,CAACU,SAAD,EAAYZ,OAAO,CAACU,IAAD,CAAnB,CAArB;IACD,CAFD,MAEO,IAAI,CAACb,KAAK,CAACc,WAAN,CAAkBZ,OAAO,CAACW,IAAD,CAAzB,CAAL,EAAuC;MAC5C,OAAOR,cAAc,CAACU,SAAD,EAAYb,OAAO,CAACW,IAAD,CAAnB,CAArB;IACD;EACF,CAvCmD,CAyCpD;;;EACA,SAASK,eAAT,CAAyBL,IAAzB,EAA+B;IAC7B,IAAIA,IAAI,IAAIV,OAAZ,EAAqB;MACnB,OAAOE,cAAc,CAACH,OAAO,CAACW,IAAD,CAAR,EAAgBV,OAAO,CAACU,IAAD,CAAvB,CAArB;IACD,CAFD,MAEO,IAAIA,IAAI,IAAIX,OAAZ,EAAqB;MAC1B,OAAOG,cAAc,CAACU,SAAD,EAAYb,OAAO,CAACW,IAAD,CAAnB,CAArB;IACD;EACF;;EAED,IAAMM,QAAQ,GAAG;IACf,OAAOH,gBADQ;IAEf,UAAUA,gBAFK;IAGf,QAAQA,gBAHO;IAIf,WAAWC,gBAJI;IAKf,oBAAoBA,gBALL;IAMf,qBAAqBA,gBANN;IAOf,oBAAoBA,gBAPL;IAQf,WAAWA,gBARI;IASf,kBAAkBA,gBATH;IAUf,mBAAmBA,gBAVJ;IAWf,WAAWA,gBAXI;IAYf,gBAAgBA,gBAZD;IAaf,kBAAkBA,gBAbH;IAcf,kBAAkBA,gBAdH;IAef,oBAAoBA,gBAfL;IAgBf,sBAAsBA,gBAhBP;IAiBf,cAAcA,gBAjBC;IAkBf,oBAAoBA,gBAlBL;IAmBf,iBAAiBA,gBAnBF;IAoBf,kBAAkBA,gBApBH;IAqBf,aAAaA,gBArBE;IAsBf,aAAaA,gBAtBE;IAuBf,cAAcA,gBAvBC;IAwBf,eAAeA,gBAxBA;IAyBf,cAAcA,gBAzBC;IA0Bf,oBAAoBA,gBA1BL;IA2Bf,kBAAkBC;EA3BH,CAAjB;EA8BAlB,KAAK,CAACoB,OAAN,CAAcC,MAAM,CAACC,IAAP,CAAYpB,OAAZ,EAAqBqB,MAArB,CAA4BF,MAAM,CAACC,IAAP,CAAYnB,OAAZ,CAA5B,CAAd,EAAiE,SAASqB,kBAAT,CAA4BX,IAA5B,EAAkC;IACjG,IAAMJ,KAAK,GAAGU,QAAQ,CAACN,IAAD,CAAR,IAAkBD,mBAAhC;IACA,IAAMa,WAAW,GAAGhB,KAAK,CAACI,IAAD,CAAzB;IACCb,KAAK,CAACc,WAAN,CAAkBW,WAAlB,KAAkChB,KAAK,KAAKS,eAA7C,KAAkEd,MAAM,CAACS,IAAD,CAAN,GAAeY,WAAjF;EACD,CAJD;EAMA,OAAOrB,MAAP;AACD"}, "metadata": {}, "sourceType": "module"}