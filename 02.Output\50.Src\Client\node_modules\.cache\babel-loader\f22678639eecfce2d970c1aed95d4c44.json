{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\n\nvar useMutationObserver = function useMutationObserver(callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n\n    if (!element) {\n      return;\n    }\n\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      if (observer) {\n        observer.disconnect();\n      }\n    };\n  }, [options], target);\n};\n\nexport default useMutationObserver;", "map": {"version": 3, "names": ["getTargetElement", "useDeepCompareEffectWithTarget", "useLatest", "useMutationObserver", "callback", "target", "options", "callback<PERSON><PERSON>", "element", "observer", "MutationObserver", "current", "observe", "disconnect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useMutationObserver/index.js"], "sourcesContent": ["import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\nvar useMutationObserver = function useMutationObserver(callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n    if (!element) {\n      return;\n    }\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      if (observer) {\n        observer.disconnect();\n      }\n    };\n  }, [options], target);\n};\nexport default useMutationObserver;"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,8BAAP,MAA2C,mCAA3C;AACA,OAAOC,SAAP,MAAsB,cAAtB;;AACA,IAAIC,mBAAmB,GAAG,SAASA,mBAAT,CAA6BC,QAA7B,EAAuCC,MAAvC,EAA+CC,OAA/C,EAAwD;EAChF,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,WAAW,GAAGL,SAAS,CAACE,QAAD,CAA3B;EACAH,8BAA8B,CAAC,YAAY;IACzC,IAAIO,OAAO,GAAGR,gBAAgB,CAACK,MAAD,CAA9B;;IACA,IAAI,CAACG,OAAL,EAAc;MACZ;IACD;;IACD,IAAIC,QAAQ,GAAG,IAAIC,gBAAJ,CAAqBH,WAAW,CAACI,OAAjC,CAAf;IACAF,QAAQ,CAACG,OAAT,CAAiBJ,OAAjB,EAA0BF,OAA1B;IACA,OAAO,YAAY;MACjB,IAAIG,QAAJ,EAAc;QACZA,QAAQ,CAACI,UAAT;MACD;IACF,CAJD;EAKD,CAZ6B,EAY3B,CAACP,OAAD,CAZ2B,EAYhBD,MAZgB,CAA9B;AAaD,CAlBD;;AAmBA,eAAeF,mBAAf"}, "metadata": {}, "sourceType": "module"}