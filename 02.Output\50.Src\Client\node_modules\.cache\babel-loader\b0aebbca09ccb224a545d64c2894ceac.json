{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n    label: 0,\n    sent: function sent() {\n      if (t[0] & 1) throw t[1];\n      return t[1];\n    },\n    trys: [],\n    ops: []\n  },\n      f,\n      y,\n      t,\n      g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n\n          case 7:\n            op = _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n\n              _.ops.push(op);\n\n              break;\n            }\n\n            if (t[2]) _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n        }\n\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\n\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\n\nfunction useAsyncEffect(effect, deps) {\n  function isAsyncGenerator(val) {\n    return isFunction(val[Symbol.asyncIterator]);\n  }\n\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3\n              /*break*/\n              , 4];\n              _a.label = 1;\n\n            case 1:\n              if (!true) return [3\n              /*break*/\n              , 3];\n              return [4\n              /*yield*/\n              , e.next()];\n\n            case 2:\n              result = _a.sent();\n\n              if (result.done || cancelled) {\n                return [3\n                /*break*/\n                , 3];\n              }\n\n              return [3\n              /*break*/\n              , 1];\n\n            case 3:\n              return [3\n              /*break*/\n              , 6];\n\n            case 4:\n              return [4\n              /*yield*/\n              , e];\n\n            case 5:\n              _a.sent();\n\n              _a.label = 6;\n\n            case 6:\n              return [2\n              /*return*/\n              ];\n          }\n        });\n      });\n    }\n\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\n\nexport default useAsyncEffect;", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "verb", "Symbol", "iterator", "n", "v", "op", "TypeError", "call", "pop", "length", "push", "useEffect", "isFunction", "useAsyncEffect", "effect", "deps", "isAsyncGenerator", "val", "asyncIterator", "cancelled", "execute", "_a"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useAsyncEffect/index.js"], "sourcesContent": ["var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function sent() {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n          case 7:\n            op = _.ops.pop();\n            _.trys.pop();\n            continue;\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n              _.ops.push(op);\n              break;\n            }\n            if (t[2]) _.ops.pop();\n            _.trys.pop();\n            continue;\n        }\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\nfunction useAsyncEffect(effect, deps) {\n  function isAsyncGenerator(val) {\n    return isFunction(val[Symbol.asyncIterator]);\n  }\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3 /*break*/, 4];\n              _a.label = 1;\n            case 1:\n              if (!true) return [3 /*break*/, 3];\n              return [4 /*yield*/, e.next()];\n            case 2:\n              result = _a.sent();\n              if (result.done || cancelled) {\n                return [3 /*break*/, 3];\n              }\n              return [3 /*break*/, 1];\n            case 3:\n              return [3 /*break*/, 6];\n            case 4:\n              return [4 /*yield*/, e];\n            case 5:\n              _a.sent();\n              _a.label = 6;\n            case 6:\n              return [2 /*return*/];\n          }\n        });\n      });\n    }\n\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\nexport default useAsyncEffect;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,QAAQ,KAAKA,SAAb,IAA0B,UAAUC,OAAV,EAAmBC,UAAnB,EAA+BC,CAA/B,EAAkCC,SAAlC,EAA6C;EACrF,SAASC,KAAT,CAAeC,KAAf,EAAsB;IACpB,OAAOA,KAAK,YAAYH,CAAjB,GAAqBG,KAArB,GAA6B,IAAIH,CAAJ,CAAM,UAAUI,OAAV,EAAmB;MAC3DA,OAAO,CAACD,KAAD,CAAP;IACD,CAFmC,CAApC;EAGD;;EACD,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAT,CAAN,EAAyB,UAAUD,OAAV,EAAmBE,MAAnB,EAA2B;IACzD,SAASC,SAAT,CAAmBJ,KAAnB,EAA0B;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAV,CAAeN,KAAf,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASC,QAAT,CAAkBR,KAAlB,EAAyB;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAD,CAAT,CAAmBE,KAAnB,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASF,IAAT,CAAcI,MAAd,EAAsB;MACpBA,MAAM,CAACC,IAAP,GAAcT,OAAO,CAACQ,MAAM,CAACT,KAAR,CAArB,GAAsCD,KAAK,CAACU,MAAM,CAACT,KAAR,CAAL,CAAoBW,IAApB,CAAyBP,SAAzB,EAAoCI,QAApC,CAAtC;IACD;;IACDH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAV,CAAgBjB,OAAhB,EAAyBC,UAAU,IAAI,EAAvC,CAAb,EAAyDU,IAAzD,EAAD,CAAJ;EACD,CAnBM,CAAP;AAoBD,CA1BD;;AA2BA,IAAIO,WAAW,GAAG,QAAQ,KAAKA,WAAb,IAA4B,UAAUlB,OAAV,EAAmBmB,IAAnB,EAAyB;EACrE,IAAIC,CAAC,GAAG;IACJC,KAAK,EAAE,CADH;IAEJC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIC,CAAC,CAAC,CAAD,CAAD,GAAO,CAAX,EAAc,MAAMA,CAAC,CAAC,CAAD,CAAP;MACd,OAAOA,CAAC,CAAC,CAAD,CAAR;IACD,CALG;IAMJC,IAAI,EAAE,EANF;IAOJC,GAAG,EAAE;EAPD,CAAR;EAAA,IASEC,CATF;EAAA,IAUEC,CAVF;EAAA,IAWEJ,CAXF;EAAA,IAYEK,CAZF;EAaA,OAAOA,CAAC,GAAG;IACTjB,IAAI,EAAEkB,IAAI,CAAC,CAAD,CADD;IAET,SAASA,IAAI,CAAC,CAAD,CAFJ;IAGT,UAAUA,IAAI,CAAC,CAAD;EAHL,CAAJ,EAIJ,OAAOC,MAAP,KAAkB,UAAlB,KAAiCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAD,GAAqB,YAAY;IACnE,OAAO,IAAP;EACD,CAFE,CAJI,EAMHH,CANJ;;EAOA,SAASC,IAAT,CAAcG,CAAd,EAAiB;IACf,OAAO,UAAUC,CAAV,EAAa;MAClB,OAAOvB,IAAI,CAAC,CAACsB,CAAD,EAAIC,CAAJ,CAAD,CAAX;IACD,CAFD;EAGD;;EACD,SAASvB,IAAT,CAAcwB,EAAd,EAAkB;IAChB,IAAIR,CAAJ,EAAO,MAAM,IAAIS,SAAJ,CAAc,iCAAd,CAAN;;IACP,OAAOf,CAAP,EAAU;MACR,IAAI;QACF,IAAIM,CAAC,GAAG,CAAJ,EAAOC,CAAC,KAAKJ,CAAC,GAAGW,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR,GAAYP,CAAC,CAAC,QAAD,CAAb,GAA0BO,EAAE,CAAC,CAAD,CAAF,GAAQP,CAAC,CAAC,OAAD,CAAD,KAAe,CAACJ,CAAC,GAAGI,CAAC,CAAC,QAAD,CAAN,KAAqBJ,CAAC,CAACa,IAAF,CAAOT,CAAP,CAArB,EAAgC,CAA/C,CAAR,GAA4DA,CAAC,CAAChB,IAAjG,CAAD,IAA2G,CAAC,CAACY,CAAC,GAAGA,CAAC,CAACa,IAAF,CAAOT,CAAP,EAAUO,EAAE,CAAC,CAAD,CAAZ,CAAL,EAAuBnB,IAA9I,EAAoJ,OAAOQ,CAAP;QACpJ,IAAII,CAAC,GAAG,CAAJ,EAAOJ,CAAX,EAAcW,EAAE,GAAG,CAACA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAT,EAAYX,CAAC,CAAClB,KAAd,CAAL;;QACd,QAAQ6B,EAAE,CAAC,CAAD,CAAV;UACE,KAAK,CAAL;UACA,KAAK,CAAL;YACEX,CAAC,GAAGW,EAAJ;YACA;;UACF,KAAK,CAAL;YACEd,CAAC,CAACC,KAAF;YACA,OAAO;cACLhB,KAAK,EAAE6B,EAAE,CAAC,CAAD,CADJ;cAELnB,IAAI,EAAE;YAFD,CAAP;;UAIF,KAAK,CAAL;YACEK,CAAC,CAACC,KAAF;YACAM,CAAC,GAAGO,EAAE,CAAC,CAAD,CAAN;YACAA,EAAE,GAAG,CAAC,CAAD,CAAL;YACA;;UACF,KAAK,CAAL;YACEA,EAAE,GAAGd,CAAC,CAACK,GAAF,CAAMY,GAAN,EAAL;;YACAjB,CAAC,CAACI,IAAF,CAAOa,GAAP;;YACA;;UACF;YACE,IAAI,EAAEd,CAAC,GAAGH,CAAC,CAACI,IAAN,EAAYD,CAAC,GAAGA,CAAC,CAACe,MAAF,GAAW,CAAX,IAAgBf,CAAC,CAACA,CAAC,CAACe,MAAF,GAAW,CAAZ,CAAnC,MAAuDJ,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAeA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAhF,CAAJ,EAAwF;cACtFd,CAAC,GAAG,CAAJ;cACA;YACD;;YACD,IAAIc,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,KAAgB,CAACX,CAAD,IAAMW,EAAE,CAAC,CAAD,CAAF,GAAQX,CAAC,CAAC,CAAD,CAAT,IAAgBW,EAAE,CAAC,CAAD,CAAF,GAAQX,CAAC,CAAC,CAAD,CAA/C,CAAJ,EAAyD;cACvDH,CAAC,CAACC,KAAF,GAAUa,EAAE,CAAC,CAAD,CAAZ;cACA;YACD;;YACD,IAAIA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAed,CAAC,CAACC,KAAF,GAAUE,CAAC,CAAC,CAAD,CAA9B,EAAmC;cACjCH,CAAC,CAACC,KAAF,GAAUE,CAAC,CAAC,CAAD,CAAX;cACAA,CAAC,GAAGW,EAAJ;cACA;YACD;;YACD,IAAIX,CAAC,IAAIH,CAAC,CAACC,KAAF,GAAUE,CAAC,CAAC,CAAD,CAApB,EAAyB;cACvBH,CAAC,CAACC,KAAF,GAAUE,CAAC,CAAC,CAAD,CAAX;;cACAH,CAAC,CAACK,GAAF,CAAMc,IAAN,CAAWL,EAAX;;cACA;YACD;;YACD,IAAIX,CAAC,CAAC,CAAD,CAAL,EAAUH,CAAC,CAACK,GAAF,CAAMY,GAAN;;YACVjB,CAAC,CAACI,IAAF,CAAOa,GAAP;;YACA;QAzCJ;;QA2CAH,EAAE,GAAGf,IAAI,CAACiB,IAAL,CAAUpC,OAAV,EAAmBoB,CAAnB,CAAL;MACD,CA/CD,CA+CE,OAAOR,CAAP,EAAU;QACVsB,EAAE,GAAG,CAAC,CAAD,EAAItB,CAAJ,CAAL;QACAe,CAAC,GAAG,CAAJ;MACD,CAlDD,SAkDU;QACRD,CAAC,GAAGH,CAAC,GAAG,CAAR;MACD;IACF;;IACD,IAAIW,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAZ,EAAe,MAAMA,EAAE,CAAC,CAAD,CAAR;IACf,OAAO;MACL7B,KAAK,EAAE6B,EAAE,CAAC,CAAD,CAAF,GAAQA,EAAE,CAAC,CAAD,CAAV,GAAgB,KAAK,CADvB;MAELnB,IAAI,EAAE;IAFD,CAAP;EAID;AACF,CAzFD;;AA0FA,SAASyB,SAAT,QAA0B,OAA1B;AACA,SAASC,UAAT,QAA2B,UAA3B;;AACA,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;EACpC,SAASC,gBAAT,CAA0BC,GAA1B,EAA+B;IAC7B,OAAOL,UAAU,CAACK,GAAG,CAAChB,MAAM,CAACiB,aAAR,CAAJ,CAAjB;EACD;;EACDP,SAAS,CAAC,YAAY;IACpB,IAAI5B,CAAC,GAAG+B,MAAM,EAAd;IACA,IAAIK,SAAS,GAAG,KAAhB;;IACA,SAASC,OAAT,GAAmB;MACjB,OAAOlD,SAAS,CAAC,IAAD,EAAO,KAAK,CAAZ,EAAe,KAAK,CAApB,EAAuB,YAAY;QACjD,IAAIe,MAAJ;QACA,OAAOI,WAAW,CAAC,IAAD,EAAO,UAAUgC,EAAV,EAAc;UACrC,QAAQA,EAAE,CAAC7B,KAAX;YACE,KAAK,CAAL;cACE,IAAI,CAACwB,gBAAgB,CAACjC,CAAD,CAArB,EAA0B,OAAO,CAAC;cAAE;cAAH,EAAc,CAAd,CAAP;cAC1BsC,EAAE,CAAC7B,KAAH,GAAW,CAAX;;YACF,KAAK,CAAL;cACE,IAAI,CAAC,IAAL,EAAW,OAAO,CAAC;cAAE;cAAH,EAAc,CAAd,CAAP;cACX,OAAO,CAAC;cAAE;cAAH,EAAcT,CAAC,CAACD,IAAF,EAAd,CAAP;;YACF,KAAK,CAAL;cACEG,MAAM,GAAGoC,EAAE,CAAC5B,IAAH,EAAT;;cACA,IAAIR,MAAM,CAACC,IAAP,IAAeiC,SAAnB,EAA8B;gBAC5B,OAAO,CAAC;gBAAE;gBAAH,EAAc,CAAd,CAAP;cACD;;cACD,OAAO,CAAC;cAAE;cAAH,EAAc,CAAd,CAAP;;YACF,KAAK,CAAL;cACE,OAAO,CAAC;cAAE;cAAH,EAAc,CAAd,CAAP;;YACF,KAAK,CAAL;cACE,OAAO,CAAC;cAAE;cAAH,EAAcpC,CAAd,CAAP;;YACF,KAAK,CAAL;cACEsC,EAAE,CAAC5B,IAAH;;cACA4B,EAAE,CAAC7B,KAAH,GAAW,CAAX;;YACF,KAAK,CAAL;cACE,OAAO,CAAC;cAAE;cAAH,CAAP;UArBJ;QAuBD,CAxBiB,CAAlB;MAyBD,CA3Be,CAAhB;IA4BD;;IAED4B,OAAO;IACP,OAAO,YAAY;MACjBD,SAAS,GAAG,IAAZ;IACD,CAFD;EAGD,CAtCQ,EAsCNJ,IAtCM,CAAT;AAuCD;;AACD,eAAeF,cAAf"}, "metadata": {}, "sourceType": "module"}