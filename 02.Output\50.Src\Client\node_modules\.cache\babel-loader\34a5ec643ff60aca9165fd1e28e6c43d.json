{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isObject: function isObject(obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  },\n  extend: function extend(obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n\n    var source, prop;\n\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n\n    return obj;\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "isObject", "obj", "type", "extend", "source", "prop", "i", "length", "arguments", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/object.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  isObject: function(obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  }\n\n, extend: function(obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiB;EACfC,QAAQ,EAAE,kBAASC,GAAT,EAAc;IACtB,IAAIC,IAAI,GAAG,OAAOD,GAAlB;IACA,OAAOC,IAAI,KAAK,UAAT,IAAuBA,IAAI,KAAK,QAAT,IAAqB,CAAC,CAACD,GAArD;EACD,CAJc;EAMfE,MAAM,EAAE,gBAASF,GAAT,EAAc;IACpB,IAAI,CAAC,KAAKD,QAAL,CAAcC,GAAd,CAAL,EAAyB;MACvB,OAAOA,GAAP;IACD;;IACD,IAAIG,MAAJ,EAAYC,IAAZ;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,MAAM,GAAGC,SAAS,CAACD,MAAnC,EAA2CD,CAAC,GAAGC,MAA/C,EAAuDD,CAAC,EAAxD,EAA4D;MAC1DF,MAAM,GAAGI,SAAS,CAACF,CAAD,CAAlB;;MACA,KAAKD,IAAL,IAAaD,MAAb,EAAqB;QACnB,IAAIK,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,MAArC,EAA6CC,IAA7C,CAAJ,EAAwD;UACtDJ,GAAG,CAACI,IAAD,CAAH,GAAYD,MAAM,CAACC,IAAD,CAAlB;QACD;MACF;IACF;;IACD,OAAOJ,GAAP;EACD;AApBc,CAAjB"}, "metadata": {}, "sourceType": "script"}