{"ast": null, "code": "'use strict';\n\nvar transportList = require('./transport-list');\n\nmodule.exports = require('./main')(transportList); // TODO can't get rid of this until all servers do\n\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}", "map": {"version": 3, "names": ["transportList", "require", "module", "exports", "global", "setTimeout", "_sockjs_onload"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/entry.js"], "sourcesContent": ["'use strict';\n\nvar transportList = require('./transport-list');\n\nmodule.exports = require('./main')(transportList);\n\n// TODO can't get rid of this until all servers do\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAD,CAA3B;;AAEAC,MAAM,CAACC,OAAP,GAAiBF,OAAO,CAAC,QAAD,CAAP,CAAkBD,aAAlB,CAAjB,C,CAEA;;AACA,IAAI,oBAAoBI,MAAxB,EAAgC;EAC9BC,UAAU,CAACD,MAAM,CAACE,cAAR,EAAwB,CAAxB,CAAV;AACD"}, "metadata": {}, "sourceType": "script"}