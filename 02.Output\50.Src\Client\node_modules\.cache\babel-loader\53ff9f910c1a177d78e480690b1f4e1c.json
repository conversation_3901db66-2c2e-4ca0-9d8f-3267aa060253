{"ast": null, "code": "import { useLayoutEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useLayoutEffect);", "map": {"version": 3, "names": ["useLayoutEffect", "createUpdateEffect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useUpdateLayoutEffect/index.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useLayoutEffect);"], "mappings": "AAAA,SAASA,eAAT,QAAgC,OAAhC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,eAAeA,kBAAkB,CAACD,eAAD,CAAjC"}, "metadata": {}, "sourceType": "module"}