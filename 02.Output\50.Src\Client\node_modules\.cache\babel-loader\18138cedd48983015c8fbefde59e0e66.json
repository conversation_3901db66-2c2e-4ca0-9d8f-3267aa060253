{"ast": null, "code": "var root = require('./_root');\n/** Built-in value references. */\n\n\nvar Symbol = root.Symbol;\nmodule.exports = Symbol;", "map": {"version": 3, "names": ["root", "require", "Symbol", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_Symbol.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAD,CAAlB;AAEA;;;AACA,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAlB;AAEAC,MAAM,CAACC,OAAP,GAAiBF,MAAjB"}, "metadata": {}, "sourceType": "script"}