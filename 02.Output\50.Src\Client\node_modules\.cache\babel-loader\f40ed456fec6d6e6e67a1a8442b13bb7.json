{"ast": null, "code": "'use strict'; // Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter,\n    version = require('../version'),\n    urlUtils = require('../utils/url'),\n    iframeUtils = require('../utils/iframe'),\n    eventUtils = require('../utils/event'),\n    random = require('../utils/random');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\n\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function (r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\n\ninherits(IframeTransport, EventEmitter);\n\nIframeTransport.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {// intentionally empty\n    }\n\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\n\nIframeTransport.prototype._message = function (e) {\n  debug('message', e.data);\n\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n\n  var iframeMessage;\n\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n\n  switch (iframeMessage.type) {\n    case 's':\n      this.iframeObj.loaded(); // window global dependency\n\n      this.postMessage('s', JSON.stringify([version, this.transport, this.transUrl, this.baseUrl]));\n      break;\n\n    case 't':\n      this.emit('message', iframeMessage.data);\n      break;\n\n    case 'c':\n      var cdata;\n\n      try {\n        cdata = JSON.parse(iframeMessage.data);\n      } catch (ignored) {\n        debug('bad json', iframeMessage.data);\n        return;\n      }\n\n      this.emit('close', cdata[0], cdata[1]);\n      this.close();\n      break;\n  }\n};\n\nIframeTransport.prototype.postMessage = function (type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId,\n    type: type,\n    data: data || ''\n  }), this.origin);\n};\n\nIframeTransport.prototype.send = function (message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\n\nIframeTransport.enabled = function () {\n  return iframeUtils.iframeEnabled;\n};\n\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\nmodule.exports = IframeTransport;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "version", "urlUtils", "iframe<PERSON><PERSON>s", "eventUtils", "random", "debug", "process", "env", "NODE_ENV", "IframeTransport", "transport", "transUrl", "baseUrl", "enabled", "Error", "call", "self", "origin", "<PERSON><PERSON><PERSON><PERSON>", "windowId", "string", "iframeUrl", "addPath", "iframeObj", "createIframe", "r", "emit", "close", "onmessageCallback", "_message", "bind", "attachEvent", "prototype", "removeAllListeners", "detachEvent", "postMessage", "x", "cleanup", "e", "data", "isOriginEqual", "iframeMessage", "JSON", "parse", "ignored", "type", "loaded", "stringify", "cdata", "post", "send", "message", "iframeEnabled", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/iframe.js"], "sourcesContent": ["'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , version = require('../version')\n  , urlUtils = require('../utils/url')\n  , iframeUtils = require('../utils/iframe')\n  , eventUtils = require('../utils/event')\n  , random = require('../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\n\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function(r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\n\ninherits(IframeTransport, EventEmitter);\n\nIframeTransport.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\n\nIframeTransport.prototype._message = function(e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n\n  switch (iframeMessage.type) {\n  case 's':\n    this.iframeObj.loaded();\n    // window global dependency\n    this.postMessage('s', JSON.stringify([\n      version\n    , this.transport\n    , this.transUrl\n    , this.baseUrl\n    ]));\n    break;\n  case 't':\n    this.emit('message', iframeMessage.data);\n    break;\n  case 'c':\n    var cdata;\n    try {\n      cdata = JSON.parse(iframeMessage.data);\n    } catch (ignored) {\n      debug('bad json', iframeMessage.data);\n      return;\n    }\n    this.emit('close', cdata[0], cdata[1]);\n    this.close();\n    break;\n  }\n};\n\nIframeTransport.prototype.postMessage = function(type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId\n  , type: type\n  , data: data || ''\n  }), this.origin);\n};\n\nIframeTransport.prototype.send = function(message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\n\nIframeTransport.enabled = function() {\n  return iframeUtils.iframeEnabled;\n};\n\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\n\nmodule.exports = IframeTransport;\n"], "mappings": "AAAA,a,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;AAAA,IAEIC,OAAO,GAAGF,OAAO,CAAC,YAAD,CAFrB;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,cAAD,CAHtB;AAAA,IAIII,WAAW,GAAGJ,OAAO,CAAC,iBAAD,CAJzB;AAAA,IAKIK,UAAU,GAAGL,OAAO,CAAC,gBAAD,CALxB;AAAA,IAMIM,MAAM,GAAGN,OAAO,CAAC,iBAAD,CANpB;;AASA,IAAIO,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGP,OAAO,CAAC,OAAD,CAAP,CAAiB,gCAAjB,CAAR;AACD;;AAED,SAASW,eAAT,CAAyBC,SAAzB,EAAoCC,QAApC,EAA8CC,OAA9C,EAAuD;EACrD,IAAI,CAACH,eAAe,CAACI,OAAhB,EAAL,EAAgC;IAC9B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDf,YAAY,CAACgB,IAAb,CAAkB,IAAlB;EAEA,IAAIC,IAAI,GAAG,IAAX;EACA,KAAKC,MAAL,GAAchB,QAAQ,CAACiB,SAAT,CAAmBN,OAAnB,CAAd;EACA,KAAKA,OAAL,GAAeA,OAAf;EACA,KAAKD,QAAL,GAAgBA,QAAhB;EACA,KAAKD,SAAL,GAAiBA,SAAjB;EACA,KAAKS,QAAL,GAAgBf,MAAM,CAACgB,MAAP,CAAc,CAAd,CAAhB;EAEA,IAAIC,SAAS,GAAGpB,QAAQ,CAACqB,OAAT,CAAiBV,OAAjB,EAA0B,cAA1B,IAA4C,GAA5C,GAAkD,KAAKO,QAAvE;EACAd,KAAK,CAACK,SAAD,EAAYC,QAAZ,EAAsBU,SAAtB,CAAL;EAEA,KAAKE,SAAL,GAAiBrB,WAAW,CAACsB,YAAZ,CAAyBH,SAAzB,EAAoC,UAASI,CAAT,EAAY;IAC/DpB,KAAK,CAAC,cAAD,CAAL;IACAW,IAAI,CAACU,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyB,+BAA+BD,CAA/B,GAAmC,GAA5D;IACAT,IAAI,CAACW,KAAL;EACD,CAJgB,CAAjB;EAMA,KAAKC,iBAAL,GAAyB,KAAKC,QAAL,CAAcC,IAAd,CAAmB,IAAnB,CAAzB;EACA3B,UAAU,CAAC4B,WAAX,CAAuB,SAAvB,EAAkC,KAAKH,iBAAvC;AACD;;AAED/B,QAAQ,CAACY,eAAD,EAAkBV,YAAlB,CAAR;;AAEAU,eAAe,CAACuB,SAAhB,CAA0BL,KAA1B,GAAkC,YAAW;EAC3CtB,KAAK,CAAC,OAAD,CAAL;EACA,KAAK4B,kBAAL;;EACA,IAAI,KAAKV,SAAT,EAAoB;IAClBpB,UAAU,CAAC+B,WAAX,CAAuB,SAAvB,EAAkC,KAAKN,iBAAvC;;IACA,IAAI;MACF;MACA;MACA,KAAKO,WAAL,CAAiB,GAAjB;IACD,CAJD,CAIE,OAAOC,CAAP,EAAU,CACV;IACD;;IACD,KAAKb,SAAL,CAAec,OAAf;IACA,KAAKd,SAAL,GAAiB,IAAjB;IACA,KAAKK,iBAAL,GAAyB,KAAKL,SAAL,GAAiB,IAA1C;EACD;AACF,CAhBD;;AAkBAd,eAAe,CAACuB,SAAhB,CAA0BH,QAA1B,GAAqC,UAASS,CAAT,EAAY;EAC/CjC,KAAK,CAAC,SAAD,EAAYiC,CAAC,CAACC,IAAd,CAAL;;EACA,IAAI,CAACtC,QAAQ,CAACuC,aAAT,CAAuBF,CAAC,CAACrB,MAAzB,EAAiC,KAAKA,MAAtC,CAAL,EAAoD;IAClDZ,KAAK,CAAC,iBAAD,EAAoBiC,CAAC,CAACrB,MAAtB,EAA8B,KAAKA,MAAnC,CAAL;IACA;EACD;;EAED,IAAIwB,aAAJ;;EACA,IAAI;IACFA,aAAa,GAAGC,IAAI,CAACC,KAAL,CAAWL,CAAC,CAACC,IAAb,CAAhB;EACD,CAFD,CAEE,OAAOK,OAAP,EAAgB;IAChBvC,KAAK,CAAC,UAAD,EAAaiC,CAAC,CAACC,IAAf,CAAL;IACA;EACD;;EAED,IAAIE,aAAa,CAACtB,QAAd,KAA2B,KAAKA,QAApC,EAA8C;IAC5Cd,KAAK,CAAC,sBAAD,EAAyBoC,aAAa,CAACtB,QAAvC,EAAiD,KAAKA,QAAtD,CAAL;IACA;EACD;;EAED,QAAQsB,aAAa,CAACI,IAAtB;IACA,KAAK,GAAL;MACE,KAAKtB,SAAL,CAAeuB,MAAf,GADF,CAEE;;MACA,KAAKX,WAAL,CAAiB,GAAjB,EAAsBO,IAAI,CAACK,SAAL,CAAe,CACnC/C,OADmC,EAEnC,KAAKU,SAF8B,EAGnC,KAAKC,QAH8B,EAInC,KAAKC,OAJ8B,CAAf,CAAtB;MAMA;;IACF,KAAK,GAAL;MACE,KAAKc,IAAL,CAAU,SAAV,EAAqBe,aAAa,CAACF,IAAnC;MACA;;IACF,KAAK,GAAL;MACE,IAAIS,KAAJ;;MACA,IAAI;QACFA,KAAK,GAAGN,IAAI,CAACC,KAAL,CAAWF,aAAa,CAACF,IAAzB,CAAR;MACD,CAFD,CAEE,OAAOK,OAAP,EAAgB;QAChBvC,KAAK,CAAC,UAAD,EAAaoC,aAAa,CAACF,IAA3B,CAAL;QACA;MACD;;MACD,KAAKb,IAAL,CAAU,OAAV,EAAmBsB,KAAK,CAAC,CAAD,CAAxB,EAA6BA,KAAK,CAAC,CAAD,CAAlC;MACA,KAAKrB,KAAL;MACA;EAxBF;AA0BD,CA9CD;;AAgDAlB,eAAe,CAACuB,SAAhB,CAA0BG,WAA1B,GAAwC,UAASU,IAAT,EAAeN,IAAf,EAAqB;EAC3DlC,KAAK,CAAC,aAAD,EAAgBwC,IAAhB,EAAsBN,IAAtB,CAAL;EACA,KAAKhB,SAAL,CAAe0B,IAAf,CAAoBP,IAAI,CAACK,SAAL,CAAe;IACjC5B,QAAQ,EAAE,KAAKA,QADkB;IAEjC0B,IAAI,EAAEA,IAF2B;IAGjCN,IAAI,EAAEA,IAAI,IAAI;EAHmB,CAAf,CAApB,EAII,KAAKtB,MAJT;AAKD,CAPD;;AASAR,eAAe,CAACuB,SAAhB,CAA0BkB,IAA1B,GAAiC,UAASC,OAAT,EAAkB;EACjD9C,KAAK,CAAC,MAAD,EAAS8C,OAAT,CAAL;EACA,KAAKhB,WAAL,CAAiB,GAAjB,EAAsBgB,OAAtB;AACD,CAHD;;AAKA1C,eAAe,CAACI,OAAhB,GAA0B,YAAW;EACnC,OAAOX,WAAW,CAACkD,aAAnB;AACD,CAFD;;AAIA3C,eAAe,CAAC4C,aAAhB,GAAgC,QAAhC;AACA5C,eAAe,CAAC6C,UAAhB,GAA6B,CAA7B;AAEAC,MAAM,CAACC,OAAP,GAAiB/C,eAAjB"}, "metadata": {}, "sourceType": "script"}