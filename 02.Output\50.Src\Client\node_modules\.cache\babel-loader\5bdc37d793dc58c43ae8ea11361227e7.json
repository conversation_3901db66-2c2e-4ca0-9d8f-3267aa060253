{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";/**\r\n * コンテンツのタイトル\r\n * \r\n * @module Title\r\n * @component\r\n * @param {propTypes} props\r\n * @returns タイトル\r\n */var Title=function Title(props){var style={fontSize:props.fontSize||'3.5rem'};return/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col items-center bg-white text-black\",style:style,children:/*#__PURE__*/_jsx(\"div\",{className:\"leading-none\",children:props.title})});};export default Title;", "map": {"version": 3, "names": ["React", "Title", "props", "style", "fontSize", "title"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/Title.js"], "sourcesContent": ["import React from 'react';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  fontSize: PropTypes.string,\r\n  title: PropTypes.string,\r\n};\r\n\r\n/**\r\n * コンテンツのタイトル\r\n * \r\n * @module Title\r\n * @component\r\n * @param {propTypes} props\r\n * @returns タイトル\r\n */\r\nconst Title = (props) => {\r\n  const style = {\r\n    fontSize: props.fontSize || '3.5rem',\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"flex flex-col items-center bg-white text-black\"\r\n      style={style}\r\n    >\r\n      <div className=\"leading-none\">{props.title}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nTitle.propTypes = propTypes;\r\nexport default Title;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,C,2CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,MAAK,CAAG,QAARA,MAAQ,CAACC,KAAD,CAAW,CACvB,GAAMC,MAAK,CAAG,CACZC,QAAQ,CAAEF,KAAK,CAACE,QAAN,EAAkB,QADhB,CAAd,CAIA,mBACE,YACE,SAAS,CAAC,gDADZ,CAEE,KAAK,CAAED,KAFT,uBAIE,YAAK,SAAS,CAAC,cAAf,UAA+BD,KAAK,CAACG,KAArC,EAJF,EADF,CAQD,CAbD,CAgBA,cAAeJ,MAAf"}, "metadata": {}, "sourceType": "module"}