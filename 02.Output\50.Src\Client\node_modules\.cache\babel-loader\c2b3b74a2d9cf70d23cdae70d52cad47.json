{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{Component}from'react';import Cell from'./elements/Cell.js';import{getCellFace,isValidSource,toThousands}from'../utils/Util.js';import Title from'./elements/Title.js';import CellBox from'./elements/CellBox.js';import'./Weather.css';/**\r\n * 気象状況コンテンツ<br>\r\n * propsは、「3.10気象コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Weather\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var Weather=function Weather(props){props.barTitle=props.barTitle==null?'気象状況':props.barTitle;console.log(\"props.barTitle: \".concat(props.barTitle));return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Title,{title:props.barTitle}),isValidSource(props)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"basis-[54.25%] grid grid-cols-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-[3.6rem] mt-[.7rem]\",children:/*#__PURE__*/_jsx(WindDirection,_objectSpread({},props.wind_direction))})}),/*#__PURE__*/_jsx(\"div\",{className:\"basis-[45.75%] ml-[2rem]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-[repeat(4,5.1rem)_3.1rem_repeat(3,5.5rem)_3.1rem_repeat(2,5rem)] grid-rows-8 gap-y-[2.3rem] mt-[3.8rem] mb-[3rem] text-5xl leading-[1] mr-[3rem]\",children:[/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u6700\\u5927\\u98A8\\u901F\",detail:props.wind_speed_max}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u5E73\\u5747\\u98A8\\u901F\",detail:props.wind_speed}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u6C17\\u6E29\",detail:props.temperature}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u96E8\\u91CF\",detail:props.rainfall}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u5B9F\\u52B9\\u6E7F\\u5EA6\",detail:props.effective_humidity}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u76F8\\u5BFE\\u6E7F\\u5EA6\",detail:props.relative_humidity}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u6C17\\u5727\",detail:props.atmospheric_pressure}),/*#__PURE__*/_jsx(WeatherRow,{weather:\"\\u5929\\u5019\",detail:props.weather})]})})]})]});};// 気象状況コンテンツの風向\nvar WindDirection=function WindDirection(props){var imageMap={北:'North.png',北_北東:'North_Northeast.png',北東:'Northeast.png',北東_東:'Northeast_East.png',東:'East.png',東_南東:'East_Southeast.png',南東:'Southeast.png',南東_南:'Southeast_South.png',南:'South.png',南_南西:'South_Southwest.png',南西:'Southwest.png',南西_西:'Southwest_West.png',西:'West.png',西_北西:'West_Northwest.png',北西:'Northwest.png',北西_北:'Northwest_North.png'};if(!props||!props.display_text){return null;}// 風向の表示色なし(画像のまま)。背景色は黒のまま\nvar imgPath=\"/images/\".concat(imageMap[props.display_text.trim()]);return/*#__PURE__*/_jsx(\"img\",{className:\"min-w-full\",src:process.env.PUBLIC_URL+imgPath,alt:props.display_text});};// 気象状況コンテンツの一行天気\nvar WeatherRow=function WeatherRow(props){var _props$detail;var unitMap={平均風速:'m/s',風向:'',最大風速:'m/s',気温:'&#8451;',雨量:'mm',実効湿度:'%',相対湿度:'%',気圧:'hPa',観測時刻:'',天候:''};var cell1Props={text_color:'#080808',background_color:'#fff',text:props.weather,className:'col-start-1 col-span-4 justify'};var cell2Props=getCellFace(props.detail,'col-span-3 col-start-6 place-self-end w-fit');var cell3Props={text_color:'#fff',background_color:'#000',className:'col-start-10 col-span-2'};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell2Props)),((_props$detail=props.detail)===null||_props$detail===void 0?void 0:_props$detail.display_text)&&/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},cell3Props),{},{children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:unitMap[props.weather]}})}))]});};export default Weather;", "map": {"version": 3, "names": ["React", "Component", "Cell", "getCellFace", "isValidSource", "toThousands", "Title", "CellBox", "Weather", "props", "bar<PERSON>itle", "console", "log", "wind_direction", "wind_speed_max", "wind_speed", "temperature", "rainfall", "effective_humidity", "relative_humidity", "atmospheric_pressure", "weather", "WindDirection", "imageMap", "北", "北_北東", "北東", "北東_東", "東", "東_南東", "南<PERSON>", "南東_南", "南", "南_南西", "南<PERSON>", "南西_西", "西", "西_北西", "北西", "北西_北", "display_text", "imgPath", "trim", "process", "env", "PUBLIC_URL", "WeatherRow", "unitMap", "平均風速", "風向", "最大風速", "気温", "雨量", "実効湿度", "相対湿度", "気圧", "観測時刻", "天候", "cell1Props", "text_color", "background_color", "text", "className", "cell2Props", "detail", "cell3Props", "__html"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Weather.js"], "sourcesContent": ["import React, { Component } from 'react';\r\nimport Cell from './elements/Cell.js';\r\nimport { getCellFace, isValidSource, toThousands } from '../utils/Util.js';\r\nimport Title from './elements/Title.js';\r\nimport CellBox from './elements/CellBox.js';\r\nimport './Weather.css';\r\n\r\n/**\r\n * 気象状況コンテンツ<br>\r\n * propsは、「3.10気象コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Weather\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Weather = (props) => {\r\n  props.barTitle = props.barTitle == null ? '気象状況' : props.barTitle;\r\n  console.log(`props.barTitle: ${props.barTitle}`);\r\n  return (\r\n    <>\r\n      <Title title={props.barTitle}/>\r\n      {isValidSource(props) && (\r\n        <div className=\"flex flex-row\">\r\n          <div className=\"basis-[54.25%] grid grid-cols-1\">\r\n\t\t\t<div className=\"p-[3.6rem] mt-[.7rem]\">\r\n\t\t\t  <WindDirection {...props.wind_direction} />\r\n\t\t\t</div>\r\n\t\t  </div>\r\n          <div className=\"basis-[45.75%] ml-[2rem]\">\r\n            <div className=\"grid grid-cols-[repeat(4,5.1rem)_3.1rem_repeat(3,5.5rem)_3.1rem_repeat(2,5rem)] grid-rows-8 gap-y-[2.3rem] mt-[3.8rem] mb-[3rem] text-5xl leading-[1] mr-[3rem]\">\r\n              <WeatherRow weather=\"最大風速\" detail={props.wind_speed_max} />\r\n              <WeatherRow weather=\"平均風速\" detail={props.wind_speed} />\r\n              <WeatherRow weather=\"気温\" detail={props.temperature} />\r\n              <WeatherRow weather=\"雨量\" detail={props.rainfall} />\r\n              <WeatherRow\r\n                weather=\"実効湿度\"\r\n                detail={props.effective_humidity}\r\n              />\r\n              <WeatherRow weather=\"相対湿度\" detail={props.relative_humidity} />\r\n              <WeatherRow weather=\"気圧\" detail={props.atmospheric_pressure} />\r\n              {/* <WeatherRow weather='観測時刻' detail={props.observation_time} /> */}\r\n              <WeatherRow weather=\"天候\" detail={props.weather} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\n// 気象状況コンテンツの風向\r\nconst WindDirection = (props) => {\r\n  const imageMap = {\r\n    北: 'North.png',\r\n    北_北東: 'North_Northeast.png',\r\n    北東: 'Northeast.png',\r\n    北東_東: 'Northeast_East.png',\r\n    東: 'East.png',\r\n    東_南東: 'East_Southeast.png',\r\n    南東: 'Southeast.png',\r\n    南東_南: 'Southeast_South.png',\r\n    南: 'South.png',\r\n    南_南西: 'South_Southwest.png',\r\n    南西: 'Southwest.png',\r\n    南西_西: 'Southwest_West.png',\r\n    西: 'West.png',\r\n    西_北西: 'West_Northwest.png',\r\n    北西: 'Northwest.png',\r\n    北西_北: 'Northwest_North.png',\r\n  };\r\n\r\n  if (!props || !props.display_text) {\r\n    return null;\r\n  }\r\n\r\n  // 風向の表示色なし(画像のまま)。背景色は黒のまま\r\n  let imgPath = `/images/${imageMap[props.display_text.trim()]}`;\r\n\r\n  return (\r\n    <img className=\"min-w-full\"\r\n      src={process.env.PUBLIC_URL + imgPath}\r\n      alt={props.display_text}\r\n    />\r\n  );\r\n};\r\n\r\n// 気象状況コンテンツの一行天気\r\nconst WeatherRow = (props) => {\r\n  const unitMap = {\r\n    平均風速: 'm/s',\r\n    風向: '',\r\n    最大風速: 'm/s',\r\n    気温: '&#8451;',\r\n    雨量: 'mm',\r\n    実効湿度: '%',\r\n    相対湿度: '%',\r\n    気圧: 'hPa',\r\n    観測時刻: '',\r\n    天候: '',\r\n  };\r\n  const cell1Props = {\r\n    text_color: '#080808',\r\n    background_color: '#fff',\r\n    text: props.weather,\r\n    className: 'col-start-1 col-span-4 justify',\r\n  };\r\n  const cell2Props = getCellFace(\r\n    props.detail,\r\n    'col-span-3 col-start-6 place-self-end w-fit'\r\n  );\r\n\r\n  const cell3Props = {\r\n    text_color: '#fff',\r\n    background_color: '#000',\r\n    className: 'col-start-10 col-span-2',\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Cell {...cell1Props} />\r\n      <Cell {...cell2Props} />\r\n      {props.detail?.display_text && (\r\n        <CellBox {...cell3Props}>\r\n          <span dangerouslySetInnerHTML={{ __html: unitMap[props.weather] }} />\r\n        </CellBox>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Weather;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,EAAgBC,SAAhB,KAAiC,OAAjC,CACA,MAAOC,KAAP,KAAiB,oBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CACA,MAAOC,MAAP,KAAkB,qBAAlB,CACA,MAAOC,QAAP,KAAoB,uBAApB,CACA,MAAO,eAAP,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,QAAO,CAAG,QAAVA,QAAU,CAACC,KAAD,CAAW,CACzBA,KAAK,CAACC,QAAN,CAAiBD,KAAK,CAACC,QAAN,EAAkB,IAAlB,CAAyB,MAAzB,CAAkCD,KAAK,CAACC,QAAzD,CACAC,OAAO,CAACC,GAAR,2BAA+BH,KAAK,CAACC,QAArC,GACA,mBACE,wCACE,KAAC,KAAD,EAAO,KAAK,CAAED,KAAK,CAACC,QAApB,EADF,CAEGN,aAAa,CAACK,KAAD,CAAb,eACC,aAAK,SAAS,CAAC,eAAf,wBACE,YAAK,SAAS,CAAC,iCAAf,uBACP,YAAK,SAAS,CAAC,uBAAf,uBACE,KAAC,aAAD,kBAAmBA,KAAK,CAACI,cAAzB,EADF,EADO,EADF,cAME,YAAK,SAAS,CAAC,0BAAf,uBACE,aAAK,SAAS,CAAC,iKAAf,wBACE,KAAC,UAAD,EAAY,OAAO,CAAC,0BAApB,CAA2B,MAAM,CAAEJ,KAAK,CAACK,cAAzC,EADF,cAEE,KAAC,UAAD,EAAY,OAAO,CAAC,0BAApB,CAA2B,MAAM,CAAEL,KAAK,CAACM,UAAzC,EAFF,cAGE,KAAC,UAAD,EAAY,OAAO,CAAC,cAApB,CAAyB,MAAM,CAAEN,KAAK,CAACO,WAAvC,EAHF,cAIE,KAAC,UAAD,EAAY,OAAO,CAAC,cAApB,CAAyB,MAAM,CAAEP,KAAK,CAACQ,QAAvC,EAJF,cAKE,KAAC,UAAD,EACE,OAAO,CAAC,0BADV,CAEE,MAAM,CAAER,KAAK,CAACS,kBAFhB,EALF,cASE,KAAC,UAAD,EAAY,OAAO,CAAC,0BAApB,CAA2B,MAAM,CAAET,KAAK,CAACU,iBAAzC,EATF,cAUE,KAAC,UAAD,EAAY,OAAO,CAAC,cAApB,CAAyB,MAAM,CAAEV,KAAK,CAACW,oBAAvC,EAVF,cAYE,KAAC,UAAD,EAAY,OAAO,CAAC,cAApB,CAAyB,MAAM,CAAEX,KAAK,CAACY,OAAvC,EAZF,GADF,EANF,GAHJ,GADF,CA8BD,CAjCD,CAmCA;AACA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACb,KAAD,CAAW,CAC/B,GAAMc,SAAQ,CAAG,CACfC,CAAC,CAAE,WADY,CAEfC,IAAI,CAAE,qBAFS,CAGfC,EAAE,CAAE,eAHW,CAIfC,IAAI,CAAE,oBAJS,CAKfC,CAAC,CAAE,UALY,CAMfC,IAAI,CAAE,oBANS,CAOfC,EAAE,CAAE,eAPW,CAQfC,IAAI,CAAE,qBARS,CASfC,CAAC,CAAE,WATY,CAUfC,IAAI,CAAE,qBAVS,CAWfC,EAAE,CAAE,eAXW,CAYfC,IAAI,CAAE,oBAZS,CAafC,CAAC,CAAE,UAbY,CAcfC,IAAI,CAAE,oBAdS,CAefC,EAAE,CAAE,eAfW,CAgBfC,IAAI,CAAE,qBAhBS,CAAjB,CAmBA,GAAI,CAAC9B,KAAD,EAAU,CAACA,KAAK,CAAC+B,YAArB,CAAmC,CACjC,MAAO,KAAP,CACD,CAED;AACA,GAAIC,QAAO,mBAAclB,QAAQ,CAACd,KAAK,CAAC+B,YAAN,CAAmBE,IAAnB,EAAD,CAAtB,CAAX,CAEA,mBACE,YAAK,SAAS,CAAC,YAAf,CACE,GAAG,CAAEC,OAAO,CAACC,GAAR,CAAYC,UAAZ,CAAyBJ,OADhC,CAEE,GAAG,CAAEhC,KAAK,CAAC+B,YAFb,EADF,CAMD,CAjCD,CAmCA;AACA,GAAMM,WAAU,CAAG,QAAbA,WAAa,CAACrC,KAAD,CAAW,mBAC5B,GAAMsC,QAAO,CAAG,CACdC,IAAI,CAAE,KADQ,CAEdC,EAAE,CAAE,EAFU,CAGdC,IAAI,CAAE,KAHQ,CAIdC,EAAE,CAAE,SAJU,CAKdC,EAAE,CAAE,IALU,CAMdC,IAAI,CAAE,GANQ,CAOdC,IAAI,CAAE,GAPQ,CAQdC,EAAE,CAAE,KARU,CASdC,IAAI,CAAE,EATQ,CAUdC,EAAE,CAAE,EAVU,CAAhB,CAYA,GAAMC,WAAU,CAAG,CACjBC,UAAU,CAAE,SADK,CAEjBC,gBAAgB,CAAE,MAFD,CAGjBC,IAAI,CAAEpD,KAAK,CAACY,OAHK,CAIjByC,SAAS,CAAE,gCAJM,CAAnB,CAMA,GAAMC,WAAU,CAAG5D,WAAW,CAC5BM,KAAK,CAACuD,MADsB,CAE5B,6CAF4B,CAA9B,CAKA,GAAMC,WAAU,CAAG,CACjBN,UAAU,CAAE,MADK,CAEjBC,gBAAgB,CAAE,MAFD,CAGjBE,SAAS,CAAE,yBAHM,CAAnB,CAMA,mBACE,wCACE,KAAC,IAAD,kBAAUJ,UAAV,EADF,cAEE,KAAC,IAAD,kBAAUK,UAAV,EAFF,CAGG,gBAAAtD,KAAK,CAACuD,MAAN,sDAAcxB,YAAd,gBACC,KAAC,OAAD,gCAAayB,UAAb,4BACE,aAAM,uBAAuB,CAAE,CAAEC,MAAM,CAAEnB,OAAO,CAACtC,KAAK,CAACY,OAAP,CAAjB,CAA/B,EADF,GAJJ,GADF,CAWD,CAzCD,CA2CA,cAAeb,QAAf"}, "metadata": {}, "sourceType": "module"}