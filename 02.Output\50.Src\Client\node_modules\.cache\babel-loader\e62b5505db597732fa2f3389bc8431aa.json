{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport envFormData from '../env/classes/FormData.js';\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */\n\nfunction isVisitable(thing) {\n  return utils.isPlainObject(thing) || utils.isArray(thing);\n}\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */\n\n\nfunction removeBrackets(key) {\n  return utils.endsWith(key, '[]') ? key.slice(0, -2) : key;\n}\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */\n\n\nfunction renderKey(path, key, dots) {\n  if (!path) return key;\n  return path.concat(key).map(function each(token, i) {\n    // eslint-disable-next-line no-param-reassign\n    token = removeBrackets(token);\n    return !dots && i ? '[' + token + ']' : token;\n  }).join(dots ? '.' : '');\n}\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */\n\n\nfunction isFlatArray(arr) {\n  return utils.isArray(arr) && !arr.some(isVisitable);\n}\n\nvar predicates = utils.toFlatObject(utils, {}, null, function filter(prop) {\n  return /^is[A-Z]/.test(prop);\n});\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */\n\nfunction isSpecCompliant(thing) {\n  return thing && utils.isFunction(thing.append) && thing[Symbol.toStringTag] === 'FormData' && thing[Symbol.iterator];\n}\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/\n\n/**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */\n\n\nfunction toFormData(obj, formData, options) {\n  if (!utils.isObject(obj)) {\n    throw new TypeError('target must be an object');\n  } // eslint-disable-next-line no-param-reassign\n\n\n  formData = formData || new (envFormData || FormData)(); // eslint-disable-next-line no-param-reassign\n\n  options = utils.toFlatObject(options, {\n    metaTokens: true,\n    dots: false,\n    indexes: false\n  }, false, function defined(option, source) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    return !utils.isUndefined(source[option]);\n  });\n  var metaTokens = options.metaTokens; // eslint-disable-next-line no-use-before-define\n\n  var visitor = options.visitor || defaultVisitor;\n  var dots = options.dots;\n  var indexes = options.indexes;\n\n  var _Blob = options.Blob || typeof Blob !== 'undefined' && Blob;\n\n  var useBlob = _Blob && isSpecCompliant(formData);\n\n  if (!utils.isFunction(visitor)) {\n    throw new TypeError('visitor must be a function');\n  }\n\n  function convertValue(value) {\n    if (value === null) return '';\n\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n\n    if (!useBlob && utils.isBlob(value)) {\n      throw new AxiosError('Blob is not supported. Use a Buffer instead.');\n    }\n\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return useBlob && typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n\n    return value;\n  }\n  /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */\n\n\n  function defaultVisitor(value, key, path) {\n    var arr = value;\n\n    if (value && !path && typeof value === 'object') {\n      if (utils.endsWith(key, '{}')) {\n        // eslint-disable-next-line no-param-reassign\n        key = metaTokens ? key : key.slice(0, -2); // eslint-disable-next-line no-param-reassign\n\n        value = JSON.stringify(value);\n      } else if (utils.isArray(value) && isFlatArray(value) || utils.isFileList(value) || utils.endsWith(key, '[]') && (arr = utils.toArray(value))) {\n        // eslint-disable-next-line no-param-reassign\n        key = removeBrackets(key);\n        arr.forEach(function each(el, index) {\n          !(utils.isUndefined(el) || el === null) && formData.append( // eslint-disable-next-line no-nested-ternary\n          indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + '[]', convertValue(el));\n        });\n        return false;\n      }\n    }\n\n    if (isVisitable(value)) {\n      return true;\n    }\n\n    formData.append(renderKey(path, key, dots), convertValue(value));\n    return false;\n  }\n\n  var stack = [];\n  var exposedHelpers = Object.assign(predicates, {\n    defaultVisitor: defaultVisitor,\n    convertValue: convertValue,\n    isVisitable: isVisitable\n  });\n\n  function build(value, path) {\n    if (utils.isUndefined(value)) return;\n\n    if (stack.indexOf(value) !== -1) {\n      throw Error('Circular reference detected in ' + path.join('.'));\n    }\n\n    stack.push(value);\n    utils.forEach(value, function each(el, key) {\n      var result = !(utils.isUndefined(el) || el === null) && visitor.call(formData, el, utils.isString(key) ? key.trim() : key, path, exposedHelpers);\n\n      if (result === true) {\n        build(el, path ? path.concat(key) : [key]);\n      }\n    });\n    stack.pop();\n  }\n\n  if (!utils.isObject(obj)) {\n    throw new TypeError('data must be an object');\n  }\n\n  build(obj);\n  return formData;\n}\n\nexport default toFormData;", "map": {"version": 3, "names": ["utils", "AxiosError", "envFormData", "isVisitable", "thing", "isPlainObject", "isArray", "removeBrackets", "key", "endsWith", "slice", "<PERSON><PERSON><PERSON>", "path", "dots", "concat", "map", "each", "token", "i", "join", "isFlatArray", "arr", "some", "predicates", "toFlatObject", "filter", "prop", "test", "isSpecCompliant", "isFunction", "append", "Symbol", "toStringTag", "iterator", "toFormData", "obj", "formData", "options", "isObject", "TypeError", "FormData", "metaTokens", "indexes", "defined", "option", "source", "isUndefined", "visitor", "defaultVisitor", "_Blob", "Blob", "useBlob", "convertValue", "value", "isDate", "toISOString", "isBlob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTypedArray", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "isFileList", "toArray", "for<PERSON>ach", "el", "index", "stack", "exposedHelpers", "Object", "assign", "build", "indexOf", "Error", "push", "result", "call", "isString", "trim", "pop"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/toFormData.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport envFormData from '../env/classes/FormData.js';\n\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */\nfunction isVisitable(thing) {\n  return utils.isPlainObject(thing) || utils.isArray(thing);\n}\n\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */\nfunction removeBrackets(key) {\n  return utils.endsWith(key, '[]') ? key.slice(0, -2) : key;\n}\n\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */\nfunction renderKey(path, key, dots) {\n  if (!path) return key;\n  return path.concat(key).map(function each(token, i) {\n    // eslint-disable-next-line no-param-reassign\n    token = removeBrackets(token);\n    return !dots && i ? '[' + token + ']' : token;\n  }).join(dots ? '.' : '');\n}\n\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */\nfunction isFlatArray(arr) {\n  return utils.isArray(arr) && !arr.some(isVisitable);\n}\n\nconst predicates = utils.toFlatObject(utils, {}, null, function filter(prop) {\n  return /^is[A-Z]/.test(prop);\n});\n\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */\nfunction isSpecCompliant(thing) {\n  return thing && utils.isFunction(thing.append) && thing[Symbol.toStringTag] === 'FormData' && thing[Symbol.iterator];\n}\n\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/\n\n/**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */\nfunction toFormData(obj, formData, options) {\n  if (!utils.isObject(obj)) {\n    throw new TypeError('target must be an object');\n  }\n\n  // eslint-disable-next-line no-param-reassign\n  formData = formData || new (envFormData || FormData)();\n\n  // eslint-disable-next-line no-param-reassign\n  options = utils.toFlatObject(options, {\n    metaTokens: true,\n    dots: false,\n    indexes: false\n  }, false, function defined(option, source) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    return !utils.isUndefined(source[option]);\n  });\n\n  const metaTokens = options.metaTokens;\n  // eslint-disable-next-line no-use-before-define\n  const visitor = options.visitor || defaultVisitor;\n  const dots = options.dots;\n  const indexes = options.indexes;\n  const _Blob = options.Blob || typeof Blob !== 'undefined' && Blob;\n  const useBlob = _Blob && isSpecCompliant(formData);\n\n  if (!utils.isFunction(visitor)) {\n    throw new TypeError('visitor must be a function');\n  }\n\n  function convertValue(value) {\n    if (value === null) return '';\n\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n\n    if (!useBlob && utils.isBlob(value)) {\n      throw new AxiosError('Blob is not supported. Use a Buffer instead.');\n    }\n\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return useBlob && typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n\n    return value;\n  }\n\n  /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */\n  function defaultVisitor(value, key, path) {\n    let arr = value;\n\n    if (value && !path && typeof value === 'object') {\n      if (utils.endsWith(key, '{}')) {\n        // eslint-disable-next-line no-param-reassign\n        key = metaTokens ? key : key.slice(0, -2);\n        // eslint-disable-next-line no-param-reassign\n        value = JSON.stringify(value);\n      } else if (\n        (utils.isArray(value) && isFlatArray(value)) ||\n        (utils.isFileList(value) || utils.endsWith(key, '[]') && (arr = utils.toArray(value))\n        )) {\n        // eslint-disable-next-line no-param-reassign\n        key = removeBrackets(key);\n\n        arr.forEach(function each(el, index) {\n          !(utils.isUndefined(el) || el === null) && formData.append(\n            // eslint-disable-next-line no-nested-ternary\n            indexes === true ? renderKey([key], index, dots) : (indexes === null ? key : key + '[]'),\n            convertValue(el)\n          );\n        });\n        return false;\n      }\n    }\n\n    if (isVisitable(value)) {\n      return true;\n    }\n\n    formData.append(renderKey(path, key, dots), convertValue(value));\n\n    return false;\n  }\n\n  const stack = [];\n\n  const exposedHelpers = Object.assign(predicates, {\n    defaultVisitor,\n    convertValue,\n    isVisitable\n  });\n\n  function build(value, path) {\n    if (utils.isUndefined(value)) return;\n\n    if (stack.indexOf(value) !== -1) {\n      throw Error('Circular reference detected in ' + path.join('.'));\n    }\n\n    stack.push(value);\n\n    utils.forEach(value, function each(el, key) {\n      const result = !(utils.isUndefined(el) || el === null) && visitor.call(\n        formData, el, utils.isString(key) ? key.trim() : key, path, exposedHelpers\n      );\n\n      if (result === true) {\n        build(el, path ? path.concat(key) : [key]);\n      }\n    });\n\n    stack.pop();\n  }\n\n  if (!utils.isObject(obj)) {\n    throw new TypeError('data must be an object');\n  }\n\n  build(obj);\n\n  return formData;\n}\n\nexport default toFormData;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,UAAP,MAAuB,uBAAvB;AACA,OAAOC,WAAP,MAAwB,4BAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC1B,OAAOJ,KAAK,CAACK,aAAN,CAAoBD,KAApB,KAA8BJ,KAAK,CAACM,OAAN,CAAcF,KAAd,CAArC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,cAAT,CAAwBC,GAAxB,EAA6B;EAC3B,OAAOR,KAAK,CAACS,QAAN,CAAeD,GAAf,EAAoB,IAApB,IAA4BA,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,CAA5B,GAA+CF,GAAtD;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,SAAT,CAAmBC,IAAnB,EAAyBJ,GAAzB,EAA8BK,IAA9B,EAAoC;EAClC,IAAI,CAACD,IAAL,EAAW,OAAOJ,GAAP;EACX,OAAOI,IAAI,CAACE,MAAL,CAAYN,GAAZ,EAAiBO,GAAjB,CAAqB,SAASC,IAAT,CAAcC,KAAd,EAAqBC,CAArB,EAAwB;IAClD;IACAD,KAAK,GAAGV,cAAc,CAACU,KAAD,CAAtB;IACA,OAAO,CAACJ,IAAD,IAASK,CAAT,GAAa,MAAMD,KAAN,GAAc,GAA3B,GAAiCA,KAAxC;EACD,CAJM,EAIJE,IAJI,CAICN,IAAI,GAAG,GAAH,GAAS,EAJd,CAAP;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,WAAT,CAAqBC,GAArB,EAA0B;EACxB,OAAOrB,KAAK,CAACM,OAAN,CAAce,GAAd,KAAsB,CAACA,GAAG,CAACC,IAAJ,CAASnB,WAAT,CAA9B;AACD;;AAED,IAAMoB,UAAU,GAAGvB,KAAK,CAACwB,YAAN,CAAmBxB,KAAnB,EAA0B,EAA1B,EAA8B,IAA9B,EAAoC,SAASyB,MAAT,CAAgBC,IAAhB,EAAsB;EAC3E,OAAO,WAAWC,IAAX,CAAgBD,IAAhB,CAAP;AACD,CAFkB,CAAnB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASE,eAAT,CAAyBxB,KAAzB,EAAgC;EAC9B,OAAOA,KAAK,IAAIJ,KAAK,CAAC6B,UAAN,CAAiBzB,KAAK,CAAC0B,MAAvB,CAAT,IAA2C1B,KAAK,CAAC2B,MAAM,CAACC,WAAR,CAAL,KAA8B,UAAzE,IAAuF5B,KAAK,CAAC2B,MAAM,CAACE,QAAR,CAAnG;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBC,GAApB,EAAyBC,QAAzB,EAAmCC,OAAnC,EAA4C;EAC1C,IAAI,CAACrC,KAAK,CAACsC,QAAN,CAAeH,GAAf,CAAL,EAA0B;IACxB,MAAM,IAAII,SAAJ,CAAc,0BAAd,CAAN;EACD,CAHyC,CAK1C;;;EACAH,QAAQ,GAAGA,QAAQ,IAAI,KAAKlC,WAAW,IAAIsC,QAApB,GAAvB,CAN0C,CAQ1C;;EACAH,OAAO,GAAGrC,KAAK,CAACwB,YAAN,CAAmBa,OAAnB,EAA4B;IACpCI,UAAU,EAAE,IADwB;IAEpC5B,IAAI,EAAE,KAF8B;IAGpC6B,OAAO,EAAE;EAH2B,CAA5B,EAIP,KAJO,EAIA,SAASC,OAAT,CAAiBC,MAAjB,EAAyBC,MAAzB,EAAiC;IACzC;IACA,OAAO,CAAC7C,KAAK,CAAC8C,WAAN,CAAkBD,MAAM,CAACD,MAAD,CAAxB,CAAR;EACD,CAPS,CAAV;EASA,IAAMH,UAAU,GAAGJ,OAAO,CAACI,UAA3B,CAlB0C,CAmB1C;;EACA,IAAMM,OAAO,GAAGV,OAAO,CAACU,OAAR,IAAmBC,cAAnC;EACA,IAAMnC,IAAI,GAAGwB,OAAO,CAACxB,IAArB;EACA,IAAM6B,OAAO,GAAGL,OAAO,CAACK,OAAxB;;EACA,IAAMO,KAAK,GAAGZ,OAAO,CAACa,IAAR,IAAgB,OAAOA,IAAP,KAAgB,WAAhB,IAA+BA,IAA7D;;EACA,IAAMC,OAAO,GAAGF,KAAK,IAAIrB,eAAe,CAACQ,QAAD,CAAxC;;EAEA,IAAI,CAACpC,KAAK,CAAC6B,UAAN,CAAiBkB,OAAjB,CAAL,EAAgC;IAC9B,MAAM,IAAIR,SAAJ,CAAc,4BAAd,CAAN;EACD;;EAED,SAASa,YAAT,CAAsBC,KAAtB,EAA6B;IAC3B,IAAIA,KAAK,KAAK,IAAd,EAAoB,OAAO,EAAP;;IAEpB,IAAIrD,KAAK,CAACsD,MAAN,CAAaD,KAAb,CAAJ,EAAyB;MACvB,OAAOA,KAAK,CAACE,WAAN,EAAP;IACD;;IAED,IAAI,CAACJ,OAAD,IAAYnD,KAAK,CAACwD,MAAN,CAAaH,KAAb,CAAhB,EAAqC;MACnC,MAAM,IAAIpD,UAAJ,CAAe,8CAAf,CAAN;IACD;;IAED,IAAID,KAAK,CAACyD,aAAN,CAAoBJ,KAApB,KAA8BrD,KAAK,CAAC0D,YAAN,CAAmBL,KAAnB,CAAlC,EAA6D;MAC3D,OAAOF,OAAO,IAAI,OAAOD,IAAP,KAAgB,UAA3B,GAAwC,IAAIA,IAAJ,CAAS,CAACG,KAAD,CAAT,CAAxC,GAA4DM,MAAM,CAACC,IAAP,CAAYP,KAAZ,CAAnE;IACD;;IAED,OAAOA,KAAP;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASL,cAAT,CAAwBK,KAAxB,EAA+B7C,GAA/B,EAAoCI,IAApC,EAA0C;IACxC,IAAIS,GAAG,GAAGgC,KAAV;;IAEA,IAAIA,KAAK,IAAI,CAACzC,IAAV,IAAkB,OAAOyC,KAAP,KAAiB,QAAvC,EAAiD;MAC/C,IAAIrD,KAAK,CAACS,QAAN,CAAeD,GAAf,EAAoB,IAApB,CAAJ,EAA+B;QAC7B;QACAA,GAAG,GAAGiC,UAAU,GAAGjC,GAAH,GAASA,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,CAAzB,CAF6B,CAG7B;;QACA2C,KAAK,GAAGQ,IAAI,CAACC,SAAL,CAAeT,KAAf,CAAR;MACD,CALD,MAKO,IACJrD,KAAK,CAACM,OAAN,CAAc+C,KAAd,KAAwBjC,WAAW,CAACiC,KAAD,CAApC,IACCrD,KAAK,CAAC+D,UAAN,CAAiBV,KAAjB,KAA2BrD,KAAK,CAACS,QAAN,CAAeD,GAAf,EAAoB,IAApB,MAA8Ba,GAAG,GAAGrB,KAAK,CAACgE,OAAN,CAAcX,KAAd,CAApC,CAFvB,EAGF;QACH;QACA7C,GAAG,GAAGD,cAAc,CAACC,GAAD,CAApB;QAEAa,GAAG,CAAC4C,OAAJ,CAAY,SAASjD,IAAT,CAAckD,EAAd,EAAkBC,KAAlB,EAAyB;UACnC,EAAEnE,KAAK,CAAC8C,WAAN,CAAkBoB,EAAlB,KAAyBA,EAAE,KAAK,IAAlC,KAA2C9B,QAAQ,CAACN,MAAT,EACzC;UACAY,OAAO,KAAK,IAAZ,GAAmB/B,SAAS,CAAC,CAACH,GAAD,CAAD,EAAQ2D,KAAR,EAAetD,IAAf,CAA5B,GAAoD6B,OAAO,KAAK,IAAZ,GAAmBlC,GAAnB,GAAyBA,GAAG,GAAG,IAF1C,EAGzC4C,YAAY,CAACc,EAAD,CAH6B,CAA3C;QAKD,CAND;QAOA,OAAO,KAAP;MACD;IACF;;IAED,IAAI/D,WAAW,CAACkD,KAAD,CAAf,EAAwB;MACtB,OAAO,IAAP;IACD;;IAEDjB,QAAQ,CAACN,MAAT,CAAgBnB,SAAS,CAACC,IAAD,EAAOJ,GAAP,EAAYK,IAAZ,CAAzB,EAA4CuC,YAAY,CAACC,KAAD,CAAxD;IAEA,OAAO,KAAP;EACD;;EAED,IAAMe,KAAK,GAAG,EAAd;EAEA,IAAMC,cAAc,GAAGC,MAAM,CAACC,MAAP,CAAchD,UAAd,EAA0B;IAC/CyB,cAAc,EAAdA,cAD+C;IAE/CI,YAAY,EAAZA,YAF+C;IAG/CjD,WAAW,EAAXA;EAH+C,CAA1B,CAAvB;;EAMA,SAASqE,KAAT,CAAenB,KAAf,EAAsBzC,IAAtB,EAA4B;IAC1B,IAAIZ,KAAK,CAAC8C,WAAN,CAAkBO,KAAlB,CAAJ,EAA8B;;IAE9B,IAAIe,KAAK,CAACK,OAAN,CAAcpB,KAAd,MAAyB,CAAC,CAA9B,EAAiC;MAC/B,MAAMqB,KAAK,CAAC,oCAAoC9D,IAAI,CAACO,IAAL,CAAU,GAAV,CAArC,CAAX;IACD;;IAEDiD,KAAK,CAACO,IAAN,CAAWtB,KAAX;IAEArD,KAAK,CAACiE,OAAN,CAAcZ,KAAd,EAAqB,SAASrC,IAAT,CAAckD,EAAd,EAAkB1D,GAAlB,EAAuB;MAC1C,IAAMoE,MAAM,GAAG,EAAE5E,KAAK,CAAC8C,WAAN,CAAkBoB,EAAlB,KAAyBA,EAAE,KAAK,IAAlC,KAA2CnB,OAAO,CAAC8B,IAAR,CACxDzC,QADwD,EAC9C8B,EAD8C,EAC1ClE,KAAK,CAAC8E,QAAN,CAAetE,GAAf,IAAsBA,GAAG,CAACuE,IAAJ,EAAtB,GAAmCvE,GADO,EACFI,IADE,EACIyD,cADJ,CAA1D;;MAIA,IAAIO,MAAM,KAAK,IAAf,EAAqB;QACnBJ,KAAK,CAACN,EAAD,EAAKtD,IAAI,GAAGA,IAAI,CAACE,MAAL,CAAYN,GAAZ,CAAH,GAAsB,CAACA,GAAD,CAA/B,CAAL;MACD;IACF,CARD;IAUA4D,KAAK,CAACY,GAAN;EACD;;EAED,IAAI,CAAChF,KAAK,CAACsC,QAAN,CAAeH,GAAf,CAAL,EAA0B;IACxB,MAAM,IAAII,SAAJ,CAAc,wBAAd,CAAN;EACD;;EAEDiC,KAAK,CAACrC,GAAD,CAAL;EAEA,OAAOC,QAAP;AACD;;AAED,eAAeF,UAAf"}, "metadata": {}, "sourceType": "module"}