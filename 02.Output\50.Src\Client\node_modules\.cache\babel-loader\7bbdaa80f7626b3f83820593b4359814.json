{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";import React,{useState,useEffect}from'react';import{useSubscription,useStompClient}from'react-stomp-hooks';import PropTypes from'prop-types';import{sendResultMsg,getWsEndpoint}from'../utils/Util.js';var propTypes={displayNo:PropTypes.number,splitNo:PropTypes.number};var fileUrl=process.env.PUBLIC_URL;var filePath=\"/sounds\";var fileName=\"/sound\";var fileType=\".mp3\";var sound=new Audio();var playcount;var repeatmax;var result;// 再生終了イベント\nsound.addEventListener(\"ended\",function(){console.log('soundPlayEnd: ended');},false);/**\r\n * @module Sound\r\n * @component \r\n * @param {*} props\r\n * @return {*} Sound\r\n */var Sound=function Sound(props){/**\r\n * public class SoundInfo {\r\n *     private Integer soundNo; //再生ファイル番号\r\n *     private Integer repeatCount; //リピート回数\r\n *     private Object sourceData; //実際のContent内容\r\n *     private String id; //該当コンテンツのID\r\n * }\r\n */var _useState=useState(),_useState2=_slicedToArray(_useState,2),soundInfo=_useState2[0],setSoundInfo=_useState2[1];var receiveSound=function receiveSound(message){console.info('receiveSound: '+message.body);var command=JSON.parse(message.body);var isString=function isString(val){return typeof val==='string';};if(command.sourceData){if(isString(command.sourceData)){command.sourceData=JSON.parse(command.sourceData);}}if(command){// 実行結果をクリア\nresult=0;// 再生停止、ループ設定クリア\nsound.pause();sound.currentTime=0;sound.loop=false;// ファイル番号設定\n//console.log( 'command.sound_no = ' + command.soundNo);\n//console.log( 'command.repeat_count = ' + command.repeatCount);\nvar no=command.soundNo;var AudPath=\"\".concat(filePath).concat(fileName).concat(no).concat(fileType);// リピート設定\nplaycount=0;repeatmax=command.repeatCount;if(repeatmax>0){sound.loop=true;}else{sound.loop=false;}console.info('receiveSound: Repeat='+repeatmax+', '+AudPath);// リソース設定して再生\nsound.src=fileUrl+AudPath;sound.play();/* 必ず正常で応答する場合はここを有効化してイベント内の更新を無効化する\r\n      // 更新\r\n      setSoundInfo((split) => ({\r\n        ...split,\r\n        ...command,\r\n      }));\r\n*/ // errorイベント\n//  - エラーによりリソースの読み込みができなかった場合に発行される\nsound.onerror=function(){console.log('sound play error: '+sound.error.message);sound.loop=false;// 更新\nresult=-1;setSoundInfo(function(split){return _objectSpread(_objectSpread({},split),command);});};// playingイベント\n//  - 再生が開始できる状態になったときに発行される\nsound.onplaying=function(){playcount++;console.log('sound playing count('+playcount+')');// リピートの停止処理\nif(playcount<=repeatmax){console.log('sound repeat');}else{sound.loop=false;console.log('sound repeat end ');}// 更新\nresult=0;setSoundInfo(function(split){return _objectSpread(_objectSpread({},split),command);});};}else{console.info('receiveSound: command none');}//console.log( 'receiveSound end.');\n};var wsEndpoint=getWsEndpoint(props.displayNo,props.splitNo);useSubscription(wsEndpoint+'/setSound',receiveSound);// Url:  /monitor/0_0/setSound\nvar stompClient=useStompClient();useEffect(function(){if(soundInfo!==null&&soundInfo!==void 0&&soundInfo.id){sendResultMsg(stompClient,soundInfo.id,result);}},[soundInfo===null||soundInfo===void 0?void 0:soundInfo.id,stompClient]);return;};Sound.propTypes=propTypes;export default Sound;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSubscription", "useStompClient", "PropTypes", "sendResultMsg", "getWsEndpoint", "propTypes", "displayNo", "number", "splitNo", "fileUrl", "process", "env", "PUBLIC_URL", "filePath", "fileName", "fileType", "sound", "Audio", "playcount", "repeatmax", "result", "addEventListener", "console", "log", "Sound", "props", "soundInfo", "setSoundInfo", "receiveSound", "message", "info", "body", "command", "JSON", "parse", "isString", "val", "sourceData", "pause", "currentTime", "loop", "no", "soundNo", "<PERSON>d<PERSON><PERSON>", "repeatCount", "src", "play", "onerror", "error", "split", "onplaying", "wsEndpoint", "stompClient", "id"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/sounds/Sound.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport PropTypes from 'prop-types';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\n\r\nconst propTypes = {\r\n  displayNo: PropTypes.number,\r\n  splitNo: PropTypes.number,\r\n};\r\n\r\nconst fileUrl = process.env.PUBLIC_URL; \r\nconst filePath = \"/sounds\";\r\nconst fileName = \"/sound\";\r\nconst fileType = \".mp3\";\r\nconst sound = new Audio();\r\nlet playcount;\r\nlet repeatmax;\r\nlet result;\r\n\r\n// 再生終了イベント\r\nsound.addEventListener(\"ended\", function () {\r\n  console.log( 'soundPlayEnd: ended');\r\n}, false);\r\n\r\n/**\r\n * @module Sound\r\n * @component \r\n * @param {*} props\r\n * @return {*} Sound\r\n */\r\nconst Sound = (props) => {\r\n /**\r\n * public class SoundInfo {\r\n *     private Integer soundNo; //再生ファイル番号\r\n *     private Integer repeatCount; //リピート回数\r\n *     private Object sourceData; //実際のContent内容\r\n *     private String id; //該当コンテンツのID\r\n * }\r\n */\r\n  const [soundInfo, setSoundInfo] = useState();\r\n\r\n  const receiveSound = (message) => {\r\n    console.info('receiveSound: ' + message.body);\r\n\r\n    let command = JSON.parse(message.body);\r\n    const isString = (val) => typeof val === 'string';\r\n    if (command.sourceData) {\r\n      if (isString(command.sourceData)) {\r\n        command.sourceData = JSON.parse(command.sourceData);\r\n      }\r\n    }\r\n\r\n    if (command) {\r\n      // 実行結果をクリア\r\n      result = 0;\r\n\r\n      // 再生停止、ループ設定クリア\r\n      sound.pause();\r\n      sound.currentTime = 0;\r\n      sound.loop = false;\r\n\r\n      // ファイル番号設定\r\n      //console.log( 'command.sound_no = ' + command.soundNo);\r\n      //console.log( 'command.repeat_count = ' + command.repeatCount);\r\n      let no = command.soundNo;\r\n      let AudPath = `${filePath}${fileName}${no}${fileType}`\r\n      \r\n      // リピート設定\r\n      playcount = 0;\r\n      repeatmax = command.repeatCount;\r\n      if( repeatmax > 0 ){\r\n        sound.loop = true;\r\n      } else {\r\n        sound.loop = false;\r\n      }\r\n\r\n      console.info('receiveSound: Repeat='+ repeatmax + ', '+ AudPath);\r\n\r\n      // リソース設定して再生\r\n      sound.src = (fileUrl + AudPath);\r\n      sound.play();\r\n\r\n/* 必ず正常で応答する場合はここを有効化してイベント内の更新を無効化する\r\n      // 更新\r\n      setSoundInfo((split) => ({\r\n        ...split,\r\n        ...command,\r\n      }));\r\n*/\r\n\r\n      // errorイベント\r\n      //  - エラーによりリソースの読み込みができなかった場合に発行される\r\n      sound.onerror = function() {\r\n        console.log( 'sound play error: ' + sound.error.message);\r\n        sound.loop = false;\r\n        // 更新\r\n        result = -1;\r\n        setSoundInfo((split) => ({\r\n          ...split,\r\n          ...command,\r\n        }));\r\n      }\r\n\r\n      // playingイベント\r\n      //  - 再生が開始できる状態になったときに発行される\r\n      sound.onplaying = function() {\r\n        playcount++;\r\n        console.log( 'sound playing count(' + playcount +')' );\r\n\r\n        // リピートの停止処理\r\n        if( playcount <= repeatmax ){\r\n          console.log( 'sound repeat');\r\n        } else {\r\n          sound.loop = false;\r\n          console.log( 'sound repeat end ' );\r\n        }\r\n\r\n        // 更新\r\n        result = 0;\r\n        setSoundInfo((split) => ({\r\n          ...split,\r\n          ...command,\r\n        }));\r\n      }\r\n\r\n    } else {\r\n      console.info('receiveSound: command none');\r\n    }\r\n\r\n    //console.log( 'receiveSound end.');\r\n  };\r\n\r\n  const wsEndpoint = getWsEndpoint(\r\n    props.displayNo,\r\n    props.splitNo,\r\n  );\r\n\r\n  useSubscription(wsEndpoint + '/setSound', receiveSound); // Url:  /monitor/0_0/setSound\r\n\r\n  const stompClient = useStompClient();\r\n\r\n  useEffect(() => {\r\n    if (soundInfo?.id) {\r\n      sendResultMsg(stompClient, soundInfo.id, result);\r\n    }\r\n  }, [soundInfo?.id, stompClient]);\r\n\r\n  return;\r\n};\r\n\r\nSound.propTypes = propTypes;\r\nexport default Sound;\r\n"], "mappings": "mRAAA,MAAOA,MAAP,EAAgBC,QAAhB,CAA0BC,SAA1B,KAA2C,OAA3C,CACA,OAASC,eAAT,CAA0BC,cAA1B,KAAgD,mBAAhD,CACA,MAAOC,UAAP,KAAsB,YAAtB,CACA,OAASC,aAAT,CAAwBC,aAAxB,KAA6C,kBAA7C,CAEA,GAAMC,UAAS,CAAG,CAChBC,SAAS,CAAEJ,SAAS,CAACK,MADL,CAEhBC,OAAO,CAAEN,SAAS,CAACK,MAFH,CAAlB,CAKA,GAAME,QAAO,CAAGC,OAAO,CAACC,GAAR,CAAYC,UAA5B,CACA,GAAMC,SAAQ,CAAG,SAAjB,CACA,GAAMC,SAAQ,CAAG,QAAjB,CACA,GAAMC,SAAQ,CAAG,MAAjB,CACA,GAAMC,MAAK,CAAG,GAAIC,MAAJ,EAAd,CACA,GAAIC,UAAJ,CACA,GAAIC,UAAJ,CACA,GAAIC,OAAJ,CAEA;AACAJ,KAAK,CAACK,gBAAN,CAAuB,OAAvB,CAAgC,UAAY,CAC1CC,OAAO,CAACC,GAAR,CAAa,qBAAb,EACD,CAFD,CAEG,KAFH,EAIA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,MAAK,CAAG,QAARA,MAAQ,CAACC,KAAD,CAAW,CACxB;AACD;AACA;AACA;AACA;AACA;AACA;AACA,GACE,cAAkC3B,QAAQ,EAA1C,wCAAO4B,SAAP,eAAkBC,YAAlB,eAEA,GAAMC,aAAY,CAAG,QAAfA,aAAe,CAACC,OAAD,CAAa,CAChCP,OAAO,CAACQ,IAAR,CAAa,iBAAmBD,OAAO,CAACE,IAAxC,EAEA,GAAIC,QAAO,CAAGC,IAAI,CAACC,KAAL,CAAWL,OAAO,CAACE,IAAnB,CAAd,CACA,GAAMI,SAAQ,CAAG,QAAXA,SAAW,CAACC,GAAD,QAAS,OAAOA,IAAP,GAAe,QAAxB,EAAjB,CACA,GAAIJ,OAAO,CAACK,UAAZ,CAAwB,CACtB,GAAIF,QAAQ,CAACH,OAAO,CAACK,UAAT,CAAZ,CAAkC,CAChCL,OAAO,CAACK,UAAR,CAAqBJ,IAAI,CAACC,KAAL,CAAWF,OAAO,CAACK,UAAnB,CAArB,CACD,CACF,CAED,GAAIL,OAAJ,CAAa,CACX;AACAZ,MAAM,CAAG,CAAT,CAEA;AACAJ,KAAK,CAACsB,KAAN,GACAtB,KAAK,CAACuB,WAAN,CAAoB,CAApB,CACAvB,KAAK,CAACwB,IAAN,CAAa,KAAb,CAEA;AACA;AACA;AACA,GAAIC,GAAE,CAAGT,OAAO,CAACU,OAAjB,CACA,GAAIC,QAAO,WAAM9B,QAAN,SAAiBC,QAAjB,SAA4B2B,EAA5B,SAAiC1B,QAAjC,CAAX,CAEA;AACAG,SAAS,CAAG,CAAZ,CACAC,SAAS,CAAGa,OAAO,CAACY,WAApB,CACA,GAAIzB,SAAS,CAAG,CAAhB,CAAmB,CACjBH,KAAK,CAACwB,IAAN,CAAa,IAAb,CACD,CAFD,IAEO,CACLxB,KAAK,CAACwB,IAAN,CAAa,KAAb,CACD,CAEDlB,OAAO,CAACQ,IAAR,CAAa,wBAAyBX,SAAzB,CAAqC,IAArC,CAA2CwB,OAAxD,EAEA;AACA3B,KAAK,CAAC6B,GAAN,CAAapC,OAAO,CAAGkC,OAAvB,CACA3B,KAAK,CAAC8B,IAAN,GAEN;AACA;AACA;AACA;AACA;AACA;AACA,EApCiB,CAsCX;AACA;AACA9B,KAAK,CAAC+B,OAAN,CAAgB,UAAW,CACzBzB,OAAO,CAACC,GAAR,CAAa,qBAAuBP,KAAK,CAACgC,KAAN,CAAYnB,OAAhD,EACAb,KAAK,CAACwB,IAAN,CAAa,KAAb,CACA;AACApB,MAAM,CAAG,CAAC,CAAV,CACAO,YAAY,CAAC,SAACsB,KAAD,wCACRA,KADQ,EAERjB,OAFQ,GAAD,CAAZ,CAID,CATD,CAWA;AACA;AACAhB,KAAK,CAACkC,SAAN,CAAkB,UAAW,CAC3BhC,SAAS,GACTI,OAAO,CAACC,GAAR,CAAa,uBAAyBL,SAAzB,CAAoC,GAAjD,EAEA;AACA,GAAIA,SAAS,EAAIC,SAAjB,CAA4B,CAC1BG,OAAO,CAACC,GAAR,CAAa,cAAb,EACD,CAFD,IAEO,CACLP,KAAK,CAACwB,IAAN,CAAa,KAAb,CACAlB,OAAO,CAACC,GAAR,CAAa,mBAAb,EACD,CAED;AACAH,MAAM,CAAG,CAAT,CACAO,YAAY,CAAC,SAACsB,KAAD,wCACRA,KADQ,EAERjB,OAFQ,GAAD,CAAZ,CAID,CAlBD,CAoBD,CAzED,IAyEO,CACLV,OAAO,CAACQ,IAAR,CAAa,4BAAb,EACD,CAED;AACD,CAzFD,CA2FA,GAAMqB,WAAU,CAAG/C,aAAa,CAC9BqB,KAAK,CAACnB,SADwB,CAE9BmB,KAAK,CAACjB,OAFwB,CAAhC,CAKAR,eAAe,CAACmD,UAAU,CAAG,WAAd,CAA2BvB,YAA3B,CAAf,CAAyD;AAEzD,GAAMwB,YAAW,CAAGnD,cAAc,EAAlC,CAEAF,SAAS,CAAC,UAAM,CACd,GAAI2B,SAAJ,SAAIA,SAAJ,WAAIA,SAAS,CAAE2B,EAAf,CAAmB,CACjBlD,aAAa,CAACiD,WAAD,CAAc1B,SAAS,CAAC2B,EAAxB,CAA4BjC,MAA5B,CAAb,CACD,CACF,CAJQ,CAIN,CAACM,SAAD,SAACA,SAAD,iBAACA,SAAS,CAAE2B,EAAZ,CAAgBD,WAAhB,CAJM,CAAT,CAMA,OACD,CAtHD,CAwHA5B,KAAK,CAACnB,SAAN,CAAkBA,SAAlB,CACA,cAAemB,MAAf"}, "metadata": {}, "sourceType": "module"}