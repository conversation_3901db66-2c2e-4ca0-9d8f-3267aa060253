{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits'),\n    urlUtils = require('./utils/url'),\n    XDR = require('./transport/sender/xdr'),\n    XHRCors = require('./transport/sender/xhr-cors'),\n    XHRLocal = require('./transport/sender/xhr-local'),\n    XHRFake = require('./transport/sender/xhr-fake'),\n    InfoIframe = require('./info-iframe'),\n    InfoAjax = require('./info-ajax');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\n\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\n\ninherits(InfoReceiver, EventEmitter); // TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function (baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n\n  return new InfoAjax(url, XHRFake);\n};\n\nInfoReceiver.prototype.doXhr = function (baseUrl, urlInfo) {\n  var self = this,\n      url = urlUtils.addPath(baseUrl, '/info');\n  debug('doXhr', url);\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n  this.timeoutRef = setTimeout(function () {\n    debug('timeout');\n\n    self._cleanup(false);\n\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n  this.xo.once('finish', function (info, rtt) {\n    debug('finish', info, rtt);\n\n    self._cleanup(true);\n\n    self.emit('finish', info, rtt);\n  });\n};\n\nInfoReceiver.prototype._cleanup = function (wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n\n  this.xo = null;\n};\n\nInfoReceiver.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n\n  this._cleanup(false);\n};\n\nInfoReceiver.timeout = 8000;\nmodule.exports = InfoReceiver;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "urlUtils", "XDR", "XHRCors", "XHRLocal", "XHRFake", "InfoIframe", "InfoAjax", "debug", "process", "env", "NODE_ENV", "InfoReceiver", "baseUrl", "urlInfo", "self", "call", "setTimeout", "doXhr", "_getReceiver", "url", "<PERSON><PERSON><PERSON><PERSON>", "enabled", "sameScheme", "prototype", "addPath", "xo", "timeoutRef", "_cleanup", "emit", "timeout", "once", "info", "rtt", "<PERSON><PERSON><PERSON>", "clearTimeout", "close", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/info-receiver.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , urlUtils = require('./utils/url')\n  , XDR = require('./transport/sender/xdr')\n  , XHRCors = require('./transport/sender/xhr-cors')\n  , XHRLocal = require('./transport/sender/xhr-local')\n  , XHRFake = require('./transport/sender/xhr-fake')\n  , InfoIframe = require('./info-iframe')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\n\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\n\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function(baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\n\nInfoReceiver.prototype.doXhr = function(baseUrl, urlInfo) {\n  var self = this\n    , url = urlUtils.addPath(baseUrl, '/info')\n    ;\n  debug('doXhr', url);\n\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n\n  this.timeoutRef = setTimeout(function() {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n\n  this.xo.once('finish', function(info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\n\nInfoReceiver.prototype._cleanup = function(wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\n\nInfoReceiver.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\n\nInfoReceiver.timeout = 8000;\n\nmodule.exports = InfoReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,aAAD,CAFtB;AAAA,IAGIG,GAAG,GAAGH,OAAO,CAAC,wBAAD,CAHjB;AAAA,IAIII,OAAO,GAAGJ,OAAO,CAAC,6BAAD,CAJrB;AAAA,IAKIK,QAAQ,GAAGL,OAAO,CAAC,8BAAD,CALtB;AAAA,IAMIM,OAAO,GAAGN,OAAO,CAAC,6BAAD,CANrB;AAAA,IAOIO,UAAU,GAAGP,OAAO,CAAC,eAAD,CAPxB;AAAA,IAQIQ,QAAQ,GAAGR,OAAO,CAAC,aAAD,CARtB;;AAWA,IAAIS,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGT,OAAO,CAAC,OAAD,CAAP,CAAiB,6BAAjB,CAAR;AACD;;AAED,SAASa,YAAT,CAAsBC,OAAtB,EAA+BC,OAA/B,EAAwC;EACtCN,KAAK,CAACK,OAAD,CAAL;EACA,IAAIE,IAAI,GAAG,IAAX;EACAjB,YAAY,CAACkB,IAAb,CAAkB,IAAlB;EAEAC,UAAU,CAAC,YAAW;IACpBF,IAAI,CAACG,KAAL,CAAWL,OAAX,EAAoBC,OAApB;EACD,CAFS,EAEP,CAFO,CAAV;AAGD;;AAEDd,QAAQ,CAACY,YAAD,EAAed,YAAf,CAAR,C,CAEA;;AAEAc,YAAY,CAACO,YAAb,GAA4B,UAASN,OAAT,EAAkBO,GAAlB,EAAuBN,OAAvB,EAAgC;EAC1D;EACA,IAAIA,OAAO,CAACO,UAAZ,EAAwB;IACtB,OAAO,IAAId,QAAJ,CAAaa,GAAb,EAAkBhB,QAAlB,CAAP;EACD;;EACD,IAAID,OAAO,CAACmB,OAAZ,EAAqB;IACnB,OAAO,IAAIf,QAAJ,CAAaa,GAAb,EAAkBjB,OAAlB,CAAP;EACD;;EACD,IAAID,GAAG,CAACoB,OAAJ,IAAeR,OAAO,CAACS,UAA3B,EAAuC;IACrC,OAAO,IAAIhB,QAAJ,CAAaa,GAAb,EAAkBlB,GAAlB,CAAP;EACD;;EACD,IAAII,UAAU,CAACgB,OAAX,EAAJ,EAA0B;IACxB,OAAO,IAAIhB,UAAJ,CAAeO,OAAf,EAAwBO,GAAxB,CAAP;EACD;;EACD,OAAO,IAAIb,QAAJ,CAAaa,GAAb,EAAkBf,OAAlB,CAAP;AACD,CAfD;;AAiBAO,YAAY,CAACY,SAAb,CAAuBN,KAAvB,GAA+B,UAASL,OAAT,EAAkBC,OAAlB,EAA2B;EACxD,IAAIC,IAAI,GAAG,IAAX;EAAA,IACIK,GAAG,GAAGnB,QAAQ,CAACwB,OAAT,CAAiBZ,OAAjB,EAA0B,OAA1B,CADV;EAGAL,KAAK,CAAC,OAAD,EAAUY,GAAV,CAAL;EAEA,KAAKM,EAAL,GAAUd,YAAY,CAACO,YAAb,CAA0BN,OAA1B,EAAmCO,GAAnC,EAAwCN,OAAxC,CAAV;EAEA,KAAKa,UAAL,GAAkBV,UAAU,CAAC,YAAW;IACtCT,KAAK,CAAC,SAAD,CAAL;;IACAO,IAAI,CAACa,QAAL,CAAc,KAAd;;IACAb,IAAI,CAACc,IAAL,CAAU,QAAV;EACD,CAJ2B,EAIzBjB,YAAY,CAACkB,OAJY,CAA5B;EAMA,KAAKJ,EAAL,CAAQK,IAAR,CAAa,QAAb,EAAuB,UAASC,IAAT,EAAeC,GAAf,EAAoB;IACzCzB,KAAK,CAAC,QAAD,EAAWwB,IAAX,EAAiBC,GAAjB,CAAL;;IACAlB,IAAI,CAACa,QAAL,CAAc,IAAd;;IACAb,IAAI,CAACc,IAAL,CAAU,QAAV,EAAoBG,IAApB,EAA0BC,GAA1B;EACD,CAJD;AAKD,CAnBD;;AAqBArB,YAAY,CAACY,SAAb,CAAuBI,QAAvB,GAAkC,UAASM,QAAT,EAAmB;EACnD1B,KAAK,CAAC,UAAD,CAAL;EACA2B,YAAY,CAAC,KAAKR,UAAN,CAAZ;EACA,KAAKA,UAAL,GAAkB,IAAlB;;EACA,IAAI,CAACO,QAAD,IAAa,KAAKR,EAAtB,EAA0B;IACxB,KAAKA,EAAL,CAAQU,KAAR;EACD;;EACD,KAAKV,EAAL,GAAU,IAAV;AACD,CARD;;AAUAd,YAAY,CAACY,SAAb,CAAuBY,KAAvB,GAA+B,YAAW;EACxC5B,KAAK,CAAC,OAAD,CAAL;EACA,KAAK6B,kBAAL;;EACA,KAAKT,QAAL,CAAc,KAAd;AACD,CAJD;;AAMAhB,YAAY,CAACkB,OAAb,GAAuB,IAAvB;AAEAQ,MAAM,CAACC,OAAP,GAAiB3B,YAAjB"}, "metadata": {}, "sourceType": "script"}