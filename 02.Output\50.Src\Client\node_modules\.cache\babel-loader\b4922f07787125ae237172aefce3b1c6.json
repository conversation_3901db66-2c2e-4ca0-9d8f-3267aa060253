{"ast": null, "code": "var SetCache = require('./_SetCache'),\n    arraySome = require('./_arraySome'),\n    cacheHas = require('./_cacheHas');\n/** Used to compose bitmasks for value comparisons. */\n\n\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\n\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  } // Check that cyclic values are equal.\n\n\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n\n  var index = -1,\n      result = true,\n      seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache() : undefined;\n  stack.set(array, other);\n  stack.set(other, array); // Ignore non-index properties.\n\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);\n    }\n\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n\n      result = false;\n      break;\n    } // Recursively compare arrays (susceptible to call stack limits).\n\n\n    if (seen) {\n      if (!arraySome(other, function (othValue, othIndex) {\n        if (!cacheHas(seen, othIndex) && (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n          return seen.push(othIndex);\n        }\n      })) {\n        result = false;\n        break;\n      }\n    } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n      result = false;\n      break;\n    }\n  }\n\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalArrays;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "arraySome", "cacheHas", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "equalArrays", "array", "other", "bitmask", "customizer", "equalFunc", "stack", "isPartial", "arr<PERSON><PERSON><PERSON>", "length", "oth<PERSON><PERSON><PERSON>", "arrStacked", "get", "othStacked", "index", "result", "seen", "undefined", "set", "arrV<PERSON>ue", "othValue", "compared", "othIndex", "push", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_equalArrays.js"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arraySome = require('./_arraySome'),\n    cacheHas = require('./_cacheHas');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  // Check that cyclic values are equal.\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1,\n      result = true,\n      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;\n\n  stack.set(array, other);\n  stack.set(other, array);\n\n  // Ignore non-index properties.\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, arrValue, index, other, array, stack)\n        : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    // Recursively compare arrays (susceptible to call stack limits).\n    if (seen) {\n      if (!arraySome(other, function(othValue, othIndex) {\n            if (!cacheHas(seen, othIndex) &&\n                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n              return seen.push(othIndex);\n            }\n          })) {\n        result = false;\n        break;\n      }\n    } else if (!(\n          arrValue === othValue ||\n            equalFunc(arrValue, othValue, bitmask, customizer, stack)\n        )) {\n      result = false;\n      break;\n    }\n  }\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalArrays;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAD,CAAtB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,cAAD,CADvB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,aAAD,CAFtB;AAIA;;;AACA,IAAIG,oBAAoB,GAAG,CAA3B;AAAA,IACIC,sBAAsB,GAAG,CAD7B;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4BC,KAA5B,EAAmCC,OAAnC,EAA4CC,UAA5C,EAAwDC,SAAxD,EAAmEC,KAAnE,EAA0E;EACxE,IAAIC,SAAS,GAAGJ,OAAO,GAAGL,oBAA1B;EAAA,IACIU,SAAS,GAAGP,KAAK,CAACQ,MADtB;EAAA,IAEIC,SAAS,GAAGR,KAAK,CAACO,MAFtB;;EAIA,IAAID,SAAS,IAAIE,SAAb,IAA0B,EAAEH,SAAS,IAAIG,SAAS,GAAGF,SAA3B,CAA9B,EAAqE;IACnE,OAAO,KAAP;EACD,CAPuE,CAQxE;;;EACA,IAAIG,UAAU,GAAGL,KAAK,CAACM,GAAN,CAAUX,KAAV,CAAjB;EACA,IAAIY,UAAU,GAAGP,KAAK,CAACM,GAAN,CAAUV,KAAV,CAAjB;;EACA,IAAIS,UAAU,IAAIE,UAAlB,EAA8B;IAC5B,OAAOF,UAAU,IAAIT,KAAd,IAAuBW,UAAU,IAAIZ,KAA5C;EACD;;EACD,IAAIa,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAG,IADb;EAAA,IAEIC,IAAI,GAAIb,OAAO,GAAGJ,sBAAX,GAAqC,IAAIL,QAAJ,EAArC,GAAoDuB,SAF/D;EAIAX,KAAK,CAACY,GAAN,CAAUjB,KAAV,EAAiBC,KAAjB;EACAI,KAAK,CAACY,GAAN,CAAUhB,KAAV,EAAiBD,KAAjB,EAnBwE,CAqBxE;;EACA,OAAO,EAAEa,KAAF,GAAUN,SAAjB,EAA4B;IAC1B,IAAIW,QAAQ,GAAGlB,KAAK,CAACa,KAAD,CAApB;IAAA,IACIM,QAAQ,GAAGlB,KAAK,CAACY,KAAD,CADpB;;IAGA,IAAIV,UAAJ,EAAgB;MACd,IAAIiB,QAAQ,GAAGd,SAAS,GACpBH,UAAU,CAACgB,QAAD,EAAWD,QAAX,EAAqBL,KAArB,EAA4BZ,KAA5B,EAAmCD,KAAnC,EAA0CK,KAA1C,CADU,GAEpBF,UAAU,CAACe,QAAD,EAAWC,QAAX,EAAqBN,KAArB,EAA4Bb,KAA5B,EAAmCC,KAAnC,EAA0CI,KAA1C,CAFd;IAGD;;IACD,IAAIe,QAAQ,KAAKJ,SAAjB,EAA4B;MAC1B,IAAII,QAAJ,EAAc;QACZ;MACD;;MACDN,MAAM,GAAG,KAAT;MACA;IACD,CAfyB,CAgB1B;;;IACA,IAAIC,IAAJ,EAAU;MACR,IAAI,CAACpB,SAAS,CAACM,KAAD,EAAQ,UAASkB,QAAT,EAAmBE,QAAnB,EAA6B;QAC7C,IAAI,CAACzB,QAAQ,CAACmB,IAAD,EAAOM,QAAP,CAAT,KACCH,QAAQ,KAAKC,QAAb,IAAyBf,SAAS,CAACc,QAAD,EAAWC,QAAX,EAAqBjB,OAArB,EAA8BC,UAA9B,EAA0CE,KAA1C,CADnC,CAAJ,EAC0F;UACxF,OAAOU,IAAI,CAACO,IAAL,CAAUD,QAAV,CAAP;QACD;MACF,CALS,CAAd,EAKQ;QACNP,MAAM,GAAG,KAAT;QACA;MACD;IACF,CAVD,MAUO,IAAI,EACLI,QAAQ,KAAKC,QAAb,IACEf,SAAS,CAACc,QAAD,EAAWC,QAAX,EAAqBjB,OAArB,EAA8BC,UAA9B,EAA0CE,KAA1C,CAFN,CAAJ,EAGA;MACLS,MAAM,GAAG,KAAT;MACA;IACD;EACF;;EACDT,KAAK,CAAC,QAAD,CAAL,CAAgBL,KAAhB;EACAK,KAAK,CAAC,QAAD,CAAL,CAAgBJ,KAAhB;EACA,OAAOa,MAAP;AACD;;AAEDS,MAAM,CAACC,OAAP,GAAiBzB,WAAjB"}, "metadata": {}, "sourceType": "script"}