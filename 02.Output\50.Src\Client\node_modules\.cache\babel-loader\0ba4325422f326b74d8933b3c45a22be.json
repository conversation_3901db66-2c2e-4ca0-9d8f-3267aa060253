{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4;import React,{useRef,useEffect,useState}from'react';import styled,{keyframes,css}from'styled-components';import{getHexColor,getTextOrDisplayColor}from'../../utils/Util.js';import{useTimeout}from'ahooks';import Cell from'./Cell';import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";/**\r\n * 子供のコンポーネントを点滅実施する\r\n * \r\n * @module BlinkBlock\r\n * @component\r\n *\r\n * @param {*} props\r\n * @return {*} \r\n */var BlinkBlock=function BlinkBlock(props){var _props$blink_setting6,_props$blink_setting7,_props$blink_setting8;var _useState=useState(false),_useState2=_slicedToArray(_useState,2),blinkTimeout=_useState2[0],setBlinkTimeout=_useState2[1];var _useState3=useState(null),_useState4=_slicedToArray(_useState3,2),delay=_useState4[0],setDelay=_useState4[1];var lastTimer=useRef(null);useEffect(function(){var _props$blink_setting,_props$blink_setting2,_props$blink_setting3;setBlinkTimeout(false);//TimerをClearしてから\nif(lastTimer.current){// 既に走っているタイマーを停止\n// lastTimer.current();\n// console.log('BlinkBlock clear timer before setDelay.');\nconsole.log('BlinkBlock timer is running.');}if((props===null||props===void 0?void 0:(_props$blink_setting=props.blink_setting)===null||_props$blink_setting===void 0?void 0:_props$blink_setting.lighting_status)==='3'&&(props===null||props===void 0?void 0:(_props$blink_setting2=props.blink_setting)===null||_props$blink_setting2===void 0?void 0:_props$blink_setting2.blink_speed)>0&&(props===null||props===void 0?void 0:(_props$blink_setting3=props.blink_setting)===null||_props$blink_setting3===void 0?void 0:_props$blink_setting3.blink_time)>0){var _props$blink_setting4,_props$blink_setting5;console.log('BlinkBlock setDelay. oldDelay: '+delay+'. newDelay:'+(props===null||props===void 0?void 0:(_props$blink_setting4=props.blink_setting)===null||_props$blink_setting4===void 0?void 0:_props$blink_setting4.blink_time)*1000);setDelay((props===null||props===void 0?void 0:(_props$blink_setting5=props.blink_setting)===null||_props$blink_setting5===void 0?void 0:_props$blink_setting5.blink_time)*1000);}},[props===null||props===void 0?void 0:props.blink_setting]);// 上記の配列中で、Delayを追加してはいけません。\nlastTimer.current=useTimeout(function(){setBlinkTimeout(true);setDelay(null);console.log('BlinkBlock useTimeout. With delay: '+delay);},delay);useEffect(function(){return function(){// BlinkBlock: clear timer when unmouting.\nif(lastTimer.current){console.log('BlinkBlock clear timer when unmouting.');lastTimer.current();}};},[]);var lightingStatus=props===null||props===void 0?void 0:(_props$blink_setting6=props.blink_setting)===null||_props$blink_setting6===void 0?void 0:_props$blink_setting6.lighting_status;if(blinkTimeout){console.log('BlinkBlock blinkTimeout true. lightingStatus: '+lightingStatus);lightingStatus='1';}if((props===null||props===void 0?void 0:(_props$blink_setting7=props.blink_setting)===null||_props$blink_setting7===void 0?void 0:_props$blink_setting7.blink_speed)<=0||(props===null||props===void 0?void 0:(_props$blink_setting8=props.blink_setting)===null||_props$blink_setting8===void 0?void 0:_props$blink_setting8.blink_time)<=0){lightingStatus='1';}return/*#__PURE__*/_jsx(_Fragment,{children:Array.isArray(props.block)&&props.block.map(function(item,index){return/*#__PURE__*/_jsx(BlockRow,_objectSpread(_objectSpread({},item),{},{lightingStatus:lightingStatus,blink_setting:props===null||props===void 0?void 0:props.blink_setting}),index);})});};var animation=function animation(props){return keyframes(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  0% {\\n    color: \",\";\\n    background-color: \",\";\\n  }\\n  100% {\\n    color: \",\";\\n    background-color: \",\";\\n  }\\n\"])),getTextOrDisplayColor(props),getHexColor(props.background_color),getLightingTextColor(props),getHexColor(props.lighting_background_color));};var animationRule=css(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  \",\" \",\"s linear infinite none alternate\\n\"])),animation,function(props){return props.blink_speed/1000;});//点滅を実施するDivを作成\nvar BlinkHtmlTag=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  animation: \",\";\\n  animation-play-state: running;\\n  animation-fill-mode: forwards;\\n  position: relative;\\n\"])),animationRule);//点滅を実施しないDivを作成\nvar StaticHtmlTag=styled.div.attrs(function(props){return{style:{color:getTextOrDisplayColor(props),backgroundColor:getHexColor(props.background_color)}};})(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  position: relative;\\n\"])));/**\r\n * 点滅対象の一行。lightingStatusに従って、点滅するかを決める\r\n * @param {*} props \r\n * @returns 一行の点滅対象\r\n */var BlockRow=function BlockRow(props){// props.showInfoにdisplay_colorが有っても、下記設定したtext_colorが優先される\nvar divStyle=_objectSpread(_objectSpread({},props.showInfo),props.blink_setting);if(props.lightingStatus==='1'){var _props$showInfo;divStyle.text_color=getTextOrDisplayColor(props.showInfo);divStyle.background_color=getHexColor((_props$showInfo=props.showInfo)===null||_props$showInfo===void 0?void 0:_props$showInfo.background_color);}else if(props.lightingStatus==='2'){var _props$blink_setting9;divStyle.text_color=getLightingTextColor(props);divStyle.background_color=getHexColor((_props$blink_setting9=props.blink_setting)===null||_props$blink_setting9===void 0?void 0:_props$blink_setting9.lighting_background_color);}if(props.lightingStatus==='3'){var _props$showInfo2;return/*#__PURE__*/_jsx(\"div\",{className:props.className,children:/*#__PURE__*/_jsx(BlinkHtmlTag,_objectSpread(_objectSpread({},divStyle),{},{children:/*#__PURE__*/_jsx(Cell,{display_text:(_props$showInfo2=props.showInfo)===null||_props$showInfo2===void 0?void 0:_props$showInfo2.display_text})}))});}else{var _props$showInfo3;return/*#__PURE__*/_jsx(\"div\",{className:props.className,children:/*#__PURE__*/_jsx(StaticHtmlTag,_objectSpread(_objectSpread({},divStyle),{},{children:/*#__PURE__*/_jsx(Cell,{display_text:(_props$showInfo3=props.showInfo)===null||_props$showInfo3===void 0?void 0:_props$showInfo3.display_text})}))});}};// APIのパラメータに違いをカバーして、色の値を返す\nfunction getLightingTextColor(props){var blink_setting=(props===null||props===void 0?void 0:props.blink_setting)||(props===null||props===void 0?void 0:props.lighting_setting);// 下記Bug対応：\n// lighting_status を 3 (点滅…文字色/背景色 ⇔ 点灯時文字色/点灯時背景色で画面に表示) に設定した場合に lighting_text_color (点滅時の文字色) の設定値が反映されず点滅時に必ず白文字となる。\n// (lighting_background_color (点滅時の背景色) は設定値が反映されて、指定色で点滅する。こちらは問題なし。)\nif(blink_setting){if(blink_setting!==null&&blink_setting!==void 0&&blink_setting.lighting_text_color){return getHexColor(blink_setting===null||blink_setting===void 0?void 0:blink_setting.lighting_text_color);}else if(blink_setting!==null&&blink_setting!==void 0&&blink_setting.lighting_display_color){return getHexColor(blink_setting===null||blink_setting===void 0?void 0:blink_setting.lighting_display_color);}}else{if(props!==null&&props!==void 0&&props.lighting_text_color){return getHexColor(props===null||props===void 0?void 0:props.lighting_text_color);}else if(props!==null&&props!==void 0&&props.lighting_display_color){return getHexColor(props===null||props===void 0?void 0:props.lighting_display_color);}return'';}}var space=' ';/**\r\n * BaseのObjectとチェックして、色と背景色をTargetObjectにCopy <br>\r\n * TargetObjectにTextがなければ、空文字を入れる\r\n * \r\n * @param {*} targetObj \r\n * @param {*} baseObj \r\n * @returns Target Object\r\n */export function checkBlinkInfo(targetObj,baseObj){var _targetObj,_targetObj2,_targetObj3,_targetObj4;if(!targetObj){targetObj={};}if(((_targetObj=targetObj)===null||_targetObj===void 0?void 0:_targetObj.display_text)===space){return targetObj;}if(!((_targetObj2=targetObj)!==null&&_targetObj2!==void 0&&_targetObj2.display_text)){targetObj.display_text=space;return targetObj;}if(!((_targetObj3=targetObj)!==null&&_targetObj3!==void 0&&_targetObj3.text_color)&&baseObj){targetObj.text_color=baseObj.text_color;}if(!((_targetObj4=targetObj)!==null&&_targetObj4!==void 0&&_targetObj4.background_color)&&baseObj){targetObj.background_color=baseObj.background_color;}return targetObj;}export default BlinkBlock;", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "styled", "keyframes", "css", "getHexColor", "getTextOrDisplayColor", "useTimeout", "Cell", "BlinkBlock", "props", "blinkTimeout", "setBlinkTimeout", "delay", "<PERSON><PERSON><PERSON><PERSON>", "lastTimer", "current", "console", "log", "blink_setting", "lighting_status", "blink_speed", "blink_time", "lightingStatus", "Array", "isArray", "block", "map", "item", "index", "animation", "background_color", "getLightingTextColor", "lighting_background_color", "animationRule", "BlinkHtmlTag", "div", "StaticHtmlTag", "attrs", "style", "color", "backgroundColor", "BlockRow", "divStyle", "showInfo", "text_color", "className", "display_text", "lighting_setting", "lighting_text_color", "lighting_display_color", "space", "checkBlinkInfo", "targetObj", "baseObj"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/BlinkBlock.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\nimport styled, { keyframes, css } from 'styled-components';\r\nimport { getHexColor, getTextOrDisplayColor } from '../../utils/Util.js';\r\nimport { useTimeout } from 'ahooks';\r\nimport Cell from './Cell';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n    blink_setting: PropTypes.object,\r\n    block: PropTypes.array.isRequired,\r\n};\r\n\r\n/**\r\n * 子供のコンポーネントを点滅実施する\r\n * \r\n * @module BlinkBlock\r\n * @component\r\n *\r\n * @param {*} props\r\n * @return {*} \r\n */\r\nconst BlinkBlock = (props) => {\r\n    const [blinkTimeout, setBlinkTimeout] = useState(false);\r\n    const [delay, setDelay] = useState(null);\r\n    const lastTimer = useRef(null);\r\n\r\n    useEffect(() => {\r\n        setBlinkTimeout(false);\r\n\r\n        //TimerをClearしてから\r\n        if (lastTimer.current) {\r\n            // 既に走っているタイマーを停止\r\n            // lastTimer.current();\r\n            // console.log('BlinkBlock clear timer before setDelay.');\r\n            console.log('BlinkBlock timer is running.');\r\n        }\r\n\r\n        if (\r\n            props?.blink_setting?.lighting_status === '3' &&\r\n            props?.blink_setting?.blink_speed > 0 &&\r\n            props?.blink_setting?.blink_time > 0\r\n        ) {\r\n            console.log('BlinkBlock setDelay. oldDelay: ' + delay + '. newDelay:' + props?.blink_setting?.blink_time * 1000);\r\n            setDelay(props?.blink_setting?.blink_time * 1000);\r\n        }\r\n    }, [props?.blink_setting]);\r\n    // 上記の配列中で、Delayを追加してはいけません。\r\n\r\n    lastTimer.current = useTimeout(() => {\r\n        setBlinkTimeout(true);\r\n        setDelay(null);\r\n        console.log('BlinkBlock useTimeout. With delay: ' + delay);\r\n    }, delay);\r\n\r\n    useEffect(() => {\r\n        return () => {\r\n            // BlinkBlock: clear timer when unmouting.\r\n            if (lastTimer.current) {\r\n                console.log('BlinkBlock clear timer when unmouting.');\r\n                lastTimer.current();\r\n            }\r\n        };\r\n    }, []);\r\n\r\n    let lightingStatus = props?.blink_setting?.lighting_status;\r\n    if (blinkTimeout) {\r\n        console.log('BlinkBlock blinkTimeout true. lightingStatus: ' + lightingStatus);\r\n        lightingStatus = '1';\r\n    }\r\n    if (\r\n        props?.blink_setting?.blink_speed <= 0 ||\r\n        props?.blink_setting?.blink_time <= 0\r\n    ) {\r\n        lightingStatus = '1';\r\n    }\r\n    return (\r\n        <>\r\n            {Array.isArray(props.block) &&\r\n                props.block.map((item, index) => {\r\n                    return (\r\n                        <BlockRow\r\n                            key={index}\r\n                            {...item}\r\n                            lightingStatus={lightingStatus}\r\n                            blink_setting={props?.blink_setting}\r\n                        />\r\n                    );\r\n                })}\r\n        </>\r\n    );\r\n};\r\n\r\nconst animation = (props) => keyframes`\r\n  0% {\r\n    color: ${getTextOrDisplayColor(props)};\r\n    background-color: ${getHexColor(props.background_color)};\r\n  }\r\n  100% {\r\n    color: ${getLightingTextColor(props)};\r\n    background-color: ${getHexColor(props.lighting_background_color)};\r\n  }\r\n`;\r\n\r\nconst animationRule = css`\r\n  ${animation} ${(props) =>\r\n        props.blink_speed / 1000}s linear infinite none alternate\r\n`;\r\n\r\n//点滅を実施するDivを作成\r\nconst BlinkHtmlTag = styled.div`\r\n  animation: ${animationRule};\r\n  animation-play-state: running;\r\n  animation-fill-mode: forwards;\r\n  position: relative;\r\n`;\r\n\r\n//点滅を実施しないDivを作成\r\nconst StaticHtmlTag = styled.div.attrs((props) => ({\r\n    style: {\r\n        color: getTextOrDisplayColor(props),\r\n        backgroundColor: getHexColor(props.background_color),\r\n    },\r\n}))`\r\n  position: relative;\r\n`;\r\n\r\n/**\r\n * 点滅対象の一行。lightingStatusに従って、点滅するかを決める\r\n * @param {*} props \r\n * @returns 一行の点滅対象\r\n */\r\nconst BlockRow = (props) => {\r\n    // props.showInfoにdisplay_colorが有っても、下記設定したtext_colorが優先される\r\n    const divStyle = { ...props.showInfo, ...props.blink_setting };\r\n\r\n    if (props.lightingStatus === '1') {\r\n        divStyle.text_color = getTextOrDisplayColor(props.showInfo);\r\n        divStyle.background_color = getHexColor(props.showInfo?.background_color);\r\n    } else if (props.lightingStatus === '2') {\r\n        divStyle.text_color = getLightingTextColor(props);\r\n        divStyle.background_color = getHexColor(\r\n            props.blink_setting?.lighting_background_color\r\n        );\r\n    }\r\n\r\n    if (props.lightingStatus === '3') {\r\n        return (\r\n            <div className={props.className}>\r\n                <BlinkHtmlTag {...divStyle}>\r\n                    <Cell display_text={props.showInfo?.display_text} />\r\n                </BlinkHtmlTag>\r\n            </div>\r\n        );\r\n    } else {\r\n        return (\r\n            <div className={props.className}>\r\n                <StaticHtmlTag {...divStyle}>\r\n                    <Cell display_text={props.showInfo?.display_text} />\r\n                </StaticHtmlTag>\r\n            </div>\r\n        );\r\n    }\r\n};\r\n\r\n// APIのパラメータに違いをカバーして、色の値を返す\r\nfunction getLightingTextColor(props) {\r\n    const blink_setting = props?.blink_setting || props?.lighting_setting;\r\n\r\n    // 下記Bug対応：\r\n    // lighting_status を 3 (点滅…文字色/背景色 ⇔ 点灯時文字色/点灯時背景色で画面に表示) に設定した場合に lighting_text_color (点滅時の文字色) の設定値が反映されず点滅時に必ず白文字となる。\r\n    // (lighting_background_color (点滅時の背景色) は設定値が反映されて、指定色で点滅する。こちらは問題なし。)\r\n    if (blink_setting) {\r\n        if (blink_setting?.lighting_text_color) {\r\n            return getHexColor(blink_setting?.lighting_text_color);\r\n        } else if (blink_setting?.lighting_display_color) {\r\n            return getHexColor(blink_setting?.lighting_display_color);\r\n        }\r\n    } else {\r\n        if (props?.lighting_text_color) {\r\n            return getHexColor(props?.lighting_text_color);\r\n        } else if (props?.lighting_display_color) {\r\n            return getHexColor(props?.lighting_display_color);\r\n        }\r\n\r\n        return '';\r\n    }\r\n}\r\n\r\nconst space = ' ';\r\n\r\n/**\r\n * BaseのObjectとチェックして、色と背景色をTargetObjectにCopy <br>\r\n * TargetObjectにTextがなければ、空文字を入れる\r\n * \r\n * @param {*} targetObj \r\n * @param {*} baseObj \r\n * @returns Target Object\r\n */\r\nexport function checkBlinkInfo(targetObj, baseObj) {\r\n    if (!targetObj) {\r\n        targetObj = {};\r\n    }\r\n    if (targetObj?.display_text === space) {\r\n        return targetObj;\r\n    }\r\n\r\n    if (!targetObj?.display_text) {\r\n        targetObj.display_text = space;\r\n        return targetObj;\r\n    }\r\n\r\n    if (!targetObj?.text_color && baseObj) {\r\n        targetObj.text_color = baseObj.text_color;\r\n    }\r\n    if (!targetObj?.background_color && baseObj) {\r\n        targetObj.background_color = baseObj.background_color;\r\n    }\r\n\r\n    return targetObj;\r\n}\r\n\r\nBlinkBlock.propTypes = propTypes;\r\nexport default BlinkBlock;\r\n"], "mappings": "ofAAA,MAAOA,MAAP,EAAgBC,MAAhB,CAAwBC,SAAxB,CAAmCC,QAAnC,KAAmD,OAAnD,CACA,MAAOC,OAAP,EAAiBC,SAAjB,CAA4BC,GAA5B,KAAuC,mBAAvC,CACA,OAASC,WAAT,CAAsBC,qBAAtB,KAAmD,qBAAnD,CACA,OAASC,UAAT,KAA2B,QAA3B,CACA,MAAOC,KAAP,KAAiB,QAAjB,C,gGAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,WAAU,CAAG,QAAbA,WAAa,CAACC,KAAD,CAAW,uEAC1B,cAAwCT,QAAQ,CAAC,KAAD,CAAhD,wCAAOU,YAAP,eAAqBC,eAArB,eACA,eAA0BX,QAAQ,CAAC,IAAD,CAAlC,yCAAOY,KAAP,eAAcC,QAAd,eACA,GAAMC,UAAS,CAAGhB,MAAM,CAAC,IAAD,CAAxB,CAEAC,SAAS,CAAC,UAAM,sEACZY,eAAe,CAAC,KAAD,CAAf,CAEA;AACA,GAAIG,SAAS,CAACC,OAAd,CAAuB,CACnB;AACA;AACA;AACAC,OAAO,CAACC,GAAR,CAAY,8BAAZ,EACH,CAED,GACI,CAAAR,KAAK,OAAL,EAAAA,KAAK,SAAL,8BAAAA,KAAK,CAAES,aAAP,oEAAsBC,eAAtB,IAA0C,GAA1C,EACA,CAAAV,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBE,WAAtB,EAAoC,CADpC,EAEA,CAAAX,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBG,UAAtB,EAAmC,CAHvC,CAIE,iDACEL,OAAO,CAACC,GAAR,CAAY,kCAAoCL,KAApC,CAA4C,aAA5C,CAA4D,CAAAH,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBG,UAAtB,EAAmC,IAA3G,EACAR,QAAQ,CAAC,CAAAJ,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBG,UAAtB,EAAmC,IAApC,CAAR,CACH,CACJ,CAnBQ,CAmBN,CAACZ,KAAD,SAACA,KAAD,iBAACA,KAAK,CAAES,aAAR,CAnBM,CAAT,CAoBA;AAEAJ,SAAS,CAACC,OAAV,CAAoBT,UAAU,CAAC,UAAM,CACjCK,eAAe,CAAC,IAAD,CAAf,CACAE,QAAQ,CAAC,IAAD,CAAR,CACAG,OAAO,CAACC,GAAR,CAAY,sCAAwCL,KAApD,EACH,CAJ6B,CAI3BA,KAJ2B,CAA9B,CAMAb,SAAS,CAAC,UAAM,CACZ,MAAO,WAAM,CACT;AACA,GAAIe,SAAS,CAACC,OAAd,CAAuB,CACnBC,OAAO,CAACC,GAAR,CAAY,wCAAZ,EACAH,SAAS,CAACC,OAAV,GACH,CACJ,CAND,CAOH,CARQ,CAQN,EARM,CAAT,CAUA,GAAIO,eAAc,CAAGb,KAAH,SAAGA,KAAH,wCAAGA,KAAK,CAAES,aAAV,gDAAG,sBAAsBC,eAA3C,CACA,GAAIT,YAAJ,CAAkB,CACdM,OAAO,CAACC,GAAR,CAAY,iDAAmDK,cAA/D,EACAA,cAAc,CAAG,GAAjB,CACH,CACD,GACI,CAAAb,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBE,WAAtB,GAAqC,CAArC,EACA,CAAAX,KAAK,OAAL,EAAAA,KAAK,SAAL,+BAAAA,KAAK,CAAES,aAAP,sEAAsBG,UAAtB,GAAoC,CAFxC,CAGE,CACEC,cAAc,CAAG,GAAjB,CACH,CACD,mBACI,yBACKC,KAAK,CAACC,OAAN,CAAcf,KAAK,CAACgB,KAApB,GACGhB,KAAK,CAACgB,KAAN,CAAYC,GAAZ,CAAgB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC7B,mBACI,KAAC,QAAD,gCAEQD,IAFR,MAGI,cAAc,CAAEL,cAHpB,CAII,aAAa,CAAEb,KAAF,SAAEA,KAAF,iBAAEA,KAAK,CAAES,aAJ1B,GACSU,KADT,CADJ,CAQH,CATD,CAFR,EADJ,CAeH,CArED,CAuEA,GAAMC,UAAS,CAAG,QAAZA,UAAY,CAACpB,KAAD,QAAWP,UAAX,yLAELG,qBAAqB,CAACI,KAAD,CAFhB,CAGML,WAAW,CAACK,KAAK,CAACqB,gBAAP,CAHjB,CAMLC,oBAAoB,CAACtB,KAAD,CANf,CAOML,WAAW,CAACK,KAAK,CAACuB,yBAAP,CAPjB,GAAlB,CAWA,GAAMC,cAAa,CAAG9B,GAAH,gHACf0B,SADe,CACF,SAACpB,KAAD,QACTA,MAAK,CAACW,WAAN,CAAoB,IADX,EADE,CAAnB,CAKA;AACA,GAAMc,aAAY,CAAGjC,MAAM,CAACkC,GAAV,mLACHF,aADG,CAAlB,CAOA;AACA,GAAMG,cAAa,CAAGnC,MAAM,CAACkC,GAAP,CAAWE,KAAX,CAAiB,SAAC5B,KAAD,QAAY,CAC/C6B,KAAK,CAAE,CACHC,KAAK,CAAElC,qBAAqB,CAACI,KAAD,CADzB,CAEH+B,eAAe,CAAEpC,WAAW,CAACK,KAAK,CAACqB,gBAAP,CAFzB,CADwC,CAAZ,EAAjB,CAAH,4FAAnB,CASA;AACA;AACA;AACA;AACA,GACA,GAAMW,SAAQ,CAAG,QAAXA,SAAW,CAAChC,KAAD,CAAW,CACxB;AACA,GAAMiC,SAAQ,gCAAQjC,KAAK,CAACkC,QAAd,EAA2BlC,KAAK,CAACS,aAAjC,CAAd,CAEA,GAAIT,KAAK,CAACa,cAAN,GAAyB,GAA7B,CAAkC,qBAC9BoB,QAAQ,CAACE,UAAT,CAAsBvC,qBAAqB,CAACI,KAAK,CAACkC,QAAP,CAA3C,CACAD,QAAQ,CAACZ,gBAAT,CAA4B1B,WAAW,kBAACK,KAAK,CAACkC,QAAP,0CAAC,gBAAgBb,gBAAjB,CAAvC,CACH,CAHD,IAGO,IAAIrB,KAAK,CAACa,cAAN,GAAyB,GAA7B,CAAkC,2BACrCoB,QAAQ,CAACE,UAAT,CAAsBb,oBAAoB,CAACtB,KAAD,CAA1C,CACAiC,QAAQ,CAACZ,gBAAT,CAA4B1B,WAAW,wBACnCK,KAAK,CAACS,aAD6B,gDACnC,sBAAqBc,yBADc,CAAvC,CAGH,CAED,GAAIvB,KAAK,CAACa,cAAN,GAAyB,GAA7B,CAAkC,sBAC9B,mBACI,YAAK,SAAS,CAAEb,KAAK,CAACoC,SAAtB,uBACI,KAAC,YAAD,gCAAkBH,QAAlB,4BACI,KAAC,IAAD,EAAM,YAAY,mBAAEjC,KAAK,CAACkC,QAAR,2CAAE,iBAAgBG,YAApC,EADJ,GADJ,EADJ,CAOH,CARD,IAQO,sBACH,mBACI,YAAK,SAAS,CAAErC,KAAK,CAACoC,SAAtB,uBACI,KAAC,aAAD,gCAAmBH,QAAnB,4BACI,KAAC,IAAD,EAAM,YAAY,mBAAEjC,KAAK,CAACkC,QAAR,2CAAE,iBAAgBG,YAApC,EADJ,GADJ,EADJ,CAOH,CACJ,CA/BD,CAiCA;AACA,QAASf,qBAAT,CAA8BtB,KAA9B,CAAqC,CACjC,GAAMS,cAAa,CAAG,CAAAT,KAAK,OAAL,EAAAA,KAAK,SAAL,QAAAA,KAAK,CAAES,aAAP,IAAwBT,KAAxB,SAAwBA,KAAxB,iBAAwBA,KAAK,CAAEsC,gBAA/B,CAAtB,CAEA;AACA;AACA;AACA,GAAI7B,aAAJ,CAAmB,CACf,GAAIA,aAAJ,SAAIA,aAAJ,WAAIA,aAAa,CAAE8B,mBAAnB,CAAwC,CACpC,MAAO5C,YAAW,CAACc,aAAD,SAACA,aAAD,iBAACA,aAAa,CAAE8B,mBAAhB,CAAlB,CACH,CAFD,IAEO,IAAI9B,aAAJ,SAAIA,aAAJ,WAAIA,aAAa,CAAE+B,sBAAnB,CAA2C,CAC9C,MAAO7C,YAAW,CAACc,aAAD,SAACA,aAAD,iBAACA,aAAa,CAAE+B,sBAAhB,CAAlB,CACH,CACJ,CAND,IAMO,CACH,GAAIxC,KAAJ,SAAIA,KAAJ,WAAIA,KAAK,CAAEuC,mBAAX,CAAgC,CAC5B,MAAO5C,YAAW,CAACK,KAAD,SAACA,KAAD,iBAACA,KAAK,CAAEuC,mBAAR,CAAlB,CACH,CAFD,IAEO,IAAIvC,KAAJ,SAAIA,KAAJ,WAAIA,KAAK,CAAEwC,sBAAX,CAAmC,CACtC,MAAO7C,YAAW,CAACK,KAAD,SAACA,KAAD,iBAACA,KAAK,CAAEwC,sBAAR,CAAlB,CACH,CAED,MAAO,EAAP,CACH,CACJ,CAED,GAAMC,MAAK,CAAG,GAAd,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASC,eAAT,CAAwBC,SAAxB,CAAmCC,OAAnC,CAA4C,oDAC/C,GAAI,CAACD,SAAL,CAAgB,CACZA,SAAS,CAAG,EAAZ,CACH,CACD,GAAI,aAAAA,SAAS,QAAT,wCAAWN,YAAX,IAA4BI,KAAhC,CAAuC,CACnC,MAAOE,UAAP,CACH,CAED,GAAI,eAACA,SAAD,gCAAC,YAAWN,YAAZ,CAAJ,CAA8B,CAC1BM,SAAS,CAACN,YAAV,CAAyBI,KAAzB,CACA,MAAOE,UAAP,CACH,CAED,GAAI,eAACA,SAAD,gCAAC,YAAWR,UAAZ,GAA0BS,OAA9B,CAAuC,CACnCD,SAAS,CAACR,UAAV,CAAuBS,OAAO,CAACT,UAA/B,CACH,CACD,GAAI,eAACQ,SAAD,gCAAC,YAAWtB,gBAAZ,GAAgCuB,OAApC,CAA6C,CACzCD,SAAS,CAACtB,gBAAV,CAA6BuB,OAAO,CAACvB,gBAArC,CACH,CAED,MAAOsB,UAAP,CACH,CAGD,cAAe5C,WAAf"}, "metadata": {}, "sourceType": "module"}