{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\n\n\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\nmodule.exports = hashClear;", "map": {"version": 3, "names": ["nativeCreate", "require", "hashClear", "__data__", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_hashClear.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\nmodule.exports = hashClear;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,SAAT,GAAqB;EACnB,KAAKC,QAAL,GAAgBH,YAAY,GAAGA,YAAY,CAAC,IAAD,CAAf,GAAwB,EAApD;EACA,KAAKI,IAAL,GAAY,CAAZ;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBJ,SAAjB"}, "metadata": {}, "sourceType": "script"}