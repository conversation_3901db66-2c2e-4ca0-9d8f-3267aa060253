{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n    label: 0,\n    sent: function sent() {\n      if (t[0] & 1) throw t[1];\n      return t[1];\n    },\n    trys: [],\n    ops: []\n  },\n      f,\n      y,\n      t,\n      g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n\n          case 7:\n            op = _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n\n              _.ops.push(op);\n\n              break;\n            }\n\n            if (t[2]) _.ops.pop();\n\n            _.trys.pop();\n\n            continue;\n        }\n\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n/* eslint-disable @typescript-eslint/no-parameter-properties */\n\n\nimport { isFunction } from '../../utils';\n\nvar Fetch =\n/** @class */\nfunction () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    } // @ts-ignore\n\n\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n\n  Fetch.prototype.runAsync = function () {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n\n    var params = [];\n\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _l, _m, stopNow, _o, returnNow, state, servicePromise, res, error_1;\n\n      var _p;\n\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _l = this.runPluginHandler('onBefore', params), _m = _l.stopNow, stopNow = _m === void 0 ? false : _m, _o = _l.returnNow, returnNow = _o === void 0 ? false : _o, state = __rest(_l, [\"stopNow\", \"returnNow\"]); // stop request\n\n            if (stopNow) {\n              return [2\n              /*return*/\n              , new Promise(function () {})];\n            }\n\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state)); // return now\n\n            if (returnNow) {\n              return [2\n              /*return*/\n              , Promise.resolve(state.data)];\n            }\n\n            (_b = (_a = this.options).onBefore) === null || _b === void 0 ? void 0 : _b.call(_a, params);\n            _q.label = 1;\n\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n\n            if (!servicePromise) {\n              servicePromise = (_p = this.serviceRef).current.apply(_p, __spreadArray([], __read(params), false));\n            }\n\n            return [4\n            /*yield*/\n            , servicePromise];\n\n          case 2:\n            res = _q.sent();\n\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2\n              /*return*/\n              , new Promise(function () {})];\n            } // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n\n\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_d = (_c = this.options).onSuccess) === null || _d === void 0 ? void 0 : _d.call(_c, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_f = (_e = this.options).onFinally) === null || _f === void 0 ? void 0 : _f.call(_e, params, res, undefined);\n\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n\n            return [2\n            /*return*/\n            , res];\n\n          case 3:\n            error_1 = _q.sent();\n\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2\n              /*return*/\n              , new Promise(function () {})];\n            }\n\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_h = (_g = this.options).onError) === null || _h === void 0 ? void 0 : _h.call(_g, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, undefined, error_1);\n\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n\n            throw error_1;\n\n          case 4:\n            return [2\n            /*return*/\n            ];\n        }\n      });\n    });\n  };\n\n  Fetch.prototype.run = function () {\n    var _this = this;\n\n    var params = [];\n\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n\n    this.runAsync.apply(this, __spreadArray([], __read(params), false))[\"catch\"](function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n\n  return Fetch;\n}();\n\nexport default Fetch;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "__generator", "body", "_", "label", "sent", "trys", "ops", "f", "y", "g", "verb", "Symbol", "iterator", "v", "op", "TypeError", "pop", "push", "__rest", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__read", "o", "m", "r", "ar", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "isFunction", "<PERSON>tch", "serviceRef", "options", "subscribe", "initState", "count", "state", "loading", "params", "undefined", "data", "manual", "setState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "rest", "_i", "pluginImpls", "map", "_a", "filter", "Boolean", "runAsync", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "currentCount", "_l", "_m", "stopNow", "_o", "returnNow", "servicePromise", "res", "error_1", "_p", "_q", "onBefore", "current", "onSuccess", "onFinally", "onError", "run", "_this", "console", "cancel", "refresh", "refreshAsync", "mutate", "targetData"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/Fetch.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function sent() {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) {\n      try {\n        if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n        if (y = 0, t) op = [op[0] & 2, t.value];\n        switch (op[0]) {\n          case 0:\n          case 1:\n            t = op;\n            break;\n          case 4:\n            _.label++;\n            return {\n              value: op[1],\n              done: false\n            };\n          case 5:\n            _.label++;\n            y = op[1];\n            op = [0];\n            continue;\n          case 7:\n            op = _.ops.pop();\n            _.trys.pop();\n            continue;\n          default:\n            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n              _ = 0;\n              continue;\n            }\n            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n              _.label = op[1];\n              break;\n            }\n            if (op[0] === 6 && _.label < t[1]) {\n              _.label = t[1];\n              t = op;\n              break;\n            }\n            if (t && _.label < t[2]) {\n              _.label = t[2];\n              _.ops.push(op);\n              break;\n            }\n            if (t[2]) _.ops.pop();\n            _.trys.pop();\n            continue;\n        }\n        op = body.call(thisArg, _);\n      } catch (e) {\n        op = [6, e];\n        y = 0;\n      } finally {\n        f = t = 0;\n      }\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n/* eslint-disable @typescript-eslint/no-parameter-properties */\nimport { isFunction } from '../../utils';\nvar Fetch = /** @class */function () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    // @ts-ignore\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n  Fetch.prototype.runAsync = function () {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _l, _m, stopNow, _o, returnNow, state, servicePromise, res, error_1;\n      var _p;\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _l = this.runPluginHandler('onBefore', params), _m = _l.stopNow, stopNow = _m === void 0 ? false : _m, _o = _l.returnNow, returnNow = _o === void 0 ? false : _o, state = __rest(_l, [\"stopNow\", \"returnNow\"]);\n            // stop request\n            if (stopNow) {\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state));\n            // return now\n            if (returnNow) {\n              return [2 /*return*/, Promise.resolve(state.data)];\n            }\n            (_b = (_a = this.options).onBefore) === null || _b === void 0 ? void 0 : _b.call(_a, params);\n            _q.label = 1;\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n            if (!servicePromise) {\n              servicePromise = (_p = this.serviceRef).current.apply(_p, __spreadArray([], __read(params), false));\n            }\n            return [4 /*yield*/, servicePromise];\n          case 2:\n            res = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_d = (_c = this.options).onSuccess) === null || _d === void 0 ? void 0 : _d.call(_c, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_f = (_e = this.options).onFinally) === null || _f === void 0 ? void 0 : _f.call(_e, params, res, undefined);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n            return [2 /*return*/, res];\n          case 3:\n            error_1 = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_h = (_g = this.options).onError) === null || _h === void 0 ? void 0 : _h.call(_g, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, undefined, error_1);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n            throw error_1;\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n\n  Fetch.prototype.run = function () {\n    var _this = this;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    this.runAsync.apply(this, __spreadArray([], __read(params), false))[\"catch\"](function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n  return Fetch;\n}();\nexport default Fetch;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,SAAS,GAAG,QAAQ,KAAKA,SAAb,IAA0B,UAAUC,OAAV,EAAmBC,UAAnB,EAA+BC,CAA/B,EAAkCC,SAAlC,EAA6C;EACrF,SAASC,KAAT,CAAeC,KAAf,EAAsB;IACpB,OAAOA,KAAK,YAAYH,CAAjB,GAAqBG,KAArB,GAA6B,IAAIH,CAAJ,CAAM,UAAUI,OAAV,EAAmB;MAC3DA,OAAO,CAACD,KAAD,CAAP;IACD,CAFmC,CAApC;EAGD;;EACD,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAT,CAAN,EAAyB,UAAUD,OAAV,EAAmBE,MAAnB,EAA2B;IACzD,SAASC,SAAT,CAAmBJ,KAAnB,EAA0B;MACxB,IAAI;QACFK,IAAI,CAACP,SAAS,CAACQ,IAAV,CAAeN,KAAf,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASC,QAAT,CAAkBR,KAAlB,EAAyB;MACvB,IAAI;QACFK,IAAI,CAACP,SAAS,CAAC,OAAD,CAAT,CAAmBE,KAAnB,CAAD,CAAJ;MACD,CAFD,CAEE,OAAOO,CAAP,EAAU;QACVJ,MAAM,CAACI,CAAD,CAAN;MACD;IACF;;IACD,SAASF,IAAT,CAAcI,MAAd,EAAsB;MACpBA,MAAM,CAACC,IAAP,GAAcT,OAAO,CAACQ,MAAM,CAACT,KAAR,CAArB,GAAsCD,KAAK,CAACU,MAAM,CAACT,KAAR,CAAL,CAAoBW,IAApB,CAAyBP,SAAzB,EAAoCI,QAApC,CAAtC;IACD;;IACDH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACL,KAAV,CAAgBE,OAAhB,EAAyBC,UAAU,IAAI,EAAvC,CAAb,EAAyDU,IAAzD,EAAD,CAAJ;EACD,CAnBM,CAAP;AAoBD,CA1BD;;AA2BA,IAAIM,WAAW,GAAG,QAAQ,KAAKA,WAAb,IAA4B,UAAUjB,OAAV,EAAmBkB,IAAnB,EAAyB;EACrE,IAAIC,CAAC,GAAG;IACJC,KAAK,EAAE,CADH;IAEJC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIjC,CAAC,CAAC,CAAD,CAAD,GAAO,CAAX,EAAc,MAAMA,CAAC,CAAC,CAAD,CAAP;MACd,OAAOA,CAAC,CAAC,CAAD,CAAR;IACD,CALG;IAMJkC,IAAI,EAAE,EANF;IAOJC,GAAG,EAAE;EAPD,CAAR;EAAA,IASEC,CATF;EAAA,IAUEC,CAVF;EAAA,IAWErC,CAXF;EAAA,IAYEsC,CAZF;EAaA,OAAOA,CAAC,GAAG;IACTf,IAAI,EAAEgB,IAAI,CAAC,CAAD,CADD;IAET,SAASA,IAAI,CAAC,CAAD,CAFJ;IAGT,UAAUA,IAAI,CAAC,CAAD;EAHL,CAAJ,EAIJ,OAAOC,MAAP,KAAkB,UAAlB,KAAiCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAD,GAAqB,YAAY;IACnE,OAAO,IAAP;EACD,CAFE,CAJI,EAMHH,CANJ;;EAOA,SAASC,IAAT,CAAcpC,CAAd,EAAiB;IACf,OAAO,UAAUuC,CAAV,EAAa;MAClB,OAAOpB,IAAI,CAAC,CAACnB,CAAD,EAAIuC,CAAJ,CAAD,CAAX;IACD,CAFD;EAGD;;EACD,SAASpB,IAAT,CAAcqB,EAAd,EAAkB;IAChB,IAAIP,CAAJ,EAAO,MAAM,IAAIQ,SAAJ,CAAc,iCAAd,CAAN;;IACP,OAAOb,CAAP,EAAU;MACR,IAAI;QACF,IAAIK,CAAC,GAAG,CAAJ,EAAOC,CAAC,KAAKrC,CAAC,GAAG2C,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAR,GAAYN,CAAC,CAAC,QAAD,CAAb,GAA0BM,EAAE,CAAC,CAAD,CAAF,GAAQN,CAAC,CAAC,OAAD,CAAD,KAAe,CAACrC,CAAC,GAAGqC,CAAC,CAAC,QAAD,CAAN,KAAqBrC,CAAC,CAACS,IAAF,CAAO4B,CAAP,CAArB,EAAgC,CAA/C,CAAR,GAA4DA,CAAC,CAACd,IAAjG,CAAD,IAA2G,CAAC,CAACvB,CAAC,GAAGA,CAAC,CAACS,IAAF,CAAO4B,CAAP,EAAUM,EAAE,CAAC,CAAD,CAAZ,CAAL,EAAuBhB,IAA9I,EAAoJ,OAAO3B,CAAP;QACpJ,IAAIqC,CAAC,GAAG,CAAJ,EAAOrC,CAAX,EAAc2C,EAAE,GAAG,CAACA,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAT,EAAY3C,CAAC,CAACiB,KAAd,CAAL;;QACd,QAAQ0B,EAAE,CAAC,CAAD,CAAV;UACE,KAAK,CAAL;UACA,KAAK,CAAL;YACE3C,CAAC,GAAG2C,EAAJ;YACA;;UACF,KAAK,CAAL;YACEZ,CAAC,CAACC,KAAF;YACA,OAAO;cACLf,KAAK,EAAE0B,EAAE,CAAC,CAAD,CADJ;cAELhB,IAAI,EAAE;YAFD,CAAP;;UAIF,KAAK,CAAL;YACEI,CAAC,CAACC,KAAF;YACAK,CAAC,GAAGM,EAAE,CAAC,CAAD,CAAN;YACAA,EAAE,GAAG,CAAC,CAAD,CAAL;YACA;;UACF,KAAK,CAAL;YACEA,EAAE,GAAGZ,CAAC,CAACI,GAAF,CAAMU,GAAN,EAAL;;YACAd,CAAC,CAACG,IAAF,CAAOW,GAAP;;YACA;;UACF;YACE,IAAI,EAAE7C,CAAC,GAAG+B,CAAC,CAACG,IAAN,EAAYlC,CAAC,GAAGA,CAAC,CAACK,MAAF,GAAW,CAAX,IAAgBL,CAAC,CAACA,CAAC,CAACK,MAAF,GAAW,CAAZ,CAAnC,MAAuDsC,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAeA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAhF,CAAJ,EAAwF;cACtFZ,CAAC,GAAG,CAAJ;cACA;YACD;;YACD,IAAIY,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,KAAgB,CAAC3C,CAAD,IAAM2C,EAAE,CAAC,CAAD,CAAF,GAAQ3C,CAAC,CAAC,CAAD,CAAT,IAAgB2C,EAAE,CAAC,CAAD,CAAF,GAAQ3C,CAAC,CAAC,CAAD,CAA/C,CAAJ,EAAyD;cACvD+B,CAAC,CAACC,KAAF,GAAUW,EAAE,CAAC,CAAD,CAAZ;cACA;YACD;;YACD,IAAIA,EAAE,CAAC,CAAD,CAAF,KAAU,CAAV,IAAeZ,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAA9B,EAAmC;cACjC+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAAX;cACAA,CAAC,GAAG2C,EAAJ;cACA;YACD;;YACD,IAAI3C,CAAC,IAAI+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAApB,EAAyB;cACvB+B,CAAC,CAACC,KAAF,GAAUhC,CAAC,CAAC,CAAD,CAAX;;cACA+B,CAAC,CAACI,GAAF,CAAMW,IAAN,CAAWH,EAAX;;cACA;YACD;;YACD,IAAI3C,CAAC,CAAC,CAAD,CAAL,EAAU+B,CAAC,CAACI,GAAF,CAAMU,GAAN;;YACVd,CAAC,CAACG,IAAF,CAAOW,GAAP;;YACA;QAzCJ;;QA2CAF,EAAE,GAAGb,IAAI,CAACrB,IAAL,CAAUG,OAAV,EAAmBmB,CAAnB,CAAL;MACD,CA/CD,CA+CE,OAAOP,CAAP,EAAU;QACVmB,EAAE,GAAG,CAAC,CAAD,EAAInB,CAAJ,CAAL;QACAa,CAAC,GAAG,CAAJ;MACD,CAlDD,SAkDU;QACRD,CAAC,GAAGpC,CAAC,GAAG,CAAR;MACD;IACF;;IACD,IAAI2C,EAAE,CAAC,CAAD,CAAF,GAAQ,CAAZ,EAAe,MAAMA,EAAE,CAAC,CAAD,CAAR;IACf,OAAO;MACL1B,KAAK,EAAE0B,EAAE,CAAC,CAAD,CAAF,GAAQA,EAAE,CAAC,CAAD,CAAV,GAAgB,KAAK,CADvB;MAELhB,IAAI,EAAE;IAFD,CAAP;EAID;AACF,CAzFD;;AA0FA,IAAIoB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAU9C,CAAV,EAAauB,CAAb,EAAgB;EAClD,IAAIxB,CAAC,GAAG,EAAR;;EACA,KAAK,IAAIM,CAAT,IAAcL,CAAd,EAAiB;IACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,KAA8CkB,CAAC,CAACwB,OAAF,CAAU1C,CAAV,IAAe,CAAjE,EAAoEN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;EACrE;;EACD,IAAIL,CAAC,IAAI,IAAL,IAAa,OAAOH,MAAM,CAACmD,qBAAd,KAAwC,UAAzD,EAAqE,KAAK,IAAI/C,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAGR,MAAM,CAACmD,qBAAP,CAA6BhD,CAA7B,CAApB,EAAqDC,CAAC,GAAGI,CAAC,CAACD,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IAC3I,IAAIsB,CAAC,CAACwB,OAAF,CAAU1C,CAAC,CAACJ,CAAD,CAAX,IAAkB,CAAlB,IAAuBJ,MAAM,CAACS,SAAP,CAAiB2C,oBAAjB,CAAsCzC,IAAtC,CAA2CR,CAA3C,EAA8CK,CAAC,CAACJ,CAAD,CAA/C,CAA3B,EAAgFF,CAAC,CAACM,CAAC,CAACJ,CAAD,CAAF,CAAD,GAAUD,CAAC,CAACK,CAAC,CAACJ,CAAD,CAAF,CAAX;EACjF;EACD,OAAOF,CAAP;AACD,CATD;;AAUA,IAAImD,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAajD,CAAb,EAAgB;EAClD,IAAIkD,CAAC,GAAG,OAAOb,MAAP,KAAkB,UAAlB,IAAgCY,CAAC,CAACZ,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACY,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIlD,CAAC,GAAGmD,CAAC,CAAC5C,IAAF,CAAO2C,CAAP,CAAR;EAAA,IACEE,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGE/B,CAHF;;EAIA,IAAI;IACF,OAAO,CAACrB,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACmD,CAAC,GAAGpD,CAAC,CAACqB,IAAF,EAAL,EAAeI,IAApD,EAA0D;MACxD4B,EAAE,CAACT,IAAH,CAAQQ,CAAC,CAACrC,KAAV;IACD;EACF,CAJD,CAIE,OAAOuC,KAAP,EAAc;IACdhC,CAAC,GAAG;MACFgC,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIF,CAAC,IAAI,CAACA,CAAC,CAAC3B,IAAR,KAAiB0B,CAAC,GAAGnD,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCmD,CAAC,CAAC5C,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIsB,CAAJ,EAAO,MAAMA,CAAC,CAACgC,KAAR;IACR;EACF;;EACD,OAAOD,EAAP;AACD,CAvBD;;AAwBA,IAAIE,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIxD,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIH,CAAC,GAAG,CAAR,EAAW2D,CAAC,GAAGF,IAAI,CAACtD,MAApB,EAA4BkD,EAAjC,EAAqCrD,CAAC,GAAG2D,CAAzC,EAA4C3D,CAAC,EAA7C,EAAiD;IACnF,IAAIqD,EAAE,IAAI,EAAErD,CAAC,IAAIyD,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACJ,EAAL,EAASA,EAAE,GAAGO,KAAK,CAACvD,SAAN,CAAgBwD,KAAhB,CAAsBtD,IAAtB,CAA2BkD,IAA3B,EAAiC,CAAjC,EAAoCzD,CAApC,CAAL;MACTqD,EAAE,CAACrD,CAAD,CAAF,GAAQyD,IAAI,CAACzD,CAAD,CAAZ;IACD;EACF;EACD,OAAOwD,EAAE,CAACM,MAAH,CAAUT,EAAE,IAAIO,KAAK,CAACvD,SAAN,CAAgBwD,KAAhB,CAAsBtD,IAAtB,CAA2BkD,IAA3B,CAAhB,CAAP;AACD,CARD;AASA;;;AACA,SAASM,UAAT,QAA2B,aAA3B;;AACA,IAAIC,KAAK;AAAG;AAAa,YAAY;EACnC,SAASA,KAAT,CAAeC,UAAf,EAA2BC,OAA3B,EAAoCC,SAApC,EAA+CC,SAA/C,EAA0D;IACxD,IAAIA,SAAS,KAAK,KAAK,CAAvB,EAA0B;MACxBA,SAAS,GAAG,EAAZ;IACD;;IACD,KAAKH,UAAL,GAAkBA,UAAlB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAKC,KAAL,GAAa;MACXC,OAAO,EAAE,KADE;MAEXC,MAAM,EAAEC,SAFG;MAGXC,IAAI,EAAED,SAHK;MAIXnB,KAAK,EAAEmB;IAJI,CAAb;IAMA,KAAKH,KAAL,GAAa3E,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAK,KAAK2E,KAAV,CAAT,EAA2B;MACvDC,OAAO,EAAE,CAACL,OAAO,CAACS;IADqC,CAA3B,CAAT,EAEjBP,SAFiB,CAArB;EAGD;;EACDJ,KAAK,CAAC3D,SAAN,CAAgBuE,QAAhB,GAA2B,UAAU7E,CAAV,EAAa;IACtC,IAAIA,CAAC,KAAK,KAAK,CAAf,EAAkB;MAChBA,CAAC,GAAG,EAAJ;IACD;;IACD,KAAKuE,KAAL,GAAa3E,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAK,KAAK2E,KAAV,CAAT,EAA2BvE,CAA3B,CAArB;IACA,KAAKoE,SAAL;EACD,CAND;;EAOAH,KAAK,CAAC3D,SAAN,CAAgBwE,gBAAhB,GAAmC,UAAUC,KAAV,EAAiB;IAClD,IAAIC,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG9E,SAAS,CAACC,MAAhC,EAAwC6E,EAAE,EAA1C,EAA8C;MAC5CD,IAAI,CAACC,EAAE,GAAG,CAAN,CAAJ,GAAe9E,SAAS,CAAC8E,EAAD,CAAxB;IACD,CAJiD,CAKlD;;;IACA,IAAI5B,CAAC,GAAG,KAAK6B,WAAL,CAAiBC,GAAjB,CAAqB,UAAUlF,CAAV,EAAa;MACxC,IAAImF,EAAJ;;MACA,OAAO,CAACA,EAAE,GAAGnF,CAAC,CAAC8E,KAAD,CAAP,MAAoB,IAApB,IAA4BK,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC5E,IAAH,CAAQC,KAAR,CAAc2E,EAAd,EAAkB5B,aAAa,CAAC,CAACvD,CAAD,CAAD,EAAMiD,MAAM,CAAC8B,IAAD,CAAZ,EAAoB,KAApB,CAA/B,CAA5D;IACD,CAHO,EAGLK,MAHK,CAGEC,OAHF,CAAR;IAIA,OAAOzF,MAAM,CAACC,MAAP,CAAcW,KAAd,CAAoBZ,MAApB,EAA4B2D,aAAa,CAAC,CAAC,EAAD,CAAD,EAAON,MAAM,CAACG,CAAD,CAAb,EAAkB,KAAlB,CAAzC,CAAP;EACD,CAXD;;EAYAY,KAAK,CAAC3D,SAAN,CAAgBiF,QAAhB,GAA2B,YAAY;IACrC,IAAIH,EAAJ,EAAQI,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC;;IACA,IAAIvB,MAAM,GAAG,EAAb;;IACA,KAAK,IAAIQ,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG9E,SAAS,CAACC,MAAhC,EAAwC6E,EAAE,EAA1C,EAA8C;MAC5CR,MAAM,CAACQ,EAAD,CAAN,GAAa9E,SAAS,CAAC8E,EAAD,CAAtB;IACD;;IACD,OAAOvE,SAAS,CAAC,IAAD,EAAO,KAAK,CAAZ,EAAe,KAAK,CAApB,EAAuB,YAAY;MACjD,IAAIuF,YAAJ,EAAkBC,EAAlB,EAAsBC,EAAtB,EAA0BC,OAA1B,EAAmCC,EAAnC,EAAuCC,SAAvC,EAAkD/B,KAAlD,EAAyDgC,cAAzD,EAAyEC,GAAzE,EAA8EC,OAA9E;;MACA,IAAIC,EAAJ;;MACA,OAAO9E,WAAW,CAAC,IAAD,EAAO,UAAU+E,EAAV,EAAc;QACrC,QAAQA,EAAE,CAAC5E,KAAX;UACE,KAAK,CAAL;YACE,KAAKuC,KAAL,IAAc,CAAd;YACA2B,YAAY,GAAG,KAAK3B,KAApB;YACA4B,EAAE,GAAG,KAAKpB,gBAAL,CAAsB,UAAtB,EAAkCL,MAAlC,CAAL,EAAgD0B,EAAE,GAAGD,EAAE,CAACE,OAAxD,EAAiEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EAAnG,EAAuGE,EAAE,GAAGH,EAAE,CAACI,SAA/G,EAA0HA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EAA9J,EAAkK9B,KAAK,GAAGzB,MAAM,CAACoD,EAAD,EAAK,CAAC,SAAD,EAAY,WAAZ,CAAL,CAAhL,CAHF,CAIE;;YACA,IAAIE,OAAJ,EAAa;cACX,OAAO,CAAC;cAAE;cAAH,EAAe,IAAIlF,OAAJ,CAAY,YAAY,CAAE,CAA1B,CAAf,CAAP;YACD;;YACD,KAAK2D,QAAL,CAAcjF,QAAQ,CAAC;cACrB4E,OAAO,EAAE,IADY;cAErBC,MAAM,EAAEA;YAFa,CAAD,EAGnBF,KAHmB,CAAtB,EARF,CAYE;;YACA,IAAI+B,SAAJ,EAAe;cACb,OAAO,CAAC;cAAE;cAAH,EAAepF,OAAO,CAACD,OAAR,CAAgBsD,KAAK,CAACI,IAAtB,CAAf,CAAP;YACD;;YACD,CAACa,EAAE,GAAG,CAACJ,EAAE,GAAG,KAAKjB,OAAX,EAAoByC,QAA1B,MAAwC,IAAxC,IAAgDpB,EAAE,KAAK,KAAK,CAA5D,GAAgE,KAAK,CAArE,GAAyEA,EAAE,CAAChF,IAAH,CAAQ4E,EAAR,EAAYX,MAAZ,CAAzE;YACAkC,EAAE,CAAC5E,KAAH,GAAW,CAAX;;UACF,KAAK,CAAL;YACE4E,EAAE,CAAC1E,IAAH,CAAQY,IAAR,CAAa,CAAC,CAAD,EAAI,CAAJ,GAAQ,CAAR,CAAb;;YACA0D,cAAc,GAAG,KAAKzB,gBAAL,CAAsB,WAAtB,EAAmC,KAAKZ,UAAL,CAAgB2C,OAAnD,EAA4DpC,MAA5D,EAAoE8B,cAArF;;YACA,IAAI,CAACA,cAAL,EAAqB;cACnBA,cAAc,GAAG,CAACG,EAAE,GAAG,KAAKxC,UAAX,EAAuB2C,OAAvB,CAA+BpG,KAA/B,CAAqCiG,EAArC,EAAyClD,aAAa,CAAC,EAAD,EAAKN,MAAM,CAACuB,MAAD,CAAX,EAAqB,KAArB,CAAtD,CAAjB;YACD;;YACD,OAAO,CAAC;YAAE;YAAH,EAAc8B,cAAd,CAAP;;UACF,KAAK,CAAL;YACEC,GAAG,GAAGG,EAAE,CAAC3E,IAAH,EAAN;;YACA,IAAIiE,YAAY,KAAK,KAAK3B,KAA1B,EAAiC;cAC/B;cACA,OAAO,CAAC;cAAE;cAAH,EAAe,IAAIpD,OAAJ,CAAY,YAAY,CAAE,CAA1B,CAAf,CAAP;YACD,CALH,CAME;;;YACA,KAAK2D,QAAL,CAAc;cACZF,IAAI,EAAE6B,GADM;cAEZjD,KAAK,EAAEmB,SAFK;cAGZF,OAAO,EAAE;YAHG,CAAd;YAKA,CAACkB,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKtB,OAAX,EAAoB2C,SAA1B,MAAyC,IAAzC,IAAiDpB,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAAClF,IAAH,CAAQiF,EAAR,EAAYe,GAAZ,EAAiB/B,MAAjB,CAA1E;YACA,KAAKK,gBAAL,CAAsB,WAAtB,EAAmC0B,GAAnC,EAAwC/B,MAAxC;YACA,CAACmB,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKxB,OAAX,EAAoB4C,SAA1B,MAAyC,IAAzC,IAAiDnB,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACpF,IAAH,CAAQmF,EAAR,EAAYlB,MAAZ,EAAoB+B,GAApB,EAAyB9B,SAAzB,CAA1E;;YACA,IAAIuB,YAAY,KAAK,KAAK3B,KAA1B,EAAiC;cAC/B,KAAKQ,gBAAL,CAAsB,WAAtB,EAAmCL,MAAnC,EAA2C+B,GAA3C,EAAgD9B,SAAhD;YACD;;YACD,OAAO,CAAC;YAAE;YAAH,EAAe8B,GAAf,CAAP;;UACF,KAAK,CAAL;YACEC,OAAO,GAAGE,EAAE,CAAC3E,IAAH,EAAV;;YACA,IAAIiE,YAAY,KAAK,KAAK3B,KAA1B,EAAiC;cAC/B;cACA,OAAO,CAAC;cAAE;cAAH,EAAe,IAAIpD,OAAJ,CAAY,YAAY,CAAE,CAA1B,CAAf,CAAP;YACD;;YACD,KAAK2D,QAAL,CAAc;cACZtB,KAAK,EAAEkD,OADK;cAEZjC,OAAO,EAAE;YAFG,CAAd;YAIA,CAACsB,EAAE,GAAG,CAACD,EAAE,GAAG,KAAK1B,OAAX,EAAoB6C,OAA1B,MAAuC,IAAvC,IAA+ClB,EAAE,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,EAAE,CAACtF,IAAH,CAAQqF,EAAR,EAAYY,OAAZ,EAAqBhC,MAArB,CAAxE;YACA,KAAKK,gBAAL,CAAsB,SAAtB,EAAiC2B,OAAjC,EAA0ChC,MAA1C;YACA,CAACuB,EAAE,GAAG,CAACD,EAAE,GAAG,KAAK5B,OAAX,EAAoB4C,SAA1B,MAAyC,IAAzC,IAAiDf,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACxF,IAAH,CAAQuF,EAAR,EAAYtB,MAAZ,EAAoBC,SAApB,EAA+B+B,OAA/B,CAA1E;;YACA,IAAIR,YAAY,KAAK,KAAK3B,KAA1B,EAAiC;cAC/B,KAAKQ,gBAAL,CAAsB,WAAtB,EAAmCL,MAAnC,EAA2CC,SAA3C,EAAsD+B,OAAtD;YACD;;YACD,MAAMA,OAAN;;UACF,KAAK,CAAL;YACE,OAAO,CAAC;YAAE;YAAH,CAAP;QA/DJ;MAiED,CAlEiB,CAAlB;IAmED,CAtEe,CAAhB;EAuED,CA7ED;;EA+EAxC,KAAK,CAAC3D,SAAN,CAAgB2G,GAAhB,GAAsB,YAAY;IAChC,IAAIC,KAAK,GAAG,IAAZ;;IACA,IAAIzC,MAAM,GAAG,EAAb;;IACA,KAAK,IAAIQ,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG9E,SAAS,CAACC,MAAhC,EAAwC6E,EAAE,EAA1C,EAA8C;MAC5CR,MAAM,CAACQ,EAAD,CAAN,GAAa9E,SAAS,CAAC8E,EAAD,CAAtB;IACD;;IACD,KAAKM,QAAL,CAAc9E,KAAd,CAAoB,IAApB,EAA0B+C,aAAa,CAAC,EAAD,EAAKN,MAAM,CAACuB,MAAD,CAAX,EAAqB,KAArB,CAAvC,EAAoE,OAApE,EAA6E,UAAUlB,KAAV,EAAiB;MAC5F,IAAI,CAAC2D,KAAK,CAAC/C,OAAN,CAAc6C,OAAnB,EAA4B;QAC1BG,OAAO,CAAC5D,KAAR,CAAcA,KAAd;MACD;IACF,CAJD;EAKD,CAXD;;EAYAU,KAAK,CAAC3D,SAAN,CAAgB8G,MAAhB,GAAyB,YAAY;IACnC,KAAK9C,KAAL,IAAc,CAAd;IACA,KAAKO,QAAL,CAAc;MACZL,OAAO,EAAE;IADG,CAAd;IAGA,KAAKM,gBAAL,CAAsB,UAAtB;EACD,CAND;;EAOAb,KAAK,CAAC3D,SAAN,CAAgB+G,OAAhB,GAA0B,YAAY;IACpC;IACA,KAAKJ,GAAL,CAASxG,KAAT,CAAe,IAAf,EAAqB+C,aAAa,CAAC,EAAD,EAAKN,MAAM,CAAC,KAAKqB,KAAL,CAAWE,MAAX,IAAqB,EAAtB,CAAX,EAAsC,KAAtC,CAAlC;EACD,CAHD;;EAIAR,KAAK,CAAC3D,SAAN,CAAgBgH,YAAhB,GAA+B,YAAY;IACzC;IACA,OAAO,KAAK/B,QAAL,CAAc9E,KAAd,CAAoB,IAApB,EAA0B+C,aAAa,CAAC,EAAD,EAAKN,MAAM,CAAC,KAAKqB,KAAL,CAAWE,MAAX,IAAqB,EAAtB,CAAX,EAAsC,KAAtC,CAAvC,CAAP;EACD,CAHD;;EAIAR,KAAK,CAAC3D,SAAN,CAAgBiH,MAAhB,GAAyB,UAAU5C,IAAV,EAAgB;IACvC,IAAI6C,UAAU,GAAGxD,UAAU,CAACW,IAAD,CAAV,GAAmBA,IAAI,CAAC,KAAKJ,KAAL,CAAWI,IAAZ,CAAvB,GAA2CA,IAA5D;IACA,KAAKG,gBAAL,CAAsB,UAAtB,EAAkC0C,UAAlC;IACA,KAAK3C,QAAL,CAAc;MACZF,IAAI,EAAE6C;IADM,CAAd;EAGD,CAND;;EAOA,OAAOvD,KAAP;AACD,CAzJwB,EAAzB;;AA0JA,eAAeA,KAAf"}, "metadata": {}, "sourceType": "module"}