{"ast": null, "code": "var arrayLikeKeys = require('./_arrayLikeKeys'),\n    baseKeys = require('./_baseKeys'),\n    isArrayLike = require('./isArrayLike');\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\n\n\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\nmodule.exports = keys;", "map": {"version": 3, "names": ["arrayLikeKeys", "require", "baseKeys", "isArrayLike", "keys", "object", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/keys.js"], "sourcesContent": ["var arrayLikeKeys = require('./_arrayLikeKeys'),\n    baseKeys = require('./_baseKeys'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\nmodule.exports = keys;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAD,CAA3B;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,aAAD,CADtB;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,eAAD,CAFzB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,IAAT,CAAcC,MAAd,EAAsB;EACpB,OAAOF,WAAW,CAACE,MAAD,CAAX,GAAsBL,aAAa,CAACK,MAAD,CAAnC,GAA8CH,QAAQ,CAACG,MAAD,CAA7D;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBH,IAAjB"}, "metadata": {}, "sourceType": "script"}