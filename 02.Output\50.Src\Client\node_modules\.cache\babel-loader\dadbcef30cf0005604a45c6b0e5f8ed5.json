{"ast": null, "code": "var root = require('./_root');\n/** Used to detect overreaching core-js shims. */\n\n\nvar coreJsData = root['__core-js_shared__'];\nmodule.exports = coreJsData;", "map": {"version": 3, "names": ["root", "require", "coreJsData", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_coreJsData.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nmodule.exports = coreJsData;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAD,CAAlB;AAEA;;;AACA,IAAIC,UAAU,GAAGF,IAAI,CAAC,oBAAD,CAArB;AAEAG,MAAM,CAACC,OAAP,GAAiBF,UAAjB"}, "metadata": {}, "sourceType": "script"}