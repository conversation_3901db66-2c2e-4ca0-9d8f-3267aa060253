{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\nimport adapters from '../adapters/index.js';\nvar DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n/**\n * If the browser has an XMLHttpRequest object, use the XHR adapter, otherwise use the HTTP\n * adapter\n *\n * @returns {Function}\n */\n\nfunction getDefaultAdapter() {\n  var adapter;\n\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = adapters.getAdapter('xhr');\n  } else if (typeof process !== 'undefined' && utils.kindOf(process) === 'process') {\n    // For node use HTTP adapter\n    adapter = adapters.getAdapter('http');\n  }\n\n  return adapter;\n}\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\n\n\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nvar defaults = {\n  transitional: transitionalDefaults,\n  adapter: getDefaultAdapter(),\n  transformRequest: [function transformRequest(data, headers) {\n    var contentType = headers.getContentType() || '';\n    var hasJSONContentType = contentType.indexOf('application/json') > -1;\n    var isObjectPayload = utils.isObject(data);\n\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n\n    var isFormData = utils.isFormData(data);\n\n    if (isFormData) {\n      if (!hasJSONContentType) {\n        return data;\n      }\n\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n\n    if (utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {\n      return data;\n    }\n\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n\n    var isFileList;\n\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        var _FormData = this.env && this.env.FormData;\n\n        return toFormData(isFileList ? {\n          'files[]': data\n        } : data, _FormData && new _FormData(), this.formSerializer);\n      }\n    }\n\n    if (isObjectPayload || hasJSONContentType) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n  transformResponse: [function transformResponse(data) {\n    var transitional = this.transitional || defaults.transitional;\n    var forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    var JSONRequested = this.responseType === 'json';\n\n    if (data && utils.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {\n      var silentJSONParsing = transitional && transitional.silentJSONParsing;\n      var strictJSONParsing = !silentJSONParsing && JSONRequested;\n\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n  maxContentLength: -1,\n  maxBodyLength: -1,\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\nexport default defaults;", "map": {"version": 3, "names": ["utils", "AxiosError", "transitionalD<PERSON>ault<PERSON>", "toFormData", "toURLEncodedForm", "platform", "formDataToJSON", "adapters", "DEFAULT_CONTENT_TYPE", "getDefaultAdapter", "adapter", "XMLHttpRequest", "getAdapter", "process", "kindOf", "stringifySafely", "rawValue", "parser", "encoder", "isString", "JSON", "parse", "trim", "e", "name", "stringify", "defaults", "transitional", "transformRequest", "data", "headers", "contentType", "getContentType", "hasJSONContentType", "indexOf", "isObjectPayload", "isObject", "isHTMLForm", "FormData", "isFormData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isStream", "isFile", "isBlob", "isArrayBuffer<PERSON>iew", "buffer", "isURLSearchParams", "setContentType", "toString", "isFileList", "formSerializer", "_FormData", "env", "transformResponse", "forcedJSONParsing", "JSONRequested", "responseType", "silentJSONParsing", "strictJSONParsing", "from", "ERR_BAD_RESPONSE", "response", "timeout", "xsrfCookieName", "xsrfHeaderName", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "classes", "Blob", "validateStatus", "status", "common", "for<PERSON>ach", "forEachMethodNoData", "method", "forEachMethodWithData", "merge"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/defaults/index.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\nimport adapters from '../adapters/index.js';\n\nconst DEFAULT_CONTENT_TYPE = {\n  'Content-Type': 'application/x-www-form-urlencoded'\n};\n\n/**\n * If the browser has an XMLHttpRequest object, use the XHR adapter, otherwise use the HTTP\n * adapter\n *\n * @returns {Function}\n */\nfunction getDefaultAdapter() {\n  let adapter;\n  if (typeof XMLHttpRequest !== 'undefined') {\n    // For browsers use XHR adapter\n    adapter = adapters.getAdapter('xhr');\n  } else if (typeof process !== 'undefined' && utils.kindOf(process) === 'process') {\n    // For node use HTTP adapter\n    adapter = adapters.getAdapter('http');\n  }\n  return adapter;\n}\n\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nconst defaults = {\n\n  transitional: transitionalDefaults,\n\n  adapter: getDefaultAdapter(),\n\n  transformRequest: [function transformRequest(data, headers) {\n    const contentType = headers.getContentType() || '';\n    const hasJSONContentType = contentType.indexOf('application/json') > -1;\n    const isObjectPayload = utils.isObject(data);\n\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n\n    const isFormData = utils.isFormData(data);\n\n    if (isFormData) {\n      if (!hasJSONContentType) {\n        return data;\n      }\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n\n    if (utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n\n    let isFileList;\n\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        const _FormData = this.env && this.env.FormData;\n\n        return toFormData(\n          isFileList ? {'files[]': data} : data,\n          _FormData && new _FormData(),\n          this.formSerializer\n        );\n      }\n    }\n\n    if (isObjectPayload || hasJSONContentType ) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    const transitional = this.transitional || defaults.transitional;\n    const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    const JSONRequested = this.responseType === 'json';\n\n    if (data && utils.isString(data) && ((forcedJSONParsing && !this.responseType) || JSONRequested)) {\n      const silentJSONParsing = transitional && transitional.silentJSONParsing;\n      const strictJSONParsing = !silentJSONParsing && JSONRequested;\n\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*'\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {\n  defaults.headers[method] = {};\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);\n});\n\nexport default defaults;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,UAAP,MAAuB,uBAAvB;AACA,OAAOC,oBAAP,MAAiC,mBAAjC;AACA,OAAOC,UAAP,MAAuB,0BAAvB;AACA,OAAOC,gBAAP,MAA6B,gCAA7B;AACA,OAAOC,QAAP,MAAqB,sBAArB;AACA,OAAOC,cAAP,MAA2B,8BAA3B;AACA,OAAOC,QAAP,MAAqB,sBAArB;AAEA,IAAMC,oBAAoB,GAAG;EAC3B,gBAAgB;AADW,CAA7B;AAIA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,iBAAT,GAA6B;EAC3B,IAAIC,OAAJ;;EACA,IAAI,OAAOC,cAAP,KAA0B,WAA9B,EAA2C;IACzC;IACAD,OAAO,GAAGH,QAAQ,CAACK,UAAT,CAAoB,KAApB,CAAV;EACD,CAHD,MAGO,IAAI,OAAOC,OAAP,KAAmB,WAAnB,IAAkCb,KAAK,CAACc,MAAN,CAAaD,OAAb,MAA0B,SAAhE,EAA2E;IAChF;IACAH,OAAO,GAAGH,QAAQ,CAACK,UAAT,CAAoB,MAApB,CAAV;EACD;;EACD,OAAOF,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,eAAT,CAAyBC,QAAzB,EAAmCC,MAAnC,EAA2CC,OAA3C,EAAoD;EAClD,IAAIlB,KAAK,CAACmB,QAAN,CAAeH,QAAf,CAAJ,EAA8B;IAC5B,IAAI;MACF,CAACC,MAAM,IAAIG,IAAI,CAACC,KAAhB,EAAuBL,QAAvB;MACA,OAAOhB,KAAK,CAACsB,IAAN,CAAWN,QAAX,CAAP;IACD,CAHD,CAGE,OAAOO,CAAP,EAAU;MACV,IAAIA,CAAC,CAACC,IAAF,KAAW,aAAf,EAA8B;QAC5B,MAAMD,CAAN;MACD;IACF;EACF;;EAED,OAAO,CAACL,OAAO,IAAIE,IAAI,CAACK,SAAjB,EAA4BT,QAA5B,CAAP;AACD;;AAED,IAAMU,QAAQ,GAAG;EAEfC,YAAY,EAAEzB,oBAFC;EAIfQ,OAAO,EAAED,iBAAiB,EAJX;EAMfmB,gBAAgB,EAAE,CAAC,SAASA,gBAAT,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyC;IAC1D,IAAMC,WAAW,GAAGD,OAAO,CAACE,cAAR,MAA4B,EAAhD;IACA,IAAMC,kBAAkB,GAAGF,WAAW,CAACG,OAAZ,CAAoB,kBAApB,IAA0C,CAAC,CAAtE;IACA,IAAMC,eAAe,GAAGnC,KAAK,CAACoC,QAAN,CAAeP,IAAf,CAAxB;;IAEA,IAAIM,eAAe,IAAInC,KAAK,CAACqC,UAAN,CAAiBR,IAAjB,CAAvB,EAA+C;MAC7CA,IAAI,GAAG,IAAIS,QAAJ,CAAaT,IAAb,CAAP;IACD;;IAED,IAAMU,UAAU,GAAGvC,KAAK,CAACuC,UAAN,CAAiBV,IAAjB,CAAnB;;IAEA,IAAIU,UAAJ,EAAgB;MACd,IAAI,CAACN,kBAAL,EAAyB;QACvB,OAAOJ,IAAP;MACD;;MACD,OAAOI,kBAAkB,GAAGb,IAAI,CAACK,SAAL,CAAenB,cAAc,CAACuB,IAAD,CAA7B,CAAH,GAA0CA,IAAnE;IACD;;IAED,IAAI7B,KAAK,CAACwC,aAAN,CAAoBX,IAApB,KACF7B,KAAK,CAACyC,QAAN,CAAeZ,IAAf,CADE,IAEF7B,KAAK,CAAC0C,QAAN,CAAeb,IAAf,CAFE,IAGF7B,KAAK,CAAC2C,MAAN,CAAad,IAAb,CAHE,IAIF7B,KAAK,CAAC4C,MAAN,CAAaf,IAAb,CAJF,EAKE;MACA,OAAOA,IAAP;IACD;;IACD,IAAI7B,KAAK,CAAC6C,iBAAN,CAAwBhB,IAAxB,CAAJ,EAAmC;MACjC,OAAOA,IAAI,CAACiB,MAAZ;IACD;;IACD,IAAI9C,KAAK,CAAC+C,iBAAN,CAAwBlB,IAAxB,CAAJ,EAAmC;MACjCC,OAAO,CAACkB,cAAR,CAAuB,iDAAvB,EAA0E,KAA1E;MACA,OAAOnB,IAAI,CAACoB,QAAL,EAAP;IACD;;IAED,IAAIC,UAAJ;;IAEA,IAAIf,eAAJ,EAAqB;MACnB,IAAIJ,WAAW,CAACG,OAAZ,CAAoB,mCAApB,IAA2D,CAAC,CAAhE,EAAmE;QACjE,OAAO9B,gBAAgB,CAACyB,IAAD,EAAO,KAAKsB,cAAZ,CAAhB,CAA4CF,QAA5C,EAAP;MACD;;MAED,IAAI,CAACC,UAAU,GAAGlD,KAAK,CAACkD,UAAN,CAAiBrB,IAAjB,CAAd,KAAyCE,WAAW,CAACG,OAAZ,CAAoB,qBAApB,IAA6C,CAAC,CAA3F,EAA8F;QAC5F,IAAMkB,SAAS,GAAG,KAAKC,GAAL,IAAY,KAAKA,GAAL,CAASf,QAAvC;;QAEA,OAAOnC,UAAU,CACf+C,UAAU,GAAG;UAAC,WAAWrB;QAAZ,CAAH,GAAuBA,IADlB,EAEfuB,SAAS,IAAI,IAAIA,SAAJ,EAFE,EAGf,KAAKD,cAHU,CAAjB;MAKD;IACF;;IAED,IAAIhB,eAAe,IAAIF,kBAAvB,EAA4C;MAC1CH,OAAO,CAACkB,cAAR,CAAuB,kBAAvB,EAA2C,KAA3C;MACA,OAAOjC,eAAe,CAACc,IAAD,CAAtB;IACD;;IAED,OAAOA,IAAP;EACD,CA1DiB,CANH;EAkEfyB,iBAAiB,EAAE,CAAC,SAASA,iBAAT,CAA2BzB,IAA3B,EAAiC;IACnD,IAAMF,YAAY,GAAG,KAAKA,YAAL,IAAqBD,QAAQ,CAACC,YAAnD;IACA,IAAM4B,iBAAiB,GAAG5B,YAAY,IAAIA,YAAY,CAAC4B,iBAAvD;IACA,IAAMC,aAAa,GAAG,KAAKC,YAAL,KAAsB,MAA5C;;IAEA,IAAI5B,IAAI,IAAI7B,KAAK,CAACmB,QAAN,CAAeU,IAAf,CAAR,KAAkC0B,iBAAiB,IAAI,CAAC,KAAKE,YAA5B,IAA6CD,aAA9E,CAAJ,EAAkG;MAChG,IAAME,iBAAiB,GAAG/B,YAAY,IAAIA,YAAY,CAAC+B,iBAAvD;MACA,IAAMC,iBAAiB,GAAG,CAACD,iBAAD,IAAsBF,aAAhD;;MAEA,IAAI;QACF,OAAOpC,IAAI,CAACC,KAAL,CAAWQ,IAAX,CAAP;MACD,CAFD,CAEE,OAAON,CAAP,EAAU;QACV,IAAIoC,iBAAJ,EAAuB;UACrB,IAAIpC,CAAC,CAACC,IAAF,KAAW,aAAf,EAA8B;YAC5B,MAAMvB,UAAU,CAAC2D,IAAX,CAAgBrC,CAAhB,EAAmBtB,UAAU,CAAC4D,gBAA9B,EAAgD,IAAhD,EAAsD,IAAtD,EAA4D,KAAKC,QAAjE,CAAN;UACD;;UACD,MAAMvC,CAAN;QACD;MACF;IACF;;IAED,OAAOM,IAAP;EACD,CAtBkB,CAlEJ;;EA0Ff;AACF;AACA;AACA;EACEkC,OAAO,EAAE,CA9FM;EAgGfC,cAAc,EAAE,YAhGD;EAiGfC,cAAc,EAAE,cAjGD;EAmGfC,gBAAgB,EAAE,CAAC,CAnGJ;EAoGfC,aAAa,EAAE,CAAC,CApGD;EAsGfd,GAAG,EAAE;IACHf,QAAQ,EAAEjC,QAAQ,CAAC+D,OAAT,CAAiB9B,QADxB;IAEH+B,IAAI,EAAEhE,QAAQ,CAAC+D,OAAT,CAAiBC;EAFpB,CAtGU;EA2GfC,cAAc,EAAE,SAASA,cAAT,CAAwBC,MAAxB,EAAgC;IAC9C,OAAOA,MAAM,IAAI,GAAV,IAAiBA,MAAM,GAAG,GAAjC;EACD,CA7Gc;EA+GfzC,OAAO,EAAE;IACP0C,MAAM,EAAE;MACN,UAAU;IADJ;EADD;AA/GM,CAAjB;AAsHAxE,KAAK,CAACyE,OAAN,CAAc,CAAC,QAAD,EAAW,KAAX,EAAkB,MAAlB,CAAd,EAAyC,SAASC,mBAAT,CAA6BC,MAA7B,EAAqC;EAC5EjD,QAAQ,CAACI,OAAT,CAAiB6C,MAAjB,IAA2B,EAA3B;AACD,CAFD;AAIA3E,KAAK,CAACyE,OAAN,CAAc,CAAC,MAAD,EAAS,KAAT,EAAgB,OAAhB,CAAd,EAAwC,SAASG,qBAAT,CAA+BD,MAA/B,EAAuC;EAC7EjD,QAAQ,CAACI,OAAT,CAAiB6C,MAAjB,IAA2B3E,KAAK,CAAC6E,KAAN,CAAYrE,oBAAZ,CAA3B;AACD,CAFD;AAIA,eAAekB,QAAf"}, "metadata": {}, "sourceType": "module"}