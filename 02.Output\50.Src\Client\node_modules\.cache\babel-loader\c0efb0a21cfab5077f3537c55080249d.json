{"ast": null, "code": "/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */\n\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED);\n\n  return this;\n}\n\nmodule.exports = setCacheAdd;", "map": {"version": 3, "names": ["HASH_UNDEFINED", "setCacheAdd", "value", "__data__", "set", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_setCacheAdd.js"], "sourcesContent": ["/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED);\n  return this;\n}\n\nmodule.exports = setCacheAdd;\n"], "mappings": "AAAA;AACA,IAAIA,cAAc,GAAG,2BAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC1B,KAAKC,QAAL,CAAcC,GAAd,CAAkBF,KAAlB,EAAyBF,cAAzB;;EACA,OAAO,IAAP;AACD;;AAEDK,MAAM,CAACC,OAAP,GAAiBL,WAAjB"}, "metadata": {}, "sourceType": "script"}