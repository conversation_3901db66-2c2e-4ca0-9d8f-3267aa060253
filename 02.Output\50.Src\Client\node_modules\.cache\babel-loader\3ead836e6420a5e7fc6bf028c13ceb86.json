{"ast": null, "code": "import _toConsumableArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。ClassNameがあれば、一緒につける\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @param {*} className\r\n * @return {*} Face Object\r\n */export function getCellFace(obj,className){if(!obj)return;var result=getTextFace(obj);if(className){result.className=className;}return result;}/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @return {*} Face Object\r\n */export function getTextFace(obj){if(!obj)return;return{text_color:getHexColor(obj.text_color),background_color:getHexColor(obj.background_color),text:obj.display_text};}/**\r\n * propsは有効なSourceなのか\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} true: 有効なSourceデータ。false: 無効なSourceデータ\r\n */export function isValidSource(props){if(!props){return false;}var entries=Object.entries(props);if(entries.length<=0){return false;}var hasValidProp=false;for(var _i=0,_entries=entries;_i<_entries.length;_i++){var _entries$_i=_slicedToArray(_entries[_i],2),key=_entries$_i[0],value=_entries$_i[1];if(value){hasValidProp=true;}}if(!hasValidProp){return false;}return true;}/**\r\n * Stompを経由で、Browser側で、該当Msgを受信できたことサーバーに返す\r\n *\r\n * @export\r\n * @param {*} stompClient\r\n * @param {*} id\r\n * @param {*} result\r\n */export function sendResultMsg(stompClient,id,result){console.log('sendResultMsg start. id='+id);if(stompClient){var resultObj={id:id,result:result};//Send Message\nstompClient.publish({destination:'/app/wsresult',body:JSON.stringify(resultObj)});console.log('sendResultMsg end. id='+id);}}/**\r\n * WebSocketのEndPointを組み立てる\r\n *\r\n * @export\r\n * @param {*} displayNo\r\n * @param {*} splitNo\r\n * @param {*} detailSplitNo\r\n * @return {*} EndPointの文字列\r\n */export function getWsEndpoint(displayNo,splitNo,detailSplitNo){var endPoint='/monitor/'+displayNo+'_'+splitNo;if(detailSplitNo!=null){endPoint=endPoint+'_'+detailSplitNo;}return endPoint;}/**\r\n * Colorを#付けて返す\r\n *\r\n * @export\r\n * @param {*} color\r\n * @return {*} #ありの文字列\r\n */export function getHexColor(color){if(color==null){return color;}if(color.startsWith('#')){return color;}else{return'#'+color;}}/**\r\n * display_colorとtext_color両方から、文字色を返す\r\n * text_colorが優先\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} 文字色\r\n */export function getTextOrDisplayColor(props){return getHexColor((props===null||props===void 0?void 0:props.text_color)||(props===null||props===void 0?void 0:props.display_color));}/**\r\n * 10月11日12時13分形式の文字列に単位の文字に特別のFontSizeを付ける\r\n *\r\n * @export\r\n * @param {*} text 10月11日12時13分形式の文字列\r\n * @param {*} unitFontSize 単位のFontsize\r\n * @return {*}\r\n */export function formateDatetimeText(text,unitFontSize){if(!text||!unitFontSize)return;var regex=/(\\d+)(月|日|時|分)/g;var result=_toConsumableArray(text.matchAll(regex));var formattedText='';result.forEach(function(element){var val=element[1];var unit=element[2];formattedText+=\"\".concat(val,\"<span style=\\\"font-size:\").concat(unitFontSize,\"\\\">\").concat(unit,\"</span>\");});return formattedText;}/**\r\n * 10月11日12時13分形式の文字列に日付と時間を分割して返す\r\n *\r\n * @export\r\n * @param {*} text\r\n * @return {*} date/timeありのObject\r\n */export function splitDateAndTime(text){if(!text)return;var regex=/(\\d+月\\d+日)(\\d+時\\d+分)/g;var result=_toConsumableArray(text.matchAll(regex));if(Array.isArray(result)&&result.length>=1){return{date:result[0][1],time:result[0][2]};}return undefined;}//Htmlを含むかを返す\nfunction hasHtmlTag(text){var reg=/<[^>]+>/gi;return reg.test(text);}/**\r\n * Textの通常SpaceをHtmlのSpaceを入れ替える\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*}\r\n */export function replaceWithHtmlSpace(text){var refinedText=text;if(refinedText&&!hasHtmlTag(refinedText)){refinedText=refinedText.replace(/ /g,'&nbsp;');refinedText=refinedText.replace(/　/g,'&nbsp;&nbsp;');//全角のSpace\n}return refinedText;}/**\r\n * 数字に,を入れて分割。12345678 -> '12,345,678'\r\n *\r\n * @export\r\n * @param {*} num\r\n * @return {*} 文字列\r\n */export function toThousands(num){return(num||0).toString().replace(/(\\d)(?=(?:\\d{3})+$)/g,'$1,');}", "map": {"version": 3, "names": ["getCellFace", "obj", "className", "result", "getTextFace", "text_color", "getHexColor", "background_color", "text", "display_text", "isValidSource", "props", "entries", "Object", "length", "hasValidProp", "key", "value", "sendResultMsg", "stompClient", "id", "console", "log", "resultObj", "publish", "destination", "body", "JSON", "stringify", "getWsEndpoint", "displayNo", "splitNo", "detailSplitNo", "endPoint", "color", "startsWith", "getTextOrDisplayColor", "display_color", "formateDatetimeText", "unitFontSize", "regex", "matchAll", "formattedText", "for<PERSON>ach", "element", "val", "unit", "splitDateAndTime", "Array", "isArray", "date", "time", "undefined", "hasHtmlTag", "reg", "test", "replaceWithHtmlSpace", "refinedText", "replace", "toThousands", "num", "toString"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/utils/Util.js"], "sourcesContent": ["/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。ClassNameがあれば、一緒につける\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @param {*} className\r\n * @return {*} Face Object\r\n */\r\nexport function getCellFace(obj, className) {\r\n  if (!obj) return;\r\n\r\n  const result = getTextFace(obj);\r\n  if (className) {\r\n    result.className = className;\r\n  }\r\n  return result;\r\n}\r\n\r\n/**\r\n * 画面の表示Cellに、色/文字をObjectを返す。\r\n *\r\n * @export\r\n * @param {*} obj\r\n * @return {*} Face Object\r\n */\r\nexport function getTextFace(obj) {\r\n  if (!obj) return;\r\n  return {\r\n    text_color: getHexColor(obj.text_color),\r\n    background_color: getHexColor(obj.background_color),\r\n    text: obj.display_text,\r\n  };\r\n}\r\n\r\n/**\r\n * propsは有効なSourceなのか\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} true: 有効なSourceデータ。false: 無効なSourceデータ\r\n */\r\nexport function isValidSource(props) {\r\n  if (!props) {\r\n    return false;\r\n  }\r\n\r\n  const entries = Object.entries(props);\r\n  if (entries.length <= 0) {\r\n    return false;\r\n  }\r\n\r\n  let hasValidProp = false;\r\n  for (const [key, value] of entries) {\r\n    if (value) {\r\n      hasValidProp = true;\r\n    }\r\n  }\r\n\r\n  if (!hasValidProp) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n}\r\n\r\n/**\r\n * Stompを経由で、Browser側で、該当Msgを受信できたことサーバーに返す\r\n *\r\n * @export\r\n * @param {*} stompClient\r\n * @param {*} id\r\n * @param {*} result\r\n */\r\nexport function sendResultMsg(stompClient, id, result) {\r\n  console.log('sendResultMsg start. id=' + id);\r\n  if (stompClient) {\r\n    var resultObj = { id: id, result: result };\r\n\r\n    //Send Message\r\n    stompClient.publish({\r\n      destination: '/app/wsresult',\r\n      body: JSON.stringify(resultObj),\r\n    });\r\n    console.log('sendResultMsg end. id=' + id);\r\n  }\r\n}\r\n\r\n/**\r\n * WebSocketのEndPointを組み立てる\r\n *\r\n * @export\r\n * @param {*} displayNo\r\n * @param {*} splitNo\r\n * @param {*} detailSplitNo\r\n * @return {*} EndPointの文字列\r\n */\r\nexport function getWsEndpoint(displayNo, splitNo, detailSplitNo) {\r\n  let endPoint = '/monitor/' + displayNo + '_' + splitNo;\r\n  if (detailSplitNo != null) {\r\n    endPoint = endPoint + '_' + detailSplitNo;\r\n  }\r\n  return endPoint;\r\n}\r\n\r\n/**\r\n * Colorを#付けて返す\r\n *\r\n * @export\r\n * @param {*} color\r\n * @return {*} #ありの文字列\r\n */\r\nexport function getHexColor(color) {\r\n  if (color == null) {\r\n    return color;\r\n  }\r\n\r\n  if (color.startsWith('#')) {\r\n    return color;\r\n  } else {\r\n    return '#' + color;\r\n  }\r\n}\r\n\r\n/**\r\n * display_colorとtext_color両方から、文字色を返す\r\n * text_colorが優先\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*} 文字色\r\n */\r\nexport function getTextOrDisplayColor(props) {\r\n  return getHexColor(props?.text_color || props?.display_color);\r\n}\r\n\r\n/**\r\n * 10月11日12時13分形式の文字列に単位の文字に特別のFontSizeを付ける\r\n *\r\n * @export\r\n * @param {*} text 10月11日12時13分形式の文字列\r\n * @param {*} unitFontSize 単位のFontsize\r\n * @return {*}\r\n */\r\nexport function formateDatetimeText(text, unitFontSize) {\r\n  if (!text || !unitFontSize) return;\r\n\r\n  const regex = /(\\d+)(月|日|時|分)/g;\r\n  let result = [...text.matchAll(regex)];\r\n\r\n  let formattedText = '';\r\n  result.forEach((element) => {\r\n    const val = element[1];\r\n    const unit = element[2];\r\n\r\n    formattedText += `${val}<span style=\"font-size:${unitFontSize}\">${unit}</span>`;\r\n  });\r\n  return formattedText;\r\n}\r\n\r\n/**\r\n * 10月11日12時13分形式の文字列に日付と時間を分割して返す\r\n *\r\n * @export\r\n * @param {*} text\r\n * @return {*} date/timeありのObject\r\n */\r\nexport function splitDateAndTime(text) {\r\n  if (!text) return;\r\n\r\n  const regex = /(\\d+月\\d+日)(\\d+時\\d+分)/g;\r\n  let result = [...text.matchAll(regex)];\r\n\r\n  if (Array.isArray(result) && result.length >= 1) {\r\n    return { date: result[0][1], time: result[0][2] };\r\n  }\r\n  return undefined;\r\n}\r\n\r\n//Htmlを含むかを返す\r\nfunction hasHtmlTag(text) {\r\n  var reg = /<[^>]+>/gi;\r\n  return reg.test(text);\r\n}\r\n\r\n/**\r\n * Textの通常SpaceをHtmlのSpaceを入れ替える\r\n *\r\n * @export\r\n * @param {*} props\r\n * @return {*}\r\n */\r\nexport function replaceWithHtmlSpace(text) {\r\n  let refinedText = text;\r\n  if (refinedText && !hasHtmlTag(refinedText)) {\r\n    refinedText = refinedText.replace(/ /g, '&nbsp;');\r\n    refinedText = refinedText.replace(/　/g, '&nbsp;&nbsp;'); //全角のSpace\r\n  }\r\n  return refinedText;\r\n}\r\n\r\n/**\r\n * 数字に,を入れて分割。12345678 -> '12,345,678'\r\n *\r\n * @export\r\n * @param {*} num\r\n * @return {*} 文字列\r\n */\r\nexport function toThousands(num) {\r\n  return (num || 0).toString().replace(/(\\d)(?=(?:\\d{3})+$)/g, '$1,');\r\n}\r\n"], "mappings": "4RAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASA,YAAT,CAAqBC,GAArB,CAA0BC,SAA1B,CAAqC,CAC1C,GAAI,CAACD,GAAL,CAAU,OAEV,GAAME,OAAM,CAAGC,WAAW,CAACH,GAAD,CAA1B,CACA,GAAIC,SAAJ,CAAe,CACbC,MAAM,CAACD,SAAP,CAAmBA,SAAnB,CACD,CACD,MAAOC,OAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASC,YAAT,CAAqBH,GAArB,CAA0B,CAC/B,GAAI,CAACA,GAAL,CAAU,OACV,MAAO,CACLI,UAAU,CAAEC,WAAW,CAACL,GAAG,CAACI,UAAL,CADlB,CAELE,gBAAgB,CAAED,WAAW,CAACL,GAAG,CAACM,gBAAL,CAFxB,CAGLC,IAAI,CAAEP,GAAG,CAACQ,YAHL,CAAP,CAKD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASC,cAAT,CAAuBC,KAAvB,CAA8B,CACnC,GAAI,CAACA,KAAL,CAAY,CACV,MAAO,MAAP,CACD,CAED,GAAMC,QAAO,CAAGC,MAAM,CAACD,OAAP,CAAeD,KAAf,CAAhB,CACA,GAAIC,OAAO,CAACE,MAAR,EAAkB,CAAtB,CAAyB,CACvB,MAAO,MAAP,CACD,CAED,GAAIC,aAAY,CAAG,KAAnB,CACA,sBAA2BH,OAA3B,yBAAoC,CAA/B,+CAAOI,GAAP,gBAAYC,KAAZ,gBACH,GAAIA,KAAJ,CAAW,CACTF,YAAY,CAAG,IAAf,CACD,CACF,CAED,GAAI,CAACA,YAAL,CAAmB,CACjB,MAAO,MAAP,CACD,CAED,MAAO,KAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASG,cAAT,CAAuBC,WAAvB,CAAoCC,EAApC,CAAwCjB,MAAxC,CAAgD,CACrDkB,OAAO,CAACC,GAAR,CAAY,2BAA6BF,EAAzC,EACA,GAAID,WAAJ,CAAiB,CACf,GAAII,UAAS,CAAG,CAAEH,EAAE,CAAEA,EAAN,CAAUjB,MAAM,CAAEA,MAAlB,CAAhB,CAEA;AACAgB,WAAW,CAACK,OAAZ,CAAoB,CAClBC,WAAW,CAAE,eADK,CAElBC,IAAI,CAAEC,IAAI,CAACC,SAAL,CAAeL,SAAf,CAFY,CAApB,EAIAF,OAAO,CAACC,GAAR,CAAY,yBAA2BF,EAAvC,EACD,CACF,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASS,cAAT,CAAuBC,SAAvB,CAAkCC,OAAlC,CAA2CC,aAA3C,CAA0D,CAC/D,GAAIC,SAAQ,CAAG,YAAcH,SAAd,CAA0B,GAA1B,CAAgCC,OAA/C,CACA,GAAIC,aAAa,EAAI,IAArB,CAA2B,CACzBC,QAAQ,CAAGA,QAAQ,CAAG,GAAX,CAAiBD,aAA5B,CACD,CACD,MAAOC,SAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAAS3B,YAAT,CAAqB4B,KAArB,CAA4B,CACjC,GAAIA,KAAK,EAAI,IAAb,CAAmB,CACjB,MAAOA,MAAP,CACD,CAED,GAAIA,KAAK,CAACC,UAAN,CAAiB,GAAjB,CAAJ,CAA2B,CACzB,MAAOD,MAAP,CACD,CAFD,IAEO,CACL,MAAO,IAAMA,KAAb,CACD,CACF,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASE,sBAAT,CAA+BzB,KAA/B,CAAsC,CAC3C,MAAOL,YAAW,CAAC,CAAAK,KAAK,OAAL,EAAAA,KAAK,SAAL,QAAAA,KAAK,CAAEN,UAAP,IAAqBM,KAArB,SAAqBA,KAArB,iBAAqBA,KAAK,CAAE0B,aAA5B,CAAD,CAAlB,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASC,oBAAT,CAA6B9B,IAA7B,CAAmC+B,YAAnC,CAAiD,CACtD,GAAI,CAAC/B,IAAD,EAAS,CAAC+B,YAAd,CAA4B,OAE5B,GAAMC,MAAK,CAAG,iBAAd,CACA,GAAIrC,OAAM,oBAAOK,IAAI,CAACiC,QAAL,CAAcD,KAAd,CAAP,CAAV,CAEA,GAAIE,cAAa,CAAG,EAApB,CACAvC,MAAM,CAACwC,OAAP,CAAe,SAACC,OAAD,CAAa,CAC1B,GAAMC,IAAG,CAAGD,OAAO,CAAC,CAAD,CAAnB,CACA,GAAME,KAAI,CAAGF,OAAO,CAAC,CAAD,CAApB,CAEAF,aAAa,YAAOG,GAAP,oCAAoCN,YAApC,eAAqDO,IAArD,WAAb,CACD,CALD,EAMA,MAAOJ,cAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASK,iBAAT,CAA0BvC,IAA1B,CAAgC,CACrC,GAAI,CAACA,IAAL,CAAW,OAEX,GAAMgC,MAAK,CAAG,uBAAd,CACA,GAAIrC,OAAM,oBAAOK,IAAI,CAACiC,QAAL,CAAcD,KAAd,CAAP,CAAV,CAEA,GAAIQ,KAAK,CAACC,OAAN,CAAc9C,MAAd,GAAyBA,MAAM,CAACW,MAAP,EAAiB,CAA9C,CAAiD,CAC/C,MAAO,CAAEoC,IAAI,CAAE/C,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAR,CAAsBgD,IAAI,CAAEhD,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAA5B,CAAP,CACD,CACD,MAAOiD,UAAP,CACD,CAED;AACA,QAASC,WAAT,CAAoB7C,IAApB,CAA0B,CACxB,GAAI8C,IAAG,CAAG,WAAV,CACA,MAAOA,IAAG,CAACC,IAAJ,CAAS/C,IAAT,CAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASgD,qBAAT,CAA8BhD,IAA9B,CAAoC,CACzC,GAAIiD,YAAW,CAAGjD,IAAlB,CACA,GAAIiD,WAAW,EAAI,CAACJ,UAAU,CAACI,WAAD,CAA9B,CAA6C,CAC3CA,WAAW,CAAGA,WAAW,CAACC,OAAZ,CAAoB,IAApB,CAA0B,QAA1B,CAAd,CACAD,WAAW,CAAGA,WAAW,CAACC,OAAZ,CAAoB,IAApB,CAA0B,cAA1B,CAAd,CAAyD;AAC1D,CACD,MAAOD,YAAP,CACD,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,SAASE,YAAT,CAAqBC,GAArB,CAA0B,CAC/B,MAAO,CAACA,GAAG,EAAI,CAAR,EAAWC,QAAX,GAAsBH,OAAtB,CAA8B,sBAA9B,CAAsD,KAAtD,CAAP,CACD"}, "metadata": {}, "sourceType": "module"}