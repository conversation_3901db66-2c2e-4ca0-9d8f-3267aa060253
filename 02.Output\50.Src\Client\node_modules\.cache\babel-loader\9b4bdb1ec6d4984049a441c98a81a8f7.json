{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nexport default function isOnline() {\n  if (isBrowser && typeof navigator.onLine !== 'undefined') {\n    return navigator.onLine;\n  }\n\n  return true;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isOnline", "navigator", "onLine"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/isOnline.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nexport default function isOnline() {\n  if (isBrowser && typeof navigator.onLine !== 'undefined') {\n    return navigator.onLine;\n  }\n  return true;\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,0BAAtB;AACA,eAAe,SAASC,QAAT,GAAoB;EACjC,IAAID,SAAS,IAAI,OAAOE,SAAS,CAACC,MAAjB,KAA4B,WAA7C,EAA0D;IACxD,OAAOD,SAAS,CAACC,MAAjB;EACD;;EACD,OAAO,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}