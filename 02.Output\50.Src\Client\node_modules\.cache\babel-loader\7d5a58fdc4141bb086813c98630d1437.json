{"ast": null, "code": "'use strict';\n\nimport AxiosURLSearchParams from '../../../helpers/AxiosURLSearchParams.js';\nexport default typeof URLSearchParams !== 'undefined' ? URLSearchParams : AxiosURLSearchParams;", "map": {"version": 3, "names": ["AxiosURLSearchParams", "URLSearchParams"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/platform/browser/classes/URLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport AxiosURLSearchParams from '../../../helpers/AxiosURLSearchParams.js';\nexport default typeof URLSearchParams !== 'undefined' ? URLSearchParams : AxiosURLSearchParams;\n"], "mappings": "AAAA;;AAEA,OAAOA,oBAAP,MAAiC,0CAAjC;AACA,eAAe,OAAOC,eAAP,KAA2B,WAA3B,GAAyCA,eAAzC,GAA2DD,oBAA1E"}, "metadata": {}, "sourceType": "module"}