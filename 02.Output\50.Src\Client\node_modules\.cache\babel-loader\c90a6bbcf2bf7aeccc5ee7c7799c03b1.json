{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    AjaxBasedTransport = require('./lib/ajax-based'),\n    XdrStreamingTransport = require('./xdr-streaming'),\n    XhrReceiver = require('./receiver/xhr'),\n    XDRObject = require('./sender/xdr');\n\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\n\ninherits(XdrPollingTransport, AjaxBasedTransport);\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;", "map": {"version": 3, "names": ["inherits", "require", "AjaxBasedTransport", "XdrStreamingTransport", "XhrReceiver", "XDRObject", "XdrPollingTransport", "transUrl", "enabled", "Error", "call", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/xdr-polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XdrStreamingTransport = require('./xdr-streaming')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\n\ninherits(XdrPollingTransport, AjaxBasedTransport);\n\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,kBAAkB,GAAGD,OAAO,CAAC,kBAAD,CADhC;AAAA,IAEIE,qBAAqB,GAAGF,OAAO,CAAC,iBAAD,CAFnC;AAAA,IAGIG,WAAW,GAAGH,OAAO,CAAC,gBAAD,CAHzB;AAAA,IAIII,SAAS,GAAGJ,OAAO,CAAC,cAAD,CAJvB;;AAOA,SAASK,mBAAT,CAA6BC,QAA7B,EAAuC;EACrC,IAAI,CAACF,SAAS,CAACG,OAAf,EAAwB;IACtB,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDP,kBAAkB,CAACQ,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,MAAxC,EAAgDH,WAAhD,EAA6DC,SAA7D;AACD;;AAEDL,QAAQ,CAACM,mBAAD,EAAsBJ,kBAAtB,CAAR;AAEAI,mBAAmB,CAACE,OAApB,GAA8BL,qBAAqB,CAACK,OAApD;AACAF,mBAAmB,CAACK,aAApB,GAAoC,aAApC;AACAL,mBAAmB,CAACM,UAApB,GAAiC,CAAjC,C,CAAoC;;AAEpCC,MAAM,CAACC,OAAP,GAAiBR,mBAAjB"}, "metadata": {}, "sourceType": "script"}