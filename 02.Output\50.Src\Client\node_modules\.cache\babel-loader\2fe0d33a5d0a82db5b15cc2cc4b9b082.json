{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\n\nvar useDebouncePlugin = function useDebouncePlugin(fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n      debounceLeading = _a.debounceLeading,\n      debounceTrailing = _a.debounceTrailing,\n      debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef();\n  var options = useMemo(function () {\n    var ret = {};\n\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options); // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n\n      fetchInstance.runAsync = function () {\n        var args = [];\n\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n\n        return new Promise(function (resolve, reject) {\n          var _a;\n\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve)[\"catch\"](reject);\n          });\n        });\n      };\n\n      return function () {\n        var _a;\n\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n\n  if (!debounceWait) {\n    return {};\n  }\n\n  return {\n    onCancel: function onCancel() {\n      var _a;\n\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\n\nexport default useDebouncePlugin;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "debounce", "useEffect", "useMemo", "useRef", "useDebouncePlugin", "fetchInstance", "_a", "debounce<PERSON>ait", "debounceLeading", "debounceTrailing", "debounceMaxWait", "debouncedRef", "options", "ret", "undefined", "leading", "trailing", "max<PERSON><PERSON>", "_originRunAsync_1", "runAsync", "bind", "current", "callback", "args", "_i", "Promise", "resolve", "reject", "apply", "then", "cancel", "onCancel"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport debounce from 'lodash/debounce';\nimport { useEffect, useMemo, useRef } from 'react';\nvar useDebouncePlugin = function useDebouncePlugin(fetchInstance, _a) {\n  var debounceWait = _a.debounceWait,\n    debounceLeading = _a.debounceLeading,\n    debounceTrailing = _a.debounceTrailing,\n    debounceMaxWait = _a.debounceMaxWait;\n  var debouncedRef = useRef();\n  var options = useMemo(function () {\n    var ret = {};\n    if (debounceLeading !== undefined) {\n      ret.leading = debounceLeading;\n    }\n    if (debounceTrailing !== undefined) {\n      ret.trailing = debounceTrailing;\n    }\n    if (debounceMaxWait !== undefined) {\n      ret.maxWait = debounceMaxWait;\n    }\n    return ret;\n  }, [debounceLeading, debounceTrailing, debounceMaxWait]);\n  useEffect(function () {\n    if (debounceWait) {\n      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);\n      debouncedRef.current = debounce(function (callback) {\n        callback();\n      }, debounceWait, options);\n      // debounce runAsync should be promise\n      // https://github.com/lodash/lodash/issues/4400#issuecomment-834800398\n      fetchInstance.runAsync = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) {\n          var _a;\n          (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.call(debouncedRef, function () {\n            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve)[\"catch\"](reject);\n          });\n        });\n      };\n      return function () {\n        var _a;\n        (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        fetchInstance.runAsync = _originRunAsync_1;\n      };\n    }\n  }, [debounceWait, options]);\n  if (!debounceWait) {\n    return {};\n  }\n  return {\n    onCancel: function onCancel() {\n      var _a;\n      (_a = debouncedRef.current) === null || _a === void 0 ? void 0 : _a.cancel();\n    }\n  };\n};\nexport default useDebouncePlugin;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,OAAOS,QAAP,MAAqB,iBAArB;AACA,SAASC,SAAT,EAAoBC,OAApB,EAA6BC,MAA7B,QAA2C,OAA3C;;AACA,IAAIC,iBAAiB,GAAG,SAASA,iBAAT,CAA2BC,aAA3B,EAA0CC,EAA1C,EAA8C;EACpE,IAAIC,YAAY,GAAGD,EAAE,CAACC,YAAtB;EAAA,IACEC,eAAe,GAAGF,EAAE,CAACE,eADvB;EAAA,IAEEC,gBAAgB,GAAGH,EAAE,CAACG,gBAFxB;EAAA,IAGEC,eAAe,GAAGJ,EAAE,CAACI,eAHvB;EAIA,IAAIC,YAAY,GAAGR,MAAM,EAAzB;EACA,IAAIS,OAAO,GAAGV,OAAO,CAAC,YAAY;IAChC,IAAIW,GAAG,GAAG,EAAV;;IACA,IAAIL,eAAe,KAAKM,SAAxB,EAAmC;MACjCD,GAAG,CAACE,OAAJ,GAAcP,eAAd;IACD;;IACD,IAAIC,gBAAgB,KAAKK,SAAzB,EAAoC;MAClCD,GAAG,CAACG,QAAJ,GAAeP,gBAAf;IACD;;IACD,IAAIC,eAAe,KAAKI,SAAxB,EAAmC;MACjCD,GAAG,CAACI,OAAJ,GAAcP,eAAd;IACD;;IACD,OAAOG,GAAP;EACD,CAZoB,EAYlB,CAACL,eAAD,EAAkBC,gBAAlB,EAAoCC,eAApC,CAZkB,CAArB;EAaAT,SAAS,CAAC,YAAY;IACpB,IAAIM,YAAJ,EAAkB;MAChB,IAAIW,iBAAiB,GAAGb,aAAa,CAACc,QAAd,CAAuBC,IAAvB,CAA4Bf,aAA5B,CAAxB;;MACAM,YAAY,CAACU,OAAb,GAAuBrB,QAAQ,CAAC,UAAUsB,QAAV,EAAoB;QAClDA,QAAQ;MACT,CAF8B,EAE5Bf,YAF4B,EAEdK,OAFc,CAA/B,CAFgB,CAKhB;MACA;;MACAP,aAAa,CAACc,QAAd,GAAyB,YAAY;QACnC,IAAII,IAAI,GAAG,EAAX;;QACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG/B,SAAS,CAACC,MAAhC,EAAwC8B,EAAE,EAA1C,EAA8C;UAC5CD,IAAI,CAACC,EAAD,CAAJ,GAAW/B,SAAS,CAAC+B,EAAD,CAApB;QACD;;QACD,OAAO,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;UAC5C,IAAIrB,EAAJ;;UACA,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwCf,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAAC1B,IAAH,CAAQ+B,YAAR,EAAsB,YAAY;YACjGO,iBAAiB,CAACU,KAAlB,CAAwB,KAAK,CAA7B,EAAgCvC,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACkD,IAAD,CAAX,EAAmB,KAAnB,CAA7C,EAAwEM,IAAxE,CAA6EH,OAA7E,EAAsF,OAAtF,EAA+FC,MAA/F;UACD,CAFgE,CAAjE;QAGD,CALM,CAAP;MAMD,CAXD;;MAYA,OAAO,YAAY;QACjB,IAAIrB,EAAJ;;QACA,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwCf,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACwB,MAAH,EAAjE;QACAzB,aAAa,CAACc,QAAd,GAAyBD,iBAAzB;MACD,CAJD;IAKD;EACF,CA1BQ,EA0BN,CAACX,YAAD,EAAeK,OAAf,CA1BM,CAAT;;EA2BA,IAAI,CAACL,YAAL,EAAmB;IACjB,OAAO,EAAP;EACD;;EACD,OAAO;IACLwB,QAAQ,EAAE,SAASA,QAAT,GAAoB;MAC5B,IAAIzB,EAAJ;;MACA,CAACA,EAAE,GAAGK,YAAY,CAACU,OAAnB,MAAgC,IAAhC,IAAwCf,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACwB,MAAH,EAAjE;IACD;EAJI,CAAP;AAMD,CAvDD;;AAwDA,eAAe1B,iBAAf"}, "metadata": {}, "sourceType": "module"}