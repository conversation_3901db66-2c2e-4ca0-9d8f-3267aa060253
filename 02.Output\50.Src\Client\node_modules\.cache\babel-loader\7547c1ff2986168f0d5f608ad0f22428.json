{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\n\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _a = __read(useState(function () {\n    var cookieValue = Cookies.get(cookieKey);\n    if (isString(cookieValue)) return cookieValue;\n\n    if (isFunction(options.defaultValue)) {\n      return options.defaultValue();\n    }\n\n    return options.defaultValue;\n  }), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n\n    var _a = __assign(__assign({}, options), newOptions),\n        defaultValue = _a.defaultValue,\n        restOptions = __rest(_a, [\"defaultValue\"]);\n\n    setState(function (prevState) {\n      var value = isFunction(newValue) ? newValue(prevState) : newValue;\n\n      if (value === undefined) {\n        Cookies.remove(cookieKey);\n      } else {\n        Cookies.set(cookieKey, value, restOptions);\n      }\n\n      return value;\n    });\n  });\n  return [state, updateState];\n}\n\nexport default useCookieState;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "next", "done", "push", "value", "error", "Cookies", "useState", "useMemoizedFn", "isFunction", "isString", "useCookieState", "<PERSON><PERSON><PERSON>", "options", "_a", "cookieValue", "get", "defaultValue", "state", "setState", "updateState", "newValue", "newOptions", "restOptions", "prevState", "undefined", "remove", "set"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useCookieState/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = __read(useState(function () {\n      var cookieValue = Cookies.get(cookieKey);\n      if (isString(cookieValue)) return cookieValue;\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n    var _a = __assign(__assign({}, options), newOptions),\n      defaultValue = _a.defaultValue,\n      restOptions = __rest(_a, [\"defaultValue\"]);\n    setState(function (prevState) {\n      var value = isFunction(newValue) ? newValue(prevState) : newValue;\n      if (value === undefined) {\n        Cookies.remove(cookieKey);\n      } else {\n        Cookies.set(cookieKey, value, restOptions);\n      }\n      return value;\n    });\n  });\n  return [state, updateState];\n}\nexport default useCookieState;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUV,CAAV,EAAaW,CAAb,EAAgB;EAClD,IAAIZ,CAAC,GAAG,EAAR;;EACA,KAAK,IAAIM,CAAT,IAAcL,CAAd,EAAiB;IACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,KAA8CM,CAAC,CAACC,OAAF,CAAUP,CAAV,IAAe,CAAjE,EAAoEN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;EACrE;;EACD,IAAIL,CAAC,IAAI,IAAL,IAAa,OAAOH,MAAM,CAACgB,qBAAd,KAAwC,UAAzD,EAAqE,KAAK,IAAIZ,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAGR,MAAM,CAACgB,qBAAP,CAA6Bb,CAA7B,CAApB,EAAqDC,CAAC,GAAGI,CAAC,CAACD,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IAC3I,IAAIU,CAAC,CAACC,OAAF,CAAUP,CAAC,CAACJ,CAAD,CAAX,IAAkB,CAAlB,IAAuBJ,MAAM,CAACS,SAAP,CAAiBQ,oBAAjB,CAAsCN,IAAtC,CAA2CR,CAA3C,EAA8CK,CAAC,CAACJ,CAAD,CAA/C,CAA3B,EAAgFF,CAAC,CAACM,CAAC,CAACJ,CAAD,CAAF,CAAD,GAAUD,CAAC,CAACK,CAAC,CAACJ,CAAD,CAAF,CAAX;EACjF;EACD,OAAOF,CAAP;AACD,CATD;;AAUA,IAAIgB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAad,CAAb,EAAgB;EAClD,IAAIe,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIf,CAAC,GAAGgB,CAAC,CAACT,IAAF,CAAOQ,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEV,CAHF;;EAIA,IAAI;IACF,OAAO,CAACT,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACkB,CAAC,GAAGnB,CAAC,CAACqB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDF,EAAE,CAACG,IAAH,CAAQJ,CAAC,CAACK,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdf,CAAC,GAAG;MACFe,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIN,CAAC,IAAI,CAACA,CAAC,CAACG,IAAR,KAAiBN,CAAC,GAAGhB,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCgB,CAAC,CAACT,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIU,CAAJ,EAAO,MAAMA,CAAC,CAACe,KAAR;IACR;EACF;;EACD,OAAOL,EAAP;AACD,CAvBD;;AAwBA,OAAOM,OAAP,MAAoB,WAApB;AACA,SAASC,QAAT,QAAyB,OAAzB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,SAASC,UAAT,EAAqBC,QAArB,QAAqC,UAArC;;AACA,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,OAAnC,EAA4C;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,EAAE,GAAGpB,MAAM,CAACa,QAAQ,CAAC,YAAY;IACjC,IAAIQ,WAAW,GAAGT,OAAO,CAACU,GAAR,CAAYJ,SAAZ,CAAlB;IACA,IAAIF,QAAQ,CAACK,WAAD,CAAZ,EAA2B,OAAOA,WAAP;;IAC3B,IAAIN,UAAU,CAACI,OAAO,CAACI,YAAT,CAAd,EAAsC;MACpC,OAAOJ,OAAO,CAACI,YAAR,EAAP;IACD;;IACD,OAAOJ,OAAO,CAACI,YAAf;EACD,CAPqB,CAAT,EAOT,CAPS,CAAf;EAAA,IAQEC,KAAK,GAAGJ,EAAE,CAAC,CAAD,CARZ;EAAA,IASEK,QAAQ,GAAGL,EAAE,CAAC,CAAD,CATf;;EAUA,IAAIM,WAAW,GAAGZ,aAAa,CAAC,UAAUa,QAAV,EAAoBC,UAApB,EAAgC;IAC9D,IAAIA,UAAU,KAAK,KAAK,CAAxB,EAA2B;MACzBA,UAAU,GAAG,EAAb;IACD;;IACD,IAAIR,EAAE,GAAGvC,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKsC,OAAL,CAAT,EAAwBS,UAAxB,CAAjB;IAAA,IACEL,YAAY,GAAGH,EAAE,CAACG,YADpB;IAAA,IAEEM,WAAW,GAAGlC,MAAM,CAACyB,EAAD,EAAK,CAAC,cAAD,CAAL,CAFtB;;IAGAK,QAAQ,CAAC,UAAUK,SAAV,EAAqB;MAC5B,IAAIpB,KAAK,GAAGK,UAAU,CAACY,QAAD,CAAV,GAAuBA,QAAQ,CAACG,SAAD,CAA/B,GAA6CH,QAAzD;;MACA,IAAIjB,KAAK,KAAKqB,SAAd,EAAyB;QACvBnB,OAAO,CAACoB,MAAR,CAAed,SAAf;MACD,CAFD,MAEO;QACLN,OAAO,CAACqB,GAAR,CAAYf,SAAZ,EAAuBR,KAAvB,EAA8BmB,WAA9B;MACD;;MACD,OAAOnB,KAAP;IACD,CARO,CAAR;EASD,CAhB8B,CAA/B;EAiBA,OAAO,CAACc,KAAD,EAAQE,WAAR,CAAP;AACD;;AACD,eAAeT,cAAf"}, "metadata": {}, "sourceType": "module"}