{"ast": null, "code": "var fails = require('../internals/fails');\n\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true : value == NATIVE ? false : isCallable(detection) ? fails(detection) : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\nmodule.exports = isForced;", "map": {"version": 3, "names": ["fails", "require", "isCallable", "replacement", "isForced", "feature", "detection", "value", "data", "normalize", "POLYFILL", "NATIVE", "string", "String", "replace", "toLowerCase", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/core-js-pure/internals/is-forced.js"], "sourcesContent": ["var fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAD,CAAnB;;AACA,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAAD,CAAxB;;AAEA,IAAIE,WAAW,GAAG,iBAAlB;;AAEA,IAAIC,QAAQ,GAAG,UAAUC,OAAV,EAAmBC,SAAnB,EAA8B;EAC3C,IAAIC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAACJ,OAAD,CAAV,CAAhB;EACA,OAAOE,KAAK,IAAIG,QAAT,GAAoB,IAApB,GACHH,KAAK,IAAII,MAAT,GAAkB,KAAlB,GACAT,UAAU,CAACI,SAAD,CAAV,GAAwBN,KAAK,CAACM,SAAD,CAA7B,GACA,CAAC,CAACA,SAHN;AAID,CAND;;AAQA,IAAIG,SAAS,GAAGL,QAAQ,CAACK,SAAT,GAAqB,UAAUG,MAAV,EAAkB;EACrD,OAAOC,MAAM,CAACD,MAAD,CAAN,CAAeE,OAAf,CAAuBX,WAAvB,EAAoC,GAApC,EAAyCY,WAAzC,EAAP;AACD,CAFD;;AAIA,IAAIP,IAAI,GAAGJ,QAAQ,CAACI,IAAT,GAAgB,EAA3B;AACA,IAAIG,MAAM,GAAGP,QAAQ,CAACO,MAAT,GAAkB,GAA/B;AACA,IAAID,QAAQ,GAAGN,QAAQ,CAACM,QAAT,GAAoB,GAAnC;AAEAM,MAAM,CAACC,OAAP,GAAiBb,QAAjB"}, "metadata": {}, "sourceType": "script"}