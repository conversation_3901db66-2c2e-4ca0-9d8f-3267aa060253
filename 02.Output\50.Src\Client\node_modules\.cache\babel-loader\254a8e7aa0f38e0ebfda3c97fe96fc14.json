{"ast": null, "code": "import _createForOfIteratorHelper from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js\";\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport { BYTE } from './byte';\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\n\nexport var FrameImpl = /*#__PURE__*/function () {\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  function FrameImpl(params) {\n    _classCallCheck(this, FrameImpl);\n\n    var command = params.command,\n        headers = params.headers,\n        body = params.body,\n        binaryBody = params.binaryBody,\n        escapeHeaderValues = params.escapeHeaderValues,\n        skipContentLengthHeader = params.skipContentLengthHeader;\n    this.command = command;\n    this.headers = Object.assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n  /**\n   * body of the frame\n   */\n\n\n  _createClass(FrameImpl, [{\n    key: \"body\",\n    get: function get() {\n      if (!this._body && this.isBinaryBody) {\n        this._body = new TextDecoder().decode(this._binaryBody);\n      }\n\n      return this._body;\n    }\n    /**\n     * body as Uint8Array\n     */\n\n  }, {\n    key: \"binaryBody\",\n    get: function get() {\n      if (!this._binaryBody && !this.isBinaryBody) {\n        this._binaryBody = new TextEncoder().encode(this._body);\n      }\n\n      return this._binaryBody;\n    }\n    /**\n     * deserialize a STOMP Frame from raw data.\n     *\n     * @internal\n     */\n\n  }, {\n    key: \"toString\",\n    value:\n    /**\n     * @internal\n     */\n    function toString() {\n      return this.serializeCmdAndHeaders();\n    }\n    /**\n     * serialize this Frame in a format suitable to be passed to WebSocket.\n     * If the body is string the output will be string.\n     * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n     *\n     * @internal\n     */\n\n  }, {\n    key: \"serialize\",\n    value: function serialize() {\n      var cmdAndHeaders = this.serializeCmdAndHeaders();\n\n      if (this.isBinaryBody) {\n        return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n      } else {\n        return cmdAndHeaders + this._body + BYTE.NULL;\n      }\n    }\n  }, {\n    key: \"serializeCmdAndHeaders\",\n    value: function serializeCmdAndHeaders() {\n      var lines = [this.command];\n\n      if (this.skipContentLengthHeader) {\n        delete this.headers['content-length'];\n      }\n\n      for (var _i = 0, _Object$keys = Object.keys(this.headers || {}); _i < _Object$keys.length; _i++) {\n        var name = _Object$keys[_i];\n        var value = this.headers[name];\n\n        if (this.escapeHeaderValues && this.command !== 'CONNECT' && this.command !== 'CONNECTED') {\n          lines.push(\"\".concat(name, \":\").concat(FrameImpl.hdrValueEscape(\"\".concat(value))));\n        } else {\n          lines.push(\"\".concat(name, \":\").concat(value));\n        }\n      }\n\n      if (this.isBinaryBody || !this.isBodyEmpty() && !this.skipContentLengthHeader) {\n        lines.push(\"content-length:\".concat(this.bodyLength()));\n      }\n\n      return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n    }\n  }, {\n    key: \"isBodyEmpty\",\n    value: function isBodyEmpty() {\n      return this.bodyLength() === 0;\n    }\n  }, {\n    key: \"bodyLength\",\n    value: function bodyLength() {\n      var binaryBody = this.binaryBody;\n      return binaryBody ? binaryBody.length : 0;\n    }\n    /**\n     * Compute the size of a UTF-8 string by counting its number of bytes\n     * (and not the number of characters composing the string)\n     */\n\n  }], [{\n    key: \"fromRawFrame\",\n    value: function fromRawFrame(rawFrame, escapeHeaderValues) {\n      var headers = {};\n\n      var trim = function trim(str) {\n        return str.replace(/^\\s+|\\s+$/g, '');\n      }; // In case of repeated headers, as per standards, first value need to be used\n\n\n      var _iterator = _createForOfIteratorHelper(rawFrame.headers.reverse()),\n          _step;\n\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var header = _step.value;\n          var idx = header.indexOf(':');\n          var key = trim(header[0]);\n          var value = trim(header[1]);\n\n          if (escapeHeaderValues && rawFrame.command !== 'CONNECT' && rawFrame.command !== 'CONNECTED') {\n            value = FrameImpl.hdrValueUnEscape(value);\n          }\n\n          headers[key] = value;\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n\n      return new FrameImpl({\n        command: rawFrame.command,\n        headers: headers,\n        binaryBody: rawFrame.binaryBody,\n        escapeHeaderValues: escapeHeaderValues\n      });\n    }\n  }, {\n    key: \"sizeOfUTF8\",\n    value: function sizeOfUTF8(s) {\n      return s ? new TextEncoder().encode(s).length : 0;\n    }\n  }, {\n    key: \"toUnit8Array\",\n    value: function toUnit8Array(cmdAndHeaders, binaryBody) {\n      var uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n      var nullTerminator = new Uint8Array([0]);\n      var uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n      uint8Frame.set(uint8CmdAndHeaders);\n      uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n      uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n      return uint8Frame;\n    }\n    /**\n     * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n     *\n     * @internal\n     */\n\n  }, {\n    key: \"marshall\",\n    value: function marshall(params) {\n      var frame = new FrameImpl(params);\n      return frame.serialize();\n    }\n    /**\n     *  Escape header values\n     */\n\n  }, {\n    key: \"hdrValueEscape\",\n    value: function hdrValueEscape(str) {\n      return str.replace(/\\\\/g, '\\\\\\\\').replace(/\\r/g, '\\\\r').replace(/\\n/g, '\\\\n').replace(/:/g, '\\\\c');\n    }\n    /**\n     * UnEscape header values\n     */\n\n  }, {\n    key: \"hdrValueUnEscape\",\n    value: function hdrValueUnEscape(str) {\n      return str.replace(/\\\\r/g, '\\r').replace(/\\\\n/g, '\\n').replace(/\\\\c/g, ':').replace(/\\\\\\\\/g, '\\\\');\n    }\n  }]);\n\n  return FrameImpl;\n}();", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,IAAT,QAAqB,QAArB;AAKA;;;;;;AAKA,WAAaC,SAAb;EAyCE;;;;;EAKA,mBAAYC,MAAZ,EAOC;IAAA;;IACC,IACEC,OADF,GAOID,MAPJ,CACEC,OADF;IAAA,IAEEC,OAFF,GAOIF,MAPJ,CAEEE,OAFF;IAAA,IAGEC,IAHF,GAOIH,MAPJ,CAGEG,IAHF;IAAA,IAIEC,UAJF,GAOIJ,MAPJ,CAIEI,UAJF;IAAA,IAKEC,kBALF,GAOIL,MAPJ,CAKEK,kBALF;IAAA,IAMEC,uBANF,GAOIN,MAPJ,CAMEM,uBANF;IAQA,KAAKL,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAgBK,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BN,OAAO,IAAI,EAArC,CAAhB;;IAEA,IAAIE,UAAJ,EAAgB;MACd,KAAKK,WAAL,GAAmBL,UAAnB;MACA,KAAKM,YAAL,GAAoB,IAApB;IACD,CAHD,MAGO;MACL,KAAKC,KAAL,GAAaR,IAAI,IAAI,EAArB;MACA,KAAKO,YAAL,GAAoB,KAApB;IACD;;IACD,KAAKL,kBAAL,GAA0BA,kBAAkB,IAAI,KAAhD;IACA,KAAKC,uBAAL,GAA+BA,uBAAuB,IAAI,KAA1D;EACD;EA1DD;;;;;EAhBF;IAAA;IAAA,KAmBE,eAAQ;MACN,IAAI,CAAC,KAAKK,KAAN,IAAe,KAAKD,YAAxB,EAAsC;QACpC,KAAKC,KAAL,GAAa,IAAIC,WAAJ,GAAkBC,MAAlB,CAAyB,KAAKJ,WAA9B,CAAb;MACD;;MACD,OAAO,KAAKE,KAAZ;IACD;IAGD;;;;EA3BF;IAAA;IAAA,KA8BE,eAAc;MACZ,IAAI,CAAC,KAAKF,WAAN,IAAqB,CAAC,KAAKC,YAA/B,EAA6C;QAC3C,KAAKD,WAAL,GAAmB,IAAIK,WAAJ,GAAkBC,MAAlB,CAAyB,KAAKJ,KAA9B,CAAnB;MACD;;MACD,OAAO,KAAKF,WAAZ;IACD;IAyCD;;;;;;EA5EF;IAAA;IAAA;IAkHE;;;IAGO,oBAAQ;MACb,OAAO,KAAKO,sBAAL,EAAP;IACD;IAED;;;;;;;;EAzHF;IAAA;IAAA,OAgIS,qBAAS;MACd,IAAMC,aAAa,GAAG,KAAKD,sBAAL,EAAtB;;MAEA,IAAI,KAAKN,YAAT,EAAuB;QACrB,OAAOX,SAAS,CAACmB,YAAV,CAAuBD,aAAvB,EAAsC,KAAKR,WAA3C,EAAwDU,MAA/D;MACD,CAFD,MAEO;QACL,OAAOF,aAAa,GAAG,KAAKN,KAArB,GAA6Bb,IAAI,CAACsB,IAAzC;MACD;IACF;EAxIH;IAAA;IAAA,OA0IU,kCAAsB;MAC5B,IAAMC,KAAK,GAAG,CAAC,KAAKpB,OAAN,CAAd;;MACA,IAAI,KAAKK,uBAAT,EAAkC;QAChC,OAAO,KAAKJ,OAAL,CAAa,gBAAb,CAAP;MACD;;MAED,gCAAmBK,MAAM,CAACe,IAAP,CAAY,KAAKpB,OAAL,IAAgB,EAA5B,CAAnB,kCAAoD;QAA/C,IAAMqB,IAAI,mBAAV;QACH,IAAMC,KAAK,GAAG,KAAKtB,OAAL,CAAaqB,IAAb,CAAd;;QACA,IACE,KAAKlB,kBAAL,IACA,KAAKJ,OAAL,KAAiB,SADjB,IAEA,KAAKA,OAAL,KAAiB,WAHnB,EAIE;UACAoB,KAAK,CAACI,IAAN,WAAcF,IAAd,cAAsBxB,SAAS,CAAC2B,cAAV,WAA4BF,KAA5B,EAAtB;QACD,CAND,MAMO;UACLH,KAAK,CAACI,IAAN,WAAcF,IAAd,cAAsBC,KAAtB;QACD;MACF;;MACD,IACE,KAAKd,YAAL,IACC,CAAC,KAAKiB,WAAL,EAAD,IAAuB,CAAC,KAAKrB,uBAFhC,EAGE;QACAe,KAAK,CAACI,IAAN,0BAA6B,KAAKG,UAAL,EAA7B;MACD;;MACD,OAAOP,KAAK,CAACQ,IAAN,CAAW/B,IAAI,CAACgC,EAAhB,IAAsBhC,IAAI,CAACgC,EAA3B,GAAgChC,IAAI,CAACgC,EAA5C;IACD;EAnKH;IAAA;IAAA,OAqKU,uBAAW;MACjB,OAAO,KAAKF,UAAL,OAAsB,CAA7B;IACD;EAvKH;IAAA;IAAA,OAyKU,sBAAU;MAChB,IAAMxB,UAAU,GAAG,KAAKA,UAAxB;MACA,OAAOA,UAAU,GAAGA,UAAU,CAAC2B,MAAd,GAAuB,CAAxC;IACD;IAED;;;;;EA9KF;IAAA;IAAA,OAiFS,sBACLC,QADK,EAEL3B,kBAFK,EAEsB;MAE3B,IAAMH,OAAO,GAAiB,EAA9B;;MACA,IAAM+B,IAAI,GAAG,SAAPA,IAAO,CAACC,GAAD;QAAA,OAAyBA,GAAG,CAACC,OAAJ,CAAY,YAAZ,EAA0B,EAA1B,CAAzB;MAAA,CAAb,CAH2B,CAK3B;;;MAL2B,2CAMNH,QAAQ,CAAC9B,OAAT,CAAiBkC,OAAjB,EANM;MAAA;;MAAA;QAM3B,oDAAiD;UAAA,IAAtCC,MAAsC;UAC/C,IAAMC,GAAG,GAAGD,MAAM,CAACE,OAAP,CAAe,GAAf,CAAZ;UAEA,IAAMC,GAAG,GAAGP,IAAI,CAACI,MAAM,CAAC,CAAD,CAAP,CAAhB;UACA,IAAIb,KAAK,GAAGS,IAAI,CAACI,MAAM,CAAC,CAAD,CAAP,CAAhB;;UAEA,IACEhC,kBAAkB,IAClB2B,QAAQ,CAAC/B,OAAT,KAAqB,SADrB,IAEA+B,QAAQ,CAAC/B,OAAT,KAAqB,WAHvB,EAIE;YACAuB,KAAK,GAAGzB,SAAS,CAAC0C,gBAAV,CAA2BjB,KAA3B,CAAR;UACD;;UAEDtB,OAAO,CAACsC,GAAD,CAAP,GAAehB,KAAf;QACD;MArB0B;QAAA;MAAA;QAAA;MAAA;;MAuB3B,OAAO,IAAIzB,SAAJ,CAAc;QACnBE,OAAO,EAAE+B,QAAQ,CAAC/B,OADC;QAEnBC,OAAO,EAAPA,OAFmB;QAGnBE,UAAU,EAAE4B,QAAQ,CAAC5B,UAHF;QAInBC,kBAAkB,EAAlBA;MAJmB,CAAd,CAAP;IAMD;EAhHH;IAAA;IAAA,OAkLU,oBAAkBqC,CAAlB,EAA2B;MACjC,OAAOA,CAAC,GAAG,IAAI5B,WAAJ,GAAkBC,MAAlB,CAAyB2B,CAAzB,EAA4BX,MAA/B,GAAwC,CAAhD;IACD;EApLH;IAAA;IAAA,OAsLU,sBACNd,aADM,EAENb,UAFM,EAEgB;MAEtB,IAAMuC,kBAAkB,GAAG,IAAI7B,WAAJ,GAAkBC,MAAlB,CAAyBE,aAAzB,CAA3B;MACA,IAAM2B,cAAc,GAAG,IAAIC,UAAJ,CAAe,CAAC,CAAD,CAAf,CAAvB;MACA,IAAMC,UAAU,GAAG,IAAID,UAAJ,CACjBF,kBAAkB,CAACZ,MAAnB,GAA4B3B,UAAU,CAAC2B,MAAvC,GAAgDa,cAAc,CAACb,MAD9C,CAAnB;MAIAe,UAAU,CAACC,GAAX,CAAeJ,kBAAf;MACAG,UAAU,CAACC,GAAX,CAAe3C,UAAf,EAA2BuC,kBAAkB,CAACZ,MAA9C;MACAe,UAAU,CAACC,GAAX,CACEH,cADF,EAEED,kBAAkB,CAACZ,MAAnB,GAA4B3B,UAAU,CAAC2B,MAFzC;MAKA,OAAOe,UAAP;IACD;IACD;;;;;;EAzMF;IAAA;IAAA,OA8MS,kBAAgB9C,MAAhB,EAON;MACC,IAAMgD,KAAK,GAAG,IAAIjD,SAAJ,CAAcC,MAAd,CAAd;MACA,OAAOgD,KAAK,CAACC,SAAN,EAAP;IACD;IAED;;;;EA1NF;IAAA;IAAA,OA6NU,wBAAsBf,GAAtB,EAAiC;MACvC,OAAOA,GAAG,CACPC,OADI,CACI,KADJ,EACW,MADX,EAEJA,OAFI,CAEI,KAFJ,EAEW,KAFX,EAGJA,OAHI,CAGI,KAHJ,EAGW,KAHX,EAIJA,OAJI,CAII,IAJJ,EAIU,KAJV,CAAP;IAKD;IAED;;;;EArOF;IAAA;IAAA,OAwOU,0BAAwBD,GAAxB,EAAmC;MACzC,OAAOA,GAAG,CACPC,OADI,CACI,MADJ,EACY,IADZ,EAEJA,OAFI,CAEI,MAFJ,EAEY,IAFZ,EAGJA,OAHI,CAGI,MAHJ,EAGY,GAHZ,EAIJA,OAJI,CAII,OAJJ,EAIa,IAJb,CAAP;IAKD;EA9OH;;EAAA;AAAA", "names": ["BYTE", "FrameImpl", "params", "command", "headers", "body", "binaryBody", "escapeHeader<PERSON><PERSON>ues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "_binaryBody", "isBinaryBody", "_body", "TextDecoder", "decode", "TextEncoder", "encode", "serializeCmdAndHeaders", "cmdAndHeaders", "toUnit8Array", "buffer", "NULL", "lines", "keys", "name", "value", "push", "hdrValueEscape", "isBodyEmpty", "<PERSON><PERSON><PERSON><PERSON>", "join", "LF", "length", "rawFrame", "trim", "str", "replace", "reverse", "header", "idx", "indexOf", "key", "hdrValueUnEscape", "s", "uint8CmdAndHeaders", "nullTerminator", "Uint8Array", "uint8Frame", "set", "frame", "serialize"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\frame-impl.ts"], "sourcesContent": ["import { BYTE } from './byte';\nimport { <PERSON>rame } from './i-frame';\nimport { StompHeaders } from './stomp-headers';\nimport { IRawFrameType } from './types';\n\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl implements IFrame {\n  /**\n   * STOMP Command\n   */\n  public command: string;\n\n  /**\n   * Headers, key value pairs.\n   */\n  public headers: StompHeaders;\n\n  /**\n   * Is this frame binary (based on whether body/binaryBody was passed when creating this frame).\n   */\n  public isBinaryBody: boolean;\n\n  /**\n   * body of the frame\n   */\n  get body(): string {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body;\n  }\n  private _body: string;\n\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody(): Uint8Array {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    return this._binaryBody;\n  }\n  private _binaryBody: Uint8Array;\n\n  private escapeHeaderValues: boolean;\n  private skipContentLengthHeader: boolean;\n\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader,\n    } = params;\n    this.command = command;\n    this.headers = (Object as any).assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  public static fromRawFrame(\n    rawFrame: IRawFrameType,\n    escapeHeaderValues: boolean\n  ): FrameImpl {\n    const headers: StompHeaders = {};\n    const trim = (str: string): string => str.replace(/^\\s+|\\s+$/g, '');\n\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n\n      if (\n        escapeHeaderValues &&\n        rawFrame.command !== 'CONNECT' &&\n        rawFrame.command !== 'CONNECTED'\n      ) {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n\n      headers[key] = value;\n    }\n\n    return new FrameImpl({\n      command: rawFrame.command,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  public toString(): string {\n    return this.serializeCmdAndHeaders();\n  }\n\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  public serialize(): string | ArrayBuffer {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n\n  private serializeCmdAndHeaders(): string {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (\n        this.escapeHeaderValues &&\n        this.command !== 'CONNECT' &&\n        this.command !== 'CONNECTED'\n      ) {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (\n      this.isBinaryBody ||\n      (!this.isBodyEmpty() && !this.skipContentLengthHeader)\n    ) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n\n  private isBodyEmpty(): boolean {\n    return this.bodyLength() === 0;\n  }\n\n  private bodyLength(): number {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  private static sizeOfUTF8(s: string): number {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n\n  private static toUnit8Array(\n    cmdAndHeaders: string,\n    binaryBody: Uint8Array\n  ): Uint8Array {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(\n      uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length\n    );\n\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(\n      nullTerminator,\n      uint8CmdAndHeaders.length + binaryBody.length\n    );\n\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  public static marshall(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n\n  /**\n   *  Escape header values\n   */\n  private static hdrValueEscape(str: string): string {\n    return str\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/:/g, '\\\\c');\n  }\n\n  /**\n   * UnEscape header values\n   */\n  private static hdrValueUnEscape(str: string): string {\n    return str\n      .replace(/\\\\r/g, '\\r')\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\c/g, ':')\n      .replace(/\\\\\\\\/g, '\\\\');\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}