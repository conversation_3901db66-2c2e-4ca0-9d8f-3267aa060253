{"ast": null, "code": "var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n/** Error message constants. */\n\n\nvar FUNC_ERROR_TEXT = 'Expected a function';\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\n\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n\n  wait = toNumber(wait) || 0;\n\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time; // Start the timer for the trailing edge.\n\n    timerId = setTimeout(timerExpired, wait); // Invoke the leading edge.\n\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n    return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime; // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n\n    return lastCallTime === undefined || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;\n  }\n\n  function timerExpired() {\n    var time = now();\n\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    } // Restart the timer.\n\n\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined; // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n\n    return result;\n  }\n\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;", "map": {"version": 3, "names": ["isObject", "require", "now", "toNumber", "FUNC_ERROR_TEXT", "nativeMax", "Math", "max", "nativeMin", "min", "debounce", "func", "wait", "options", "lastArgs", "lastThis", "max<PERSON><PERSON>", "result", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "trailing", "TypeError", "invokeFunc", "time", "args", "thisArg", "undefined", "apply", "leading<PERSON>dge", "setTimeout", "timerExpired", "remainingWait", "timeSinceLastCall", "timeSinceLastInvoke", "timeWaiting", "shouldInvoke", "trailingEdge", "cancel", "clearTimeout", "flush", "debounced", "isInvoking", "arguments", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/debounce.js"], "sourcesContent": ["var isObject = require('./isObject'),\n    now = require('./now'),\n    toNumber = require('./toNumber');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nmodule.exports = debounce;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAD,CAAtB;AAAA,IACIC,GAAG,GAAGD,OAAO,CAAC,OAAD,CADjB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,YAAD,CAFtB;AAIA;;;AACA,IAAIG,eAAe,GAAG,qBAAtB;AAEA;;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAArB;AAAA,IACIC,SAAS,GAAGF,IAAI,CAACG,GADrB;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,IAAlB,EAAwBC,IAAxB,EAA8BC,OAA9B,EAAuC;EACrC,IAAIC,QAAJ;EAAA,IACIC,QADJ;EAAA,IAEIC,OAFJ;EAAA,IAGIC,MAHJ;EAAA,IAIIC,OAJJ;EAAA,IAKIC,YALJ;EAAA,IAMIC,cAAc,GAAG,CANrB;EAAA,IAOIC,OAAO,GAAG,KAPd;EAAA,IAQIC,MAAM,GAAG,KARb;EAAA,IASIC,QAAQ,GAAG,IATf;;EAWA,IAAI,OAAOZ,IAAP,IAAe,UAAnB,EAA+B;IAC7B,MAAM,IAAIa,SAAJ,CAAcpB,eAAd,CAAN;EACD;;EACDQ,IAAI,GAAGT,QAAQ,CAACS,IAAD,CAAR,IAAkB,CAAzB;;EACA,IAAIZ,QAAQ,CAACa,OAAD,CAAZ,EAAuB;IACrBQ,OAAO,GAAG,CAAC,CAACR,OAAO,CAACQ,OAApB;IACAC,MAAM,GAAG,aAAaT,OAAtB;IACAG,OAAO,GAAGM,MAAM,GAAGjB,SAAS,CAACF,QAAQ,CAACU,OAAO,CAACG,OAAT,CAAR,IAA6B,CAA9B,EAAiCJ,IAAjC,CAAZ,GAAqDI,OAArE;IACAO,QAAQ,GAAG,cAAcV,OAAd,GAAwB,CAAC,CAACA,OAAO,CAACU,QAAlC,GAA6CA,QAAxD;EACD;;EAED,SAASE,UAAT,CAAoBC,IAApB,EAA0B;IACxB,IAAIC,IAAI,GAAGb,QAAX;IAAA,IACIc,OAAO,GAAGb,QADd;IAGAD,QAAQ,GAAGC,QAAQ,GAAGc,SAAtB;IACAT,cAAc,GAAGM,IAAjB;IACAT,MAAM,GAAGN,IAAI,CAACmB,KAAL,CAAWF,OAAX,EAAoBD,IAApB,CAAT;IACA,OAAOV,MAAP;EACD;;EAED,SAASc,WAAT,CAAqBL,IAArB,EAA2B;IACzB;IACAN,cAAc,GAAGM,IAAjB,CAFyB,CAGzB;;IACAR,OAAO,GAAGc,UAAU,CAACC,YAAD,EAAerB,IAAf,CAApB,CAJyB,CAKzB;;IACA,OAAOS,OAAO,GAAGI,UAAU,CAACC,IAAD,CAAb,GAAsBT,MAApC;EACD;;EAED,SAASiB,aAAT,CAAuBR,IAAvB,EAA6B;IAC3B,IAAIS,iBAAiB,GAAGT,IAAI,GAAGP,YAA/B;IAAA,IACIiB,mBAAmB,GAAGV,IAAI,GAAGN,cADjC;IAAA,IAEIiB,WAAW,GAAGzB,IAAI,GAAGuB,iBAFzB;IAIA,OAAOb,MAAM,GACTd,SAAS,CAAC6B,WAAD,EAAcrB,OAAO,GAAGoB,mBAAxB,CADA,GAETC,WAFJ;EAGD;;EAED,SAASC,YAAT,CAAsBZ,IAAtB,EAA4B;IAC1B,IAAIS,iBAAiB,GAAGT,IAAI,GAAGP,YAA/B;IAAA,IACIiB,mBAAmB,GAAGV,IAAI,GAAGN,cADjC,CAD0B,CAI1B;IACA;IACA;;IACA,OAAQD,YAAY,KAAKU,SAAjB,IAA+BM,iBAAiB,IAAIvB,IAApD,IACLuB,iBAAiB,GAAG,CADf,IACsBb,MAAM,IAAIc,mBAAmB,IAAIpB,OAD/D;EAED;;EAED,SAASiB,YAAT,GAAwB;IACtB,IAAIP,IAAI,GAAGxB,GAAG,EAAd;;IACA,IAAIoC,YAAY,CAACZ,IAAD,CAAhB,EAAwB;MACtB,OAAOa,YAAY,CAACb,IAAD,CAAnB;IACD,CAJqB,CAKtB;;;IACAR,OAAO,GAAGc,UAAU,CAACC,YAAD,EAAeC,aAAa,CAACR,IAAD,CAA5B,CAApB;EACD;;EAED,SAASa,YAAT,CAAsBb,IAAtB,EAA4B;IAC1BR,OAAO,GAAGW,SAAV,CAD0B,CAG1B;IACA;;IACA,IAAIN,QAAQ,IAAIT,QAAhB,EAA0B;MACxB,OAAOW,UAAU,CAACC,IAAD,CAAjB;IACD;;IACDZ,QAAQ,GAAGC,QAAQ,GAAGc,SAAtB;IACA,OAAOZ,MAAP;EACD;;EAED,SAASuB,MAAT,GAAkB;IAChB,IAAItB,OAAO,KAAKW,SAAhB,EAA2B;MACzBY,YAAY,CAACvB,OAAD,CAAZ;IACD;;IACDE,cAAc,GAAG,CAAjB;IACAN,QAAQ,GAAGK,YAAY,GAAGJ,QAAQ,GAAGG,OAAO,GAAGW,SAA/C;EACD;;EAED,SAASa,KAAT,GAAiB;IACf,OAAOxB,OAAO,KAAKW,SAAZ,GAAwBZ,MAAxB,GAAiCsB,YAAY,CAACrC,GAAG,EAAJ,CAApD;EACD;;EAED,SAASyC,SAAT,GAAqB;IACnB,IAAIjB,IAAI,GAAGxB,GAAG,EAAd;IAAA,IACI0C,UAAU,GAAGN,YAAY,CAACZ,IAAD,CAD7B;IAGAZ,QAAQ,GAAG+B,SAAX;IACA9B,QAAQ,GAAG,IAAX;IACAI,YAAY,GAAGO,IAAf;;IAEA,IAAIkB,UAAJ,EAAgB;MACd,IAAI1B,OAAO,KAAKW,SAAhB,EAA2B;QACzB,OAAOE,WAAW,CAACZ,YAAD,CAAlB;MACD;;MACD,IAAIG,MAAJ,EAAY;QACV;QACAmB,YAAY,CAACvB,OAAD,CAAZ;QACAA,OAAO,GAAGc,UAAU,CAACC,YAAD,EAAerB,IAAf,CAApB;QACA,OAAOa,UAAU,CAACN,YAAD,CAAjB;MACD;IACF;;IACD,IAAID,OAAO,KAAKW,SAAhB,EAA2B;MACzBX,OAAO,GAAGc,UAAU,CAACC,YAAD,EAAerB,IAAf,CAApB;IACD;;IACD,OAAOK,MAAP;EACD;;EACD0B,SAAS,CAACH,MAAV,GAAmBA,MAAnB;EACAG,SAAS,CAACD,KAAV,GAAkBA,KAAlB;EACA,OAAOC,SAAP;AACD;;AAEDG,MAAM,CAACC,OAAP,GAAiBrC,QAAjB"}, "metadata": {}, "sourceType": "script"}