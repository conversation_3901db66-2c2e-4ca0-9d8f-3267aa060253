{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nexport default function isDocumentVisible() {\n  if (isBrowser) {\n    return document.visibilityState !== 'hidden';\n  }\n\n  return true;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDocumentVisible", "document", "visibilityState"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nexport default function isDocumentVisible() {\n  if (isBrowser) {\n    return document.visibilityState !== 'hidden';\n  }\n  return true;\n}"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,0BAAtB;AACA,eAAe,SAASC,iBAAT,GAA6B;EAC1C,IAAID,SAAJ,EAAe;IACb,OAAOE,QAAQ,CAACC,eAAT,KAA6B,QAApC;EACD;;EACD,OAAO,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}