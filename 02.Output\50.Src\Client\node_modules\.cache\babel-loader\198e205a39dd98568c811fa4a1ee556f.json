{"ast": null, "code": "var coreJsData = require('./_coreJsData');\n/** Used to detect methods masquerading as native. */\n\n\nvar maskSrcKey = function () {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? 'Symbol(src)_1.' + uid : '';\n}();\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\n\n\nfunction isMasked(func) {\n  return !!maskSrcKey && maskSrcKey in func;\n}\n\nmodule.exports = isMasked;", "map": {"version": 3, "names": ["coreJsData", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uid", "exec", "keys", "IE_PROTO", "isMasked", "func", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_isMasked.js"], "sourcesContent": ["var coreJsData = require('./_coreJsData');\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nmodule.exports = isMasked;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;;;AACA,IAAIC,UAAU,GAAI,YAAW;EAC3B,IAAIC,GAAG,GAAG,SAASC,IAAT,CAAcJ,UAAU,IAAIA,UAAU,CAACK,IAAzB,IAAiCL,UAAU,CAACK,IAAX,CAAgBC,QAAjD,IAA6D,EAA3E,CAAV;EACA,OAAOH,GAAG,GAAI,mBAAmBA,GAAvB,GAA8B,EAAxC;AACD,CAHiB,EAAlB;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASI,QAAT,CAAkBC,IAAlB,EAAwB;EACtB,OAAO,CAAC,CAACN,UAAF,IAAiBA,UAAU,IAAIM,IAAtC;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBH,QAAjB"}, "metadata": {}, "sourceType": "script"}