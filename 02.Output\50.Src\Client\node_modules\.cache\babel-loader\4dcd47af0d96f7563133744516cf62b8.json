{"ast": null, "code": "'use strict';\n\nvar random = require('../../utils/random'),\n    urlUtils = require('../../utils/url');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\n\nvar form, area;\n\nfunction createIframe(id) {\n  debug('createIframe', id);\n\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\n\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n  global.document.body.appendChild(form);\n}\n\nmodule.exports = function (url, payload, callback) {\n  debug(url, payload);\n\n  if (!form) {\n    createForm();\n  }\n\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n\n  try {\n    area.value = payload;\n  } catch (e) {// seriously broken browsers get here\n  }\n\n  form.submit();\n\n  var completed = function completed(err) {\n    debug('completed', id, err);\n\n    if (!iframe.onerror) {\n      return;\n    }\n\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null; // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n\n    setTimeout(function () {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = ''; // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n\n    callback(err);\n  };\n\n  iframe.onerror = function () {\n    debug('onerror', id);\n    completed();\n  };\n\n  iframe.onload = function () {\n    debug('onload', id);\n    completed();\n  };\n\n  iframe.onreadystatechange = function (e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n\n  return function () {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};", "map": {"version": 3, "names": ["random", "require", "urlUtils", "debug", "process", "env", "NODE_ENV", "form", "area", "createIframe", "id", "global", "document", "createElement", "x", "iframe", "name", "createForm", "style", "display", "position", "method", "enctype", "acceptCharset", "append<PERSON><PERSON><PERSON>", "body", "module", "exports", "url", "payload", "callback", "string", "target", "action", "<PERSON><PERSON><PERSON><PERSON>", "addPath", "value", "e", "submit", "completed", "err", "onerror", "onreadystatechange", "onload", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "readyState", "Error"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/sender/jsonp.js"], "sourcesContent": ["'use strict';\n\nvar random = require('../../utils/random')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\n\nvar form, area;\n\nfunction createIframe(id) {\n  debug('createIframe', id);\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\n\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n\n  global.document.body.appendChild(form);\n}\n\nmodule.exports = function(url, payload, callback) {\n  debug(url, payload);\n  if (!form) {\n    createForm();\n  }\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n\n  try {\n    area.value = payload;\n  } catch (e) {\n    // seriously broken browsers get here\n  }\n  form.submit();\n\n  var completed = function(err) {\n    debug('completed', id, err);\n    if (!iframe.onerror) {\n      return;\n    }\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null;\n    // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n    setTimeout(function() {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = '';\n    // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n    callback(err);\n  };\n  iframe.onerror = function() {\n    debug('onerror', id);\n    completed();\n  };\n  iframe.onload = function() {\n    debug('onload', id);\n    completed();\n  };\n  iframe.onreadystatechange = function(e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n  return function() {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,oBAAD,CAApB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,iBAAD,CADtB;;AAIA,IAAIE,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAD,CAAP,CAAiB,4BAAjB,CAAR;AACD;;AAED,IAAIM,IAAJ,EAAUC,IAAV;;AAEA,SAASC,YAAT,CAAsBC,EAAtB,EAA0B;EACxBP,KAAK,CAAC,cAAD,EAAiBO,EAAjB,CAAL;;EACA,IAAI;IACF;IACA,OAAOC,MAAM,CAACC,QAAP,CAAgBC,aAAhB,CAA8B,mBAAmBH,EAAnB,GAAwB,IAAtD,CAAP;EACD,CAHD,CAGE,OAAOI,CAAP,EAAU;IACV,IAAIC,MAAM,GAAGJ,MAAM,CAACC,QAAP,CAAgBC,aAAhB,CAA8B,QAA9B,CAAb;IACAE,MAAM,CAACC,IAAP,GAAcN,EAAd;IACA,OAAOK,MAAP;EACD;AACF;;AAED,SAASE,UAAT,GAAsB;EACpBd,KAAK,CAAC,YAAD,CAAL;EACAI,IAAI,GAAGI,MAAM,CAACC,QAAP,CAAgBC,aAAhB,CAA8B,MAA9B,CAAP;EACAN,IAAI,CAACW,KAAL,CAAWC,OAAX,GAAqB,MAArB;EACAZ,IAAI,CAACW,KAAL,CAAWE,QAAX,GAAsB,UAAtB;EACAb,IAAI,CAACc,MAAL,GAAc,MAAd;EACAd,IAAI,CAACe,OAAL,GAAe,mCAAf;EACAf,IAAI,CAACgB,aAAL,GAAqB,OAArB;EAEAf,IAAI,GAAGG,MAAM,CAACC,QAAP,CAAgBC,aAAhB,CAA8B,UAA9B,CAAP;EACAL,IAAI,CAACQ,IAAL,GAAY,GAAZ;EACAT,IAAI,CAACiB,WAAL,CAAiBhB,IAAjB;EAEAG,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAAqBD,WAArB,CAAiCjB,IAAjC;AACD;;AAEDmB,MAAM,CAACC,OAAP,GAAiB,UAASC,GAAT,EAAcC,OAAd,EAAuBC,QAAvB,EAAiC;EAChD3B,KAAK,CAACyB,GAAD,EAAMC,OAAN,CAAL;;EACA,IAAI,CAACtB,IAAL,EAAW;IACTU,UAAU;EACX;;EACD,IAAIP,EAAE,GAAG,MAAMV,MAAM,CAAC+B,MAAP,CAAc,CAAd,CAAf;EACAxB,IAAI,CAACyB,MAAL,GAActB,EAAd;EACAH,IAAI,CAAC0B,MAAL,GAAc/B,QAAQ,CAACgC,QAAT,CAAkBhC,QAAQ,CAACiC,OAAT,CAAiBP,GAAjB,EAAsB,aAAtB,CAAlB,EAAwD,OAAOlB,EAA/D,CAAd;EAEA,IAAIK,MAAM,GAAGN,YAAY,CAACC,EAAD,CAAzB;EACAK,MAAM,CAACL,EAAP,GAAYA,EAAZ;EACAK,MAAM,CAACG,KAAP,CAAaC,OAAb,GAAuB,MAAvB;EACAZ,IAAI,CAACiB,WAAL,CAAiBT,MAAjB;;EAEA,IAAI;IACFP,IAAI,CAAC4B,KAAL,GAAaP,OAAb;EACD,CAFD,CAEE,OAAOQ,CAAP,EAAU,CACV;EACD;;EACD9B,IAAI,CAAC+B,MAAL;;EAEA,IAAIC,SAAS,GAAG,SAAZA,SAAY,CAASC,GAAT,EAAc;IAC5BrC,KAAK,CAAC,WAAD,EAAcO,EAAd,EAAkB8B,GAAlB,CAAL;;IACA,IAAI,CAACzB,MAAM,CAAC0B,OAAZ,EAAqB;MACnB;IACD;;IACD1B,MAAM,CAAC2B,kBAAP,GAA4B3B,MAAM,CAAC0B,OAAP,GAAiB1B,MAAM,CAAC4B,MAAP,GAAgB,IAA7D,CAL4B,CAM5B;IACA;;IACAC,UAAU,CAAC,YAAW;MACpBzC,KAAK,CAAC,aAAD,EAAgBO,EAAhB,CAAL;MACAK,MAAM,CAAC8B,UAAP,CAAkBC,WAAlB,CAA8B/B,MAA9B;MACAA,MAAM,GAAG,IAAT;IACD,CAJS,EAIP,GAJO,CAAV;IAKAP,IAAI,CAAC4B,KAAL,GAAa,EAAb,CAb4B,CAc5B;IACA;;IACAN,QAAQ,CAACU,GAAD,CAAR;EACD,CAjBD;;EAkBAzB,MAAM,CAAC0B,OAAP,GAAiB,YAAW;IAC1BtC,KAAK,CAAC,SAAD,EAAYO,EAAZ,CAAL;IACA6B,SAAS;EACV,CAHD;;EAIAxB,MAAM,CAAC4B,MAAP,GAAgB,YAAW;IACzBxC,KAAK,CAAC,QAAD,EAAWO,EAAX,CAAL;IACA6B,SAAS;EACV,CAHD;;EAIAxB,MAAM,CAAC2B,kBAAP,GAA4B,UAASL,CAAT,EAAY;IACtClC,KAAK,CAAC,oBAAD,EAAuBO,EAAvB,EAA2BK,MAAM,CAACgC,UAAlC,EAA8CV,CAA9C,CAAL;;IACA,IAAItB,MAAM,CAACgC,UAAP,KAAsB,UAA1B,EAAsC;MACpCR,SAAS;IACV;EACF,CALD;;EAMA,OAAO,YAAW;IAChBpC,KAAK,CAAC,SAAD,EAAYO,EAAZ,CAAL;IACA6B,SAAS,CAAC,IAAIS,KAAJ,CAAU,SAAV,CAAD,CAAT;EACD,CAHD;AAID,CAzDD"}, "metadata": {}, "sourceType": "script"}