{"ast": null, "code": "/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function () {\n  'use strict'; // Exit early if we're not running in a browser.\n\n  if (typeof window !== 'object') {\n    return;\n  } // Exit early if all IntersectionObserver and IntersectionObserverEntry\n  // features are natively supported.\n\n\n  if ('IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n    // Minimal polyfill for Edge 15's lack of `isIntersecting`\n    // See: https://github.com/w3c/IntersectionObserver/issues/211\n    if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n      Object.defineProperty(window.IntersectionObserverEntry.prototype, 'isIntersecting', {\n        get: function get() {\n          return this.intersectionRatio > 0;\n        }\n      });\n    }\n\n    return;\n  }\n  /**\n   * Returns the embedding frame element, if any.\n   * @param {!Document} doc\n   * @return {!Element}\n   */\n\n\n  function getFrameElement(doc) {\n    try {\n      return doc.defaultView && doc.defaultView.frameElement || null;\n    } catch (e) {\n      // Ignore the error.\n      return null;\n    }\n  }\n  /**\n   * A local reference to the root document.\n   */\n\n\n  var document = function (startDoc) {\n    var doc = startDoc;\n    var frame = getFrameElement(doc);\n\n    while (frame) {\n      doc = frame.ownerDocument;\n      frame = getFrameElement(doc);\n    }\n\n    return doc;\n  }(window.document);\n  /**\n   * An IntersectionObserver registry. This registry exists to hold a strong\n   * reference to IntersectionObserver instances currently observing a target\n   * element. Without this registry, instances without another reference may be\n   * garbage collected.\n   */\n\n\n  var registry = [];\n  /**\n   * The signal updater for cross-origin intersection. When not null, it means\n   * that the polyfill is configured to work in a cross-origin mode.\n   * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n   */\n\n  var crossOriginUpdater = null;\n  /**\n   * The current cross-origin intersection. Only used in the cross-origin mode.\n   * @type {DOMRect|ClientRect}\n   */\n\n  var crossOriginRect = null;\n  /**\n   * Creates the global IntersectionObserverEntry constructor.\n   * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n   * @param {Object} entry A dictionary of instance properties.\n   * @constructor\n   */\n\n  function IntersectionObserverEntry(entry) {\n    this.time = entry.time;\n    this.target = entry.target;\n    this.rootBounds = ensureDOMRect(entry.rootBounds);\n    this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n    this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n    this.isIntersecting = !!entry.intersectionRect; // Calculates the intersection ratio.\n\n    var targetRect = this.boundingClientRect;\n    var targetArea = targetRect.width * targetRect.height;\n    var intersectionRect = this.intersectionRect;\n    var intersectionArea = intersectionRect.width * intersectionRect.height; // Sets intersection ratio.\n\n    if (targetArea) {\n      // Round the intersection ratio to avoid floating point math issues:\n      // https://github.com/w3c/IntersectionObserver/issues/324\n      this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n    } else {\n      // If area is zero and is intersecting, sets to 1, otherwise to 0\n      this.intersectionRatio = this.isIntersecting ? 1 : 0;\n    }\n  }\n  /**\n   * Creates the global IntersectionObserver constructor.\n   * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n   * @param {Function} callback The function to be invoked after intersection\n   *     changes have queued. The function is not invoked if the queue has\n   *     been emptied by calling the `takeRecords` method.\n   * @param {Object=} opt_options Optional configuration options.\n   * @constructor\n   */\n\n\n  function IntersectionObserver(callback, opt_options) {\n    var options = opt_options || {};\n\n    if (typeof callback != 'function') {\n      throw new Error('callback must be a function');\n    }\n\n    if (options.root && options.root.nodeType != 1 && options.root.nodeType != 9) {\n      throw new Error('root must be a Document or Element');\n    } // Binds and throttles `this._checkForIntersections`.\n\n\n    this._checkForIntersections = throttle(this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT); // Private properties.\n\n    this._callback = callback;\n    this._observationTargets = [];\n    this._queuedEntries = [];\n    this._rootMarginValues = this._parseRootMargin(options.rootMargin); // Public properties.\n\n    this.thresholds = this._initThresholds(options.threshold);\n    this.root = options.root || null;\n    this.rootMargin = this._rootMarginValues.map(function (margin) {\n      return margin.value + margin.unit;\n    }).join(' ');\n    /** @private @const {!Array<!Document>} */\n\n    this._monitoringDocuments = [];\n    /** @private @const {!Array<function()>} */\n\n    this._monitoringUnsubscribes = [];\n  }\n  /**\n   * The minimum interval within which the document will be checked for\n   * intersection changes.\n   */\n\n\n  IntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n  /**\n   * The frequency in which the polyfill polls for intersection changes.\n   * this can be updated on a per instance basis and must be set prior to\n   * calling `observe` on the first target.\n   */\n\n  IntersectionObserver.prototype.POLL_INTERVAL = null;\n  /**\n   * Use a mutation observer on the root element\n   * to detect intersection changes.\n   */\n\n  IntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n  /**\n   * Sets up the polyfill in the cross-origin mode. The result is the\n   * updater function that accepts two arguments: `boundingClientRect` and\n   * `intersectionRect` - just as these fields would be available to the\n   * parent via `IntersectionObserverEntry`. This function should be called\n   * each time the iframe receives intersection information from the parent\n   * window, e.g. via messaging.\n   * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n   */\n\n  IntersectionObserver._setupCrossOriginUpdater = function () {\n    if (!crossOriginUpdater) {\n      /**\n       * @param {DOMRect|ClientRect} boundingClientRect\n       * @param {DOMRect|ClientRect} intersectionRect\n       */\n      crossOriginUpdater = function crossOriginUpdater(boundingClientRect, intersectionRect) {\n        if (!boundingClientRect || !intersectionRect) {\n          crossOriginRect = getEmptyRect();\n        } else {\n          crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n        }\n\n        registry.forEach(function (observer) {\n          observer._checkForIntersections();\n        });\n      };\n    }\n\n    return crossOriginUpdater;\n  };\n  /**\n   * Resets the cross-origin mode.\n   */\n\n\n  IntersectionObserver._resetCrossOriginUpdater = function () {\n    crossOriginUpdater = null;\n    crossOriginRect = null;\n  };\n  /**\n   * Starts observing a target element for intersection changes based on\n   * the thresholds values.\n   * @param {Element} target The DOM element to observe.\n   */\n\n\n  IntersectionObserver.prototype.observe = function (target) {\n    var isTargetAlreadyObserved = this._observationTargets.some(function (item) {\n      return item.element == target;\n    });\n\n    if (isTargetAlreadyObserved) {\n      return;\n    }\n\n    if (!(target && target.nodeType == 1)) {\n      throw new Error('target must be an Element');\n    }\n\n    this._registerInstance();\n\n    this._observationTargets.push({\n      element: target,\n      entry: null\n    });\n\n    this._monitorIntersections(target.ownerDocument);\n\n    this._checkForIntersections();\n  };\n  /**\n   * Stops observing a target element for intersection changes.\n   * @param {Element} target The DOM element to observe.\n   */\n\n\n  IntersectionObserver.prototype.unobserve = function (target) {\n    this._observationTargets = this._observationTargets.filter(function (item) {\n      return item.element != target;\n    });\n\n    this._unmonitorIntersections(target.ownerDocument);\n\n    if (this._observationTargets.length == 0) {\n      this._unregisterInstance();\n    }\n  };\n  /**\n   * Stops observing all target elements for intersection changes.\n   */\n\n\n  IntersectionObserver.prototype.disconnect = function () {\n    this._observationTargets = [];\n\n    this._unmonitorAllIntersections();\n\n    this._unregisterInstance();\n  };\n  /**\n   * Returns any queue entries that have not yet been reported to the\n   * callback and clears the queue. This can be used in conjunction with the\n   * callback to obtain the absolute most up-to-date intersection information.\n   * @return {Array} The currently queued entries.\n   */\n\n\n  IntersectionObserver.prototype.takeRecords = function () {\n    var records = this._queuedEntries.slice();\n\n    this._queuedEntries = [];\n    return records;\n  };\n  /**\n   * Accepts the threshold value from the user configuration object and\n   * returns a sorted array of unique threshold values. If a value is not\n   * between 0 and 1 and error is thrown.\n   * @private\n   * @param {Array|number=} opt_threshold An optional threshold value or\n   *     a list of threshold values, defaulting to [0].\n   * @return {Array} A sorted list of unique and valid threshold values.\n   */\n\n\n  IntersectionObserver.prototype._initThresholds = function (opt_threshold) {\n    var threshold = opt_threshold || [0];\n    if (!Array.isArray(threshold)) threshold = [threshold];\n    return threshold.sort().filter(function (t, i, a) {\n      if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n        throw new Error('threshold must be a number between 0 and 1 inclusively');\n      }\n\n      return t !== a[i - 1];\n    });\n  };\n  /**\n   * Accepts the rootMargin value from the user configuration object\n   * and returns an array of the four margin values as an object containing\n   * the value and unit properties. If any of the values are not properly\n   * formatted or use a unit other than px or %, and error is thrown.\n   * @private\n   * @param {string=} opt_rootMargin An optional rootMargin value,\n   *     defaulting to '0px'.\n   * @return {Array<Object>} An array of margin objects with the keys\n   *     value and unit.\n   */\n\n\n  IntersectionObserver.prototype._parseRootMargin = function (opt_rootMargin) {\n    var marginString = opt_rootMargin || '0px';\n    var margins = marginString.split(/\\s+/).map(function (margin) {\n      var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n\n      if (!parts) {\n        throw new Error('rootMargin must be specified in pixels or percent');\n      }\n\n      return {\n        value: parseFloat(parts[1]),\n        unit: parts[2]\n      };\n    }); // Handles shorthand.\n\n    margins[1] = margins[1] || margins[0];\n    margins[2] = margins[2] || margins[0];\n    margins[3] = margins[3] || margins[1];\n    return margins;\n  };\n  /**\n   * Starts polling for intersection changes if the polling is not already\n   * happening, and if the page's visibility state is visible.\n   * @param {!Document} doc\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._monitorIntersections = function (doc) {\n    var win = doc.defaultView;\n\n    if (!win) {\n      // Already destroyed.\n      return;\n    }\n\n    if (this._monitoringDocuments.indexOf(doc) != -1) {\n      // Already monitoring.\n      return;\n    } // Private state for monitoring.\n\n\n    var callback = this._checkForIntersections;\n    var monitoringInterval = null;\n    var domObserver = null; // If a poll interval is set, use polling instead of listening to\n    // resize and scroll events or DOM mutations.\n\n    if (this.POLL_INTERVAL) {\n      monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n    } else {\n      addEvent(win, 'resize', callback, true);\n      addEvent(doc, 'scroll', callback, true);\n\n      if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n        domObserver = new win.MutationObserver(callback);\n        domObserver.observe(doc, {\n          attributes: true,\n          childList: true,\n          characterData: true,\n          subtree: true\n        });\n      }\n    }\n\n    this._monitoringDocuments.push(doc);\n\n    this._monitoringUnsubscribes.push(function () {\n      // Get the window object again. When a friendly iframe is destroyed, it\n      // will be null.\n      var win = doc.defaultView;\n\n      if (win) {\n        if (monitoringInterval) {\n          win.clearInterval(monitoringInterval);\n        }\n\n        removeEvent(win, 'resize', callback, true);\n      }\n\n      removeEvent(doc, 'scroll', callback, true);\n\n      if (domObserver) {\n        domObserver.disconnect();\n      }\n    }); // Also monitor the parent.\n\n\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;\n\n    if (doc != rootDoc) {\n      var frame = getFrameElement(doc);\n\n      if (frame) {\n        this._monitorIntersections(frame.ownerDocument);\n      }\n    }\n  };\n  /**\n   * Stops polling for intersection changes.\n   * @param {!Document} doc\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._unmonitorIntersections = function (doc) {\n    var index = this._monitoringDocuments.indexOf(doc);\n\n    if (index == -1) {\n      return;\n    }\n\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document; // Check if any dependent targets are still remaining.\n\n    var hasDependentTargets = this._observationTargets.some(function (item) {\n      var itemDoc = item.element.ownerDocument; // Target is in this context.\n\n      if (itemDoc == doc) {\n        return true;\n      } // Target is nested in this context.\n\n\n      while (itemDoc && itemDoc != rootDoc) {\n        var frame = getFrameElement(itemDoc);\n        itemDoc = frame && frame.ownerDocument;\n\n        if (itemDoc == doc) {\n          return true;\n        }\n      }\n\n      return false;\n    });\n\n    if (hasDependentTargets) {\n      return;\n    } // Unsubscribe.\n\n\n    var unsubscribe = this._monitoringUnsubscribes[index];\n\n    this._monitoringDocuments.splice(index, 1);\n\n    this._monitoringUnsubscribes.splice(index, 1);\n\n    unsubscribe(); // Also unmonitor the parent.\n\n    if (doc != rootDoc) {\n      var frame = getFrameElement(doc);\n\n      if (frame) {\n        this._unmonitorIntersections(frame.ownerDocument);\n      }\n    }\n  };\n  /**\n   * Stops polling for intersection changes.\n   * @param {!Document} doc\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._unmonitorAllIntersections = function () {\n    var unsubscribes = this._monitoringUnsubscribes.slice(0);\n\n    this._monitoringDocuments.length = 0;\n    this._monitoringUnsubscribes.length = 0;\n\n    for (var i = 0; i < unsubscribes.length; i++) {\n      unsubscribes[i]();\n    }\n  };\n  /**\n   * Scans each observation target for intersection changes and adds them\n   * to the internal entries queue. If new entries are found, it\n   * schedules the callback to be invoked.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._checkForIntersections = function () {\n    if (!this.root && crossOriginUpdater && !crossOriginRect) {\n      // Cross origin monitoring, but no initial data available yet.\n      return;\n    }\n\n    var rootIsInDom = this._rootIsInDom();\n\n    var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n    this._observationTargets.forEach(function (item) {\n      var target = item.element;\n      var targetRect = getBoundingClientRect(target);\n\n      var rootContainsTarget = this._rootContainsTarget(target);\n\n      var oldEntry = item.entry;\n\n      var intersectionRect = rootIsInDom && rootContainsTarget && this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n      var rootBounds = null;\n\n      if (!this._rootContainsTarget(target)) {\n        rootBounds = getEmptyRect();\n      } else if (!crossOriginUpdater || this.root) {\n        rootBounds = rootRect;\n      }\n\n      var newEntry = item.entry = new IntersectionObserverEntry({\n        time: now(),\n        target: target,\n        boundingClientRect: targetRect,\n        rootBounds: rootBounds,\n        intersectionRect: intersectionRect\n      });\n\n      if (!oldEntry) {\n        this._queuedEntries.push(newEntry);\n      } else if (rootIsInDom && rootContainsTarget) {\n        // If the new entry intersection ratio has crossed any of the\n        // thresholds, add a new entry.\n        if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n          this._queuedEntries.push(newEntry);\n        }\n      } else {\n        // If the root is not in the DOM or target is not contained within\n        // root but the previous entry for this target had an intersection,\n        // add a new record indicating removal.\n        if (oldEntry && oldEntry.isIntersecting) {\n          this._queuedEntries.push(newEntry);\n        }\n      }\n    }, this);\n\n    if (this._queuedEntries.length) {\n      this._callback(this.takeRecords(), this);\n    }\n  };\n  /**\n   * Accepts a target and root rect computes the intersection between then\n   * following the algorithm in the spec.\n   * TODO(philipwalton): at this time clip-path is not considered.\n   * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n   * @param {Element} target The target DOM element\n   * @param {Object} targetRect The bounding rect of the target.\n   * @param {Object} rootRect The bounding rect of the root after being\n   *     expanded by the rootMargin value.\n   * @return {?Object} The final intersection rect object or undefined if no\n   *     intersection is found.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._computeTargetAndRootIntersection = function (target, targetRect, rootRect) {\n    // If the element isn't displayed, an intersection can't happen.\n    if (window.getComputedStyle(target).display == 'none') return;\n    var intersectionRect = targetRect;\n    var parent = getParentNode(target);\n    var atRoot = false;\n\n    while (!atRoot && parent) {\n      var parentRect = null;\n      var parentComputedStyle = parent.nodeType == 1 ? window.getComputedStyle(parent) : {}; // If the parent isn't displayed, an intersection can't happen.\n\n      if (parentComputedStyle.display == 'none') return null;\n\n      if (parent == this.root || parent.nodeType ==\n      /* DOCUMENT */\n      9) {\n        atRoot = true;\n\n        if (parent == this.root || parent == document) {\n          if (crossOriginUpdater && !this.root) {\n            if (!crossOriginRect || crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n              // A 0-size cross-origin intersection means no-intersection.\n              parent = null;\n              parentRect = null;\n              intersectionRect = null;\n            } else {\n              parentRect = crossOriginRect;\n            }\n          } else {\n            parentRect = rootRect;\n          }\n        } else {\n          // Check if there's a frame that can be navigated to.\n          var frame = getParentNode(parent);\n          var frameRect = frame && getBoundingClientRect(frame);\n\n          var frameIntersect = frame && this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n\n          if (frameRect && frameIntersect) {\n            parent = frame;\n            parentRect = convertFromParentRect(frameRect, frameIntersect);\n          } else {\n            parent = null;\n            intersectionRect = null;\n          }\n        }\n      } else {\n        // If the element has a non-visible overflow, and it's not the <body>\n        // or <html> element, update the intersection rect.\n        // Note: <body> and <html> cannot be clipped to a rect that's not also\n        // the document rect, so no need to compute a new intersection.\n        var doc = parent.ownerDocument;\n\n        if (parent != doc.body && parent != doc.documentElement && parentComputedStyle.overflow != 'visible') {\n          parentRect = getBoundingClientRect(parent);\n        }\n      } // If either of the above conditionals set a new parentRect,\n      // calculate new intersection data.\n\n\n      if (parentRect) {\n        intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n      }\n\n      if (!intersectionRect) break;\n      parent = parent && getParentNode(parent);\n    }\n\n    return intersectionRect;\n  };\n  /**\n   * Returns the root rect after being expanded by the rootMargin value.\n   * @return {ClientRect} The expanded root rect.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._getRootRect = function () {\n    var rootRect;\n\n    if (this.root && !isDoc(this.root)) {\n      rootRect = getBoundingClientRect(this.root);\n    } else {\n      // Use <html>/<body> instead of window since scroll bars affect size.\n      var doc = isDoc(this.root) ? this.root : document;\n      var html = doc.documentElement;\n      var body = doc.body;\n      rootRect = {\n        top: 0,\n        left: 0,\n        right: html.clientWidth || body.clientWidth,\n        width: html.clientWidth || body.clientWidth,\n        bottom: html.clientHeight || body.clientHeight,\n        height: html.clientHeight || body.clientHeight\n      };\n    }\n\n    return this._expandRectByRootMargin(rootRect);\n  };\n  /**\n   * Accepts a rect and expands it by the rootMargin value.\n   * @param {DOMRect|ClientRect} rect The rect object to expand.\n   * @return {ClientRect} The expanded rect.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._expandRectByRootMargin = function (rect) {\n    var margins = this._rootMarginValues.map(function (margin, i) {\n      return margin.unit == 'px' ? margin.value : margin.value * (i % 2 ? rect.width : rect.height) / 100;\n    });\n\n    var newRect = {\n      top: rect.top - margins[0],\n      right: rect.right + margins[1],\n      bottom: rect.bottom + margins[2],\n      left: rect.left - margins[3]\n    };\n    newRect.width = newRect.right - newRect.left;\n    newRect.height = newRect.bottom - newRect.top;\n    return newRect;\n  };\n  /**\n   * Accepts an old and new entry and returns true if at least one of the\n   * threshold values has been crossed.\n   * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n   *    particular target element or null if no previous entry exists.\n   * @param {IntersectionObserverEntry} newEntry The current entry for a\n   *    particular target element.\n   * @return {boolean} Returns true if a any threshold has been crossed.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._hasCrossedThreshold = function (oldEntry, newEntry) {\n    // To make comparing easier, an entry that has a ratio of 0\n    // but does not actually intersect is given a value of -1\n    var oldRatio = oldEntry && oldEntry.isIntersecting ? oldEntry.intersectionRatio || 0 : -1;\n    var newRatio = newEntry.isIntersecting ? newEntry.intersectionRatio || 0 : -1; // Ignore unchanged ratios\n\n    if (oldRatio === newRatio) return;\n\n    for (var i = 0; i < this.thresholds.length; i++) {\n      var threshold = this.thresholds[i]; // Return true if an entry matches a threshold or if the new ratio\n      // and the old ratio are on the opposite sides of a threshold.\n\n      if (threshold == oldRatio || threshold == newRatio || threshold < oldRatio !== threshold < newRatio) {\n        return true;\n      }\n    }\n  };\n  /**\n   * Returns whether or not the root element is an element and is in the DOM.\n   * @return {boolean} True if the root element is an element and is in the DOM.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._rootIsInDom = function () {\n    return !this.root || containsDeep(document, this.root);\n  };\n  /**\n   * Returns whether or not the target element is a child of root.\n   * @param {Element} target The target element to check.\n   * @return {boolean} True if the target element is a child of root.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._rootContainsTarget = function (target) {\n    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document;\n    return containsDeep(rootDoc, target) && (!this.root || rootDoc == target.ownerDocument);\n  };\n  /**\n   * Adds the instance to the global IntersectionObserver registry if it isn't\n   * already present.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._registerInstance = function () {\n    if (registry.indexOf(this) < 0) {\n      registry.push(this);\n    }\n  };\n  /**\n   * Removes the instance from the global IntersectionObserver registry.\n   * @private\n   */\n\n\n  IntersectionObserver.prototype._unregisterInstance = function () {\n    var index = registry.indexOf(this);\n    if (index != -1) registry.splice(index, 1);\n  };\n  /**\n   * Returns the result of the performance.now() method or null in browsers\n   * that don't support the API.\n   * @return {number} The elapsed time since the page was requested.\n   */\n\n\n  function now() {\n    return window.performance && performance.now && performance.now();\n  }\n  /**\n   * Throttles a function and delays its execution, so it's only called at most\n   * once within a given time period.\n   * @param {Function} fn The function to throttle.\n   * @param {number} timeout The amount of time that must pass before the\n   *     function can be called again.\n   * @return {Function} The throttled function.\n   */\n\n\n  function throttle(fn, timeout) {\n    var timer = null;\n    return function () {\n      if (!timer) {\n        timer = setTimeout(function () {\n          fn();\n          timer = null;\n        }, timeout);\n      }\n    };\n  }\n  /**\n   * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n   * @param {Node} node The DOM node to add the event handler to.\n   * @param {string} event The event name.\n   * @param {Function} fn The event handler to add.\n   * @param {boolean} opt_useCapture Optionally adds the even to the capture\n   *     phase. Note: this only works in modern browsers.\n   */\n\n\n  function addEvent(node, event, fn, opt_useCapture) {\n    if (typeof node.addEventListener == 'function') {\n      node.addEventListener(event, fn, opt_useCapture || false);\n    } else if (typeof node.attachEvent == 'function') {\n      node.attachEvent('on' + event, fn);\n    }\n  }\n  /**\n   * Removes a previously added event handler from a DOM node.\n   * @param {Node} node The DOM node to remove the event handler from.\n   * @param {string} event The event name.\n   * @param {Function} fn The event handler to remove.\n   * @param {boolean} opt_useCapture If the event handler was added with this\n   *     flag set to true, it should be set to true here in order to remove it.\n   */\n\n\n  function removeEvent(node, event, fn, opt_useCapture) {\n    if (typeof node.removeEventListener == 'function') {\n      node.removeEventListener(event, fn, opt_useCapture || false);\n    } else if (typeof node.detachEvent == 'function') {\n      node.detachEvent('on' + event, fn);\n    }\n  }\n  /**\n   * Returns the intersection between two rect objects.\n   * @param {Object} rect1 The first rect.\n   * @param {Object} rect2 The second rect.\n   * @return {?Object|?ClientRect} The intersection rect or undefined if no\n   *     intersection is found.\n   */\n\n\n  function computeRectIntersection(rect1, rect2) {\n    var top = Math.max(rect1.top, rect2.top);\n    var bottom = Math.min(rect1.bottom, rect2.bottom);\n    var left = Math.max(rect1.left, rect2.left);\n    var right = Math.min(rect1.right, rect2.right);\n    var width = right - left;\n    var height = bottom - top;\n    return width >= 0 && height >= 0 && {\n      top: top,\n      bottom: bottom,\n      left: left,\n      right: right,\n      width: width,\n      height: height\n    } || null;\n  }\n  /**\n   * Shims the native getBoundingClientRect for compatibility with older IE.\n   * @param {Element} el The element whose bounding rect to get.\n   * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n   */\n\n\n  function getBoundingClientRect(el) {\n    var rect;\n\n    try {\n      rect = el.getBoundingClientRect();\n    } catch (err) {// Ignore Windows 7 IE11 \"Unspecified error\"\n      // https://github.com/w3c/IntersectionObserver/pull/205\n    }\n\n    if (!rect) return getEmptyRect(); // Older IE\n\n    if (!(rect.width && rect.height)) {\n      rect = {\n        top: rect.top,\n        right: rect.right,\n        bottom: rect.bottom,\n        left: rect.left,\n        width: rect.right - rect.left,\n        height: rect.bottom - rect.top\n      };\n    }\n\n    return rect;\n  }\n  /**\n   * Returns an empty rect object. An empty rect is returned when an element\n   * is not in the DOM.\n   * @return {ClientRect} The empty rect.\n   */\n\n\n  function getEmptyRect() {\n    return {\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  /**\n   * Ensure that the result has all of the necessary fields of the DOMRect.\n   * Specifically this ensures that `x` and `y` fields are set.\n   *\n   * @param {?DOMRect|?ClientRect} rect\n   * @return {?DOMRect}\n   */\n\n\n  function ensureDOMRect(rect) {\n    // A `DOMRect` object has `x` and `y` fields.\n    if (!rect || 'x' in rect) {\n      return rect;\n    } // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n    // for internally calculated Rect objects. For the purposes of\n    // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n    // for these fields.\n\n\n    return {\n      top: rect.top,\n      y: rect.top,\n      bottom: rect.bottom,\n      left: rect.left,\n      x: rect.left,\n      right: rect.right,\n      width: rect.width,\n      height: rect.height\n    };\n  }\n  /**\n   * Inverts the intersection and bounding rect from the parent (frame) BCR to\n   * the local BCR space.\n   * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n   * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n   * @return {ClientRect} The local root bounding rect for the parent's children.\n   */\n\n\n  function convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n    var top = parentIntersectionRect.top - parentBoundingRect.top;\n    var left = parentIntersectionRect.left - parentBoundingRect.left;\n    return {\n      top: top,\n      left: left,\n      height: parentIntersectionRect.height,\n      width: parentIntersectionRect.width,\n      bottom: top + parentIntersectionRect.height,\n      right: left + parentIntersectionRect.width\n    };\n  }\n  /**\n   * Checks to see if a parent element contains a child element (including inside\n   * shadow DOM).\n   * @param {Node} parent The parent element.\n   * @param {Node} child The child element.\n   * @return {boolean} True if the parent node contains the child node.\n   */\n\n\n  function containsDeep(parent, child) {\n    var node = child;\n\n    while (node) {\n      if (node == parent) return true;\n      node = getParentNode(node);\n    }\n\n    return false;\n  }\n  /**\n   * Gets the parent node of an element or its host element if the parent node\n   * is a shadow root.\n   * @param {Node} node The node whose parent to get.\n   * @return {Node|null} The parent node or null if no parent exists.\n   */\n\n\n  function getParentNode(node) {\n    var parent = node.parentNode;\n\n    if (node.nodeType ==\n    /* DOCUMENT */\n    9 && node != document) {\n      // If this node is a document node, look for the embedding frame.\n      return getFrameElement(node);\n    } // If the parent has element that is assigned through shadow root slot\n\n\n    if (parent && parent.assignedSlot) {\n      parent = parent.assignedSlot.parentNode;\n    }\n\n    if (parent && parent.nodeType == 11 && parent.host) {\n      // If the parent is a shadow root, return the host element.\n      return parent.host;\n    }\n\n    return parent;\n  }\n  /**\n   * Returns true if `node` is a Document.\n   * @param {!Node} node\n   * @returns {boolean}\n   */\n\n\n  function isDoc(node) {\n    return node && node.nodeType === 9;\n  } // Exposes the constructors globally.\n\n\n  window.IntersectionObserver = IntersectionObserver;\n  window.IntersectionObserverEntry = IntersectionObserverEntry;\n})();", "map": {"version": 3, "names": ["window", "IntersectionObserverEntry", "prototype", "Object", "defineProperty", "get", "intersectionRatio", "getFrameElement", "doc", "defaultView", "frameElement", "e", "document", "startDoc", "frame", "ownerDocument", "registry", "crossOriginUpdater", "crossOriginRect", "entry", "time", "target", "rootBounds", "ensureDOMRect", "boundingClientRect", "intersectionRect", "getEmptyRect", "isIntersecting", "targetRect", "targetArea", "width", "height", "intersectionArea", "Number", "toFixed", "IntersectionObserver", "callback", "opt_options", "options", "Error", "root", "nodeType", "_checkForIntersections", "throttle", "bind", "THROTTLE_TIMEOUT", "_callback", "_observationTargets", "_queuedEntries", "_rootMarginValues", "_parseR<PERSON>Margin", "rootMargin", "thresholds", "_initThresholds", "threshold", "map", "margin", "value", "unit", "join", "_monitoringDocuments", "_monitoringUnsubscribes", "POLL_INTERVAL", "USE_MUTATION_OBSERVER", "_setupCrossOriginUpdater", "convertFromParentRect", "for<PERSON>ach", "observer", "_resetCrossOriginUpdater", "observe", "isTargetAlreadyObserved", "some", "item", "element", "_registerInstance", "push", "_monitorIntersections", "unobserve", "filter", "_unmonitorIntersections", "length", "_unregisterInstance", "disconnect", "_unmonitorAllIntersections", "takeRecords", "records", "slice", "opt_threshold", "Array", "isArray", "sort", "t", "i", "a", "isNaN", "opt_rootMargin", "marginString", "margins", "split", "parts", "exec", "parseFloat", "win", "indexOf", "monitoringInterval", "domObserver", "setInterval", "addEvent", "MutationObserver", "attributes", "childList", "characterData", "subtree", "clearInterval", "removeEvent", "rootDoc", "index", "hasDependentTargets", "itemDoc", "unsubscribe", "splice", "unsubscribes", "rootIsInDom", "_rootIsInDom", "rootRect", "_getRootRect", "getBoundingClientRect", "rootContainsTarget", "_rootContainsTarget", "oldEntry", "_computeTargetAndRootIntersection", "newEntry", "now", "_hasCrossedThreshold", "getComputedStyle", "display", "parent", "getParentNode", "atRoot", "parentRect", "parentComputedStyle", "frameRect", "frameIntersect", "body", "documentElement", "overflow", "computeRectIntersection", "isDoc", "html", "top", "left", "right", "clientWidth", "bottom", "clientHeight", "_expandRectByRootMargin", "rect", "newRect", "oldRatio", "newRatio", "contains<PERSON>eep", "performance", "fn", "timeout", "timer", "setTimeout", "node", "event", "opt_useCapture", "addEventListener", "attachEvent", "removeEventListener", "detachEvent", "rect1", "rect2", "Math", "max", "min", "el", "err", "y", "x", "parentBoundingRect", "parentIntersectionRect", "child", "parentNode", "assignedSlot", "host"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/intersection-observer/intersection-observer.js"], "sourcesContent": ["/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function() {\n'use strict';\n\n// Exit early if we're not running in a browser.\nif (typeof window !== 'object') {\n  return;\n}\n\n// Exit early if all IntersectionObserver and IntersectionObserverEntry\n// features are natively supported.\nif ('IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n\n  // Minimal polyfill for Edge 15's lack of `isIntersecting`\n  // See: https://github.com/w3c/IntersectionObserver/issues/211\n  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n    Object.defineProperty(window.IntersectionObserverEntry.prototype,\n      'isIntersecting', {\n      get: function () {\n        return this.intersectionRatio > 0;\n      }\n    });\n  }\n  return;\n}\n\n/**\n * Returns the embedding frame element, if any.\n * @param {!Document} doc\n * @return {!Element}\n */\nfunction getFrameElement(doc) {\n  try {\n    return doc.defaultView && doc.defaultView.frameElement || null;\n  } catch (e) {\n    // Ignore the error.\n    return null;\n  }\n}\n\n/**\n * A local reference to the root document.\n */\nvar document = (function(startDoc) {\n  var doc = startDoc;\n  var frame = getFrameElement(doc);\n  while (frame) {\n    doc = frame.ownerDocument;\n    frame = getFrameElement(doc);\n  }\n  return doc;\n})(window.document);\n\n/**\n * An IntersectionObserver registry. This registry exists to hold a strong\n * reference to IntersectionObserver instances currently observing a target\n * element. Without this registry, instances without another reference may be\n * garbage collected.\n */\nvar registry = [];\n\n/**\n * The signal updater for cross-origin intersection. When not null, it means\n * that the polyfill is configured to work in a cross-origin mode.\n * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nvar crossOriginUpdater = null;\n\n/**\n * The current cross-origin intersection. Only used in the cross-origin mode.\n * @type {DOMRect|ClientRect}\n */\nvar crossOriginRect = null;\n\n\n/**\n * Creates the global IntersectionObserverEntry constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n * @param {Object} entry A dictionary of instance properties.\n * @constructor\n */\nfunction IntersectionObserverEntry(entry) {\n  this.time = entry.time;\n  this.target = entry.target;\n  this.rootBounds = ensureDOMRect(entry.rootBounds);\n  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n  this.isIntersecting = !!entry.intersectionRect;\n\n  // Calculates the intersection ratio.\n  var targetRect = this.boundingClientRect;\n  var targetArea = targetRect.width * targetRect.height;\n  var intersectionRect = this.intersectionRect;\n  var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n  // Sets intersection ratio.\n  if (targetArea) {\n    // Round the intersection ratio to avoid floating point math issues:\n    // https://github.com/w3c/IntersectionObserver/issues/324\n    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n  } else {\n    // If area is zero and is intersecting, sets to 1, otherwise to 0\n    this.intersectionRatio = this.isIntersecting ? 1 : 0;\n  }\n}\n\n\n/**\n * Creates the global IntersectionObserver constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n * @param {Function} callback The function to be invoked after intersection\n *     changes have queued. The function is not invoked if the queue has\n *     been emptied by calling the `takeRecords` method.\n * @param {Object=} opt_options Optional configuration options.\n * @constructor\n */\nfunction IntersectionObserver(callback, opt_options) {\n\n  var options = opt_options || {};\n\n  if (typeof callback != 'function') {\n    throw new Error('callback must be a function');\n  }\n\n  if (\n    options.root &&\n    options.root.nodeType != 1 &&\n    options.root.nodeType != 9\n  ) {\n    throw new Error('root must be a Document or Element');\n  }\n\n  // Binds and throttles `this._checkForIntersections`.\n  this._checkForIntersections = throttle(\n      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n  // Private properties.\n  this._callback = callback;\n  this._observationTargets = [];\n  this._queuedEntries = [];\n  this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n  // Public properties.\n  this.thresholds = this._initThresholds(options.threshold);\n  this.root = options.root || null;\n  this.rootMargin = this._rootMarginValues.map(function(margin) {\n    return margin.value + margin.unit;\n  }).join(' ');\n\n  /** @private @const {!Array<!Document>} */\n  this._monitoringDocuments = [];\n  /** @private @const {!Array<function()>} */\n  this._monitoringUnsubscribes = [];\n}\n\n\n/**\n * The minimum interval within which the document will be checked for\n * intersection changes.\n */\nIntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n\n/**\n * The frequency in which the polyfill polls for intersection changes.\n * this can be updated on a per instance basis and must be set prior to\n * calling `observe` on the first target.\n */\nIntersectionObserver.prototype.POLL_INTERVAL = null;\n\n/**\n * Use a mutation observer on the root element\n * to detect intersection changes.\n */\nIntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n\n/**\n * Sets up the polyfill in the cross-origin mode. The result is the\n * updater function that accepts two arguments: `boundingClientRect` and\n * `intersectionRect` - just as these fields would be available to the\n * parent via `IntersectionObserverEntry`. This function should be called\n * each time the iframe receives intersection information from the parent\n * window, e.g. via messaging.\n * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nIntersectionObserver._setupCrossOriginUpdater = function() {\n  if (!crossOriginUpdater) {\n    /**\n     * @param {DOMRect|ClientRect} boundingClientRect\n     * @param {DOMRect|ClientRect} intersectionRect\n     */\n    crossOriginUpdater = function(boundingClientRect, intersectionRect) {\n      if (!boundingClientRect || !intersectionRect) {\n        crossOriginRect = getEmptyRect();\n      } else {\n        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n      }\n      registry.forEach(function(observer) {\n        observer._checkForIntersections();\n      });\n    };\n  }\n  return crossOriginUpdater;\n};\n\n\n/**\n * Resets the cross-origin mode.\n */\nIntersectionObserver._resetCrossOriginUpdater = function() {\n  crossOriginUpdater = null;\n  crossOriginRect = null;\n};\n\n\n/**\n * Starts observing a target element for intersection changes based on\n * the thresholds values.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.observe = function(target) {\n  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {\n    return item.element == target;\n  });\n\n  if (isTargetAlreadyObserved) {\n    return;\n  }\n\n  if (!(target && target.nodeType == 1)) {\n    throw new Error('target must be an Element');\n  }\n\n  this._registerInstance();\n  this._observationTargets.push({element: target, entry: null});\n  this._monitorIntersections(target.ownerDocument);\n  this._checkForIntersections();\n};\n\n\n/**\n * Stops observing a target element for intersection changes.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.unobserve = function(target) {\n  this._observationTargets =\n      this._observationTargets.filter(function(item) {\n        return item.element != target;\n      });\n  this._unmonitorIntersections(target.ownerDocument);\n  if (this._observationTargets.length == 0) {\n    this._unregisterInstance();\n  }\n};\n\n\n/**\n * Stops observing all target elements for intersection changes.\n */\nIntersectionObserver.prototype.disconnect = function() {\n  this._observationTargets = [];\n  this._unmonitorAllIntersections();\n  this._unregisterInstance();\n};\n\n\n/**\n * Returns any queue entries that have not yet been reported to the\n * callback and clears the queue. This can be used in conjunction with the\n * callback to obtain the absolute most up-to-date intersection information.\n * @return {Array} The currently queued entries.\n */\nIntersectionObserver.prototype.takeRecords = function() {\n  var records = this._queuedEntries.slice();\n  this._queuedEntries = [];\n  return records;\n};\n\n\n/**\n * Accepts the threshold value from the user configuration object and\n * returns a sorted array of unique threshold values. If a value is not\n * between 0 and 1 and error is thrown.\n * @private\n * @param {Array|number=} opt_threshold An optional threshold value or\n *     a list of threshold values, defaulting to [0].\n * @return {Array} A sorted list of unique and valid threshold values.\n */\nIntersectionObserver.prototype._initThresholds = function(opt_threshold) {\n  var threshold = opt_threshold || [0];\n  if (!Array.isArray(threshold)) threshold = [threshold];\n\n  return threshold.sort().filter(function(t, i, a) {\n    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n      throw new Error('threshold must be a number between 0 and 1 inclusively');\n    }\n    return t !== a[i - 1];\n  });\n};\n\n\n/**\n * Accepts the rootMargin value from the user configuration object\n * and returns an array of the four margin values as an object containing\n * the value and unit properties. If any of the values are not properly\n * formatted or use a unit other than px or %, and error is thrown.\n * @private\n * @param {string=} opt_rootMargin An optional rootMargin value,\n *     defaulting to '0px'.\n * @return {Array<Object>} An array of margin objects with the keys\n *     value and unit.\n */\nIntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {\n  var marginString = opt_rootMargin || '0px';\n  var margins = marginString.split(/\\s+/).map(function(margin) {\n    var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n    if (!parts) {\n      throw new Error('rootMargin must be specified in pixels or percent');\n    }\n    return {value: parseFloat(parts[1]), unit: parts[2]};\n  });\n\n  // Handles shorthand.\n  margins[1] = margins[1] || margins[0];\n  margins[2] = margins[2] || margins[0];\n  margins[3] = margins[3] || margins[1];\n\n  return margins;\n};\n\n\n/**\n * Starts polling for intersection changes if the polling is not already\n * happening, and if the page's visibility state is visible.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._monitorIntersections = function(doc) {\n  var win = doc.defaultView;\n  if (!win) {\n    // Already destroyed.\n    return;\n  }\n  if (this._monitoringDocuments.indexOf(doc) != -1) {\n    // Already monitoring.\n    return;\n  }\n\n  // Private state for monitoring.\n  var callback = this._checkForIntersections;\n  var monitoringInterval = null;\n  var domObserver = null;\n\n  // If a poll interval is set, use polling instead of listening to\n  // resize and scroll events or DOM mutations.\n  if (this.POLL_INTERVAL) {\n    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n  } else {\n    addEvent(win, 'resize', callback, true);\n    addEvent(doc, 'scroll', callback, true);\n    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n      domObserver = new win.MutationObserver(callback);\n      domObserver.observe(doc, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    }\n  }\n\n  this._monitoringDocuments.push(doc);\n  this._monitoringUnsubscribes.push(function() {\n    // Get the window object again. When a friendly iframe is destroyed, it\n    // will be null.\n    var win = doc.defaultView;\n\n    if (win) {\n      if (monitoringInterval) {\n        win.clearInterval(monitoringInterval);\n      }\n      removeEvent(win, 'resize', callback, true);\n    }\n\n    removeEvent(doc, 'scroll', callback, true);\n    if (domObserver) {\n      domObserver.disconnect();\n    }\n  });\n\n  // Also monitor the parent.\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._monitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorIntersections = function(doc) {\n  var index = this._monitoringDocuments.indexOf(doc);\n  if (index == -1) {\n    return;\n  }\n\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n\n  // Check if any dependent targets are still remaining.\n  var hasDependentTargets =\n      this._observationTargets.some(function(item) {\n        var itemDoc = item.element.ownerDocument;\n        // Target is in this context.\n        if (itemDoc == doc) {\n          return true;\n        }\n        // Target is nested in this context.\n        while (itemDoc && itemDoc != rootDoc) {\n          var frame = getFrameElement(itemDoc);\n          itemDoc = frame && frame.ownerDocument;\n          if (itemDoc == doc) {\n            return true;\n          }\n        }\n        return false;\n      });\n  if (hasDependentTargets) {\n    return;\n  }\n\n  // Unsubscribe.\n  var unsubscribe = this._monitoringUnsubscribes[index];\n  this._monitoringDocuments.splice(index, 1);\n  this._monitoringUnsubscribes.splice(index, 1);\n  unsubscribe();\n\n  // Also unmonitor the parent.\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._unmonitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorAllIntersections = function() {\n  var unsubscribes = this._monitoringUnsubscribes.slice(0);\n  this._monitoringDocuments.length = 0;\n  this._monitoringUnsubscribes.length = 0;\n  for (var i = 0; i < unsubscribes.length; i++) {\n    unsubscribes[i]();\n  }\n};\n\n\n/**\n * Scans each observation target for intersection changes and adds them\n * to the internal entries queue. If new entries are found, it\n * schedules the callback to be invoked.\n * @private\n */\nIntersectionObserver.prototype._checkForIntersections = function() {\n  if (!this.root && crossOriginUpdater && !crossOriginRect) {\n    // Cross origin monitoring, but no initial data available yet.\n    return;\n  }\n\n  var rootIsInDom = this._rootIsInDom();\n  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n  this._observationTargets.forEach(function(item) {\n    var target = item.element;\n    var targetRect = getBoundingClientRect(target);\n    var rootContainsTarget = this._rootContainsTarget(target);\n    var oldEntry = item.entry;\n    var intersectionRect = rootIsInDom && rootContainsTarget &&\n        this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n    var rootBounds = null;\n    if (!this._rootContainsTarget(target)) {\n      rootBounds = getEmptyRect();\n    } else if (!crossOriginUpdater || this.root) {\n      rootBounds = rootRect;\n    }\n\n    var newEntry = item.entry = new IntersectionObserverEntry({\n      time: now(),\n      target: target,\n      boundingClientRect: targetRect,\n      rootBounds: rootBounds,\n      intersectionRect: intersectionRect\n    });\n\n    if (!oldEntry) {\n      this._queuedEntries.push(newEntry);\n    } else if (rootIsInDom && rootContainsTarget) {\n      // If the new entry intersection ratio has crossed any of the\n      // thresholds, add a new entry.\n      if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n        this._queuedEntries.push(newEntry);\n      }\n    } else {\n      // If the root is not in the DOM or target is not contained within\n      // root but the previous entry for this target had an intersection,\n      // add a new record indicating removal.\n      if (oldEntry && oldEntry.isIntersecting) {\n        this._queuedEntries.push(newEntry);\n      }\n    }\n  }, this);\n\n  if (this._queuedEntries.length) {\n    this._callback(this.takeRecords(), this);\n  }\n};\n\n\n/**\n * Accepts a target and root rect computes the intersection between then\n * following the algorithm in the spec.\n * TODO(philipwalton): at this time clip-path is not considered.\n * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n * @param {Element} target The target DOM element\n * @param {Object} targetRect The bounding rect of the target.\n * @param {Object} rootRect The bounding rect of the root after being\n *     expanded by the rootMargin value.\n * @return {?Object} The final intersection rect object or undefined if no\n *     intersection is found.\n * @private\n */\nIntersectionObserver.prototype._computeTargetAndRootIntersection =\n    function(target, targetRect, rootRect) {\n  // If the element isn't displayed, an intersection can't happen.\n  if (window.getComputedStyle(target).display == 'none') return;\n\n  var intersectionRect = targetRect;\n  var parent = getParentNode(target);\n  var atRoot = false;\n\n  while (!atRoot && parent) {\n    var parentRect = null;\n    var parentComputedStyle = parent.nodeType == 1 ?\n        window.getComputedStyle(parent) : {};\n\n    // If the parent isn't displayed, an intersection can't happen.\n    if (parentComputedStyle.display == 'none') return null;\n\n    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {\n      atRoot = true;\n      if (parent == this.root || parent == document) {\n        if (crossOriginUpdater && !this.root) {\n          if (!crossOriginRect ||\n              crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n            // A 0-size cross-origin intersection means no-intersection.\n            parent = null;\n            parentRect = null;\n            intersectionRect = null;\n          } else {\n            parentRect = crossOriginRect;\n          }\n        } else {\n          parentRect = rootRect;\n        }\n      } else {\n        // Check if there's a frame that can be navigated to.\n        var frame = getParentNode(parent);\n        var frameRect = frame && getBoundingClientRect(frame);\n        var frameIntersect =\n            frame &&\n            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n        if (frameRect && frameIntersect) {\n          parent = frame;\n          parentRect = convertFromParentRect(frameRect, frameIntersect);\n        } else {\n          parent = null;\n          intersectionRect = null;\n        }\n      }\n    } else {\n      // If the element has a non-visible overflow, and it's not the <body>\n      // or <html> element, update the intersection rect.\n      // Note: <body> and <html> cannot be clipped to a rect that's not also\n      // the document rect, so no need to compute a new intersection.\n      var doc = parent.ownerDocument;\n      if (parent != doc.body &&\n          parent != doc.documentElement &&\n          parentComputedStyle.overflow != 'visible') {\n        parentRect = getBoundingClientRect(parent);\n      }\n    }\n\n    // If either of the above conditionals set a new parentRect,\n    // calculate new intersection data.\n    if (parentRect) {\n      intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n    }\n    if (!intersectionRect) break;\n    parent = parent && getParentNode(parent);\n  }\n  return intersectionRect;\n};\n\n\n/**\n * Returns the root rect after being expanded by the rootMargin value.\n * @return {ClientRect} The expanded root rect.\n * @private\n */\nIntersectionObserver.prototype._getRootRect = function() {\n  var rootRect;\n  if (this.root && !isDoc(this.root)) {\n    rootRect = getBoundingClientRect(this.root);\n  } else {\n    // Use <html>/<body> instead of window since scroll bars affect size.\n    var doc = isDoc(this.root) ? this.root : document;\n    var html = doc.documentElement;\n    var body = doc.body;\n    rootRect = {\n      top: 0,\n      left: 0,\n      right: html.clientWidth || body.clientWidth,\n      width: html.clientWidth || body.clientWidth,\n      bottom: html.clientHeight || body.clientHeight,\n      height: html.clientHeight || body.clientHeight\n    };\n  }\n  return this._expandRectByRootMargin(rootRect);\n};\n\n\n/**\n * Accepts a rect and expands it by the rootMargin value.\n * @param {DOMRect|ClientRect} rect The rect object to expand.\n * @return {ClientRect} The expanded rect.\n * @private\n */\nIntersectionObserver.prototype._expandRectByRootMargin = function(rect) {\n  var margins = this._rootMarginValues.map(function(margin, i) {\n    return margin.unit == 'px' ? margin.value :\n        margin.value * (i % 2 ? rect.width : rect.height) / 100;\n  });\n  var newRect = {\n    top: rect.top - margins[0],\n    right: rect.right + margins[1],\n    bottom: rect.bottom + margins[2],\n    left: rect.left - margins[3]\n  };\n  newRect.width = newRect.right - newRect.left;\n  newRect.height = newRect.bottom - newRect.top;\n\n  return newRect;\n};\n\n\n/**\n * Accepts an old and new entry and returns true if at least one of the\n * threshold values has been crossed.\n * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n *    particular target element or null if no previous entry exists.\n * @param {IntersectionObserverEntry} newEntry The current entry for a\n *    particular target element.\n * @return {boolean} Returns true if a any threshold has been crossed.\n * @private\n */\nIntersectionObserver.prototype._hasCrossedThreshold =\n    function(oldEntry, newEntry) {\n\n  // To make comparing easier, an entry that has a ratio of 0\n  // but does not actually intersect is given a value of -1\n  var oldRatio = oldEntry && oldEntry.isIntersecting ?\n      oldEntry.intersectionRatio || 0 : -1;\n  var newRatio = newEntry.isIntersecting ?\n      newEntry.intersectionRatio || 0 : -1;\n\n  // Ignore unchanged ratios\n  if (oldRatio === newRatio) return;\n\n  for (var i = 0; i < this.thresholds.length; i++) {\n    var threshold = this.thresholds[i];\n\n    // Return true if an entry matches a threshold or if the new ratio\n    // and the old ratio are on the opposite sides of a threshold.\n    if (threshold == oldRatio || threshold == newRatio ||\n        threshold < oldRatio !== threshold < newRatio) {\n      return true;\n    }\n  }\n};\n\n\n/**\n * Returns whether or not the root element is an element and is in the DOM.\n * @return {boolean} True if the root element is an element and is in the DOM.\n * @private\n */\nIntersectionObserver.prototype._rootIsInDom = function() {\n  return !this.root || containsDeep(document, this.root);\n};\n\n\n/**\n * Returns whether or not the target element is a child of root.\n * @param {Element} target The target element to check.\n * @return {boolean} True if the target element is a child of root.\n * @private\n */\nIntersectionObserver.prototype._rootContainsTarget = function(target) {\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  return (\n    containsDeep(rootDoc, target) &&\n    (!this.root || rootDoc == target.ownerDocument)\n  );\n};\n\n\n/**\n * Adds the instance to the global IntersectionObserver registry if it isn't\n * already present.\n * @private\n */\nIntersectionObserver.prototype._registerInstance = function() {\n  if (registry.indexOf(this) < 0) {\n    registry.push(this);\n  }\n};\n\n\n/**\n * Removes the instance from the global IntersectionObserver registry.\n * @private\n */\nIntersectionObserver.prototype._unregisterInstance = function() {\n  var index = registry.indexOf(this);\n  if (index != -1) registry.splice(index, 1);\n};\n\n\n/**\n * Returns the result of the performance.now() method or null in browsers\n * that don't support the API.\n * @return {number} The elapsed time since the page was requested.\n */\nfunction now() {\n  return window.performance && performance.now && performance.now();\n}\n\n\n/**\n * Throttles a function and delays its execution, so it's only called at most\n * once within a given time period.\n * @param {Function} fn The function to throttle.\n * @param {number} timeout The amount of time that must pass before the\n *     function can be called again.\n * @return {Function} The throttled function.\n */\nfunction throttle(fn, timeout) {\n  var timer = null;\n  return function () {\n    if (!timer) {\n      timer = setTimeout(function() {\n        fn();\n        timer = null;\n      }, timeout);\n    }\n  };\n}\n\n\n/**\n * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n * @param {Node} node The DOM node to add the event handler to.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to add.\n * @param {boolean} opt_useCapture Optionally adds the even to the capture\n *     phase. Note: this only works in modern browsers.\n */\nfunction addEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.addEventListener == 'function') {\n    node.addEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.attachEvent == 'function') {\n    node.attachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Removes a previously added event handler from a DOM node.\n * @param {Node} node The DOM node to remove the event handler from.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to remove.\n * @param {boolean} opt_useCapture If the event handler was added with this\n *     flag set to true, it should be set to true here in order to remove it.\n */\nfunction removeEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.removeEventListener == 'function') {\n    node.removeEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.detachEvent == 'function') {\n    node.detachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Returns the intersection between two rect objects.\n * @param {Object} rect1 The first rect.\n * @param {Object} rect2 The second rect.\n * @return {?Object|?ClientRect} The intersection rect or undefined if no\n *     intersection is found.\n */\nfunction computeRectIntersection(rect1, rect2) {\n  var top = Math.max(rect1.top, rect2.top);\n  var bottom = Math.min(rect1.bottom, rect2.bottom);\n  var left = Math.max(rect1.left, rect2.left);\n  var right = Math.min(rect1.right, rect2.right);\n  var width = right - left;\n  var height = bottom - top;\n\n  return (width >= 0 && height >= 0) && {\n    top: top,\n    bottom: bottom,\n    left: left,\n    right: right,\n    width: width,\n    height: height\n  } || null;\n}\n\n\n/**\n * Shims the native getBoundingClientRect for compatibility with older IE.\n * @param {Element} el The element whose bounding rect to get.\n * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n */\nfunction getBoundingClientRect(el) {\n  var rect;\n\n  try {\n    rect = el.getBoundingClientRect();\n  } catch (err) {\n    // Ignore Windows 7 IE11 \"Unspecified error\"\n    // https://github.com/w3c/IntersectionObserver/pull/205\n  }\n\n  if (!rect) return getEmptyRect();\n\n  // Older IE\n  if (!(rect.width && rect.height)) {\n    rect = {\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      left: rect.left,\n      width: rect.right - rect.left,\n      height: rect.bottom - rect.top\n    };\n  }\n  return rect;\n}\n\n\n/**\n * Returns an empty rect object. An empty rect is returned when an element\n * is not in the DOM.\n * @return {ClientRect} The empty rect.\n */\nfunction getEmptyRect() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    width: 0,\n    height: 0\n  };\n}\n\n\n/**\n * Ensure that the result has all of the necessary fields of the DOMRect.\n * Specifically this ensures that `x` and `y` fields are set.\n *\n * @param {?DOMRect|?ClientRect} rect\n * @return {?DOMRect}\n */\nfunction ensureDOMRect(rect) {\n  // A `DOMRect` object has `x` and `y` fields.\n  if (!rect || 'x' in rect) {\n    return rect;\n  }\n  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n  // for internally calculated Rect objects. For the purposes of\n  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n  // for these fields.\n  return {\n    top: rect.top,\n    y: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    right: rect.right,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n\n/**\n * Inverts the intersection and bounding rect from the parent (frame) BCR to\n * the local BCR space.\n * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n * @return {ClientRect} The local root bounding rect for the parent's children.\n */\nfunction convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n  var top = parentIntersectionRect.top - parentBoundingRect.top;\n  var left = parentIntersectionRect.left - parentBoundingRect.left;\n  return {\n    top: top,\n    left: left,\n    height: parentIntersectionRect.height,\n    width: parentIntersectionRect.width,\n    bottom: top + parentIntersectionRect.height,\n    right: left + parentIntersectionRect.width\n  };\n}\n\n\n/**\n * Checks to see if a parent element contains a child element (including inside\n * shadow DOM).\n * @param {Node} parent The parent element.\n * @param {Node} child The child element.\n * @return {boolean} True if the parent node contains the child node.\n */\nfunction containsDeep(parent, child) {\n  var node = child;\n  while (node) {\n    if (node == parent) return true;\n\n    node = getParentNode(node);\n  }\n  return false;\n}\n\n\n/**\n * Gets the parent node of an element or its host element if the parent node\n * is a shadow root.\n * @param {Node} node The node whose parent to get.\n * @return {Node|null} The parent node or null if no parent exists.\n */\nfunction getParentNode(node) {\n  var parent = node.parentNode;\n\n  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {\n    // If this node is a document node, look for the embedding frame.\n    return getFrameElement(node);\n  }\n\n  // If the parent has element that is assigned through shadow root slot\n  if (parent && parent.assignedSlot) {\n    parent = parent.assignedSlot.parentNode\n  }\n\n  if (parent && parent.nodeType == 11 && parent.host) {\n    // If the parent is a shadow root, return the host element.\n    return parent.host;\n  }\n\n  return parent;\n}\n\n/**\n * Returns true if `node` is a Document.\n * @param {!Node} node\n * @returns {boolean}\n */\nfunction isDoc(node) {\n  return node && node.nodeType === 9;\n}\n\n\n// Exposes the constructors globally.\nwindow.IntersectionObserver = IntersectionObserver;\nwindow.IntersectionObserverEntry = IntersectionObserverEntry;\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACC,aAAW;EACZ,aADY,CAGZ;;EACA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;IAC9B;EACD,CANW,CAQZ;EACA;;;EACA,IAAI,0BAA0BA,MAA1B,IACA,+BAA+BA,MAD/B,IAEA,uBAAuBA,MAAM,CAACC,yBAAP,CAAiCC,SAF5D,EAEuE;IAErE;IACA;IACA,IAAI,EAAE,oBAAoBF,MAAM,CAACC,yBAAP,CAAiCC,SAAvD,CAAJ,EAAuE;MACrEC,MAAM,CAACC,cAAP,CAAsBJ,MAAM,CAACC,yBAAP,CAAiCC,SAAvD,EACE,gBADF,EACoB;QAClBG,GAAG,EAAE,eAAY;UACf,OAAO,KAAKC,iBAAL,GAAyB,CAAhC;QACD;MAHiB,CADpB;IAMD;;IACD;EACD;EAED;AACA;AACA;AACA;AACA;;;EACA,SAASC,eAAT,CAAyBC,GAAzB,EAA8B;IAC5B,IAAI;MACF,OAAOA,GAAG,CAACC,WAAJ,IAAmBD,GAAG,CAACC,WAAJ,CAAgBC,YAAnC,IAAmD,IAA1D;IACD,CAFD,CAEE,OAAOC,CAAP,EAAU;MACV;MACA,OAAO,IAAP;IACD;EACF;EAED;AACA;AACA;;;EACA,IAAIC,QAAQ,GAAI,UAASC,QAAT,EAAmB;IACjC,IAAIL,GAAG,GAAGK,QAAV;IACA,IAAIC,KAAK,GAAGP,eAAe,CAACC,GAAD,CAA3B;;IACA,OAAOM,KAAP,EAAc;MACZN,GAAG,GAAGM,KAAK,CAACC,aAAZ;MACAD,KAAK,GAAGP,eAAe,CAACC,GAAD,CAAvB;IACD;;IACD,OAAOA,GAAP;EACD,CARc,CAQZR,MAAM,CAACY,QARK,CAAf;EAUA;AACA;AACA;AACA;AACA;AACA;;;EACA,IAAII,QAAQ,GAAG,EAAf;EAEA;AACA;AACA;AACA;AACA;;EACA,IAAIC,kBAAkB,GAAG,IAAzB;EAEA;AACA;AACA;AACA;;EACA,IAAIC,eAAe,GAAG,IAAtB;EAGA;AACA;AACA;AACA;AACA;AACA;;EACA,SAASjB,yBAAT,CAAmCkB,KAAnC,EAA0C;IACxC,KAAKC,IAAL,GAAYD,KAAK,CAACC,IAAlB;IACA,KAAKC,MAAL,GAAcF,KAAK,CAACE,MAApB;IACA,KAAKC,UAAL,GAAkBC,aAAa,CAACJ,KAAK,CAACG,UAAP,CAA/B;IACA,KAAKE,kBAAL,GAA0BD,aAAa,CAACJ,KAAK,CAACK,kBAAP,CAAvC;IACA,KAAKC,gBAAL,GAAwBF,aAAa,CAACJ,KAAK,CAACM,gBAAN,IAA0BC,YAAY,EAAvC,CAArC;IACA,KAAKC,cAAL,GAAsB,CAAC,CAACR,KAAK,CAACM,gBAA9B,CANwC,CAQxC;;IACA,IAAIG,UAAU,GAAG,KAAKJ,kBAAtB;IACA,IAAIK,UAAU,GAAGD,UAAU,CAACE,KAAX,GAAmBF,UAAU,CAACG,MAA/C;IACA,IAAIN,gBAAgB,GAAG,KAAKA,gBAA5B;IACA,IAAIO,gBAAgB,GAAGP,gBAAgB,CAACK,KAAjB,GAAyBL,gBAAgB,CAACM,MAAjE,CAZwC,CAcxC;;IACA,IAAIF,UAAJ,EAAgB;MACd;MACA;MACA,KAAKvB,iBAAL,GAAyB2B,MAAM,CAAC,CAACD,gBAAgB,GAAGH,UAApB,EAAgCK,OAAhC,CAAwC,CAAxC,CAAD,CAA/B;IACD,CAJD,MAIO;MACL;MACA,KAAK5B,iBAAL,GAAyB,KAAKqB,cAAL,GAAsB,CAAtB,GAA0B,CAAnD;IACD;EACF;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASQ,oBAAT,CAA8BC,QAA9B,EAAwCC,WAAxC,EAAqD;IAEnD,IAAIC,OAAO,GAAGD,WAAW,IAAI,EAA7B;;IAEA,IAAI,OAAOD,QAAP,IAAmB,UAAvB,EAAmC;MACjC,MAAM,IAAIG,KAAJ,CAAU,6BAAV,CAAN;IACD;;IAED,IACED,OAAO,CAACE,IAAR,IACAF,OAAO,CAACE,IAAR,CAAaC,QAAb,IAAyB,CADzB,IAEAH,OAAO,CAACE,IAAR,CAAaC,QAAb,IAAyB,CAH3B,EAIE;MACA,MAAM,IAAIF,KAAJ,CAAU,oCAAV,CAAN;IACD,CAdkD,CAgBnD;;;IACA,KAAKG,sBAAL,GAA8BC,QAAQ,CAClC,KAAKD,sBAAL,CAA4BE,IAA5B,CAAiC,IAAjC,CADkC,EACM,KAAKC,gBADX,CAAtC,CAjBmD,CAoBnD;;IACA,KAAKC,SAAL,GAAiBV,QAAjB;IACA,KAAKW,mBAAL,GAA2B,EAA3B;IACA,KAAKC,cAAL,GAAsB,EAAtB;IACA,KAAKC,iBAAL,GAAyB,KAAKC,gBAAL,CAAsBZ,OAAO,CAACa,UAA9B,CAAzB,CAxBmD,CA0BnD;;IACA,KAAKC,UAAL,GAAkB,KAAKC,eAAL,CAAqBf,OAAO,CAACgB,SAA7B,CAAlB;IACA,KAAKd,IAAL,GAAYF,OAAO,CAACE,IAAR,IAAgB,IAA5B;IACA,KAAKW,UAAL,GAAkB,KAAKF,iBAAL,CAAuBM,GAAvB,CAA2B,UAASC,MAAT,EAAiB;MAC5D,OAAOA,MAAM,CAACC,KAAP,GAAeD,MAAM,CAACE,IAA7B;IACD,CAFiB,EAEfC,IAFe,CAEV,GAFU,CAAlB;IAIA;;IACA,KAAKC,oBAAL,GAA4B,EAA5B;IACA;;IACA,KAAKC,uBAAL,GAA+B,EAA/B;EACD;EAGD;AACA;AACA;AACA;;;EACA1B,oBAAoB,CAACjC,SAArB,CAA+B2C,gBAA/B,GAAkD,GAAlD;EAGA;AACA;AACA;AACA;AACA;;EACAV,oBAAoB,CAACjC,SAArB,CAA+B4D,aAA/B,GAA+C,IAA/C;EAEA;AACA;AACA;AACA;;EACA3B,oBAAoB,CAACjC,SAArB,CAA+B6D,qBAA/B,GAAuD,IAAvD;EAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACA5B,oBAAoB,CAAC6B,wBAArB,GAAgD,YAAW;IACzD,IAAI,CAAC/C,kBAAL,EAAyB;MACvB;AACJ;AACA;AACA;MACIA,kBAAkB,GAAG,4BAASO,kBAAT,EAA6BC,gBAA7B,EAA+C;QAClE,IAAI,CAACD,kBAAD,IAAuB,CAACC,gBAA5B,EAA8C;UAC5CP,eAAe,GAAGQ,YAAY,EAA9B;QACD,CAFD,MAEO;UACLR,eAAe,GAAG+C,qBAAqB,CAACzC,kBAAD,EAAqBC,gBAArB,CAAvC;QACD;;QACDT,QAAQ,CAACkD,OAAT,CAAiB,UAASC,QAAT,EAAmB;UAClCA,QAAQ,CAACzB,sBAAT;QACD,CAFD;MAGD,CATD;IAUD;;IACD,OAAOzB,kBAAP;EACD,CAlBD;EAqBA;AACA;AACA;;;EACAkB,oBAAoB,CAACiC,wBAArB,GAAgD,YAAW;IACzDnD,kBAAkB,GAAG,IAArB;IACAC,eAAe,GAAG,IAAlB;EACD,CAHD;EAMA;AACA;AACA;AACA;AACA;;;EACAiB,oBAAoB,CAACjC,SAArB,CAA+BmE,OAA/B,GAAyC,UAAShD,MAAT,EAAiB;IACxD,IAAIiD,uBAAuB,GAAG,KAAKvB,mBAAL,CAAyBwB,IAAzB,CAA8B,UAASC,IAAT,EAAe;MACzE,OAAOA,IAAI,CAACC,OAAL,IAAgBpD,MAAvB;IACD,CAF6B,CAA9B;;IAIA,IAAIiD,uBAAJ,EAA6B;MAC3B;IACD;;IAED,IAAI,EAAEjD,MAAM,IAAIA,MAAM,CAACoB,QAAP,IAAmB,CAA/B,CAAJ,EAAuC;MACrC,MAAM,IAAIF,KAAJ,CAAU,2BAAV,CAAN;IACD;;IAED,KAAKmC,iBAAL;;IACA,KAAK3B,mBAAL,CAAyB4B,IAAzB,CAA8B;MAACF,OAAO,EAAEpD,MAAV;MAAkBF,KAAK,EAAE;IAAzB,CAA9B;;IACA,KAAKyD,qBAAL,CAA2BvD,MAAM,CAACN,aAAlC;;IACA,KAAK2B,sBAAL;EACD,CAjBD;EAoBA;AACA;AACA;AACA;;;EACAP,oBAAoB,CAACjC,SAArB,CAA+B2E,SAA/B,GAA2C,UAASxD,MAAT,EAAiB;IAC1D,KAAK0B,mBAAL,GACI,KAAKA,mBAAL,CAAyB+B,MAAzB,CAAgC,UAASN,IAAT,EAAe;MAC7C,OAAOA,IAAI,CAACC,OAAL,IAAgBpD,MAAvB;IACD,CAFD,CADJ;;IAIA,KAAK0D,uBAAL,CAA6B1D,MAAM,CAACN,aAApC;;IACA,IAAI,KAAKgC,mBAAL,CAAyBiC,MAAzB,IAAmC,CAAvC,EAA0C;MACxC,KAAKC,mBAAL;IACD;EACF,CATD;EAYA;AACA;AACA;;;EACA9C,oBAAoB,CAACjC,SAArB,CAA+BgF,UAA/B,GAA4C,YAAW;IACrD,KAAKnC,mBAAL,GAA2B,EAA3B;;IACA,KAAKoC,0BAAL;;IACA,KAAKF,mBAAL;EACD,CAJD;EAOA;AACA;AACA;AACA;AACA;AACA;;;EACA9C,oBAAoB,CAACjC,SAArB,CAA+BkF,WAA/B,GAA6C,YAAW;IACtD,IAAIC,OAAO,GAAG,KAAKrC,cAAL,CAAoBsC,KAApB,EAAd;;IACA,KAAKtC,cAAL,GAAsB,EAAtB;IACA,OAAOqC,OAAP;EACD,CAJD;EAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACAlD,oBAAoB,CAACjC,SAArB,CAA+BmD,eAA/B,GAAiD,UAASkC,aAAT,EAAwB;IACvE,IAAIjC,SAAS,GAAGiC,aAAa,IAAI,CAAC,CAAD,CAAjC;IACA,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcnC,SAAd,CAAL,EAA+BA,SAAS,GAAG,CAACA,SAAD,CAAZ;IAE/B,OAAOA,SAAS,CAACoC,IAAV,GAAiBZ,MAAjB,CAAwB,UAASa,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkB;MAC/C,IAAI,OAAOF,CAAP,IAAY,QAAZ,IAAwBG,KAAK,CAACH,CAAD,CAA7B,IAAoCA,CAAC,GAAG,CAAxC,IAA6CA,CAAC,GAAG,CAArD,EAAwD;QACtD,MAAM,IAAIpD,KAAJ,CAAU,wDAAV,CAAN;MACD;;MACD,OAAOoD,CAAC,KAAKE,CAAC,CAACD,CAAC,GAAG,CAAL,CAAd;IACD,CALM,CAAP;EAMD,CAVD;EAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACAzD,oBAAoB,CAACjC,SAArB,CAA+BgD,gBAA/B,GAAkD,UAAS6C,cAAT,EAAyB;IACzE,IAAIC,YAAY,GAAGD,cAAc,IAAI,KAArC;IACA,IAAIE,OAAO,GAAGD,YAAY,CAACE,KAAb,CAAmB,KAAnB,EAA0B3C,GAA1B,CAA8B,UAASC,MAAT,EAAiB;MAC3D,IAAI2C,KAAK,GAAG,wBAAwBC,IAAxB,CAA6B5C,MAA7B,CAAZ;;MACA,IAAI,CAAC2C,KAAL,EAAY;QACV,MAAM,IAAI5D,KAAJ,CAAU,mDAAV,CAAN;MACD;;MACD,OAAO;QAACkB,KAAK,EAAE4C,UAAU,CAACF,KAAK,CAAC,CAAD,CAAN,CAAlB;QAA8BzC,IAAI,EAAEyC,KAAK,CAAC,CAAD;MAAzC,CAAP;IACD,CANa,CAAd,CAFyE,CAUzE;;IACAF,OAAO,CAAC,CAAD,CAAP,GAAaA,OAAO,CAAC,CAAD,CAAP,IAAcA,OAAO,CAAC,CAAD,CAAlC;IACAA,OAAO,CAAC,CAAD,CAAP,GAAaA,OAAO,CAAC,CAAD,CAAP,IAAcA,OAAO,CAAC,CAAD,CAAlC;IACAA,OAAO,CAAC,CAAD,CAAP,GAAaA,OAAO,CAAC,CAAD,CAAP,IAAcA,OAAO,CAAC,CAAD,CAAlC;IAEA,OAAOA,OAAP;EACD,CAhBD;EAmBA;AACA;AACA;AACA;AACA;AACA;;;EACA9D,oBAAoB,CAACjC,SAArB,CAA+B0E,qBAA/B,GAAuD,UAASpE,GAAT,EAAc;IACnE,IAAI8F,GAAG,GAAG9F,GAAG,CAACC,WAAd;;IACA,IAAI,CAAC6F,GAAL,EAAU;MACR;MACA;IACD;;IACD,IAAI,KAAK1C,oBAAL,CAA0B2C,OAA1B,CAAkC/F,GAAlC,KAA0C,CAAC,CAA/C,EAAkD;MAChD;MACA;IACD,CATkE,CAWnE;;;IACA,IAAI4B,QAAQ,GAAG,KAAKM,sBAApB;IACA,IAAI8D,kBAAkB,GAAG,IAAzB;IACA,IAAIC,WAAW,GAAG,IAAlB,CAdmE,CAgBnE;IACA;;IACA,IAAI,KAAK3C,aAAT,EAAwB;MACtB0C,kBAAkB,GAAGF,GAAG,CAACI,WAAJ,CAAgBtE,QAAhB,EAA0B,KAAK0B,aAA/B,CAArB;IACD,CAFD,MAEO;MACL6C,QAAQ,CAACL,GAAD,EAAM,QAAN,EAAgBlE,QAAhB,EAA0B,IAA1B,CAAR;MACAuE,QAAQ,CAACnG,GAAD,EAAM,QAAN,EAAgB4B,QAAhB,EAA0B,IAA1B,CAAR;;MACA,IAAI,KAAK2B,qBAAL,IAA8B,sBAAsBuC,GAAxD,EAA6D;QAC3DG,WAAW,GAAG,IAAIH,GAAG,CAACM,gBAAR,CAAyBxE,QAAzB,CAAd;QACAqE,WAAW,CAACpC,OAAZ,CAAoB7D,GAApB,EAAyB;UACvBqG,UAAU,EAAE,IADW;UAEvBC,SAAS,EAAE,IAFY;UAGvBC,aAAa,EAAE,IAHQ;UAIvBC,OAAO,EAAE;QAJc,CAAzB;MAMD;IACF;;IAED,KAAKpD,oBAAL,CAA0Be,IAA1B,CAA+BnE,GAA/B;;IACA,KAAKqD,uBAAL,CAA6Bc,IAA7B,CAAkC,YAAW;MAC3C;MACA;MACA,IAAI2B,GAAG,GAAG9F,GAAG,CAACC,WAAd;;MAEA,IAAI6F,GAAJ,EAAS;QACP,IAAIE,kBAAJ,EAAwB;UACtBF,GAAG,CAACW,aAAJ,CAAkBT,kBAAlB;QACD;;QACDU,WAAW,CAACZ,GAAD,EAAM,QAAN,EAAgBlE,QAAhB,EAA0B,IAA1B,CAAX;MACD;;MAED8E,WAAW,CAAC1G,GAAD,EAAM,QAAN,EAAgB4B,QAAhB,EAA0B,IAA1B,CAAX;;MACA,IAAIqE,WAAJ,EAAiB;QACfA,WAAW,CAACvB,UAAZ;MACD;IACF,CAhBD,EAnCmE,CAqDnE;;;IACA,IAAIiC,OAAO,GACR,KAAK3E,IAAL,KAAc,KAAKA,IAAL,CAAUzB,aAAV,IAA2B,KAAKyB,IAA9C,CAAD,IAAyD5B,QAD3D;;IAEA,IAAIJ,GAAG,IAAI2G,OAAX,EAAoB;MAClB,IAAIrG,KAAK,GAAGP,eAAe,CAACC,GAAD,CAA3B;;MACA,IAAIM,KAAJ,EAAW;QACT,KAAK8D,qBAAL,CAA2B9D,KAAK,CAACC,aAAjC;MACD;IACF;EACF,CA9DD;EAiEA;AACA;AACA;AACA;AACA;;;EACAoB,oBAAoB,CAACjC,SAArB,CAA+B6E,uBAA/B,GAAyD,UAASvE,GAAT,EAAc;IACrE,IAAI4G,KAAK,GAAG,KAAKxD,oBAAL,CAA0B2C,OAA1B,CAAkC/F,GAAlC,CAAZ;;IACA,IAAI4G,KAAK,IAAI,CAAC,CAAd,EAAiB;MACf;IACD;;IAED,IAAID,OAAO,GACR,KAAK3E,IAAL,KAAc,KAAKA,IAAL,CAAUzB,aAAV,IAA2B,KAAKyB,IAA9C,CAAD,IAAyD5B,QAD3D,CANqE,CASrE;;IACA,IAAIyG,mBAAmB,GACnB,KAAKtE,mBAAL,CAAyBwB,IAAzB,CAA8B,UAASC,IAAT,EAAe;MAC3C,IAAI8C,OAAO,GAAG9C,IAAI,CAACC,OAAL,CAAa1D,aAA3B,CAD2C,CAE3C;;MACA,IAAIuG,OAAO,IAAI9G,GAAf,EAAoB;QAClB,OAAO,IAAP;MACD,CAL0C,CAM3C;;;MACA,OAAO8G,OAAO,IAAIA,OAAO,IAAIH,OAA7B,EAAsC;QACpC,IAAIrG,KAAK,GAAGP,eAAe,CAAC+G,OAAD,CAA3B;QACAA,OAAO,GAAGxG,KAAK,IAAIA,KAAK,CAACC,aAAzB;;QACA,IAAIuG,OAAO,IAAI9G,GAAf,EAAoB;UAClB,OAAO,IAAP;QACD;MACF;;MACD,OAAO,KAAP;IACD,CAfD,CADJ;;IAiBA,IAAI6G,mBAAJ,EAAyB;MACvB;IACD,CA7BoE,CA+BrE;;;IACA,IAAIE,WAAW,GAAG,KAAK1D,uBAAL,CAA6BuD,KAA7B,CAAlB;;IACA,KAAKxD,oBAAL,CAA0B4D,MAA1B,CAAiCJ,KAAjC,EAAwC,CAAxC;;IACA,KAAKvD,uBAAL,CAA6B2D,MAA7B,CAAoCJ,KAApC,EAA2C,CAA3C;;IACAG,WAAW,GAnC0D,CAqCrE;;IACA,IAAI/G,GAAG,IAAI2G,OAAX,EAAoB;MAClB,IAAIrG,KAAK,GAAGP,eAAe,CAACC,GAAD,CAA3B;;MACA,IAAIM,KAAJ,EAAW;QACT,KAAKiE,uBAAL,CAA6BjE,KAAK,CAACC,aAAnC;MACD;IACF;EACF,CA5CD;EA+CA;AACA;AACA;AACA;AACA;;;EACAoB,oBAAoB,CAACjC,SAArB,CAA+BiF,0BAA/B,GAA4D,YAAW;IACrE,IAAIsC,YAAY,GAAG,KAAK5D,uBAAL,CAA6ByB,KAA7B,CAAmC,CAAnC,CAAnB;;IACA,KAAK1B,oBAAL,CAA0BoB,MAA1B,GAAmC,CAAnC;IACA,KAAKnB,uBAAL,CAA6BmB,MAA7B,GAAsC,CAAtC;;IACA,KAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6B,YAAY,CAACzC,MAAjC,EAAyCY,CAAC,EAA1C,EAA8C;MAC5C6B,YAAY,CAAC7B,CAAD,CAAZ;IACD;EACF,CAPD;EAUA;AACA;AACA;AACA;AACA;AACA;;;EACAzD,oBAAoB,CAACjC,SAArB,CAA+BwC,sBAA/B,GAAwD,YAAW;IACjE,IAAI,CAAC,KAAKF,IAAN,IAAcvB,kBAAd,IAAoC,CAACC,eAAzC,EAA0D;MACxD;MACA;IACD;;IAED,IAAIwG,WAAW,GAAG,KAAKC,YAAL,EAAlB;;IACA,IAAIC,QAAQ,GAAGF,WAAW,GAAG,KAAKG,YAAL,EAAH,GAAyBnG,YAAY,EAA/D;;IAEA,KAAKqB,mBAAL,CAAyBmB,OAAzB,CAAiC,UAASM,IAAT,EAAe;MAC9C,IAAInD,MAAM,GAAGmD,IAAI,CAACC,OAAlB;MACA,IAAI7C,UAAU,GAAGkG,qBAAqB,CAACzG,MAAD,CAAtC;;MACA,IAAI0G,kBAAkB,GAAG,KAAKC,mBAAL,CAAyB3G,MAAzB,CAAzB;;MACA,IAAI4G,QAAQ,GAAGzD,IAAI,CAACrD,KAApB;;MACA,IAAIM,gBAAgB,GAAGiG,WAAW,IAAIK,kBAAf,IACnB,KAAKG,iCAAL,CAAuC7G,MAAvC,EAA+CO,UAA/C,EAA2DgG,QAA3D,CADJ;;MAGA,IAAItG,UAAU,GAAG,IAAjB;;MACA,IAAI,CAAC,KAAK0G,mBAAL,CAAyB3G,MAAzB,CAAL,EAAuC;QACrCC,UAAU,GAAGI,YAAY,EAAzB;MACD,CAFD,MAEO,IAAI,CAACT,kBAAD,IAAuB,KAAKuB,IAAhC,EAAsC;QAC3ClB,UAAU,GAAGsG,QAAb;MACD;;MAED,IAAIO,QAAQ,GAAG3D,IAAI,CAACrD,KAAL,GAAa,IAAIlB,yBAAJ,CAA8B;QACxDmB,IAAI,EAAEgH,GAAG,EAD+C;QAExD/G,MAAM,EAAEA,MAFgD;QAGxDG,kBAAkB,EAAEI,UAHoC;QAIxDN,UAAU,EAAEA,UAJ4C;QAKxDG,gBAAgB,EAAEA;MALsC,CAA9B,CAA5B;;MAQA,IAAI,CAACwG,QAAL,EAAe;QACb,KAAKjF,cAAL,CAAoB2B,IAApB,CAAyBwD,QAAzB;MACD,CAFD,MAEO,IAAIT,WAAW,IAAIK,kBAAnB,EAAuC;QAC5C;QACA;QACA,IAAI,KAAKM,oBAAL,CAA0BJ,QAA1B,EAAoCE,QAApC,CAAJ,EAAmD;UACjD,KAAKnF,cAAL,CAAoB2B,IAApB,CAAyBwD,QAAzB;QACD;MACF,CANM,MAMA;QACL;QACA;QACA;QACA,IAAIF,QAAQ,IAAIA,QAAQ,CAACtG,cAAzB,EAAyC;UACvC,KAAKqB,cAAL,CAAoB2B,IAApB,CAAyBwD,QAAzB;QACD;MACF;IACF,CAvCD,EAuCG,IAvCH;;IAyCA,IAAI,KAAKnF,cAAL,CAAoBgC,MAAxB,EAAgC;MAC9B,KAAKlC,SAAL,CAAe,KAAKsC,WAAL,EAAf,EAAmC,IAAnC;IACD;EACF,CArDD;EAwDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACAjD,oBAAoB,CAACjC,SAArB,CAA+BgI,iCAA/B,GACI,UAAS7G,MAAT,EAAiBO,UAAjB,EAA6BgG,QAA7B,EAAuC;IACzC;IACA,IAAI5H,MAAM,CAACsI,gBAAP,CAAwBjH,MAAxB,EAAgCkH,OAAhC,IAA2C,MAA/C,EAAuD;IAEvD,IAAI9G,gBAAgB,GAAGG,UAAvB;IACA,IAAI4G,MAAM,GAAGC,aAAa,CAACpH,MAAD,CAA1B;IACA,IAAIqH,MAAM,GAAG,KAAb;;IAEA,OAAO,CAACA,MAAD,IAAWF,MAAlB,EAA0B;MACxB,IAAIG,UAAU,GAAG,IAAjB;MACA,IAAIC,mBAAmB,GAAGJ,MAAM,CAAC/F,QAAP,IAAmB,CAAnB,GACtBzC,MAAM,CAACsI,gBAAP,CAAwBE,MAAxB,CADsB,GACY,EADtC,CAFwB,CAKxB;;MACA,IAAII,mBAAmB,CAACL,OAApB,IAA+B,MAAnC,EAA2C,OAAO,IAAP;;MAE3C,IAAIC,MAAM,IAAI,KAAKhG,IAAf,IAAuBgG,MAAM,CAAC/F,QAAP;MAAmB;MAAe,CAA7D,EAAgE;QAC9DiG,MAAM,GAAG,IAAT;;QACA,IAAIF,MAAM,IAAI,KAAKhG,IAAf,IAAuBgG,MAAM,IAAI5H,QAArC,EAA+C;UAC7C,IAAIK,kBAAkB,IAAI,CAAC,KAAKuB,IAAhC,EAAsC;YACpC,IAAI,CAACtB,eAAD,IACAA,eAAe,CAACY,KAAhB,IAAyB,CAAzB,IAA8BZ,eAAe,CAACa,MAAhB,IAA0B,CAD5D,EAC+D;cAC7D;cACAyG,MAAM,GAAG,IAAT;cACAG,UAAU,GAAG,IAAb;cACAlH,gBAAgB,GAAG,IAAnB;YACD,CAND,MAMO;cACLkH,UAAU,GAAGzH,eAAb;YACD;UACF,CAVD,MAUO;YACLyH,UAAU,GAAGf,QAAb;UACD;QACF,CAdD,MAcO;UACL;UACA,IAAI9G,KAAK,GAAG2H,aAAa,CAACD,MAAD,CAAzB;UACA,IAAIK,SAAS,GAAG/H,KAAK,IAAIgH,qBAAqB,CAAChH,KAAD,CAA9C;;UACA,IAAIgI,cAAc,GACdhI,KAAK,IACL,KAAKoH,iCAAL,CAAuCpH,KAAvC,EAA8C+H,SAA9C,EAAyDjB,QAAzD,CAFJ;;UAGA,IAAIiB,SAAS,IAAIC,cAAjB,EAAiC;YAC/BN,MAAM,GAAG1H,KAAT;YACA6H,UAAU,GAAG1E,qBAAqB,CAAC4E,SAAD,EAAYC,cAAZ,CAAlC;UACD,CAHD,MAGO;YACLN,MAAM,GAAG,IAAT;YACA/G,gBAAgB,GAAG,IAAnB;UACD;QACF;MACF,CA/BD,MA+BO;QACL;QACA;QACA;QACA;QACA,IAAIjB,GAAG,GAAGgI,MAAM,CAACzH,aAAjB;;QACA,IAAIyH,MAAM,IAAIhI,GAAG,CAACuI,IAAd,IACAP,MAAM,IAAIhI,GAAG,CAACwI,eADd,IAEAJ,mBAAmB,CAACK,QAApB,IAAgC,SAFpC,EAE+C;UAC7CN,UAAU,GAAGb,qBAAqB,CAACU,MAAD,CAAlC;QACD;MACF,CAlDuB,CAoDxB;MACA;;;MACA,IAAIG,UAAJ,EAAgB;QACdlH,gBAAgB,GAAGyH,uBAAuB,CAACP,UAAD,EAAalH,gBAAb,CAA1C;MACD;;MACD,IAAI,CAACA,gBAAL,EAAuB;MACvB+G,MAAM,GAAGA,MAAM,IAAIC,aAAa,CAACD,MAAD,CAAhC;IACD;;IACD,OAAO/G,gBAAP;EACD,CAtED;EAyEA;AACA;AACA;AACA;AACA;;;EACAU,oBAAoB,CAACjC,SAArB,CAA+B2H,YAA/B,GAA8C,YAAW;IACvD,IAAID,QAAJ;;IACA,IAAI,KAAKpF,IAAL,IAAa,CAAC2G,KAAK,CAAC,KAAK3G,IAAN,CAAvB,EAAoC;MAClCoF,QAAQ,GAAGE,qBAAqB,CAAC,KAAKtF,IAAN,CAAhC;IACD,CAFD,MAEO;MACL;MACA,IAAIhC,GAAG,GAAG2I,KAAK,CAAC,KAAK3G,IAAN,CAAL,GAAmB,KAAKA,IAAxB,GAA+B5B,QAAzC;MACA,IAAIwI,IAAI,GAAG5I,GAAG,CAACwI,eAAf;MACA,IAAID,IAAI,GAAGvI,GAAG,CAACuI,IAAf;MACAnB,QAAQ,GAAG;QACTyB,GAAG,EAAE,CADI;QAETC,IAAI,EAAE,CAFG;QAGTC,KAAK,EAAEH,IAAI,CAACI,WAAL,IAAoBT,IAAI,CAACS,WAHvB;QAIT1H,KAAK,EAAEsH,IAAI,CAACI,WAAL,IAAoBT,IAAI,CAACS,WAJvB;QAKTC,MAAM,EAAEL,IAAI,CAACM,YAAL,IAAqBX,IAAI,CAACW,YALzB;QAMT3H,MAAM,EAAEqH,IAAI,CAACM,YAAL,IAAqBX,IAAI,CAACW;MANzB,CAAX;IAQD;;IACD,OAAO,KAAKC,uBAAL,CAA6B/B,QAA7B,CAAP;EACD,CAnBD;EAsBA;AACA;AACA;AACA;AACA;AACA;;;EACAzF,oBAAoB,CAACjC,SAArB,CAA+ByJ,uBAA/B,GAAyD,UAASC,IAAT,EAAe;IACtE,IAAI3D,OAAO,GAAG,KAAKhD,iBAAL,CAAuBM,GAAvB,CAA2B,UAASC,MAAT,EAAiBoC,CAAjB,EAAoB;MAC3D,OAAOpC,MAAM,CAACE,IAAP,IAAe,IAAf,GAAsBF,MAAM,CAACC,KAA7B,GACHD,MAAM,CAACC,KAAP,IAAgBmC,CAAC,GAAG,CAAJ,GAAQgE,IAAI,CAAC9H,KAAb,GAAqB8H,IAAI,CAAC7H,MAA1C,IAAoD,GADxD;IAED,CAHa,CAAd;;IAIA,IAAI8H,OAAO,GAAG;MACZR,GAAG,EAAEO,IAAI,CAACP,GAAL,GAAWpD,OAAO,CAAC,CAAD,CADX;MAEZsD,KAAK,EAAEK,IAAI,CAACL,KAAL,GAAatD,OAAO,CAAC,CAAD,CAFf;MAGZwD,MAAM,EAAEG,IAAI,CAACH,MAAL,GAAcxD,OAAO,CAAC,CAAD,CAHjB;MAIZqD,IAAI,EAAEM,IAAI,CAACN,IAAL,GAAYrD,OAAO,CAAC,CAAD;IAJb,CAAd;IAMA4D,OAAO,CAAC/H,KAAR,GAAgB+H,OAAO,CAACN,KAAR,GAAgBM,OAAO,CAACP,IAAxC;IACAO,OAAO,CAAC9H,MAAR,GAAiB8H,OAAO,CAACJ,MAAR,GAAiBI,OAAO,CAACR,GAA1C;IAEA,OAAOQ,OAAP;EACD,CAfD;EAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA1H,oBAAoB,CAACjC,SAArB,CAA+BmI,oBAA/B,GACI,UAASJ,QAAT,EAAmBE,QAAnB,EAA6B;IAE/B;IACA;IACA,IAAI2B,QAAQ,GAAG7B,QAAQ,IAAIA,QAAQ,CAACtG,cAArB,GACXsG,QAAQ,CAAC3H,iBAAT,IAA8B,CADnB,GACuB,CAAC,CADvC;IAEA,IAAIyJ,QAAQ,GAAG5B,QAAQ,CAACxG,cAAT,GACXwG,QAAQ,CAAC7H,iBAAT,IAA8B,CADnB,GACuB,CAAC,CADvC,CAN+B,CAS/B;;IACA,IAAIwJ,QAAQ,KAAKC,QAAjB,EAA2B;;IAE3B,KAAK,IAAInE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxC,UAAL,CAAgB4B,MAApC,EAA4CY,CAAC,EAA7C,EAAiD;MAC/C,IAAItC,SAAS,GAAG,KAAKF,UAAL,CAAgBwC,CAAhB,CAAhB,CAD+C,CAG/C;MACA;;MACA,IAAItC,SAAS,IAAIwG,QAAb,IAAyBxG,SAAS,IAAIyG,QAAtC,IACAzG,SAAS,GAAGwG,QAAZ,KAAyBxG,SAAS,GAAGyG,QADzC,EACmD;QACjD,OAAO,IAAP;MACD;IACF;EACF,CAvBD;EA0BA;AACA;AACA;AACA;AACA;;;EACA5H,oBAAoB,CAACjC,SAArB,CAA+ByH,YAA/B,GAA8C,YAAW;IACvD,OAAO,CAAC,KAAKnF,IAAN,IAAcwH,YAAY,CAACpJ,QAAD,EAAW,KAAK4B,IAAhB,CAAjC;EACD,CAFD;EAKA;AACA;AACA;AACA;AACA;AACA;;;EACAL,oBAAoB,CAACjC,SAArB,CAA+B8H,mBAA/B,GAAqD,UAAS3G,MAAT,EAAiB;IACpE,IAAI8F,OAAO,GACR,KAAK3E,IAAL,KAAc,KAAKA,IAAL,CAAUzB,aAAV,IAA2B,KAAKyB,IAA9C,CAAD,IAAyD5B,QAD3D;IAEA,OACEoJ,YAAY,CAAC7C,OAAD,EAAU9F,MAAV,CAAZ,KACC,CAAC,KAAKmB,IAAN,IAAc2E,OAAO,IAAI9F,MAAM,CAACN,aADjC,CADF;EAID,CAPD;EAUA;AACA;AACA;AACA;AACA;;;EACAoB,oBAAoB,CAACjC,SAArB,CAA+BwE,iBAA/B,GAAmD,YAAW;IAC5D,IAAI1D,QAAQ,CAACuF,OAAT,CAAiB,IAAjB,IAAyB,CAA7B,EAAgC;MAC9BvF,QAAQ,CAAC2D,IAAT,CAAc,IAAd;IACD;EACF,CAJD;EAOA;AACA;AACA;AACA;;;EACAxC,oBAAoB,CAACjC,SAArB,CAA+B+E,mBAA/B,GAAqD,YAAW;IAC9D,IAAImC,KAAK,GAAGpG,QAAQ,CAACuF,OAAT,CAAiB,IAAjB,CAAZ;IACA,IAAIa,KAAK,IAAI,CAAC,CAAd,EAAiBpG,QAAQ,CAACwG,MAAT,CAAgBJ,KAAhB,EAAuB,CAAvB;EAClB,CAHD;EAMA;AACA;AACA;AACA;AACA;;;EACA,SAASgB,GAAT,GAAe;IACb,OAAOpI,MAAM,CAACiK,WAAP,IAAsBA,WAAW,CAAC7B,GAAlC,IAAyC6B,WAAW,CAAC7B,GAAZ,EAAhD;EACD;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASzF,QAAT,CAAkBuH,EAAlB,EAAsBC,OAAtB,EAA+B;IAC7B,IAAIC,KAAK,GAAG,IAAZ;IACA,OAAO,YAAY;MACjB,IAAI,CAACA,KAAL,EAAY;QACVA,KAAK,GAAGC,UAAU,CAAC,YAAW;UAC5BH,EAAE;UACFE,KAAK,GAAG,IAAR;QACD,CAHiB,EAGfD,OAHe,CAAlB;MAID;IACF,CAPD;EAQD;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASxD,QAAT,CAAkB2D,IAAlB,EAAwBC,KAAxB,EAA+BL,EAA/B,EAAmCM,cAAnC,EAAmD;IACjD,IAAI,OAAOF,IAAI,CAACG,gBAAZ,IAAgC,UAApC,EAAgD;MAC9CH,IAAI,CAACG,gBAAL,CAAsBF,KAAtB,EAA6BL,EAA7B,EAAiCM,cAAc,IAAI,KAAnD;IACD,CAFD,MAGK,IAAI,OAAOF,IAAI,CAACI,WAAZ,IAA2B,UAA/B,EAA2C;MAC9CJ,IAAI,CAACI,WAAL,CAAiB,OAAOH,KAAxB,EAA+BL,EAA/B;IACD;EACF;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAAShD,WAAT,CAAqBoD,IAArB,EAA2BC,KAA3B,EAAkCL,EAAlC,EAAsCM,cAAtC,EAAsD;IACpD,IAAI,OAAOF,IAAI,CAACK,mBAAZ,IAAmC,UAAvC,EAAmD;MACjDL,IAAI,CAACK,mBAAL,CAAyBJ,KAAzB,EAAgCL,EAAhC,EAAoCM,cAAc,IAAI,KAAtD;IACD,CAFD,MAGK,IAAI,OAAOF,IAAI,CAACM,WAAZ,IAA2B,UAA/B,EAA2C;MAC9CN,IAAI,CAACM,WAAL,CAAiB,OAAOL,KAAxB,EAA+BL,EAA/B;IACD;EACF;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAAShB,uBAAT,CAAiC2B,KAAjC,EAAwCC,KAAxC,EAA+C;IAC7C,IAAIzB,GAAG,GAAG0B,IAAI,CAACC,GAAL,CAASH,KAAK,CAACxB,GAAf,EAAoByB,KAAK,CAACzB,GAA1B,CAAV;IACA,IAAII,MAAM,GAAGsB,IAAI,CAACE,GAAL,CAASJ,KAAK,CAACpB,MAAf,EAAuBqB,KAAK,CAACrB,MAA7B,CAAb;IACA,IAAIH,IAAI,GAAGyB,IAAI,CAACC,GAAL,CAASH,KAAK,CAACvB,IAAf,EAAqBwB,KAAK,CAACxB,IAA3B,CAAX;IACA,IAAIC,KAAK,GAAGwB,IAAI,CAACE,GAAL,CAASJ,KAAK,CAACtB,KAAf,EAAsBuB,KAAK,CAACvB,KAA5B,CAAZ;IACA,IAAIzH,KAAK,GAAGyH,KAAK,GAAGD,IAApB;IACA,IAAIvH,MAAM,GAAG0H,MAAM,GAAGJ,GAAtB;IAEA,OAAQvH,KAAK,IAAI,CAAT,IAAcC,MAAM,IAAI,CAAzB,IAA+B;MACpCsH,GAAG,EAAEA,GAD+B;MAEpCI,MAAM,EAAEA,MAF4B;MAGpCH,IAAI,EAAEA,IAH8B;MAIpCC,KAAK,EAAEA,KAJ6B;MAKpCzH,KAAK,EAAEA,KAL6B;MAMpCC,MAAM,EAAEA;IAN4B,CAA/B,IAOF,IAPL;EAQD;EAGD;AACA;AACA;AACA;AACA;;;EACA,SAAS+F,qBAAT,CAA+BoD,EAA/B,EAAmC;IACjC,IAAItB,IAAJ;;IAEA,IAAI;MACFA,IAAI,GAAGsB,EAAE,CAACpD,qBAAH,EAAP;IACD,CAFD,CAEE,OAAOqD,GAAP,EAAY,CACZ;MACA;IACD;;IAED,IAAI,CAACvB,IAAL,EAAW,OAAOlI,YAAY,EAAnB,CAVsB,CAYjC;;IACA,IAAI,EAAEkI,IAAI,CAAC9H,KAAL,IAAc8H,IAAI,CAAC7H,MAArB,CAAJ,EAAkC;MAChC6H,IAAI,GAAG;QACLP,GAAG,EAAEO,IAAI,CAACP,GADL;QAELE,KAAK,EAAEK,IAAI,CAACL,KAFP;QAGLE,MAAM,EAAEG,IAAI,CAACH,MAHR;QAILH,IAAI,EAAEM,IAAI,CAACN,IAJN;QAKLxH,KAAK,EAAE8H,IAAI,CAACL,KAAL,GAAaK,IAAI,CAACN,IALpB;QAMLvH,MAAM,EAAE6H,IAAI,CAACH,MAAL,GAAcG,IAAI,CAACP;MANtB,CAAP;IAQD;;IACD,OAAOO,IAAP;EACD;EAGD;AACA;AACA;AACA;AACA;;;EACA,SAASlI,YAAT,GAAwB;IACtB,OAAO;MACL2H,GAAG,EAAE,CADA;MAELI,MAAM,EAAE,CAFH;MAGLH,IAAI,EAAE,CAHD;MAILC,KAAK,EAAE,CAJF;MAKLzH,KAAK,EAAE,CALF;MAMLC,MAAM,EAAE;IANH,CAAP;EAQD;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASR,aAAT,CAAuBqI,IAAvB,EAA6B;IAC3B;IACA,IAAI,CAACA,IAAD,IAAS,OAAOA,IAApB,EAA0B;MACxB,OAAOA,IAAP;IACD,CAJ0B,CAK3B;IACA;IACA;IACA;;;IACA,OAAO;MACLP,GAAG,EAAEO,IAAI,CAACP,GADL;MAEL+B,CAAC,EAAExB,IAAI,CAACP,GAFH;MAGLI,MAAM,EAAEG,IAAI,CAACH,MAHR;MAILH,IAAI,EAAEM,IAAI,CAACN,IAJN;MAKL+B,CAAC,EAAEzB,IAAI,CAACN,IALH;MAMLC,KAAK,EAAEK,IAAI,CAACL,KANP;MAOLzH,KAAK,EAAE8H,IAAI,CAAC9H,KAPP;MAQLC,MAAM,EAAE6H,IAAI,CAAC7H;IARR,CAAP;EAUD;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASkC,qBAAT,CAA+BqH,kBAA/B,EAAmDC,sBAAnD,EAA2E;IACzE,IAAIlC,GAAG,GAAGkC,sBAAsB,CAAClC,GAAvB,GAA6BiC,kBAAkB,CAACjC,GAA1D;IACA,IAAIC,IAAI,GAAGiC,sBAAsB,CAACjC,IAAvB,GAA8BgC,kBAAkB,CAAChC,IAA5D;IACA,OAAO;MACLD,GAAG,EAAEA,GADA;MAELC,IAAI,EAAEA,IAFD;MAGLvH,MAAM,EAAEwJ,sBAAsB,CAACxJ,MAH1B;MAILD,KAAK,EAAEyJ,sBAAsB,CAACzJ,KAJzB;MAKL2H,MAAM,EAAEJ,GAAG,GAAGkC,sBAAsB,CAACxJ,MALhC;MAMLwH,KAAK,EAAED,IAAI,GAAGiC,sBAAsB,CAACzJ;IANhC,CAAP;EAQD;EAGD;AACA;AACA;AACA;AACA;AACA;AACA;;;EACA,SAASkI,YAAT,CAAsBxB,MAAtB,EAA8BgD,KAA9B,EAAqC;IACnC,IAAIlB,IAAI,GAAGkB,KAAX;;IACA,OAAOlB,IAAP,EAAa;MACX,IAAIA,IAAI,IAAI9B,MAAZ,EAAoB,OAAO,IAAP;MAEpB8B,IAAI,GAAG7B,aAAa,CAAC6B,IAAD,CAApB;IACD;;IACD,OAAO,KAAP;EACD;EAGD;AACA;AACA;AACA;AACA;AACA;;;EACA,SAAS7B,aAAT,CAAuB6B,IAAvB,EAA6B;IAC3B,IAAI9B,MAAM,GAAG8B,IAAI,CAACmB,UAAlB;;IAEA,IAAInB,IAAI,CAAC7H,QAAL;IAAiB;IAAe,CAAhC,IAAqC6H,IAAI,IAAI1J,QAAjD,EAA2D;MACzD;MACA,OAAOL,eAAe,CAAC+J,IAAD,CAAtB;IACD,CAN0B,CAQ3B;;;IACA,IAAI9B,MAAM,IAAIA,MAAM,CAACkD,YAArB,EAAmC;MACjClD,MAAM,GAAGA,MAAM,CAACkD,YAAP,CAAoBD,UAA7B;IACD;;IAED,IAAIjD,MAAM,IAAIA,MAAM,CAAC/F,QAAP,IAAmB,EAA7B,IAAmC+F,MAAM,CAACmD,IAA9C,EAAoD;MAClD;MACA,OAAOnD,MAAM,CAACmD,IAAd;IACD;;IAED,OAAOnD,MAAP;EACD;EAED;AACA;AACA;AACA;AACA;;;EACA,SAASW,KAAT,CAAemB,IAAf,EAAqB;IACnB,OAAOA,IAAI,IAAIA,IAAI,CAAC7H,QAAL,KAAkB,CAAjC;EACD,CAt+BW,CAy+BZ;;;EACAzC,MAAM,CAACmC,oBAAP,GAA8BA,oBAA9B;EACAnC,MAAM,CAACC,yBAAP,GAAmCA,yBAAnC;AAEC,CA7+BA,GAAD"}, "metadata": {}, "sourceType": "script"}