{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useMemo } from 'react';\nimport useToggle from '../useToggle';\nexport default function useBoolean(defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n\n  var _a = __read(useToggle(defaultValue), 2),\n      state = _a[0],\n      _b = _a[1],\n      toggle = _b.toggle,\n      _set = _b.set;\n\n  var actions = useMemo(function () {\n    var setTrue = function setTrue() {\n      return _set(true);\n    };\n\n    var setFalse = function setFalse() {\n      return _set(false);\n    };\n\n    return {\n      toggle: toggle,\n      set: function set(v) {\n        return _set(!!v);\n      },\n      setTrue: setTrue,\n      setFalse: setFalse\n    };\n  }, []);\n  return [state, actions];\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useMemo", "useToggle", "useBoolean", "defaultValue", "_a", "state", "_b", "toggle", "_set", "set", "actions", "setTrue", "setFalse", "v"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useBoolean/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useMemo } from 'react';\nimport useToggle from '../useToggle';\nexport default function useBoolean(defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useToggle(defaultValue), 2),\n    state = _a[0],\n    _b = _a[1],\n    toggle = _b.toggle,\n    _set = _b.set;\n  var actions = useMemo(function () {\n    var setTrue = function setTrue() {\n      return _set(true);\n    };\n    var setFalse = function setFalse() {\n      return _set(false);\n    };\n    return {\n      toggle: toggle,\n      set: function set(v) {\n        return _set(!!v);\n      },\n      setTrue: setTrue,\n      setFalse: setFalse\n    };\n  }, []);\n  return [state, actions];\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,OAAT,QAAwB,OAAxB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,eAAe,SAASC,UAAT,CAAoBC,YAApB,EAAkC;EAC/C,IAAIA,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAG,KAAf;EACD;;EACD,IAAIC,EAAE,GAAGpB,MAAM,CAACiB,SAAS,CAACE,YAAD,CAAV,EAA0B,CAA1B,CAAf;EAAA,IACEE,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,EAAE,GAAGF,EAAE,CAAC,CAAD,CAFT;EAAA,IAGEG,MAAM,GAAGD,EAAE,CAACC,MAHd;EAAA,IAIEC,IAAI,GAAGF,EAAE,CAACG,GAJZ;;EAKA,IAAIC,OAAO,GAAGV,OAAO,CAAC,YAAY;IAChC,IAAIW,OAAO,GAAG,SAASA,OAAT,GAAmB;MAC/B,OAAOH,IAAI,CAAC,IAAD,CAAX;IACD,CAFD;;IAGA,IAAII,QAAQ,GAAG,SAASA,QAAT,GAAoB;MACjC,OAAOJ,IAAI,CAAC,KAAD,CAAX;IACD,CAFD;;IAGA,OAAO;MACLD,MAAM,EAAEA,MADH;MAELE,GAAG,EAAE,SAASA,GAAT,CAAaI,CAAb,EAAgB;QACnB,OAAOL,IAAI,CAAC,CAAC,CAACK,CAAH,CAAX;MACD,CAJI;MAKLF,OAAO,EAAEA,OALJ;MAMLC,QAAQ,EAAEA;IANL,CAAP;EAQD,CAfoB,EAelB,EAfkB,CAArB;EAgBA,OAAO,CAACP,KAAD,EAAQK,OAAR,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}