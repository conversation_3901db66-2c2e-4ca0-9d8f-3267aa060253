{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n    inherits = require('inherits'),\n    utils = require('../../utils/event'),\n    urlUtils = require('../../utils/url'),\n    XHR = global.XMLHttpRequest;\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\n\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\n\ninherits(AbstractXHRObject, EventEmitter);\n\nAbstractXHRObject.prototype._start = function (method, url, payload, opts) {\n  var self = this;\n\n  try {\n    this.xhr = new XHR();\n  } catch (x) {// intentionally empty\n  }\n\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n\n    this._cleanup();\n\n    return;\n  } // several browsers cache POSTs\n\n\n  url = urlUtils.addQuery(url, 't=' + +new Date()); // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload cleanup');\n\n    self._cleanup(true);\n  });\n\n  try {\n    this.xhr.open(method, url, true);\n\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n\n      this.xhr.ontimeout = function () {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e); // IE raises an exception on wrong port.\n\n    this.emit('finish', 0, '');\n\n    this._cleanup(false);\n\n    return;\n  }\n\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials'); // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n\n  this.xhr.onreadystatechange = function () {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n\n      switch (x.readyState) {\n        case 3:\n          // IE doesn't like peeking into responseText or status\n          // on Microsoft.XMLHTTP and readystate=3\n          try {\n            status = x.status;\n            text = x.responseText;\n          } catch (e) {// intentionally empty\n          }\n\n          debug('status', status); // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n\n          if (status === 1223) {\n            status = 204;\n          } // IE does return readystate == 3 for 404 answers.\n\n\n          if (status === 200 && text && text.length > 0) {\n            debug('chunk');\n            self.emit('chunk', status, text);\n          }\n\n          break;\n\n        case 4:\n          status = x.status;\n          debug('status', status); // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n\n          if (status === 1223) {\n            status = 204;\n          } // IE returns this for a bad port\n          // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n\n\n          if (status === 12005 || status === 12029) {\n            status = 0;\n          }\n\n          debug('finish', status, x.responseText);\n          self.emit('finish', status, x.responseText);\n\n          self._cleanup(false);\n\n          break;\n      }\n    }\n  };\n\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n\n    self._cleanup(false);\n  }\n};\n\nAbstractXHRObject.prototype._cleanup = function (abort) {\n  debug('cleanup');\n\n  if (!this.xhr) {\n    return;\n  }\n\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef); // IE needs this field to be a function\n\n  this.xhr.onreadystatechange = function () {};\n\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {// intentionally empty\n    }\n  }\n\n  this.unloadRef = this.xhr = null;\n};\n\nAbstractXHRObject.prototype.close = function () {\n  debug('close');\n\n  this._cleanup(true);\n};\n\nAbstractXHRObject.enabled = !!XHR; // override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\n\nvar axo = ['Active'].concat('Object').join('X');\n\nif (!AbstractXHRObject.enabled && axo in global) {\n  debug('overriding xmlhttprequest');\n\n  XHR = function XHR() {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n\n  AbstractXHRObject.enabled = !!new XHR();\n}\n\nvar cors = false;\n\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {// intentionally empty\n}\n\nAbstractXHRObject.supportsCORS = cors;\nmodule.exports = AbstractXHRObject;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "utils", "urlUtils", "XHR", "global", "XMLHttpRequest", "debug", "process", "env", "NODE_ENV", "AbstractXHRObject", "method", "url", "payload", "opts", "self", "call", "setTimeout", "_start", "prototype", "xhr", "x", "emit", "_cleanup", "<PERSON><PERSON><PERSON><PERSON>", "Date", "unloadRef", "unloadAdd", "open", "timeout", "ontimeout", "e", "noCredentials", "supportsCORS", "withCredentials", "headers", "key", "setRequestHeader", "onreadystatechange", "text", "status", "readyState", "responseText", "length", "send", "abort", "removeAllListeners", "unloadDel", "close", "enabled", "axo", "concat", "join", "cors", "ignored", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/browser/abstract-xhr.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('../../utils/event')\n  , urlUtils = require('../../utils/url')\n  , XHR = global.XMLHttpRequest\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\n\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\n\ninherits(AbstractXHRObject, EventEmitter);\n\nAbstractXHRObject.prototype._start = function(method, url, payload, opts) {\n  var self = this;\n\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function() {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n\n  this.xhr.onreadystatechange = function() {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n      case 3:\n        // IE doesn't like peeking into responseText or status\n        // on Microsoft.XMLHTTP and readystate=3\n        try {\n          status = x.status;\n          text = x.responseText;\n        } catch (e) {\n          // intentionally empty\n        }\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n\n        // IE does return readystate == 3 for 404 answers.\n        if (status === 200 && text && text.length > 0) {\n          debug('chunk');\n          self.emit('chunk', status, text);\n        }\n        break;\n      case 4:\n        status = x.status;\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n        // IE returns this for a bad port\n        // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n        if (status === 12005 || status === 12029) {\n          status = 0;\n        }\n\n        debug('finish', status, x.responseText);\n        self.emit('finish', status, x.responseText);\n        self._cleanup(false);\n        break;\n      }\n    }\n  };\n\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\n\nAbstractXHRObject.prototype._cleanup = function(abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function() {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\n\nAbstractXHRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && (axo in global)) {\n  debug('overriding xmlhttprequest');\n  XHR = function() {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\n\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\n\nAbstractXHRObject.supportsCORS = cors;\n\nmodule.exports = AbstractXHRObject;\n"], "mappings": "AAAA;;AAEA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAD,CAAP,CAAkBD,YAArC;AAAA,IACIE,QAAQ,GAAGD,OAAO,CAAC,UAAD,CADtB;AAAA,IAEIE,KAAK,GAAGF,OAAO,CAAC,mBAAD,CAFnB;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,iBAAD,CAHtB;AAAA,IAIII,GAAG,GAAGC,MAAM,CAACC,cAJjB;;AAOA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGP,OAAO,CAAC,OAAD,CAAP,CAAiB,2BAAjB,CAAR;AACD;;AAED,SAASW,iBAAT,CAA2BC,MAA3B,EAAmCC,GAAnC,EAAwCC,OAAxC,EAAiDC,IAAjD,EAAuD;EACrDR,KAAK,CAACK,MAAD,EAASC,GAAT,CAAL;EACA,IAAIG,IAAI,GAAG,IAAX;EACAjB,YAAY,CAACkB,IAAb,CAAkB,IAAlB;EAEAC,UAAU,CAAC,YAAY;IACrBF,IAAI,CAACG,MAAL,CAAYP,MAAZ,EAAoBC,GAApB,EAAyBC,OAAzB,EAAkCC,IAAlC;EACD,CAFS,EAEP,CAFO,CAAV;AAGD;;AAEDd,QAAQ,CAACU,iBAAD,EAAoBZ,YAApB,CAAR;;AAEAY,iBAAiB,CAACS,SAAlB,CAA4BD,MAA5B,GAAqC,UAASP,MAAT,EAAiBC,GAAjB,EAAsBC,OAAtB,EAA+BC,IAA/B,EAAqC;EACxE,IAAIC,IAAI,GAAG,IAAX;;EAEA,IAAI;IACF,KAAKK,GAAL,GAAW,IAAIjB,GAAJ,EAAX;EACD,CAFD,CAEE,OAAOkB,CAAP,EAAU,CACV;EACD;;EAED,IAAI,CAAC,KAAKD,GAAV,EAAe;IACbd,KAAK,CAAC,QAAD,CAAL;IACA,KAAKgB,IAAL,CAAU,QAAV,EAAoB,CAApB,EAAuB,gBAAvB;;IACA,KAAKC,QAAL;;IACA;EACD,CAduE,CAgBxE;;;EACAX,GAAG,GAAGV,QAAQ,CAACsB,QAAT,CAAkBZ,GAAlB,EAAuB,OAAQ,CAAC,IAAIa,IAAJ,EAAhC,CAAN,CAjBwE,CAmBxE;EACA;;EACA,KAAKC,SAAL,GAAiBzB,KAAK,CAAC0B,SAAN,CAAgB,YAAW;IAC1CrB,KAAK,CAAC,gBAAD,CAAL;;IACAS,IAAI,CAACQ,QAAL,CAAc,IAAd;EACD,CAHgB,CAAjB;;EAIA,IAAI;IACF,KAAKH,GAAL,CAASQ,IAAT,CAAcjB,MAAd,EAAsBC,GAAtB,EAA2B,IAA3B;;IACA,IAAI,KAAKiB,OAAL,IAAgB,aAAa,KAAKT,GAAtC,EAA2C;MACzC,KAAKA,GAAL,CAASS,OAAT,GAAmB,KAAKA,OAAxB;;MACA,KAAKT,GAAL,CAASU,SAAT,GAAqB,YAAW;QAC9BxB,KAAK,CAAC,aAAD,CAAL;QACAS,IAAI,CAACO,IAAL,CAAU,QAAV,EAAoB,CAApB,EAAuB,EAAvB;;QACAP,IAAI,CAACQ,QAAL,CAAc,KAAd;MACD,CAJD;IAKD;EACF,CAVD,CAUE,OAAOQ,CAAP,EAAU;IACVzB,KAAK,CAAC,WAAD,EAAcyB,CAAd,CAAL,CADU,CAEV;;IACA,KAAKT,IAAL,CAAU,QAAV,EAAoB,CAApB,EAAuB,EAAvB;;IACA,KAAKC,QAAL,CAAc,KAAd;;IACA;EACD;;EAED,IAAI,CAAC,CAACT,IAAD,IAAS,CAACA,IAAI,CAACkB,aAAhB,KAAkCtB,iBAAiB,CAACuB,YAAxD,EAAsE;IACpE3B,KAAK,CAAC,iBAAD,CAAL,CADoE,CAEpE;IACA;;IAEA,KAAKc,GAAL,CAASc,eAAT,GAA2B,IAA3B;EACD;;EACD,IAAIpB,IAAI,IAAIA,IAAI,CAACqB,OAAjB,EAA0B;IACxB,KAAK,IAAIC,GAAT,IAAgBtB,IAAI,CAACqB,OAArB,EAA8B;MAC5B,KAAKf,GAAL,CAASiB,gBAAT,CAA0BD,GAA1B,EAA+BtB,IAAI,CAACqB,OAAL,CAAaC,GAAb,CAA/B;IACD;EACF;;EAED,KAAKhB,GAAL,CAASkB,kBAAT,GAA8B,YAAW;IACvC,IAAIvB,IAAI,CAACK,GAAT,EAAc;MACZ,IAAIC,CAAC,GAAGN,IAAI,CAACK,GAAb;MACA,IAAImB,IAAJ,EAAUC,MAAV;MACAlC,KAAK,CAAC,YAAD,EAAee,CAAC,CAACoB,UAAjB,CAAL;;MACA,QAAQpB,CAAC,CAACoB,UAAV;QACA,KAAK,CAAL;UACE;UACA;UACA,IAAI;YACFD,MAAM,GAAGnB,CAAC,CAACmB,MAAX;YACAD,IAAI,GAAGlB,CAAC,CAACqB,YAAT;UACD,CAHD,CAGE,OAAOX,CAAP,EAAU,CACV;UACD;;UACDzB,KAAK,CAAC,QAAD,EAAWkC,MAAX,CAAL,CATF,CAUE;;UACA,IAAIA,MAAM,KAAK,IAAf,EAAqB;YACnBA,MAAM,GAAG,GAAT;UACD,CAbH,CAeE;;;UACA,IAAIA,MAAM,KAAK,GAAX,IAAkBD,IAAlB,IAA0BA,IAAI,CAACI,MAAL,GAAc,CAA5C,EAA+C;YAC7CrC,KAAK,CAAC,OAAD,CAAL;YACAS,IAAI,CAACO,IAAL,CAAU,OAAV,EAAmBkB,MAAnB,EAA2BD,IAA3B;UACD;;UACD;;QACF,KAAK,CAAL;UACEC,MAAM,GAAGnB,CAAC,CAACmB,MAAX;UACAlC,KAAK,CAAC,QAAD,EAAWkC,MAAX,CAAL,CAFF,CAGE;;UACA,IAAIA,MAAM,KAAK,IAAf,EAAqB;YACnBA,MAAM,GAAG,GAAT;UACD,CANH,CAOE;UACA;;;UACA,IAAIA,MAAM,KAAK,KAAX,IAAoBA,MAAM,KAAK,KAAnC,EAA0C;YACxCA,MAAM,GAAG,CAAT;UACD;;UAEDlC,KAAK,CAAC,QAAD,EAAWkC,MAAX,EAAmBnB,CAAC,CAACqB,YAArB,CAAL;UACA3B,IAAI,CAACO,IAAL,CAAU,QAAV,EAAoBkB,MAApB,EAA4BnB,CAAC,CAACqB,YAA9B;;UACA3B,IAAI,CAACQ,QAAL,CAAc,KAAd;;UACA;MAtCF;IAwCD;EACF,CA9CD;;EAgDA,IAAI;IACFR,IAAI,CAACK,GAAL,CAASwB,IAAT,CAAc/B,OAAd;EACD,CAFD,CAEE,OAAOkB,CAAP,EAAU;IACVhB,IAAI,CAACO,IAAL,CAAU,QAAV,EAAoB,CAApB,EAAuB,EAAvB;;IACAP,IAAI,CAACQ,QAAL,CAAc,KAAd;EACD;AACF,CA9GD;;AAgHAb,iBAAiB,CAACS,SAAlB,CAA4BI,QAA5B,GAAuC,UAASsB,KAAT,EAAgB;EACrDvC,KAAK,CAAC,SAAD,CAAL;;EACA,IAAI,CAAC,KAAKc,GAAV,EAAe;IACb;EACD;;EACD,KAAK0B,kBAAL;EACA7C,KAAK,CAAC8C,SAAN,CAAgB,KAAKrB,SAArB,EANqD,CAQrD;;EACA,KAAKN,GAAL,CAASkB,kBAAT,GAA8B,YAAW,CAAE,CAA3C;;EACA,IAAI,KAAKlB,GAAL,CAASU,SAAb,EAAwB;IACtB,KAAKV,GAAL,CAASU,SAAT,GAAqB,IAArB;EACD;;EAED,IAAIe,KAAJ,EAAW;IACT,IAAI;MACF,KAAKzB,GAAL,CAASyB,KAAT;IACD,CAFD,CAEE,OAAOxB,CAAP,EAAU,CACV;IACD;EACF;;EACD,KAAKK,SAAL,GAAiB,KAAKN,GAAL,GAAW,IAA5B;AACD,CAtBD;;AAwBAV,iBAAiB,CAACS,SAAlB,CAA4B6B,KAA5B,GAAoC,YAAW;EAC7C1C,KAAK,CAAC,OAAD,CAAL;;EACA,KAAKiB,QAAL,CAAc,IAAd;AACD,CAHD;;AAKAb,iBAAiB,CAACuC,OAAlB,GAA4B,CAAC,CAAC9C,GAA9B,C,CACA;AACA;;AACA,IAAI+C,GAAG,GAAG,CAAC,QAAD,EAAWC,MAAX,CAAkB,QAAlB,EAA4BC,IAA5B,CAAiC,GAAjC,CAAV;;AACA,IAAI,CAAC1C,iBAAiB,CAACuC,OAAnB,IAA+BC,GAAG,IAAI9C,MAA1C,EAAmD;EACjDE,KAAK,CAAC,2BAAD,CAAL;;EACAH,GAAG,GAAG,eAAW;IACf,IAAI;MACF,OAAO,IAAIC,MAAM,CAAC8C,GAAD,CAAV,CAAgB,mBAAhB,CAAP;IACD,CAFD,CAEE,OAAOnB,CAAP,EAAU;MACV,OAAO,IAAP;IACD;EACF,CAND;;EAOArB,iBAAiB,CAACuC,OAAlB,GAA4B,CAAC,CAAC,IAAI9C,GAAJ,EAA9B;AACD;;AAED,IAAIkD,IAAI,GAAG,KAAX;;AACA,IAAI;EACFA,IAAI,GAAG,qBAAqB,IAAIlD,GAAJ,EAA5B;AACD,CAFD,CAEE,OAAOmD,OAAP,EAAgB,CAChB;AACD;;AAED5C,iBAAiB,CAACuB,YAAlB,GAAiCoB,IAAjC;AAEAE,MAAM,CAACC,OAAP,GAAiB9C,iBAAjB"}, "metadata": {}, "sourceType": "script"}