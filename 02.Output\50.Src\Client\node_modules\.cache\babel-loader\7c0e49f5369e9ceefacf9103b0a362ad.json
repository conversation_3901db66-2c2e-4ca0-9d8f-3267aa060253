{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n/* eslint-disable no-empty */\n\n\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    var storage; // https://github.com/alibaba/hooks/issues/800\n\n    try {\n      storage = getStorage();\n    } catch (err) {\n      console.error(err);\n    }\n\n    var serializer = function serializer(value) {\n      if (options === null || options === void 0 ? void 0 : options.serializer) {\n        return options === null || options === void 0 ? void 0 : options.serializer(value);\n      }\n\n      return JSON.stringify(value);\n    };\n\n    var deserializer = function deserializer(value) {\n      if (options === null || options === void 0 ? void 0 : options.deserializer) {\n        return options === null || options === void 0 ? void 0 : options.deserializer(value);\n      }\n\n      return JSON.parse(value);\n    };\n\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        console.error(e);\n      }\n\n      if (isFunction(options === null || options === void 0 ? void 0 : options.defaultValue)) {\n        return options === null || options === void 0 ? void 0 : options.defaultValue();\n      }\n\n      return options === null || options === void 0 ? void 0 : options.defaultValue;\n    }\n\n    var _a = __read(useState(function () {\n      return getStoredValue();\n    }), 2),\n        state = _a[0],\n        setState = _a[1];\n\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n\n    var updateState = function updateState(value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      setState(currentState);\n\n      if (isUndef(currentState)) {\n        storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n      } else {\n        try {\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, serializer(currentState));\n        } catch (e) {\n          console.error(e);\n        }\n      }\n    };\n\n    return [state, useMemoizedFn(updateState)];\n  }\n\n  return useStorageState;\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useMemoizedFn", "useUpdateEffect", "isFunction", "isUndef", "createUseStorageState", "getStorage", "useStorageState", "key", "options", "storage", "err", "console", "serializer", "JSON", "stringify", "deserializer", "parse", "getStoredValue", "raw", "getItem", "defaultValue", "_a", "state", "setState", "updateState", "currentState", "removeItem", "setItem"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/createUseStorageState/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\n/* eslint-disable no-empty */\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    var storage;\n    // https://github.com/alibaba/hooks/issues/800\n    try {\n      storage = getStorage();\n    } catch (err) {\n      console.error(err);\n    }\n    var serializer = function serializer(value) {\n      if (options === null || options === void 0 ? void 0 : options.serializer) {\n        return options === null || options === void 0 ? void 0 : options.serializer(value);\n      }\n      return JSON.stringify(value);\n    };\n    var deserializer = function deserializer(value) {\n      if (options === null || options === void 0 ? void 0 : options.deserializer) {\n        return options === null || options === void 0 ? void 0 : options.deserializer(value);\n      }\n      return JSON.parse(value);\n    };\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        console.error(e);\n      }\n      if (isFunction(options === null || options === void 0 ? void 0 : options.defaultValue)) {\n        return options === null || options === void 0 ? void 0 : options.defaultValue();\n      }\n      return options === null || options === void 0 ? void 0 : options.defaultValue;\n    }\n    var _a = __read(useState(function () {\n        return getStoredValue();\n      }), 2),\n      state = _a[0],\n      setState = _a[1];\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n    var updateState = function updateState(value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      setState(currentState);\n      if (isUndef(currentState)) {\n        storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n      } else {\n        try {\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, serializer(currentState));\n        } catch (e) {\n          console.error(e);\n        }\n      }\n    };\n    return [state, useMemoizedFn(updateState)];\n  }\n  return useStorageState;\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;AAwBA;;;AACA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,eAAP,MAA4B,oBAA5B;AACA,SAASC,UAAT,EAAqBC,OAArB,QAAoC,UAApC;AACA,OAAO,SAASC,qBAAT,CAA+BC,UAA/B,EAA2C;EAChD,SAASC,eAAT,CAAyBC,GAAzB,EAA8BC,OAA9B,EAAuC;IACrC,IAAIC,OAAJ,CADqC,CAErC;;IACA,IAAI;MACFA,OAAO,GAAGJ,UAAU,EAApB;IACD,CAFD,CAEE,OAAOK,GAAP,EAAY;MACZC,OAAO,CAACb,KAAR,CAAcY,GAAd;IACD;;IACD,IAAIE,UAAU,GAAG,SAASA,UAAT,CAAoBf,KAApB,EAA2B;MAC1C,IAAIW,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACI,UAA9D,EAA0E;QACxE,OAAOJ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACI,UAAR,CAAmBf,KAAnB,CAAzD;MACD;;MACD,OAAOgB,IAAI,CAACC,SAAL,CAAejB,KAAf,CAAP;IACD,CALD;;IAMA,IAAIkB,YAAY,GAAG,SAASA,YAAT,CAAsBlB,KAAtB,EAA6B;MAC9C,IAAIW,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACO,YAA9D,EAA4E;QAC1E,OAAOP,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACO,YAAR,CAAqBlB,KAArB,CAAzD;MACD;;MACD,OAAOgB,IAAI,CAACG,KAAL,CAAWnB,KAAX,CAAP;IACD,CALD;;IAMA,SAASoB,cAAT,GAA0B;MACxB,IAAI;QACF,IAAIC,GAAG,GAAGT,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACU,OAAR,CAAgBZ,GAAhB,CAA5D;;QACA,IAAIW,GAAJ,EAAS;UACP,OAAOH,YAAY,CAACG,GAAD,CAAnB;QACD;MACF,CALD,CAKE,OAAOzB,CAAP,EAAU;QACVkB,OAAO,CAACb,KAAR,CAAcL,CAAd;MACD;;MACD,IAAIS,UAAU,CAACM,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACY,YAA3D,CAAd,EAAwF;QACtF,OAAOZ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACY,YAAR,EAAzD;MACD;;MACD,OAAOZ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACY,YAAjE;IACD;;IACD,IAAIC,EAAE,GAAGtC,MAAM,CAACgB,QAAQ,CAAC,YAAY;MACjC,OAAOkB,cAAc,EAArB;IACD,CAFqB,CAAT,EAET,CAFS,CAAf;IAAA,IAGEK,KAAK,GAAGD,EAAE,CAAC,CAAD,CAHZ;IAAA,IAIEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAJf;;IAKApB,eAAe,CAAC,YAAY;MAC1BsB,QAAQ,CAACN,cAAc,EAAf,CAAR;IACD,CAFc,EAEZ,CAACV,GAAD,CAFY,CAAf;;IAGA,IAAIiB,WAAW,GAAG,SAASA,WAAT,CAAqB3B,KAArB,EAA4B;MAC5C,IAAI4B,YAAY,GAAGvB,UAAU,CAACL,KAAD,CAAV,GAAoBA,KAAK,CAACyB,KAAD,CAAzB,GAAmCzB,KAAtD;MACA0B,QAAQ,CAACE,YAAD,CAAR;;MACA,IAAItB,OAAO,CAACsB,YAAD,CAAX,EAA2B;QACzBhB,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACiB,UAAR,CAAmBnB,GAAnB,CAAlD;MACD,CAFD,MAEO;QACL,IAAI;UACFE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACkB,OAAR,CAAgBpB,GAAhB,EAAqBK,UAAU,CAACa,YAAD,CAA/B,CAAlD;QACD,CAFD,CAEE,OAAOhC,CAAP,EAAU;UACVkB,OAAO,CAACb,KAAR,CAAcL,CAAd;QACD;MACF;IACF,CAZD;;IAaA,OAAO,CAAC6B,KAAD,EAAQtB,aAAa,CAACwB,WAAD,CAArB,CAAP;EACD;;EACD,OAAOlB,eAAP;AACD"}, "metadata": {}, "sourceType": "module"}