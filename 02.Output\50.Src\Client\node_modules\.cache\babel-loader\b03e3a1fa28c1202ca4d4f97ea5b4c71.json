{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n        screenY = event.screenY,\n        clientX = event.clientX,\n        clientY = event.clientY,\n        pageX = event.pageX,\n        pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n          left = _a.left,\n          top_1 = _a.top,\n          width = _a.width,\n          height = _a.height;\n\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n\n    setState(newState);\n  }, {\n    target: function target() {\n      return document;\n    }\n  });\n  return state;\n});", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useRafState", "useEventListener", "getTargetElement", "initState", "screenX", "NaN", "screenY", "clientX", "clientY", "pageX", "pageY", "elementX", "elementY", "elementH", "elementW", "elementPosX", "elementPosY", "target", "_a", "state", "setState", "event", "newState", "targetElement", "getBoundingClientRect", "left", "top_1", "top", "width", "height", "window", "pageXOffset", "pageYOffset", "document"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useMouse/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n      screenY = event.screenY,\n      clientX = event.clientX,\n      clientY = event.clientY,\n      pageX = event.pageX,\n      pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n        left = _a.left,\n        top_1 = _a.top,\n        width = _a.width,\n        height = _a.height;\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n    setState(newState);\n  }, {\n    target: function target() {\n      return document;\n    }\n  });\n  return state;\n});"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,OAAOO,WAAP,MAAwB,gBAAxB;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,IAAIC,SAAS,GAAG;EACdC,OAAO,EAAEC,GADK;EAEdC,OAAO,EAAED,GAFK;EAGdE,OAAO,EAAEF,GAHK;EAIdG,OAAO,EAAEH,GAJK;EAKdI,KAAK,EAAEJ,GALO;EAMdK,KAAK,EAAEL,GANO;EAOdM,QAAQ,EAAEN,GAPI;EAQdO,QAAQ,EAAEP,GARI;EASdQ,QAAQ,EAAER,GATI;EAUdS,QAAQ,EAAET,GAVI;EAWdU,WAAW,EAAEV,GAXC;EAYdW,WAAW,EAAEX;AAZC,CAAhB;AAcA,gBAAgB,UAAUY,MAAV,EAAkB;EAChC,IAAIC,EAAE,GAAGlC,MAAM,CAACgB,WAAW,CAACG,SAAD,CAAZ,EAAyB,CAAzB,CAAf;EAAA,IACEgB,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGAjB,gBAAgB,CAAC,WAAD,EAAc,UAAUoB,KAAV,EAAiB;IAC7C,IAAIjB,OAAO,GAAGiB,KAAK,CAACjB,OAApB;IAAA,IACEE,OAAO,GAAGe,KAAK,CAACf,OADlB;IAAA,IAEEC,OAAO,GAAGc,KAAK,CAACd,OAFlB;IAAA,IAGEC,OAAO,GAAGa,KAAK,CAACb,OAHlB;IAAA,IAIEC,KAAK,GAAGY,KAAK,CAACZ,KAJhB;IAAA,IAKEC,KAAK,GAAGW,KAAK,CAACX,KALhB;IAMA,IAAIY,QAAQ,GAAG;MACblB,OAAO,EAAEA,OADI;MAEbE,OAAO,EAAEA,OAFI;MAGbC,OAAO,EAAEA,OAHI;MAIbC,OAAO,EAAEA,OAJI;MAKbC,KAAK,EAAEA,KALM;MAMbC,KAAK,EAAEA,KANM;MAObC,QAAQ,EAAEN,GAPG;MAQbO,QAAQ,EAAEP,GARG;MASbQ,QAAQ,EAAER,GATG;MAUbS,QAAQ,EAAET,GAVG;MAWbU,WAAW,EAAEV,GAXA;MAYbW,WAAW,EAAEX;IAZA,CAAf;IAcA,IAAIkB,aAAa,GAAGrB,gBAAgB,CAACe,MAAD,CAApC;;IACA,IAAIM,aAAJ,EAAmB;MACjB,IAAIL,EAAE,GAAGK,aAAa,CAACC,qBAAd,EAAT;MAAA,IACEC,IAAI,GAAGP,EAAE,CAACO,IADZ;MAAA,IAEEC,KAAK,GAAGR,EAAE,CAACS,GAFb;MAAA,IAGEC,KAAK,GAAGV,EAAE,CAACU,KAHb;MAAA,IAIEC,MAAM,GAAGX,EAAE,CAACW,MAJd;;MAKAP,QAAQ,CAACP,WAAT,GAAuBU,IAAI,GAAGK,MAAM,CAACC,WAArC;MACAT,QAAQ,CAACN,WAAT,GAAuBU,KAAK,GAAGI,MAAM,CAACE,WAAtC;MACAV,QAAQ,CAACX,QAAT,GAAoBF,KAAK,GAAGa,QAAQ,CAACP,WAArC;MACAO,QAAQ,CAACV,QAAT,GAAoBF,KAAK,GAAGY,QAAQ,CAACN,WAArC;MACAM,QAAQ,CAACR,QAAT,GAAoBc,KAApB;MACAN,QAAQ,CAACT,QAAT,GAAoBgB,MAApB;IACD;;IACDT,QAAQ,CAACE,QAAD,CAAR;EACD,CApCe,EAoCb;IACDL,MAAM,EAAE,SAASA,MAAT,GAAkB;MACxB,OAAOgB,QAAP;IACD;EAHA,CApCa,CAAhB;EAyCA,OAAOd,KAAP;AACD,CA9CD"}, "metadata": {}, "sourceType": "module"}