{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    HtmlfileReceiver = require('./receiver/htmlfile'),\n    XHRLocalObject = require('./sender/xhr-local'),\n    AjaxBasedTransport = require('./lib/ajax-based');\n\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\n\ninherits(HtmlFileTransport, AjaxBasedTransport);\n\nHtmlFileTransport.enabled = function (info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\n\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\nmodule.exports = HtmlFileTransport;", "map": {"version": 3, "names": ["inherits", "require", "HtmlfileReceiver", "XHRLocalObject", "AjaxBasedTransport", "HtmlFileTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/htmlfile.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , HtmlfileReceiver = require('./receiver/htmlfile')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  ;\n\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\n\ninherits(HtmlFileTransport, AjaxBasedTransport);\n\nHtmlFileTransport.enabled = function(info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\n\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\n\nmodule.exports = HtmlFileTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,gBAAgB,GAAGD,OAAO,CAAC,qBAAD,CAD9B;AAAA,IAEIE,cAAc,GAAGF,OAAO,CAAC,oBAAD,CAF5B;AAAA,IAGIG,kBAAkB,GAAGH,OAAO,CAAC,kBAAD,CAHhC;;AAMA,SAASI,iBAAT,CAA2BC,QAA3B,EAAqC;EACnC,IAAI,CAACJ,gBAAgB,CAACK,OAAtB,EAA+B;IAC7B,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EACDJ,kBAAkB,CAACK,IAAnB,CAAwB,IAAxB,EAA8BH,QAA9B,EAAwC,WAAxC,EAAqDJ,gBAArD,EAAuEC,cAAvE;AACD;;AAEDH,QAAQ,CAACK,iBAAD,EAAoBD,kBAApB,CAAR;;AAEAC,iBAAiB,CAACE,OAAlB,GAA4B,UAASG,IAAT,EAAe;EACzC,OAAOR,gBAAgB,CAACK,OAAjB,IAA4BG,IAAI,CAACC,UAAxC;AACD,CAFD;;AAIAN,iBAAiB,CAACO,aAAlB,GAAkC,UAAlC;AACAP,iBAAiB,CAACQ,UAAlB,GAA+B,CAA/B;AAEAC,MAAM,CAACC,OAAP,GAAiBV,iBAAjB"}, "metadata": {}, "sourceType": "script"}