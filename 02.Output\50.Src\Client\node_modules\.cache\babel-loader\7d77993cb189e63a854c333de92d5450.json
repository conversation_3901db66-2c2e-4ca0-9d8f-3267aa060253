{"ast": null, "code": "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Takes an array of string of versions, typical elements '1.0', '1.1', or '1.2'\n   *\n   * You will an instance if this class if you want to override supported versions to be declared during\n   * STOMP handshake.\n   */\n  constructor(versions) {\n    this.versions = versions;\n  }\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n\n\n  supportedVersions() {\n    return this.versions.join(',');\n  }\n  /**\n   * Used while creating a WebSocket\n   */\n\n\n  protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n\n}\n/**\n * Indicates protocol version 1.0\n */\n\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\n\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\n\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\n\nVersions.default = new Versions([Versions.V1_0, Versions.V1_1, Versions.V1_2]);", "map": {"version": 3, "mappings": "AAAA;;;;;AAKA,OAAM,MAAOA,QAAP,CAAe;EAuBnB;;;;;;EAMAC,YAAmBC,QAAnB,EAAqC;IAAlB;EAAsB;EAEzC;;;;;EAGOC,iBAAiB;IACtB,OAAO,KAAKD,QAAL,CAAcE,IAAd,CAAmB,GAAnB,CAAP;EACD;EAED;;;;;EAGOC,gBAAgB;IACrB,OAAO,KAAKH,QAAL,CAAcI,GAAd,CAAkBC,CAAC,IAAI,IAAIA,CAAC,CAACC,OAAF,CAAU,GAAV,EAAe,EAAf,CAAkB,QAA7C,CAAP;EACD;;AA3CkB;AACnB;;;;AAGcR,gBAAO,KAAP;AACd;;;;AAGcA,gBAAO,KAAP;AACd;;;;AAGcA,gBAAO,KAAP;AAEd;;;;AAGcA,mBAAU,IAAIA,QAAJ,CAAa,CACnCA,QAAQ,CAACS,IAD0B,EAEnCT,QAAQ,CAACU,IAF0B,EAGnCV,QAAQ,CAACW,IAH0B,CAAb,CAAV", "names": ["Versions", "constructor", "versions", "supportedVersions", "join", "protocolVersions", "map", "x", "replace", "V1_0", "V1_1", "V1_2"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\versions.ts"], "sourcesContent": ["/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Indicates protocol version 1.0\n   */\n  public static V1_0 = '1.0';\n  /**\n   * Indicates protocol version 1.1\n   */\n  public static V1_1 = '1.1';\n  /**\n   * Indicates protocol version 1.2\n   */\n  public static V1_2 = '1.2';\n\n  /**\n   * @internal\n   */\n  public static default = new Versions([\n    Versions.V1_0,\n    Versions.V1_1,\n    Versions.V1_2,\n  ]);\n\n  /**\n   * Takes an array of string of versions, typical elements '1.0', '1.1', or '1.2'\n   *\n   * You will an instance if this class if you want to override supported versions to be declared during\n   * STOMP handshake.\n   */\n  constructor(public versions: string[]) {}\n\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  public supportedVersions() {\n    return this.versions.join(',');\n  }\n\n  /**\n   * Used while creating a WebSocket\n   */\n  public protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}