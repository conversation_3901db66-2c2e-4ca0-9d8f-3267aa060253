{"ast": null, "code": "var getNative = require('./_getNative'),\n    root = require('./_root');\n/* Built-in method references that are verified to be native. */\n\n\nvar DataView = getNative(root, 'DataView');\nmodule.exports = DataView;", "map": {"version": 3, "names": ["getNative", "require", "root", "DataView", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_DataView.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\n\nmodule.exports = DataView;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAD,CAAvB;AAAA,IACIC,IAAI,GAAGD,OAAO,CAAC,SAAD,CADlB;AAGA;;;AACA,IAAIE,QAAQ,GAAGH,SAAS,CAACE,IAAD,EAAO,UAAP,CAAxB;AAEAE,MAAM,CAACC,OAAP,GAAiBF,QAAjB"}, "metadata": {}, "sourceType": "script"}