{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isOpera: function isOpera() {\n    return global.navigator && /opera/i.test(global.navigator.userAgent);\n  },\n  isKonqueror: function isKonqueror() {\n    return global.navigator && /konqueror/i.test(global.navigator.userAgent);\n  } // #187 wrap document.domain in try/catch because of WP8 from file:///\n  ,\n  hasDomain: function hasDomain() {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "isOpera", "global", "navigator", "test", "userAgent", "isKonqueror", "hasDomain", "document", "domain", "e"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  isOpera: function() {\n    return global.navigator &&\n      /opera/i.test(global.navigator.userAgent);\n  }\n\n, isKonqueror: function() {\n    return global.navigator &&\n      /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n, hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiB;EACfC,OAAO,EAAE,mBAAW;IAClB,OAAOC,MAAM,CAACC,SAAP,IACL,SAASC,IAAT,CAAcF,MAAM,CAACC,SAAP,CAAiBE,SAA/B,CADF;EAED,CAJc;EAMfC,WAAW,EAAE,uBAAW;IACtB,OAAOJ,MAAM,CAACC,SAAP,IACL,aAAaC,IAAb,CAAkBF,MAAM,CAACC,SAAP,CAAiBE,SAAnC,CADF;EAED,CATc,CAWf;EAXe;EAYfE,SAAS,EAAE,qBAAY;IACrB;IACA,IAAI,CAACL,MAAM,CAACM,QAAZ,EAAsB;MACpB,OAAO,IAAP;IACD;;IAED,IAAI;MACF,OAAO,CAAC,CAACN,MAAM,CAACM,QAAP,CAAgBC,MAAzB;IACD,CAFD,CAEE,OAAOC,CAAP,EAAU;MACV,OAAO,KAAP;IACD;EACF;AAvBc,CAAjB"}, "metadata": {}, "sourceType": "script"}