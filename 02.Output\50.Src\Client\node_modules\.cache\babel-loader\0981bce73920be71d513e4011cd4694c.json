{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    iframeUtils = require('../../utils/iframe'),\n    urlUtils = require('../../utils/url'),\n    EventEmitter = require('events').EventEmitter,\n    random = require('../../utils/random');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\n\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ? iframeUtils.createHtmlfile : iframeUtils.createIframe;\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function start() {\n      debug('start');\n      self.iframeObj.loaded();\n    },\n    message: function message(data) {\n      debug('message', data);\n      self.emit('message', data);\n    },\n    stop: function stop() {\n      debug('stop');\n\n      self._cleanup();\n\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function () {\n    debug('callback');\n\n    self._cleanup();\n\n    self._close('permanent');\n  });\n}\n\ninherits(HtmlfileReceiver, EventEmitter);\n\nHtmlfileReceiver.prototype.abort = function () {\n  debug('abort');\n\n  this._cleanup();\n\n  this._close('user');\n};\n\nHtmlfileReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n\n  delete global[iframeUtils.WPrefix][this.id];\n};\n\nHtmlfileReceiver.prototype._close = function (reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\n\nHtmlfileReceiver.htmlfileEnabled = false; // obfuscate to avoid firewalls\n\nvar axo = ['Active'].concat('Object').join('X');\n\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {// intentionally empty\n  }\n}\n\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\nmodule.exports = HtmlfileReceiver;", "map": {"version": 3, "names": ["inherits", "require", "iframe<PERSON><PERSON>s", "urlUtils", "EventEmitter", "random", "debug", "process", "env", "NODE_ENV", "HtmlfileReceiver", "url", "call", "self", "polluteGlobalNamespace", "id", "string", "<PERSON><PERSON><PERSON><PERSON>", "decodeURIComponent", "WPrefix", "htmlfileEnabled", "constructFunc", "createHtmlfile", "createIframe", "global", "start", "iframeObj", "loaded", "message", "data", "emit", "stop", "_cleanup", "_close", "prototype", "abort", "cleanup", "reason", "removeAllListeners", "axo", "concat", "join", "x", "enabled", "iframeEnabled", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/receiver/htmlfile.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , iframeUtils = require('../../utils/iframe')\n  , urlUtils = require('../../utils/url')\n  , EventEmitter = require('events').EventEmitter\n  , random = require('../../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\n\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ?\n      iframeUtils.createHtmlfile : iframeUtils.createIframe;\n\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function() {\n      debug('start');\n      self.iframeObj.loaded();\n    }\n  , message: function(data) {\n      debug('message', data);\n      self.emit('message', data);\n    }\n  , stop: function() {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function() {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\n\ninherits(HtmlfileReceiver, EventEmitter);\n\nHtmlfileReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nHtmlfileReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\n\nHtmlfileReceiver.prototype._close = function(reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\n\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\n\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\n\nmodule.exports = HtmlfileReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,WAAW,GAAGD,OAAO,CAAC,oBAAD,CADzB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,iBAAD,CAFtB;AAAA,IAGIG,YAAY,GAAGH,OAAO,CAAC,QAAD,CAAP,CAAkBG,YAHrC;AAAA,IAIIC,MAAM,GAAGJ,OAAO,CAAC,oBAAD,CAJpB;;AAOA,IAAIK,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAD,CAAP,CAAiB,iCAAjB,CAAR;AACD;;AAED,SAASS,gBAAT,CAA0BC,GAA1B,EAA+B;EAC7BL,KAAK,CAACK,GAAD,CAAL;EACAP,YAAY,CAACQ,IAAb,CAAkB,IAAlB;EACA,IAAIC,IAAI,GAAG,IAAX;EACAX,WAAW,CAACY,sBAAZ;EAEA,KAAKC,EAAL,GAAU,MAAMV,MAAM,CAACW,MAAP,CAAc,CAAd,CAAhB;EACAL,GAAG,GAAGR,QAAQ,CAACc,QAAT,CAAkBN,GAAlB,EAAuB,OAAOO,kBAAkB,CAAChB,WAAW,CAACiB,OAAZ,GAAsB,GAAtB,GAA4B,KAAKJ,EAAlC,CAAhD,CAAN;EAEAT,KAAK,CAAC,gBAAD,EAAmBI,gBAAgB,CAACU,eAApC,CAAL;EACA,IAAIC,aAAa,GAAGX,gBAAgB,CAACU,eAAjB,GAChBlB,WAAW,CAACoB,cADI,GACapB,WAAW,CAACqB,YAD7C;EAGAC,MAAM,CAACtB,WAAW,CAACiB,OAAb,CAAN,CAA4B,KAAKJ,EAAjC,IAAuC;IACrCU,KAAK,EAAE,iBAAW;MAChBnB,KAAK,CAAC,OAAD,CAAL;MACAO,IAAI,CAACa,SAAL,CAAeC,MAAf;IACD,CAJoC;IAKrCC,OAAO,EAAE,iBAASC,IAAT,EAAe;MACtBvB,KAAK,CAAC,SAAD,EAAYuB,IAAZ,CAAL;MACAhB,IAAI,CAACiB,IAAL,CAAU,SAAV,EAAqBD,IAArB;IACD,CARoC;IASrCE,IAAI,EAAE,gBAAW;MACfzB,KAAK,CAAC,MAAD,CAAL;;MACAO,IAAI,CAACmB,QAAL;;MACAnB,IAAI,CAACoB,MAAL,CAAY,SAAZ;IACD;EAboC,CAAvC;EAeA,KAAKP,SAAL,GAAiBL,aAAa,CAACV,GAAD,EAAM,YAAW;IAC7CL,KAAK,CAAC,UAAD,CAAL;;IACAO,IAAI,CAACmB,QAAL;;IACAnB,IAAI,CAACoB,MAAL,CAAY,WAAZ;EACD,CAJ6B,CAA9B;AAKD;;AAEDjC,QAAQ,CAACU,gBAAD,EAAmBN,YAAnB,CAAR;;AAEAM,gBAAgB,CAACwB,SAAjB,CAA2BC,KAA3B,GAAmC,YAAW;EAC5C7B,KAAK,CAAC,OAAD,CAAL;;EACA,KAAK0B,QAAL;;EACA,KAAKC,MAAL,CAAY,MAAZ;AACD,CAJD;;AAMAvB,gBAAgB,CAACwB,SAAjB,CAA2BF,QAA3B,GAAsC,YAAW;EAC/C1B,KAAK,CAAC,UAAD,CAAL;;EACA,IAAI,KAAKoB,SAAT,EAAoB;IAClB,KAAKA,SAAL,CAAeU,OAAf;IACA,KAAKV,SAAL,GAAiB,IAAjB;EACD;;EACD,OAAOF,MAAM,CAACtB,WAAW,CAACiB,OAAb,CAAN,CAA4B,KAAKJ,EAAjC,CAAP;AACD,CAPD;;AASAL,gBAAgB,CAACwB,SAAjB,CAA2BD,MAA3B,GAAoC,UAASI,MAAT,EAAiB;EACnD/B,KAAK,CAAC,QAAD,EAAW+B,MAAX,CAAL;EACA,KAAKP,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyBO,MAAzB;EACA,KAAKC,kBAAL;AACD,CAJD;;AAMA5B,gBAAgB,CAACU,eAAjB,GAAmC,KAAnC,C,CAEA;;AACA,IAAImB,GAAG,GAAG,CAAC,QAAD,EAAWC,MAAX,CAAkB,QAAlB,EAA4BC,IAA5B,CAAiC,GAAjC,CAAV;;AACA,IAAIF,GAAG,IAAIf,MAAX,EAAmB;EACjB,IAAI;IACFd,gBAAgB,CAACU,eAAjB,GAAmC,CAAC,CAAC,IAAII,MAAM,CAACe,GAAD,CAAV,CAAgB,UAAhB,CAArC;EACD,CAFD,CAEE,OAAOG,CAAP,EAAU,CACV;EACD;AACF;;AAEDhC,gBAAgB,CAACiC,OAAjB,GAA2BjC,gBAAgB,CAACU,eAAjB,IAAoClB,WAAW,CAAC0C,aAA3E;AAEAC,MAAM,CAACC,OAAP,GAAiBpC,gBAAjB"}, "metadata": {}, "sourceType": "script"}