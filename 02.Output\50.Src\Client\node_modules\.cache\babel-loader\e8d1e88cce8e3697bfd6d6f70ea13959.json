{"ast": null, "code": "'use strict';\n\nvar eventUtils = require('./event'),\n    browser = require('./browser');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\n\nmodule.exports = {\n  WPrefix: '_jp',\n  currentWindowId: null,\n  polluteGlobalNamespace: function polluteGlobalNamespace() {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  },\n  postMessage: function postMessage(type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId,\n        type: type,\n        data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  },\n  createIframe: function createIframe(iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n\n    var unattach = function unattach() {\n      debug('unattach');\n      clearTimeout(tref); // Explorer had problems with that.\n\n      try {\n        iframe.onload = null;\n      } catch (x) {// intentionally empty\n      }\n\n      iframe.onerror = null;\n    };\n\n    var cleanup = function cleanup() {\n      debug('cleanup');\n\n      if (iframe) {\n        unattach(); // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n\n        setTimeout(function () {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n\n    var onerror = function onerror(err) {\n      debug('onerror', err);\n\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n\n    var post = function post(msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function () {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {// intentionally empty\n        }\n      }, 0);\n    };\n\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n\n    iframe.onload = function () {\n      debug('onload'); // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n\n      clearTimeout(tref);\n      tref = setTimeout(function () {\n        onerror('onload timeout');\n      }, 2000);\n    };\n\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n  /* eslint no-undef: \"off\", new-cap: \"off\" */\n  ,\n  createHtmlfile: function createHtmlfile(iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n\n    var unattach = function unattach() {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n\n    var cleanup = function cleanup() {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n\n    var onerror = function onerror(r) {\n      debug('onerror', r);\n\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n\n    var post = function post(msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function () {\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {// intentionally empty\n      }\n    };\n\n    doc.open();\n    doc.write('<html><s' + 'cript>' + 'document.domain=\"' + global.document.domain + '\";' + '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n\n    iframe.onerror = function () {\n      onerror('onerror');\n    };\n\n    tref = setTimeout(function () {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post,\n      cleanup: cleanup,\n      loaded: unattach\n    };\n  }\n};\nmodule.exports.iframeEnabled = false;\n\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' || typeof global.postMessage === 'object') && !browser.isKonqueror();\n}", "map": {"version": 3, "names": ["eventUtils", "require", "browser", "debug", "process", "env", "NODE_ENV", "module", "exports", "WPrefix", "currentWindowId", "polluteGlobalNamespace", "global", "postMessage", "type", "data", "parent", "JSON", "stringify", "windowId", "createIframe", "iframeUrl", "<PERSON><PERSON><PERSON><PERSON>", "iframe", "document", "createElement", "tref", "unloadRef", "unattach", "clearTimeout", "onload", "x", "onerror", "cleanup", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "unloadDel", "err", "post", "msg", "origin", "contentWindow", "src", "style", "display", "position", "body", "append<PERSON><PERSON><PERSON>", "unloadAdd", "loaded", "createHtmlfile", "axo", "concat", "join", "doc", "CollectGarbage", "r", "open", "write", "domain", "close", "parentWindow", "c", "iframeEnabled", "isKonqueror"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/utils/iframe.js"], "sourcesContent": ["'use strict';\n\nvar eventUtils = require('./event')\n  , browser = require('./browser')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\n\nmodule.exports = {\n  WPrefix: '_jp'\n, currentWindowId: null\n\n, polluteGlobalNamespace: function() {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  }\n\n, postMessage: function(type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId\n      , type: type\n      , data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  }\n\n, createIframe: function(iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function() {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function() {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function(err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function(msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function() {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    iframe.onload = function() {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function() {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n\n/* eslint no-undef: \"off\", new-cap: \"off\" */\n, createHtmlfile: function(iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function() {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function(r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function(msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function() {\n          if (iframe && iframe.contentWindow) {\n              iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n\n    doc.open();\n    doc.write('<html><s' + 'cript>' +\n              'document.domain=\"' + global.document.domain + '\";' +\n              '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n};\n\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' ||\n    typeof global.postMessage === 'object') && (!browser.isKonqueror());\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,UAAU,GAAGC,OAAO,CAAC,SAAD,CAAxB;AAAA,IACIC,OAAO,GAAGD,OAAO,CAAC,WAAD,CADrB;;AAIA,IAAIE,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAD,CAAP,CAAiB,4BAAjB,CAAR;AACD;;AAEDM,MAAM,CAACC,OAAP,GAAiB;EACfC,OAAO,EAAE,KADM;EAEfC,eAAe,EAAE,IAFF;EAIfC,sBAAsB,EAAE,kCAAW;IACjC,IAAI,EAAEJ,MAAM,CAACC,OAAP,CAAeC,OAAf,IAA0BG,MAA5B,CAAJ,EAAyC;MACvCA,MAAM,CAACL,MAAM,CAACC,OAAP,CAAeC,OAAhB,CAAN,GAAiC,EAAjC;IACD;EACF,CARc;EAUfI,WAAW,EAAE,qBAASC,IAAT,EAAeC,IAAf,EAAqB;IAChC,IAAIH,MAAM,CAACI,MAAP,KAAkBJ,MAAtB,EAA8B;MAC5BA,MAAM,CAACI,MAAP,CAAcH,WAAd,CAA0BI,IAAI,CAACC,SAAL,CAAe;QACvCC,QAAQ,EAAEZ,MAAM,CAACC,OAAP,CAAeE,eADc;QAEvCI,IAAI,EAAEA,IAFiC;QAGvCC,IAAI,EAAEA,IAAI,IAAI;MAHyB,CAAf,CAA1B,EAII,GAJJ;IAKD,CAND,MAMO;MACLZ,KAAK,CAAC,uCAAD,EAA0CW,IAA1C,EAAgDC,IAAhD,CAAL;IACD;EACF,CApBc;EAsBfK,YAAY,EAAE,sBAASC,SAAT,EAAoBC,aAApB,EAAmC;IAC/C,IAAIC,MAAM,GAAGX,MAAM,CAACY,QAAP,CAAgBC,aAAhB,CAA8B,QAA9B,CAAb;IACA,IAAIC,IAAJ,EAAUC,SAAV;;IACA,IAAIC,QAAQ,GAAG,SAAXA,QAAW,GAAW;MACxBzB,KAAK,CAAC,UAAD,CAAL;MACA0B,YAAY,CAACH,IAAD,CAAZ,CAFwB,CAGxB;;MACA,IAAI;QACFH,MAAM,CAACO,MAAP,GAAgB,IAAhB;MACD,CAFD,CAEE,OAAOC,CAAP,EAAU,CACV;MACD;;MACDR,MAAM,CAACS,OAAP,GAAiB,IAAjB;IACD,CAVD;;IAWA,IAAIC,OAAO,GAAG,SAAVA,OAAU,GAAW;MACvB9B,KAAK,CAAC,SAAD,CAAL;;MACA,IAAIoB,MAAJ,EAAY;QACVK,QAAQ,GADE,CAEV;QACA;QACA;;QACAM,UAAU,CAAC,YAAW;UACpB,IAAIX,MAAJ,EAAY;YACVA,MAAM,CAACY,UAAP,CAAkBC,WAAlB,CAA8Bb,MAA9B;UACD;;UACDA,MAAM,GAAG,IAAT;QACD,CALS,EAKP,CALO,CAAV;QAMAvB,UAAU,CAACqC,SAAX,CAAqBV,SAArB;MACD;IACF,CAfD;;IAgBA,IAAIK,OAAO,GAAG,SAAVA,OAAU,CAASM,GAAT,EAAc;MAC1BnC,KAAK,CAAC,SAAD,EAAYmC,GAAZ,CAAL;;MACA,IAAIf,MAAJ,EAAY;QACVU,OAAO;QACPX,aAAa,CAACgB,GAAD,CAAb;MACD;IACF,CAND;;IAOA,IAAIC,IAAI,GAAG,SAAPA,IAAO,CAASC,GAAT,EAAcC,MAAd,EAAsB;MAC/BtC,KAAK,CAAC,MAAD,EAASqC,GAAT,EAAcC,MAAd,CAAL;MACAP,UAAU,CAAC,YAAW;QACpB,IAAI;UACF;UACA;UACA,IAAIX,MAAM,IAAIA,MAAM,CAACmB,aAArB,EAAoC;YAClCnB,MAAM,CAACmB,aAAP,CAAqB7B,WAArB,CAAiC2B,GAAjC,EAAsCC,MAAtC;UACD;QACF,CAND,CAME,OAAOV,CAAP,EAAU,CACV;QACD;MACF,CAVS,EAUP,CAVO,CAAV;IAWD,CAbD;;IAeAR,MAAM,CAACoB,GAAP,GAAatB,SAAb;IACAE,MAAM,CAACqB,KAAP,CAAaC,OAAb,GAAuB,MAAvB;IACAtB,MAAM,CAACqB,KAAP,CAAaE,QAAb,GAAwB,UAAxB;;IACAvB,MAAM,CAACS,OAAP,GAAiB,YAAW;MAC1BA,OAAO,CAAC,SAAD,CAAP;IACD,CAFD;;IAGAT,MAAM,CAACO,MAAP,GAAgB,YAAW;MACzB3B,KAAK,CAAC,QAAD,CAAL,CADyB,CAEzB;MACA;;MACA0B,YAAY,CAACH,IAAD,CAAZ;MACAA,IAAI,GAAGQ,UAAU,CAAC,YAAW;QAC3BF,OAAO,CAAC,gBAAD,CAAP;MACD,CAFgB,EAEd,IAFc,CAAjB;IAGD,CARD;;IASApB,MAAM,CAACY,QAAP,CAAgBuB,IAAhB,CAAqBC,WAArB,CAAiCzB,MAAjC;IACAG,IAAI,GAAGQ,UAAU,CAAC,YAAW;MAC3BF,OAAO,CAAC,SAAD,CAAP;IACD,CAFgB,EAEd,KAFc,CAAjB;IAGAL,SAAS,GAAG3B,UAAU,CAACiD,SAAX,CAAqBhB,OAArB,CAAZ;IACA,OAAO;MACLM,IAAI,EAAEA,IADD;MAELN,OAAO,EAAEA,OAFJ;MAGLiB,MAAM,EAAEtB;IAHH,CAAP;EAKD;EAEH;EArGiB;EAsGfuB,cAAc,EAAE,wBAAS9B,SAAT,EAAoBC,aAApB,EAAmC;IACjD,IAAI8B,GAAG,GAAG,CAAC,QAAD,EAAWC,MAAX,CAAkB,QAAlB,EAA4BC,IAA5B,CAAiC,GAAjC,CAAV;IACA,IAAIC,GAAG,GAAG,IAAI3C,MAAM,CAACwC,GAAD,CAAV,CAAgB,UAAhB,CAAV;IACA,IAAI1B,IAAJ,EAAUC,SAAV;IACA,IAAIJ,MAAJ;;IACA,IAAIK,QAAQ,GAAG,SAAXA,QAAW,GAAW;MACxBC,YAAY,CAACH,IAAD,CAAZ;MACAH,MAAM,CAACS,OAAP,GAAiB,IAAjB;IACD,CAHD;;IAIA,IAAIC,OAAO,GAAG,SAAVA,OAAU,GAAW;MACvB,IAAIsB,GAAJ,EAAS;QACP3B,QAAQ;QACR5B,UAAU,CAACqC,SAAX,CAAqBV,SAArB;QACAJ,MAAM,CAACY,UAAP,CAAkBC,WAAlB,CAA8Bb,MAA9B;QACAA,MAAM,GAAGgC,GAAG,GAAG,IAAf;QACAC,cAAc;MACf;IACF,CARD;;IASA,IAAIxB,OAAO,GAAG,SAAVA,OAAU,CAASyB,CAAT,EAAY;MACxBtD,KAAK,CAAC,SAAD,EAAYsD,CAAZ,CAAL;;MACA,IAAIF,GAAJ,EAAS;QACPtB,OAAO;QACPX,aAAa,CAACmC,CAAD,CAAb;MACD;IACF,CAND;;IAOA,IAAIlB,IAAI,GAAG,SAAPA,IAAO,CAASC,GAAT,EAAcC,MAAd,EAAsB;MAC/B,IAAI;QACF;QACA;QACAP,UAAU,CAAC,YAAW;UACpB,IAAIX,MAAM,IAAIA,MAAM,CAACmB,aAArB,EAAoC;YAChCnB,MAAM,CAACmB,aAAP,CAAqB7B,WAArB,CAAiC2B,GAAjC,EAAsCC,MAAtC;UACH;QACF,CAJS,EAIP,CAJO,CAAV;MAKD,CARD,CAQE,OAAOV,CAAP,EAAU,CACV;MACD;IACF,CAZD;;IAcAwB,GAAG,CAACG,IAAJ;IACAH,GAAG,CAACI,KAAJ,CAAU,aAAa,QAAb,GACA,mBADA,GACsB/C,MAAM,CAACY,QAAP,CAAgBoC,MADtC,GAC+C,IAD/C,GAEA,KAFA,GAEQ,eAFlB;IAGAL,GAAG,CAACM,KAAJ;IACAN,GAAG,CAACO,YAAJ,CAAiBvD,MAAM,CAACC,OAAP,CAAeC,OAAhC,IAA2CG,MAAM,CAACL,MAAM,CAACC,OAAP,CAAeC,OAAhB,CAAjD;IACA,IAAIsD,CAAC,GAAGR,GAAG,CAAC9B,aAAJ,CAAkB,KAAlB,CAAR;IACA8B,GAAG,CAACR,IAAJ,CAASC,WAAT,CAAqBe,CAArB;IACAxC,MAAM,GAAGgC,GAAG,CAAC9B,aAAJ,CAAkB,QAAlB,CAAT;IACAsC,CAAC,CAACf,WAAF,CAAczB,MAAd;IACAA,MAAM,CAACoB,GAAP,GAAatB,SAAb;;IACAE,MAAM,CAACS,OAAP,GAAiB,YAAW;MAC1BA,OAAO,CAAC,SAAD,CAAP;IACD,CAFD;;IAGAN,IAAI,GAAGQ,UAAU,CAAC,YAAW;MAC3BF,OAAO,CAAC,SAAD,CAAP;IACD,CAFgB,EAEd,KAFc,CAAjB;IAGAL,SAAS,GAAG3B,UAAU,CAACiD,SAAX,CAAqBhB,OAArB,CAAZ;IACA,OAAO;MACLM,IAAI,EAAEA,IADD;MAELN,OAAO,EAAEA,OAFJ;MAGLiB,MAAM,EAAEtB;IAHH,CAAP;EAKD;AApKc,CAAjB;AAuKArB,MAAM,CAACC,OAAP,CAAewD,aAAf,GAA+B,KAA/B;;AACA,IAAIpD,MAAM,CAACY,QAAX,EAAqB;EACnB;EACA;EACAjB,MAAM,CAACC,OAAP,CAAewD,aAAf,GAA+B,CAAC,OAAOpD,MAAM,CAACC,WAAd,KAA8B,UAA9B,IAC9B,OAAOD,MAAM,CAACC,WAAd,KAA8B,QADD,KACe,CAACX,OAAO,CAAC+D,WAAR,EAD/C;AAED"}, "metadata": {}, "sourceType": "script"}