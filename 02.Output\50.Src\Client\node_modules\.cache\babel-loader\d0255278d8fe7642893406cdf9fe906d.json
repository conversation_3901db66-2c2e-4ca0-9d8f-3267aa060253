{"ast": null, "code": "var Hash = require('./_Hash'),\n    ListCache = require('./_ListCache'),\n    Map = require('./_Map');\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\n\n\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash(),\n    'map': new (Map || ListCache)(),\n    'string': new Hash()\n  };\n}\n\nmodule.exports = mapCacheClear;", "map": {"version": 3, "names": ["Hash", "require", "ListCache", "Map", "mapCacheClear", "size", "__data__", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapCacheClear.js"], "sourcesContent": ["var Hash = require('./_Hash'),\n    ListCache = require('./_ListCache'),\n    Map = require('./_Map');\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\nmodule.exports = mapCacheClear;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAD,CAAlB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,cAAD,CADvB;AAAA,IAEIE,GAAG,GAAGF,OAAO,CAAC,QAAD,CAFjB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,aAAT,GAAyB;EACvB,KAAKC,IAAL,GAAY,CAAZ;EACA,KAAKC,QAAL,GAAgB;IACd,QAAQ,IAAIN,IAAJ,EADM;IAEd,OAAO,KAAKG,GAAG,IAAID,SAAZ,GAFO;IAGd,UAAU,IAAIF,IAAJ;EAHI,CAAhB;AAKD;;AAEDO,MAAM,CAACC,OAAP,GAAiBJ,aAAjB"}, "metadata": {}, "sourceType": "script"}