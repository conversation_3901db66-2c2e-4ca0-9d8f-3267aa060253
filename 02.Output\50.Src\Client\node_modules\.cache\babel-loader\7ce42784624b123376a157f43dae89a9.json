{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\n\nfunction useThrottle(value, options) {\n  var _a = __read(useState(value), 2),\n      throttled = _a[0],\n      setThrottled = _a[1];\n\n  var run = useThrottleFn(function () {\n    setThrottled(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return throttled;\n}\n\nexport default useThrottle;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useState", "useThrottleFn", "useThrottle", "options", "_a", "throttled", "setThrottled", "run"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useThrottle/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useState } from 'react';\nimport useThrottleFn from '../useThrottleFn';\nfunction useThrottle(value, options) {\n  var _a = __read(useState(value), 2),\n    throttled = _a[0],\n    setThrottled = _a[1];\n  var run = useThrottleFn(function () {\n    setThrottled(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return throttled;\n}\nexport default useThrottle;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,QAApB,QAAoC,OAApC;AACA,OAAOC,aAAP,MAA0B,kBAA1B;;AACA,SAASC,WAAT,CAAqBL,KAArB,EAA4BM,OAA5B,EAAqC;EACnC,IAAIC,EAAE,GAAGrB,MAAM,CAACiB,QAAQ,CAACH,KAAD,CAAT,EAAkB,CAAlB,CAAf;EAAA,IACEQ,SAAS,GAAGD,EAAE,CAAC,CAAD,CADhB;EAAA,IAEEE,YAAY,GAAGF,EAAE,CAAC,CAAD,CAFnB;;EAGA,IAAIG,GAAG,GAAGN,aAAa,CAAC,YAAY;IAClCK,YAAY,CAACT,KAAD,CAAZ;EACD,CAFsB,EAEpBM,OAFoB,CAAb,CAEEI,GAFZ;EAGAR,SAAS,CAAC,YAAY;IACpBQ,GAAG;EACJ,CAFQ,EAEN,CAACV,KAAD,CAFM,CAAT;EAGA,OAAOQ,SAAP;AACD;;AACD,eAAeH,WAAf"}, "metadata": {}, "sourceType": "module"}