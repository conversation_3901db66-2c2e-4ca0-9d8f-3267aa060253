{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n/** Used for built-in method references. */\n\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\n\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n\nmodule.exports = hashHas;", "map": {"version": 3, "names": ["nativeCreate", "require", "objectProto", "Object", "prototype", "hasOwnProperty", "hashHas", "key", "data", "__data__", "undefined", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_hashHas.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\nmodule.exports = hashHas;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;;;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBC,GAAjB,EAAsB;EACpB,IAAIC,IAAI,GAAG,KAAKC,QAAhB;EACA,OAAOT,YAAY,GAAIQ,IAAI,CAACD,GAAD,CAAJ,KAAcG,SAAlB,GAA+BL,cAAc,CAACM,IAAf,CAAoBH,IAApB,EAA0BD,GAA1B,CAAlD;AACD;;AAEDK,MAAM,CAACC,OAAP,GAAiBP,OAAjB"}, "metadata": {}, "sourceType": "script"}