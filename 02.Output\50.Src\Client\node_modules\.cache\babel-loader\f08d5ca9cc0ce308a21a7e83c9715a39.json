{"ast": null, "code": "import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "useEffectWithTarget", "useLayoutEffectWithTarget", "useIsomorphicLayoutEffectWithTarget"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js"], "sourcesContent": ["import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,aAAtB;AACA,OAAOC,mBAAP,MAAgC,uBAAhC;AACA,OAAOC,yBAAP,MAAsC,6BAAtC;AACA,IAAIC,mCAAmC,GAAGH,SAAS,GAAGE,yBAAH,GAA+BD,mBAAlF;AACA,eAAeE,mCAAf"}, "metadata": {}, "sourceType": "module"}