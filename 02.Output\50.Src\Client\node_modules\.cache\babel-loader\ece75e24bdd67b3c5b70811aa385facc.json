{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useMemo, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nexport default function useSelections(items, defaultSelected) {\n  if (defaultSelected === void 0) {\n    defaultSelected = [];\n  }\n\n  var _a = __read(useState(defaultSelected), 2),\n      selected = _a[0],\n      setSelected = _a[1];\n\n  var selectedSet = useMemo(function () {\n    return new Set(selected);\n  }, [selected]);\n\n  var isSelected = function isSelected(item) {\n    return selectedSet.has(item);\n  };\n\n  var select = function select(item) {\n    selectedSet.add(item);\n    return setSelected(Array.from(selectedSet));\n  };\n\n  var unSelect = function unSelect(item) {\n    selectedSet[\"delete\"](item);\n    return setSelected(Array.from(selectedSet));\n  };\n\n  var toggle = function toggle(item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n\n  var selectAll = function selectAll() {\n    items.forEach(function (o) {\n      selectedSet.add(o);\n    });\n    setSelected(Array.from(selectedSet));\n  };\n\n  var unSelectAll = function unSelectAll() {\n    items.forEach(function (o) {\n      selectedSet[\"delete\"](o);\n    });\n    setSelected(Array.from(selectedSet));\n  };\n\n  var noneSelected = useMemo(function () {\n    return items.every(function (o) {\n      return !selectedSet.has(o);\n    });\n  }, [items, selectedSet]);\n  var allSelected = useMemo(function () {\n    return items.every(function (o) {\n      return selectedSet.has(o);\n    }) && !noneSelected;\n  }, [items, selectedSet, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n\n  var toggleAll = function toggleAll() {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useMemo", "useState", "useMemoizedFn", "useSelections", "items", "defaultSelected", "_a", "selected", "setSelected", "selectedSet", "Set", "isSelected", "item", "has", "select", "add", "Array", "from", "unSelect", "toggle", "selectAll", "for<PERSON>ach", "unSelectAll", "noneSelected", "every", "allSelected", "partiallySelected", "toggleAll"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useSelections/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useMemo, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nexport default function useSelections(items, defaultSelected) {\n  if (defaultSelected === void 0) {\n    defaultSelected = [];\n  }\n  var _a = __read(useState(defaultSelected), 2),\n    selected = _a[0],\n    setSelected = _a[1];\n  var selectedSet = useMemo(function () {\n    return new Set(selected);\n  }, [selected]);\n  var isSelected = function isSelected(item) {\n    return selectedSet.has(item);\n  };\n  var select = function select(item) {\n    selectedSet.add(item);\n    return setSelected(Array.from(selectedSet));\n  };\n  var unSelect = function unSelect(item) {\n    selectedSet[\"delete\"](item);\n    return setSelected(Array.from(selectedSet));\n  };\n  var toggle = function toggle(item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n  var selectAll = function selectAll() {\n    items.forEach(function (o) {\n      selectedSet.add(o);\n    });\n    setSelected(Array.from(selectedSet));\n  };\n  var unSelectAll = function unSelectAll() {\n    items.forEach(function (o) {\n      selectedSet[\"delete\"](o);\n    });\n    setSelected(Array.from(selectedSet));\n  };\n  var noneSelected = useMemo(function () {\n    return items.every(function (o) {\n      return !selectedSet.has(o);\n    });\n  }, [items, selectedSet]);\n  var allSelected = useMemo(function () {\n    return items.every(function (o) {\n      return selectedSet.has(o);\n    }) && !noneSelected;\n  }, [items, selectedSet, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n  var toggleAll = function toggleAll() {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,OAAT,EAAkBC,QAAlB,QAAkC,OAAlC;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,eAAe,SAASC,aAAT,CAAuBC,KAAvB,EAA8BC,eAA9B,EAA+C;EAC5D,IAAIA,eAAe,KAAK,KAAK,CAA7B,EAAgC;IAC9BA,eAAe,GAAG,EAAlB;EACD;;EACD,IAAIC,EAAE,GAAGtB,MAAM,CAACiB,QAAQ,CAACI,eAAD,CAAT,EAA4B,CAA5B,CAAf;EAAA,IACEE,QAAQ,GAAGD,EAAE,CAAC,CAAD,CADf;EAAA,IAEEE,WAAW,GAAGF,EAAE,CAAC,CAAD,CAFlB;;EAGA,IAAIG,WAAW,GAAGT,OAAO,CAAC,YAAY;IACpC,OAAO,IAAIU,GAAJ,CAAQH,QAAR,CAAP;EACD,CAFwB,EAEtB,CAACA,QAAD,CAFsB,CAAzB;;EAGA,IAAII,UAAU,GAAG,SAASA,UAAT,CAAoBC,IAApB,EAA0B;IACzC,OAAOH,WAAW,CAACI,GAAZ,CAAgBD,IAAhB,CAAP;EACD,CAFD;;EAGA,IAAIE,MAAM,GAAG,SAASA,MAAT,CAAgBF,IAAhB,EAAsB;IACjCH,WAAW,CAACM,GAAZ,CAAgBH,IAAhB;IACA,OAAOJ,WAAW,CAACQ,KAAK,CAACC,IAAN,CAAWR,WAAX,CAAD,CAAlB;EACD,CAHD;;EAIA,IAAIS,QAAQ,GAAG,SAASA,QAAT,CAAkBN,IAAlB,EAAwB;IACrCH,WAAW,CAAC,QAAD,CAAX,CAAsBG,IAAtB;IACA,OAAOJ,WAAW,CAACQ,KAAK,CAACC,IAAN,CAAWR,WAAX,CAAD,CAAlB;EACD,CAHD;;EAIA,IAAIU,MAAM,GAAG,SAASA,MAAT,CAAgBP,IAAhB,EAAsB;IACjC,IAAID,UAAU,CAACC,IAAD,CAAd,EAAsB;MACpBM,QAAQ,CAACN,IAAD,CAAR;IACD,CAFD,MAEO;MACLE,MAAM,CAACF,IAAD,CAAN;IACD;EACF,CAND;;EAOA,IAAIQ,SAAS,GAAG,SAASA,SAAT,GAAqB;IACnChB,KAAK,CAACiB,OAAN,CAAc,UAAUpC,CAAV,EAAa;MACzBwB,WAAW,CAACM,GAAZ,CAAgB9B,CAAhB;IACD,CAFD;IAGAuB,WAAW,CAACQ,KAAK,CAACC,IAAN,CAAWR,WAAX,CAAD,CAAX;EACD,CALD;;EAMA,IAAIa,WAAW,GAAG,SAASA,WAAT,GAAuB;IACvClB,KAAK,CAACiB,OAAN,CAAc,UAAUpC,CAAV,EAAa;MACzBwB,WAAW,CAAC,QAAD,CAAX,CAAsBxB,CAAtB;IACD,CAFD;IAGAuB,WAAW,CAACQ,KAAK,CAACC,IAAN,CAAWR,WAAX,CAAD,CAAX;EACD,CALD;;EAMA,IAAIc,YAAY,GAAGvB,OAAO,CAAC,YAAY;IACrC,OAAOI,KAAK,CAACoB,KAAN,CAAY,UAAUvC,CAAV,EAAa;MAC9B,OAAO,CAACwB,WAAW,CAACI,GAAZ,CAAgB5B,CAAhB,CAAR;IACD,CAFM,CAAP;EAGD,CAJyB,EAIvB,CAACmB,KAAD,EAAQK,WAAR,CAJuB,CAA1B;EAKA,IAAIgB,WAAW,GAAGzB,OAAO,CAAC,YAAY;IACpC,OAAOI,KAAK,CAACoB,KAAN,CAAY,UAAUvC,CAAV,EAAa;MAC9B,OAAOwB,WAAW,CAACI,GAAZ,CAAgB5B,CAAhB,CAAP;IACD,CAFM,KAED,CAACsC,YAFP;EAGD,CAJwB,EAItB,CAACnB,KAAD,EAAQK,WAAR,EAAqBc,YAArB,CAJsB,CAAzB;EAKA,IAAIG,iBAAiB,GAAG1B,OAAO,CAAC,YAAY;IAC1C,OAAO,CAACuB,YAAD,IAAiB,CAACE,WAAzB;EACD,CAF8B,EAE5B,CAACF,YAAD,EAAeE,WAAf,CAF4B,CAA/B;;EAGA,IAAIE,SAAS,GAAG,SAASA,SAAT,GAAqB;IACnC,OAAOF,WAAW,GAAGH,WAAW,EAAd,GAAmBF,SAAS,EAA9C;EACD,CAFD;;EAGA,OAAO;IACLb,QAAQ,EAAEA,QADL;IAELgB,YAAY,EAAEA,YAFT;IAGLE,WAAW,EAAEA,WAHR;IAILC,iBAAiB,EAAEA,iBAJd;IAKLlB,WAAW,EAAEA,WALR;IAMLG,UAAU,EAAEA,UANP;IAOLG,MAAM,EAAEZ,aAAa,CAACY,MAAD,CAPhB;IAQLI,QAAQ,EAAEhB,aAAa,CAACgB,QAAD,CARlB;IASLC,MAAM,EAAEjB,aAAa,CAACiB,MAAD,CAThB;IAULC,SAAS,EAAElB,aAAa,CAACkB,SAAD,CAVnB;IAWLE,WAAW,EAAEpB,aAAa,CAACoB,WAAD,CAXrB;IAYLK,SAAS,EAAEzB,aAAa,CAACyB,SAAD;EAZnB,CAAP;AAcD"}, "metadata": {}, "sourceType": "module"}