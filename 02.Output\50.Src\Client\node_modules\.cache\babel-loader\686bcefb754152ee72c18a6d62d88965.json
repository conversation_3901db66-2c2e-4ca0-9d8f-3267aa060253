{"ast": null, "code": "import { useEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useEffect);\nexport default useEffectWithTarget;", "map": {"version": 3, "names": ["useEffect", "createEffectWithTarget", "useEffectWithTarget"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/utils/useEffectWithTarget.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport createEffectWithTarget from './createEffectWithTarget';\nvar useEffectWithTarget = createEffectWithTarget(useEffect);\nexport default useEffectWithTarget;"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,OAAOC,sBAAP,MAAmC,0BAAnC;AACA,IAAIC,mBAAmB,GAAGD,sBAAsB,CAACD,SAAD,CAAhD;AACA,eAAeE,mBAAf"}, "metadata": {}, "sourceType": "module"}