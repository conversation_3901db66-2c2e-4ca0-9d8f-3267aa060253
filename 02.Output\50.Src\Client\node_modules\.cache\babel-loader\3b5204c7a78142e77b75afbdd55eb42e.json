{"ast": null, "code": "import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nexport default function useCreation(factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n\n  return current.obj;\n}", "map": {"version": 3, "names": ["useRef", "depsAreSame", "useCreation", "factory", "deps", "current", "obj", "undefined", "initialized"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useCreation/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nexport default function useCreation(factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n  return current.obj;\n}"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;AACA,OAAOC,WAAP,MAAwB,sBAAxB;AACA,eAAe,SAASC,WAAT,CAAqBC,OAArB,EAA8BC,IAA9B,EAAoC;EACjD,IAAIC,OAAO,GAAGL,MAAM,CAAC;IACnBI,IAAI,EAAEA,IADa;IAEnBE,GAAG,EAAEC,SAFc;IAGnBC,WAAW,EAAE;EAHM,CAAD,CAAN,CAIXH,OAJH;;EAKA,IAAIA,OAAO,CAACG,WAAR,KAAwB,KAAxB,IAAiC,CAACP,WAAW,CAACI,OAAO,CAACD,IAAT,EAAeA,IAAf,CAAjD,EAAuE;IACrEC,OAAO,CAACD,IAAR,GAAeA,IAAf;IACAC,OAAO,CAACC,GAAR,GAAcH,OAAO,EAArB;IACAE,OAAO,CAACG,WAAR,GAAsB,IAAtB;EACD;;EACD,OAAOH,OAAO,CAACC,GAAf;AACD"}, "metadata": {}, "sourceType": "module"}