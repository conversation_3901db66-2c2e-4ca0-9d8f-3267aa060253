{"ast": null, "code": "import _slicedToArray from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";import React,{useState,useEffect}from'react';import SplitScreen from'../components/SplitScreen';import Sound from'../sounds/Sound';import{useSubscription,useStompClient}from'react-stomp-hooks';import{useSearchParams}from'react-router-dom';import{sendResultMsg,getWsEndpoint}from'../utils/Util.js';import Message from'../components/elements/Message';import{useUpdateEffect}from'ahooks';/**\r\n * 単面、或いは4分割の箱を作成する<br>\r\n * WebSocketを経由で、サーバーから、1面または1/4面でコンテンツ表示の制御を受けて、<br>\r\n * ①ブラウザの画面構成を調整する<br>\r\n * ②HtmlのRoot Fontサイズを設定。1面：16PX、1/4面：8px<br>\r\n * @module Home\r\n *\r\n * @return {*} Homeページ\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";function Home(){var _useState=useState({rowNum:1,colNum:1}),_useState2=_slicedToArray(_useState,2),rowColInfo=_useState2[0],setRowColInfo=_useState2[1];// http://operationcenter/contentapp?display_no=1&display_split_no=0\nvar _useSearchParams=useSearchParams(),_useSearchParams2=_slicedToArray(_useSearchParams,2),search=_useSearchParams2[0],setSearch=_useSearchParams2[1];var splitNo=search.get('display_split_no');var displayNo=search.get('display_no');useEffect(function(){document.title='表示盤番号：'+displayNo+', 面番号：'+splitNo;},[displayNo,splitNo]);var receiveControl=function receiveControl(message){console.info('receiveControl: '+message.body);var command=JSON.parse(message.body);if(command){if(command.rowNum===1&&command.colNum===1){// HtmlのFontSizeを16pxにする\nsetRootFontSize(16);}else{// HtmlのFontSizeを8pxにする\nsetRootFontSize(8);}setRowColInfo(command);}};var screenInfoPara={displayNo:displayNo,splitNo:splitNo};var wsEndpoint=getWsEndpoint(displayNo,splitNo);useSubscription(wsEndpoint+'/setControl',receiveControl,screenInfoPara);// Url:  /monitor/0_1/setControl\nvar stompClient=useStompClient();useEffect(function(){if(rowColInfo!==null&&rowColInfo!==void 0&&rowColInfo.id){sendResultMsg(stompClient,rowColInfo.id,0);}},[rowColInfo===null||rowColInfo===void 0?void 0:rowColInfo.id,stompClient]);if(splitNo==null||displayNo==null){return/*#__PURE__*/_jsx(Message,{msg:'表示盤番号、及び面番号を設定してください。例：http://operationcenter/contentapp/?display_no=0&display_split_no=0'});}splitNo=Number(splitNo);displayNo=Number(displayNo);// モニターの自動画角調整機能対応\n// モニターが描画範囲を検知して自動で画角調整を行うため、描画範囲を示すための目印を四隅に追加する。\nvar cornerCommonStyle=\"absolute h-[1px] w-[3px] bg-white\";//四隅の横並び3pxを白色(#FFFFFF)に塗りつぶす\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"static\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(cornerCommonStyle,\" left-0 top-0\")}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(cornerCommonStyle,\" top-0 right-0\")}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(cornerCommonStyle,\" bottom-0 left-0\")}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(cornerCommonStyle,\" bottom-0 right-0\")}),rowColInfo.rowNum===2&&rowColInfo.colNum===2&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-black grid min-h-screen grid-rows-2 grid-cols-2 place-content-stretch select-none overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col text-white w-full h-full border-r-[1px] border-b-[1px] border-white p-[1px]\",children:/*#__PURE__*/_jsx(SplitScreen,{displayNo:displayNo,splitNo:splitNo,detailSplitNo:0})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col text-white w-full h-full border-l-[1px] border-b-[1px] border-white p-[1px]\",children:/*#__PURE__*/_jsx(SplitScreen,{displayNo:displayNo,splitNo:splitNo,detailSplitNo:1})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col text-white w-full h-full border-r-[1px] border-t-[1px] border-white p-[1px]\",children:/*#__PURE__*/_jsx(SplitScreen,{displayNo:displayNo,splitNo:splitNo,detailSplitNo:2})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col text-white w-full h-full border-l-[1px] border-t-[1px] border-white p-[1px]\",children:/*#__PURE__*/_jsx(SplitScreen,{displayNo:displayNo,splitNo:splitNo,detailSplitNo:3})})]}),rowColInfo.rowNum===1&&rowColInfo.colNum===1&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-stretch select-none overflow-hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col text-white w-full h-full p-[1px]\",children:/*#__PURE__*/_jsx(SplitScreen,{displayNo:displayNo,splitNo:splitNo,detailSplitNo:0})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-black\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-black text-white\",children:/*#__PURE__*/_jsx(Sound,{displayNo:displayNo,splitNo:splitNo})})})]});}/**\r\n * HtmlRootのFontSize変更\r\n *\r\n * @param {Number} size px単位のFontSizeの数字\r\n */function setRootFontSize(size){var fontSize=size||16;document.getElementsByTagName('html')[0].style['font-size']=fontSize+'px';}export default Home;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "SplitScreen", "Sound", "useSubscription", "useStompClient", "useSearchParams", "sendResultMsg", "getWsEndpoint", "Message", "useUpdateEffect", "Home", "row<PERSON>um", "colNum", "rowColInfo", "setRowColInfo", "search", "setSearch", "splitNo", "get", "displayNo", "document", "title", "receiveControl", "message", "console", "info", "body", "command", "JSON", "parse", "setRootFontSize", "screenInfoPara", "wsEndpoint", "stompClient", "id", "Number", "cornerCommonStyle", "size", "fontSize", "getElementsByTagName", "style"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport SplitScreen from '../components/SplitScreen';\r\nimport Sound from '../sounds/Sound';\r\nimport { useSubscription, useStompClient } from 'react-stomp-hooks';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { sendResultMsg, getWsEndpoint } from '../utils/Util.js';\r\nimport Message from '../components/elements/Message';\r\nimport { useUpdateEffect } from 'ahooks';\r\n\r\n/**\r\n * 単面、或いは4分割の箱を作成する<br>\r\n * WebSocketを経由で、サーバーから、1面または1/4面でコンテンツ表示の制御を受けて、<br>\r\n * ①ブラウザの画面構成を調整する<br>\r\n * ②HtmlのRoot Fontサイズを設定。1面：16PX、1/4面：8px<br>\r\n * @module Home\r\n *\r\n * @return {*} Homeページ\r\n */\r\nfunction Home() {\r\n  const [rowColInfo, setRowColInfo] = useState({ rowNum: 1, colNum: 1 });\r\n\r\n  // http://operationcenter/contentapp?display_no=1&display_split_no=0\r\n  const [search, setSearch] = useSearchParams();\r\n\r\n  let splitNo = search.get('display_split_no');\r\n  let displayNo = search.get('display_no');\r\n\r\n  useEffect(() => {\r\n    document.title = '表示盤番号：' + displayNo + ', 面番号：' + splitNo;\r\n  }, [displayNo, splitNo]);\r\n\r\n  const receiveControl = (message) => {\r\n    console.info('receiveControl: ' + message.body);\r\n    let command = JSON.parse(message.body);\r\n    if (command) {\r\n      if (command.rowNum === 1 && command.colNum === 1) {\r\n        // HtmlのFontSizeを16pxにする\r\n        setRootFontSize(16);\r\n      } else {\r\n        // HtmlのFontSizeを8pxにする\r\n        setRootFontSize(8);\r\n      }\r\n      setRowColInfo(command);\r\n    }\r\n  };\r\n\r\n  const screenInfoPara = {\r\n    displayNo: displayNo,\r\n    splitNo: splitNo,\r\n  };\r\n  const wsEndpoint = getWsEndpoint(displayNo, splitNo);\r\n\r\n  useSubscription(wsEndpoint + '/setControl', receiveControl, screenInfoPara); // Url:  /monitor/0_1/setControl\r\n\r\n  const stompClient = useStompClient();\r\n\r\n  useEffect(() => {\r\n    if (rowColInfo?.id) {\r\n      sendResultMsg(stompClient, rowColInfo.id, 0);\r\n    }\r\n  }, [rowColInfo?.id, stompClient]);\r\n\r\n  if (splitNo == null || displayNo == null) {\r\n    return (\r\n      <Message\r\n        msg={\r\n          '表示盤番号、及び面番号を設定してください。例：http://operationcenter/contentapp/?display_no=0&display_split_no=0'\r\n        }\r\n      />\r\n    );\r\n  }\r\n\r\n  splitNo = Number(splitNo);\r\n  displayNo = Number(displayNo);\r\n\r\n  // モニターの自動画角調整機能対応\r\n  // モニターが描画範囲を検知して自動で画角調整を行うため、描画範囲を示すための目印を四隅に追加する。\r\n  const cornerCommonStyle = \"absolute h-[1px] w-[3px] bg-white\"; //四隅の横並び3pxを白色(#FFFFFF)に塗りつぶす\r\n\r\n  return (\r\n    <div className=\"static\">\r\n      <div className={`${cornerCommonStyle} left-0 top-0`}></div>\r\n      <div className={`${cornerCommonStyle} top-0 right-0`}></div>\r\n      <div className={`${cornerCommonStyle} bottom-0 left-0`}></div>\r\n      <div className={`${cornerCommonStyle} bottom-0 right-0`}></div>\r\n\r\n      {rowColInfo.rowNum === 2 && rowColInfo.colNum === 2 && (\r\n\r\n        <div className=\"bg-black grid min-h-screen grid-rows-2 grid-cols-2 place-content-stretch select-none overflow-hidden\">\r\n\r\n          {/* border-xxのClassで、分割したContent間の線を引く */}\r\n          <div className=\"flex flex-col text-white w-full h-full border-r-[1px] border-b-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={0}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-l-[1px] border-b-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={1}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-r-[1px] border-t-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={2}\r\n            />\r\n          </div>\r\n          <div className=\"flex flex-col text-white w-full h-full border-l-[1px] border-t-[1px] border-white p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={3}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {rowColInfo.rowNum === 1 && rowColInfo.colNum === 1 && (\r\n        <div className=\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-stretch select-none overflow-hidden\">\r\n          <div className=\"flex flex-col text-white w-full h-full p-[1px]\">\r\n            <SplitScreen\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n              detailSplitNo={0}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {\r\n          <div className=\"bg-black\">\r\n          <div className=\"bg-black text-white\">\r\n            <Sound\r\n              displayNo={displayNo}\r\n              splitNo={splitNo}\r\n            />\r\n          </div>\r\n        </div>\r\n      }\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * HtmlRootのFontSize変更\r\n *\r\n * @param {Number} size px単位のFontSizeの数字\r\n */\r\n function setRootFontSize(size) {\r\n  let fontSize = size || 16;\r\n\r\n  document.getElementsByTagName('html')[0].style['font-size'] = fontSize + 'px';\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": "0IAAA,MAAOA,MAAP,EAAgBC,QAAhB,CAA0BC,SAA1B,KAA2C,OAA3C,CACA,MAAOC,YAAP,KAAwB,2BAAxB,CACA,MAAOC,MAAP,KAAkB,iBAAlB,CACA,OAASC,eAAT,CAA0BC,cAA1B,KAAgD,mBAAhD,CACA,OAASC,eAAT,KAAgC,kBAAhC,CACA,OAASC,aAAT,CAAwBC,aAAxB,KAA6C,kBAA7C,CACA,MAAOC,QAAP,KAAoB,gCAApB,CACA,OAASC,eAAT,KAAgC,QAAhC,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,wFACA,QAASC,KAAT,EAAgB,CACd,cAAoCX,QAAQ,CAAC,CAAEY,MAAM,CAAE,CAAV,CAAaC,MAAM,CAAE,CAArB,CAAD,CAA5C,wCAAOC,UAAP,eAAmBC,aAAnB,eAEA;AACA,qBAA4BT,eAAe,EAA3C,sDAAOU,MAAP,sBAAeC,SAAf,sBAEA,GAAIC,QAAO,CAAGF,MAAM,CAACG,GAAP,CAAW,kBAAX,CAAd,CACA,GAAIC,UAAS,CAAGJ,MAAM,CAACG,GAAP,CAAW,YAAX,CAAhB,CAEAlB,SAAS,CAAC,UAAM,CACdoB,QAAQ,CAACC,KAAT,CAAiB,SAAWF,SAAX,CAAuB,QAAvB,CAAkCF,OAAnD,CACD,CAFQ,CAEN,CAACE,SAAD,CAAYF,OAAZ,CAFM,CAAT,CAIA,GAAMK,eAAc,CAAG,QAAjBA,eAAiB,CAACC,OAAD,CAAa,CAClCC,OAAO,CAACC,IAAR,CAAa,mBAAqBF,OAAO,CAACG,IAA1C,EACA,GAAIC,QAAO,CAAGC,IAAI,CAACC,KAAL,CAAWN,OAAO,CAACG,IAAnB,CAAd,CACA,GAAIC,OAAJ,CAAa,CACX,GAAIA,OAAO,CAAChB,MAAR,GAAmB,CAAnB,EAAwBgB,OAAO,CAACf,MAAR,GAAmB,CAA/C,CAAkD,CAChD;AACAkB,eAAe,CAAC,EAAD,CAAf,CACD,CAHD,IAGO,CACL;AACAA,eAAe,CAAC,CAAD,CAAf,CACD,CACDhB,aAAa,CAACa,OAAD,CAAb,CACD,CACF,CAbD,CAeA,GAAMI,eAAc,CAAG,CACrBZ,SAAS,CAAEA,SADU,CAErBF,OAAO,CAAEA,OAFY,CAAvB,CAIA,GAAMe,WAAU,CAAGzB,aAAa,CAACY,SAAD,CAAYF,OAAZ,CAAhC,CAEAd,eAAe,CAAC6B,UAAU,CAAG,aAAd,CAA6BV,cAA7B,CAA6CS,cAA7C,CAAf,CAA6E;AAE7E,GAAME,YAAW,CAAG7B,cAAc,EAAlC,CAEAJ,SAAS,CAAC,UAAM,CACd,GAAIa,UAAJ,SAAIA,UAAJ,WAAIA,UAAU,CAAEqB,EAAhB,CAAoB,CAClB5B,aAAa,CAAC2B,WAAD,CAAcpB,UAAU,CAACqB,EAAzB,CAA6B,CAA7B,CAAb,CACD,CACF,CAJQ,CAIN,CAACrB,UAAD,SAACA,UAAD,iBAACA,UAAU,CAAEqB,EAAb,CAAiBD,WAAjB,CAJM,CAAT,CAMA,GAAIhB,OAAO,EAAI,IAAX,EAAmBE,SAAS,EAAI,IAApC,CAA0C,CACxC,mBACE,KAAC,OAAD,EACE,GAAG,CACD,2FAFJ,EADF,CAOD,CAEDF,OAAO,CAAGkB,MAAM,CAAClB,OAAD,CAAhB,CACAE,SAAS,CAAGgB,MAAM,CAAChB,SAAD,CAAlB,CAEA;AACA;AACA,GAAMiB,kBAAiB,CAAG,mCAA1B,CAA+D;AAE/D,mBACE,aAAK,SAAS,CAAC,QAAf,wBACE,YAAK,SAAS,WAAKA,iBAAL,iBAAd,EADF,cAEE,YAAK,SAAS,WAAKA,iBAAL,kBAAd,EAFF,cAGE,YAAK,SAAS,WAAKA,iBAAL,oBAAd,EAHF,cAIE,YAAK,SAAS,WAAKA,iBAAL,qBAAd,EAJF,CAMGvB,UAAU,CAACF,MAAX,GAAsB,CAAtB,EAA2BE,UAAU,CAACD,MAAX,GAAsB,CAAjD,eAEC,aAAK,SAAS,CAAC,sGAAf,wBAGE,YAAK,SAAS,CAAC,2FAAf,uBACE,KAAC,WAAD,EACE,SAAS,CAAEO,SADb,CAEE,OAAO,CAAEF,OAFX,CAGE,aAAa,CAAE,CAHjB,EADF,EAHF,cAUE,YAAK,SAAS,CAAC,2FAAf,uBACE,KAAC,WAAD,EACE,SAAS,CAAEE,SADb,CAEE,OAAO,CAAEF,OAFX,CAGE,aAAa,CAAE,CAHjB,EADF,EAVF,cAiBE,YAAK,SAAS,CAAC,2FAAf,uBACE,KAAC,WAAD,EACE,SAAS,CAAEE,SADb,CAEE,OAAO,CAAEF,OAFX,CAGE,aAAa,CAAE,CAHjB,EADF,EAjBF,cAwBE,YAAK,SAAS,CAAC,2FAAf,uBACE,KAAC,WAAD,EACE,SAAS,CAAEE,SADb,CAEE,OAAO,CAAEF,OAFX,CAGE,aAAa,CAAE,CAHjB,EADF,EAxBF,GARJ,CAyCGJ,UAAU,CAACF,MAAX,GAAsB,CAAtB,EAA2BE,UAAU,CAACD,MAAX,GAAsB,CAAjD,eACC,YAAK,SAAS,CAAC,sGAAf,uBACE,YAAK,SAAS,CAAC,gDAAf,uBACE,KAAC,WAAD,EACE,SAAS,CAAEO,SADb,CAEE,OAAO,CAAEF,OAFX,CAGE,aAAa,CAAE,CAHjB,EADF,EADF,EA1CJ,cAqDM,YAAK,SAAS,CAAC,UAAf,uBACA,YAAK,SAAS,CAAC,qBAAf,uBACE,KAAC,KAAD,EACE,SAAS,CAAEE,SADb,CAEE,OAAO,CAAEF,OAFX,EADF,EADA,EArDN,GADF,CAiED,CAED;AACA;AACA;AACA;AACA,GACC,QAASa,gBAAT,CAAyBO,IAAzB,CAA+B,CAC9B,GAAIC,SAAQ,CAAGD,IAAI,EAAI,EAAvB,CAEAjB,QAAQ,CAACmB,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyCC,KAAzC,CAA+C,WAA/C,EAA8DF,QAAQ,CAAG,IAAzE,CACD,CAED,cAAe5B,KAAf"}, "metadata": {}, "sourceType": "module"}