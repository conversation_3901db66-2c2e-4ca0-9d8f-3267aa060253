{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import TextScroll from'./elements/TextScroll';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource}from'../utils/Util.js';/**\r\n * 引継事項コンテンツ<br>\r\n * propsは、「3.16引継事項コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * フリーテキストの引継事項の内容を表示する。（最大6件）\r\n * 引継事項が長い場合は流動表示することとし、文字色は任意に設定可能とする。\r\n *\r\n * @module HandOver\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{createElement as _createElement}from\"react\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var HandOver=function HandOver(props){var _props$handover_listi;var MAX_ROW=6;return/*#__PURE__*/_jsxs(\"div\",{className:\"text-7xl\",children:[/*#__PURE__*/_jsx(Title,{title:'引継事項'}),/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-1 leading-[1] gap-y-[3.2rem] mt-[3rem]\",children:isValidSource(props)&&((_props$handover_listi=props.handover_listing)===null||_props$handover_listi===void 0?void 0:_props$handover_listi.map(function(item,index){//フリーテキストの引継事項の内容を表示する。（最大6件）\nif(index>=MAX_ROW)return undefined;var cellInfo=getCellFace(item);var cellInfoPropFrame={className:cellInfo.className,text_color:cellInfo.text_color};return/*#__PURE__*/_createElement(CellBox,_objectSpread(_objectSpread({},cellInfoPropFrame),{},{key:index}),/*#__PURE__*/_jsx(TextScroll,{content:item.display_text,background_color:cellInfo.background_color}));}))})]});};export default HandOver;", "map": {"version": 3, "names": ["React", "Title", "TextScroll", "CellBox", "getCellFace", "isValidSource", "HandOver", "props", "MAX_ROW", "handover_listing", "map", "item", "index", "undefined", "cellInfo", "cellInfoPropFrame", "className", "text_color", "display_text", "background_color"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Handover.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport TextScroll from './elements/TextScroll';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 引継事項コンテンツ<br>\r\n * propsは、「3.16引継事項コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * フリーテキストの引継事項の内容を表示する。（最大6件）\r\n * 引継事項が長い場合は流動表示することとし、文字色は任意に設定可能とする。\r\n *\r\n * @module HandOver\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst HandOver = (props) => {\r\n  const MAX_ROW = 6;\r\n  return (\r\n    <div className=\"text-7xl\">\r\n      <Title title={'引継事項'} />\r\n      <div className=\"border-transparent border-x-[1rem] grid grid-cols-1 leading-[1] gap-y-[3.2rem] mt-[3rem]\">\r\n        {isValidSource(props) &&\r\n          props.handover_listing?.map((item, index) => {\r\n            //フリーテキストの引継事項の内容を表示する。（最大6件）\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            let cellInfo = getCellFace(item);\r\n            let cellInfoPropFrame = {\r\n              className: cellInfo.className,\r\n              text_color: cellInfo.text_color,\r\n            };\r\n            return (\r\n              <CellBox {...cellInfoPropFrame} key={index}>\r\n                <TextScroll\r\n                  content={item.display_text}\r\n                  background_color={cellInfo.background_color}\r\n                />\r\n              </CellBox>\r\n            );\r\n          })}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HandOver;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,2IACA,GAAMC,SAAQ,CAAG,QAAXA,SAAW,CAACC,KAAD,CAAW,2BAC1B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,aAAK,SAAS,CAAC,UAAf,wBACE,KAAC,KAAD,EAAO,KAAK,CAAE,MAAd,EADF,cAEE,YAAK,SAAS,CAAC,0FAAf,UACGH,aAAa,CAACE,KAAD,CAAb,0BACCA,KAAK,CAACE,gBADP,gDACC,sBAAwBC,GAAxB,CAA4B,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC3C;AACA,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,GAAIC,SAAQ,CAAGV,WAAW,CAACO,IAAD,CAA1B,CACA,GAAII,kBAAiB,CAAG,CACtBC,SAAS,CAAEF,QAAQ,CAACE,SADE,CAEtBC,UAAU,CAAEH,QAAQ,CAACG,UAFC,CAAxB,CAIA,mBACE,eAAC,OAAD,gCAAaF,iBAAb,MAAgC,GAAG,CAAEH,KAArC,gBACE,KAAC,UAAD,EACE,OAAO,CAAED,IAAI,CAACO,YADhB,CAEE,gBAAgB,CAAEJ,QAAQ,CAACK,gBAF7B,EADF,CADF,CAQD,CAjBD,CADD,CADH,EAFF,GADF,CA0BD,CA5BD,CA8BA,cAAeb,SAAf"}, "metadata": {}, "sourceType": "module"}