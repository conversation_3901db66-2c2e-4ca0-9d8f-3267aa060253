{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport useBoolean from '../useBoolean';\nimport useEventListener from '../useEventListener';\nexport default (function (target, options) {\n  var _a = options || {},\n      onEnter = _a.onEnter,\n      onLeave = _a.onLeave,\n      onChange = _a.onChange;\n\n  var _b = __read(useBoolean(false), 2),\n      state = _b[0],\n      _c = _b[1],\n      setTrue = _c.setTrue,\n      setFalse = _c.setFalse;\n\n  useEventListener('mouseenter', function () {\n    onEnter === null || onEnter === void 0 ? void 0 : onEnter();\n    setTrue();\n    onChange === null || onChange === void 0 ? void 0 : onChange(true);\n  }, {\n    target: target\n  });\n  useEventListener('mouseleave', function () {\n    onLeave === null || onLeave === void 0 ? void 0 : onLeave();\n    setFalse();\n    onChange === null || onChange === void 0 ? void 0 : onChange(false);\n  }, {\n    target: target\n  });\n  return state;\n});", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useBoolean", "useEventListener", "target", "options", "_a", "onEnter", "onLeave", "onChange", "_b", "state", "_c", "setTrue", "setFalse"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useHover/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport useBoolean from '../useBoolean';\nimport useEventListener from '../useEventListener';\nexport default (function (target, options) {\n  var _a = options || {},\n    onEnter = _a.onEnter,\n    onLeave = _a.onLeave,\n    onChange = _a.onChange;\n  var _b = __read(useBoolean(false), 2),\n    state = _b[0],\n    _c = _b[1],\n    setTrue = _c.setTrue,\n    setFalse = _c.setFalse;\n  useEventListener('mouseenter', function () {\n    onEnter === null || onEnter === void 0 ? void 0 : onEnter();\n    setTrue();\n    onChange === null || onChange === void 0 ? void 0 : onChange(true);\n  }, {\n    target: target\n  });\n  useEventListener('mouseleave', function () {\n    onLeave === null || onLeave === void 0 ? void 0 : onLeave();\n    setFalse();\n    onChange === null || onChange === void 0 ? void 0 : onChange(false);\n  }, {\n    target: target\n  });\n  return state;\n});"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,OAAOO,UAAP,MAAuB,eAAvB;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,gBAAgB,UAAUC,MAAV,EAAkBC,OAAlB,EAA2B;EACzC,IAAIC,EAAE,GAAGD,OAAO,IAAI,EAApB;EAAA,IACEE,OAAO,GAAGD,EAAE,CAACC,OADf;EAAA,IAEEC,OAAO,GAAGF,EAAE,CAACE,OAFf;EAAA,IAGEC,QAAQ,GAAGH,EAAE,CAACG,QAHhB;;EAIA,IAAIC,EAAE,GAAGxB,MAAM,CAACgB,UAAU,CAAC,KAAD,CAAX,EAAoB,CAApB,CAAf;EAAA,IACES,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,EAAE,GAAGF,EAAE,CAAC,CAAD,CAFT;EAAA,IAGEG,OAAO,GAAGD,EAAE,CAACC,OAHf;EAAA,IAIEC,QAAQ,GAAGF,EAAE,CAACE,QAJhB;;EAKAX,gBAAgB,CAAC,YAAD,EAAe,YAAY;IACzCI,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,EAAzD;IACAM,OAAO;IACPJ,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC,IAAD,CAA5D;EACD,CAJe,EAIb;IACDL,MAAM,EAAEA;EADP,CAJa,CAAhB;EAOAD,gBAAgB,CAAC,YAAD,EAAe,YAAY;IACzCK,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,EAAzD;IACAM,QAAQ;IACRL,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC,KAAD,CAA5D;EACD,CAJe,EAIb;IACDL,MAAM,EAAEA;EADP,CAJa,CAAhB;EAOA,OAAOO,KAAP;AACD,CAzBD"}, "metadata": {}, "sourceType": "module"}