{"ast": null, "code": "import _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\n\n/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport var HeartbeatInfo = /*#__PURE__*/function () {\n  function HeartbeatInfo(client) {\n    _classCallCheck(this, HeartbeatInfo);\n\n    this.client = client;\n  }\n\n  _createClass(HeartbeatInfo, [{\n    key: \"outgoing\",\n    get: function get() {\n      return this.client.heartbeatOutgoing;\n    },\n    set: function set(value) {\n      this.client.heartbeatOutgoing = value;\n    }\n  }, {\n    key: \"incoming\",\n    get: function get() {\n      return this.client.heartbeatIncoming;\n    },\n    set: function set(value) {\n      this.client.heartbeatIncoming = value;\n    }\n  }]);\n\n  return HeartbeatInfo;\n}();", "map": {"version": 3, "mappings": ";;;AAEA;;;;;AAKA,WAAaA,aAAb;EACE,uBAAoBC,MAApB,EAAwC;IAAA;;IAApB;EAAwB;;EAD9C;IAAA;IAAA,KAGE,eAAY;MACV,OAAO,KAAKA,MAAL,CAAYC,iBAAnB;IACD,CALH;IAAA,KAOE,aAAaC,KAAb,EAA0B;MACxB,KAAKF,MAAL,CAAYC,iBAAZ,GAAgCC,KAAhC;IACD;EATH;IAAA;IAAA,KAWE,eAAY;MACV,OAAO,KAAKF,MAAL,CAAYG,iBAAnB;IACD,CAbH;IAAA,KAeE,aAAaD,KAAb,EAA0B;MACxB,KAAKF,MAAL,CAAYG,iBAAZ,GAAgCD,KAAhC;IACD;EAjBH;;EAAA;AAAA", "names": ["HeartbeatInfo", "client", "heartbeatOutgoing", "value", "heartbeatIncoming"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\compatibility\\heartbeat-info.ts"], "sourcesContent": ["import { CompatClient } from './compat-client';\n\n/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(private client: CompatClient) {}\n\n  get outgoing(): number {\n    return this.client.heartbeatOutgoing;\n  }\n\n  set outgoing(value: number) {\n    this.client.heartbeatOutgoing = value;\n  }\n\n  get incoming(): number {\n    return this.client.heartbeatIncoming;\n  }\n\n  set incoming(value: number) {\n    this.client.heartbeatIncoming = value;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}