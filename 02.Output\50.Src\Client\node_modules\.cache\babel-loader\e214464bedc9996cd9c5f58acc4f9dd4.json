{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\n\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = typeof Ctor == 'function' && Ctor.prototype || objectProto;\n  return value === proto;\n}\n\nmodule.exports = isPrototype;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "isPrototype", "value", "Ctor", "constructor", "proto", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_isPrototype.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\nmodule.exports = isPrototype;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC1B,IAAIC,IAAI,GAAGD,KAAK,IAAIA,KAAK,CAACE,WAA1B;EAAA,IACIC,KAAK,GAAI,OAAOF,IAAP,IAAe,UAAf,IAA6BA,IAAI,CAACH,SAAnC,IAAiDF,WAD7D;EAGA,OAAOI,KAAK,KAAKG,KAAjB;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBN,WAAjB"}, "metadata": {}, "sourceType": "script"}