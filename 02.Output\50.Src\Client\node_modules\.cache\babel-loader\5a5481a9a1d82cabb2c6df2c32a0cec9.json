{"ast": null, "code": "import { Client } from '../client';\nimport { HeartbeatInfo } from './heartbeat-info';\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n */\n\nexport class CompatClient extends Client {\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory) {\n    super();\n    /**\n     * It is no op now. No longer needed. Large packets work out of the box.\n     */\n\n    this.maxWebSocketFrameSize = 16 * 1024;\n    this._heartbeatInfo = new HeartbeatInfo(this);\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory; // Default from previous version\n\n    this.debug = function () {\n      console.log(...arguments);\n    };\n  }\n\n  _parseConnect() {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers = {};\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [headers.login, headers.passcode, connectCallback, errorCallback, closeEventCallback, headers.host] = args;\n          break;\n\n        default:\n          [headers.login, headers.passcode, connectCallback, errorCallback, closeEventCallback] = args;\n      }\n    }\n\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n\n\n  connect() {\n    const out = this._parseConnect(...arguments);\n\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n\n    super.activate();\n  }\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n\n\n  disconnect(disconnectCallback) {\n    let headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n\n    this.disconnectHeaders = headers;\n    super.deactivate();\n  }\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n\n\n  send(destination) {\n    let headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';\n    headers = Object.assign({}, headers);\n    const skipContentLengthHeader = headers['content-length'] === false;\n\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n\n    this.publish({\n      destination,\n      headers: headers,\n      body,\n      skipContentLengthHeader\n    });\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n\n\n  set reconnect_delay(value) {\n    this.reconnectDelay = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n\n\n  get ws() {\n    return this.webSocket;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n\n\n  get version() {\n    return this.connectedVersion;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n\n\n  get onreceive() {\n    return this.onUnhandledMessage;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n\n\n  set onreceive(value) {\n    this.onUnhandledMessage = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n\n\n  get onreceipt() {\n    return this.onUnhandledReceipt;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n\n\n  set onreceipt(value) {\n    this.onUnhandledReceipt = value;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n\n\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n\n\n  set heartbeat(value) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n\n}", "map": {"version": 3, "mappings": "AAAA,SAASA,MAAT,QAAuB,WAAvB;AAGA,SAASC,aAAT,QAA8B,kBAA9B;AAEA;;;;;;;;;;AASA,OAAM,MAAOC,YAAP,SAA4BF,MAA5B,CAAkC;EAMtC;;;;;;;;EAQAG,YAAYC,gBAAZ,EAAuC;IACrC;IAdF;;;;IAGO,6BAAgC,KAAK,IAArC;IAoOC,sBAAgC,IAAIH,aAAJ,CAAkB,IAAlB,CAAhC;IAxNN,KAAKI,eAAL,GAAuB,CAAvB;IACA,KAAKD,gBAAL,GAAwBA,gBAAxB,CAHqC,CAIrC;;IACA,KAAKE,KAAL,GAAa,YAAsB;MACjCC,OAAO,CAACC,GAAR,CAAY,YAAZ;IACD,CAFD;EAGD;;EAEOC,aAAa,GAAe;IAClC,IAAIC,kBAAJ;IACA,IAAIC,eAAJ;IACA,IAAIC,aAAJ;IACA,IAAIC,OAAO,GAAiB,EAA5B;;IAJkC,kCAAXC,IAAW;MAAXA,IAAW;IAAA;;IAKlC,IAAIA,IAAI,CAACC,MAAL,GAAc,CAAlB,EAAqB;MACnB,MAAM,IAAIC,KAAJ,CAAU,uCAAV,CAAN;IACD;;IACD,IAAI,OAAOF,IAAI,CAAC,CAAD,CAAX,KAAmB,UAAvB,EAAmC;MACjC,CAACD,OAAD,EAAUF,eAAV,EAA2BC,aAA3B,EAA0CF,kBAA1C,IAAgEI,IAAhE;IACD,CAFD,MAEO;MACL,QAAQA,IAAI,CAACC,MAAb;QACE,KAAK,CAAL;UACE,CACEF,OAAO,CAACI,KADV,EAEEJ,OAAO,CAACK,QAFV,EAGEP,eAHF,EAIEC,aAJF,EAKEF,kBALF,EAMEG,OAAO,CAACM,IANV,IAOIL,IAPJ;UAQA;;QACF;UACE,CACED,OAAO,CAACI,KADV,EAEEJ,OAAO,CAACK,QAFV,EAGEP,eAHF,EAIEC,aAJF,EAKEF,kBALF,IAMII,IANJ;MAZJ;IAoBD;;IAED,OAAO,CAACD,OAAD,EAAUF,eAAV,EAA2BC,aAA3B,EAA0CF,kBAA1C,CAAP;EACD;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BOU,OAAO,GAAe;IAC3B,MAAMC,GAAG,GAAG,KAAKZ,aAAL,CAAmB,YAAnB,CAAZ;;IAEA,IAAIY,GAAG,CAAC,CAAD,CAAP,EAAY;MACV,KAAKC,cAAL,GAAsBD,GAAG,CAAC,CAAD,CAAzB;IACD;;IACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;MACV,KAAKE,SAAL,GAAiBF,GAAG,CAAC,CAAD,CAApB;IACD;;IACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;MACV,KAAKG,YAAL,GAAoBH,GAAG,CAAC,CAAD,CAAvB;IACD;;IACD,IAAIA,GAAG,CAAC,CAAD,CAAP,EAAY;MACV,KAAKI,gBAAL,GAAwBJ,GAAG,CAAC,CAAD,CAA3B;IACD;;IAED,MAAMK,QAAN;EACD;EAED;;;;;;;;;;;;;EAWOC,UAAU,CACfC,kBADe,EAEW;IAAA,IAA1Bf,OAA0B,uEAAF,EAAE;;IAE1B,IAAIe,kBAAJ,EAAwB;MACtB,KAAKC,YAAL,GAAoBD,kBAApB;IACD;;IACD,KAAKE,iBAAL,GAAyBjB,OAAzB;IAEA,MAAMkB,UAAN;EACD;EAED;;;;;;;;;;;;;;;;;;;;;;EAoBOC,IAAI,CACTC,WADS,EAGQ;IAAA,IADjBpB,OACiB,uEADiB,EACjB;IAAA,IAAjBqB,IAAiB,uEAAF,EAAE;IAEjBrB,OAAO,GAAIsB,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BvB,OAA1B,CAAX;IAEA,MAAMwB,uBAAuB,GAAGxB,OAAO,CAAC,gBAAD,CAAP,KAA8B,KAA9D;;IACA,IAAIwB,uBAAJ,EAA6B;MAC3B,OAAOxB,OAAO,CAAC,gBAAD,CAAd;IACD;;IACD,KAAKyB,OAAL,CAAa;MACXL,WADW;MAEXpB,OAAO,EAAEA,OAFE;MAGXqB,IAHW;MAIXG;IAJW,CAAb;EAMD;EAED;;;;;;;EAKmB,IAAfhC,eAAe,CAACkC,KAAD,EAAc;IAC/B,KAAKC,cAAL,GAAsBD,KAAtB;EACD;EAED;;;;;;;EAKM,IAAFE,EAAE;IACJ,OAAO,KAAKC,SAAZ;EACD;EAED;;;;;;;EAKW,IAAPC,OAAO;IACT,OAAO,KAAKC,gBAAZ;EACD;EAED;;;;;;;EAKa,IAATC,SAAS;IACX,OAAO,KAAKC,kBAAZ;EACD;EAED;;;;;;;EAKa,IAATD,SAAS,CAACN,KAAD,EAA2B;IACtC,KAAKO,kBAAL,GAA0BP,KAA1B;EACD;EAED;;;;;;;;EAMa,IAATQ,SAAS;IACX,OAAO,KAAKC,kBAAZ;EACD;EAED;;;;;;;EAKa,IAATD,SAAS,CAACR,KAAD,EAAyB;IACpC,KAAKS,kBAAL,GAA0BT,KAA1B;EACD;EAID;;;;;;;;EAMa,IAATU,SAAS;IACX,OAAO,KAAKC,cAAZ;EACD;EAED;;;;;;;;EAMa,IAATD,SAAS,CAACV,KAAD,EAA8C;IACzD,KAAKY,iBAAL,GAAyBZ,KAAK,CAACa,QAA/B;IACA,KAAKC,iBAAL,GAAyBd,KAAK,CAACe,QAA/B;EACD;;AA7PqC", "names": ["Client", "HeartbeatInfo", "CompatClient", "constructor", "webSocketFactory", "reconnect_delay", "debug", "console", "log", "_parseConnect", "closeEventCallback", "connectCallback", "<PERSON><PERSON><PERSON><PERSON>", "headers", "args", "length", "Error", "login", "passcode", "host", "connect", "out", "connectHeaders", "onConnect", "onStompError", "onWebSocketClose", "activate", "disconnect", "disconnectCallback", "onDisconnect", "disconnectHeaders", "deactivate", "send", "destination", "body", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publish", "value", "reconnectDelay", "ws", "webSocket", "version", "connectedVersion", "onreceive", "onUnhandledMessage", "onreceipt", "onUnhandledReceipt", "heartbeat", "_heartbeatInfo", "heartbeatIncoming", "incoming", "heartbeatOutgoing", "outgoing"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\compatibility\\compat-client.ts"], "sourcesContent": ["import { Client } from '../client';\nimport { StompHeaders } from '../stomp-headers';\nimport { frameCallbackType, messageCallbackType } from '../types';\nimport { HeartbeatInfo } from './heartbeat-info';\n\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n */\nexport class CompatClient extends Client {\n  /**\n   * It is no op now. No longer needed. Large packets work out of the box.\n   */\n  public maxWebSocketFrameSize: number = 16 * 1024;\n\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory: () => any) {\n    super();\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory;\n    // Default from previous version\n    this.debug = (...message: any[]) => {\n      console.log(...message);\n    };\n  }\n\n  private _parseConnect(...args: any[]): any {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers: StompHeaders = {};\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n            headers.host,\n          ] = args;\n          break;\n        default:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n          ] = args;\n      }\n    }\n\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public connect(...args: any[]): void {\n    const out = this._parseConnect(...args);\n\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n\n    super.activate();\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public disconnect(\n    disconnectCallback?: any,\n    headers: StompHeaders = {}\n  ): void {\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n    this.disconnectHeaders = headers;\n\n    super.deactivate();\n  }\n\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public send(\n    destination: string,\n    headers: { [key: string]: any } = {},\n    body: string = ''\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    const skipContentLengthHeader = headers['content-length'] === false;\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n    this.publish({\n      destination,\n      headers: headers as StompHeaders,\n      body,\n      skipContentLengthHeader,\n    });\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n  set reconnect_delay(value: number) {\n    this.reconnectDelay = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n  get ws(): any {\n    return this.webSocket;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n  get version() {\n    return this.connectedVersion;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  get onreceive(): messageCallbackType {\n    return this.onUnhandledMessage;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  set onreceive(value: messageCallbackType) {\n    this.onUnhandledMessage = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n  get onreceipt(): frameCallbackType {\n    return this.onUnhandledReceipt;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n  set onreceipt(value: frameCallbackType) {\n    this.onUnhandledReceipt = value;\n  }\n\n  private _heartbeatInfo: HeartbeatInfo = new HeartbeatInfo(this);\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  set heartbeat(value: { incoming: number; outgoing: number }) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}