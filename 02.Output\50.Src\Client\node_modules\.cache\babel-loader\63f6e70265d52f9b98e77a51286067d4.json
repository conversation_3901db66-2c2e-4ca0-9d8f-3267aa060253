{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n      m = s && o[s],\n      i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\n\nfunction useInViewport(target, options) {\n  var _a = __read(useState(), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var _b = __read(useState(), 2),\n      ratio = _b[0],\n      setRatio = _b[1];\n\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target);\n\n    if (!el) {\n      return;\n    }\n\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1[\"return\"])) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, options), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    observer.observe(el);\n    return function () {\n      observer.disconnect();\n    };\n  }, [], target);\n  return [state, ratio];\n}\n\nexport default useInViewport;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "e", "next", "done", "push", "value", "error", "__values", "TypeError", "useState", "getTargetElement", "useEffectWithTarget", "useInViewport", "target", "options", "_a", "state", "setState", "_b", "ratio", "setRatio", "el", "observer", "IntersectionObserver", "entries", "e_1", "entries_1", "entries_1_1", "entry", "intersectionRatio", "isIntersecting", "e_1_1", "root", "observe", "disconnect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useInViewport/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useInViewport(target, options) {\n  var _a = __read(useState(), 2),\n    state = _a[0],\n    setState = _a[1];\n  var _b = __read(useState(), 2),\n    ratio = _b[0],\n    setRatio = _b[1];\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1[\"return\"])) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, options), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    observer.observe(el);\n    return function () {\n      observer.disconnect();\n    };\n  }, [], target);\n  return [state, ratio];\n}\nexport default useInViewport;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaT,CAAb,EAAgB;EAClD,IAAIU,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIV,CAAC,GAAGW,CAAC,CAACJ,IAAF,CAAOG,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACf,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACa,CAAC,GAAGd,CAAC,CAACiB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBP,CAAC,GAAGX,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCW,CAAC,CAACJ,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIgB,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,UAAUZ,CAAV,EAAa;EACnD,IAAIX,CAAC,GAAG,OAAOa,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA/C;EAAA,IACEF,CAAC,GAAGZ,CAAC,IAAIW,CAAC,CAACX,CAAD,CADZ;EAAA,IAEEC,CAAC,GAAG,CAFN;EAGA,IAAIW,CAAJ,EAAO,OAAOA,CAAC,CAACJ,IAAF,CAAOG,CAAP,CAAP;EACP,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACP,MAAT,KAAoB,QAA7B,EAAuC,OAAO;IAC5Cc,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIP,CAAC,IAAIV,CAAC,IAAIU,CAAC,CAACP,MAAhB,EAAwBO,CAAC,GAAG,KAAK,CAAT;MACxB,OAAO;QACLU,KAAK,EAAEV,CAAC,IAAIA,CAAC,CAACV,CAAC,EAAF,CADR;QAELkB,IAAI,EAAE,CAACR;MAFF,CAAP;IAID;EAP2C,CAAP;EASvC,MAAM,IAAIa,SAAJ,CAAcxB,CAAC,GAAG,yBAAH,GAA+B,iCAA9C,CAAN;AACD,CAfD;;AAgBA,OAAO,uBAAP;AACA,SAASyB,QAAT,QAAyB,OAAzB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;;AACA,SAASC,aAAT,CAAuBC,MAAvB,EAA+BC,OAA/B,EAAwC;EACtC,IAAIC,EAAE,GAAGrB,MAAM,CAACe,QAAQ,EAAT,EAAa,CAAb,CAAf;EAAA,IACEO,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,EAAE,GAAGxB,MAAM,CAACe,QAAQ,EAAT,EAAa,CAAb,CAAf;EAAA,IACEU,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGAP,mBAAmB,CAAC,YAAY;IAC9B,IAAIU,EAAE,GAAGX,gBAAgB,CAACG,MAAD,CAAzB;;IACA,IAAI,CAACQ,EAAL,EAAS;MACP;IACD;;IACD,IAAIC,QAAQ,GAAG,IAAIC,oBAAJ,CAAyB,UAAUC,OAAV,EAAmB;MACzD,IAAIC,GAAJ,EAASV,EAAT;;MACA,IAAI;QACF,KAAK,IAAIW,SAAS,GAAGnB,QAAQ,CAACiB,OAAD,CAAxB,EAAmCG,WAAW,GAAGD,SAAS,CAACxB,IAAV,EAAtD,EAAwE,CAACyB,WAAW,CAACxB,IAArF,EAA2FwB,WAAW,GAAGD,SAAS,CAACxB,IAAV,EAAzG,EAA2H;UACzH,IAAI0B,KAAK,GAAGD,WAAW,CAACtB,KAAxB;UACAe,QAAQ,CAACQ,KAAK,CAACC,iBAAP,CAAR;UACAZ,QAAQ,CAACW,KAAK,CAACE,cAAP,CAAR;QACD;MACF,CAND,CAME,OAAOC,KAAP,EAAc;QACdN,GAAG,GAAG;UACJnB,KAAK,EAAEyB;QADH,CAAN;MAGD,CAVD,SAUU;QACR,IAAI;UACF,IAAIJ,WAAW,IAAI,CAACA,WAAW,CAACxB,IAA5B,KAAqCY,EAAE,GAAGW,SAAS,CAAC,QAAD,CAAnD,CAAJ,EAAoEX,EAAE,CAACvB,IAAH,CAAQkC,SAAR;QACrE,CAFD,SAEU;UACR,IAAID,GAAJ,EAAS,MAAMA,GAAG,CAACnB,KAAV;QACV;MACF;IACF,CAnBc,EAmBZ1B,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKkC,OAAL,CAAT,EAAwB;MACjCkB,IAAI,EAAEtB,gBAAgB,CAACI,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACkB,IAA3D;IADW,CAAxB,CAnBI,CAAf;IAsBAV,QAAQ,CAACW,OAAT,CAAiBZ,EAAjB;IACA,OAAO,YAAY;MACjBC,QAAQ,CAACY,UAAT;IACD,CAFD;EAGD,CA/BkB,EA+BhB,EA/BgB,EA+BZrB,MA/BY,CAAnB;EAgCA,OAAO,CAACG,KAAD,EAAQG,KAAR,CAAP;AACD;;AACD,eAAeP,aAAf"}, "metadata": {}, "sourceType": "module"}