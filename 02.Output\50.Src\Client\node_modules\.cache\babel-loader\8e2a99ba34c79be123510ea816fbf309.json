{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\TotalFrequency.js\";\nimport React from 'react';\nimport Title from './elements/Title';\nimport Cell from './elements/Cell';\nimport { getCellFace, isValidSource, toThousands } from '../utils/Util.js';\n/**\r\n * 総合度数コンテンツ<br>\r\n * propsは、「3.11総合度数コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module TotalFrequency\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\n\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst TotalFrequency = props => {\n  let cell1, cell2, cell3;\n\n  if (isValidSource(props) && props.agg_unit) {\n    cell1 = getCellFace(props.agg_unit[0], 'text-right col-start-2 justify');\n    cell2 = getCellFace(props.agg_unit[1], 'text-right col-start-2 justify');\n    cell3 = getCellFace(props.agg_unit[2], 'text-right col-start-2 justify');\n  }\n\n  const MAX_ROW = 5;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      title: '119着信度数'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), isValidSource(props) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]\",\n      children: [props.agg_unit && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-start-3 grid grid-cols-[1fr,13.5rem]\",\n          children: /*#__PURE__*/_jsxDEV(Cell, { ...cell1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-start-5 grid grid-cols-[1fr,13.5rem]\",\n          children: /*#__PURE__*/_jsxDEV(Cell, { ...cell2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-start-7 grid grid-cols-[1fr,13.5rem]\",\n          children: /*#__PURE__*/_jsxDEV(Cell, { ...cell3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), props.agg_info && props.agg_info.map((infoItem, infoIndex) => {\n        if (infoIndex >= MAX_ROW) return undefined;\n        return /*#__PURE__*/_jsxDEV(TotalFrequencyRow, {\n          agg_info: infoItem\n        }, infoIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 22\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n\n_c = TotalFrequency;\n\nconst TotalFrequencyRow = props => {\n  var _props$agg_info, _props$agg_info$numbe;\n\n  let typeProp = getCellFace(props.agg_info, 'text-5xl justify');\n  let numCell1;\n  let numCell2;\n  let numCell3;\n  (_props$agg_info = props.agg_info) === null || _props$agg_info === void 0 ? void 0 : (_props$agg_info$numbe = _props$agg_info.number_list) === null || _props$agg_info$numbe === void 0 ? void 0 : _props$agg_info$numbe.forEach((numItem, numIndex) => {\n    if (numIndex === 0) {\n      numCell1 = getCellFace(numItem, 'justify-self-end w-fit text-6xl col-start-3');\n      numCell1.text = toThousands(numCell1.text);\n      addUnit(numCell1);\n    } else if (numIndex === 1) {\n      numCell2 = getCellFace(numItem, 'justify-self-end w-fit text-6xl col-start-5');\n      numCell2.text = toThousands(numCell2.text);\n      addUnit(numCell2);\n    } else if (numIndex === 2) {\n      numCell3 = getCellFace(numItem, 'justify-self-end w-fit text-6xl col-start-7');\n      numCell3.text = toThousands(numCell3.text);\n      addUnit(numCell3);\n    }\n  }); // props.agg_info.number_listのLength<3の場合に、Dummyの列情報を作る\n\n  if (!numCell1) {\n    numCell1 = {\n      className: 'col-start-3'\n    };\n  }\n\n  if (!numCell2) {\n    numCell2 = {\n      className: 'col-start-5'\n    };\n  }\n\n  if (!numCell3) {\n    numCell3 = {\n      className: 'col-start-7'\n    };\n  }\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Cell, { ...typeProp\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...numCell1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...numCell2\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Cell, { ...numCell3\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n_c2 = TotalFrequencyRow;\n\nfunction addUnit(countObj) {\n  if (countObj.text && countObj.text.endsWith('件')) {\n    countObj.text = countObj.text.substring(countObj.text.length - 1);\n  }\n\n  countObj.unit = '件';\n  countObj.unitStyle = {\n    fontSize: '3.5rem'\n  };\n}\n\nexport default TotalFrequency;\n\nvar _c, _c2;\n\n$RefreshReg$(_c, \"TotalFrequency\");\n$RefreshReg$(_c2, \"TotalFrequencyRow\");", "map": {"version": 3, "names": ["React", "Title", "Cell", "getCellFace", "isValidSource", "toThousands", "TotalFrequency", "props", "cell1", "cell2", "cell3", "agg_unit", "MAX_ROW", "agg_info", "map", "infoItem", "infoIndex", "undefined", "TotalFrequencyRow", "typeProp", "numCell1", "numCell2", "numCell3", "number_list", "for<PERSON>ach", "numItem", "numIndex", "text", "addUnit", "className", "count<PERSON>b<PERSON>", "endsWith", "substring", "length", "unit", "unitStyle", "fontSize"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/TotalFrequency.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource, toThousands } from '../utils/Util.js';\r\n\r\n/**\r\n * 総合度数コンテンツ<br>\r\n * propsは、「3.11総合度数コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module TotalFrequency\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst TotalFrequency = (props) => {\r\n  let cell1, cell2, cell3;\r\n  if (isValidSource(props) && props.agg_unit) {\r\n    cell1 = getCellFace(props.agg_unit[0], 'text-right col-start-2 justify');\r\n    cell2 = getCellFace(props.agg_unit[1], 'text-right col-start-2 justify');\r\n    cell3 = getCellFace(props.agg_unit[2], 'text-right col-start-2 justify');\r\n  }\r\n\r\n  const MAX_ROW = 5;\r\n\r\n  return (\r\n    <div>\r\n      <Title title={'119着信度数'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]\">\r\n          {props.agg_unit && (\r\n            <>\r\n              <div className=\"col-start-3 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell1} />\r\n              </div>\r\n              <div className=\"col-start-5 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell2} />\r\n              </div>\r\n              <div className=\"col-start-7 grid grid-cols-[1fr,13.5rem]\">\r\n                <Cell {...cell3} />\r\n              </div>\r\n            </>\r\n          )}\r\n          {props.agg_info &&\r\n            props.agg_info.map((infoItem, infoIndex) => {\r\n              if (infoIndex >= MAX_ROW) return undefined;\r\n\r\n              return <TotalFrequencyRow key={infoIndex} agg_info={infoItem} />;\r\n            })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst TotalFrequencyRow = (props) => {\r\n  let typeProp = getCellFace(props.agg_info, 'text-5xl justify');\r\n  let numCell1;\r\n  let numCell2;\r\n  let numCell3;\r\n\r\n  props.agg_info?.number_list?.forEach((numItem, numIndex) => {\r\n    if (numIndex === 0) {\r\n      numCell1 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-3'\r\n      );\r\n      numCell1.text = toThousands(numCell1.text);\r\n      addUnit(numCell1);\r\n    } else if (numIndex === 1) {\r\n      numCell2 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-5'\r\n      );\r\n      numCell2.text = toThousands(numCell2.text);\r\n      addUnit(numCell2);\r\n    } else if (numIndex === 2) {\r\n      numCell3 = getCellFace(\r\n        numItem,\r\n        'justify-self-end w-fit text-6xl col-start-7'\r\n      );\r\n      numCell3.text = toThousands(numCell3.text);\r\n      addUnit(numCell3);\r\n    }\r\n  });\r\n\r\n  // props.agg_info.number_listのLength<3の場合に、Dummyの列情報を作る\r\n  if (!numCell1) {\r\n    numCell1 = { className: 'col-start-3' };\r\n  }\r\n  if (!numCell2) {\r\n    numCell2 = { className: 'col-start-5' };\r\n  }\r\n  if (!numCell3) {\r\n    numCell3 = { className: 'col-start-7' };\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Cell {...typeProp} />\r\n      <Cell {...numCell1} />\r\n      <Cell {...numCell2} />\r\n      <Cell {...numCell3} />\r\n    </>\r\n  );\r\n};\r\n\r\nfunction addUnit(countObj) {\r\n  if (countObj.text && countObj.text.endsWith('件')) {\r\n    countObj.text = countObj.text.substring(countObj.text.length - 1);\r\n  }\r\n\r\n  countObj.unit = '件';\r\n  countObj.unitStyle = { fontSize: '3.5rem' };\r\n}\r\n\r\nexport default TotalFrequency;\r\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,KAAP,MAAkB,kBAAlB;AACA,OAAOC,IAAP,MAAiB,iBAAjB;AACA,SAASC,WAAT,EAAsBC,aAAtB,EAAqCC,WAArC,QAAwD,kBAAxD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AACA,MAAMC,cAAc,GAAIC,KAAD,IAAW;EAChC,IAAIC,KAAJ,EAAWC,KAAX,EAAkBC,KAAlB;;EACA,IAAIN,aAAa,CAACG,KAAD,CAAb,IAAwBA,KAAK,CAACI,QAAlC,EAA4C;IAC1CH,KAAK,GAAGL,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,EAAoB,gCAApB,CAAnB;IACAF,KAAK,GAAGN,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,EAAoB,gCAApB,CAAnB;IACAD,KAAK,GAAGP,WAAW,CAACI,KAAK,CAACI,QAAN,CAAe,CAAf,CAAD,EAAoB,gCAApB,CAAnB;EACD;;EAED,MAAMC,OAAO,GAAG,CAAhB;EAEA,oBACE;IAAA,wBACE,QAAC,KAAD;MAAO,KAAK,EAAE;IAAd;MAAA;MAAA;MAAA;IAAA,QADF,EAEGR,aAAa,CAACG,KAAD,CAAb,iBACC;MAAK,SAAS,EAAC,gMAAf;MAAA,WACGA,KAAK,CAACI,QAAN,iBACC;QAAA,wBACE;UAAK,SAAS,EAAC,0CAAf;UAAA,uBACE,QAAC,IAAD,OAAUH;UAAV;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QADF,eAIE;UAAK,SAAS,EAAC,0CAAf;UAAA,uBACE,QAAC,IAAD,OAAUC;UAAV;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAJF,eAOE;UAAK,SAAS,EAAC,0CAAf;UAAA,uBACE,QAAC,IAAD,OAAUC;UAAV;YAAA;YAAA;YAAA;UAAA;QADF;UAAA;UAAA;UAAA;QAAA,QAPF;MAAA,gBAFJ,EAcGH,KAAK,CAACM,QAAN,IACCN,KAAK,CAACM,QAAN,CAAeC,GAAf,CAAmB,CAACC,QAAD,EAAWC,SAAX,KAAyB;QAC1C,IAAIA,SAAS,IAAIJ,OAAjB,EAA0B,OAAOK,SAAP;QAE1B,oBAAO,QAAC,iBAAD;UAAmC,QAAQ,EAAEF;QAA7C,GAAwBC,SAAxB;UAAA;UAAA;UAAA;QAAA,QAAP;MACD,CAJD,CAfJ;IAAA;MAAA;MAAA;MAAA;IAAA,QAHJ;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AA4BD,CAtCD;;KAAMV,c;;AAwCN,MAAMY,iBAAiB,GAAIX,KAAD,IAAW;EAAA;;EACnC,IAAIY,QAAQ,GAAGhB,WAAW,CAACI,KAAK,CAACM,QAAP,EAAiB,kBAAjB,CAA1B;EACA,IAAIO,QAAJ;EACA,IAAIC,QAAJ;EACA,IAAIC,QAAJ;EAEA,mBAAAf,KAAK,CAACM,QAAN,6FAAgBU,WAAhB,gFAA6BC,OAA7B,CAAqC,CAACC,OAAD,EAAUC,QAAV,KAAuB;IAC1D,IAAIA,QAAQ,KAAK,CAAjB,EAAoB;MAClBN,QAAQ,GAAGjB,WAAW,CACpBsB,OADoB,EAEpB,6CAFoB,CAAtB;MAIAL,QAAQ,CAACO,IAAT,GAAgBtB,WAAW,CAACe,QAAQ,CAACO,IAAV,CAA3B;MACAC,OAAO,CAACR,QAAD,CAAP;IACD,CAPD,MAOO,IAAIM,QAAQ,KAAK,CAAjB,EAAoB;MACzBL,QAAQ,GAAGlB,WAAW,CACpBsB,OADoB,EAEpB,6CAFoB,CAAtB;MAIAJ,QAAQ,CAACM,IAAT,GAAgBtB,WAAW,CAACgB,QAAQ,CAACM,IAAV,CAA3B;MACAC,OAAO,CAACP,QAAD,CAAP;IACD,CAPM,MAOA,IAAIK,QAAQ,KAAK,CAAjB,EAAoB;MACzBJ,QAAQ,GAAGnB,WAAW,CACpBsB,OADoB,EAEpB,6CAFoB,CAAtB;MAIAH,QAAQ,CAACK,IAAT,GAAgBtB,WAAW,CAACiB,QAAQ,CAACK,IAAV,CAA3B;MACAC,OAAO,CAACN,QAAD,CAAP;IACD;EACF,CAvBD,EANmC,CA+BnC;;EACA,IAAI,CAACF,QAAL,EAAe;IACbA,QAAQ,GAAG;MAAES,SAAS,EAAE;IAAb,CAAX;EACD;;EACD,IAAI,CAACR,QAAL,EAAe;IACbA,QAAQ,GAAG;MAAEQ,SAAS,EAAE;IAAb,CAAX;EACD;;EACD,IAAI,CAACP,QAAL,EAAe;IACbA,QAAQ,GAAG;MAAEO,SAAS,EAAE;IAAb,CAAX;EACD;;EAED,oBACE;IAAA,wBACE,QAAC,IAAD,OAAUV;IAAV;MAAA;MAAA;MAAA;IAAA,QADF,eAEE,QAAC,IAAD,OAAUC;IAAV;MAAA;MAAA;MAAA;IAAA,QAFF,eAGE,QAAC,IAAD,OAAUC;IAAV;MAAA;MAAA;MAAA;IAAA,QAHF,eAIE,QAAC,IAAD,OAAUC;IAAV;MAAA;MAAA;MAAA;IAAA,QAJF;EAAA,gBADF;AAQD,CAlDD;;MAAMJ,iB;;AAoDN,SAASU,OAAT,CAAiBE,QAAjB,EAA2B;EACzB,IAAIA,QAAQ,CAACH,IAAT,IAAiBG,QAAQ,CAACH,IAAT,CAAcI,QAAd,CAAuB,GAAvB,CAArB,EAAkD;IAChDD,QAAQ,CAACH,IAAT,GAAgBG,QAAQ,CAACH,IAAT,CAAcK,SAAd,CAAwBF,QAAQ,CAACH,IAAT,CAAcM,MAAd,GAAuB,CAA/C,CAAhB;EACD;;EAEDH,QAAQ,CAACI,IAAT,GAAgB,GAAhB;EACAJ,QAAQ,CAACK,SAAT,GAAqB;IAAEC,QAAQ,EAAE;EAAZ,CAArB;AACD;;AAED,eAAe9B,cAAf"}, "metadata": {}, "sourceType": "module"}