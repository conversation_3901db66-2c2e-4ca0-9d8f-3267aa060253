{"ast": null, "code": "'use strict';\n\nvar utils = require('../../utils/iframe'),\n    random = require('../../utils/random'),\n    browser = require('../../utils/browser'),\n    urlUtils = require('../../utils/url'),\n    inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter;\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\n\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n  utils.polluteGlobalNamespace();\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n\n  this._createScript(urlWithId); // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n\n\n  this.timeoutId = setTimeout(function () {\n    debug('timeout');\n\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\n\ninherits(JsonpReceiver, EventEmitter);\n\nJsonpReceiver.prototype.abort = function () {\n  debug('abort');\n\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n\n    this._abort(err);\n  }\n};\n\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\n\nJsonpReceiver.prototype._callback = function (data) {\n  debug('_callback', data);\n\n  this._cleanup();\n\n  if (this.aborting) {\n    return;\n  }\n\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._abort = function (err) {\n  debug('_abort', err);\n\n  this._cleanup();\n\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._cleanup = function () {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n\n  if (this.script) {\n    var script = this.script; // Unfortunately, you can't really abort script loading of\n    // the script.\n\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror = script.onload = script.onclick = null;\n    this.script = null;\n  }\n\n  delete global[utils.WPrefix][this.id];\n};\n\nJsonpReceiver.prototype._scriptError = function () {\n  debug('_scriptError');\n  var self = this;\n\n  if (this.errorTimer) {\n    return;\n  }\n\n  this.errorTimer = setTimeout(function () {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\n\nJsonpReceiver.prototype._createScript = function (url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2; // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n\n  script.onload = function () {\n    debug('onload');\n\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  }; // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n\n\n  script.onreadystatechange = function () {\n    debug('onreadystatechange', script.readyState);\n\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {// intentionally empty\n        }\n      }\n\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  }; // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n\n\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {// intentionally empty\n      }\n\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\n\nmodule.exports = JsonpReceiver;", "map": {"version": 3, "names": ["utils", "require", "random", "browser", "urlUtils", "inherits", "EventEmitter", "debug", "process", "env", "NODE_ENV", "JsonpReceiver", "url", "self", "call", "polluteGlobalNamespace", "id", "string", "urlWithId", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "WPrefix", "global", "_callback", "bind", "_createScript", "timeoutId", "setTimeout", "_abort", "Error", "timeout", "prototype", "abort", "err", "code", "scriptErrorTimeout", "data", "_cleanup", "aborting", "emit", "removeAllListeners", "message", "clearTimeout", "script2", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "script", "onreadystatechange", "onerror", "onload", "onclick", "_scriptError", "errorTimer", "loaded<PERSON>kay", "document", "createElement", "src", "type", "charset", "readyState", "test", "htmlFor", "x", "async", "attachEvent", "isOpera", "event", "text", "head", "getElementsByTagName", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/receiver/jsonp.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../../utils/iframe')\n  , random = require('../../utils/random')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\n\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n\n  utils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n  this._createScript(urlWithId);\n\n  // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n  this.timeoutId = setTimeout(function() {\n    debug('timeout');\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\n\ninherits(JsonpReceiver, EventEmitter);\n\nJsonpReceiver.prototype.abort = function() {\n  debug('abort');\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n    this._abort(err);\n  }\n};\n\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\n\nJsonpReceiver.prototype._callback = function(data) {\n  debug('_callback', data);\n  this._cleanup();\n\n  if (this.aborting) {\n    return;\n  }\n\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._abort = function(err) {\n  debug('_abort', err);\n  this._cleanup();\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n  if (this.script) {\n    var script = this.script;\n    // Unfortunately, you can't really abort script loading of\n    // the script.\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror =\n        script.onload = script.onclick = null;\n    this.script = null;\n  }\n  delete global[utils.WPrefix][this.id];\n};\n\nJsonpReceiver.prototype._scriptError = function() {\n  debug('_scriptError');\n  var self = this;\n  if (this.errorTimer) {\n    return;\n  }\n\n  this.errorTimer = setTimeout(function() {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\n\nJsonpReceiver.prototype._createScript = function(url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2;  // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n  script.onload = function() {\n    debug('onload');\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  };\n\n  // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n  script.onreadystatechange = function() {\n    debug('onreadystatechange', script.readyState);\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {\n          // intentionally empty\n        }\n      }\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  };\n  // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {\n        // intentionally empty\n      }\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\n\nmodule.exports = JsonpReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAD,CAAnB;AAAA,IACIC,MAAM,GAAGD,OAAO,CAAC,oBAAD,CADpB;AAAA,IAEIE,OAAO,GAAGF,OAAO,CAAC,qBAAD,CAFrB;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,iBAAD,CAHtB;AAAA,IAIII,QAAQ,GAAGJ,OAAO,CAAC,UAAD,CAJtB;AAAA,IAKIK,YAAY,GAAGL,OAAO,CAAC,QAAD,CAAP,CAAkBK,YALrC;;AAQA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGN,OAAO,CAAC,OAAD,CAAP,CAAiB,8BAAjB,CAAR;AACD;;AAED,SAASU,aAAT,CAAuBC,GAAvB,EAA4B;EAC1BL,KAAK,CAACK,GAAD,CAAL;EACA,IAAIC,IAAI,GAAG,IAAX;EACAP,YAAY,CAACQ,IAAb,CAAkB,IAAlB;EAEAd,KAAK,CAACe,sBAAN;EAEA,KAAKC,EAAL,GAAU,MAAMd,MAAM,CAACe,MAAP,CAAc,CAAd,CAAhB;EACA,IAAIC,SAAS,GAAGd,QAAQ,CAACe,QAAT,CAAkBP,GAAlB,EAAuB,OAAOQ,kBAAkB,CAACpB,KAAK,CAACqB,OAAN,GAAgB,GAAhB,GAAsB,KAAKL,EAA5B,CAAhD,CAAhB;EAEAM,MAAM,CAACtB,KAAK,CAACqB,OAAP,CAAN,CAAsB,KAAKL,EAA3B,IAAiC,KAAKO,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAAjC;;EACA,KAAKC,aAAL,CAAmBP,SAAnB,EAX0B,CAa1B;;;EACA,KAAKQ,SAAL,GAAiBC,UAAU,CAAC,YAAW;IACrCpB,KAAK,CAAC,SAAD,CAAL;;IACAM,IAAI,CAACe,MAAL,CAAY,IAAIC,KAAJ,CAAU,0CAAV,CAAZ;EACD,CAH0B,EAGxBlB,aAAa,CAACmB,OAHU,CAA3B;AAID;;AAEDzB,QAAQ,CAACM,aAAD,EAAgBL,YAAhB,CAAR;;AAEAK,aAAa,CAACoB,SAAd,CAAwBC,KAAxB,GAAgC,YAAW;EACzCzB,KAAK,CAAC,OAAD,CAAL;;EACA,IAAIe,MAAM,CAACtB,KAAK,CAACqB,OAAP,CAAN,CAAsB,KAAKL,EAA3B,CAAJ,EAAoC;IAClC,IAAIiB,GAAG,GAAG,IAAIJ,KAAJ,CAAU,yBAAV,CAAV;IACAI,GAAG,CAACC,IAAJ,GAAW,IAAX;;IACA,KAAKN,MAAL,CAAYK,GAAZ;EACD;AACF,CAPD;;AASAtB,aAAa,CAACmB,OAAd,GAAwB,KAAxB;AACAnB,aAAa,CAACwB,kBAAd,GAAmC,IAAnC;;AAEAxB,aAAa,CAACoB,SAAd,CAAwBR,SAAxB,GAAoC,UAASa,IAAT,EAAe;EACjD7B,KAAK,CAAC,WAAD,EAAc6B,IAAd,CAAL;;EACA,KAAKC,QAAL;;EAEA,IAAI,KAAKC,QAAT,EAAmB;IACjB;EACD;;EAED,IAAIF,IAAJ,EAAU;IACR7B,KAAK,CAAC,SAAD,EAAY6B,IAAZ,CAAL;IACA,KAAKG,IAAL,CAAU,SAAV,EAAqBH,IAArB;EACD;;EACD,KAAKG,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyB,SAAzB;EACA,KAAKC,kBAAL;AACD,CAdD;;AAgBA7B,aAAa,CAACoB,SAAd,CAAwBH,MAAxB,GAAiC,UAASK,GAAT,EAAc;EAC7C1B,KAAK,CAAC,QAAD,EAAW0B,GAAX,CAAL;;EACA,KAAKI,QAAL;;EACA,KAAKC,QAAL,GAAgB,IAAhB;EACA,KAAKC,IAAL,CAAU,OAAV,EAAmBN,GAAG,CAACC,IAAvB,EAA6BD,GAAG,CAACQ,OAAjC;EACA,KAAKD,kBAAL;AACD,CAND;;AAQA7B,aAAa,CAACoB,SAAd,CAAwBM,QAAxB,GAAmC,YAAW;EAC5C9B,KAAK,CAAC,UAAD,CAAL;EACAmC,YAAY,CAAC,KAAKhB,SAAN,CAAZ;;EACA,IAAI,KAAKiB,OAAT,EAAkB;IAChB,KAAKA,OAAL,CAAaC,UAAb,CAAwBC,WAAxB,CAAoC,KAAKF,OAAzC;IACA,KAAKA,OAAL,GAAe,IAAf;EACD;;EACD,IAAI,KAAKG,MAAT,EAAiB;IACf,IAAIA,MAAM,GAAG,KAAKA,MAAlB,CADe,CAEf;IACA;;IACAA,MAAM,CAACF,UAAP,CAAkBC,WAAlB,CAA8BC,MAA9B;IACAA,MAAM,CAACC,kBAAP,GAA4BD,MAAM,CAACE,OAAP,GACxBF,MAAM,CAACG,MAAP,GAAgBH,MAAM,CAACI,OAAP,GAAiB,IADrC;IAEA,KAAKJ,MAAL,GAAc,IAAd;EACD;;EACD,OAAOxB,MAAM,CAACtB,KAAK,CAACqB,OAAP,CAAN,CAAsB,KAAKL,EAA3B,CAAP;AACD,CAjBD;;AAmBAL,aAAa,CAACoB,SAAd,CAAwBoB,YAAxB,GAAuC,YAAW;EAChD5C,KAAK,CAAC,cAAD,CAAL;EACA,IAAIM,IAAI,GAAG,IAAX;;EACA,IAAI,KAAKuC,UAAT,EAAqB;IACnB;EACD;;EAED,KAAKA,UAAL,GAAkBzB,UAAU,CAAC,YAAW;IACtC,IAAI,CAACd,IAAI,CAACwC,UAAV,EAAsB;MACpBxC,IAAI,CAACe,MAAL,CAAY,IAAIC,KAAJ,CAAU,0CAAV,CAAZ;IACD;EACF,CAJ2B,EAIzBlB,aAAa,CAACwB,kBAJW,CAA5B;AAKD,CAZD;;AAcAxB,aAAa,CAACoB,SAAd,CAAwBN,aAAxB,GAAwC,UAASb,GAAT,EAAc;EACpDL,KAAK,CAAC,eAAD,EAAkBK,GAAlB,CAAL;EACA,IAAIC,IAAI,GAAG,IAAX;EACA,IAAIiC,MAAM,GAAG,KAAKA,MAAL,GAAcxB,MAAM,CAACgC,QAAP,CAAgBC,aAAhB,CAA8B,QAA9B,CAA3B;EACA,IAAIZ,OAAJ,CAJoD,CAItC;;EAEdG,MAAM,CAAC9B,EAAP,GAAY,MAAMd,MAAM,CAACe,MAAP,CAAc,CAAd,CAAlB;EACA6B,MAAM,CAACU,GAAP,GAAa5C,GAAb;EACAkC,MAAM,CAACW,IAAP,GAAc,iBAAd;EACAX,MAAM,CAACY,OAAP,GAAiB,OAAjB;EACAZ,MAAM,CAACE,OAAP,GAAiB,KAAKG,YAAL,CAAkB3B,IAAlB,CAAuB,IAAvB,CAAjB;;EACAsB,MAAM,CAACG,MAAP,GAAgB,YAAW;IACzB1C,KAAK,CAAC,QAAD,CAAL;;IACAM,IAAI,CAACe,MAAL,CAAY,IAAIC,KAAJ,CAAU,yCAAV,CAAZ;EACD,CAHD,CAXoD,CAgBpD;EACA;;;EACAiB,MAAM,CAACC,kBAAP,GAA4B,YAAW;IACrCxC,KAAK,CAAC,oBAAD,EAAuBuC,MAAM,CAACa,UAA9B,CAAL;;IACA,IAAI,gBAAgBC,IAAhB,CAAqBd,MAAM,CAACa,UAA5B,CAAJ,EAA6C;MAC3C,IAAIb,MAAM,IAAIA,MAAM,CAACe,OAAjB,IAA4Bf,MAAM,CAACI,OAAvC,EAAgD;QAC9CrC,IAAI,CAACwC,UAAL,GAAkB,IAAlB;;QACA,IAAI;UACF;UACAP,MAAM,CAACI,OAAP;QACD,CAHD,CAGE,OAAOY,CAAP,EAAU,CACV;QACD;MACF;;MACD,IAAIhB,MAAJ,EAAY;QACVjC,IAAI,CAACe,MAAL,CAAY,IAAIC,KAAJ,CAAU,qDAAV,CAAZ;MACD;IACF;EACF,CAhBD,CAlBoD,CAmCpD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAI,OAAOiB,MAAM,CAACiB,KAAd,KAAwB,WAAxB,IAAuCzC,MAAM,CAACgC,QAAP,CAAgBU,WAA3D,EAAwE;IACtE;IACA;IACA;IACA,IAAI,CAAC7D,OAAO,CAAC8D,OAAR,EAAL,EAAwB;MACtB;MACA,IAAI;QACFnB,MAAM,CAACe,OAAP,GAAiBf,MAAM,CAAC9B,EAAxB;QACA8B,MAAM,CAACoB,KAAP,GAAe,SAAf;MACD,CAHD,CAGE,OAAOJ,CAAP,EAAU,CACV;MACD;;MACDhB,MAAM,CAACiB,KAAP,GAAe,IAAf;IACD,CATD,MASO;MACL;MACApB,OAAO,GAAG,KAAKA,OAAL,GAAerB,MAAM,CAACgC,QAAP,CAAgBC,aAAhB,CAA8B,QAA9B,CAAzB;MACAZ,OAAO,CAACwB,IAAR,GAAe,0CAA0CrB,MAAM,CAAC9B,EAAjD,GAAsD,mCAArE;MACA8B,MAAM,CAACiB,KAAP,GAAepB,OAAO,CAACoB,KAAR,GAAgB,KAA/B;IACD;EACF;;EACD,IAAI,OAAOjB,MAAM,CAACiB,KAAd,KAAwB,WAA5B,EAAyC;IACvCjB,MAAM,CAACiB,KAAP,GAAe,IAAf;EACD;;EAED,IAAIK,IAAI,GAAG9C,MAAM,CAACgC,QAAP,CAAgBe,oBAAhB,CAAqC,MAArC,EAA6C,CAA7C,CAAX;EACAD,IAAI,CAACE,YAAL,CAAkBxB,MAAlB,EAA0BsB,IAAI,CAACG,UAA/B;;EACA,IAAI5B,OAAJ,EAAa;IACXyB,IAAI,CAACE,YAAL,CAAkB3B,OAAlB,EAA2ByB,IAAI,CAACG,UAAhC;EACD;AACF,CA1ED;;AA4EAC,MAAM,CAACC,OAAP,GAAiB9D,aAAjB"}, "metadata": {}, "sourceType": "script"}