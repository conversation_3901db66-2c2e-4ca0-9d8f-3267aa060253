{"ast": null, "code": "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useLocalStorageState = createUseStorageState(function () {\n  return isBrowser ? localStorage : undefined;\n});\nexport default useLocalStorageState;", "map": {"version": 3, "names": ["createUseStorageState", "<PERSON><PERSON><PERSON><PERSON>", "useLocalStorageState", "localStorage", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useLocalStorageState/index.js"], "sourcesContent": ["import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useLocalStorageState = createUseStorageState(function () {\n  return isBrowser ? localStorage : undefined;\n});\nexport default useLocalStorageState;"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,0BAAtC;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,IAAIC,oBAAoB,GAAGF,qBAAqB,CAAC,YAAY;EAC3D,OAAOC,SAAS,GAAGE,YAAH,GAAkBC,SAAlC;AACD,CAF+C,CAAhD;AAGA,eAAeF,oBAAf"}, "metadata": {}, "sourceType": "module"}