{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import ArrayScroll from'./elements/ArrayScroll';import{getCellFace,isValidSource,getHexColor}from'../utils/Util.js';/**\r\n * 事案コンテンツ<br>\r\n * propsは、「3.13事案コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Case\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var Case=function Case(props){var row1Cell1Props,row1Cell2Props,row1Cell3Props,row1Props;var row2Cell1Props,row2Cell2Props;var row3Cell1Props,row3Cell2Props;var row5Cell1Props;var row6Cell1Props,row6Props;var Max_Cars=12;var Max_Hospitals=1;if(isValidSource(props)){var obj=props.disaster_class;row1Props={backgroundColor:getHexColor(obj.background_color)};row1Cell1Props=getCellFace(obj.disaster_type,'col-span-4');row1Cell2Props=getCellFace(obj.case_no,'col-span-7 col-start-6');row1Cell3Props=getCellFace(obj.fire_station_name,'col-span-4 col-start-14');row2Cell1Props=getCellFace(props.town_name,'col-span-6 w-fit');row2Cell2Props=getCellFace(props.target_name,'col-span-9 col-start-8 w-fit');row3Cell1Props=getCellFace(props.awareness_time,'col-span-8 col-start-2 w-fit');row3Cell2Props=getCellFace(props.command_time,'col-span-8 col-start-12 w-fit');row5Cell1Props=getCellFace(props.latest_dynamic_state_time,'col-span-15 col-start-2 text-center');row6Cell1Props=getCellFace(props.disaster_dynamic_state,'col-span-4 col-start-6 text-center');row6Props={backgroundColor:getHexColor(props.disaster_dynamic_state.background_color)};// /**\n//  * 病院があるときに、\n//  * 表示位置：No.10の「直近動態時刻」\n//   最大文字数：15文字\n//   文字色と背景色：「直近動態時刻」と同じ\n//  */\n// props.transport_hospital?.display_data.forEach((element) => {\n//   element.text_color = props.latest_dynamic_state_time.text_color;\n//   element.background_color =\n//     props.latest_dynamic_state_time.background_color;\n// });\n// 病院があるときに、病院の色を表示するように仕様変更 20230118\n}return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&/*#__PURE__*/_jsxs(\"div\",{className:\"leading-[1] text-6xl\",children:[/*#__PURE__*/_jsxs(\"div\",{style:row1Props,className:\"border-transparent border-x-[1rem] grid grid-cols-[repeat(4,7rem)_minmax(0.25rem,1fr)_repeat(7,7rem)_minmax(0.25rem,1fr)_repeat(4,7rem)] text-7xl\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell2Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row1Cell3Props))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-16 text-7xl mt-[3.1rem]\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row2Cell2Props))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-[minmax(3.5rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(3.5rem,2fr)] text-6xl auto-cols-fr my-[2.9rem]\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},row3Cell1Props)),/*#__PURE__*/_jsx(Cell,_objectSpread({},row3Cell2Props))]}),/*#__PURE__*/_jsx(ArrayScroll,{max:Max_Cars,gridLevelProps:'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr text-6xl leading-[1] gap-y-[1.2rem] gap-x-[1.2rem]',cellLevelProps:['col-span-4 col-start-2','col-span-4','col-span-4','col-span-4'],display_data:props.car_name.display_data,change_setting:props.car_name.change_setting}),!props.transport_hospital&&/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] leading-[1] my-[1.9rem]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},row5Cell1Props))}),props.transport_hospital&&/*#__PURE__*/_jsx(\"div\",{className:\"leading-[1.5]\",children:/*#__PURE__*/_jsx(ArrayScroll,{max:Max_Hospitals,gridLevelProps:'grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] text-6xl leading-[1] my-[1.9rem]',cellLevelProps:['col-span-15 col-start-2 w-fit'],display_data:props.transport_hospital.display_data,change_setting:props.transport_hospital.change_setting})}),/*#__PURE__*/_jsx(\"div\",{style:row6Props,className:\"grid grid-cols-14 text-8xl auto-cols-fr leading-[1]\",children:/*#__PURE__*/_jsx(Cell,_objectSpread({},row6Cell1Props))})]})});};export default Case;", "map": {"version": 3, "names": ["React", "Cell", "ArrayScroll", "getCellFace", "isValidSource", "getHexColor", "Case", "props", "row1Cell1Props", "row1Cell2Props", "row1Cell3Props", "row1Props", "row2Cell1Props", "row2Cell2Props", "row3Cell1Props", "row3Cell2Props", "row5Cell1Props", "row6Cell1Props", "row6Props", "Max_Cars", "Max_Hospitals", "obj", "disaster_class", "backgroundColor", "background_color", "disaster_type", "case_no", "fire_station_name", "town_name", "target_name", "awareness_time", "command_time", "latest_dynamic_state_time", "disaster_dynamic_state", "car_name", "display_data", "change_setting", "transport_hospital"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Case.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport ArrayScroll from './elements/ArrayScroll';\r\nimport { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';\r\n\r\n/**\r\n * 事案コンテンツ<br>\r\n * propsは、「3.13事案コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Case\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Case = (props) => {\r\n  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;\r\n  let row2Cell1Props, row2Cell2Props;\r\n  let row3Cell1Props, row3Cell2Props;\r\n  let row5Cell1Props;\r\n  let row6Cell1Props, row6Props;\r\n  const Max_Cars = 12;\r\n  const Max_Hospitals = 1;\r\n\r\n  if (isValidSource(props)) {\r\n    let obj = props.disaster_class;\r\n\r\n    row1Props = { backgroundColor: getHexColor(obj.background_color) };\r\n    row1Cell1Props = getCellFace(obj.disaster_type, 'col-span-4');\r\n    row1Cell2Props = getCellFace(obj.case_no, 'col-span-7 col-start-6');\r\n    row1Cell3Props = getCellFace(\r\n      obj.fire_station_name,\r\n      'col-span-4 col-start-14'\r\n    );\r\n\r\n    row2Cell1Props = getCellFace(props.town_name, 'col-span-6 w-fit');\r\n    row2Cell2Props = getCellFace(props.target_name, 'col-span-9 col-start-8 w-fit');\r\n\r\n    row3Cell1Props = getCellFace(\r\n      props.awareness_time,\r\n      'col-span-8 col-start-2 w-fit'\r\n    );\r\n    row3Cell2Props = getCellFace(props.command_time, 'col-span-8 col-start-12 w-fit');\r\n\r\n    row5Cell1Props = getCellFace(\r\n      props.latest_dynamic_state_time,\r\n      'col-span-15 col-start-2 text-center'\r\n    );\r\n\r\n    row6Cell1Props = getCellFace(\r\n      props.disaster_dynamic_state,\r\n      'col-span-4 col-start-6 text-center'\r\n    );\r\n    row6Props = {\r\n      backgroundColor: getHexColor(\r\n        props.disaster_dynamic_state.background_color\r\n      ),\r\n    };\r\n\r\n    // /**\r\n    //  * 病院があるときに、\r\n    //  * 表示位置：No.10の「直近動態時刻」\r\n    //   最大文字数：15文字\r\n    //   文字色と背景色：「直近動態時刻」と同じ\r\n    //  */\r\n    // props.transport_hospital?.display_data.forEach((element) => {\r\n    //   element.text_color = props.latest_dynamic_state_time.text_color;\r\n    //   element.background_color =\r\n    //     props.latest_dynamic_state_time.background_color;\r\n    // });\r\n    // 病院があるときに、病院の色を表示するように仕様変更 20230118\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {isValidSource(props) && (\r\n        <div className=\"leading-[1] text-6xl\">\r\n          <div\r\n            style={row1Props}\r\n            className=\"border-transparent border-x-[1rem] grid grid-cols-[repeat(4,7rem)_minmax(0.25rem,1fr)_repeat(7,7rem)_minmax(0.25rem,1fr)_repeat(4,7rem)] text-7xl\"\r\n          >\r\n            <Cell {...row1Cell1Props} />\r\n            <Cell {...row1Cell2Props} />\r\n            <Cell {...row1Cell3Props} />\r\n          </div>\r\n          <div className=\"border-transparent border-x-[1rem] grid grid-cols-16 text-7xl mt-[3.1rem]\">\r\n            <Cell {...row2Cell1Props} />\r\n            <Cell {...row2Cell2Props} />\r\n          </div>\r\n          <div className=\"grid grid-cols-[minmax(3.5rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(3.5rem,2fr)] text-6xl auto-cols-fr my-[2.9rem]\">\r\n            <Cell {...row3Cell1Props} />\r\n            <Cell {...row3Cell2Props} />\r\n          </div>\r\n\r\n          <ArrayScroll\r\n            max={Max_Cars}\r\n            gridLevelProps={\r\n              'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr text-6xl leading-[1] gap-y-[1.2rem] gap-x-[1.2rem]'\r\n            }\r\n            cellLevelProps={[\r\n              'col-span-4 col-start-2',\r\n              'col-span-4',\r\n              'col-span-4',\r\n              'col-span-4',\r\n            ]}\r\n            display_data={props.car_name.display_data}\r\n            change_setting={props.car_name.change_setting}\r\n          />\r\n          {!props.transport_hospital && (\r\n            <div className=\"grid grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] leading-[1] my-[1.9rem]\">\r\n              <Cell {...row5Cell1Props} />\r\n            </div>\r\n          )}\r\n          {props.transport_hospital && (\r\n            <div className=\"leading-[1.5]\">\r\n              <ArrayScroll\r\n                max={Max_Hospitals}\r\n                gridLevelProps={\r\n                  'grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] text-6xl leading-[1] my-[1.9rem]'\r\n                }\r\n                cellLevelProps={['col-span-15 col-start-2 w-fit']}\r\n                display_data={props.transport_hospital.display_data}\r\n                change_setting={props.transport_hospital.change_setting}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div\r\n            style={row6Props}\r\n            className=\"grid grid-cols-14 text-8xl auto-cols-fr leading-[1]\"\r\n          >\r\n            <Cell {...row6Cell1Props} />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Case;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,YAAP,KAAwB,wBAAxB,CACA,OAASC,WAAT,CAAsBC,aAAtB,CAAqCC,WAArC,KAAwD,kBAAxD,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,KAAI,CAAG,QAAPA,KAAO,CAACC,KAAD,CAAW,CACtB,GAAIC,eAAJ,CAAoBC,cAApB,CAAoCC,cAApC,CAAoDC,SAApD,CACA,GAAIC,eAAJ,CAAoBC,cAApB,CACA,GAAIC,eAAJ,CAAoBC,cAApB,CACA,GAAIC,eAAJ,CACA,GAAIC,eAAJ,CAAoBC,SAApB,CACA,GAAMC,SAAQ,CAAG,EAAjB,CACA,GAAMC,cAAa,CAAG,CAAtB,CAEA,GAAIhB,aAAa,CAACG,KAAD,CAAjB,CAA0B,CACxB,GAAIc,IAAG,CAAGd,KAAK,CAACe,cAAhB,CAEAX,SAAS,CAAG,CAAEY,eAAe,CAAElB,WAAW,CAACgB,GAAG,CAACG,gBAAL,CAA9B,CAAZ,CACAhB,cAAc,CAAGL,WAAW,CAACkB,GAAG,CAACI,aAAL,CAAoB,YAApB,CAA5B,CACAhB,cAAc,CAAGN,WAAW,CAACkB,GAAG,CAACK,OAAL,CAAc,wBAAd,CAA5B,CACAhB,cAAc,CAAGP,WAAW,CAC1BkB,GAAG,CAACM,iBADsB,CAE1B,yBAF0B,CAA5B,CAKAf,cAAc,CAAGT,WAAW,CAACI,KAAK,CAACqB,SAAP,CAAkB,kBAAlB,CAA5B,CACAf,cAAc,CAAGV,WAAW,CAACI,KAAK,CAACsB,WAAP,CAAoB,8BAApB,CAA5B,CAEAf,cAAc,CAAGX,WAAW,CAC1BI,KAAK,CAACuB,cADoB,CAE1B,8BAF0B,CAA5B,CAIAf,cAAc,CAAGZ,WAAW,CAACI,KAAK,CAACwB,YAAP,CAAqB,+BAArB,CAA5B,CAEAf,cAAc,CAAGb,WAAW,CAC1BI,KAAK,CAACyB,yBADoB,CAE1B,qCAF0B,CAA5B,CAKAf,cAAc,CAAGd,WAAW,CAC1BI,KAAK,CAAC0B,sBADoB,CAE1B,oCAF0B,CAA5B,CAIAf,SAAS,CAAG,CACVK,eAAe,CAAElB,WAAW,CAC1BE,KAAK,CAAC0B,sBAAN,CAA6BT,gBADH,CADlB,CAAZ,CAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACD,CAED,mBACE,yBACGpB,aAAa,CAACG,KAAD,CAAb,eACC,aAAK,SAAS,CAAC,sBAAf,wBACE,aACE,KAAK,CAAEI,SADT,CAEE,SAAS,CAAC,mJAFZ,wBAIE,KAAC,IAAD,kBAAUH,cAAV,EAJF,cAKE,KAAC,IAAD,kBAAUC,cAAV,EALF,cAME,KAAC,IAAD,kBAAUC,cAAV,EANF,GADF,cASE,aAAK,SAAS,CAAC,2EAAf,wBACE,KAAC,IAAD,kBAAUE,cAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,cAAV,EAFF,GATF,cAaE,aAAK,SAAS,CAAC,gIAAf,wBACE,KAAC,IAAD,kBAAUC,cAAV,EADF,cAEE,KAAC,IAAD,kBAAUC,cAAV,EAFF,GAbF,cAkBE,KAAC,WAAD,EACE,GAAG,CAAEI,QADP,CAEE,cAAc,CACZ,iLAHJ,CAKE,cAAc,CAAE,CACd,wBADc,CAEd,YAFc,CAGd,YAHc,CAId,YAJc,CALlB,CAWE,YAAY,CAAEZ,KAAK,CAAC2B,QAAN,CAAeC,YAX/B,CAYE,cAAc,CAAE5B,KAAK,CAAC2B,QAAN,CAAeE,cAZjC,EAlBF,CAgCG,CAAC7B,KAAK,CAAC8B,kBAAP,eACC,YAAK,SAAS,CAAC,kGAAf,uBACE,KAAC,IAAD,kBAAUrB,cAAV,EADF,EAjCJ,CAqCGT,KAAK,CAAC8B,kBAAN,eACC,YAAK,SAAS,CAAC,eAAf,uBACE,KAAC,WAAD,EACE,GAAG,CAAEjB,aADP,CAEE,cAAc,CACZ,sGAHJ,CAKE,cAAc,CAAE,CAAC,+BAAD,CALlB,CAME,YAAY,CAAEb,KAAK,CAAC8B,kBAAN,CAAyBF,YANzC,CAOE,cAAc,CAAE5B,KAAK,CAAC8B,kBAAN,CAAyBD,cAP3C,EADF,EAtCJ,cAmDE,YACE,KAAK,CAAElB,SADT,CAEE,SAAS,CAAC,qDAFZ,uBAIE,KAAC,IAAD,kBAAUD,cAAV,EAJF,EAnDF,GAFJ,EADF,CAgED,CA1HD,CA4HA,cAAeX,KAAf"}, "metadata": {}, "sourceType": "module"}