{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    var sourceURLs = cssMapping.sources.map(function (source) {\n      return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || \"\").concat(source, \" */\");\n    });\n    return [content].concat(sourceURLs).concat([sourceMapping]).join(\"\\n\");\n  }\n\n  return [content].join(\"\\n\");\n};", "map": {"version": 3, "names": ["module", "exports", "item", "content", "cssMapping", "btoa", "base64", "unescape", "encodeURIComponent", "JSON", "stringify", "data", "concat", "sourceMapping", "sourceURLs", "sources", "map", "source", "sourceRoot", "join"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/css-loader/dist/runtime/sourceMaps.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (item) {\n  var content = item[1];\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (typeof btoa === \"function\") {\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(cssMapping))));\n    var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n    var sourceMapping = \"/*# \".concat(data, \" */\");\n    var sourceURLs = cssMapping.sources.map(function (source) {\n      return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || \"\").concat(source, \" */\");\n    });\n    return [content].concat(sourceURLs).concat([sourceMapping]).join(\"\\n\");\n  }\n\n  return [content].join(\"\\n\");\n};"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiB,UAAUC,IAAV,EAAgB;EAC/B,IAAIC,OAAO,GAAGD,IAAI,CAAC,CAAD,CAAlB;EACA,IAAIE,UAAU,GAAGF,IAAI,CAAC,CAAD,CAArB;;EAEA,IAAI,CAACE,UAAL,EAAiB;IACf,OAAOD,OAAP;EACD;;EAED,IAAI,OAAOE,IAAP,KAAgB,UAApB,EAAgC;IAC9B,IAAIC,MAAM,GAAGD,IAAI,CAACE,QAAQ,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAL,CAAeN,UAAf,CAAD,CAAnB,CAAT,CAAjB;IACA,IAAIO,IAAI,GAAG,+DAA+DC,MAA/D,CAAsEN,MAAtE,CAAX;IACA,IAAIO,aAAa,GAAG,OAAOD,MAAP,CAAcD,IAAd,EAAoB,KAApB,CAApB;IACA,IAAIG,UAAU,GAAGV,UAAU,CAACW,OAAX,CAAmBC,GAAnB,CAAuB,UAAUC,MAAV,EAAkB;MACxD,OAAO,iBAAiBL,MAAjB,CAAwBR,UAAU,CAACc,UAAX,IAAyB,EAAjD,EAAqDN,MAArD,CAA4DK,MAA5D,EAAoE,KAApE,CAAP;IACD,CAFgB,CAAjB;IAGA,OAAO,CAACd,OAAD,EAAUS,MAAV,CAAiBE,UAAjB,EAA6BF,MAA7B,CAAoC,CAACC,aAAD,CAApC,EAAqDM,IAArD,CAA0D,IAA1D,CAAP;EACD;;EAED,OAAO,CAAChB,OAAD,EAAUgB,IAAV,CAAe,IAAf,CAAP;AACD,CAnBD"}, "metadata": {}, "sourceType": "script"}