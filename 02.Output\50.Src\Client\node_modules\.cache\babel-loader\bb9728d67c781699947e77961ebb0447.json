{"ast": null, "code": "import React from'react';import Home from'./pages/Home';import{Routes,Route}from'react-router-dom';/**\r\n * WebAppの入り口\r\n * @module App\r\n *\r\n * @return {*} Routeを作成してAppを返す\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(\"header\",{className:\"App-header\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(NotFound,{})})]})})});}/**\r\n * 404 Pageを作成して返す\r\n * @returns 404 Page\r\n */var NotFound=function NotFound(){var msg=\"ServerUrl\".concat(process.env.REACT_APP_APP_URL,\"?display_no=xx&display_split_no=xx \\u306E\\u3088\\u3046\\u306A\\u30A2\\u30C9\\u30EC\\u30B9\\u3067\\u3001\\u8868\\u793A\\u76E4ID\\u3068\\u30E2\\u30CB\\u30BF\\u30FCID\\u3092\\u63D0\\u4F9B\\u3057\\u3066\\u3001\\u30A2\\u30AF\\u30BB\\u30B9\\u3057\\u3066\\u304F\\u3060\\u3055\\u3044\\u3002\");return/*#__PURE__*/_jsx(\"div\",{className:\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center text-green-400\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"m-10 text-4xl\",children:\"\\u5B58\\u5728\\u3057\\u306A\\u3044\\u30DA\\u30FC\\u30B8\\u3067\\u3059\\u3002\"}),/*#__PURE__*/_jsx(\"div\",{className:\"m-10 text-2xl\",children:msg})]})});};export default App;", "map": {"version": 3, "names": ["React", "Home", "Routes", "Route", "App", "NotFound", "msg", "process", "env", "REACT_APP_APP_URL"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport Home from './pages/Home';\r\n\r\nimport { Routes, Route } from 'react-router-dom';\r\n/**\r\n * WebAppの入り口\r\n * @module App\r\n *\r\n * @return {*} Routeを作成してAppを返す\r\n */\r\nfunction App() {\r\n  return (\r\n    <div className=\"App\">\r\n      <header className=\"App-header\">\r\n        <Routes>\r\n          <Route index element={<Home />} />\r\n          <Route path=\"/\" element={<Home />}></Route>\r\n          <Route path=\"*\" element={<NotFound />} />\r\n        </Routes>\r\n      </header>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * 404 Pageを作成して返す\r\n * @returns 404 Page\r\n */\r\nconst NotFound = () => {\r\n  const msg = `ServerUrl${process.env.REACT_APP_APP_URL}?display_no=xx&display_split_no=xx のようなアドレスで、表示盤IDとモニターIDを提供して、アクセスしてください。`;\r\n\r\n  return (\r\n    <div className=\"bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center\">\r\n      <div className=\"flex flex-col items-center text-green-400\">\r\n        <div className=\"m-10 text-4xl\">存在しないページです。</div>\r\n        <div className=\"m-10 text-2xl\">{msg}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,cAAjB,CAEA,OAASC,MAAT,CAAiBC,KAAjB,KAA8B,kBAA9B,CACA;AACA;AACA;AACA;AACA;AACA,G,wFACA,QAASC,IAAT,EAAe,CACb,mBACE,YAAK,SAAS,CAAC,KAAf,uBACE,eAAQ,SAAS,CAAC,YAAlB,uBACE,MAAC,MAAD,yBACE,KAAC,KAAD,EAAO,KAAK,KAAZ,CAAa,OAAO,cAAE,KAAC,IAAD,IAAtB,EADF,cAEE,KAAC,KAAD,EAAO,IAAI,CAAC,GAAZ,CAAgB,OAAO,cAAE,KAAC,IAAD,IAAzB,EAFF,cAGE,KAAC,KAAD,EAAO,IAAI,CAAC,GAAZ,CAAgB,OAAO,cAAE,KAAC,QAAD,IAAzB,EAHF,GADF,EADF,EADF,CAWD,CAED;AACA;AACA;AACA,GACA,GAAMC,SAAQ,CAAG,QAAXA,SAAW,EAAM,CACrB,GAAMC,IAAG,oBAAeC,OAAO,CAACC,GAAR,CAAYC,iBAA3B,6PAAT,CAEA,mBACE,YAAK,SAAS,CAAC,yEAAf,uBACE,aAAK,SAAS,CAAC,2CAAf,wBACE,YAAK,SAAS,CAAC,eAAf,gFADF,cAEE,YAAK,SAAS,CAAC,eAAf,UAAgCH,GAAhC,EAFF,GADF,EADF,CAQD,CAXD,CAaA,cAAeF,IAAf"}, "metadata": {}, "sourceType": "module"}