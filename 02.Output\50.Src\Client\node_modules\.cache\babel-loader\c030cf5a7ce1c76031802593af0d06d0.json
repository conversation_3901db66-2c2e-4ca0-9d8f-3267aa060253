{"ast": null, "code": "/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n\n  return result;\n}\n\nmodule.exports = baseTimes;", "map": {"version": 3, "names": ["baseTimes", "n", "iteratee", "index", "result", "Array", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseTimes.js"], "sourcesContent": ["/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\nmodule.exports = baseTimes;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,CAAnB,EAAsBC,QAAtB,EAAgC;EAC9B,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGC,KAAK,CAACJ,CAAD,CADlB;;EAGA,OAAO,EAAEE,KAAF,GAAUF,CAAjB,EAAoB;IAClBG,MAAM,CAACD,KAAD,CAAN,GAAgBD,QAAQ,CAACC,KAAD,CAAxB;EACD;;EACD,OAAOC,MAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBP,SAAjB"}, "metadata": {}, "sourceType": "script"}