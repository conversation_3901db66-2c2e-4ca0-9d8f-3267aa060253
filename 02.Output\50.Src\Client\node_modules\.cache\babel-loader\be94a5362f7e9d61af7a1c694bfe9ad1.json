{"ast": null, "code": "var mapCacheClear = require('./_mapCacheClear'),\n    mapCacheDelete = require('./_mapCacheDelete'),\n    mapCacheGet = require('./_mapCacheGet'),\n    mapCacheHas = require('./_mapCacheHas'),\n    mapCacheSet = require('./_mapCacheSet');\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\n\n\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n  this.clear();\n\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n} // Add methods to `MapCache`.\n\n\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\nmodule.exports = MapCache;", "map": {"version": 3, "names": ["mapCacheClear", "require", "mapCacheDelete", "mapCacheGet", "mapCacheHas", "mapCacheSet", "MapCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_MapCache.js"], "sourcesContent": ["var mapCacheClear = require('./_mapCacheClear'),\n    mapCacheDelete = require('./_mapCacheDelete'),\n    mapCacheGet = require('./_mapCacheGet'),\n    mapCacheHas = require('./_mapCacheHas'),\n    mapCacheSet = require('./_mapCacheSet');\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\nmodule.exports = MapCache;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAD,CAA3B;AAAA,IACIC,cAAc,GAAGD,OAAO,CAAC,mBAAD,CAD5B;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAAA,IAGIG,WAAW,GAAGH,OAAO,CAAC,gBAAD,CAHzB;AAAA,IAIII,WAAW,GAAGJ,OAAO,CAAC,gBAAD,CAJzB;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,QAAT,CAAkBC,OAAlB,EAA2B;EACzB,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGF,OAAO,IAAI,IAAX,GAAkB,CAAlB,GAAsBA,OAAO,CAACE,MAD3C;EAGA,KAAKC,KAAL;;EACA,OAAO,EAAEF,KAAF,GAAUC,MAAjB,EAAyB;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAD,CAAnB;IACA,KAAKI,GAAL,CAASD,KAAK,CAAC,CAAD,CAAd,EAAmBA,KAAK,CAAC,CAAD,CAAxB;EACD;AACF,C,CAED;;;AACAL,QAAQ,CAACO,SAAT,CAAmBH,KAAnB,GAA2BV,aAA3B;AACAM,QAAQ,CAACO,SAAT,CAAmB,QAAnB,IAA+BX,cAA/B;AACAI,QAAQ,CAACO,SAAT,CAAmBC,GAAnB,GAAyBX,WAAzB;AACAG,QAAQ,CAACO,SAAT,CAAmBE,GAAnB,GAAyBX,WAAzB;AACAE,QAAQ,CAACO,SAAT,CAAmBD,GAAnB,GAAyBP,WAAzB;AAEAW,MAAM,CAACC,OAAP,GAAiBX,QAAjB"}, "metadata": {}, "sourceType": "script"}