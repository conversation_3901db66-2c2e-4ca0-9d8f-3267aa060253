{"ast": null, "code": "/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\nmodule.exports = stackGet;", "map": {"version": 3, "names": ["stackGet", "key", "__data__", "get", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_stackGet.js"], "sourcesContent": ["/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\nmodule.exports = stackGet;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAT,CAAkBC,GAAlB,EAAuB;EACrB,OAAO,KAAKC,QAAL,CAAcC,GAAd,CAAkBF,GAAlB,CAAP;AACD;;AAEDG,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}