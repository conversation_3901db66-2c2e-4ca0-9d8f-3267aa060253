{"ast": null, "code": "var getAllKeys = require('./_getAllKeys');\n/** Used to compose bitmasks for value comparisons. */\n\n\nvar COMPARE_PARTIAL_FLAG = 1;\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\n\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n\n  var index = objLength;\n\n  while (index--) {\n    var key = objProps[index];\n\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  } // Check that cyclic values are equal.\n\n\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n  var skipCtor = isPartial;\n\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n    } // Recursively compare objects (susceptible to call stack limits).\n\n\n    if (!(compared === undefined ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n      result = false;\n      break;\n    }\n\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor; // Non `Object` object instances with different constructors are not equal.\n\n    if (objCtor != othCtor && 'constructor' in object && 'constructor' in other && !(typeof objCtor == 'function' && objCtor instanceof objCtor && typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalObjects;", "map": {"version": 3, "names": ["getAllKeys", "require", "COMPARE_PARTIAL_FLAG", "objectProto", "Object", "prototype", "hasOwnProperty", "equalObjects", "object", "other", "bitmask", "customizer", "equalFunc", "stack", "isPartial", "objProps", "obj<PERSON><PERSON><PERSON>", "length", "othProps", "oth<PERSON><PERSON><PERSON>", "index", "key", "call", "objStacked", "get", "othStacked", "result", "set", "skip<PERSON><PERSON>", "objValue", "othValue", "compared", "undefined", "objCtor", "constructor", "othCtor", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_equalObjects.js"], "sourcesContent": ["var getAllKeys = require('./_getAllKeys');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  }\n  // Check that cyclic values are equal.\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, objValue, key, other, object, stack)\n        : customizer(objValue, othValue, key, object, other, stack);\n    }\n    // Recursively compare objects (susceptible to call stack limits).\n    if (!(compared === undefined\n          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))\n          : compared\n        )) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor;\n\n    // Non `Object` object instances with different constructors are not equal.\n    if (objCtor != othCtor &&\n        ('constructor' in object && 'constructor' in other) &&\n        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&\n          typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalObjects;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;;;AACA,IAAIC,oBAAoB,GAAG,CAA3B;AAEA;;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,YAAT,CAAsBC,MAAtB,EAA8BC,KAA9B,EAAqCC,OAArC,EAA8CC,UAA9C,EAA0DC,SAA1D,EAAqEC,KAArE,EAA4E;EAC1E,IAAIC,SAAS,GAAGJ,OAAO,GAAGR,oBAA1B;EAAA,IACIa,QAAQ,GAAGf,UAAU,CAACQ,MAAD,CADzB;EAAA,IAEIQ,SAAS,GAAGD,QAAQ,CAACE,MAFzB;EAAA,IAGIC,QAAQ,GAAGlB,UAAU,CAACS,KAAD,CAHzB;EAAA,IAIIU,SAAS,GAAGD,QAAQ,CAACD,MAJzB;;EAMA,IAAID,SAAS,IAAIG,SAAb,IAA0B,CAACL,SAA/B,EAA0C;IACxC,OAAO,KAAP;EACD;;EACD,IAAIM,KAAK,GAAGJ,SAAZ;;EACA,OAAOI,KAAK,EAAZ,EAAgB;IACd,IAAIC,GAAG,GAAGN,QAAQ,CAACK,KAAD,CAAlB;;IACA,IAAI,EAAEN,SAAS,GAAGO,GAAG,IAAIZ,KAAV,GAAkBH,cAAc,CAACgB,IAAf,CAAoBb,KAApB,EAA2BY,GAA3B,CAA7B,CAAJ,EAAmE;MACjE,OAAO,KAAP;IACD;EACF,CAhByE,CAiB1E;;;EACA,IAAIE,UAAU,GAAGV,KAAK,CAACW,GAAN,CAAUhB,MAAV,CAAjB;EACA,IAAIiB,UAAU,GAAGZ,KAAK,CAACW,GAAN,CAAUf,KAAV,CAAjB;;EACA,IAAIc,UAAU,IAAIE,UAAlB,EAA8B;IAC5B,OAAOF,UAAU,IAAId,KAAd,IAAuBgB,UAAU,IAAIjB,MAA5C;EACD;;EACD,IAAIkB,MAAM,GAAG,IAAb;EACAb,KAAK,CAACc,GAAN,CAAUnB,MAAV,EAAkBC,KAAlB;EACAI,KAAK,CAACc,GAAN,CAAUlB,KAAV,EAAiBD,MAAjB;EAEA,IAAIoB,QAAQ,GAAGd,SAAf;;EACA,OAAO,EAAEM,KAAF,GAAUJ,SAAjB,EAA4B;IAC1BK,GAAG,GAAGN,QAAQ,CAACK,KAAD,CAAd;IACA,IAAIS,QAAQ,GAAGrB,MAAM,CAACa,GAAD,CAArB;IAAA,IACIS,QAAQ,GAAGrB,KAAK,CAACY,GAAD,CADpB;;IAGA,IAAIV,UAAJ,EAAgB;MACd,IAAIoB,QAAQ,GAAGjB,SAAS,GACpBH,UAAU,CAACmB,QAAD,EAAWD,QAAX,EAAqBR,GAArB,EAA0BZ,KAA1B,EAAiCD,MAAjC,EAAyCK,KAAzC,CADU,GAEpBF,UAAU,CAACkB,QAAD,EAAWC,QAAX,EAAqBT,GAArB,EAA0Bb,MAA1B,EAAkCC,KAAlC,EAAyCI,KAAzC,CAFd;IAGD,CATyB,CAU1B;;;IACA,IAAI,EAAEkB,QAAQ,KAAKC,SAAb,GACGH,QAAQ,KAAKC,QAAb,IAAyBlB,SAAS,CAACiB,QAAD,EAAWC,QAAX,EAAqBpB,OAArB,EAA8BC,UAA9B,EAA0CE,KAA1C,CADrC,GAEEkB,QAFJ,CAAJ,EAGO;MACLL,MAAM,GAAG,KAAT;MACA;IACD;;IACDE,QAAQ,KAAKA,QAAQ,GAAGP,GAAG,IAAI,aAAvB,CAAR;EACD;;EACD,IAAIK,MAAM,IAAI,CAACE,QAAf,EAAyB;IACvB,IAAIK,OAAO,GAAGzB,MAAM,CAAC0B,WAArB;IAAA,IACIC,OAAO,GAAG1B,KAAK,CAACyB,WADpB,CADuB,CAIvB;;IACA,IAAID,OAAO,IAAIE,OAAX,IACC,iBAAiB3B,MAAjB,IAA2B,iBAAiBC,KAD7C,IAEA,EAAE,OAAOwB,OAAP,IAAkB,UAAlB,IAAgCA,OAAO,YAAYA,OAAnD,IACA,OAAOE,OAAP,IAAkB,UADlB,IACgCA,OAAO,YAAYA,OADrD,CAFJ,EAGmE;MACjET,MAAM,GAAG,KAAT;IACD;EACF;;EACDb,KAAK,CAAC,QAAD,CAAL,CAAgBL,MAAhB;EACAK,KAAK,CAAC,QAAD,CAAL,CAAgBJ,KAAhB;EACA,OAAOiB,MAAP;AACD;;AAEDU,MAAM,CAACC,OAAP,GAAiB9B,YAAjB"}, "metadata": {}, "sourceType": "script"}