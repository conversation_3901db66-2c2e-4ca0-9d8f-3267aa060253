{"ast": null, "code": "import { useEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useEffect);", "map": {"version": 3, "names": ["useEffect", "createUpdateEffect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useUpdateEffect/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useEffect);"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,eAAeA,kBAAkB,CAACD,SAAD,CAAjC"}, "metadata": {}, "sourceType": "module"}