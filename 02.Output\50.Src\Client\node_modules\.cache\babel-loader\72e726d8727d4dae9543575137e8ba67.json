{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter;\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\n\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\n\ninherits(BufferedSender, EventEmitter);\n\nBufferedSender.prototype.send = function (message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n}; // For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\n\n\nBufferedSender.prototype.sendScheduleWait = function () {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n\n  this.sendStop = function () {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n\n  tref = setTimeout(function () {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\n\nBufferedSender.prototype.sendSchedule = function () {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function (err) {\n      self.sendStop = null;\n\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\n\nBufferedSender.prototype._cleanup = function () {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nBufferedSender.prototype.close = function () {\n  debug('close');\n\n  this._cleanup();\n\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\n\nmodule.exports = BufferedSender;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "BufferedSender", "url", "sender", "call", "send<PERSON><PERSON><PERSON>", "prototype", "send", "message", "push", "sendStop", "sendSchedule", "sendScheduleWait", "self", "tref", "clearTimeout", "setTimeout", "length", "payload", "join", "err", "emit", "code", "close", "_cleanup", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/lib/buffered-sender.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\n\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\n\ninherits(BufferedSender, EventEmitter);\n\nBufferedSender.prototype.send = function(message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n};\n\n// For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\nBufferedSender.prototype.sendScheduleWait = function() {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n  this.sendStop = function() {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n  tref = setTimeout(function() {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\n\nBufferedSender.prototype.sendSchedule = function() {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function(err) {\n      self.sendStop = null;\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\n\nBufferedSender.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nBufferedSender.prototype.close = function() {\n  debug('close');\n  this._cleanup();\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\n\nmodule.exports = BufferedSender;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;;AAIA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAD,CAAP,CAAiB,+BAAjB,CAAR;AACD;;AAED,SAASM,cAAT,CAAwBC,GAAxB,EAA6BC,MAA7B,EAAqC;EACnCN,KAAK,CAACK,GAAD,CAAL;EACAN,YAAY,CAACQ,IAAb,CAAkB,IAAlB;EACA,KAAKC,UAAL,GAAkB,EAAlB;EACA,KAAKF,MAAL,GAAcA,MAAd;EACA,KAAKD,GAAL,GAAWA,GAAX;AACD;;AAEDR,QAAQ,CAACO,cAAD,EAAiBL,YAAjB,CAAR;;AAEAK,cAAc,CAACK,SAAf,CAAyBC,IAAzB,GAAgC,UAASC,OAAT,EAAkB;EAChDX,KAAK,CAAC,MAAD,EAASW,OAAT,CAAL;EACA,KAAKH,UAAL,CAAgBI,IAAhB,CAAqBD,OAArB;;EACA,IAAI,CAAC,KAAKE,QAAV,EAAoB;IAClB,KAAKC,YAAL;EACD;AACF,CAND,C,CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAV,cAAc,CAACK,SAAf,CAAyBM,gBAAzB,GAA4C,YAAW;EACrDf,KAAK,CAAC,kBAAD,CAAL;EACA,IAAIgB,IAAI,GAAG,IAAX;EACA,IAAIC,IAAJ;;EACA,KAAKJ,QAAL,GAAgB,YAAW;IACzBb,KAAK,CAAC,UAAD,CAAL;IACAgB,IAAI,CAACH,QAAL,GAAgB,IAAhB;IACAK,YAAY,CAACD,IAAD,CAAZ;EACD,CAJD;;EAKAA,IAAI,GAAGE,UAAU,CAAC,YAAW;IAC3BnB,KAAK,CAAC,SAAD,CAAL;IACAgB,IAAI,CAACH,QAAL,GAAgB,IAAhB;IACAG,IAAI,CAACF,YAAL;EACD,CAJgB,EAId,EAJc,CAAjB;AAKD,CAdD;;AAgBAV,cAAc,CAACK,SAAf,CAAyBK,YAAzB,GAAwC,YAAW;EACjDd,KAAK,CAAC,cAAD,EAAiB,KAAKQ,UAAL,CAAgBY,MAAjC,CAAL;EACA,IAAIJ,IAAI,GAAG,IAAX;;EACA,IAAI,KAAKR,UAAL,CAAgBY,MAAhB,GAAyB,CAA7B,EAAgC;IAC9B,IAAIC,OAAO,GAAG,MAAM,KAAKb,UAAL,CAAgBc,IAAhB,CAAqB,GAArB,CAAN,GAAkC,GAAhD;IACA,KAAKT,QAAL,GAAgB,KAAKP,MAAL,CAAY,KAAKD,GAAjB,EAAsBgB,OAAtB,EAA+B,UAASE,GAAT,EAAc;MAC3DP,IAAI,CAACH,QAAL,GAAgB,IAAhB;;MACA,IAAIU,GAAJ,EAAS;QACPvB,KAAK,CAAC,OAAD,EAAUuB,GAAV,CAAL;QACAP,IAAI,CAACQ,IAAL,CAAU,OAAV,EAAmBD,GAAG,CAACE,IAAJ,IAAY,IAA/B,EAAqC,oBAAoBF,GAAzD;QACAP,IAAI,CAACU,KAAL;MACD,CAJD,MAIO;QACLV,IAAI,CAACD,gBAAL;MACD;IACF,CATe,CAAhB;IAUA,KAAKP,UAAL,GAAkB,EAAlB;EACD;AACF,CAjBD;;AAmBAJ,cAAc,CAACK,SAAf,CAAyBkB,QAAzB,GAAoC,YAAW;EAC7C3B,KAAK,CAAC,UAAD,CAAL;EACA,KAAK4B,kBAAL;AACD,CAHD;;AAKAxB,cAAc,CAACK,SAAf,CAAyBiB,KAAzB,GAAiC,YAAW;EAC1C1B,KAAK,CAAC,OAAD,CAAL;;EACA,KAAK2B,QAAL;;EACA,IAAI,KAAKd,QAAT,EAAmB;IACjB,KAAKA,QAAL;IACA,KAAKA,QAAL,GAAgB,IAAhB;EACD;AACF,CAPD;;AASAgB,MAAM,CAACC,OAAP,GAAiB1B,cAAjB"}, "metadata": {}, "sourceType": "script"}