{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\n\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var min = options.min,\n      max = options.max;\n  var target = val;\n\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n\n  return target;\n}\n\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n\n  if (options === void 0) {\n    options = {};\n  }\n\n  var min = options.min,\n      max = options.max;\n\n  var _a = __read(useState(function () {\n    return getTargetValue(initialValue, {\n      min: min,\n      max: max\n    });\n  }), 2),\n      current = _a[0],\n      setCurrent = _a[1];\n\n  var setValue = function setValue(value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n\n  var inc = function inc(delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n\n  var dec = function dec(delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n\n  var set = function set(value) {\n    setValue(value);\n  };\n\n  var reset = function reset() {\n    setValue(initialValue);\n  };\n\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\n\nexport default useCounter;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useMemoizedFn", "isNumber", "getTargetValue", "val", "options", "min", "max", "target", "Math", "useCounter", "initialValue", "_a", "current", "setCurrent", "setValue", "c", "inc", "delta", "dec", "set", "reset"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useCounter/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var target = val;\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n  return target;\n}\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var _a = __read(useState(function () {\n      return getTargetValue(initialValue, {\n        min: min,\n        max: max\n      });\n    }), 2),\n    current = _a[0],\n    setCurrent = _a[1];\n  var setValue = function setValue(value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n  var inc = function inc(delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n  var dec = function dec(delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n  var set = function set(value) {\n    setValue(value);\n  };\n  var reset = function reset() {\n    setValue(initialValue);\n  };\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useCounter;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,SAASC,cAAT,CAAwBC,GAAxB,EAA6BC,OAA7B,EAAsC;EACpC,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,GAAG,GAAGD,OAAO,CAACC,GAAlB;EAAA,IACEC,GAAG,GAAGF,OAAO,CAACE,GADhB;EAEA,IAAIC,MAAM,GAAGJ,GAAb;;EACA,IAAIF,QAAQ,CAACK,GAAD,CAAZ,EAAmB;IACjBC,MAAM,GAAGC,IAAI,CAACH,GAAL,CAASC,GAAT,EAAcC,MAAd,CAAT;EACD;;EACD,IAAIN,QAAQ,CAACI,GAAD,CAAZ,EAAmB;IACjBE,MAAM,GAAGC,IAAI,CAACF,GAAL,CAASD,GAAT,EAAcE,MAAd,CAAT;EACD;;EACD,OAAOA,MAAP;AACD;;AACD,SAASE,UAAT,CAAoBC,YAApB,EAAkCN,OAAlC,EAA2C;EACzC,IAAIM,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAG,CAAf;EACD;;EACD,IAAIN,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,GAAG,GAAGD,OAAO,CAACC,GAAlB;EAAA,IACEC,GAAG,GAAGF,OAAO,CAACE,GADhB;;EAEA,IAAIK,EAAE,GAAG5B,MAAM,CAACgB,QAAQ,CAAC,YAAY;IACjC,OAAOG,cAAc,CAACQ,YAAD,EAAe;MAClCL,GAAG,EAAEA,GAD6B;MAElCC,GAAG,EAAEA;IAF6B,CAAf,CAArB;EAID,CALqB,CAAT,EAKT,CALS,CAAf;EAAA,IAMEM,OAAO,GAAGD,EAAE,CAAC,CAAD,CANd;EAAA,IAOEE,UAAU,GAAGF,EAAE,CAAC,CAAD,CAPjB;;EAQA,IAAIG,QAAQ,GAAG,SAASA,QAAT,CAAkBjB,KAAlB,EAAyB;IACtCgB,UAAU,CAAC,UAAUE,CAAV,EAAa;MACtB,IAAIR,MAAM,GAAGN,QAAQ,CAACJ,KAAD,CAAR,GAAkBA,KAAlB,GAA0BA,KAAK,CAACkB,CAAD,CAA5C;MACA,OAAOb,cAAc,CAACK,MAAD,EAAS;QAC5BD,GAAG,EAAEA,GADuB;QAE5BD,GAAG,EAAEA;MAFuB,CAAT,CAArB;IAID,CANS,CAAV;EAOD,CARD;;EASA,IAAIW,GAAG,GAAG,SAASA,GAAT,CAAaC,KAAb,EAAoB;IAC5B,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;MACpBA,KAAK,GAAG,CAAR;IACD;;IACDH,QAAQ,CAAC,UAAUC,CAAV,EAAa;MACpB,OAAOA,CAAC,GAAGE,KAAX;IACD,CAFO,CAAR;EAGD,CAPD;;EAQA,IAAIC,GAAG,GAAG,SAASA,GAAT,CAAaD,KAAb,EAAoB;IAC5B,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;MACpBA,KAAK,GAAG,CAAR;IACD;;IACDH,QAAQ,CAAC,UAAUC,CAAV,EAAa;MACpB,OAAOA,CAAC,GAAGE,KAAX;IACD,CAFO,CAAR;EAGD,CAPD;;EAQA,IAAIE,GAAG,GAAG,SAASA,GAAT,CAAatB,KAAb,EAAoB;IAC5BiB,QAAQ,CAACjB,KAAD,CAAR;EACD,CAFD;;EAGA,IAAIuB,KAAK,GAAG,SAASA,KAAT,GAAiB;IAC3BN,QAAQ,CAACJ,YAAD,CAAR;EACD,CAFD;;EAGA,OAAO,CAACE,OAAD,EAAU;IACfI,GAAG,EAAEhB,aAAa,CAACgB,GAAD,CADH;IAEfE,GAAG,EAAElB,aAAa,CAACkB,GAAD,CAFH;IAGfC,GAAG,EAAEnB,aAAa,CAACmB,GAAD,CAHH;IAIfC,KAAK,EAAEpB,aAAa,CAACoB,KAAD;EAJL,CAAV,CAAP;AAMD;;AACD,eAAeX,UAAf"}, "metadata": {}, "sourceType": "module"}