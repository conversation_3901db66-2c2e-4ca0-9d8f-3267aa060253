{"ast": null, "code": "import axios from './lib/axios.js'; // Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\n\nvar Axios = axios.Axios,\n    AxiosError = axios.AxiosError,\n    CanceledError = axios.CanceledError,\n    isCancel = axios.isCancel,\n    CancelToken = axios.CancelToken,\n    VERSION = axios.VERSION,\n    all = axios.all,\n    Cancel = axios.Cancel,\n    isAxiosError = axios.isAxiosError,\n    spread = axios.spread,\n    toFormData = axios.toFormData;\nexport default axios;\nexport { Axios, AxiosError, CanceledError, isCancel, CancelToken, VERSION, all, Cancel, isAxiosError, spread, toFormData };", "map": {"version": 3, "names": ["axios", "A<PERSON>os", "AxiosError", "CanceledError", "isCancel", "CancelToken", "VERSION", "all", "Cancel", "isAxiosError", "spread", "toFormData"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/index.js"], "sourcesContent": ["import axios from './lib/axios.js';\n\n// Keep top-level export same with static properties\n// so that it can keep same with es module or cjs\nconst {\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData\n} = axios;\n\nexport default axios;\nexport {\n  Axios,\n  AxiosError,\n  CanceledError,\n  isCancel,\n  CancelToken,\n  VERSION,\n  all,\n  Cancel,\n  isAxiosError,\n  spread,\n  toFormData\n}\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,gBAAlB,C,CAEA;AACA;;AACA,IACEC,KADF,GAYID,KAZJ,CACEC,KADF;AAAA,IAEEC,UAFF,GAYIF,KAZJ,CAEEE,UAFF;AAAA,IAGEC,aAHF,GAYIH,KAZJ,CAGEG,aAHF;AAAA,IAIEC,QAJF,GAYIJ,KAZJ,CAIEI,QAJF;AAAA,IAKEC,WALF,GAYIL,KAZJ,CAKEK,WALF;AAAA,IAMEC,OANF,GAYIN,KAZJ,CAMEM,OANF;AAAA,IAOEC,GAPF,GAYIP,KAZJ,CAOEO,GAPF;AAAA,IAQEC,MARF,GAYIR,KAZJ,CAQEQ,MARF;AAAA,IASEC,YATF,GAYIT,KAZJ,CASES,YATF;AAAA,IAUEC,MAVF,GAYIV,KAZJ,CAUEU,MAVF;AAAA,IAWEC,UAXF,GAYIX,KAZJ,CAWEW,UAXF;AAcA,eAAeX,KAAf;AACA,SACEC,KADF,EAEEC,UAFF,EAGEC,aAHF,EAIEC,QAJF,EAKEC,WALF,EAMEC,OANF,EAOEC,GAPF,EAQEC,MARF,EASEC,YATF,EAUEC,MAVF,EAWEC,UAXF"}, "metadata": {}, "sourceType": "module"}