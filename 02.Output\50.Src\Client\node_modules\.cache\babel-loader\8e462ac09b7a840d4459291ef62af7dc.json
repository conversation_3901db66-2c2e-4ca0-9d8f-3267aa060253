{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\n\nfunction useTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(null);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n\n    timerRef.current = setTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\n\nexport default useTimeout;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "useTimeout", "fn", "delay", "fnRef", "timerRef", "current", "setTimeout", "clearTimeout", "clear"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useTimeout/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nfunction useTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef(null);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    timerRef.current = setTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\nexport default useTimeout;"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,SAAtB,EAAiCC,MAAjC,QAA+C,OAA/C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,SAASC,UAAT,CAAoBC,EAApB,EAAwBC,KAAxB,EAA+B;EAC7B,IAAIC,KAAK,GAAGL,SAAS,CAACG,EAAD,CAArB;EACA,IAAIG,QAAQ,GAAGP,MAAM,CAAC,IAAD,CAArB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAD,CAAT,IAAoBA,KAAK,GAAG,CAAhC,EAAmC;MACjC;IACD;;IACDE,QAAQ,CAACC,OAAT,GAAmBC,UAAU,CAAC,YAAY;MACxCH,KAAK,CAACE,OAAN;IACD,CAF4B,EAE1BH,KAF0B,CAA7B;IAGA,OAAO,YAAY;MACjB,IAAIE,QAAQ,CAACC,OAAb,EAAsB;QACpBE,YAAY,CAACH,QAAQ,CAACC,OAAV,CAAZ;MACD;IACF,CAJD;EAKD,CAZQ,EAYN,CAACH,KAAD,CAZM,CAAT;EAaA,IAAIM,KAAK,GAAGb,WAAW,CAAC,YAAY;IAClC,IAAIS,QAAQ,CAACC,OAAb,EAAsB;MACpBE,YAAY,CAACH,QAAQ,CAACC,OAAV,CAAZ;IACD;EACF,CAJsB,EAIpB,EAJoB,CAAvB;EAKA,OAAOG,KAAP;AACD;;AACD,eAAeR,UAAf"}, "metadata": {}, "sourceType": "module"}