{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils/event'),\n    urlUtils = require('../utils/url'),\n    inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter,\n    WebsocketDriver = require('./driver/websocket');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\n\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n\n  this.url = url;\n  this.ws = new WebsocketDriver(this.url, [], options);\n\n  this.ws.onmessage = function (e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  }; // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n\n\n  this.unloadRef = utils.unloadAdd(function () {\n    debug('unload');\n    self.ws.close();\n  });\n\n  this.ws.onclose = function (e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n\n    self._cleanup();\n  };\n\n  this.ws.onerror = function (e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n\n    self._cleanup();\n  };\n}\n\ninherits(WebSocketTransport, EventEmitter);\n\nWebSocketTransport.prototype.send = function (data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\n\nWebSocketTransport.prototype.close = function () {\n  debug('close');\n  var ws = this.ws;\n\n  this._cleanup();\n\n  if (ws) {\n    ws.close();\n  }\n};\n\nWebSocketTransport.prototype._cleanup = function () {\n  debug('_cleanup');\n  var ws = this.ws;\n\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\n\nWebSocketTransport.enabled = function () {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\n\nWebSocketTransport.transportName = 'websocket'; // In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\n\nWebSocketTransport.roundTrips = 2;\nmodule.exports = WebSocketTransport;", "map": {"version": 3, "names": ["utils", "require", "urlUtils", "inherits", "EventEmitter", "WebsocketDriver", "debug", "process", "env", "NODE_ENV", "WebSocketTransport", "transUrl", "ignore", "options", "enabled", "Error", "call", "self", "url", "addPath", "slice", "ws", "onmessage", "e", "data", "emit", "unloadRef", "unloadAdd", "close", "onclose", "code", "reason", "_cleanup", "onerror", "prototype", "send", "msg", "unloadDel", "removeAllListeners", "transportName", "roundTrips", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/websocket.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils/event')\n  , urlUtils = require('../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , WebsocketDriver = require('./driver/websocket')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\n\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function(e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function(e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function(e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\n\ninherits(WebSocketTransport, EventEmitter);\n\nWebSocketTransport.prototype.send = function(data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\n\nWebSocketTransport.prototype.close = function() {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\n\nWebSocketTransport.prototype._cleanup = function() {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\n\nWebSocketTransport.enabled = function() {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\n\nmodule.exports = WebSocketTransport;\n"], "mappings": "AAAA;;AAEA,IAAIA,KAAK,GAAGC,OAAO,CAAC,gBAAD,CAAnB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,cAAD,CADtB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,UAAD,CAFtB;AAAA,IAGIG,YAAY,GAAGH,OAAO,CAAC,QAAD,CAAP,CAAkBG,YAHrC;AAAA,IAIIC,eAAe,GAAGJ,OAAO,CAAC,oBAAD,CAJ7B;;AAOA,IAAIK,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAD,CAAP,CAAiB,yBAAjB,CAAR;AACD;;AAED,SAASS,kBAAT,CAA4BC,QAA5B,EAAsCC,MAAtC,EAA8CC,OAA9C,EAAuD;EACrD,IAAI,CAACH,kBAAkB,CAACI,OAAnB,EAAL,EAAmC;IACjC,MAAM,IAAIC,KAAJ,CAAU,iCAAV,CAAN;EACD;;EAEDX,YAAY,CAACY,IAAb,CAAkB,IAAlB;EACAV,KAAK,CAAC,aAAD,EAAgBK,QAAhB,CAAL;EAEA,IAAIM,IAAI,GAAG,IAAX;EACA,IAAIC,GAAG,GAAGhB,QAAQ,CAACiB,OAAT,CAAiBR,QAAjB,EAA2B,YAA3B,CAAV;;EACA,IAAIO,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAa,CAAb,MAAoB,OAAxB,EAAiC;IAC/BF,GAAG,GAAG,QAAQA,GAAG,CAACE,KAAJ,CAAU,CAAV,CAAd;EACD,CAFD,MAEO;IACLF,GAAG,GAAG,OAAOA,GAAG,CAACE,KAAJ,CAAU,CAAV,CAAb;EACD;;EACD,KAAKF,GAAL,GAAWA,GAAX;EAEA,KAAKG,EAAL,GAAU,IAAIhB,eAAJ,CAAoB,KAAKa,GAAzB,EAA8B,EAA9B,EAAkCL,OAAlC,CAAV;;EACA,KAAKQ,EAAL,CAAQC,SAAR,GAAoB,UAASC,CAAT,EAAY;IAC9BjB,KAAK,CAAC,eAAD,EAAkBiB,CAAC,CAACC,IAApB,CAAL;IACAP,IAAI,CAACQ,IAAL,CAAU,SAAV,EAAqBF,CAAC,CAACC,IAAvB;EACD,CAHD,CAlBqD,CAsBrD;EACA;EACA;EACA;EACA;EACA;;;EACA,KAAKE,SAAL,GAAiB1B,KAAK,CAAC2B,SAAN,CAAgB,YAAW;IAC1CrB,KAAK,CAAC,QAAD,CAAL;IACAW,IAAI,CAACI,EAAL,CAAQO,KAAR;EACD,CAHgB,CAAjB;;EAIA,KAAKP,EAAL,CAAQQ,OAAR,GAAkB,UAASN,CAAT,EAAY;IAC5BjB,KAAK,CAAC,aAAD,EAAgBiB,CAAC,CAACO,IAAlB,EAAwBP,CAAC,CAACQ,MAA1B,CAAL;IACAd,IAAI,CAACQ,IAAL,CAAU,OAAV,EAAmBF,CAAC,CAACO,IAArB,EAA2BP,CAAC,CAACQ,MAA7B;;IACAd,IAAI,CAACe,QAAL;EACD,CAJD;;EAKA,KAAKX,EAAL,CAAQY,OAAR,GAAkB,UAASV,CAAT,EAAY;IAC5BjB,KAAK,CAAC,aAAD,EAAgBiB,CAAhB,CAAL;IACAN,IAAI,CAACQ,IAAL,CAAU,OAAV,EAAmB,IAAnB,EAAyB,6BAAzB;;IACAR,IAAI,CAACe,QAAL;EACD,CAJD;AAKD;;AAED7B,QAAQ,CAACO,kBAAD,EAAqBN,YAArB,CAAR;;AAEAM,kBAAkB,CAACwB,SAAnB,CAA6BC,IAA7B,GAAoC,UAASX,IAAT,EAAe;EACjD,IAAIY,GAAG,GAAG,MAAMZ,IAAN,GAAa,GAAvB;EACAlB,KAAK,CAAC,MAAD,EAAS8B,GAAT,CAAL;EACA,KAAKf,EAAL,CAAQc,IAAR,CAAaC,GAAb;AACD,CAJD;;AAMA1B,kBAAkB,CAACwB,SAAnB,CAA6BN,KAA7B,GAAqC,YAAW;EAC9CtB,KAAK,CAAC,OAAD,CAAL;EACA,IAAIe,EAAE,GAAG,KAAKA,EAAd;;EACA,KAAKW,QAAL;;EACA,IAAIX,EAAJ,EAAQ;IACNA,EAAE,CAACO,KAAH;EACD;AACF,CAPD;;AASAlB,kBAAkB,CAACwB,SAAnB,CAA6BF,QAA7B,GAAwC,YAAW;EACjD1B,KAAK,CAAC,UAAD,CAAL;EACA,IAAIe,EAAE,GAAG,KAAKA,EAAd;;EACA,IAAIA,EAAJ,EAAQ;IACNA,EAAE,CAACC,SAAH,GAAeD,EAAE,CAACQ,OAAH,GAAaR,EAAE,CAACY,OAAH,GAAa,IAAzC;EACD;;EACDjC,KAAK,CAACqC,SAAN,CAAgB,KAAKX,SAArB;EACA,KAAKA,SAAL,GAAiB,KAAKL,EAAL,GAAU,IAA3B;EACA,KAAKiB,kBAAL;AACD,CATD;;AAWA5B,kBAAkB,CAACI,OAAnB,GAA6B,YAAW;EACtCR,KAAK,CAAC,SAAD,CAAL;EACA,OAAO,CAAC,CAACD,eAAT;AACD,CAHD;;AAIAK,kBAAkB,CAAC6B,aAAnB,GAAmC,WAAnC,C,CAEA;AACA;AACA;AACA;;AACA7B,kBAAkB,CAAC8B,UAAnB,GAAgC,CAAhC;AAEAC,MAAM,CAACC,OAAP,GAAiBhC,kBAAjB"}, "metadata": {}, "sourceType": "script"}