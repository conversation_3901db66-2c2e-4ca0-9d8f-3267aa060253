{"ast": null, "code": "/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n  this.size = data.size;\n  return result;\n}\n\nmodule.exports = stackDelete;", "map": {"version": 3, "names": ["stackDelete", "key", "data", "__data__", "result", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_stackDelete.js"], "sourcesContent": ["/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n\n  this.size = data.size;\n  return result;\n}\n\nmodule.exports = stackDelete;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAT,CAAqBC,GAArB,EAA0B;EACxB,IAAIC,IAAI,GAAG,KAAKC,QAAhB;EAAA,IACIC,MAAM,GAAGF,IAAI,CAAC,QAAD,CAAJ,CAAeD,GAAf,CADb;EAGA,KAAKI,IAAL,GAAYH,IAAI,CAACG,IAAjB;EACA,OAAOD,MAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBP,WAAjB"}, "metadata": {}, "sourceType": "script"}