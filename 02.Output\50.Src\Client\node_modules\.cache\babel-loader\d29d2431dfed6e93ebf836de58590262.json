{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\n\nvar getVisibility = function getVisibility() {\n  if (!isBrowser) {\n    return 'visible';\n  }\n\n  return document.visibilityState;\n};\n\nfunction useDocumentVisibility() {\n  var _a = __read(useState(function () {\n    return getVisibility();\n  }), 2),\n      documentVisibility = _a[0],\n      setDocumentVisibility = _a[1];\n\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function target() {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\n\nexport default useDocumentVisibility;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useEventListener", "<PERSON><PERSON><PERSON><PERSON>", "getVisibility", "document", "visibilityState", "useDocumentVisibility", "_a", "documentVisibility", "setDocumentVisibility", "target"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useDocumentVisibility/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\nvar getVisibility = function getVisibility() {\n  if (!isBrowser) {\n    return 'visible';\n  }\n  return document.visibilityState;\n};\nfunction useDocumentVisibility() {\n  var _a = __read(useState(function () {\n      return getVisibility();\n    }), 2),\n    documentVisibility = _a[0],\n    setDocumentVisibility = _a[1];\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function target() {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\nexport default useDocumentVisibility;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,OAAOC,SAAP,MAAsB,oBAAtB;;AACA,IAAIC,aAAa,GAAG,SAASA,aAAT,GAAyB;EAC3C,IAAI,CAACD,SAAL,EAAgB;IACd,OAAO,SAAP;EACD;;EACD,OAAOE,QAAQ,CAACC,eAAhB;AACD,CALD;;AAMA,SAASC,qBAAT,GAAiC;EAC/B,IAAIC,EAAE,GAAGvB,MAAM,CAACgB,QAAQ,CAAC,YAAY;IACjC,OAAOG,aAAa,EAApB;EACD,CAFqB,CAAT,EAET,CAFS,CAAf;EAAA,IAGEK,kBAAkB,GAAGD,EAAE,CAAC,CAAD,CAHzB;EAAA,IAIEE,qBAAqB,GAAGF,EAAE,CAAC,CAAD,CAJ5B;;EAKAN,gBAAgB,CAAC,kBAAD,EAAqB,YAAY;IAC/CQ,qBAAqB,CAACN,aAAa,EAAd,CAArB;EACD,CAFe,EAEb;IACDO,MAAM,EAAE,SAASA,MAAT,GAAkB;MACxB,OAAON,QAAP;IACD;EAHA,CAFa,CAAhB;EAOA,OAAOI,kBAAP;AACD;;AACD,eAAeF,qBAAf"}, "metadata": {}, "sourceType": "module"}