{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\ExtendedVehicle.js\";\nimport React from 'react';\nimport CellBox from './elements/CellBox';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MAX_ROW = 50;\nconst GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\nconst GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\n/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */\n\nconst ExtendedVehicle = props => {\n  if (!isValidSource(props)) return;\n  let showDelopy = props.is_deployment === 1;\n  let totalRowCounter = 0;\n  let targetArray = [];\n  if (!props.title_name) return; //最大MaxRowのデータを取り出す\n\n  for (const item of props.title_name) {\n    if (!item.car_name) return; // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\n\n    let subRowCounter = item.car_name.length + 1;\n\n    if (totalRowCounter + subRowCounter <= MAX_ROW) {\n      targetArray.push(item);\n      totalRowCounter = totalRowCounter + subRowCounter;\n    } else if (totalRowCounter <= MAX_ROW - 2) {\n      var _newObj$deployment, _newObj$car_name, _newObj$town_name, _newObj$disaster_type, _newObj$avm_dynamic_s, _newObj$lighting_sett;\n\n      subRowCounter = MAX_ROW - totalRowCounter; // この - 1がSubTitleの行\n\n      const subItemCounter = subRowCounter - 1;\n      let newObj = { ...item\n      };\n      newObj.deployment = (_newObj$deployment = newObj.deployment) === null || _newObj$deployment === void 0 ? void 0 : _newObj$deployment.slice(0, subItemCounter);\n      newObj.car_name = (_newObj$car_name = newObj.car_name) === null || _newObj$car_name === void 0 ? void 0 : _newObj$car_name.slice(0, subItemCounter);\n      newObj.town_name = (_newObj$town_name = newObj.town_name) === null || _newObj$town_name === void 0 ? void 0 : _newObj$town_name.slice(0, subItemCounter);\n      newObj.disaster_type = (_newObj$disaster_type = newObj.disaster_type) === null || _newObj$disaster_type === void 0 ? void 0 : _newObj$disaster_type.slice(0, subItemCounter);\n      newObj.avm_dynamic_state = (_newObj$avm_dynamic_s = newObj.avm_dynamic_state) === null || _newObj$avm_dynamic_s === void 0 ? void 0 : _newObj$avm_dynamic_s.slice(0, subItemCounter);\n      newObj.lighting_setting = (_newObj$lighting_sett = newObj.lighting_setting) === null || _newObj$lighting_sett === void 0 ? void 0 : _newObj$lighting_sett.slice(0, subItemCounter);\n      targetArray.push(newObj);\n      totalRowCounter = totalRowCounter + subRowCounter;\n    }\n  }\n\n  let nextStartRow = 1;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isValidSource(props) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 grid-rows-25 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]\",\n      children: targetArray.map((item, index) => {\n        var _item$car_name;\n\n        let startRow = nextStartRow; //現在のStart\n\n        nextStartRow = nextStartRow + (item === null || item === void 0 ? void 0 : (_item$car_name = item.car_name) === null || _item$car_name === void 0 ? void 0 : _item$car_name.length) + 1; //次のStartRowを計算\n\n        return /*#__PURE__*/_jsxDEV(ExtendedStation, { ...item,\n          index: index,\n          showDelopy: showDelopy,\n          startRow: startRow\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */\n\n\n_c = ExtendedVehicle;\n\nconst ExtendedStation = props => {\n  let gridCol;\n  let subTitleSpan = 'col-span-full';\n\n  if (props.showDelopy) {\n    gridCol = 'grid-cols-extended-vehicle-deploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n  } else {\n    gridCol = 'grid-cols-extended-vehicle-nodeploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n  }\n\n  const subTitleProp = getCellFace(props, `${subTitleSpan} flex flex-col items-center`);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(CellBox, { ...subTitleProp,\n        children: props.display_text && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: props.display_text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), props.car_name.map((item, index) => {\n      return /*#__PURE__*/_jsxDEV(ExtendedVehicleDetailRow, { ...props,\n        index: index\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 24\n      }, this);\n    })]\n  }, void 0, true);\n};\n/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */\n\n\n_c2 = ExtendedStation;\n\nconst ExtendedVehicleDetailRow = props => {\n  let showInfoSeperator0 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator1 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator2 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator3 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator4 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoSeperator5 = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoDeployment = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoCarName = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoTownName = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoDisasterType = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n  let showInfoAvmDynamicState = {\n    display_text: '',\n    text_color: '',\n    background_color: ''\n  };\n\n  if (props.deployment && props.deployment[props.index]) {\n    showInfoDeployment = props.deployment[props.index];\n  }\n\n  if (props.car_name && props.car_name[props.index]) {\n    showInfoCarName = props.car_name[props.index];\n  }\n\n  if (props.town_name && props.town_name[props.index]) {\n    showInfoTownName = props.town_name[props.index];\n  }\n\n  if (props.disaster_type && props.disaster_type[props.index]) {\n    showInfoDisasterType = props.disaster_type[props.index];\n  }\n\n  if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {\n    showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\n  } //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\n\n\n  let baseObj = props.car_name[props.index];\n\n  if (!baseObj) {\n    baseObj = props.town_name[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.disaster_type[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.avm_dynamic_state[props.index];\n  }\n\n  if (!baseObj) {\n    baseObj = props.deployment[props.index];\n  }\n\n  showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\n  showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\n  showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\n  showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\n  showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\n  showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);\n  showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);\n  showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);\n  showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);\n  showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);\n  showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);\n  let gridCol;\n  let showBlock = [];\n\n  if (props.showDelopy) {\n    gridCol = 'grid-cols-extended-vehicle-deploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n\n    if (props.startRow + props.index <= MAX_ROW / 2) {\n      //左半分\n      showBlock.push({\n        showInfo: showInfoSeperator0,\n        className: 'col-span-1'\n      });\n    }\n\n    showBlock.push({\n      showInfo: showInfoDeployment,\n      className: 'col-span-1 col-start-2'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator1,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoCarName,\n      className: 'col-span-4 col-start-4'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator2,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoTownName,\n      className: 'col-span-6 col-start-9'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator3,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoDisasterType,\n      className: 'col-span-2 col-start-16'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator4,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoAvmDynamicState,\n      className: 'col-span-2 col-start-19'\n    });\n\n    if (props.startRow + props.index > MAX_ROW / 2) {\n      //右半分\n      showBlock.push({\n        showInfo: showInfoSeperator5,\n        className: 'col-span-1'\n      });\n    }\n  } else {\n    gridCol = 'grid-cols-extended-vehicle-nodeploy'; //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n\n    showBlock.push({\n      showInfo: showInfoSeperator1,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoCarName,\n      className: 'col-span-4 col-start-2'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator2,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoTownName,\n      className: 'col-span-6 col-start-7'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator3,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoDisasterType,\n      className: 'col-span-2 col-start-14'\n    });\n    showBlock.push({\n      showInfo: showInfoSeperator4,\n      className: 'col-span-1'\n    });\n    showBlock.push({\n      showInfo: showInfoAvmDynamicState,\n      className: 'col-span-2 col-start-17'\n    });\n\n    if (props.startRow + props.index > MAX_ROW / 2) {\n      //右半分\n      showBlock.push({\n        showInfo: showInfoSeperator5,\n        className: 'col-span-1'\n      });\n    }\n  }\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [props.showDelopy && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(BlinkBlock, {\n        block: showBlock,\n        blink_setting: props.lighting_setting[props.index]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 17\n    }, this), !props.showDelopy && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${gridCol}`,\n      children: /*#__PURE__*/_jsxDEV(BlinkBlock, {\n        block: showBlock,\n        blink_setting: props.lighting_setting[props.index]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true);\n};\n\n_c3 = ExtendedVehicleDetailRow;\nexport default ExtendedVehicle;\n\nvar _c, _c2, _c3;\n\n$RefreshReg$(_c, \"ExtendedVehicle\");\n$RefreshReg$(_c2, \"ExtendedStation\");\n$RefreshReg$(_c3, \"ExtendedVehicleDetailRow\");", "map": {"version": 3, "names": ["React", "CellBox", "getCellFace", "isValidSource", "BlinkBlock", "checkBlinkInfo", "MAX_ROW", "GRID_COLS_EXTENDED_VEHICLE_DEPLOY", "GRID_COLS_EXTENDED_VEHICLE_NODEPLOY", "ExtendedVehicle", "props", "showDelopy", "is_deployment", "totalRowCounter", "targetArray", "title_name", "item", "car_name", "subRowCounter", "length", "push", "subItemCounter", "newObj", "deployment", "slice", "town_name", "disaster_type", "avm_dynamic_state", "lighting_setting", "nextStartRow", "map", "index", "startRow", "ExtendedStation", "gridCol", "subTitleSpan", "subTitleProp", "display_text", "ExtendedVehicleDetailRow", "showInfoSeperator0", "text_color", "background_color", "showInfoSeperator1", "showInfoSeperator2", "showInfoSeperator3", "showInfoSeperator4", "showInfoSeperator5", "showInfoDeployment", "showInfoCarName", "showInfoTownName", "showInfoDisasterType", "showInfoAvmDynamicState", "baseObj", "showBlock", "showInfo", "className"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/ExtendedVehicle.js"], "sourcesContent": ["import React from 'react';\nimport CellBox from './elements/CellBox';\nimport { getCellFace, isValidSource } from '../utils/Util.js';\nimport Blink<PERSON>lock, { checkBlinkInfo } from './elements/BlinkBlock';\n\nconst MAX_ROW = 50;\nconst GRID_COLS_EXTENDED_VEHICLE_DEPLOY = '16px 56px    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(6, 56px)     minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)    16px';\nconst GRID_COLS_EXTENDED_VEHICLE_NODEPLOY = '16px repeat(4, 56px)    minmax(4px,1fr) repeat(4, 56px)    minmax(4px,1fr) repeat(2, 56px)    minmax(4px,1fr) repeat(2, 56px)     16px';\n\n/**\n * 拡張車両コンテンツ（50行表示）<br>\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\n * @module ExtendedVehicle\n * @component\n * @param {*} props\n * @return {*} 表示データ\n */\nconst ExtendedVehicle = (props) => {\n    if (!isValidSource(props)) return;\n\n    let showDelopy = props.is_deployment === 1;\n\n    let totalRowCounter = 0;\n\n    let targetArray = [];\n\n    if (!props.title_name)\n        return;\n    \n    //最大MaxRowのデータを取り出す\n    for (const item of props.title_name) {\n        \n        if (!item.car_name) \n            return;\n\n        // 車両名称のcar_nameがあれば、一行のデータを表示する。この + 1がSubTitleの行\n        let subRowCounter = item.car_name.length + 1;\n\n        if (totalRowCounter + subRowCounter <= MAX_ROW) {\n            targetArray.push(item);\n            totalRowCounter = totalRowCounter + subRowCounter;\n        } else if (totalRowCounter <= MAX_ROW - 2) {\n            subRowCounter = MAX_ROW - totalRowCounter;\n\n            // この - 1がSubTitleの行\n            const subItemCounter = subRowCounter - 1;\n\n            let newObj = { ...item };\n            newObj.deployment = newObj.deployment?.slice(0, subItemCounter);\n            newObj.car_name = newObj.car_name?.slice(0, subItemCounter);\n            newObj.town_name = newObj.town_name?.slice(0, subItemCounter);\n            newObj.disaster_type = newObj.disaster_type?.slice(0, subItemCounter);\n            newObj.avm_dynamic_state = newObj.avm_dynamic_state?.slice(\n                0,\n                subItemCounter\n            );\n            newObj.lighting_setting = newObj.lighting_setting?.slice(\n                0,\n                subItemCounter\n            );\n\n            targetArray.push(newObj);\n            totalRowCounter = totalRowCounter + subRowCounter;\n        }\n    }\n\n    let nextStartRow = 1;\n    return (\n        <>\n            {isValidSource(props) && (\n                <div className=\"grid grid-cols-2 grid-rows-25 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]\">\n                    {targetArray.map((item, index) => {\n                        let startRow = nextStartRow; //現在のStart\n                        nextStartRow = nextStartRow + item?.car_name?.length + 1; //次のStartRowを計算\n\n                        return (\n                            <ExtendedStation\n                                key={index}\n                                {...item}\n                                index={index}\n                                showDelopy={showDelopy}\n                                startRow={startRow}\n                            />\n                        );\n                    })}\n                </div>\n            )}\n        </>\n    );\n};\n\n/**\n * 署所(部隊)名または車両種別単位のデータを表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedStation = (props) => {\n    let gridCol;\n    let subTitleSpan = 'col-span-full';\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n    }\n    const subTitleProp = getCellFace(\n        props,\n        `${subTitleSpan} flex flex-col items-center`\n    );\n\n    return (\n        <>\n            <div className={`grid ${gridCol}`}>\n                <CellBox {...subTitleProp}>\n                    {props.display_text && <span>{props.display_text}</span>}\n                </CellBox>\n            </div>\n            {props.car_name.map((item, index) => {\n                return <ExtendedVehicleDetailRow key={index} {...props} index={index} />;\n            })}\n        </>\n    );\n};\n\n/**\n * 車両詳細行の表示\n * @param {*} props\n * @returns 表示データ\n */\nconst ExtendedVehicleDetailRow = (props) => {\n    let showInfoSeperator0 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator1 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator2 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator3 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator4 = { display_text: '', text_color: '', background_color: '' };\n    let showInfoSeperator5 = { display_text: '', text_color: '', background_color: '' };\n\n    let showInfoDeployment = { display_text: '', text_color: '', background_color: '' };\n    let showInfoCarName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoTownName = { display_text: '', text_color: '', background_color: '' };\n    let showInfoDisasterType = { display_text: '', text_color: '', background_color: '' };\n    let showInfoAvmDynamicState = { display_text: '', text_color: '', background_color: '' };\n\n    if (props.deployment && props.deployment[props.index]) {\n        showInfoDeployment = props.deployment[props.index];\n    }\n\n    if (props.car_name && props.car_name[props.index]) {\n        showInfoCarName = props.car_name[props.index];\n    }\n\n    if (props.town_name && props.town_name[props.index]) {\n        showInfoTownName = props.town_name[props.index];\n    }\n\n    if (props.disaster_type && props.disaster_type[props.index]) {\n        showInfoDisasterType = props.disaster_type[props.index];\n    }\n\n    if (props.avm_dynamic_state && props.avm_dynamic_state[props.index]) {\n        showInfoAvmDynamicState = props.avm_dynamic_state[props.index];\n    }\n\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\n    let baseObj = props.car_name[props.index];\n    if (!baseObj) { baseObj = props.town_name[props.index] }\n    if (!baseObj) { baseObj = props.disaster_type[props.index] }\n    if (!baseObj) { baseObj = props.avm_dynamic_state[props.index] }\n    if (!baseObj) { baseObj = props.deployment[props.index] }\n\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\n\n    showInfoSeperator0 = checkBlinkInfo(showInfoSeperator0, baseObj);\n    showInfoSeperator1 = checkBlinkInfo(showInfoSeperator1, baseObj);\n    showInfoSeperator2 = checkBlinkInfo(showInfoSeperator2, baseObj);\n    showInfoSeperator3 = checkBlinkInfo(showInfoSeperator3, baseObj);\n    showInfoSeperator4 = checkBlinkInfo(showInfoSeperator4, baseObj);\n    showInfoSeperator5 = checkBlinkInfo(showInfoSeperator5, baseObj);\n\n    let gridCol;\n    let showBlock = [];\n    if (props.showDelopy) {\n        gridCol = 'grid-cols-extended-vehicle-deploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_DEPLOY;\n\n        if ((props.startRow + props.index) <= (MAX_ROW / 2)) { //左半分\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\n        }\n        showBlock.push({\n            showInfo: showInfoDeployment,\n            className: 'col-span-1 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-4',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-9',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-16',\n        });\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-19',\n        });\n\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\n        }\n\n    } else {\n        gridCol = 'grid-cols-extended-vehicle-nodeploy';\n        //gridCol = GRID_COLS_EXTENDED_VEHICLE_NODEPLOY;\n\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoCarName,\n            className: 'col-span-4 col-start-2',\n        });\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoTownName,\n            className: 'col-span-6 col-start-7',\n        });\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoDisasterType,\n            className: 'col-span-2 col-start-14',\n        });\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\n        showBlock.push({\n            showInfo: showInfoAvmDynamicState,\n            className: 'col-span-2 col-start-17',\n        });\n\n        if ((props.startRow + props.index) > (MAX_ROW / 2)) { //右半分\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\n        }\n\n    }\n\n    return (\n        <>\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\n            {props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n            {!props.showDelopy && (\n                <div className={`grid ${gridCol}`}>\n                    <BlinkBlock\n                        block={showBlock}\n                        blink_setting={props.lighting_setting[props.index]}\n                    />\n                </div>\n            )}\n        </>\n    );\n};\n\n\n\nexport default ExtendedVehicle;\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,OAAP,MAAoB,oBAApB;AACA,SAASC,WAAT,EAAsBC,aAAtB,QAA2C,kBAA3C;AACA,OAAOC,UAAP,IAAqBC,cAArB,QAA2C,uBAA3C;;;AAEA,MAAMC,OAAO,GAAG,EAAhB;AACA,MAAMC,iCAAiC,GAAG,gKAA1C;AACA,MAAMC,mCAAmC,GAAG,wIAA5C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,eAAe,GAAIC,KAAD,IAAW;EAC/B,IAAI,CAACP,aAAa,CAACO,KAAD,CAAlB,EAA2B;EAE3B,IAAIC,UAAU,GAAGD,KAAK,CAACE,aAAN,KAAwB,CAAzC;EAEA,IAAIC,eAAe,GAAG,CAAtB;EAEA,IAAIC,WAAW,GAAG,EAAlB;EAEA,IAAI,CAACJ,KAAK,CAACK,UAAX,EACI,OAV2B,CAY/B;;EACA,KAAK,MAAMC,IAAX,IAAmBN,KAAK,CAACK,UAAzB,EAAqC;IAEjC,IAAI,CAACC,IAAI,CAACC,QAAV,EACI,OAH6B,CAKjC;;IACA,IAAIC,aAAa,GAAGF,IAAI,CAACC,QAAL,CAAcE,MAAd,GAAuB,CAA3C;;IAEA,IAAIN,eAAe,GAAGK,aAAlB,IAAmCZ,OAAvC,EAAgD;MAC5CQ,WAAW,CAACM,IAAZ,CAAiBJ,IAAjB;MACAH,eAAe,GAAGA,eAAe,GAAGK,aAApC;IACH,CAHD,MAGO,IAAIL,eAAe,IAAIP,OAAO,GAAG,CAAjC,EAAoC;MAAA;;MACvCY,aAAa,GAAGZ,OAAO,GAAGO,eAA1B,CADuC,CAGvC;;MACA,MAAMQ,cAAc,GAAGH,aAAa,GAAG,CAAvC;MAEA,IAAII,MAAM,GAAG,EAAE,GAAGN;MAAL,CAAb;MACAM,MAAM,CAACC,UAAP,yBAAoBD,MAAM,CAACC,UAA3B,uDAAoB,mBAAmBC,KAAnB,CAAyB,CAAzB,EAA4BH,cAA5B,CAApB;MACAC,MAAM,CAACL,QAAP,uBAAkBK,MAAM,CAACL,QAAzB,qDAAkB,iBAAiBO,KAAjB,CAAuB,CAAvB,EAA0BH,cAA1B,CAAlB;MACAC,MAAM,CAACG,SAAP,wBAAmBH,MAAM,CAACG,SAA1B,sDAAmB,kBAAkBD,KAAlB,CAAwB,CAAxB,EAA2BH,cAA3B,CAAnB;MACAC,MAAM,CAACI,aAAP,4BAAuBJ,MAAM,CAACI,aAA9B,0DAAuB,sBAAsBF,KAAtB,CAA4B,CAA5B,EAA+BH,cAA/B,CAAvB;MACAC,MAAM,CAACK,iBAAP,4BAA2BL,MAAM,CAACK,iBAAlC,0DAA2B,sBAA0BH,KAA1B,CACvB,CADuB,EAEvBH,cAFuB,CAA3B;MAIAC,MAAM,CAACM,gBAAP,4BAA0BN,MAAM,CAACM,gBAAjC,0DAA0B,sBAAyBJ,KAAzB,CACtB,CADsB,EAEtBH,cAFsB,CAA1B;MAKAP,WAAW,CAACM,IAAZ,CAAiBE,MAAjB;MACAT,eAAe,GAAGA,eAAe,GAAGK,aAApC;IACH;EACJ;;EAED,IAAIW,YAAY,GAAG,CAAnB;EACA,oBACI;IAAA,UACK1B,aAAa,CAACO,KAAD,CAAb,iBACG;MAAK,SAAS,EAAC,uGAAf;MAAA,UACKI,WAAW,CAACgB,GAAZ,CAAgB,CAACd,IAAD,EAAOe,KAAP,KAAiB;QAAA;;QAC9B,IAAIC,QAAQ,GAAGH,YAAf,CAD8B,CACD;;QAC7BA,YAAY,GAAGA,YAAY,IAAGb,IAAH,aAAGA,IAAH,yCAAGA,IAAI,CAAEC,QAAT,mDAAG,eAAgBE,MAAnB,CAAZ,GAAwC,CAAvD,CAF8B,CAE4B;;QAE1D,oBACI,QAAC,eAAD,OAEQH,IAFR;UAGI,KAAK,EAAEe,KAHX;UAII,UAAU,EAAEpB,UAJhB;UAKI,QAAQ,EAAEqB;QALd,GACSD,KADT;UAAA;UAAA;UAAA;QAAA,QADJ;MASH,CAbA;IADL;MAAA;MAAA;MAAA;IAAA;EAFR,iBADJ;AAsBH,CAxED;AA0EA;AACA;AACA;AACA;AACA;;;KA9EMtB,e;;AA+EN,MAAMwB,eAAe,GAAIvB,KAAD,IAAW;EAC/B,IAAIwB,OAAJ;EACA,IAAIC,YAAY,GAAG,eAAnB;;EACA,IAAIzB,KAAK,CAACC,UAAV,EAAsB;IAClBuB,OAAO,GAAG,mCAAV,CADkB,CAElB;EACH,CAHD,MAGO;IACHA,OAAO,GAAG,qCAAV,CADG,CAEH;EACH;;EACD,MAAME,YAAY,GAAGlC,WAAW,CAC5BQ,KAD4B,EAE3B,GAAEyB,YAAa,6BAFY,CAAhC;EAKA,oBACI;IAAA,wBACI;MAAK,SAAS,EAAG,QAAOD,OAAQ,EAAhC;MAAA,uBACI,QAAC,OAAD,OAAaE,YAAb;QAAA,UACK1B,KAAK,CAAC2B,YAAN,iBAAsB;UAAA,UAAO3B,KAAK,CAAC2B;QAAb;UAAA;UAAA;UAAA;QAAA;MAD3B;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QADJ,EAMK3B,KAAK,CAACO,QAAN,CAAea,GAAf,CAAmB,CAACd,IAAD,EAAOe,KAAP,KAAiB;MACjC,oBAAO,QAAC,wBAAD,OAA0CrB,KAA1C;QAAiD,KAAK,EAAEqB;MAAxD,GAA+BA,KAA/B;QAAA;QAAA;QAAA;MAAA,QAAP;IACH,CAFA,CANL;EAAA,gBADJ;AAYH,CA3BD;AA6BA;AACA;AACA;AACA;AACA;;;MAjCME,e;;AAkCN,MAAMK,wBAAwB,GAAI5B,KAAD,IAAW;EACxC,IAAI6B,kBAAkB,GAAG;IAAEF,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIC,kBAAkB,GAAG;IAAEL,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIE,kBAAkB,GAAG;IAAEN,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIG,kBAAkB,GAAG;IAAEP,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAII,kBAAkB,GAAG;IAAER,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIK,kBAAkB,GAAG;IAAET,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EAEA,IAAIM,kBAAkB,GAAG;IAAEV,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAzB;EACA,IAAIO,eAAe,GAAG;IAAEX,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAtB;EACA,IAAIQ,gBAAgB,GAAG;IAAEZ,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAAvB;EACA,IAAIS,oBAAoB,GAAG;IAAEb,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAA3B;EACA,IAAIU,uBAAuB,GAAG;IAAEd,YAAY,EAAE,EAAhB;IAAoBG,UAAU,EAAE,EAAhC;IAAoCC,gBAAgB,EAAE;EAAtD,CAA9B;;EAEA,IAAI/B,KAAK,CAACa,UAAN,IAAoBb,KAAK,CAACa,UAAN,CAAiBb,KAAK,CAACqB,KAAvB,CAAxB,EAAuD;IACnDgB,kBAAkB,GAAGrC,KAAK,CAACa,UAAN,CAAiBb,KAAK,CAACqB,KAAvB,CAArB;EACH;;EAED,IAAIrB,KAAK,CAACO,QAAN,IAAkBP,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACqB,KAArB,CAAtB,EAAmD;IAC/CiB,eAAe,GAAGtC,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACqB,KAArB,CAAlB;EACH;;EAED,IAAIrB,KAAK,CAACe,SAAN,IAAmBf,KAAK,CAACe,SAAN,CAAgBf,KAAK,CAACqB,KAAtB,CAAvB,EAAqD;IACjDkB,gBAAgB,GAAGvC,KAAK,CAACe,SAAN,CAAgBf,KAAK,CAACqB,KAAtB,CAAnB;EACH;;EAED,IAAIrB,KAAK,CAACgB,aAAN,IAAuBhB,KAAK,CAACgB,aAAN,CAAoBhB,KAAK,CAACqB,KAA1B,CAA3B,EAA6D;IACzDmB,oBAAoB,GAAGxC,KAAK,CAACgB,aAAN,CAAoBhB,KAAK,CAACqB,KAA1B,CAAvB;EACH;;EAED,IAAIrB,KAAK,CAACiB,iBAAN,IAA2BjB,KAAK,CAACiB,iBAAN,CAAwBjB,KAAK,CAACqB,KAA9B,CAA/B,EAAqE;IACjEoB,uBAAuB,GAAGzC,KAAK,CAACiB,iBAAN,CAAwBjB,KAAK,CAACqB,KAA9B,CAA1B;EACH,CAhCuC,CAkCxC;;;EACA,IAAIqB,OAAO,GAAG1C,KAAK,CAACO,QAAN,CAAeP,KAAK,CAACqB,KAArB,CAAd;;EACA,IAAI,CAACqB,OAAL,EAAc;IAAEA,OAAO,GAAG1C,KAAK,CAACe,SAAN,CAAgBf,KAAK,CAACqB,KAAtB,CAAV;EAAwC;;EACxD,IAAI,CAACqB,OAAL,EAAc;IAAEA,OAAO,GAAG1C,KAAK,CAACgB,aAAN,CAAoBhB,KAAK,CAACqB,KAA1B,CAAV;EAA4C;;EAC5D,IAAI,CAACqB,OAAL,EAAc;IAAEA,OAAO,GAAG1C,KAAK,CAACiB,iBAAN,CAAwBjB,KAAK,CAACqB,KAA9B,CAAV;EAAgD;;EAChE,IAAI,CAACqB,OAAL,EAAc;IAAEA,OAAO,GAAG1C,KAAK,CAACa,UAAN,CAAiBb,KAAK,CAACqB,KAAvB,CAAV;EAAyC;;EAEzDgB,kBAAkB,GAAG1C,cAAc,CAAC0C,kBAAD,EAAqBK,OAArB,CAAnC;EACAJ,eAAe,GAAG3C,cAAc,CAAC2C,eAAD,EAAkBI,OAAlB,CAAhC;EACAH,gBAAgB,GAAG5C,cAAc,CAAC4C,gBAAD,EAAmBG,OAAnB,CAAjC;EACAF,oBAAoB,GAAG7C,cAAc,CAAC6C,oBAAD,EAAuBE,OAAvB,CAArC;EACAD,uBAAuB,GAAG9C,cAAc,CAAC8C,uBAAD,EAA0BC,OAA1B,CAAxC;EAEAb,kBAAkB,GAAGlC,cAAc,CAACkC,kBAAD,EAAqBa,OAArB,CAAnC;EACAV,kBAAkB,GAAGrC,cAAc,CAACqC,kBAAD,EAAqBU,OAArB,CAAnC;EACAT,kBAAkB,GAAGtC,cAAc,CAACsC,kBAAD,EAAqBS,OAArB,CAAnC;EACAR,kBAAkB,GAAGvC,cAAc,CAACuC,kBAAD,EAAqBQ,OAArB,CAAnC;EACAP,kBAAkB,GAAGxC,cAAc,CAACwC,kBAAD,EAAqBO,OAArB,CAAnC;EACAN,kBAAkB,GAAGzC,cAAc,CAACyC,kBAAD,EAAqBM,OAArB,CAAnC;EAEA,IAAIlB,OAAJ;EACA,IAAImB,SAAS,GAAG,EAAhB;;EACA,IAAI3C,KAAK,CAACC,UAAV,EAAsB;IAClBuB,OAAO,GAAG,mCAAV,CADkB,CAElB;;IAEA,IAAKxB,KAAK,CAACsB,QAAN,GAAiBtB,KAAK,CAACqB,KAAxB,IAAmCzB,OAAO,GAAG,CAAjD,EAAqD;MAAE;MACnD+C,SAAS,CAACjC,IAAV,CAAe;QAAEkC,QAAQ,EAAEf,kBAAZ;QAAgCgB,SAAS,EAAE;MAA3C,CAAf;IACH;;IACDF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEP,kBADC;MAEXQ,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEZ,kBAAZ;MAAgCa,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEN,eADC;MAEXO,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEX,kBAAZ;MAAgCY,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEL,gBADC;MAEXM,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEV,kBAAZ;MAAgCW,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEJ,oBADC;MAEXK,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAET,kBAAZ;MAAgCU,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEH,uBADC;MAEXI,SAAS,EAAE;IAFA,CAAf;;IAKA,IAAK7C,KAAK,CAACsB,QAAN,GAAiBtB,KAAK,CAACqB,KAAxB,GAAkCzB,OAAO,GAAG,CAAhD,EAAoD;MAAE;MAClD+C,SAAS,CAACjC,IAAV,CAAe;QAAEkC,QAAQ,EAAER,kBAAZ;QAAgCS,SAAS,EAAE;MAA3C,CAAf;IACH;EAEJ,CApCD,MAoCO;IACHrB,OAAO,GAAG,qCAAV,CADG,CAEH;;IAEAmB,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEZ,kBAAZ;MAAgCa,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEN,eADC;MAEXO,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEX,kBAAZ;MAAgCY,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEL,gBADC;MAEXM,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAEV,kBAAZ;MAAgCW,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEJ,oBADC;MAEXK,SAAS,EAAE;IAFA,CAAf;IAIAF,SAAS,CAACjC,IAAV,CAAe;MAAEkC,QAAQ,EAAET,kBAAZ;MAAgCU,SAAS,EAAE;IAA3C,CAAf;IACAF,SAAS,CAACjC,IAAV,CAAe;MACXkC,QAAQ,EAAEH,uBADC;MAEXI,SAAS,EAAE;IAFA,CAAf;;IAKA,IAAK7C,KAAK,CAACsB,QAAN,GAAiBtB,KAAK,CAACqB,KAAxB,GAAkCzB,OAAO,GAAG,CAAhD,EAAoD;MAAE;MAClD+C,SAAS,CAACjC,IAAV,CAAe;QAAEkC,QAAQ,EAAER,kBAAZ;QAAgCS,SAAS,EAAE;MAA3C,CAAf;IACH;EAEJ;;EAED,oBACI;IAAA,WAEK7C,KAAK,CAACC,UAAN,iBACG;MAAK,SAAS,EAAG,QAAOuB,OAAQ,EAAhC;MAAA,uBACI,QAAC,UAAD;QACI,KAAK,EAAEmB,SADX;QAEI,aAAa,EAAE3C,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B;MAFnB;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QAHR,EAUK,CAACrB,KAAK,CAACC,UAAP,iBACG;MAAK,SAAS,EAAG,QAAOuB,OAAQ,EAAhC;MAAA,uBACI,QAAC,UAAD;QACI,KAAK,EAAEmB,SADX;QAEI,aAAa,EAAE3C,KAAK,CAACkB,gBAAN,CAAuBlB,KAAK,CAACqB,KAA7B;MAFnB;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QAXR;EAAA,gBADJ;AAqBH,CAhJD;;MAAMO,wB;AAoJN,eAAe7B,eAAf"}, "metadata": {}, "sourceType": "module"}