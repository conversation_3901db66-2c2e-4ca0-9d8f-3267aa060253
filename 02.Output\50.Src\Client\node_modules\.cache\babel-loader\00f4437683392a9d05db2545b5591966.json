{"ast": null, "code": "'use strict';\n\nvar iframeUtils = require('./utils/iframe');\n\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\n\nFacadeJS.prototype._transportClose = function (code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\n\nFacadeJS.prototype._transportMessage = function (frame) {\n  iframeUtils.postMessage('t', frame);\n};\n\nFacadeJS.prototype._send = function (data) {\n  this._transport.send(data);\n};\n\nFacadeJS.prototype._close = function () {\n  this._transport.close();\n\n  this._transport.removeAllListeners();\n};\n\nmodule.exports = FacadeJS;", "map": {"version": 3, "names": ["iframe<PERSON><PERSON>s", "require", "FacadeJS", "transport", "_transport", "on", "_transportMessage", "bind", "_transportClose", "prototype", "code", "reason", "postMessage", "JSON", "stringify", "frame", "_send", "data", "send", "_close", "close", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/facade.js"], "sourcesContent": ["'use strict';\n\nvar iframeUtils = require('./utils/iframe')\n  ;\n\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\n\nFacadeJS.prototype._transportClose = function(code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function(frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function(data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function() {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\n\nmodule.exports = FacadeJS;\n"], "mappings": "AAAA;;AAEA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAD,CAAzB;;AAGA,SAASC,QAAT,CAAkBC,SAAlB,EAA6B;EAC3B,KAAKC,UAAL,GAAkBD,SAAlB;EACAA,SAAS,CAACE,EAAV,CAAa,SAAb,EAAwB,KAAKC,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B,CAAxB;EACAJ,SAAS,CAACE,EAAV,CAAa,OAAb,EAAsB,KAAKG,eAAL,CAAqBD,IAArB,CAA0B,IAA1B,CAAtB;AACD;;AAEDL,QAAQ,CAACO,SAAT,CAAmBD,eAAnB,GAAqC,UAASE,IAAT,EAAeC,MAAf,EAAuB;EAC1DX,WAAW,CAACY,WAAZ,CAAwB,GAAxB,EAA6BC,IAAI,CAACC,SAAL,CAAe,CAACJ,IAAD,EAAOC,MAAP,CAAf,CAA7B;AACD,CAFD;;AAGAT,QAAQ,CAACO,SAAT,CAAmBH,iBAAnB,GAAuC,UAASS,KAAT,EAAgB;EACrDf,WAAW,CAACY,WAAZ,CAAwB,GAAxB,EAA6BG,KAA7B;AACD,CAFD;;AAGAb,QAAQ,CAACO,SAAT,CAAmBO,KAAnB,GAA2B,UAASC,IAAT,EAAe;EACxC,KAAKb,UAAL,CAAgBc,IAAhB,CAAqBD,IAArB;AACD,CAFD;;AAGAf,QAAQ,CAACO,SAAT,CAAmBU,MAAnB,GAA4B,YAAW;EACrC,KAAKf,UAAL,CAAgBgB,KAAhB;;EACA,KAAKhB,UAAL,CAAgBiB,kBAAhB;AACD,CAHD;;AAKAC,MAAM,CAACC,OAAP,GAAiBrB,QAAjB"}, "metadata": {}, "sourceType": "script"}