{"ast": null, "code": "import React from'react';import Title from'./elements/Title';import BlinkBlock from'./elements/BlinkBlock';import{isValidSource}from'../utils/Util.js';/**\r\n * 着信状況コンテンツA<br>\r\n * propsは、「3.8着信状況コンテンツA情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module IncomingCallA\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var IncomingCallA=function IncomingCallA(props){var MAX_ROW=28;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Title,{title:'着信状況'}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-[2rem]\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-[repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)] items-center leading-[1]\",children:// 回線名間の間隔は4remとの仕様ですが、実際には、自動調整に任せる\nisValidSource(props)&&props.line_name.map(function(item,index){if(index>=MAX_ROW)return undefined;if(!item.display_text){return'';}var colIndex=index%4;var colStart;var colSpan='col-span-4';var font='text-6.5xl';switch(colIndex){case 1:colStart='col-start-6';break;case 2:colStart='col-start-11';break;case 3:colStart='col-start-16';break;default:colStart='col-start-1';break;}var isEng=checkEng(item.display_text);if(isEng){font='text-4.5xl';}var showBlock=[{showInfo:item,className:'my-[1.5rem] w-fit'}];return/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(colSpan,\" \").concat(colStart,\" \").concat(font),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,index:index,blink_setting:item.blink_setting},index)},index);})})})]});};//英語かを返す\nfunction checkEng(num){var reg=/^[A-Za-z]+$/;return reg.test(num);}export default IncomingCallA;", "map": {"version": 3, "names": ["React", "Title", "BlinkBlock", "isValidSource", "IncomingCallA", "props", "MAX_ROW", "line_name", "map", "item", "index", "undefined", "display_text", "colIndex", "colStart", "colSpan", "font", "isEng", "checkEng", "showBlock", "showInfo", "className", "blink_setting", "num", "reg", "test"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/IncomingCallA.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport BlinkBlock from './elements/BlinkBlock';\r\nimport { isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 着信状況コンテンツA<br>\r\n * propsは、「3.8着信状況コンテンツA情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module IncomingCallA\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst IncomingCallA = (props) => {\r\n  const MAX_ROW = 28;\r\n  return (\r\n    <>\r\n      <Title title={'着信状況'} />\r\n      <div className=\"mx-[2rem]\">\r\n        <div className=\"grid grid-cols-[repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)] items-center leading-[1]\">\r\n          {\r\n            // 回線名間の間隔は4remとの仕様ですが、実際には、自動調整に任せる\r\n            isValidSource(props) &&\r\n              props.line_name.map((item, index) => {\r\n                if (index >= MAX_ROW) return undefined;\r\n                \r\n                if (!item.display_text) {\r\n                  return '';\r\n                }\r\n\r\n                const colIndex = index % 4;\r\n                let colStart;\r\n                let colSpan = 'col-span-4';\r\n                let font = 'text-6.5xl';\r\n                switch (colIndex) {\r\n                  case 1:\r\n                    colStart = 'col-start-6';\r\n                    break;\r\n                  case 2:\r\n                    colStart = 'col-start-11';\r\n                    break;\r\n                  case 3:\r\n                    colStart = 'col-start-16';\r\n                    break;\r\n                  default:\r\n                    colStart = 'col-start-1';\r\n                    break;\r\n                }\r\n\r\n                const isEng = checkEng(item.display_text);\r\n                if (isEng) {\r\n                  font = 'text-4.5xl';\r\n                }\r\n\r\n                const showBlock = [\r\n                  { showInfo: item, className: 'my-[1.5rem] w-fit' },\r\n                ];\r\n\r\n                return (\r\n                  <div key={index} className={`${colSpan} ${colStart} ${font}`}>\r\n                    <BlinkBlock\r\n                      key={index}\r\n                      block={showBlock}\r\n                      index={index}\r\n                      blink_setting={item.blink_setting}\r\n                    />\r\n                  </div>\r\n                );\r\n              })\r\n          }\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\n//英語かを返す\r\nfunction checkEng(num) {\r\n  var reg = /^[A-Za-z]+$/;\r\n  return reg.test(num);\r\n}\r\n\r\nexport default IncomingCallA;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CACA,OAASC,aAAT,KAA8B,kBAA9B,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACC,KAAD,CAAW,CAC/B,GAAMC,QAAO,CAAG,EAAhB,CACA,mBACE,wCACE,KAAC,KAAD,EAAO,KAAK,CAAE,MAAd,EADF,cAEE,YAAK,SAAS,CAAC,WAAf,uBACE,YAAK,SAAS,CAAC,2KAAf,UAEI;AACAH,aAAa,CAACE,KAAD,CAAb,EACEA,KAAK,CAACE,SAAN,CAAgBC,GAAhB,CAAoB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACnC,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,GAAI,CAACF,IAAI,CAACG,YAAV,CAAwB,CACtB,MAAO,EAAP,CACD,CAED,GAAMC,SAAQ,CAAGH,KAAK,CAAG,CAAzB,CACA,GAAII,SAAJ,CACA,GAAIC,QAAO,CAAG,YAAd,CACA,GAAIC,KAAI,CAAG,YAAX,CACA,OAAQH,QAAR,EACE,IAAK,EAAL,CACEC,QAAQ,CAAG,aAAX,CACA,MACF,IAAK,EAAL,CACEA,QAAQ,CAAG,cAAX,CACA,MACF,IAAK,EAAL,CACEA,QAAQ,CAAG,cAAX,CACA,MACF,QACEA,QAAQ,CAAG,aAAX,CACA,MAZJ,CAeA,GAAMG,MAAK,CAAGC,QAAQ,CAACT,IAAI,CAACG,YAAN,CAAtB,CACA,GAAIK,KAAJ,CAAW,CACTD,IAAI,CAAG,YAAP,CACD,CAED,GAAMG,UAAS,CAAG,CAChB,CAAEC,QAAQ,CAAEX,IAAZ,CAAkBY,SAAS,CAAE,mBAA7B,CADgB,CAAlB,CAIA,mBACE,YAAiB,SAAS,WAAKN,OAAL,aAAgBD,QAAhB,aAA4BE,IAA5B,CAA1B,uBACE,KAAC,UAAD,EAEE,KAAK,CAAEG,SAFT,CAGE,KAAK,CAAET,KAHT,CAIE,aAAa,CAAED,IAAI,CAACa,aAJtB,EACOZ,KADP,CADF,EAAUA,KAAV,CADF,CAUD,CA7CD,CAJN,EADF,EAFF,GADF,CA2DD,CA7DD,CA+DA;AACA,QAASQ,SAAT,CAAkBK,GAAlB,CAAuB,CACrB,GAAIC,IAAG,CAAG,aAAV,CACA,MAAOA,IAAG,CAACC,IAAJ,CAASF,GAAT,CAAP,CACD,CAED,cAAenB,cAAf"}, "metadata": {}, "sourceType": "module"}