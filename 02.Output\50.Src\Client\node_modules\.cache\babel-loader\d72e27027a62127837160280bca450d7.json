{"ast": null, "code": "'use strict';\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\n\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n  min = min !== undefined ? min : 1000;\n  return function push(chunkLength) {\n    const now = Date.now();\n    const startedAt = timestamps[tail];\n\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n    let i = tail;\n    let bytesCount = 0;\n\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n\n    head = (head + 1) % samplesCount;\n\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n\n    if (now - firstSampleTS < min) {\n      return;\n    }\n\n    const passed = startedAt && now - startedAt;\n    return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\n\nexport default speedometer;", "map": {"version": 3, "names": ["speedometer", "samplesCount", "min", "bytes", "Array", "timestamps", "head", "tail", "firstSampleTS", "undefined", "push", "chunkLength", "now", "Date", "startedAt", "i", "bytesCount", "passed", "Math", "round"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/speedometer.js"], "sourcesContent": ["'use strict';\n\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n\n  min = min !== undefined ? min : 1000;\n\n  return function push(chunkLength) {\n    const now = Date.now();\n\n    const startedAt = timestamps[tail];\n\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n\n    let i = tail;\n    let bytesCount = 0;\n\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n\n    head = (head + 1) % samplesCount;\n\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n\n    if (now - firstSampleTS < min) {\n      return;\n    }\n\n    const passed = startedAt && now - startedAt;\n\n    return  passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\n\nexport default speedometer;\n"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASA,WAAT,CAAqBC,YAArB,EAAmCC,GAAnC,EAAwC;EACtCD,YAAY,GAAGA,YAAY,IAAI,EAA/B;EACA,MAAME,KAAK,GAAG,IAAIC,KAAJ,CAAUH,YAAV,CAAd;EACA,MAAMI,UAAU,GAAG,IAAID,KAAJ,CAAUH,YAAV,CAAnB;EACA,IAAIK,IAAI,GAAG,CAAX;EACA,IAAIC,IAAI,GAAG,CAAX;EACA,IAAIC,aAAJ;EAEAN,GAAG,GAAGA,GAAG,KAAKO,SAAR,GAAoBP,GAApB,GAA0B,IAAhC;EAEA,OAAO,SAASQ,IAAT,CAAcC,WAAd,EAA2B;IAChC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;IAEA,MAAME,SAAS,GAAGT,UAAU,CAACE,IAAD,CAA5B;;IAEA,IAAI,CAACC,aAAL,EAAoB;MAClBA,aAAa,GAAGI,GAAhB;IACD;;IAEDT,KAAK,CAACG,IAAD,CAAL,GAAcK,WAAd;IACAN,UAAU,CAACC,IAAD,CAAV,GAAmBM,GAAnB;IAEA,IAAIG,CAAC,GAAGR,IAAR;IACA,IAAIS,UAAU,GAAG,CAAjB;;IAEA,OAAOD,CAAC,KAAKT,IAAb,EAAmB;MACjBU,UAAU,IAAIb,KAAK,CAACY,CAAC,EAAF,CAAnB;MACAA,CAAC,GAAGA,CAAC,GAAGd,YAAR;IACD;;IAEDK,IAAI,GAAG,CAACA,IAAI,GAAG,CAAR,IAAaL,YAApB;;IAEA,IAAIK,IAAI,KAAKC,IAAb,EAAmB;MACjBA,IAAI,GAAG,CAACA,IAAI,GAAG,CAAR,IAAaN,YAApB;IACD;;IAED,IAAIW,GAAG,GAAGJ,aAAN,GAAsBN,GAA1B,EAA+B;MAC7B;IACD;;IAED,MAAMe,MAAM,GAAGH,SAAS,IAAIF,GAAG,GAAGE,SAAlC;IAEA,OAAQG,MAAM,GAAGC,IAAI,CAACC,KAAL,CAAWH,UAAU,GAAG,IAAb,GAAoBC,MAA/B,CAAH,GAA4CR,SAA1D;EACD,CAjCD;AAkCD;;AAED,eAAeT,WAAf"}, "metadata": {}, "sourceType": "module"}