{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\n\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(function (match) {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\n\n\nfunction arrayToObject(arr) {\n  var obj = {};\n  var keys = Object.keys(arr);\n  var i;\n  var len = keys.length;\n  var key;\n\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n\n  return obj;\n}\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\n\n\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    var name = path[index++];\n    var isNumericKey = Number.isFinite(+name);\n    var isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n\n      return !isNumericKey;\n    }\n\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n\n    var result = buildPath(path, value, target[name], index);\n\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n\n    return !isNumericKey;\n  }\n\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    var obj = {};\n    utils.forEachEntry(formData, function (name, value) {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n    return obj;\n  }\n\n  return null;\n}\n\nexport default formDataToJSON;", "map": {"version": 3, "names": ["utils", "parsePropPath", "name", "matchAll", "map", "match", "arrayToObject", "arr", "obj", "keys", "Object", "i", "len", "length", "key", "formDataToJSON", "formData", "buildPath", "path", "value", "target", "index", "isNumericKey", "Number", "isFinite", "isLast", "isArray", "hasOwnProp", "isObject", "result", "isFormData", "isFunction", "entries", "forEachEntry"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/formDataToJSON.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(match => {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\nfunction arrayToObject(arr) {\n  const obj = {};\n  const keys = Object.keys(arr);\n  let i;\n  const len = keys.length;\n  let key;\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n  return obj;\n}\n\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    let name = path[index++];\n    const isNumericKey = Number.isFinite(+name);\n    const isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n\n      return !isNumericKey;\n    }\n\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n\n    const result = buildPath(path, value, target[name], index);\n\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n\n    return !isNumericKey;\n  }\n\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    const obj = {};\n\n    utils.forEachEntry(formData, (name, value) => {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n\n    return obj;\n  }\n\n  return null;\n}\n\nexport default formDataToJSON;\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,aAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;EAC3B;EACA;EACA;EACA;EACA,OAAOF,KAAK,CAACG,QAAN,CAAe,eAAf,EAAgCD,IAAhC,EAAsCE,GAAtC,CAA0C,UAAAC,KAAK,EAAI;IACxD,OAAOA,KAAK,CAAC,CAAD,CAAL,KAAa,IAAb,GAAoB,EAApB,GAAyBA,KAAK,CAAC,CAAD,CAAL,IAAYA,KAAK,CAAC,CAAD,CAAjD;EACD,CAFM,CAAP;AAGD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,IAAMC,GAAG,GAAG,EAAZ;EACA,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYF,GAAZ,CAAb;EACA,IAAII,CAAJ;EACA,IAAMC,GAAG,GAAGH,IAAI,CAACI,MAAjB;EACA,IAAIC,GAAJ;;EACA,KAAKH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,GAAhB,EAAqBD,CAAC,EAAtB,EAA0B;IACxBG,GAAG,GAAGL,IAAI,CAACE,CAAD,CAAV;IACAH,GAAG,CAACM,GAAD,CAAH,GAAWP,GAAG,CAACO,GAAD,CAAd;EACD;;EACD,OAAON,GAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,cAAT,CAAwBC,QAAxB,EAAkC;EAChC,SAASC,SAAT,CAAmBC,IAAnB,EAAyBC,KAAzB,EAAgCC,MAAhC,EAAwCC,KAAxC,EAA+C;IAC7C,IAAInB,IAAI,GAAGgB,IAAI,CAACG,KAAK,EAAN,CAAf;IACA,IAAMC,YAAY,GAAGC,MAAM,CAACC,QAAP,CAAgB,CAACtB,IAAjB,CAArB;IACA,IAAMuB,MAAM,GAAGJ,KAAK,IAAIH,IAAI,CAACL,MAA7B;IACAX,IAAI,GAAG,CAACA,IAAD,IAASF,KAAK,CAAC0B,OAAN,CAAcN,MAAd,CAAT,GAAiCA,MAAM,CAACP,MAAxC,GAAiDX,IAAxD;;IAEA,IAAIuB,MAAJ,EAAY;MACV,IAAIzB,KAAK,CAAC2B,UAAN,CAAiBP,MAAjB,EAAyBlB,IAAzB,CAAJ,EAAoC;QAClCkB,MAAM,CAAClB,IAAD,CAAN,GAAe,CAACkB,MAAM,CAAClB,IAAD,CAAP,EAAeiB,KAAf,CAAf;MACD,CAFD,MAEO;QACLC,MAAM,CAAClB,IAAD,CAAN,GAAeiB,KAAf;MACD;;MAED,OAAO,CAACG,YAAR;IACD;;IAED,IAAI,CAACF,MAAM,CAAClB,IAAD,CAAP,IAAiB,CAACF,KAAK,CAAC4B,QAAN,CAAeR,MAAM,CAAClB,IAAD,CAArB,CAAtB,EAAoD;MAClDkB,MAAM,CAAClB,IAAD,CAAN,GAAe,EAAf;IACD;;IAED,IAAM2B,MAAM,GAAGZ,SAAS,CAACC,IAAD,EAAOC,KAAP,EAAcC,MAAM,CAAClB,IAAD,CAApB,EAA4BmB,KAA5B,CAAxB;;IAEA,IAAIQ,MAAM,IAAI7B,KAAK,CAAC0B,OAAN,CAAcN,MAAM,CAAClB,IAAD,CAApB,CAAd,EAA2C;MACzCkB,MAAM,CAAClB,IAAD,CAAN,GAAeI,aAAa,CAACc,MAAM,CAAClB,IAAD,CAAP,CAA5B;IACD;;IAED,OAAO,CAACoB,YAAR;EACD;;EAED,IAAItB,KAAK,CAAC8B,UAAN,CAAiBd,QAAjB,KAA8BhB,KAAK,CAAC+B,UAAN,CAAiBf,QAAQ,CAACgB,OAA1B,CAAlC,EAAsE;IACpE,IAAMxB,GAAG,GAAG,EAAZ;IAEAR,KAAK,CAACiC,YAAN,CAAmBjB,QAAnB,EAA6B,UAACd,IAAD,EAAOiB,KAAP,EAAiB;MAC5CF,SAAS,CAAChB,aAAa,CAACC,IAAD,CAAd,EAAsBiB,KAAtB,EAA6BX,GAA7B,EAAkC,CAAlC,CAAT;IACD,CAFD;IAIA,OAAOA,GAAP;EACD;;EAED,OAAO,IAAP;AACD;;AAED,eAAeO,cAAf"}, "metadata": {}, "sourceType": "module"}