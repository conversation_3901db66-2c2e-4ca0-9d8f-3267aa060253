{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nimport { getTargetElement } from '../utils/domTarget';\n\nvar useFullscreen = function useFullscreen(target, options) {\n  var _a = options || {},\n      onExit = _a.onExit,\n      onEnter = _a.onEnter;\n\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n\n  var _b = __read(useState(false), 2),\n      state = _b[0],\n      setState = _b[1];\n\n  var onChange = function onChange() {\n    var _a, _b, _c;\n\n    if (screenfull.isEnabled) {\n      var el = getTargetElement(target);\n\n      if (!screenfull.element) {\n        (_a = onExitRef.current) === null || _a === void 0 ? void 0 : _a.call(onExitRef);\n        setState(false);\n        screenfull.off('change', onChange);\n      } else {\n        var isFullscreen = screenfull.element === el;\n\n        if (isFullscreen) {\n          (_b = onEnterRef.current) === null || _b === void 0 ? void 0 : _b.call(onEnterRef);\n        } else {\n          (_c = onExitRef.current) === null || _c === void 0 ? void 0 : _c.call(onExitRef);\n        }\n\n        setState(isFullscreen);\n      }\n    }\n  };\n\n  var enterFullscreen = function enterFullscreen() {\n    var el = getTargetElement(target);\n\n    if (!el) {\n      return;\n    }\n\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n        screenfull.on('change', onChange);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n\n  var exitFullscreen = function exitFullscreen() {\n    var el = getTargetElement(target);\n\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n\n  var toggleFullscreen = function toggleFullscreen() {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n\n  useUnmount(function () {\n    if (screenfull.isEnabled) {\n      screenfull.off('change', onChange);\n    }\n  });\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\n\nexport default useFullscreen;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "screenfull", "useLatest", "useMemoizedFn", "useUnmount", "getTargetElement", "useFullscreen", "target", "options", "_a", "onExit", "onEnter", "onExitRef", "onEnterRef", "_b", "state", "setState", "onChange", "_c", "isEnabled", "el", "element", "current", "off", "isFullscreen", "enterFullscreen", "request", "on", "console", "exitFullscreen", "exit", "toggleFullscreen"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useFullscreen/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nimport { getTargetElement } from '../utils/domTarget';\nvar useFullscreen = function useFullscreen(target, options) {\n  var _a = options || {},\n    onExit = _a.onExit,\n    onEnter = _a.onEnter;\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n  var _b = __read(useState(false), 2),\n    state = _b[0],\n    setState = _b[1];\n  var onChange = function onChange() {\n    var _a, _b, _c;\n    if (screenfull.isEnabled) {\n      var el = getTargetElement(target);\n      if (!screenfull.element) {\n        (_a = onExitRef.current) === null || _a === void 0 ? void 0 : _a.call(onExitRef);\n        setState(false);\n        screenfull.off('change', onChange);\n      } else {\n        var isFullscreen = screenfull.element === el;\n        if (isFullscreen) {\n          (_b = onEnterRef.current) === null || _b === void 0 ? void 0 : _b.call(onEnterRef);\n        } else {\n          (_c = onExitRef.current) === null || _c === void 0 ? void 0 : _c.call(onExitRef);\n        }\n        setState(isFullscreen);\n      }\n    }\n  };\n  var enterFullscreen = function enterFullscreen() {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n        screenfull.on('change', onChange);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  var exitFullscreen = function exitFullscreen() {\n    var el = getTargetElement(target);\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n  var toggleFullscreen = function toggleFullscreen() {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n  useUnmount(function () {\n    if (screenfull.isEnabled) {\n      screenfull.off('change', onChange);\n    }\n  });\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\nexport default useFullscreen;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,UAAP,MAAuB,YAAvB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,UAAP,MAAuB,eAAvB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;;AACA,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,MAAvB,EAA+BC,OAA/B,EAAwC;EAC1D,IAAIC,EAAE,GAAGD,OAAO,IAAI,EAApB;EAAA,IACEE,MAAM,GAAGD,EAAE,CAACC,MADd;EAAA,IAEEC,OAAO,GAAGF,EAAE,CAACE,OAFf;;EAGA,IAAIC,SAAS,GAAGV,SAAS,CAACQ,MAAD,CAAzB;EACA,IAAIG,UAAU,GAAGX,SAAS,CAACS,OAAD,CAA1B;;EACA,IAAIG,EAAE,GAAG9B,MAAM,CAACgB,QAAQ,CAAC,KAAD,CAAT,EAAkB,CAAlB,CAAf;EAAA,IACEe,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,QAAQ,GAAG,SAASA,QAAT,GAAoB;IACjC,IAAIR,EAAJ,EAAQK,EAAR,EAAYI,EAAZ;;IACA,IAAIjB,UAAU,CAACkB,SAAf,EAA0B;MACxB,IAAIC,EAAE,GAAGf,gBAAgB,CAACE,MAAD,CAAzB;;MACA,IAAI,CAACN,UAAU,CAACoB,OAAhB,EAAyB;QACvB,CAACZ,EAAE,GAAGG,SAAS,CAACU,OAAhB,MAA6B,IAA7B,IAAqCb,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAAClB,IAAH,CAAQqB,SAAR,CAA9D;QACAI,QAAQ,CAAC,KAAD,CAAR;QACAf,UAAU,CAACsB,GAAX,CAAe,QAAf,EAAyBN,QAAzB;MACD,CAJD,MAIO;QACL,IAAIO,YAAY,GAAGvB,UAAU,CAACoB,OAAX,KAAuBD,EAA1C;;QACA,IAAII,YAAJ,EAAkB;UAChB,CAACV,EAAE,GAAGD,UAAU,CAACS,OAAjB,MAA8B,IAA9B,IAAsCR,EAAE,KAAK,KAAK,CAAlD,GAAsD,KAAK,CAA3D,GAA+DA,EAAE,CAACvB,IAAH,CAAQsB,UAAR,CAA/D;QACD,CAFD,MAEO;UACL,CAACK,EAAE,GAAGN,SAAS,CAACU,OAAhB,MAA6B,IAA7B,IAAqCJ,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAAC3B,IAAH,CAAQqB,SAAR,CAA9D;QACD;;QACDI,QAAQ,CAACQ,YAAD,CAAR;MACD;IACF;EACF,CAlBD;;EAmBA,IAAIC,eAAe,GAAG,SAASA,eAAT,GAA2B;IAC/C,IAAIL,EAAE,GAAGf,gBAAgB,CAACE,MAAD,CAAzB;;IACA,IAAI,CAACa,EAAL,EAAS;MACP;IACD;;IACD,IAAInB,UAAU,CAACkB,SAAf,EAA0B;MACxB,IAAI;QACFlB,UAAU,CAACyB,OAAX,CAAmBN,EAAnB;QACAnB,UAAU,CAAC0B,EAAX,CAAc,QAAd,EAAwBV,QAAxB;MACD,CAHD,CAGE,OAAOlB,KAAP,EAAc;QACd6B,OAAO,CAAC7B,KAAR,CAAcA,KAAd;MACD;IACF;EACF,CAbD;;EAcA,IAAI8B,cAAc,GAAG,SAASA,cAAT,GAA0B;IAC7C,IAAIT,EAAE,GAAGf,gBAAgB,CAACE,MAAD,CAAzB;;IACA,IAAIN,UAAU,CAACkB,SAAX,IAAwBlB,UAAU,CAACoB,OAAX,KAAuBD,EAAnD,EAAuD;MACrDnB,UAAU,CAAC6B,IAAX;IACD;EACF,CALD;;EAMA,IAAIC,gBAAgB,GAAG,SAASA,gBAAT,GAA4B;IACjD,IAAIhB,KAAJ,EAAW;MACTc,cAAc;IACf,CAFD,MAEO;MACLJ,eAAe;IAChB;EACF,CAND;;EAOArB,UAAU,CAAC,YAAY;IACrB,IAAIH,UAAU,CAACkB,SAAf,EAA0B;MACxBlB,UAAU,CAACsB,GAAX,CAAe,QAAf,EAAyBN,QAAzB;IACD;EACF,CAJS,CAAV;EAKA,OAAO,CAACF,KAAD,EAAQ;IACbU,eAAe,EAAEtB,aAAa,CAACsB,eAAD,CADjB;IAEbI,cAAc,EAAE1B,aAAa,CAAC0B,cAAD,CAFhB;IAGbE,gBAAgB,EAAE5B,aAAa,CAAC4B,gBAAD,CAHlB;IAIbZ,SAAS,EAAElB,UAAU,CAACkB;EAJT,CAAR,CAAP;AAMD,CAlED;;AAmEA,eAAeb,aAAf"}, "metadata": {}, "sourceType": "module"}