{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    urlUtils = require('../../utils/url'),\n    BufferedSender = require('./buffered-sender'),\n    Polling = require('./polling');\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\n\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function (msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function (code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\n\ninherits(SenderReceiver, BufferedSender);\n\nSenderReceiver.prototype.close = function () {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\n\nmodule.exports = SenderReceiver;", "map": {"version": 3, "names": ["inherits", "require", "urlUtils", "BufferedSender", "Polling", "debug", "process", "env", "NODE_ENV", "SenderReceiver", "transUrl", "urlSuffix", "senderFunc", "Receiver", "AjaxObject", "pollUrl", "addPath", "self", "call", "poll", "on", "msg", "emit", "once", "code", "reason", "close", "prototype", "removeAllListeners", "abort", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/lib/sender-receiver.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , BufferedSender = require('./buffered-sender')\n  , Polling = require('./polling')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\n\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function(msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function(code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\n\ninherits(SenderReceiver, BufferedSender);\n\nSenderReceiver.prototype.close = function() {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\n\nmodule.exports = SenderReceiver;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,QAAQ,GAAGD,OAAO,CAAC,iBAAD,CADtB;AAAA,IAEIE,cAAc,GAAGF,OAAO,CAAC,mBAAD,CAF5B;AAAA,IAGIG,OAAO,GAAGH,OAAO,CAAC,WAAD,CAHrB;;AAMA,IAAII,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGJ,OAAO,CAAC,OAAD,CAAP,CAAiB,+BAAjB,CAAR;AACD;;AAED,SAASQ,cAAT,CAAwBC,QAAxB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyDC,QAAzD,EAAmEC,UAAnE,EAA+E;EAC7E,IAAIC,OAAO,GAAGb,QAAQ,CAACc,OAAT,CAAiBN,QAAjB,EAA2BC,SAA3B,CAAd;EACAN,KAAK,CAACU,OAAD,CAAL;EACA,IAAIE,IAAI,GAAG,IAAX;EACAd,cAAc,CAACe,IAAf,CAAoB,IAApB,EAA0BR,QAA1B,EAAoCE,UAApC;EAEA,KAAKO,IAAL,GAAY,IAAIf,OAAJ,CAAYS,QAAZ,EAAsBE,OAAtB,EAA+BD,UAA/B,CAAZ;EACA,KAAKK,IAAL,CAAUC,EAAV,CAAa,SAAb,EAAwB,UAASC,GAAT,EAAc;IACpChB,KAAK,CAAC,cAAD,EAAiBgB,GAAjB,CAAL;IACAJ,IAAI,CAACK,IAAL,CAAU,SAAV,EAAqBD,GAArB;EACD,CAHD;EAIA,KAAKF,IAAL,CAAUI,IAAV,CAAe,OAAf,EAAwB,UAASC,IAAT,EAAeC,MAAf,EAAuB;IAC7CpB,KAAK,CAAC,YAAD,EAAemB,IAAf,EAAqBC,MAArB,CAAL;IACAR,IAAI,CAACE,IAAL,GAAY,IAAZ;IACAF,IAAI,CAACK,IAAL,CAAU,OAAV,EAAmBE,IAAnB,EAAyBC,MAAzB;IACAR,IAAI,CAACS,KAAL;EACD,CALD;AAMD;;AAED1B,QAAQ,CAACS,cAAD,EAAiBN,cAAjB,CAAR;;AAEAM,cAAc,CAACkB,SAAf,CAAyBD,KAAzB,GAAiC,YAAW;EAC1CvB,cAAc,CAACwB,SAAf,CAAyBD,KAAzB,CAA+BR,IAA/B,CAAoC,IAApC;EACAb,KAAK,CAAC,OAAD,CAAL;EACA,KAAKuB,kBAAL;;EACA,IAAI,KAAKT,IAAT,EAAe;IACb,KAAKA,IAAL,CAAUU,KAAV;IACA,KAAKV,IAAL,GAAY,IAAZ;EACD;AACF,CARD;;AAUAW,MAAM,CAACC,OAAP,GAAiBtB,cAAjB"}, "metadata": {}, "sourceType": "script"}