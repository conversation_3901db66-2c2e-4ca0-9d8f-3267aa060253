{"ast": null, "code": "'use strict';\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\n\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '') : baseURL;\n}", "map": {"version": 3, "names": ["combineURLs", "baseURL", "relativeURL", "replace"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/combineURLs.js"], "sourcesContent": ["'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/+$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n}\n"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASA,WAAT,CAAqBC,OAArB,EAA8BC,WAA9B,EAA2C;EACxD,OAAOA,WAAW,GACdD,OAAO,CAACE,OAAR,CAAgB,MAAhB,EAAwB,EAAxB,IAA8B,GAA9B,GAAoCD,WAAW,CAACC,OAAZ,CAAoB,MAApB,EAA4B,EAA5B,CADtB,GAEdF,OAFJ;AAGD"}, "metadata": {}, "sourceType": "module"}