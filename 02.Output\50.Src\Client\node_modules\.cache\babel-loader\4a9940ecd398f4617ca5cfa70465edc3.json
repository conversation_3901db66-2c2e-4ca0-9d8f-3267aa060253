{"ast": null, "code": "/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\n\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n\n  return index;\n}\n\nmodule.exports = trimmedEndIndex;", "map": {"version": 3, "names": ["reWhitespace", "trimmedEndIndex", "string", "index", "length", "test", "char<PERSON>t", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_trimmedEndIndex.js"], "sourcesContent": ["/** Used to match a single whitespace character. */\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nmodule.exports = trimmedEndIndex;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG,IAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,eAAT,CAAyBC,MAAzB,EAAiC;EAC/B,IAAIC,KAAK,GAAGD,MAAM,CAACE,MAAnB;;EAEA,OAAOD,KAAK,MAAMH,YAAY,CAACK,IAAb,CAAkBH,MAAM,CAACI,MAAP,CAAcH,KAAd,CAAlB,CAAlB,EAA2D,CAAE;;EAC7D,OAAOA,KAAP;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiBP,eAAjB"}, "metadata": {}, "sourceType": "script"}