{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js'; // RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\n\nvar ignoreDuplicateOf = utils.toObjectSet(['age', 'authorization', 'content-length', 'content-type', 'etag', 'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since', 'last-modified', 'location', 'max-forwards', 'proxy-authorization', 'referer', 'retry-after', 'user-agent']);\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\n\nexport default (function (rawHeaders) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n\n    if (!key || parsed[key] && ignoreDuplicateOf[key]) {\n      return;\n    }\n\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n  return parsed;\n});", "map": {"version": 3, "names": ["utils", "ignoreDuplicateOf", "toObjectSet", "rawHeaders", "parsed", "key", "val", "i", "split", "for<PERSON>ach", "parser", "line", "indexOf", "substring", "trim", "toLowerCase", "push"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/parseHeaders.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils.toObjectSet([\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n]);\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\nexport default rawHeaders => {\n  const parsed = {};\n  let key;\n  let val;\n  let i;\n\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n\n    if (!key || (parsed[key] && ignoreDuplicateOf[key])) {\n      return;\n    }\n\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n\n  return parsed;\n};\n"], "mappings": "AAAA;;AAEA,OAAOA,KAAP,MAAkB,eAAlB,C,CAEA;AACA;;AACA,IAAMC,iBAAiB,GAAGD,KAAK,CAACE,WAAN,CAAkB,CAC1C,KAD0C,EACnC,eADmC,EAClB,gBADkB,EACA,cADA,EACgB,MADhB,EAE1C,SAF0C,EAE/B,MAF+B,EAEvB,MAFuB,EAEf,mBAFe,EAEM,qBAFN,EAG1C,eAH0C,EAGzB,UAHyB,EAGb,cAHa,EAGG,qBAHH,EAI1C,SAJ0C,EAI/B,aAJ+B,EAIhB,YAJgB,CAAlB,CAA1B;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,gBAAe,UAAAC,UAAU,EAAI;EAC3B,IAAMC,MAAM,GAAG,EAAf;EACA,IAAIC,GAAJ;EACA,IAAIC,GAAJ;EACA,IAAIC,CAAJ;EAEAJ,UAAU,IAAIA,UAAU,CAACK,KAAX,CAAiB,IAAjB,EAAuBC,OAAvB,CAA+B,SAASC,MAAT,CAAgBC,IAAhB,EAAsB;IACjEJ,CAAC,GAAGI,IAAI,CAACC,OAAL,CAAa,GAAb,CAAJ;IACAP,GAAG,GAAGM,IAAI,CAACE,SAAL,CAAe,CAAf,EAAkBN,CAAlB,EAAqBO,IAArB,GAA4BC,WAA5B,EAAN;IACAT,GAAG,GAAGK,IAAI,CAACE,SAAL,CAAeN,CAAC,GAAG,CAAnB,EAAsBO,IAAtB,EAAN;;IAEA,IAAI,CAACT,GAAD,IAASD,MAAM,CAACC,GAAD,CAAN,IAAeJ,iBAAiB,CAACI,GAAD,CAA7C,EAAqD;MACnD;IACD;;IAED,IAAIA,GAAG,KAAK,YAAZ,EAA0B;MACxB,IAAID,MAAM,CAACC,GAAD,CAAV,EAAiB;QACfD,MAAM,CAACC,GAAD,CAAN,CAAYW,IAAZ,CAAiBV,GAAjB;MACD,CAFD,MAEO;QACLF,MAAM,CAACC,GAAD,CAAN,GAAc,CAACC,GAAD,CAAd;MACD;IACF,CAND,MAMO;MACLF,MAAM,CAACC,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAAN,GAAcD,MAAM,CAACC,GAAD,CAAN,GAAc,IAAd,GAAqBC,GAAnC,GAAyCA,GAAvD;IACD;EACF,CAlBa,CAAd;EAoBA,OAAOF,MAAP;AACD,CA3BD"}, "metadata": {}, "sourceType": "module"}