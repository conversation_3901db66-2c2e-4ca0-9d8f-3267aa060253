{"ast": null, "code": "import { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\n\nfunction useMemoizedFn(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMemoizedFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n\n  var fnRef = useRef(fn); // why not write `fnRef.current = fn`?\n  // https://github.com/alibaba/hooks/issues/728\n\n  fnRef.current = useMemo(function () {\n    return fn;\n  }, [fn]);\n  var memoizedFn = useRef();\n\n  if (!memoizedFn.current) {\n    memoizedFn.current = function () {\n      var args = [];\n\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n\n      return fnRef.current.apply(this, args);\n    };\n  }\n\n  return memoizedFn.current;\n}\n\nexport default useMemoizedFn;", "map": {"version": 3, "names": ["useMemo", "useRef", "isFunction", "isDev", "useMemoizedFn", "fn", "console", "error", "concat", "fnRef", "current", "memoizedFn", "args", "_i", "arguments", "length", "apply"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useMemoizedFn/index.js"], "sourcesContent": ["import { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useMemoizedFn(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMemoizedFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useRef(fn);\n  // why not write `fnRef.current = fn`?\n  // https://github.com/alibaba/hooks/issues/728\n  fnRef.current = useMemo(function () {\n    return fn;\n  }, [fn]);\n  var memoizedFn = useRef();\n  if (!memoizedFn.current) {\n    memoizedFn.current = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(this, args);\n    };\n  }\n  return memoizedFn.current;\n}\nexport default useMemoizedFn;"], "mappings": "AAAA,SAASA,OAAT,EAAkBC,MAAlB,QAAgC,OAAhC;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,KAAP,MAAkB,gBAAlB;;AACA,SAASC,aAAT,CAAuBC,EAAvB,EAA2B;EACzB,IAAIF,KAAJ,EAAW;IACT,IAAI,CAACD,UAAU,CAACG,EAAD,CAAf,EAAqB;MACnBC,OAAO,CAACC,KAAR,CAAc,uDAAuDC,MAAvD,CAA8D,OAAOH,EAArE,CAAd;IACD;EACF;;EACD,IAAII,KAAK,GAAGR,MAAM,CAACI,EAAD,CAAlB,CANyB,CAOzB;EACA;;EACAI,KAAK,CAACC,OAAN,GAAgBV,OAAO,CAAC,YAAY;IAClC,OAAOK,EAAP;EACD,CAFsB,EAEpB,CAACA,EAAD,CAFoB,CAAvB;EAGA,IAAIM,UAAU,GAAGV,MAAM,EAAvB;;EACA,IAAI,CAACU,UAAU,CAACD,OAAhB,EAAyB;IACvBC,UAAU,CAACD,OAAX,GAAqB,YAAY;MAC/B,IAAIE,IAAI,GAAG,EAAX;;MACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGC,SAAS,CAACC,MAAhC,EAAwCF,EAAE,EAA1C,EAA8C;QAC5CD,IAAI,CAACC,EAAD,CAAJ,GAAWC,SAAS,CAACD,EAAD,CAApB;MACD;;MACD,OAAOJ,KAAK,CAACC,OAAN,CAAcM,KAAd,CAAoB,IAApB,EAA0BJ,IAA1B,CAAP;IACD,CAND;EAOD;;EACD,OAAOD,UAAU,CAACD,OAAlB;AACD;;AACD,eAAeN,aAAf"}, "metadata": {}, "sourceType": "module"}