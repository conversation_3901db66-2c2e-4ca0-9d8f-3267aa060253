{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\n\nvar useResetState = function useResetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var resetState = useMemoizedFn(function () {\n    setState(initialState);\n  });\n  return [state, setState, resetState];\n};\n\nexport default useResetState;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useState", "useMemoizedFn", "useResetState", "initialState", "_a", "state", "setState", "resetState"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useResetState/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nvar useResetState = function useResetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var resetState = useMemoizedFn(function () {\n    setState(initialState);\n  });\n  return [state, setState, resetState];\n};\nexport default useResetState;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,QAAT,QAAyB,OAAzB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;;AACA,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,YAAvB,EAAqC;EACvD,IAAIC,EAAE,GAAGpB,MAAM,CAACgB,QAAQ,CAACG,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEE,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,UAAU,GAAGN,aAAa,CAAC,YAAY;IACzCK,QAAQ,CAACH,YAAD,CAAR;EACD,CAF6B,CAA9B;EAGA,OAAO,CAACE,KAAD,EAAQC,QAAR,EAAkBC,UAAlB,CAAP;AACD,CARD;;AASA,eAAeL,aAAf"}, "metadata": {}, "sourceType": "module"}