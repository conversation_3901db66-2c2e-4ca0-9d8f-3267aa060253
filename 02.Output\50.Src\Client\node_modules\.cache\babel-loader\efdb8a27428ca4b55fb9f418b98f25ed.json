{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useRef, useState } from 'react'; // {[path]: count}\n// remove external when no used\n\nvar EXTERNAL_USED_COUNT = {};\n\nvar loadScript = function loadScript(path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\n\nvar loadCss = function loadCss(path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    }); // IE9+\n\n    var isLegacyIECss = ('hideFocus' in newCss_1); // use preload in IE Edge (to detect load errors)\n\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\n\nvar useExternal = function useExternal(path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n      status = _a[0],\n      setStatus = _a[1];\n\n  var ref = useRef();\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n\n    var pathname = path.replace(/[|#].*$/, '');\n\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n\n    if (!ref.current) {\n      return;\n    }\n\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n\n    var handler = function handler(event) {\n      var _a;\n\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n\n      if (EXTERNAL_USED_COUNT[path] === 0) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\n\nexport default useExternal;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useRef", "useState", "EXTERNAL_USED_COUNT", "loadScript", "path", "props", "script", "document", "querySelector", "concat", "newScript_1", "createElement", "src", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "ref", "status", "getAttribute", "loadCss", "css", "newCss_1", "rel", "href", "isLegacyIECss", "relList", "as", "head", "useExternal", "options", "_a", "setStatus", "pathname", "replace", "type", "test", "result", "current", "js", "console", "undefined", "handler", "event", "targetStatus", "addEventListener", "_b", "_c", "removeEventListener", "remove"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useExternal/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useRef, useState } from 'react';\n// {[path]: count}\n// remove external when no used\nvar EXTERNAL_USED_COUNT = {};\nvar loadScript = function loadScript(path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\nvar loadCss = function loadCss(path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    });\n    // IE9+\n    var isLegacyIECss = ('hideFocus' in newCss_1);\n    // use preload in IE Edge (to detect load errors)\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\nvar useExternal = function useExternal(path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n    status = _a[0],\n    setStatus = _a[1];\n  var ref = useRef();\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n    var pathname = path.replace(/[|#].*$/, '');\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n    if (!ref.current) {\n      return;\n    }\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n    var handler = function handler(event) {\n      var _a;\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n      if (EXTERNAL_USED_COUNT[path] === 0) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\nexport default useExternal;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,MAApB,EAA4BC,QAA5B,QAA4C,OAA5C,C,CACA;AACA;;AACA,IAAIC,mBAAmB,GAAG,EAA1B;;AACA,IAAIC,UAAU,GAAG,SAASA,UAAT,CAAoBC,IAApB,EAA0BC,KAA1B,EAAiC;EAChD,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,gBAAgBC,MAAhB,CAAuBL,IAAvB,EAA6B,KAA7B,CAAvB,CAAb;;EACA,IAAI,CAACE,MAAL,EAAa;IACX,IAAII,WAAW,GAAGH,QAAQ,CAACI,aAAT,CAAuB,QAAvB,CAAlB;IACAD,WAAW,CAACE,GAAZ,GAAkBR,IAAlB;IACAS,MAAM,CAACC,IAAP,CAAYT,KAAZ,EAAmBU,OAAnB,CAA2B,UAAUC,GAAV,EAAe;MACxCN,WAAW,CAACM,GAAD,CAAX,GAAmBX,KAAK,CAACW,GAAD,CAAxB;IACD,CAFD;IAGAN,WAAW,CAACO,YAAZ,CAAyB,aAAzB,EAAwC,SAAxC;IACAV,QAAQ,CAACW,IAAT,CAAcC,WAAd,CAA0BT,WAA1B;IACA,OAAO;MACLU,GAAG,EAAEV,WADA;MAELW,MAAM,EAAE;IAFH,CAAP;EAID;;EACD,OAAO;IACLD,GAAG,EAAEd,MADA;IAELe,MAAM,EAAEf,MAAM,CAACgB,YAAP,CAAoB,aAApB,KAAsC;EAFzC,CAAP;AAID,CAtBD;;AAuBA,IAAIC,OAAO,GAAG,SAASA,OAAT,CAAiBnB,IAAjB,EAAuBC,KAAvB,EAA8B;EAC1C,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,IAAImB,GAAG,GAAGjB,QAAQ,CAACC,aAAT,CAAuB,eAAeC,MAAf,CAAsBL,IAAtB,EAA4B,KAA5B,CAAvB,CAAV;;EACA,IAAI,CAACoB,GAAL,EAAU;IACR,IAAIC,QAAQ,GAAGlB,QAAQ,CAACI,aAAT,CAAuB,MAAvB,CAAf;IACAc,QAAQ,CAACC,GAAT,GAAe,YAAf;IACAD,QAAQ,CAACE,IAAT,GAAgBvB,IAAhB;IACAS,MAAM,CAACC,IAAP,CAAYT,KAAZ,EAAmBU,OAAnB,CAA2B,UAAUC,GAAV,EAAe;MACxCS,QAAQ,CAACT,GAAD,CAAR,GAAgBX,KAAK,CAACW,GAAD,CAArB;IACD,CAFD,EAJQ,CAOR;;IACA,IAAIY,aAAa,IAAI,eAAeH,QAAnB,CAAjB,CARQ,CASR;;IACA,IAAIG,aAAa,IAAIH,QAAQ,CAACI,OAA9B,EAAuC;MACrCJ,QAAQ,CAACC,GAAT,GAAe,SAAf;MACAD,QAAQ,CAACK,EAAT,GAAc,OAAd;IACD;;IACDL,QAAQ,CAACR,YAAT,CAAsB,aAAtB,EAAqC,SAArC;IACAV,QAAQ,CAACwB,IAAT,CAAcZ,WAAd,CAA0BM,QAA1B;IACA,OAAO;MACLL,GAAG,EAAEK,QADA;MAELJ,MAAM,EAAE;IAFH,CAAP;EAID;;EACD,OAAO;IACLD,GAAG,EAAEI,GADA;IAELH,MAAM,EAAEG,GAAG,CAACF,YAAJ,CAAiB,aAAjB,KAAmC;EAFtC,CAAP;AAID,CA9BD;;AA+BA,IAAIU,WAAW,GAAG,SAASA,WAAT,CAAqB5B,IAArB,EAA2B6B,OAA3B,EAAoC;EACpD,IAAIC,EAAE,GAAGnD,MAAM,CAACkB,QAAQ,CAACG,IAAI,GAAG,SAAH,GAAe,OAApB,CAAT,EAAuC,CAAvC,CAAf;EAAA,IACEiB,MAAM,GAAGa,EAAE,CAAC,CAAD,CADb;EAAA,IAEEC,SAAS,GAAGD,EAAE,CAAC,CAAD,CAFhB;;EAGA,IAAId,GAAG,GAAGpB,MAAM,EAAhB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACK,IAAL,EAAW;MACT+B,SAAS,CAAC,OAAD,CAAT;MACA;IACD;;IACD,IAAIC,QAAQ,GAAGhC,IAAI,CAACiC,OAAL,CAAa,SAAb,EAAwB,EAAxB,CAAf;;IACA,IAAI,CAACJ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,IAA3D,MAAqE,KAArE,IAA8E,EAAEL,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,IAA5D,KAAqE,iBAAiBC,IAAjB,CAAsBH,QAAtB,CAAvJ,EAAwL;MACtL,IAAII,MAAM,GAAGjB,OAAO,CAACnB,IAAD,EAAO6B,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACT,GAAjE,CAApB;MACAJ,GAAG,CAACqB,OAAJ,GAAcD,MAAM,CAACpB,GAArB;MACAe,SAAS,CAACK,MAAM,CAACnB,MAAR,CAAT;IACD,CAJD,MAIO,IAAI,CAACY,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,IAA3D,MAAqE,IAArE,IAA6E,EAAEL,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,IAA5D,KAAqE,eAAeC,IAAf,CAAoBH,QAApB,CAAtJ,EAAqL;MAC1L,IAAII,MAAM,GAAGrC,UAAU,CAACC,IAAD,EAAO6B,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACS,EAAjE,CAAvB;MACAtB,GAAG,CAACqB,OAAJ,GAAcD,MAAM,CAACpB,GAArB;MACAe,SAAS,CAACK,MAAM,CAACnB,MAAR,CAAT;IACD,CAJM,MAIA;MACL;MACAsB,OAAO,CAAC7C,KAAR,CAAc,2FAA2F,oEAAzG;IACD;;IACD,IAAI,CAACsB,GAAG,CAACqB,OAAT,EAAkB;MAChB;IACD;;IACD,IAAIvC,mBAAmB,CAACE,IAAD,CAAnB,KAA8BwC,SAAlC,EAA6C;MAC3C1C,mBAAmB,CAACE,IAAD,CAAnB,GAA4B,CAA5B;IACD,CAFD,MAEO;MACLF,mBAAmB,CAACE,IAAD,CAAnB,IAA6B,CAA7B;IACD;;IACD,IAAIyC,OAAO,GAAG,SAASA,OAAT,CAAiBC,KAAjB,EAAwB;MACpC,IAAIZ,EAAJ;;MACA,IAAIa,YAAY,GAAGD,KAAK,CAACR,IAAN,KAAe,MAAf,GAAwB,OAAxB,GAAkC,OAArD;MACA,CAACJ,EAAE,GAAGd,GAAG,CAACqB,OAAV,MAAuB,IAAvB,IAA+BP,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACjB,YAAH,CAAgB,aAAhB,EAA+B8B,YAA/B,CAAxD;MACAZ,SAAS,CAACY,YAAD,CAAT;IACD,CALD;;IAMA3B,GAAG,CAACqB,OAAJ,CAAYO,gBAAZ,CAA6B,MAA7B,EAAqCH,OAArC;IACAzB,GAAG,CAACqB,OAAJ,CAAYO,gBAAZ,CAA6B,OAA7B,EAAsCH,OAAtC;IACA,OAAO,YAAY;MACjB,IAAIX,EAAJ,EAAQe,EAAR,EAAYC,EAAZ;;MACA,CAAChB,EAAE,GAAGd,GAAG,CAACqB,OAAV,MAAuB,IAAvB,IAA+BP,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACiB,mBAAH,CAAuB,MAAvB,EAA+BN,OAA/B,CAAxD;MACA,CAACI,EAAE,GAAG7B,GAAG,CAACqB,OAAV,MAAuB,IAAvB,IAA+BQ,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACE,mBAAH,CAAuB,OAAvB,EAAgCN,OAAhC,CAAxD;MACA3C,mBAAmB,CAACE,IAAD,CAAnB,IAA6B,CAA7B;;MACA,IAAIF,mBAAmB,CAACE,IAAD,CAAnB,KAA8B,CAAlC,EAAqC;QACnC,CAAC8C,EAAE,GAAG9B,GAAG,CAACqB,OAAV,MAAuB,IAAvB,IAA+BS,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACE,MAAH,EAAxD;MACD;;MACDhC,GAAG,CAACqB,OAAJ,GAAcG,SAAd;IACD,CATD;EAUD,CA5CQ,EA4CN,CAACxC,IAAD,CA5CM,CAAT;EA6CA,OAAOiB,MAAP;AACD,CAnDD;;AAoDA,eAAeW,WAAf"}, "metadata": {}, "sourceType": "module"}