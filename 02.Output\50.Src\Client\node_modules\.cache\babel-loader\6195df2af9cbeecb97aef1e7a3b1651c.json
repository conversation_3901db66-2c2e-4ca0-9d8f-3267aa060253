{"ast": null, "code": "/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar b = \"function\" === typeof Symbol && Symbol.for,\n    c = b ? Symbol.for(\"react.element\") : 60103,\n    d = b ? Symbol.for(\"react.portal\") : 60106,\n    e = b ? Symbol.for(\"react.fragment\") : 60107,\n    f = b ? Symbol.for(\"react.strict_mode\") : 60108,\n    g = b ? Symbol.for(\"react.profiler\") : 60114,\n    h = b ? Symbol.for(\"react.provider\") : 60109,\n    k = b ? Symbol.for(\"react.context\") : 60110,\n    l = b ? Symbol.for(\"react.async_mode\") : 60111,\n    m = b ? Symbol.for(\"react.concurrent_mode\") : 60111,\n    n = b ? Symbol.for(\"react.forward_ref\") : 60112,\n    p = b ? Symbol.for(\"react.suspense\") : 60113,\n    q = b ? Symbol.for(\"react.suspense_list\") : 60120,\n    r = b ? Symbol.for(\"react.memo\") : 60115,\n    t = b ? Symbol.for(\"react.lazy\") : 60116,\n    v = b ? Symbol.for(\"react.block\") : 60121,\n    w = b ? Symbol.for(\"react.fundamental\") : 60117,\n    x = b ? Symbol.for(\"react.responder\") : 60118,\n    y = b ? Symbol.for(\"react.scope\") : 60119;\n\nfunction z(a) {\n  if (\"object\" === typeof a && null !== a) {\n    var u = a.$$typeof;\n\n    switch (u) {\n      case c:\n        switch (a = a.type, a) {\n          case l:\n          case m:\n          case e:\n          case g:\n          case f:\n          case p:\n            return a;\n\n          default:\n            switch (a = a && a.$$typeof, a) {\n              case k:\n              case n:\n              case t:\n              case r:\n              case h:\n                return a;\n\n              default:\n                return u;\n            }\n\n        }\n\n      case d:\n        return u;\n    }\n  }\n}\n\nfunction A(a) {\n  return z(a) === m;\n}\n\nexports.AsyncMode = l;\nexports.ConcurrentMode = m;\nexports.ContextConsumer = k;\nexports.ContextProvider = h;\nexports.Element = c;\nexports.ForwardRef = n;\nexports.Fragment = e;\nexports.Lazy = t;\nexports.Memo = r;\nexports.Portal = d;\nexports.Profiler = g;\nexports.StrictMode = f;\nexports.Suspense = p;\n\nexports.isAsyncMode = function (a) {\n  return A(a) || z(a) === l;\n};\n\nexports.isConcurrentMode = A;\n\nexports.isContextConsumer = function (a) {\n  return z(a) === k;\n};\n\nexports.isContextProvider = function (a) {\n  return z(a) === h;\n};\n\nexports.isElement = function (a) {\n  return \"object\" === typeof a && null !== a && a.$$typeof === c;\n};\n\nexports.isForwardRef = function (a) {\n  return z(a) === n;\n};\n\nexports.isFragment = function (a) {\n  return z(a) === e;\n};\n\nexports.isLazy = function (a) {\n  return z(a) === t;\n};\n\nexports.isMemo = function (a) {\n  return z(a) === r;\n};\n\nexports.isPortal = function (a) {\n  return z(a) === d;\n};\n\nexports.isProfiler = function (a) {\n  return z(a) === g;\n};\n\nexports.isStrictMode = function (a) {\n  return z(a) === f;\n};\n\nexports.isSuspense = function (a) {\n  return z(a) === p;\n};\n\nexports.isValidElementType = function (a) {\n  return \"string\" === typeof a || \"function\" === typeof a || a === e || a === m || a === g || a === f || a === p || a === q || \"object\" === typeof a && null !== a && (a.$$typeof === t || a.$$typeof === r || a.$$typeof === h || a.$$typeof === k || a.$$typeof === n || a.$$typeof === w || a.$$typeof === x || a.$$typeof === y || a.$$typeof === v);\n};\n\nexports.typeOf = z;", "map": {"version": 3, "names": ["b", "Symbol", "for", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "t", "v", "w", "x", "y", "z", "a", "u", "$$typeof", "type", "A", "exports", "AsyncMode", "ConcurrentMode", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/hoist-non-react-statics/node_modules/react-is/cjs/react-is.production.min.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\nSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\nfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;\nexports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;exports.isAsyncMode=function(a){return A(a)||z(a)===l};exports.isConcurrentMode=A;exports.isContextConsumer=function(a){return z(a)===k};exports.isContextProvider=function(a){return z(a)===h};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return z(a)===n};exports.isFragment=function(a){return z(a)===e};exports.isLazy=function(a){return z(a)===t};\nexports.isMemo=function(a){return z(a)===r};exports.isPortal=function(a){return z(a)===d};exports.isProfiler=function(a){return z(a)===g};exports.isStrictMode=function(a){return z(a)===f};exports.isSuspense=function(a){return z(a)===p};\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};exports.typeOf=z;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAAa,IAAIA,CAAC,GAAC,eAAa,OAAOC,MAApB,IAA4BA,MAAM,CAACC,GAAzC;AAAA,IAA6CC,CAAC,GAACH,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,eAAX,CAAD,GAA6B,KAA7E;AAAA,IAAmFE,CAAC,GAACJ,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,cAAX,CAAD,GAA4B,KAAlH;AAAA,IAAwHG,CAAC,GAACL,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,gBAAX,CAAD,GAA8B,KAAzJ;AAAA,IAA+JI,CAAC,GAACN,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,mBAAX,CAAD,GAAiC,KAAnM;AAAA,IAAyMK,CAAC,GAACP,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,gBAAX,CAAD,GAA8B,KAA1O;AAAA,IAAgPM,CAAC,GAACR,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,gBAAX,CAAD,GAA8B,KAAjR;AAAA,IAAuRO,CAAC,GAACT,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,eAAX,CAAD,GAA6B,KAAvT;AAAA,IAA6TQ,CAAC,GAACV,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,kBAAX,CAAD,GAAgC,KAAhW;AAAA,IAAsWS,CAAC,GAACX,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,uBAAX,CAAD,GAAqC,KAA9Y;AAAA,IAAoZU,CAAC,GAACZ,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,mBAAX,CAAD,GAAiC,KAAxb;AAAA,IAA8bW,CAAC,GAACb,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,gBAAX,CAAD,GAA8B,KAA/d;AAAA,IAAqeY,CAAC,GAACd,CAAC,GACrfC,MAAM,CAACC,GAAP,CAAW,qBAAX,CADqf,GACnd,KADrB;AAAA,IAC2Ba,CAAC,GAACf,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,YAAX,CAAD,GAA0B,KADxD;AAAA,IAC8Dc,CAAC,GAAChB,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,YAAX,CAAD,GAA0B,KAD3F;AAAA,IACiGe,CAAC,GAACjB,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,aAAX,CAAD,GAA2B,KAD/H;AAAA,IACqIgB,CAAC,GAAClB,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,mBAAX,CAAD,GAAiC,KADzK;AAAA,IAC+KiB,CAAC,GAACnB,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,iBAAX,CAAD,GAA+B,KADjN;AAAA,IACuNkB,CAAC,GAACpB,CAAC,GAACC,MAAM,CAACC,GAAP,CAAW,aAAX,CAAD,GAA2B,KADrP;;AAEb,SAASmB,CAAT,CAAWC,CAAX,EAAa;EAAC,IAAG,aAAW,OAAOA,CAAlB,IAAqB,SAAOA,CAA/B,EAAiC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,QAAR;;IAAiB,QAAOD,CAAP;MAAU,KAAKpB,CAAL;QAAO,QAAOmB,CAAC,GAACA,CAAC,CAACG,IAAJ,EAASH,CAAhB;UAAmB,KAAKZ,CAAL;UAAO,KAAKC,CAAL;UAAO,KAAKN,CAAL;UAAO,KAAKE,CAAL;UAAO,KAAKD,CAAL;UAAO,KAAKO,CAAL;YAAO,OAAOS,CAAP;;UAAS;YAAQ,QAAOA,CAAC,GAACA,CAAC,IAAEA,CAAC,CAACE,QAAP,EAAgBF,CAAvB;cAA0B,KAAKb,CAAL;cAAO,KAAKG,CAAL;cAAO,KAAKI,CAAL;cAAO,KAAKD,CAAL;cAAO,KAAKP,CAAL;gBAAO,OAAOc,CAAP;;cAAS;gBAAQ,OAAOC,CAAP;YAA9E;;QAA9E;;MAAsK,KAAKnB,CAAL;QAAO,OAAOmB,CAAP;IAA9L;EAAwM;AAAC;;AAAA,SAASG,CAAT,CAAWJ,CAAX,EAAa;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOX,CAAd;AAAgB;;AAAAgB,OAAO,CAACC,SAAR,GAAkBlB,CAAlB;AAAoBiB,OAAO,CAACE,cAAR,GAAuBlB,CAAvB;AAAyBgB,OAAO,CAACG,eAAR,GAAwBrB,CAAxB;AAA0BkB,OAAO,CAACI,eAAR,GAAwBvB,CAAxB;AAA0BmB,OAAO,CAACK,OAAR,GAAgB7B,CAAhB;AAAkBwB,OAAO,CAACM,UAAR,GAAmBrB,CAAnB;AAAqBe,OAAO,CAACO,QAAR,GAAiB7B,CAAjB;AAAmBsB,OAAO,CAACQ,IAAR,GAAanB,CAAb;AAAeW,OAAO,CAACS,IAAR,GAAarB,CAAb;AAAeY,OAAO,CAACU,MAAR,GAAejC,CAAf;AACjeuB,OAAO,CAACW,QAAR,GAAiB/B,CAAjB;AAAmBoB,OAAO,CAACY,UAAR,GAAmBjC,CAAnB;AAAqBqB,OAAO,CAACa,QAAR,GAAiB3B,CAAjB;;AAAmBc,OAAO,CAACc,WAAR,GAAoB,UAASnB,CAAT,EAAW;EAAC,OAAOI,CAAC,CAACJ,CAAD,CAAD,IAAMD,CAAC,CAACC,CAAD,CAAD,KAAOZ,CAApB;AAAsB,CAAtD;;AAAuDiB,OAAO,CAACe,gBAAR,GAAyBhB,CAAzB;;AAA2BC,OAAO,CAACgB,iBAAR,GAA0B,UAASrB,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOb,CAAd;AAAgB,CAAtD;;AAAuDkB,OAAO,CAACiB,iBAAR,GAA0B,UAAStB,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOd,CAAd;AAAgB,CAAtD;;AAAuDmB,OAAO,CAACkB,SAAR,GAAkB,UAASvB,CAAT,EAAW;EAAC,OAAM,aAAW,OAAOA,CAAlB,IAAqB,SAAOA,CAA5B,IAA+BA,CAAC,CAACE,QAAF,KAAarB,CAAlD;AAAoD,CAAlF;;AAAmFwB,OAAO,CAACmB,YAAR,GAAqB,UAASxB,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOV,CAAd;AAAgB,CAAjD;;AAAkDe,OAAO,CAACoB,UAAR,GAAmB,UAASzB,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOjB,CAAd;AAAgB,CAA/C;;AAAgDsB,OAAO,CAACqB,MAAR,GAAe,UAAS1B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAON,CAAd;AAAgB,CAA3C;;AAChbW,OAAO,CAACsB,MAAR,GAAe,UAAS3B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOP,CAAd;AAAgB,CAA3C;;AAA4CY,OAAO,CAACuB,QAAR,GAAiB,UAAS5B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOlB,CAAd;AAAgB,CAA7C;;AAA8CuB,OAAO,CAACwB,UAAR,GAAmB,UAAS7B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOf,CAAd;AAAgB,CAA/C;;AAAgDoB,OAAO,CAACyB,YAAR,GAAqB,UAAS9B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOhB,CAAd;AAAgB,CAAjD;;AAAkDqB,OAAO,CAAC0B,UAAR,GAAmB,UAAS/B,CAAT,EAAW;EAAC,OAAOD,CAAC,CAACC,CAAD,CAAD,KAAOT,CAAd;AAAgB,CAA/C;;AAC5Lc,OAAO,CAAC2B,kBAAR,GAA2B,UAAShC,CAAT,EAAW;EAAC,OAAM,aAAW,OAAOA,CAAlB,IAAqB,eAAa,OAAOA,CAAzC,IAA4CA,CAAC,KAAGjB,CAAhD,IAAmDiB,CAAC,KAAGX,CAAvD,IAA0DW,CAAC,KAAGf,CAA9D,IAAiEe,CAAC,KAAGhB,CAArE,IAAwEgB,CAAC,KAAGT,CAA5E,IAA+ES,CAAC,KAAGR,CAAnF,IAAsF,aAAW,OAAOQ,CAAlB,IAAqB,SAAOA,CAA5B,KAAgCA,CAAC,CAACE,QAAF,KAAaR,CAAb,IAAgBM,CAAC,CAACE,QAAF,KAAaT,CAA7B,IAAgCO,CAAC,CAACE,QAAF,KAAahB,CAA7C,IAAgDc,CAAC,CAACE,QAAF,KAAaf,CAA7D,IAAgEa,CAAC,CAACE,QAAF,KAAaZ,CAA7E,IAAgFU,CAAC,CAACE,QAAF,KAAaN,CAA7F,IAAgGI,CAAC,CAACE,QAAF,KAAaL,CAA7G,IAAgHG,CAAC,CAACE,QAAF,KAAaJ,CAA7H,IAAgIE,CAAC,CAACE,QAAF,KAAaP,CAA7K,CAA5F;AAA4Q,CAAnT;;AAAoTU,OAAO,CAAC4B,MAAR,GAAelC,CAAf"}, "metadata": {}, "sourceType": "script"}