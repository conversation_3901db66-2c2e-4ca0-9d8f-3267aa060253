{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n/** Used to stand-in for `undefined` hash values. */\n\n\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\n\nfunction hashGet(key) {\n  var data = this.__data__;\n\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\nmodule.exports = hashGet;", "map": {"version": 3, "names": ["nativeCreate", "require", "HASH_UNDEFINED", "objectProto", "Object", "prototype", "hasOwnProperty", "hashGet", "key", "data", "__data__", "result", "undefined", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_hashGet.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\nmodule.exports = hashGet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;;;AACA,IAAIC,cAAc,GAAG,2BAArB;AAEA;;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBC,GAAjB,EAAsB;EACpB,IAAIC,IAAI,GAAG,KAAKC,QAAhB;;EACA,IAAIV,YAAJ,EAAkB;IAChB,IAAIW,MAAM,GAAGF,IAAI,CAACD,GAAD,CAAjB;IACA,OAAOG,MAAM,KAAKT,cAAX,GAA4BU,SAA5B,GAAwCD,MAA/C;EACD;;EACD,OAAOL,cAAc,CAACO,IAAf,CAAoBJ,IAApB,EAA0BD,GAA1B,IAAiCC,IAAI,CAACD,GAAD,CAArC,GAA6CI,SAApD;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBR,OAAjB"}, "metadata": {}, "sourceType": "script"}