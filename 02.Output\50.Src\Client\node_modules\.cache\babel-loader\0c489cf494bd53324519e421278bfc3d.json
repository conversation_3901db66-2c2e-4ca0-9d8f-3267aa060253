{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\n\nvar setRafInterval = function setRafInterval(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n\n  var start = new Date().getTime();\n  var handle = {\n    id: 0\n  };\n\n  var loop = function loop() {\n    var current = new Date().getTime();\n\n    if (current - start >= delay) {\n      callback();\n      start = new Date().getTime();\n    }\n\n    handle.id = requestAnimationFrame(loop);\n  };\n\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\n\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\n\nvar clearRafInterval = function clearRafInterval(handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n\n  cancelAnimationFrame(handle.id);\n};\n\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n\n    if (immediate) {\n      fnRef.current();\n    }\n\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearRafInterval(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\n\nexport default useRafInterval;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "setRafInterval", "callback", "delay", "requestAnimationFrame", "undefined", "id", "setInterval", "start", "Date", "getTime", "handle", "loop", "current", "cancelAnimationFrameIsNotDefined", "t", "cancelAnimationFrame", "clearRafInterval", "clearInterval", "useRafInterval", "fn", "options", "immediate", "fnRef", "timerRef", "clear"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRafInterval/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafInterval = function setRafInterval(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n  var start = new Date().getTime();\n  var handle = {\n    id: 0\n  };\n  var loop = function loop() {\n    var current = new Date().getTime();\n    if (current - start >= delay) {\n      callback();\n      start = new Date().getTime();\n    }\n    handle.id = requestAnimationFrame(loop);\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafInterval = function clearRafInterval(handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearRafInterval(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\nexport default useRafInterval;"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,SAAtB,EAAiCC,MAAjC,QAA+C,OAA/C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,QAAxB,EAAkCC,KAAlC,EAAyC;EAC5D,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,CAAR;EACD;;EACD,IAAI,OAAOC,qBAAP,KAAiC,OAAOC,SAA5C,EAAuD;IACrD,OAAO;MACLC,EAAE,EAAEC,WAAW,CAACL,QAAD,EAAWC,KAAX;IADV,CAAP;EAGD;;EACD,IAAIK,KAAK,GAAG,IAAIC,IAAJ,GAAWC,OAAX,EAAZ;EACA,IAAIC,MAAM,GAAG;IACXL,EAAE,EAAE;EADO,CAAb;;EAGA,IAAIM,IAAI,GAAG,SAASA,IAAT,GAAgB;IACzB,IAAIC,OAAO,GAAG,IAAIJ,IAAJ,GAAWC,OAAX,EAAd;;IACA,IAAIG,OAAO,GAAGL,KAAV,IAAmBL,KAAvB,EAA8B;MAC5BD,QAAQ;MACRM,KAAK,GAAG,IAAIC,IAAJ,GAAWC,OAAX,EAAR;IACD;;IACDC,MAAM,CAACL,EAAP,GAAYF,qBAAqB,CAACQ,IAAD,CAAjC;EACD,CAPD;;EAQAD,MAAM,CAACL,EAAP,GAAYF,qBAAqB,CAACQ,IAAD,CAAjC;EACA,OAAOD,MAAP;AACD,CAvBD;;AAwBA,SAASG,gCAAT,CAA0CC,CAA1C,EAA6C;EAC3C,OAAO,OAAOC,oBAAP,KAAgC,OAAOX,SAA9C;AACD;;AACD,IAAIY,gBAAgB,GAAG,SAASA,gBAAT,CAA0BN,MAA1B,EAAkC;EACvD,IAAIG,gCAAgC,CAACH,MAAM,CAACL,EAAR,CAApC,EAAiD;IAC/C,OAAOY,aAAa,CAACP,MAAM,CAACL,EAAR,CAApB;EACD;;EACDU,oBAAoB,CAACL,MAAM,CAACL,EAAR,CAApB;AACD,CALD;;AAMA,SAASa,cAAT,CAAwBC,EAAxB,EAA4BjB,KAA5B,EAAmCkB,OAAnC,EAA4C;EAC1C,IAAIC,SAAS,GAAGD,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACC,SAA1E;EACA,IAAIC,KAAK,GAAGxB,SAAS,CAACqB,EAAD,CAArB;EACA,IAAII,QAAQ,GAAG1B,MAAM,EAArB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAD,CAAT,IAAoBA,KAAK,GAAG,CAAhC,EAAmC;;IACnC,IAAImB,SAAJ,EAAe;MACbC,KAAK,CAACV,OAAN;IACD;;IACDW,QAAQ,CAACX,OAAT,GAAmBZ,cAAc,CAAC,YAAY;MAC5CsB,KAAK,CAACV,OAAN;IACD,CAFgC,EAE9BV,KAF8B,CAAjC;IAGA,OAAO,YAAY;MACjB,IAAIqB,QAAQ,CAACX,OAAb,EAAsB;QACpBI,gBAAgB,CAACO,QAAQ,CAACX,OAAV,CAAhB;MACD;IACF,CAJD;EAKD,CAbQ,EAaN,CAACV,KAAD,CAbM,CAAT;EAcA,IAAIsB,KAAK,GAAG7B,WAAW,CAAC,YAAY;IAClC,IAAI4B,QAAQ,CAACX,OAAb,EAAsB;MACpBI,gBAAgB,CAACO,QAAQ,CAACX,OAAV,CAAhB;IACD;EACF,CAJsB,EAIpB,EAJoB,CAAvB;EAKA,OAAOY,KAAP;AACD;;AACD,eAAeN,cAAf"}, "metadata": {}, "sourceType": "module"}