{"ast": null, "code": "/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nmodule.exports = arraySome;", "map": {"version": 3, "names": ["arraySome", "array", "predicate", "index", "length", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_arraySome.js"], "sourcesContent": ["/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nmodule.exports = arraySome;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,KAAnB,EAA0BC,SAA1B,EAAqC;EACnC,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGH,KAAK,IAAI,IAAT,GAAgB,CAAhB,GAAoBA,KAAK,CAACG,MADvC;;EAGA,OAAO,EAAED,KAAF,GAAUC,MAAjB,EAAyB;IACvB,IAAIF,SAAS,CAACD,KAAK,CAACE,KAAD,CAAN,EAAeA,KAAf,EAAsBF,KAAtB,CAAb,EAA2C;MACzC,OAAO,IAAP;IACD;EACF;;EACD,OAAO,KAAP;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiBN,SAAjB"}, "metadata": {}, "sourceType": "script"}