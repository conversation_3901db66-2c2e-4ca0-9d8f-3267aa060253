{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\n\nvar nativeObjectToString = objectProto.toString;\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\n\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "nativeObjectToString", "toString", "objectToString", "value", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_objectToString.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACI,QAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBC,KAAxB,EAA+B;EAC7B,OAAOH,oBAAoB,CAACI,IAArB,CAA0BD,KAA1B,CAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBJ,cAAjB"}, "metadata": {}, "sourceType": "script"}