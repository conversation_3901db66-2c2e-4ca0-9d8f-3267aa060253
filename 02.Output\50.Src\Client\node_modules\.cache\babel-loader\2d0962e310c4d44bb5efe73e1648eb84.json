{"ast": null, "code": "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n/** Used to detect unsigned integer values. */\n\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\n\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length && (type == 'number' || type != 'symbol' && reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\n\nmodule.exports = isIndex;", "map": {"version": 3, "names": ["MAX_SAFE_INTEGER", "reIsUint", "isIndex", "value", "length", "type", "test", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_isIndex.js"], "sourcesContent": ["/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n\n  return !!length &&\n    (type == 'number' ||\n      (type != 'symbol' && reIsUint.test(value))) &&\n        (value > -1 && value % 1 == 0 && value < length);\n}\n\nmodule.exports = isIndex;\n"], "mappings": "AAAA;AACA,IAAIA,gBAAgB,GAAG,gBAAvB;AAEA;;AACA,IAAIC,QAAQ,GAAG,kBAAf;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBC,KAAjB,EAAwBC,MAAxB,EAAgC;EAC9B,IAAIC,IAAI,GAAG,OAAOF,KAAlB;EACAC,MAAM,GAAGA,MAAM,IAAI,IAAV,GAAiBJ,gBAAjB,GAAoCI,MAA7C;EAEA,OAAO,CAAC,CAACA,MAAF,KACJC,IAAI,IAAI,QAAR,IACEA,IAAI,IAAI,QAAR,IAAoBJ,QAAQ,CAACK,IAAT,CAAcH,KAAd,CAFlB,KAGAA,KAAK,GAAG,CAAC,CAAT,IAAcA,KAAK,GAAG,CAAR,IAAa,CAA3B,IAAgCA,KAAK,GAAGC,MAH/C;AAID;;AAEDG,MAAM,CAACC,OAAP,GAAiBN,OAAjB"}, "metadata": {}, "sourceType": "script"}