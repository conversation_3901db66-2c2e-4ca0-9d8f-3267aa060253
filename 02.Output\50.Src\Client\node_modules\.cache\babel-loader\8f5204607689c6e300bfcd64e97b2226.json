{"ast": null, "code": "/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\n\nmodule.exports = cacheHas;", "map": {"version": 3, "names": ["cacheHas", "cache", "key", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_cacheHas.js"], "sourcesContent": ["/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\n\nmodule.exports = cacheHas;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAT,CAAkBC,KAAlB,EAAyBC,GAAzB,EAA8B;EAC5B,OAAOD,KAAK,CAACE,GAAN,CAAUD,GAAV,CAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBL,QAAjB"}, "metadata": {}, "sourceType": "script"}