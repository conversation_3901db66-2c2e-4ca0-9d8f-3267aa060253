{"ast": null, "code": "var isPrototype = require('./_isPrototype'),\n    nativeKeys = require('./_nativeKeys');\n/** Used for built-in method references. */\n\n\nvar objectProto = Object.prototype;\n/** Used to check objects for own properties. */\n\nvar hasOwnProperty = objectProto.hasOwnProperty;\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\n\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n\n  var result = [];\n\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n\n  return result;\n}\n\nmodule.exports = baseKeys;", "map": {"version": 3, "names": ["isPrototype", "require", "nativeKeys", "objectProto", "Object", "prototype", "hasOwnProperty", "baseKeys", "object", "result", "key", "call", "push", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_baseKeys.js"], "sourcesContent": ["var isPrototype = require('./_isPrototype'),\n    nativeKeys = require('./_nativeKeys');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseKeys;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAD,CAAzB;AAAA,IACIC,UAAU,GAAGD,OAAO,CAAC,eAAD,CADxB;AAGA;;;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CAAkBC,MAAlB,EAA0B;EACxB,IAAI,CAACR,WAAW,CAACQ,MAAD,CAAhB,EAA0B;IACxB,OAAON,UAAU,CAACM,MAAD,CAAjB;EACD;;EACD,IAAIC,MAAM,GAAG,EAAb;;EACA,KAAK,IAAIC,GAAT,IAAgBN,MAAM,CAACI,MAAD,CAAtB,EAAgC;IAC9B,IAAIF,cAAc,CAACK,IAAf,CAAoBH,MAApB,EAA4BE,GAA5B,KAAoCA,GAAG,IAAI,aAA/C,EAA8D;MAC5DD,MAAM,CAACG,IAAP,CAAYF,GAAZ;IACD;EACF;;EACD,OAAOD,MAAP;AACD;;AAEDI,MAAM,CAACC,OAAP,GAAiBP,QAAjB"}, "metadata": {}, "sourceType": "script"}