{"ast": null, "code": "import { useRef } from 'react';\n\nfunction useLatest(value) {\n  var ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\n\nexport default useLatest;", "map": {"version": 3, "names": ["useRef", "useLatest", "value", "ref", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useLatest/index.js"], "sourcesContent": ["import { useRef } from 'react';\nfunction useLatest(value) {\n  var ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\nexport default useLatest;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;;AACA,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;EACxB,IAAIC,GAAG,GAAGH,MAAM,CAACE,KAAD,CAAhB;EACAC,GAAG,CAACC,OAAJ,GAAcF,KAAd;EACA,OAAOC,GAAP;AACD;;AACD,eAAeF,SAAf"}, "metadata": {}, "sourceType": "module"}