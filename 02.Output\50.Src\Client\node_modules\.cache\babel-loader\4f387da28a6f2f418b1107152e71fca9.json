{"ast": null, "code": "'use strict';\n\nimport toFormData from './toFormData.js';\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\n\nfunction encode(str) {\n  var charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\n\n\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n  params && toFormData(params, this, options);\n}\n\nvar prototype = AxiosURLSearchParams.prototype;\n\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\n\nprototype.toString = function toString(encoder) {\n  var _encode = encoder ? function (value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\n\nexport default AxiosURLSearchParams;", "map": {"version": 3, "names": ["toFormData", "encode", "str", "charMap", "encodeURIComponent", "replace", "replacer", "match", "AxiosURLSearchParams", "params", "options", "_pairs", "prototype", "append", "name", "value", "push", "toString", "encoder", "_encode", "call", "map", "each", "pair", "join"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/helpers/AxiosURLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport toFormData from './toFormData.js';\n\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\nfunction encode(str) {\n  const charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n\n  params && toFormData(params, this, options);\n}\n\nconst prototype = AxiosURLSearchParams.prototype;\n\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\n\nprototype.toString = function toString(encoder) {\n  const _encode = encoder ? function(value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\n\nexport default AxiosURLSearchParams;\n"], "mappings": "AAAA;;AAEA,OAAOA,UAAP,MAAuB,iBAAvB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,MAAT,CAAgBC,GAAhB,EAAqB;EACnB,IAAMC,OAAO,GAAG;IACd,KAAK,KADS;IAEd,KAAK,KAFS;IAGd,KAAK,KAHS;IAId,KAAK,KAJS;IAKd,KAAK,KALS;IAMd,OAAO,GANO;IAOd,OAAO;EAPO,CAAhB;EASA,OAAOC,kBAAkB,CAACF,GAAD,CAAlB,CAAwBG,OAAxB,CAAgC,kBAAhC,EAAoD,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;IAClF,OAAOJ,OAAO,CAACI,KAAD,CAAd;EACD,CAFM,CAAP;AAGD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,oBAAT,CAA8BC,MAA9B,EAAsCC,OAAtC,EAA+C;EAC7C,KAAKC,MAAL,GAAc,EAAd;EAEAF,MAAM,IAAIT,UAAU,CAACS,MAAD,EAAS,IAAT,EAAeC,OAAf,CAApB;AACD;;AAED,IAAME,SAAS,GAAGJ,oBAAoB,CAACI,SAAvC;;AAEAA,SAAS,CAACC,MAAV,GAAmB,SAASA,MAAT,CAAgBC,IAAhB,EAAsBC,KAAtB,EAA6B;EAC9C,KAAKJ,MAAL,CAAYK,IAAZ,CAAiB,CAACF,IAAD,EAAOC,KAAP,CAAjB;AACD,CAFD;;AAIAH,SAAS,CAACK,QAAV,GAAqB,SAASA,QAAT,CAAkBC,OAAlB,EAA2B;EAC9C,IAAMC,OAAO,GAAGD,OAAO,GAAG,UAASH,KAAT,EAAgB;IACxC,OAAOG,OAAO,CAACE,IAAR,CAAa,IAAb,EAAmBL,KAAnB,EAA0Bd,MAA1B,CAAP;EACD,CAFsB,GAEnBA,MAFJ;;EAIA,OAAO,KAAKU,MAAL,CAAYU,GAAZ,CAAgB,SAASC,IAAT,CAAcC,IAAd,EAAoB;IACzC,OAAOJ,OAAO,CAACI,IAAI,CAAC,CAAD,CAAL,CAAP,GAAmB,GAAnB,GAAyBJ,OAAO,CAACI,IAAI,CAAC,CAAD,CAAL,CAAvC;EACD,CAFM,EAEJ,EAFI,EAEAC,IAFA,CAEK,GAFL,CAAP;AAGD,CARD;;AAUA,eAAehB,oBAAf"}, "metadata": {}, "sourceType": "module"}