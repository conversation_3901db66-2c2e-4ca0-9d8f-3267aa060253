{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\n\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n\n  var _a = __read(useState(initialState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\n\nexport default useRafState;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useCallback", "useRef", "useState", "useUnmount", "useRafState", "initialState", "ref", "_a", "state", "setState", "setRafState", "cancelAnimationFrame", "current", "requestAnimationFrame"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRafState/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\nexport default useRafState;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,WAAT,EAAsBC,MAAtB,EAA8BC,QAA9B,QAA8C,OAA9C;AACA,OAAOC,UAAP,MAAuB,eAAvB;;AACA,SAASC,WAAT,CAAqBC,YAArB,EAAmC;EACjC,IAAIC,GAAG,GAAGL,MAAM,CAAC,CAAD,CAAhB;;EACA,IAAIM,EAAE,GAAGvB,MAAM,CAACkB,QAAQ,CAACG,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEG,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,WAAW,GAAGV,WAAW,CAAC,UAAUF,KAAV,EAAiB;IAC7Ca,oBAAoB,CAACL,GAAG,CAACM,OAAL,CAApB;IACAN,GAAG,CAACM,OAAJ,GAAcC,qBAAqB,CAAC,YAAY;MAC9CJ,QAAQ,CAACX,KAAD,CAAR;IACD,CAFkC,CAAnC;EAGD,CAL4B,EAK1B,EAL0B,CAA7B;EAMAK,UAAU,CAAC,YAAY;IACrBQ,oBAAoB,CAACL,GAAG,CAACM,OAAL,CAApB;EACD,CAFS,CAAV;EAGA,OAAO,CAACJ,KAAD,EAAQE,WAAR,CAAP;AACD;;AACD,eAAeN,WAAf"}, "metadata": {}, "sourceType": "module"}