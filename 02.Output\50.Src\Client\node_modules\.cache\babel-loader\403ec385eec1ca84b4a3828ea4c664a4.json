{"ast": null, "code": "/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function (arg) {\n    return func(transform(arg));\n  };\n}\n\nmodule.exports = overArg;", "map": {"version": 3, "names": ["overArg", "func", "transform", "arg", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_overArg.js"], "sourcesContent": ["/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\nmodule.exports = overArg;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAT,CAAiBC,IAAjB,EAAuBC,SAAvB,EAAkC;EAChC,OAAO,UAASC,GAAT,EAAc;IACnB,OAAOF,IAAI,CAACC,SAAS,CAACC,GAAD,CAAV,CAAX;EACD,CAFD;AAGD;;AAEDC,MAAM,CAACC,OAAP,GAAiBL,OAAjB"}, "metadata": {}, "sourceType": "script"}