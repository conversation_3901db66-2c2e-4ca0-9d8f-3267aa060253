{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n      m = s && o[s],\n      i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\n\nfunction handleResize() {\n  var e_1, _a;\n\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) return;\n\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1[\"return\"])) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\n\nvar listening = false;\n\nfunction calculate() {\n  var e_2, _a;\n\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b[\"return\"])) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\n\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nexport function useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n\n  var _a = __read(useState(info), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  useEffect(function () {\n    if (!isBrowser) return;\n\n    var subscriber = function subscriber() {\n      setState(info);\n    };\n\n    subscribers.add(subscriber);\n    return function () {\n      subscribers[\"delete\"](subscriber);\n\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "__read", "n", "r", "ar", "e", "push", "error", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON>", "subscribers", "Set", "info", "responsiveConfig", "xs", "sm", "md", "lg", "xl", "handleResize", "e_1", "_a", "oldInfo", "calculate", "subscribers_1", "subscribers_1_1", "subscriber", "e_1_1", "listening", "e_2", "width", "window", "innerWidth", "newInfo", "shouldUpdate", "_b", "Object", "keys", "_c", "key", "e_2_1", "configResponsive", "config", "useResponsive", "addEventListener", "state", "setState", "add", "size", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useResponsive/index.js"], "sourcesContent": ["var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function next() {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction handleResize() {\n  var e_1, _a;\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) return;\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1[\"return\"])) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\nvar listening = false;\nfunction calculate() {\n  var e_2, _a;\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b[\"return\"])) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nexport function useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n  var _a = __read(useState(info), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    if (!isBrowser) return;\n    var subscriber = function subscriber() {\n      setState(info);\n    };\n    subscribers.add(subscriber);\n    return function () {\n      subscribers[\"delete\"](subscriber);\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,UAAUC,CAAV,EAAa;EACnD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA/C;EAAA,IACEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAD,CADZ;EAAA,IAEEI,CAAC,GAAG,CAFN;EAGA,IAAID,CAAJ,EAAO,OAAOA,CAAC,CAACE,IAAF,CAAON,CAAP,CAAP;EACP,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAT,KAAoB,QAA7B,EAAuC,OAAO;IAC5CC,IAAI,EAAE,SAASA,IAAT,GAAgB;MACpB,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAhB,EAAwBP,CAAC,GAAG,KAAK,CAAT;MACxB,OAAO;QACLS,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAF,CADR;QAELK,IAAI,EAAE,CAACV;MAFF,CAAP;IAID;EAP2C,CAAP;EASvC,MAAM,IAAIW,SAAJ,CAAcV,CAAC,GAAG,yBAAH,GAA+B,iCAA9C,CAAN;AACD,CAfD;;AAgBA,IAAIW,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUZ,CAAV,EAAaa,CAAb,EAAgB;EAClD,IAAIT,CAAC,GAAG,OAAOF,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACC,CAAL,EAAQ,OAAOJ,CAAP;EACR,IAAIK,CAAC,GAAGD,CAAC,CAACE,IAAF,CAAON,CAAP,CAAR;EAAA,IACEc,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACH,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACC,CAAC,GAAGT,CAAC,CAACG,IAAF,EAAL,EAAeE,IAApD,EAA0D;MACxDK,EAAE,CAACE,IAAH,CAAQH,CAAC,CAACL,KAAV;IACD;EACF,CAJD,CAIE,OAAOS,KAAP,EAAc;IACdF,CAAC,GAAG;MACFE,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIJ,CAAC,IAAI,CAACA,CAAC,CAACJ,IAAR,KAAiBN,CAAC,GAAGC,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCD,CAAC,CAACE,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAIW,CAAJ,EAAO,MAAMA,CAAC,CAACE,KAAR;IACR;EACF;;EACD,OAAOH,EAAP;AACD,CAvBD;;AAwBA,SAASI,SAAT,EAAoBC,QAApB,QAAoC,OAApC;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,IAAIC,WAAW,GAAG,IAAIC,GAAJ,EAAlB;AACA,IAAIC,IAAJ;AACA,IAAIC,gBAAgB,GAAG;EACrBC,EAAE,EAAE,CADiB;EAErBC,EAAE,EAAE,GAFiB;EAGrBC,EAAE,EAAE,GAHiB;EAIrBC,EAAE,EAAE,GAJiB;EAKrBC,EAAE,EAAE;AALiB,CAAvB;;AAOA,SAASC,YAAT,GAAwB;EACtB,IAAIC,GAAJ,EAASC,EAAT;;EACA,IAAIC,OAAO,GAAGV,IAAd;EACAW,SAAS;EACT,IAAID,OAAO,KAAKV,IAAhB,EAAsB;;EACtB,IAAI;IACF,KAAK,IAAIY,aAAa,GAAGrC,QAAQ,CAACuB,WAAD,CAA5B,EAA2Ce,eAAe,GAAGD,aAAa,CAAC5B,IAAd,EAAlE,EAAwF,CAAC6B,eAAe,CAAC3B,IAAzG,EAA+G2B,eAAe,GAAGD,aAAa,CAAC5B,IAAd,EAAjI,EAAuJ;MACrJ,IAAI8B,UAAU,GAAGD,eAAe,CAAC5B,KAAjC;MACA6B,UAAU;IACX;EACF,CALD,CAKE,OAAOC,KAAP,EAAc;IACdP,GAAG,GAAG;MACJd,KAAK,EAAEqB;IADH,CAAN;EAGD,CATD,SASU;IACR,IAAI;MACF,IAAIF,eAAe,IAAI,CAACA,eAAe,CAAC3B,IAApC,KAA6CuB,EAAE,GAAGG,aAAa,CAAC,QAAD,CAA/D,CAAJ,EAAgFH,EAAE,CAAC3B,IAAH,CAAQ8B,aAAR;IACjF,CAFD,SAEU;MACR,IAAIJ,GAAJ,EAAS,MAAMA,GAAG,CAACd,KAAV;IACV;EACF;AACF;;AACD,IAAIsB,SAAS,GAAG,KAAhB;;AACA,SAASL,SAAT,GAAqB;EACnB,IAAIM,GAAJ,EAASR,EAAT;;EACA,IAAIS,KAAK,GAAGC,MAAM,CAACC,UAAnB;EACA,IAAIC,OAAO,GAAG,EAAd;EACA,IAAIC,YAAY,GAAG,KAAnB;;EACA,IAAI;IACF,KAAK,IAAIC,EAAE,GAAGhD,QAAQ,CAACiD,MAAM,CAACC,IAAP,CAAYxB,gBAAZ,CAAD,CAAjB,EAAkDyB,EAAE,GAAGH,EAAE,CAACvC,IAAH,EAA5D,EAAuE,CAAC0C,EAAE,CAACxC,IAA3E,EAAiFwC,EAAE,GAAGH,EAAE,CAACvC,IAAH,EAAtF,EAAiG;MAC/F,IAAI2C,GAAG,GAAGD,EAAE,CAACzC,KAAb;MACAoC,OAAO,CAACM,GAAD,CAAP,GAAeT,KAAK,IAAIjB,gBAAgB,CAAC0B,GAAD,CAAxC;;MACA,IAAIN,OAAO,CAACM,GAAD,CAAP,KAAiB3B,IAAI,CAAC2B,GAAD,CAAzB,EAAgC;QAC9BL,YAAY,GAAG,IAAf;MACD;IACF;EACF,CARD,CAQE,OAAOM,KAAP,EAAc;IACdX,GAAG,GAAG;MACJvB,KAAK,EAAEkC;IADH,CAAN;EAGD,CAZD,SAYU;IACR,IAAI;MACF,IAAIF,EAAE,IAAI,CAACA,EAAE,CAACxC,IAAV,KAAmBuB,EAAE,GAAGc,EAAE,CAAC,QAAD,CAA1B,CAAJ,EAA2Cd,EAAE,CAAC3B,IAAH,CAAQyC,EAAR;IAC5C,CAFD,SAEU;MACR,IAAIN,GAAJ,EAAS,MAAMA,GAAG,CAACvB,KAAV;IACV;EACF;;EACD,IAAI4B,YAAJ,EAAkB;IAChBtB,IAAI,GAAGqB,OAAP;EACD;AACF;;AACD,OAAO,SAASQ,gBAAT,CAA0BC,MAA1B,EAAkC;EACvC7B,gBAAgB,GAAG6B,MAAnB;EACA,IAAI9B,IAAJ,EAAUW,SAAS;AACpB;AACD,OAAO,SAASoB,aAAT,GAAyB;EAC9B,IAAIlC,SAAS,IAAI,CAACmB,SAAlB,EAA6B;IAC3BhB,IAAI,GAAG,EAAP;IACAW,SAAS;IACTQ,MAAM,CAACa,gBAAP,CAAwB,QAAxB,EAAkCzB,YAAlC;IACAS,SAAS,GAAG,IAAZ;EACD;;EACD,IAAIP,EAAE,GAAGrB,MAAM,CAACQ,QAAQ,CAACI,IAAD,CAAT,EAAiB,CAAjB,CAAf;EAAA,IACEiC,KAAK,GAAGxB,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEyB,QAAQ,GAAGzB,EAAE,CAAC,CAAD,CAFf;;EAGAd,SAAS,CAAC,YAAY;IACpB,IAAI,CAACE,SAAL,EAAgB;;IAChB,IAAIiB,UAAU,GAAG,SAASA,UAAT,GAAsB;MACrCoB,QAAQ,CAAClC,IAAD,CAAR;IACD,CAFD;;IAGAF,WAAW,CAACqC,GAAZ,CAAgBrB,UAAhB;IACA,OAAO,YAAY;MACjBhB,WAAW,CAAC,QAAD,CAAX,CAAsBgB,UAAtB;;MACA,IAAIhB,WAAW,CAACsC,IAAZ,KAAqB,CAAzB,EAA4B;QAC1BjB,MAAM,CAACkB,mBAAP,CAA2B,QAA3B,EAAqC9B,YAArC;QACAS,SAAS,GAAG,KAAZ;MACD;IACF,CAND;EAOD,CAbQ,EAaN,EAbM,CAAT;EAcA,OAAOiB,KAAP;AACD"}, "metadata": {}, "sourceType": "module"}