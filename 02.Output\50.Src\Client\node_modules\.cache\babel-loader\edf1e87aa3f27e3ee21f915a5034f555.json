{"ast": null, "code": "import React from'react';import{getHexColor}from'../../utils/Util.js';import{jsx as _jsx}from\"react/jsx-runtime\";/**\r\n * 色/背景色/文字列の情報を従い、子供のHtmlTagを表示する\r\n * @module CellBox\r\n * @component\r\n * @param {*} props \r\n * @returns div箱\r\n */var CellBox=function CellBox(props){var _props$className;var textStyle={};if(props.text_color){textStyle.color=getHexColor(props.text_color);}if(props.background_color){textStyle.backgroundColor=getHexColor(props.background_color);}return/*#__PURE__*/_jsx(\"div\",{className:props===null||props===void 0?void 0:(_props$className=props.className)===null||_props$className===void 0?void 0:_props$className.trim(),style:textStyle,children:props.children});};export default CellBox;", "map": {"version": 3, "names": ["React", "getHexColor", "CellBox", "props", "textStyle", "text_color", "color", "background_color", "backgroundColor", "className", "trim", "children"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/CellBox.js"], "sourcesContent": ["import React from 'react';\r\nimport { getHexColor } from '../../utils/Util.js';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n  className: PropTypes.string,\r\n  text_color: PropTypes.string,\r\n  background_color: PropTypes.string,\r\n};\r\n\r\n/**\r\n * 色/背景色/文字列の情報を従い、子供のHtmlTagを表示する\r\n * @module CellBox\r\n * @component\r\n * @param {*} props \r\n * @returns div箱\r\n */\r\nconst CellBox = (props) => {\r\n  const textStyle = {};\r\n\r\n  if (props.text_color) {\r\n    textStyle.color = getHexColor(props.text_color);\r\n  }\r\n\r\n  if (props.background_color) {\r\n    textStyle.backgroundColor = getHexColor(props.background_color);\r\n  }\r\n\r\n  return (\r\n    <div className={props?.className?.trim()} style={textStyle}>\r\n      {props.children}\r\n    </div>\r\n  );\r\n};\r\n\r\nCellBox.propTypes = propTypes;\r\nexport default CellBox;\r\n"], "mappings": "AAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,OAASC,WAAT,KAA4B,qBAA5B,C,2CASA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,GAAMC,QAAO,CAAG,QAAVA,QAAU,CAACC,KAAD,CAAW,sBACzB,GAAMC,UAAS,CAAG,EAAlB,CAEA,GAAID,KAAK,CAACE,UAAV,CAAsB,CACpBD,SAAS,CAACE,KAAV,CAAkBL,WAAW,CAACE,KAAK,CAACE,UAAP,CAA7B,CACD,CAED,GAAIF,KAAK,CAACI,gBAAV,CAA4B,CAC1BH,SAAS,CAACI,eAAV,CAA4BP,WAAW,CAACE,KAAK,CAACI,gBAAP,CAAvC,CACD,CAED,mBACE,YAAK,SAAS,CAAEJ,KAAF,SAAEA,KAAF,mCAAEA,KAAK,CAAEM,SAAT,2CAAE,iBAAkBC,IAAlB,EAAhB,CAA0C,KAAK,CAAEN,SAAjD,UACGD,KAAK,CAACQ,QADT,EADF,CAKD,CAhBD,CAmBA,cAAeT,QAAf"}, "metadata": {}, "sourceType": "module"}