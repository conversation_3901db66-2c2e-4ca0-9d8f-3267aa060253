{"ast": null, "code": "var getMapData = require('./_getMapData');\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\n\n\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nmodule.exports = mapCacheGet;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheGet", "key", "get", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapCacheGet.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nmodule.exports = mapCacheGet;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBC,GAArB,EAA0B;EACxB,OAAOH,UAAU,CAAC,IAAD,EAAOG,GAAP,CAAV,CAAsBC,GAAtB,CAA0BD,GAA1B,CAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBJ,WAAjB"}, "metadata": {}, "sourceType": "script"}