{"ast": null, "code": "/* eslint-disable */\n\n/* jscs: disable */\n'use strict'; // pulled specific shims from https://github.com/es-shims/es5-shim\n\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\nvar _toString = ObjectPrototype.toString;\n\nvar isFunction = function (val) {\n  return ObjectPrototype.toString.call(val) === '[object Function]';\n};\n\nvar isArray = function isArray(obj) {\n  return _toString.call(obj) === '[object Array]';\n};\n\nvar isString = function isString(obj) {\n  return _toString.call(obj) === '[object String]';\n};\n\nvar supportsDescriptors = Object.defineProperty && function () {\n  try {\n    Object.defineProperty({}, 'x', {});\n    return true;\n  } catch (e) {\n    /* this is ES3 */\n    return false;\n  }\n}(); // Define configurable, writable and non-enumerable props\n// if they don't exist.\n\n\nvar defineProperty;\n\nif (supportsDescriptors) {\n  defineProperty = function (object, name, method, forceAssign) {\n    if (!forceAssign && name in object) {\n      return;\n    }\n\n    Object.defineProperty(object, name, {\n      configurable: true,\n      enumerable: false,\n      writable: true,\n      value: method\n    });\n  };\n} else {\n  defineProperty = function (object, name, method, forceAssign) {\n    if (!forceAssign && name in object) {\n      return;\n    }\n\n    object[name] = method;\n  };\n}\n\nvar defineProperties = function (object, map, forceAssign) {\n  for (var name in map) {\n    if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n      defineProperty(object, name, map[name], forceAssign);\n    }\n  }\n};\n\nvar toObject = function (o) {\n  if (o == null) {\n    // this matches both null and undefined\n    throw new TypeError(\"can't convert \" + o + ' to object');\n  }\n\n  return Object(o);\n}; //\n// Util\n// ======\n//\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\n\nfunction toInteger(num) {\n  var n = +num;\n\n  if (n !== n) {\n    // isNaN\n    n = 0;\n  } else if (n !== 0 && n !== 1 / 0 && n !== -(1 / 0)) {\n    n = (n > 0 || -1) * Math.floor(Math.abs(n));\n  }\n\n  return n;\n}\n\nfunction ToUint32(x) {\n  return x >>> 0;\n} //\n// Function\n// ========\n//\n// ES-5 ********\n// http://es5.github.com/#x********\n\n\nfunction Empty() {}\n\ndefineProperties(FunctionPrototype, {\n  bind: function bind(that) {\n    // .length is 1\n    // 1. Let Target be the this value.\n    var target = this; // 2. If IsCallable(Target) is false, throw a TypeError exception.\n\n    if (!isFunction(target)) {\n      throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n    } // 3. Let A be a new (possibly empty) internal list of all of the\n    //   argument values provided after thisArg (arg1, arg2 etc), in order.\n    // XXX slicedArgs will stand in for \"A\" if used\n\n\n    var args = array_slice.call(arguments, 1); // for normal call\n    // 4. Let F be a new native ECMAScript object.\n    // 11. Set the [[Prototype]] internal property of F to the standard\n    //   built-in Function prototype object as specified in ********.\n    // 12. Set the [[Call]] internal property of F as described in\n    //   ********.1.\n    // 13. Set the [[Construct]] internal property of F as described in\n    //   ********.2.\n    // 14. Set the [[HasInstance]] internal property of F as described in\n    //   ********.3.\n\n    var binder = function () {\n      if (this instanceof bound) {\n        // ********.2 [[Construct]]\n        // When the [[Construct]] internal method of a function object,\n        // F that was created using the bind function is called with a\n        // list of arguments ExtraArgs, the following steps are taken:\n        // 1. Let target be the value of F's [[TargetFunction]]\n        //   internal property.\n        // 2. If target has no [[Construct]] internal method, a\n        //   TypeError exception is thrown.\n        // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n        //   property.\n        // 4. Let args be a new list containing the same values as the\n        //   list boundArgs in the same order followed by the same\n        //   values as the list ExtraArgs in the same order.\n        // 5. Return the result of calling the [[Construct]] internal\n        //   method of target providing args as the arguments.\n        var result = target.apply(this, args.concat(array_slice.call(arguments)));\n\n        if (Object(result) === result) {\n          return result;\n        }\n\n        return this;\n      } else {\n        // ********.1 [[Call]]\n        // When the [[Call]] internal method of a function object, F,\n        // which was created using the bind function is called with a\n        // this value and a list of arguments ExtraArgs, the following\n        // steps are taken:\n        // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n        //   property.\n        // 2. Let boundThis be the value of F's [[BoundThis]] internal\n        //   property.\n        // 3. Let target be the value of F's [[TargetFunction]] internal\n        //   property.\n        // 4. Let args be a new list containing the same values as the\n        //   list boundArgs in the same order followed by the same\n        //   values as the list ExtraArgs in the same order.\n        // 5. Return the result of calling the [[Call]] internal method\n        //   of target providing boundThis as the this value and\n        //   providing args as the arguments.\n        // equiv: target.call(this, ...boundArgs, ...args)\n        return target.apply(that, args.concat(array_slice.call(arguments)));\n      }\n    }; // 15. If the [[Class]] internal property of Target is \"Function\", then\n    //     a. Let L be the length property of Target minus the length of A.\n    //     b. Set the length own property of F to either 0 or L, whichever is\n    //       larger.\n    // 16. Else set the length own property of F to 0.\n\n\n    var boundLength = Math.max(0, target.length - args.length); // 17. Set the attributes of the length own property of F to the values\n    //   specified in 15.3.5.1.\n\n    var boundArgs = [];\n\n    for (var i = 0; i < boundLength; i++) {\n      boundArgs.push('$' + i);\n    } // XXX Build a dynamic function with desired amount of arguments is the only\n    // way to set the length property of a function.\n    // In environments where Content Security Policies enabled (Chrome extensions,\n    // for ex.) all use of eval or Function costructor throws an exception.\n    // However in all of these environments Function.prototype.bind exists\n    // and so this code will never be executed.\n\n\n    var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n\n    if (target.prototype) {\n      Empty.prototype = target.prototype;\n      bound.prototype = new Empty(); // Clean up dangling references.\n\n      Empty.prototype = null;\n    } // TODO\n    // 18. Set the [[Extensible]] internal property of F to true.\n    // TODO\n    // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n    // 20. Call the [[DefineOwnProperty]] internal method of F with\n    //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n    //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n    //   false.\n    // 21. Call the [[DefineOwnProperty]] internal method of F with\n    //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n    //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n    //   and false.\n    // TODO\n    // NOTE Function objects created using Function.prototype.bind do not\n    // have a prototype property or the [[Code]], [[FormalParameters]], and\n    // [[Scope]] internal properties.\n    // XXX can't delete prototype in pure-js.\n    // 22. Return F.\n\n\n    return bound;\n  }\n}); //\n// Array\n// =====\n//\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\n\ndefineProperties(Array, {\n  isArray: isArray\n});\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\nvar properlyBoxesContext = function properlyBoxed(method) {\n  // Check node 0.6.21 bug where third parameter is not boxed\n  var properlyBoxesNonStrict = true;\n  var properlyBoxesStrict = true;\n\n  if (method) {\n    method.call('foo', function (_, __, context) {\n      if (typeof context !== 'object') {\n        properlyBoxesNonStrict = false;\n      }\n    });\n    method.call([1], function () {\n      'use strict';\n\n      properlyBoxesStrict = typeof this === 'string';\n    }, 'x');\n  }\n\n  return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\n\ndefineProperties(ArrayPrototype, {\n  forEach: function forEach(fun\n  /*, thisp*/\n  ) {\n    var object = toObject(this),\n        self = splitString && isString(this) ? this.split('') : object,\n        thisp = arguments[1],\n        i = -1,\n        length = self.length >>> 0; // If no callback function or if callback is not a callable function\n\n    if (!isFunction(fun)) {\n      throw new TypeError(); // TODO message\n    }\n\n    while (++i < length) {\n      if (i in self) {\n        // Invoke the callback function with call, passing arguments:\n        // context, property value, property key, thisArg object\n        // context\n        fun.call(thisp, self[i], i, object);\n      }\n    }\n  }\n}, !properlyBoxesContext(ArrayPrototype.forEach)); // ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\n\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n  indexOf: function indexOf(sought\n  /*, fromIndex */\n  ) {\n    var self = splitString && isString(this) ? this.split('') : toObject(this),\n        length = self.length >>> 0;\n\n    if (!length) {\n      return -1;\n    }\n\n    var i = 0;\n\n    if (arguments.length > 1) {\n      i = toInteger(arguments[1]);\n    } // handle negative indices\n\n\n    i = i >= 0 ? i : Math.max(0, length + i);\n\n    for (; i < length; i++) {\n      if (i in self && self[i] === sought) {\n        return i;\n      }\n    }\n\n    return -1;\n  }\n}, hasFirefox2IndexOfBug); //\n// String\n// ======\n//\n// ES5 *********\n// http://es5.github.com/#x*********\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\n\nif ('ab'.split(/(?:ab)*/).length !== 2 || '.'.split(/(.?)(.?)/).length !== 4 || 'tesst'.split(/(s)*/)[1] === 't' || 'test'.split(/(?:)/, -1).length !== 4 || ''.split(/.?/).length || '.'.split(/()()/).length > 1) {\n  (function () {\n    var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n    StringPrototype.split = function (separator, limit) {\n      var string = this;\n\n      if (separator === void 0 && limit === 0) {\n        return [];\n      } // If `separator` is not a regex, use native split\n\n\n      if (_toString.call(separator) !== '[object RegExp]') {\n        return string_split.call(this, separator, limit);\n      }\n\n      var output = [],\n          flags = (separator.ignoreCase ? 'i' : '') + (separator.multiline ? 'm' : '') + (separator.extended ? 'x' : '') + ( // Proposed for ES6\n      separator.sticky ? 'y' : ''),\n          // Firefox 3+\n      lastLastIndex = 0,\n          // Make `global` and avoid `lastIndex` issues by working with a copy\n      separator2,\n          match,\n          lastIndex,\n          lastLength;\n      separator = new RegExp(separator.source, flags + 'g');\n      string += ''; // Type-convert\n\n      if (!compliantExecNpcg) {\n        // Doesn't need flags gy, but they don't hurt\n        separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n      }\n      /* Values for `limit`, per the spec:\n       * If undefined: 4294967295 // Math.pow(2, 32) - 1\n       * If 0, Infinity, or NaN: 0\n       * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n       * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n       * If other: Type-convert, then use the above rules\n       */\n\n\n      limit = limit === void 0 ? -1 >>> 0 : // Math.pow(2, 32) - 1\n      ToUint32(limit);\n\n      while (match = separator.exec(string)) {\n        // `separator.lastIndex` is not reliable cross-browser\n        lastIndex = match.index + match[0].length;\n\n        if (lastIndex > lastLastIndex) {\n          output.push(string.slice(lastLastIndex, match.index)); // Fix browsers whose `exec` methods don't consistently return `undefined` for\n          // nonparticipating capturing groups\n\n          if (!compliantExecNpcg && match.length > 1) {\n            match[0].replace(separator2, function () {\n              for (var i = 1; i < arguments.length - 2; i++) {\n                if (arguments[i] === void 0) {\n                  match[i] = void 0;\n                }\n              }\n            });\n          }\n\n          if (match.length > 1 && match.index < string.length) {\n            ArrayPrototype.push.apply(output, match.slice(1));\n          }\n\n          lastLength = match[0].length;\n          lastLastIndex = lastIndex;\n\n          if (output.length >= limit) {\n            break;\n          }\n        }\n\n        if (separator.lastIndex === match.index) {\n          separator.lastIndex++; // Avoid an infinite loop\n        }\n      }\n\n      if (lastLastIndex === string.length) {\n        if (lastLength || !separator.test('')) {\n          output.push('');\n        }\n      } else {\n        output.push(string.slice(lastLastIndex));\n      }\n\n      return output.length > limit ? output.slice(0, limit) : output;\n    };\n  })(); // [bugfix, chrome]\n  // If separator is undefined, then the result array contains just one String,\n  // which is the this value (converted to a String). If limit is not undefined,\n  // then the output array is truncated so that it contains no more than limit\n  // elements.\n  // \"0\".split(undefined, 0) -> []\n\n} else if ('0'.split(void 0, 0).length) {\n  StringPrototype.split = function split(separator, limit) {\n    if (separator === void 0 && limit === 0) {\n      return [];\n    }\n\n    return string_split.call(this, separator, limit);\n  };\n} // ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\n\n\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n  substr: function substr(start, length) {\n    return string_substr.call(this, start < 0 ? (start = this.length + start) < 0 ? 0 : start : start, length);\n  }\n}, hasNegativeSubstrBug);", "map": {"version": 3, "names": ["ArrayPrototype", "Array", "prototype", "ObjectPrototype", "Object", "FunctionPrototype", "Function", "StringPrototype", "String", "array_slice", "slice", "_toString", "toString", "isFunction", "val", "call", "isArray", "obj", "isString", "supportsDescriptors", "defineProperty", "e", "object", "name", "method", "forceAssign", "configurable", "enumerable", "writable", "value", "defineProperties", "map", "hasOwnProperty", "toObject", "o", "TypeError", "toInteger", "num", "n", "Math", "floor", "abs", "ToUint32", "x", "Empty", "bind", "that", "target", "args", "arguments", "binder", "bound", "result", "apply", "concat", "<PERSON><PERSON><PERSON><PERSON>", "max", "length", "boundArgs", "i", "push", "join", "boxedString", "splitString", "properlyBoxesContext", "properlyBoxed", "properlyBoxesNonStrict", "properlyBoxesStrict", "_", "__", "context", "for<PERSON>ach", "fun", "self", "split", "thisp", "hasFirefox2IndexOfBug", "indexOf", "sought", "string_split", "compliantExecNpcg", "exec", "separator", "limit", "string", "output", "flags", "ignoreCase", "multiline", "extended", "sticky", "lastLastIndex", "separator2", "match", "lastIndex", "last<PERSON><PERSON><PERSON>", "RegExp", "source", "index", "replace", "test", "string_substr", "substr", "hasNegativeSubstrBug", "start"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/shims.js"], "sourcesContent": ["/* eslint-disable */\n/* jscs: disable */\n'use strict';\n\n// pulled specific shims from https://github.com/es-shims/es5-shim\n\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\n\nvar _toString = ObjectPrototype.toString;\nvar isFunction = function (val) {\n    return ObjectPrototype.toString.call(val) === '[object Function]';\n};\nvar isArray = function isArray(obj) {\n    return _toString.call(obj) === '[object Array]';\n};\nvar isString = function isString(obj) {\n    return _toString.call(obj) === '[object String]';\n};\n\nvar supportsDescriptors = Object.defineProperty && (function () {\n    try {\n        Object.defineProperty({}, 'x', {});\n        return true;\n    } catch (e) { /* this is ES3 */\n        return false;\n    }\n}());\n\n// Define configurable, writable and non-enumerable props\n// if they don't exist.\nvar defineProperty;\nif (supportsDescriptors) {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        Object.defineProperty(object, name, {\n            configurable: true,\n            enumerable: false,\n            writable: true,\n            value: method\n        });\n    };\n} else {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        object[name] = method;\n    };\n}\nvar defineProperties = function (object, map, forceAssign) {\n    for (var name in map) {\n        if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n          defineProperty(object, name, map[name], forceAssign);\n        }\n    }\n};\n\nvar toObject = function (o) {\n    if (o == null) { // this matches both null and undefined\n        throw new TypeError(\"can't convert \" + o + ' to object');\n    }\n    return Object(o);\n};\n\n//\n// Util\n// ======\n//\n\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\nfunction toInteger(num) {\n    var n = +num;\n    if (n !== n) { // isNaN\n        n = 0;\n    } else if (n !== 0 && n !== (1 / 0) && n !== -(1 / 0)) {\n        n = (n > 0 || -1) * Math.floor(Math.abs(n));\n    }\n    return n;\n}\n\nfunction ToUint32(x) {\n    return x >>> 0;\n}\n\n//\n// Function\n// ========\n//\n\n// ES-5 ********\n// http://es5.github.com/#x********\n\nfunction Empty() {}\n\ndefineProperties(FunctionPrototype, {\n    bind: function bind(that) { // .length is 1\n        // 1. Let Target be the this value.\n        var target = this;\n        // 2. If IsCallable(Target) is false, throw a TypeError exception.\n        if (!isFunction(target)) {\n            throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n        }\n        // 3. Let A be a new (possibly empty) internal list of all of the\n        //   argument values provided after thisArg (arg1, arg2 etc), in order.\n        // XXX slicedArgs will stand in for \"A\" if used\n        var args = array_slice.call(arguments, 1); // for normal call\n        // 4. Let F be a new native ECMAScript object.\n        // 11. Set the [[Prototype]] internal property of F to the standard\n        //   built-in Function prototype object as specified in ********.\n        // 12. Set the [[Call]] internal property of F as described in\n        //   ********.1.\n        // 13. Set the [[Construct]] internal property of F as described in\n        //   ********.2.\n        // 14. Set the [[HasInstance]] internal property of F as described in\n        //   ********.3.\n        var binder = function () {\n\n            if (this instanceof bound) {\n                // ********.2 [[Construct]]\n                // When the [[Construct]] internal method of a function object,\n                // F that was created using the bind function is called with a\n                // list of arguments ExtraArgs, the following steps are taken:\n                // 1. Let target be the value of F's [[TargetFunction]]\n                //   internal property.\n                // 2. If target has no [[Construct]] internal method, a\n                //   TypeError exception is thrown.\n                // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Construct]] internal\n                //   method of target providing args as the arguments.\n\n                var result = target.apply(\n                    this,\n                    args.concat(array_slice.call(arguments))\n                );\n                if (Object(result) === result) {\n                    return result;\n                }\n                return this;\n\n            } else {\n                // ********.1 [[Call]]\n                // When the [[Call]] internal method of a function object, F,\n                // which was created using the bind function is called with a\n                // this value and a list of arguments ExtraArgs, the following\n                // steps are taken:\n                // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 2. Let boundThis be the value of F's [[BoundThis]] internal\n                //   property.\n                // 3. Let target be the value of F's [[TargetFunction]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Call]] internal method\n                //   of target providing boundThis as the this value and\n                //   providing args as the arguments.\n\n                // equiv: target.call(this, ...boundArgs, ...args)\n                return target.apply(\n                    that,\n                    args.concat(array_slice.call(arguments))\n                );\n\n            }\n\n        };\n\n        // 15. If the [[Class]] internal property of Target is \"Function\", then\n        //     a. Let L be the length property of Target minus the length of A.\n        //     b. Set the length own property of F to either 0 or L, whichever is\n        //       larger.\n        // 16. Else set the length own property of F to 0.\n\n        var boundLength = Math.max(0, target.length - args.length);\n\n        // 17. Set the attributes of the length own property of F to the values\n        //   specified in 15.3.5.1.\n        var boundArgs = [];\n        for (var i = 0; i < boundLength; i++) {\n            boundArgs.push('$' + i);\n        }\n\n        // XXX Build a dynamic function with desired amount of arguments is the only\n        // way to set the length property of a function.\n        // In environments where Content Security Policies enabled (Chrome extensions,\n        // for ex.) all use of eval or Function costructor throws an exception.\n        // However in all of these environments Function.prototype.bind exists\n        // and so this code will never be executed.\n        var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n\n        if (target.prototype) {\n            Empty.prototype = target.prototype;\n            bound.prototype = new Empty();\n            // Clean up dangling references.\n            Empty.prototype = null;\n        }\n\n        // TODO\n        // 18. Set the [[Extensible]] internal property of F to true.\n\n        // TODO\n        // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n        // 20. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n        //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n        //   false.\n        // 21. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n        //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n        //   and false.\n\n        // TODO\n        // NOTE Function objects created using Function.prototype.bind do not\n        // have a prototype property or the [[Code]], [[FormalParameters]], and\n        // [[Scope]] internal properties.\n        // XXX can't delete prototype in pure-js.\n\n        // 22. Return F.\n        return bound;\n    }\n});\n\n//\n// Array\n// =====\n//\n\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\ndefineProperties(Array, { isArray: isArray });\n\n\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\nvar properlyBoxesContext = function properlyBoxed(method) {\n    // Check node 0.6.21 bug where third parameter is not boxed\n    var properlyBoxesNonStrict = true;\n    var properlyBoxesStrict = true;\n    if (method) {\n        method.call('foo', function (_, __, context) {\n            if (typeof context !== 'object') { properlyBoxesNonStrict = false; }\n        });\n\n        method.call([1], function () {\n            'use strict';\n            properlyBoxesStrict = typeof this === 'string';\n        }, 'x');\n    }\n    return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\n\ndefineProperties(ArrayPrototype, {\n    forEach: function forEach(fun /*, thisp*/) {\n        var object = toObject(this),\n            self = splitString && isString(this) ? this.split('') : object,\n            thisp = arguments[1],\n            i = -1,\n            length = self.length >>> 0;\n\n        // If no callback function or if callback is not a callable function\n        if (!isFunction(fun)) {\n            throw new TypeError(); // TODO message\n        }\n\n        while (++i < length) {\n            if (i in self) {\n                // Invoke the callback function with call, passing arguments:\n                // context, property value, property key, thisArg object\n                // context\n                fun.call(thisp, self[i], i, object);\n            }\n        }\n    }\n}, !properlyBoxesContext(ArrayPrototype.forEach));\n\n// ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n    indexOf: function indexOf(sought /*, fromIndex */ ) {\n        var self = splitString && isString(this) ? this.split('') : toObject(this),\n            length = self.length >>> 0;\n\n        if (!length) {\n            return -1;\n        }\n\n        var i = 0;\n        if (arguments.length > 1) {\n            i = toInteger(arguments[1]);\n        }\n\n        // handle negative indices\n        i = i >= 0 ? i : Math.max(0, length + i);\n        for (; i < length; i++) {\n            if (i in self && self[i] === sought) {\n                return i;\n            }\n        }\n        return -1;\n    }\n}, hasFirefox2IndexOfBug);\n\n//\n// String\n// ======\n//\n\n// ES5 *********\n// http://es5.github.com/#x*********\n\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\nif (\n    'ab'.split(/(?:ab)*/).length !== 2 ||\n    '.'.split(/(.?)(.?)/).length !== 4 ||\n    'tesst'.split(/(s)*/)[1] === 't' ||\n    'test'.split(/(?:)/, -1).length !== 4 ||\n    ''.split(/.?/).length ||\n    '.'.split(/()()/).length > 1\n) {\n    (function () {\n        var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n        StringPrototype.split = function (separator, limit) {\n            var string = this;\n            if (separator === void 0 && limit === 0) {\n                return [];\n            }\n\n            // If `separator` is not a regex, use native split\n            if (_toString.call(separator) !== '[object RegExp]') {\n                return string_split.call(this, separator, limit);\n            }\n\n            var output = [],\n                flags = (separator.ignoreCase ? 'i' : '') +\n                        (separator.multiline  ? 'm' : '') +\n                        (separator.extended   ? 'x' : '') + // Proposed for ES6\n                        (separator.sticky     ? 'y' : ''), // Firefox 3+\n                lastLastIndex = 0,\n                // Make `global` and avoid `lastIndex` issues by working with a copy\n                separator2, match, lastIndex, lastLength;\n            separator = new RegExp(separator.source, flags + 'g');\n            string += ''; // Type-convert\n            if (!compliantExecNpcg) {\n                // Doesn't need flags gy, but they don't hurt\n                separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n            }\n            /* Values for `limit`, per the spec:\n             * If undefined: 4294967295 // Math.pow(2, 32) - 1\n             * If 0, Infinity, or NaN: 0\n             * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n             * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n             * If other: Type-convert, then use the above rules\n             */\n            limit = limit === void 0 ?\n                -1 >>> 0 : // Math.pow(2, 32) - 1\n                ToUint32(limit);\n            while (match = separator.exec(string)) {\n                // `separator.lastIndex` is not reliable cross-browser\n                lastIndex = match.index + match[0].length;\n                if (lastIndex > lastLastIndex) {\n                    output.push(string.slice(lastLastIndex, match.index));\n                    // Fix browsers whose `exec` methods don't consistently return `undefined` for\n                    // nonparticipating capturing groups\n                    if (!compliantExecNpcg && match.length > 1) {\n                        match[0].replace(separator2, function () {\n                            for (var i = 1; i < arguments.length - 2; i++) {\n                                if (arguments[i] === void 0) {\n                                    match[i] = void 0;\n                                }\n                            }\n                        });\n                    }\n                    if (match.length > 1 && match.index < string.length) {\n                        ArrayPrototype.push.apply(output, match.slice(1));\n                    }\n                    lastLength = match[0].length;\n                    lastLastIndex = lastIndex;\n                    if (output.length >= limit) {\n                        break;\n                    }\n                }\n                if (separator.lastIndex === match.index) {\n                    separator.lastIndex++; // Avoid an infinite loop\n                }\n            }\n            if (lastLastIndex === string.length) {\n                if (lastLength || !separator.test('')) {\n                    output.push('');\n                }\n            } else {\n                output.push(string.slice(lastLastIndex));\n            }\n            return output.length > limit ? output.slice(0, limit) : output;\n        };\n    }());\n\n// [bugfix, chrome]\n// If separator is undefined, then the result array contains just one String,\n// which is the this value (converted to a String). If limit is not undefined,\n// then the output array is truncated so that it contains no more than limit\n// elements.\n// \"0\".split(undefined, 0) -> []\n} else if ('0'.split(void 0, 0).length) {\n    StringPrototype.split = function split(separator, limit) {\n        if (separator === void 0 && limit === 0) { return []; }\n        return string_split.call(this, separator, limit);\n    };\n}\n\n// ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n    substr: function substr(start, length) {\n        return string_substr.call(\n            this,\n            start < 0 ? ((start = this.length + start) < 0 ? 0 : start) : start,\n            length\n        );\n    }\n}, hasNegativeSubstrBug);\n"], "mappings": "AAAA;;AACA;AACA,a,CAEA;;AAEA,IAAIA,cAAc,GAAGC,KAAK,CAACC,SAA3B;AACA,IAAIC,eAAe,GAAGC,MAAM,CAACF,SAA7B;AACA,IAAIG,iBAAiB,GAAGC,QAAQ,CAACJ,SAAjC;AACA,IAAIK,eAAe,GAAGC,MAAM,CAACN,SAA7B;AACA,IAAIO,WAAW,GAAGT,cAAc,CAACU,KAAjC;AAEA,IAAIC,SAAS,GAAGR,eAAe,CAACS,QAAhC;;AACA,IAAIC,UAAU,GAAG,UAAUC,GAAV,EAAe;EAC5B,OAAOX,eAAe,CAACS,QAAhB,CAAyBG,IAAzB,CAA8BD,GAA9B,MAAuC,mBAA9C;AACH,CAFD;;AAGA,IAAIE,OAAO,GAAG,SAASA,OAAT,CAAiBC,GAAjB,EAAsB;EAChC,OAAON,SAAS,CAACI,IAAV,CAAeE,GAAf,MAAwB,gBAA/B;AACH,CAFD;;AAGA,IAAIC,QAAQ,GAAG,SAASA,QAAT,CAAkBD,GAAlB,EAAuB;EAClC,OAAON,SAAS,CAACI,IAAV,CAAeE,GAAf,MAAwB,iBAA/B;AACH,CAFD;;AAIA,IAAIE,mBAAmB,GAAGf,MAAM,CAACgB,cAAP,IAA0B,YAAY;EAC5D,IAAI;IACAhB,MAAM,CAACgB,cAAP,CAAsB,EAAtB,EAA0B,GAA1B,EAA+B,EAA/B;IACA,OAAO,IAAP;EACH,CAHD,CAGE,OAAOC,CAAP,EAAU;IAAE;IACV,OAAO,KAAP;EACH;AACJ,CAPmD,EAApD,C,CASA;AACA;;;AACA,IAAID,cAAJ;;AACA,IAAID,mBAAJ,EAAyB;EACrBC,cAAc,GAAG,UAAUE,MAAV,EAAkBC,IAAlB,EAAwBC,MAAxB,EAAgCC,WAAhC,EAA6C;IAC1D,IAAI,CAACA,WAAD,IAAiBF,IAAI,IAAID,MAA7B,EAAsC;MAAE;IAAS;;IACjDlB,MAAM,CAACgB,cAAP,CAAsBE,MAAtB,EAA8BC,IAA9B,EAAoC;MAChCG,YAAY,EAAE,IADkB;MAEhCC,UAAU,EAAE,KAFoB;MAGhCC,QAAQ,EAAE,IAHsB;MAIhCC,KAAK,EAAEL;IAJyB,CAApC;EAMH,CARD;AASH,CAVD,MAUO;EACHJ,cAAc,GAAG,UAAUE,MAAV,EAAkBC,IAAlB,EAAwBC,MAAxB,EAAgCC,WAAhC,EAA6C;IAC1D,IAAI,CAACA,WAAD,IAAiBF,IAAI,IAAID,MAA7B,EAAsC;MAAE;IAAS;;IACjDA,MAAM,CAACC,IAAD,CAAN,GAAeC,MAAf;EACH,CAHD;AAIH;;AACD,IAAIM,gBAAgB,GAAG,UAAUR,MAAV,EAAkBS,GAAlB,EAAuBN,WAAvB,EAAoC;EACvD,KAAK,IAAIF,IAAT,IAAiBQ,GAAjB,EAAsB;IAClB,IAAI5B,eAAe,CAAC6B,cAAhB,CAA+BjB,IAA/B,CAAoCgB,GAApC,EAAyCR,IAAzC,CAAJ,EAAoD;MAClDH,cAAc,CAACE,MAAD,EAASC,IAAT,EAAeQ,GAAG,CAACR,IAAD,CAAlB,EAA0BE,WAA1B,CAAd;IACD;EACJ;AACJ,CAND;;AAQA,IAAIQ,QAAQ,GAAG,UAAUC,CAAV,EAAa;EACxB,IAAIA,CAAC,IAAI,IAAT,EAAe;IAAE;IACb,MAAM,IAAIC,SAAJ,CAAc,mBAAmBD,CAAnB,GAAuB,YAArC,CAAN;EACH;;EACD,OAAO9B,MAAM,CAAC8B,CAAD,CAAb;AACH,CALD,C,CAOA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAEA,SAASE,SAAT,CAAmBC,GAAnB,EAAwB;EACpB,IAAIC,CAAC,GAAG,CAACD,GAAT;;EACA,IAAIC,CAAC,KAAKA,CAAV,EAAa;IAAE;IACXA,CAAC,GAAG,CAAJ;EACH,CAFD,MAEO,IAAIA,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAM,IAAI,CAAtB,IAA4BA,CAAC,KAAK,EAAE,IAAI,CAAN,CAAtC,EAAgD;IACnDA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAJ,IAAS,CAAC,CAAX,IAAgBC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,GAAL,CAASH,CAAT,CAAX,CAApB;EACH;;EACD,OAAOA,CAAP;AACH;;AAED,SAASI,QAAT,CAAkBC,CAAlB,EAAqB;EACjB,OAAOA,CAAC,KAAK,CAAb;AACH,C,CAED;AACA;AACA;AACA;AAEA;AACA;;;AAEA,SAASC,KAAT,GAAiB,CAAE;;AAEnBd,gBAAgB,CAACzB,iBAAD,EAAoB;EAChCwC,IAAI,EAAE,SAASA,IAAT,CAAcC,IAAd,EAAoB;IAAE;IACxB;IACA,IAAIC,MAAM,GAAG,IAAb,CAFsB,CAGtB;;IACA,IAAI,CAAClC,UAAU,CAACkC,MAAD,CAAf,EAAyB;MACrB,MAAM,IAAIZ,SAAJ,CAAc,oDAAoDY,MAAlE,CAAN;IACH,CANqB,CAOtB;IACA;IACA;;;IACA,IAAIC,IAAI,GAAGvC,WAAW,CAACM,IAAZ,CAAiBkC,SAAjB,EAA4B,CAA5B,CAAX,CAVsB,CAUqB;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIC,MAAM,GAAG,YAAY;MAErB,IAAI,gBAAgBC,KAApB,EAA2B;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,IAAIC,MAAM,GAAGL,MAAM,CAACM,KAAP,CACT,IADS,EAETL,IAAI,CAACM,MAAL,CAAY7C,WAAW,CAACM,IAAZ,CAAiBkC,SAAjB,CAAZ,CAFS,CAAb;;QAIA,IAAI7C,MAAM,CAACgD,MAAD,CAAN,KAAmBA,MAAvB,EAA+B;UAC3B,OAAOA,MAAP;QACH;;QACD,OAAO,IAAP;MAEH,CA1BD,MA0BO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA,OAAOL,MAAM,CAACM,KAAP,CACHP,IADG,EAEHE,IAAI,CAACM,MAAL,CAAY7C,WAAW,CAACM,IAAZ,CAAiBkC,SAAjB,CAAZ,CAFG,CAAP;MAKH;IAEJ,CAvDD,CApBsB,CA6EtB;IACA;IACA;IACA;IACA;;;IAEA,IAAIM,WAAW,GAAGhB,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAYT,MAAM,CAACU,MAAP,GAAgBT,IAAI,CAACS,MAAjC,CAAlB,CAnFsB,CAqFtB;IACA;;IACA,IAAIC,SAAS,GAAG,EAAhB;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,WAApB,EAAiCI,CAAC,EAAlC,EAAsC;MAClCD,SAAS,CAACE,IAAV,CAAe,MAAMD,CAArB;IACH,CA1FqB,CA4FtB;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAIR,KAAK,GAAG7C,QAAQ,CAAC,QAAD,EAAW,sBAAsBoD,SAAS,CAACG,IAAV,CAAe,GAAf,CAAtB,GAA4C,4CAAvD,CAAR,CAA6GX,MAA7G,CAAZ;;IAEA,IAAIH,MAAM,CAAC7C,SAAX,EAAsB;MAClB0C,KAAK,CAAC1C,SAAN,GAAkB6C,MAAM,CAAC7C,SAAzB;MACAiD,KAAK,CAACjD,SAAN,GAAkB,IAAI0C,KAAJ,EAAlB,CAFkB,CAGlB;;MACAA,KAAK,CAAC1C,SAAN,GAAkB,IAAlB;IACH,CAzGqB,CA2GtB;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;;;IACA,OAAOiD,KAAP;EACH;AAlI+B,CAApB,CAAhB,C,CAqIA;AACA;AACA;AACA;AAEA;AACA;AACA;;AACArB,gBAAgB,CAAC7B,KAAD,EAAQ;EAAEe,OAAO,EAAEA;AAAX,CAAR,CAAhB;AAGA,IAAI8C,WAAW,GAAG1D,MAAM,CAAC,GAAD,CAAxB;AACA,IAAI2D,WAAW,GAAGD,WAAW,CAAC,CAAD,CAAX,KAAmB,GAAnB,IAA0B,EAAE,KAAKA,WAAP,CAA5C;;AAEA,IAAIE,oBAAoB,GAAG,SAASC,aAAT,CAAuBzC,MAAvB,EAA+B;EACtD;EACA,IAAI0C,sBAAsB,GAAG,IAA7B;EACA,IAAIC,mBAAmB,GAAG,IAA1B;;EACA,IAAI3C,MAAJ,EAAY;IACRA,MAAM,CAACT,IAAP,CAAY,KAAZ,EAAmB,UAAUqD,CAAV,EAAaC,EAAb,EAAiBC,OAAjB,EAA0B;MACzC,IAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;QAAEJ,sBAAsB,GAAG,KAAzB;MAAiC;IACvE,CAFD;IAIA1C,MAAM,CAACT,IAAP,CAAY,CAAC,CAAD,CAAZ,EAAiB,YAAY;MACzB;;MACAoD,mBAAmB,GAAG,OAAO,IAAP,KAAgB,QAAtC;IACH,CAHD,EAGG,GAHH;EAIH;;EACD,OAAO,CAAC,CAAC3C,MAAF,IAAY0C,sBAAZ,IAAsCC,mBAA7C;AACH,CAfD;;AAiBArC,gBAAgB,CAAC9B,cAAD,EAAiB;EAC7BuE,OAAO,EAAE,SAASA,OAAT,CAAiBC;EAAI;EAArB,EAAkC;IACvC,IAAIlD,MAAM,GAAGW,QAAQ,CAAC,IAAD,CAArB;IAAA,IACIwC,IAAI,GAAGV,WAAW,IAAI7C,QAAQ,CAAC,IAAD,CAAvB,GAAgC,KAAKwD,KAAL,CAAW,EAAX,CAAhC,GAAiDpD,MAD5D;IAAA,IAEIqD,KAAK,GAAG1B,SAAS,CAAC,CAAD,CAFrB;IAAA,IAGIU,CAAC,GAAG,CAAC,CAHT;IAAA,IAIIF,MAAM,GAAGgB,IAAI,CAAChB,MAAL,KAAgB,CAJ7B,CADuC,CAOvC;;IACA,IAAI,CAAC5C,UAAU,CAAC2D,GAAD,CAAf,EAAsB;MAClB,MAAM,IAAIrC,SAAJ,EAAN,CADkB,CACK;IAC1B;;IAED,OAAO,EAAEwB,CAAF,GAAMF,MAAb,EAAqB;MACjB,IAAIE,CAAC,IAAIc,IAAT,EAAe;QACX;QACA;QACA;QACAD,GAAG,CAACzD,IAAJ,CAAS4D,KAAT,EAAgBF,IAAI,CAACd,CAAD,CAApB,EAAyBA,CAAzB,EAA4BrC,MAA5B;MACH;IACJ;EACJ;AArB4B,CAAjB,EAsBb,CAAC0C,oBAAoB,CAAChE,cAAc,CAACuE,OAAhB,CAtBR,CAAhB,C,CAwBA;AACA;AACA;;AACA,IAAIK,qBAAqB,GAAG3E,KAAK,CAACC,SAAN,CAAgB2E,OAAhB,IAA2B,CAAC,CAAD,EAAI,CAAJ,EAAOA,OAAP,CAAe,CAAf,EAAkB,CAAlB,MAAyB,CAAC,CAAjF;AACA/C,gBAAgB,CAAC9B,cAAD,EAAiB;EAC7B6E,OAAO,EAAE,SAASA,OAAT,CAAiBC;EAAO;EAAxB,EAA2C;IAChD,IAAIL,IAAI,GAAGV,WAAW,IAAI7C,QAAQ,CAAC,IAAD,CAAvB,GAAgC,KAAKwD,KAAL,CAAW,EAAX,CAAhC,GAAiDzC,QAAQ,CAAC,IAAD,CAApE;IAAA,IACIwB,MAAM,GAAGgB,IAAI,CAAChB,MAAL,KAAgB,CAD7B;;IAGA,IAAI,CAACA,MAAL,EAAa;MACT,OAAO,CAAC,CAAR;IACH;;IAED,IAAIE,CAAC,GAAG,CAAR;;IACA,IAAIV,SAAS,CAACQ,MAAV,GAAmB,CAAvB,EAA0B;MACtBE,CAAC,GAAGvB,SAAS,CAACa,SAAS,CAAC,CAAD,CAAV,CAAb;IACH,CAX+C,CAahD;;;IACAU,CAAC,GAAGA,CAAC,IAAI,CAAL,GAASA,CAAT,GAAapB,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAYC,MAAM,GAAGE,CAArB,CAAjB;;IACA,OAAOA,CAAC,GAAGF,MAAX,EAAmBE,CAAC,EAApB,EAAwB;MACpB,IAAIA,CAAC,IAAIc,IAAL,IAAaA,IAAI,CAACd,CAAD,CAAJ,KAAYmB,MAA7B,EAAqC;QACjC,OAAOnB,CAAP;MACH;IACJ;;IACD,OAAO,CAAC,CAAR;EACH;AAtB4B,CAAjB,EAuBbiB,qBAvBa,CAAhB,C,CAyBA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIG,YAAY,GAAGxE,eAAe,CAACmE,KAAnC;;AACA,IACI,KAAKA,KAAL,CAAW,SAAX,EAAsBjB,MAAtB,KAAiC,CAAjC,IACA,IAAIiB,KAAJ,CAAU,UAAV,EAAsBjB,MAAtB,KAAiC,CADjC,IAEA,QAAQiB,KAAR,CAAc,MAAd,EAAsB,CAAtB,MAA6B,GAF7B,IAGA,OAAOA,KAAP,CAAa,MAAb,EAAqB,CAAC,CAAtB,EAAyBjB,MAAzB,KAAoC,CAHpC,IAIA,GAAGiB,KAAH,CAAS,IAAT,EAAejB,MAJf,IAKA,IAAIiB,KAAJ,CAAU,MAAV,EAAkBjB,MAAlB,GAA2B,CAN/B,EAOE;EACG,aAAY;IACT,IAAIuB,iBAAiB,GAAG,OAAOC,IAAP,CAAY,EAAZ,EAAgB,CAAhB,MAAuB,KAAK,CAApD,CADS,CAC8C;;IAEvD1E,eAAe,CAACmE,KAAhB,GAAwB,UAAUQ,SAAV,EAAqBC,KAArB,EAA4B;MAChD,IAAIC,MAAM,GAAG,IAAb;;MACA,IAAIF,SAAS,KAAK,KAAK,CAAnB,IAAwBC,KAAK,KAAK,CAAtC,EAAyC;QACrC,OAAO,EAAP;MACH,CAJ+C,CAMhD;;;MACA,IAAIxE,SAAS,CAACI,IAAV,CAAemE,SAAf,MAA8B,iBAAlC,EAAqD;QACjD,OAAOH,YAAY,CAAChE,IAAb,CAAkB,IAAlB,EAAwBmE,SAAxB,EAAmCC,KAAnC,CAAP;MACH;;MAED,IAAIE,MAAM,GAAG,EAAb;MAAA,IACIC,KAAK,GAAG,CAACJ,SAAS,CAACK,UAAV,GAAuB,GAAvB,GAA6B,EAA9B,KACCL,SAAS,CAACM,SAAV,GAAuB,GAAvB,GAA6B,EAD9B,KAECN,SAAS,CAACO,QAAV,GAAuB,GAAvB,GAA6B,EAF9B,MAEoC;MACnCP,SAAS,CAACQ,MAAV,GAAuB,GAAvB,GAA6B,EAH9B,CADZ;MAAA,IAI+C;MAC3CC,aAAa,GAAG,CALpB;MAAA,IAMI;MACAC,UAPJ;MAAA,IAOgBC,KAPhB;MAAA,IAOuBC,SAPvB;MAAA,IAOkCC,UAPlC;MAQAb,SAAS,GAAG,IAAIc,MAAJ,CAAWd,SAAS,CAACe,MAArB,EAA6BX,KAAK,GAAG,GAArC,CAAZ;MACAF,MAAM,IAAI,EAAV,CApBgD,CAoBlC;;MACd,IAAI,CAACJ,iBAAL,EAAwB;QACpB;QACAY,UAAU,GAAG,IAAII,MAAJ,CAAW,MAAMd,SAAS,CAACe,MAAhB,GAAyB,UAApC,EAAgDX,KAAhD,CAAb;MACH;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;;;MACYH,KAAK,GAAGA,KAAK,KAAK,KAAK,CAAf,GACJ,CAAC,CAAD,KAAO,CADH,GACO;MACXzC,QAAQ,CAACyC,KAAD,CAFZ;;MAGA,OAAOU,KAAK,GAAGX,SAAS,CAACD,IAAV,CAAeG,MAAf,CAAf,EAAuC;QACnC;QACAU,SAAS,GAAGD,KAAK,CAACK,KAAN,GAAcL,KAAK,CAAC,CAAD,CAAL,CAASpC,MAAnC;;QACA,IAAIqC,SAAS,GAAGH,aAAhB,EAA+B;UAC3BN,MAAM,CAACzB,IAAP,CAAYwB,MAAM,CAAC1E,KAAP,CAAaiF,aAAb,EAA4BE,KAAK,CAACK,KAAlC,CAAZ,EAD2B,CAE3B;UACA;;UACA,IAAI,CAAClB,iBAAD,IAAsBa,KAAK,CAACpC,MAAN,GAAe,CAAzC,EAA4C;YACxCoC,KAAK,CAAC,CAAD,CAAL,CAASM,OAAT,CAAiBP,UAAjB,EAA6B,YAAY;cACrC,KAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,SAAS,CAACQ,MAAV,GAAmB,CAAvC,EAA0CE,CAAC,EAA3C,EAA+C;gBAC3C,IAAIV,SAAS,CAACU,CAAD,CAAT,KAAiB,KAAK,CAA1B,EAA6B;kBACzBkC,KAAK,CAAClC,CAAD,CAAL,GAAW,KAAK,CAAhB;gBACH;cACJ;YACJ,CAND;UAOH;;UACD,IAAIkC,KAAK,CAACpC,MAAN,GAAe,CAAf,IAAoBoC,KAAK,CAACK,KAAN,GAAcd,MAAM,CAAC3B,MAA7C,EAAqD;YACjDzD,cAAc,CAAC4D,IAAf,CAAoBP,KAApB,CAA0BgC,MAA1B,EAAkCQ,KAAK,CAACnF,KAAN,CAAY,CAAZ,CAAlC;UACH;;UACDqF,UAAU,GAAGF,KAAK,CAAC,CAAD,CAAL,CAASpC,MAAtB;UACAkC,aAAa,GAAGG,SAAhB;;UACA,IAAIT,MAAM,CAAC5B,MAAP,IAAiB0B,KAArB,EAA4B;YACxB;UACH;QACJ;;QACD,IAAID,SAAS,CAACY,SAAV,KAAwBD,KAAK,CAACK,KAAlC,EAAyC;UACrChB,SAAS,CAACY,SAAV,GADqC,CACd;QAC1B;MACJ;;MACD,IAAIH,aAAa,KAAKP,MAAM,CAAC3B,MAA7B,EAAqC;QACjC,IAAIsC,UAAU,IAAI,CAACb,SAAS,CAACkB,IAAV,CAAe,EAAf,CAAnB,EAAuC;UACnCf,MAAM,CAACzB,IAAP,CAAY,EAAZ;QACH;MACJ,CAJD,MAIO;QACHyB,MAAM,CAACzB,IAAP,CAAYwB,MAAM,CAAC1E,KAAP,CAAaiF,aAAb,CAAZ;MACH;;MACD,OAAON,MAAM,CAAC5B,MAAP,GAAgB0B,KAAhB,GAAwBE,MAAM,CAAC3E,KAAP,CAAa,CAAb,EAAgByE,KAAhB,CAAxB,GAAiDE,MAAxD;IACH,CAxED;EAyEH,CA5EA,GAAD,CADF,CA+EF;EACA;EACA;EACA;EACA;EACA;;AACC,CA5FD,MA4FO,IAAI,IAAIX,KAAJ,CAAU,KAAK,CAAf,EAAkB,CAAlB,EAAqBjB,MAAzB,EAAiC;EACpClD,eAAe,CAACmE,KAAhB,GAAwB,SAASA,KAAT,CAAeQ,SAAf,EAA0BC,KAA1B,EAAiC;IACrD,IAAID,SAAS,KAAK,KAAK,CAAnB,IAAwBC,KAAK,KAAK,CAAtC,EAAyC;MAAE,OAAO,EAAP;IAAY;;IACvD,OAAOJ,YAAY,CAAChE,IAAb,CAAkB,IAAlB,EAAwBmE,SAAxB,EAAmCC,KAAnC,CAAP;EACH,CAHD;AAIH,C,CAED;AACA;AACA;AACA;AACA;;;AACA,IAAIkB,aAAa,GAAG9F,eAAe,CAAC+F,MAApC;AACA,IAAIC,oBAAoB,GAAG,GAAGD,MAAH,IAAa,KAAKA,MAAL,CAAY,CAAC,CAAb,MAAoB,GAA5D;AACAxE,gBAAgB,CAACvB,eAAD,EAAkB;EAC9B+F,MAAM,EAAE,SAASA,MAAT,CAAgBE,KAAhB,EAAuB/C,MAAvB,EAA+B;IACnC,OAAO4C,aAAa,CAACtF,IAAd,CACH,IADG,EAEHyF,KAAK,GAAG,CAAR,GAAa,CAACA,KAAK,GAAG,KAAK/C,MAAL,GAAc+C,KAAvB,IAAgC,CAAhC,GAAoC,CAApC,GAAwCA,KAArD,GAA8DA,KAF3D,EAGH/C,MAHG,CAAP;EAKH;AAP6B,CAAlB,EAQb8C,oBARa,CAAhB"}, "metadata": {}, "sourceType": "script"}