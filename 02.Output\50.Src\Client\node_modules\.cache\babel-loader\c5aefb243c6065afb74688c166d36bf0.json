{"ast": null, "code": "import URLSearchParams from './classes/URLSearchParams.js';\nimport FormData from './classes/FormData.js';\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\n\nconst isStandardBrowserEnv = (() => {\n  let product;\n\n  if (typeof navigator !== 'undefined' && ((product = navigator.product) === 'ReactNative' || product === 'NativeScript' || product === 'NS')) {\n    return false;\n  }\n\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n})();\n\nexport default {\n  isBrowser: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob\n  },\n  isStandardBrowserEnv,\n  protocols: ['http', 'https', 'file', 'blob', 'url', 'data']\n};", "map": {"version": 3, "names": ["URLSearchParams", "FormData", "isStandardBrowserEnv", "product", "navigator", "window", "document", "<PERSON><PERSON><PERSON><PERSON>", "classes", "Blob", "protocols"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/platform/browser/index.js"], "sourcesContent": ["import URLSearchParams from './classes/URLSearchParams.js'\nimport FormData from './classes/FormData.js'\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\nconst isStandardBrowserEnv = (() => {\n  let product;\n  if (typeof navigator !== 'undefined' && (\n    (product = navigator.product) === 'ReactNative' ||\n    product === 'NativeScript' ||\n    product === 'NS')\n  ) {\n    return false;\n  }\n\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n})();\n\nexport default {\n  isBrowser: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob\n  },\n  isStandardBrowserEnv,\n  protocols: ['http', 'https', 'file', 'blob', 'url', 'data']\n};\n"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,8BAA5B;AACA,OAAOC,QAAP,MAAqB,uBAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG,CAAC,MAAM;EAClC,IAAIC,OAAJ;;EACA,IAAI,OAAOC,SAAP,KAAqB,WAArB,KACF,CAACD,OAAO,GAAGC,SAAS,CAACD,OAArB,MAAkC,aAAlC,IACAA,OAAO,KAAK,cADZ,IAEAA,OAAO,KAAK,IAHV,CAAJ,EAIE;IACA,OAAO,KAAP;EACD;;EAED,OAAO,OAAOE,MAAP,KAAkB,WAAlB,IAAiC,OAAOC,QAAP,KAAoB,WAA5D;AACD,CAX4B,GAA7B;;AAaA,eAAe;EACbC,SAAS,EAAE,IADE;EAEbC,OAAO,EAAE;IACPR,eADO;IAEPC,QAFO;IAGPQ;EAHO,CAFI;EAObP,oBAPa;EAQbQ,SAAS,EAAE,CAAC,MAAD,EAAS,OAAT,EAAkB,MAAlB,EAA0B,MAA1B,EAAkC,KAAlC,EAAyC,MAAzC;AARE,CAAf"}, "metadata": {}, "sourceType": "module"}