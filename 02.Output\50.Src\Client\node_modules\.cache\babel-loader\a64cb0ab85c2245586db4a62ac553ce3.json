{"ast": null, "code": "'use strict';\n\nmodule.exports = [// streaming transports\nrequire('./transport/websocket'), require('./transport/xhr-streaming'), require('./transport/xdr-streaming'), require('./transport/eventsource'), require('./transport/lib/iframe-wrap')(require('./transport/eventsource')) // polling transports\n, require('./transport/htmlfile'), require('./transport/lib/iframe-wrap')(require('./transport/htmlfile')), require('./transport/xhr-polling'), require('./transport/xdr-polling'), require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling')), require('./transport/jsonp-polling')];", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport-list.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = [\n  // streaming transports\n  require('./transport/websocket')\n, require('./transport/xhr-streaming')\n, require('./transport/xdr-streaming')\n, require('./transport/eventsource')\n, require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n  // polling transports\n, require('./transport/htmlfile')\n, require('./transport/lib/iframe-wrap')(require('./transport/htmlfile'))\n, require('./transport/xhr-polling')\n, require('./transport/xdr-polling')\n, require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling'))\n, require('./transport/jsonp-polling')\n];\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiB,CACf;AACAC,OAAO,CAAC,uBAAD,CAFQ,EAGfA,OAAO,CAAC,2BAAD,CAHQ,EAIfA,OAAO,CAAC,2BAAD,CAJQ,EAKfA,OAAO,CAAC,yBAAD,CALQ,EAMfA,OAAO,CAAC,6BAAD,CAAP,CAAuCA,OAAO,CAAC,yBAAD,CAA9C,CANe,CAQf;AARe,EASfA,OAAO,CAAC,sBAAD,CATQ,EAUfA,OAAO,CAAC,6BAAD,CAAP,CAAuCA,OAAO,CAAC,sBAAD,CAA9C,CAVe,EAWfA,OAAO,CAAC,yBAAD,CAXQ,EAYfA,OAAO,CAAC,yBAAD,CAZQ,EAafA,OAAO,CAAC,6BAAD,CAAP,CAAuCA,OAAO,CAAC,yBAAD,CAA9C,CAbe,EAcfA,OAAO,CAAC,2BAAD,CAdQ,CAAjB"}, "metadata": {}, "sourceType": "script"}