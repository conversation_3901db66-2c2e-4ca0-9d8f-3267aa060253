{"ast": null, "code": "'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\n\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\nexport default CanceledError;", "map": {"version": 3, "names": ["AxiosError", "utils", "CanceledError", "message", "config", "request", "call", "ERR_CANCELED", "name", "inherits", "__CANCEL__"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/cancel/CanceledError.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\n\nexport default CanceledError;\n"], "mappings": "AAAA;;AAEA,OAAOA,UAAP,MAAuB,uBAAvB;AACA,OAAOC,KAAP,MAAkB,aAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,aAAT,CAAuBC,OAAvB,EAAgCC,MAAhC,EAAwCC,OAAxC,EAAiD;EAC/C;EACAL,UAAU,CAACM,IAAX,CAAgB,IAAhB,EAAsBH,OAAO,IAAI,IAAX,GAAkB,UAAlB,GAA+BA,OAArD,EAA8DH,UAAU,CAACO,YAAzE,EAAuFH,MAAvF,EAA+FC,OAA/F;EACA,KAAKG,IAAL,GAAY,eAAZ;AACD;;AAEDP,KAAK,CAACQ,QAAN,CAAeP,aAAf,EAA8BF,UAA9B,EAA0C;EACxCU,UAAU,EAAE;AAD4B,CAA1C;AAIA,eAAeR,aAAf"}, "metadata": {}, "sourceType": "module"}