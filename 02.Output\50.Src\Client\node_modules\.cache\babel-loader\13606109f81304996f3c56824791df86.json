{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useCallback, useState } from 'react';\nimport { isFunction } from '../utils';\n\nvar useSetState = function useSetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var setMergeState = useCallback(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  }, []);\n  return [state, setMergeState];\n};\n\nexport default useSetState;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "e", "next", "done", "push", "value", "error", "useCallback", "useState", "isFunction", "useSetState", "initialState", "_a", "state", "setState", "setMergeState", "patch", "prevState", "newState"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useSetState/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useCallback, useState } from 'react';\nimport { isFunction } from '../utils';\nvar useSetState = function useSetState(initialState) {\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setMergeState = useCallback(function (patch) {\n    setState(function (prevState) {\n      var newState = isFunction(patch) ? patch(prevState) : patch;\n      return newState ? __assign(__assign({}, prevState), newState) : prevState;\n    });\n  }, []);\n  return [state, setMergeState];\n};\nexport default useSetState;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaT,CAAb,EAAgB;EAClD,IAAIU,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIV,CAAC,GAAGW,CAAC,CAACJ,IAAF,CAAOG,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACf,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACa,CAAC,GAAGd,CAAC,CAACiB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBP,CAAC,GAAGX,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCW,CAAC,CAACJ,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIgB,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,WAAT,EAAsBC,QAAtB,QAAsC,OAAtC;AACA,SAASC,UAAT,QAA2B,UAA3B;;AACA,IAAIC,WAAW,GAAG,SAASA,WAAT,CAAqBC,YAArB,EAAmC;EACnD,IAAIC,EAAE,GAAGlB,MAAM,CAACc,QAAQ,CAACG,YAAD,CAAT,EAAyB,CAAzB,CAAf;EAAA,IACEE,KAAK,GAAGD,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEE,QAAQ,GAAGF,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIG,aAAa,GAAGR,WAAW,CAAC,UAAUS,KAAV,EAAiB;IAC/CF,QAAQ,CAAC,UAAUG,SAAV,EAAqB;MAC5B,IAAIC,QAAQ,GAAGT,UAAU,CAACO,KAAD,CAAV,GAAoBA,KAAK,CAACC,SAAD,CAAzB,GAAuCD,KAAtD;MACA,OAAOE,QAAQ,GAAGtC,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKqC,SAAL,CAAT,EAA0BC,QAA1B,CAAX,GAAiDD,SAAhE;IACD,CAHO,CAAR;EAID,CAL8B,EAK5B,EAL4B,CAA/B;EAMA,OAAO,CAACJ,KAAD,EAAQE,aAAR,CAAP;AACD,CAXD;;AAYA,eAAeL,WAAf"}, "metadata": {}, "sourceType": "module"}