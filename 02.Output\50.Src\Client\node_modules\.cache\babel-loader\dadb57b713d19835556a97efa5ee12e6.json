{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import Cell from'./elements/Cell';import{getCellFace,isValidSource}from'../utils/Util.js';/**\r\n * 配備状況コンテンツ<br>\r\n * propsは、「3.4配備状況コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Deployment\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var Deployment=function Deployment(props){return/*#__PURE__*/_jsxs(\"div\",{className:\"text-6xl\",children:[/*#__PURE__*/_jsx(Title,{title:'配備状況'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]\",children:// 車両名称の配列を軸で、行を作る\nprops.car_name.map(function(item,index){return/*#__PURE__*/_jsx(DeploymentRow,_objectSpread(_objectSpread({},props),{},{index:index}),index);})})]});};var DeploymentRow=function DeploymentRow(props){var cell1=getCellFace(props.deployment?props.deployment[props.index]:null,'col-span-2');var cell2=getCellFace(props.car_name?props.car_name[props.index]:null,'col-span-4 col-start-5');var cell3=getCellFace(props.arrow?props.arrow[props.index]:null,'col-span-1 col-start-11');var cell4=getCellFace(props.move_car_name?props.move_car_name[props.index]:null,'col-span-4 col-start-14');var cell5=getCellFace(props.car_type?props.car_type[props.index]:null,'col-span-4 col-start-20');return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell2)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell3)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell4)),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell5))]});};export default Deployment;", "map": {"version": 3, "names": ["React", "Title", "Cell", "getCellFace", "isValidSource", "Deployment", "props", "car_name", "map", "item", "index", "DeploymentRow", "cell1", "deployment", "cell2", "cell3", "arrow", "cell4", "move_car_name", "cell5", "car_type"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/Deployment.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 配備状況コンテンツ<br>\r\n * propsは、「3.4配備状況コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module Deployment\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst Deployment = (props) => {\r\n  return (\r\n    <div className=\"text-6xl\">\r\n      <Title title={'配備状況'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]\">\r\n          {\r\n            // 車両名称の配列を軸で、行を作る\r\n            props.car_name.map((item, index) => {\r\n              return <DeploymentRow key={index} {...props} index={index} />;\r\n            })\r\n          }\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DeploymentRow = (props) => {\r\n  let cell1 = getCellFace(\r\n    props.deployment ? props.deployment[props.index] : null,\r\n    'col-span-2'\r\n  );\r\n  let cell2 = getCellFace(\r\n    props.car_name ? props.car_name[props.index] : null,\r\n    'col-span-4 col-start-5'\r\n  );\r\n  let cell3 = getCellFace(\r\n    props.arrow ? props.arrow[props.index] : null,\r\n    'col-span-1 col-start-11'\r\n  );\r\n  let cell4 = getCellFace(\r\n    props.move_car_name ? props.move_car_name[props.index] : null,\r\n    'col-span-4 col-start-14'\r\n  );\r\n  let cell5 = getCellFace(\r\n    props.car_type ? props.car_type[props.index] : null,\r\n    'col-span-4 col-start-20'\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <Cell {...cell1} />\r\n      <Cell {...cell2} />\r\n      <Cell {...cell3} />\r\n      <Cell {...cell4} />\r\n      <Cell {...cell5} />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Deployment;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,WAAU,CAAG,QAAbA,WAAa,CAACC,KAAD,CAAW,CAC5B,mBACE,aAAK,SAAS,CAAC,UAAf,wBACE,KAAC,KAAD,EAAO,KAAK,CAAE,MAAd,EADF,CAEGF,aAAa,CAACE,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,0FAAf,UAEI;AACAA,KAAK,CAACC,QAAN,CAAeC,GAAf,CAAmB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAClC,mBAAO,KAAC,aAAD,gCAA+BJ,KAA/B,MAAsC,KAAK,CAAEI,KAA7C,GAAoBA,KAApB,CAAP,CACD,CAFD,CAHJ,EAHJ,GADF,CAeD,CAhBD,CAkBA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACL,KAAD,CAAW,CAC/B,GAAIM,MAAK,CAAGT,WAAW,CACrBG,KAAK,CAACO,UAAN,CAAmBP,KAAK,CAACO,UAAN,CAAiBP,KAAK,CAACI,KAAvB,CAAnB,CAAmD,IAD9B,CAErB,YAFqB,CAAvB,CAIA,GAAII,MAAK,CAAGX,WAAW,CACrBG,KAAK,CAACC,QAAN,CAAiBD,KAAK,CAACC,QAAN,CAAeD,KAAK,CAACI,KAArB,CAAjB,CAA+C,IAD1B,CAErB,wBAFqB,CAAvB,CAIA,GAAIK,MAAK,CAAGZ,WAAW,CACrBG,KAAK,CAACU,KAAN,CAAcV,KAAK,CAACU,KAAN,CAAYV,KAAK,CAACI,KAAlB,CAAd,CAAyC,IADpB,CAErB,yBAFqB,CAAvB,CAIA,GAAIO,MAAK,CAAGd,WAAW,CACrBG,KAAK,CAACY,aAAN,CAAsBZ,KAAK,CAACY,aAAN,CAAoBZ,KAAK,CAACI,KAA1B,CAAtB,CAAyD,IADpC,CAErB,yBAFqB,CAAvB,CAIA,GAAIS,MAAK,CAAGhB,WAAW,CACrBG,KAAK,CAACc,QAAN,CAAiBd,KAAK,CAACc,QAAN,CAAed,KAAK,CAACI,KAArB,CAAjB,CAA+C,IAD1B,CAErB,yBAFqB,CAAvB,CAKA,mBACE,wCACE,KAAC,IAAD,kBAAUE,KAAV,EADF,cAEE,KAAC,IAAD,kBAAUE,KAAV,EAFF,cAGE,KAAC,IAAD,kBAAUC,KAAV,EAHF,cAIE,KAAC,IAAD,kBAAUE,KAAV,EAJF,cAKE,KAAC,IAAD,kBAAUE,KAAV,EALF,GADF,CASD,CA/BD,CAiCA,cAAed,WAAf"}, "metadata": {}, "sourceType": "module"}