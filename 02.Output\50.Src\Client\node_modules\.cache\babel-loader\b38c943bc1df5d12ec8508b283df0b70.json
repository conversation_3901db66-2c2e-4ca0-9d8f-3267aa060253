{"ast": null, "code": "import { useEffect } from 'react';\nvar ImgTypeMap = {\n  SVG: 'image/svg+xml',\n  ICO: 'image/x-icon',\n  GIF: 'image/gif',\n  PNG: 'image/png'\n};\n\nvar useFavicon = function useFavicon(href) {\n  useEffect(function () {\n    if (!href) return;\n    var cutUrl = href.split('.');\n    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();\n    var link = document.querySelector(\"link[rel*='icon']\") || document.createElement('link');\n    link.type = ImgTypeMap[imgSuffix];\n    link.href = href;\n    link.rel = 'shortcut icon';\n    document.getElementsByTagName('head')[0].appendChild(link);\n  }, [href]);\n};\n\nexport default useFavicon;", "map": {"version": 3, "names": ["useEffect", "ImgTypeMap", "SVG", "ICO", "GIF", "PNG", "useFavicon", "href", "cutUrl", "split", "imgSuffix", "length", "toLocaleUpperCase", "link", "document", "querySelector", "createElement", "type", "rel", "getElementsByTagName", "append<PERSON><PERSON><PERSON>"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useFavicon/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nvar ImgTypeMap = {\n  SVG: 'image/svg+xml',\n  ICO: 'image/x-icon',\n  GIF: 'image/gif',\n  PNG: 'image/png'\n};\nvar useFavicon = function useFavicon(href) {\n  useEffect(function () {\n    if (!href) return;\n    var cutUrl = href.split('.');\n    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();\n    var link = document.querySelector(\"link[rel*='icon']\") || document.createElement('link');\n    link.type = ImgTypeMap[imgSuffix];\n    link.href = href;\n    link.rel = 'shortcut icon';\n    document.getElementsByTagName('head')[0].appendChild(link);\n  }, [href]);\n};\nexport default useFavicon;"], "mappings": "AAAA,SAASA,SAAT,QAA0B,OAA1B;AACA,IAAIC,UAAU,GAAG;EACfC,GAAG,EAAE,eADU;EAEfC,GAAG,EAAE,cAFU;EAGfC,GAAG,EAAE,WAHU;EAIfC,GAAG,EAAE;AAJU,CAAjB;;AAMA,IAAIC,UAAU,GAAG,SAASA,UAAT,CAAoBC,IAApB,EAA0B;EACzCP,SAAS,CAAC,YAAY;IACpB,IAAI,CAACO,IAAL,EAAW;IACX,IAAIC,MAAM,GAAGD,IAAI,CAACE,KAAL,CAAW,GAAX,CAAb;IACA,IAAIC,SAAS,GAAGF,MAAM,CAACA,MAAM,CAACG,MAAP,GAAgB,CAAjB,CAAN,CAA0BC,iBAA1B,EAAhB;IACA,IAAIC,IAAI,GAAGC,QAAQ,CAACC,aAAT,CAAuB,mBAAvB,KAA+CD,QAAQ,CAACE,aAAT,CAAuB,MAAvB,CAA1D;IACAH,IAAI,CAACI,IAAL,GAAYhB,UAAU,CAACS,SAAD,CAAtB;IACAG,IAAI,CAACN,IAAL,GAAYA,IAAZ;IACAM,IAAI,CAACK,GAAL,GAAW,eAAX;IACAJ,QAAQ,CAACK,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyCC,WAAzC,CAAqDP,IAArD;EACD,CATQ,EASN,CAACN,IAAD,CATM,CAAT;AAUD,CAXD;;AAYA,eAAeD,UAAf"}, "metadata": {}, "sourceType": "module"}