{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\elements\\\\BlinkBlock.js\",\n    _s = $RefreshSig$();\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { getHexColor, getTextOrDisplayColor } from '../../utils/Util.js';\nimport { useTimeout } from 'ahooks';\nimport Cell from './Cell';\nimport PropTypes from 'prop-types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst propTypes = {\n  blink_setting: PropTypes.object,\n  block: PropTypes.array.isRequired\n};\n/**\r\n * 子供のコンポーネントを点滅実施する\r\n * \r\n * @module BlinkBlock\r\n * @component\r\n *\r\n * @param {*} props\r\n * @return {*} \r\n */\n\nconst BlinkBlock = props => {\n  _s();\n\n  var _props$blink_setting6, _props$blink_setting7, _props$blink_setting8;\n\n  const [blinkTimeout, setBlinkTimeout] = useState(false);\n  const [delay, setDelay] = useState(null);\n  const lastTimer = useRef(null);\n  useEffect(() => {\n    var _props$blink_setting, _props$blink_setting2, _props$blink_setting3;\n\n    setBlinkTimeout(false); //TimerをClearしてから\n\n    if (lastTimer.current) {\n      // 既に走っているタイマーを停止\n      // lastTimer.current();\n      // console.log('BlinkBlock clear timer before setDelay.');\n      console.log('BlinkBlock timer is running.');\n    }\n\n    if ((props === null || props === void 0 ? void 0 : (_props$blink_setting = props.blink_setting) === null || _props$blink_setting === void 0 ? void 0 : _props$blink_setting.lighting_status) === '3' && (props === null || props === void 0 ? void 0 : (_props$blink_setting2 = props.blink_setting) === null || _props$blink_setting2 === void 0 ? void 0 : _props$blink_setting2.blink_speed) > 0 && (props === null || props === void 0 ? void 0 : (_props$blink_setting3 = props.blink_setting) === null || _props$blink_setting3 === void 0 ? void 0 : _props$blink_setting3.blink_time) > 0) {\n      var _props$blink_setting4, _props$blink_setting5;\n\n      console.log('BlinkBlock setDelay. oldDelay: ' + delay + '. newDelay:' + (props === null || props === void 0 ? void 0 : (_props$blink_setting4 = props.blink_setting) === null || _props$blink_setting4 === void 0 ? void 0 : _props$blink_setting4.blink_time) * 1000);\n      setDelay((props === null || props === void 0 ? void 0 : (_props$blink_setting5 = props.blink_setting) === null || _props$blink_setting5 === void 0 ? void 0 : _props$blink_setting5.blink_time) * 1000);\n    }\n  }, [props === null || props === void 0 ? void 0 : props.blink_setting]); // 上記の配列中で、Delayを追加してはいけません。\n\n  lastTimer.current = useTimeout(() => {\n    setBlinkTimeout(true);\n    setDelay(null);\n    console.log('BlinkBlock useTimeout. With delay: ' + delay);\n  }, delay);\n  useEffect(() => {\n    return () => {\n      // BlinkBlock: clear timer when unmouting.\n      if (lastTimer.current) {\n        console.log('BlinkBlock clear timer when unmouting.');\n        lastTimer.current();\n      }\n    };\n  }, []);\n  let lightingStatus = props === null || props === void 0 ? void 0 : (_props$blink_setting6 = props.blink_setting) === null || _props$blink_setting6 === void 0 ? void 0 : _props$blink_setting6.lighting_status;\n\n  if (blinkTimeout) {\n    console.log('BlinkBlock blinkTimeout true. lightingStatus: ' + lightingStatus);\n    lightingStatus = '1';\n  }\n\n  if ((props === null || props === void 0 ? void 0 : (_props$blink_setting7 = props.blink_setting) === null || _props$blink_setting7 === void 0 ? void 0 : _props$blink_setting7.blink_speed) <= 0 || (props === null || props === void 0 ? void 0 : (_props$blink_setting8 = props.blink_setting) === null || _props$blink_setting8 === void 0 ? void 0 : _props$blink_setting8.blink_time) <= 0) {\n    lightingStatus = '1';\n  }\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: Array.isArray(props.block) && props.block.map((item, index) => {\n      return /*#__PURE__*/_jsxDEV(BlockRow, { ...item,\n        lightingStatus: lightingStatus,\n        blink_setting: props === null || props === void 0 ? void 0 : props.blink_setting\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n};\n\n_s(BlinkBlock, \"HnOlE85panRQYRv2ER122CiPSOs=\", false, function () {\n  return [useTimeout];\n});\n\n_c = BlinkBlock;\n\nconst animation = props => keyframes`\n  0% {\n    color: ${getTextOrDisplayColor(props)};\n    background-color: ${getHexColor(props.background_color)};\n  }\n  100% {\n    color: ${getLightingTextColor(props)};\n    background-color: ${getHexColor(props.lighting_background_color)};\n  }\n`;\n\nconst animationRule = css`\n  ${animation} ${props => props.blink_speed / 1000}s linear infinite none alternate\n`; //点滅を実施するDivを作成\n\nconst BlinkHtmlTag = styled.div`\n  animation: ${animationRule};\n  animation-play-state: running;\n  animation-fill-mode: forwards;\n  position: relative;\n`; //点滅を実施しないDivを作成\n\n_c2 = BlinkHtmlTag;\nconst StaticHtmlTag = styled.div.attrs(props => ({\n  style: {\n    color: getTextOrDisplayColor(props),\n    backgroundColor: getHexColor(props.background_color)\n  }\n}))`\n  position: relative;\n`;\n/**\r\n * 点滅対象の一行。lightingStatusに従って、点滅するかを決める\r\n * @param {*} props \r\n * @returns 一行の点滅対象\r\n */\n\n_c3 = StaticHtmlTag;\n\nconst BlockRow = props => {\n  // props.showInfoにdisplay_colorが有っても、下記設定したtext_colorが優先される\n  const divStyle = { ...props.showInfo,\n    ...props.blink_setting\n  };\n\n  if (props.lightingStatus === '1') {\n    var _props$showInfo;\n\n    divStyle.text_color = getTextOrDisplayColor(props.showInfo);\n    divStyle.background_color = getHexColor((_props$showInfo = props.showInfo) === null || _props$showInfo === void 0 ? void 0 : _props$showInfo.background_color);\n  } else if (props.lightingStatus === '2') {\n    var _props$blink_setting9;\n\n    divStyle.text_color = getLightingTextColor(props);\n    divStyle.background_color = getHexColor((_props$blink_setting9 = props.blink_setting) === null || _props$blink_setting9 === void 0 ? void 0 : _props$blink_setting9.lighting_background_color);\n  }\n\n  if (props.lightingStatus === '3') {\n    var _props$showInfo2;\n\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${props.className} overflow-hidden`,\n      children: /*#__PURE__*/_jsxDEV(BlinkHtmlTag, { ...divStyle,\n        children: /*#__PURE__*/_jsxDEV(Cell, {\n          display_text: (_props$showInfo2 = props.showInfo) === null || _props$showInfo2 === void 0 ? void 0 : _props$showInfo2.display_text,\n          className: \"truncate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 13\n    }, this);\n  } else {\n    var _props$showInfo3;\n\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${props.className} overflow-hidden`,\n      children: /*#__PURE__*/_jsxDEV(StaticHtmlTag, { ...divStyle,\n        children: /*#__PURE__*/_jsxDEV(Cell, {\n          display_text: (_props$showInfo3 = props.showInfo) === null || _props$showInfo3 === void 0 ? void 0 : _props$showInfo3.display_text,\n          className: \"truncate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this);\n  }\n}; // APIのパラメータに違いをカバーして、色の値を返す\n\n\n_c4 = BlockRow;\n\nfunction getLightingTextColor(props) {\n  const blink_setting = (props === null || props === void 0 ? void 0 : props.blink_setting) || (props === null || props === void 0 ? void 0 : props.lighting_setting); // 下記Bug対応：\n  // lighting_status を 3 (点滅…文字色/背景色 ⇔ 点灯時文字色/点灯時背景色で画面に表示) に設定した場合に lighting_text_color (点滅時の文字色) の設定値が反映されず点滅時に必ず白文字となる。\n  // (lighting_background_color (点滅時の背景色) は設定値が反映されて、指定色で点滅する。こちらは問題なし。)\n\n  if (blink_setting) {\n    if (blink_setting !== null && blink_setting !== void 0 && blink_setting.lighting_text_color) {\n      return getHexColor(blink_setting === null || blink_setting === void 0 ? void 0 : blink_setting.lighting_text_color);\n    } else if (blink_setting !== null && blink_setting !== void 0 && blink_setting.lighting_display_color) {\n      return getHexColor(blink_setting === null || blink_setting === void 0 ? void 0 : blink_setting.lighting_display_color);\n    }\n  } else {\n    if (props !== null && props !== void 0 && props.lighting_text_color) {\n      return getHexColor(props === null || props === void 0 ? void 0 : props.lighting_text_color);\n    } else if (props !== null && props !== void 0 && props.lighting_display_color) {\n      return getHexColor(props === null || props === void 0 ? void 0 : props.lighting_display_color);\n    }\n\n    return '';\n  }\n}\n\nconst space = ' ';\n/**\r\n * BaseのObjectとチェックして、色と背景色をTargetObjectにCopy <br>\r\n * TargetObjectにTextがなければ、空文字を入れる\r\n * \r\n * @param {*} targetObj \r\n * @param {*} baseObj \r\n * @returns Target Object\r\n */\n\nexport function checkBlinkInfo(targetObj, baseObj) {\n  var _targetObj, _targetObj2, _targetObj3, _targetObj4;\n\n  if (!targetObj) {\n    targetObj = {};\n  }\n\n  if (((_targetObj = targetObj) === null || _targetObj === void 0 ? void 0 : _targetObj.display_text) === space) {\n    return targetObj;\n  }\n\n  if (!((_targetObj2 = targetObj) !== null && _targetObj2 !== void 0 && _targetObj2.display_text)) {\n    targetObj.display_text = space;\n    return targetObj;\n  }\n\n  if (!((_targetObj3 = targetObj) !== null && _targetObj3 !== void 0 && _targetObj3.text_color) && baseObj) {\n    targetObj.text_color = baseObj.text_color;\n  }\n\n  if (!((_targetObj4 = targetObj) !== null && _targetObj4 !== void 0 && _targetObj4.background_color) && baseObj) {\n    targetObj.background_color = baseObj.background_color;\n  }\n\n  return targetObj;\n}\nBlinkBlock.propTypes = propTypes;\nexport default BlinkBlock;\n\nvar _c, _c2, _c3, _c4;\n\n$RefreshReg$(_c, \"BlinkBlock\");\n$RefreshReg$(_c2, \"BlinkHtmlTag\");\n$RefreshReg$(_c3, \"StaticHtmlTag\");\n$RefreshReg$(_c4, \"BlockRow\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "styled", "keyframes", "css", "getHexColor", "getTextOrDisplayColor", "useTimeout", "Cell", "PropTypes", "propTypes", "blink_setting", "object", "block", "array", "isRequired", "BlinkBlock", "props", "blinkTimeout", "setBlinkTimeout", "delay", "<PERSON><PERSON><PERSON><PERSON>", "lastTimer", "current", "console", "log", "lighting_status", "blink_speed", "blink_time", "lightingStatus", "Array", "isArray", "map", "item", "index", "animation", "background_color", "getLightingTextColor", "lighting_background_color", "animationRule", "BlinkHtmlTag", "div", "StaticHtmlTag", "attrs", "style", "color", "backgroundColor", "BlockRow", "divStyle", "showInfo", "text_color", "className", "display_text", "lighting_setting", "lighting_text_color", "lighting_display_color", "space", "checkBlinkInfo", "targetObj", "baseObj"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/BlinkBlock.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\r\nimport styled, { keyframes, css } from 'styled-components';\r\nimport { getHexColor, getTextOrDisplayColor } from '../../utils/Util.js';\r\nimport { useTimeout } from 'ahooks';\r\nimport Cell from './Cell';\r\nimport PropTypes from 'prop-types';\r\n\r\nconst propTypes = {\r\n    blink_setting: PropTypes.object,\r\n    block: PropTypes.array.isRequired,\r\n};\r\n\r\n/**\r\n * 子供のコンポーネントを点滅実施する\r\n * \r\n * @module BlinkBlock\r\n * @component\r\n *\r\n * @param {*} props\r\n * @return {*} \r\n */\r\nconst BlinkBlock = (props) => {\r\n    const [blinkTimeout, setBlinkTimeout] = useState(false);\r\n    const [delay, setDelay] = useState(null);\r\n    const lastTimer = useRef(null);\r\n\r\n    useEffect(() => {\r\n        setBlinkTimeout(false);\r\n\r\n        //TimerをClearしてから\r\n        if (lastTimer.current) {\r\n            // 既に走っているタイマーを停止\r\n            // lastTimer.current();\r\n            // console.log('BlinkBlock clear timer before setDelay.');\r\n            console.log('BlinkBlock timer is running.');\r\n        }\r\n\r\n        if (\r\n            props?.blink_setting?.lighting_status === '3' &&\r\n            props?.blink_setting?.blink_speed > 0 &&\r\n            props?.blink_setting?.blink_time > 0\r\n        ) {\r\n            console.log('BlinkBlock setDelay. oldDelay: ' + delay + '. newDelay:' + props?.blink_setting?.blink_time * 1000);\r\n            setDelay(props?.blink_setting?.blink_time * 1000);\r\n        }\r\n    }, [props?.blink_setting]);\r\n    // 上記の配列中で、Delayを追加してはいけません。\r\n\r\n    lastTimer.current = useTimeout(() => {\r\n        setBlinkTimeout(true);\r\n        setDelay(null);\r\n        console.log('BlinkBlock useTimeout. With delay: ' + delay);\r\n    }, delay);\r\n\r\n    useEffect(() => {\r\n        return () => {\r\n            // BlinkBlock: clear timer when unmouting.\r\n            if (lastTimer.current) {\r\n                console.log('BlinkBlock clear timer when unmouting.');\r\n                lastTimer.current();\r\n            }\r\n        };\r\n    }, []);\r\n\r\n    let lightingStatus = props?.blink_setting?.lighting_status;\r\n    if (blinkTimeout) {\r\n        console.log('BlinkBlock blinkTimeout true. lightingStatus: ' + lightingStatus);\r\n        lightingStatus = '1';\r\n    }\r\n    if (\r\n        props?.blink_setting?.blink_speed <= 0 ||\r\n        props?.blink_setting?.blink_time <= 0\r\n    ) {\r\n        lightingStatus = '1';\r\n    }\r\n    return (\r\n        <>\r\n            {Array.isArray(props.block) &&\r\n                props.block.map((item, index) => {\r\n                    return (\r\n                        <BlockRow\r\n                            key={index}\r\n                            {...item}\r\n                            lightingStatus={lightingStatus}\r\n                            blink_setting={props?.blink_setting}\r\n                        />\r\n                    );\r\n                })}\r\n        </>\r\n    );\r\n};\r\n\r\nconst animation = (props) => keyframes`\r\n  0% {\r\n    color: ${getTextOrDisplayColor(props)};\r\n    background-color: ${getHexColor(props.background_color)};\r\n  }\r\n  100% {\r\n    color: ${getLightingTextColor(props)};\r\n    background-color: ${getHexColor(props.lighting_background_color)};\r\n  }\r\n`;\r\n\r\nconst animationRule = css`\r\n  ${animation} ${(props) =>\r\n        props.blink_speed / 1000}s linear infinite none alternate\r\n`;\r\n\r\n//点滅を実施するDivを作成\r\nconst BlinkHtmlTag = styled.div`\r\n  animation: ${animationRule};\r\n  animation-play-state: running;\r\n  animation-fill-mode: forwards;\r\n  position: relative;\r\n`;\r\n\r\n//点滅を実施しないDivを作成\r\nconst StaticHtmlTag = styled.div.attrs((props) => ({\r\n    style: {\r\n        color: getTextOrDisplayColor(props),\r\n        backgroundColor: getHexColor(props.background_color),\r\n    },\r\n}))`\r\n  position: relative;\r\n`;\r\n\r\n/**\r\n * 点滅対象の一行。lightingStatusに従って、点滅するかを決める\r\n * @param {*} props \r\n * @returns 一行の点滅対象\r\n */\r\nconst BlockRow = (props) => {\r\n    // props.showInfoにdisplay_colorが有っても、下記設定したtext_colorが優先される\r\n    const divStyle = { ...props.showInfo, ...props.blink_setting };\r\n\r\n    if (props.lightingStatus === '1') {\r\n        divStyle.text_color = getTextOrDisplayColor(props.showInfo);\r\n        divStyle.background_color = getHexColor(props.showInfo?.background_color);\r\n    } else if (props.lightingStatus === '2') {\r\n        divStyle.text_color = getLightingTextColor(props);\r\n        divStyle.background_color = getHexColor(\r\n            props.blink_setting?.lighting_background_color\r\n        );\r\n    }\r\n\r\n    if (props.lightingStatus === '3') {\r\n        return (\r\n            <div className={`${props.className} overflow-hidden`}>\r\n                <BlinkHtmlTag {...divStyle}>\r\n                    <Cell display_text={props.showInfo?.display_text} className=\"truncate\" />\r\n                </BlinkHtmlTag>\r\n            </div>\r\n        );\r\n    } else {\r\n        return (\r\n            <div className={`${props.className} overflow-hidden`}>\r\n                <StaticHtmlTag {...divStyle}>\r\n                    <Cell display_text={props.showInfo?.display_text} className=\"truncate\" />\r\n                </StaticHtmlTag>\r\n            </div>\r\n        );\r\n    }\r\n};\r\n\r\n// APIのパラメータに違いをカバーして、色の値を返す\r\nfunction getLightingTextColor(props) {\r\n    const blink_setting = props?.blink_setting || props?.lighting_setting;\r\n\r\n    // 下記Bug対応：\r\n    // lighting_status を 3 (点滅…文字色/背景色 ⇔ 点灯時文字色/点灯時背景色で画面に表示) に設定した場合に lighting_text_color (点滅時の文字色) の設定値が反映されず点滅時に必ず白文字となる。\r\n    // (lighting_background_color (点滅時の背景色) は設定値が反映されて、指定色で点滅する。こちらは問題なし。)\r\n    if (blink_setting) {\r\n        if (blink_setting?.lighting_text_color) {\r\n            return getHexColor(blink_setting?.lighting_text_color);\r\n        } else if (blink_setting?.lighting_display_color) {\r\n            return getHexColor(blink_setting?.lighting_display_color);\r\n        }\r\n    } else {\r\n        if (props?.lighting_text_color) {\r\n            return getHexColor(props?.lighting_text_color);\r\n        } else if (props?.lighting_display_color) {\r\n            return getHexColor(props?.lighting_display_color);\r\n        }\r\n\r\n        return '';\r\n    }\r\n}\r\n\r\nconst space = ' ';\r\n\r\n/**\r\n * BaseのObjectとチェックして、色と背景色をTargetObjectにCopy <br>\r\n * TargetObjectにTextがなければ、空文字を入れる\r\n * \r\n * @param {*} targetObj \r\n * @param {*} baseObj \r\n * @returns Target Object\r\n */\r\nexport function checkBlinkInfo(targetObj, baseObj) {\r\n    if (!targetObj) {\r\n        targetObj = {};\r\n    }\r\n    if (targetObj?.display_text === space) {\r\n        return targetObj;\r\n    }\r\n\r\n    if (!targetObj?.display_text) {\r\n        targetObj.display_text = space;\r\n        return targetObj;\r\n    }\r\n\r\n    if (!targetObj?.text_color && baseObj) {\r\n        targetObj.text_color = baseObj.text_color;\r\n    }\r\n    if (!targetObj?.background_color && baseObj) {\r\n        targetObj.background_color = baseObj.background_color;\r\n    }\r\n\r\n    return targetObj;\r\n}\r\n\r\nBlinkBlock.propTypes = propTypes;\r\nexport default BlinkBlock;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,MAAhB,EAAwBC,SAAxB,EAAmCC,QAAnC,QAAmD,OAAnD;AACA,OAAOC,MAAP,IAAiBC,SAAjB,EAA4BC,GAA5B,QAAuC,mBAAvC;AACA,SAASC,WAAT,EAAsBC,qBAAtB,QAAmD,qBAAnD;AACA,SAASC,UAAT,QAA2B,QAA3B;AACA,OAAOC,IAAP,MAAiB,QAAjB;AACA,OAAOC,SAAP,MAAsB,YAAtB;;;AAEA,MAAMC,SAAS,GAAG;EACdC,aAAa,EAAEF,SAAS,CAACG,MADX;EAEdC,KAAK,EAAEJ,SAAS,CAACK,KAAV,CAAgBC;AAFT,CAAlB;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,UAAU,GAAIC,KAAD,IAAW;EAAA;;EAAA;;EAC1B,MAAM,CAACC,YAAD,EAAeC,eAAf,IAAkClB,QAAQ,CAAC,KAAD,CAAhD;EACA,MAAM,CAACmB,KAAD,EAAQC,QAAR,IAAoBpB,QAAQ,CAAC,IAAD,CAAlC;EACA,MAAMqB,SAAS,GAAGvB,MAAM,CAAC,IAAD,CAAxB;EAEAC,SAAS,CAAC,MAAM;IAAA;;IACZmB,eAAe,CAAC,KAAD,CAAf,CADY,CAGZ;;IACA,IAAIG,SAAS,CAACC,OAAd,EAAuB;MACnB;MACA;MACA;MACAC,OAAO,CAACC,GAAR,CAAY,8BAAZ;IACH;;IAED,IACI,CAAAR,KAAK,SAAL,IAAAA,KAAK,WAAL,oCAAAA,KAAK,CAAEN,aAAP,8EAAsBe,eAAtB,MAA0C,GAA1C,IACA,CAAAT,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBgB,WAAtB,IAAoC,CADpC,IAEA,CAAAV,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBiB,UAAtB,IAAmC,CAHvC,EAIE;MAAA;;MACEJ,OAAO,CAACC,GAAR,CAAY,oCAAoCL,KAApC,GAA4C,aAA5C,GAA4D,CAAAH,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBiB,UAAtB,IAAmC,IAA3G;MACAP,QAAQ,CAAC,CAAAJ,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBiB,UAAtB,IAAmC,IAApC,CAAR;IACH;EACJ,CAnBQ,EAmBN,CAACX,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEN,aAAR,CAnBM,CAAT,CAL0B,CAyB1B;;EAEAW,SAAS,CAACC,OAAV,GAAoBhB,UAAU,CAAC,MAAM;IACjCY,eAAe,CAAC,IAAD,CAAf;IACAE,QAAQ,CAAC,IAAD,CAAR;IACAG,OAAO,CAACC,GAAR,CAAY,wCAAwCL,KAApD;EACH,CAJ6B,EAI3BA,KAJ2B,CAA9B;EAMApB,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT;MACA,IAAIsB,SAAS,CAACC,OAAd,EAAuB;QACnBC,OAAO,CAACC,GAAR,CAAY,wCAAZ;QACAH,SAAS,CAACC,OAAV;MACH;IACJ,CAND;EAOH,CARQ,EAQN,EARM,CAAT;EAUA,IAAIM,cAAc,GAAGZ,KAAH,aAAGA,KAAH,gDAAGA,KAAK,CAAEN,aAAV,0DAAG,sBAAsBe,eAA3C;;EACA,IAAIR,YAAJ,EAAkB;IACdM,OAAO,CAACC,GAAR,CAAY,mDAAmDI,cAA/D;IACAA,cAAc,GAAG,GAAjB;EACH;;EACD,IACI,CAAAZ,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBgB,WAAtB,KAAqC,CAArC,IACA,CAAAV,KAAK,SAAL,IAAAA,KAAK,WAAL,qCAAAA,KAAK,CAAEN,aAAP,gFAAsBiB,UAAtB,KAAoC,CAFxC,EAGE;IACEC,cAAc,GAAG,GAAjB;EACH;;EACD,oBACI;IAAA,UACKC,KAAK,CAACC,OAAN,CAAcd,KAAK,CAACJ,KAApB,KACGI,KAAK,CAACJ,KAAN,CAAYmB,GAAZ,CAAgB,CAACC,IAAD,EAAOC,KAAP,KAAiB;MAC7B,oBACI,QAAC,QAAD,OAEQD,IAFR;QAGI,cAAc,EAAEJ,cAHpB;QAII,aAAa,EAAEZ,KAAF,aAAEA,KAAF,uBAAEA,KAAK,CAAEN;MAJ1B,GACSuB,KADT;QAAA;QAAA;QAAA;MAAA,QADJ;IAQH,CATD;EAFR,iBADJ;AAeH,CArED;;GAAMlB,U;UA2BkBT,U;;;KA3BlBS,U;;AAuEN,MAAMmB,SAAS,GAAIlB,KAAD,IAAWd,SAAU;AACvC;AACA,aAAaG,qBAAqB,CAACW,KAAD,CAAQ;AAC1C,wBAAwBZ,WAAW,CAACY,KAAK,CAACmB,gBAAP,CAAyB;AAC5D;AACA;AACA,aAAaC,oBAAoB,CAACpB,KAAD,CAAQ;AACzC,wBAAwBZ,WAAW,CAACY,KAAK,CAACqB,yBAAP,CAAkC;AACrE;AACA,CATA;;AAWA,MAAMC,aAAa,GAAGnC,GAAI;AAC1B,IAAI+B,SAAU,IAAIlB,KAAD,IACTA,KAAK,CAACU,WAAN,GAAoB,IAAK;AACjC,CAHA,C,CAKA;;AACA,MAAMa,YAAY,GAAGtC,MAAM,CAACuC,GAAI;AAChC,eAAeF,aAAc;AAC7B;AACA;AACA;AACA,CALA,C,CAOA;;MAPMC,Y;AAQN,MAAME,aAAa,GAAGxC,MAAM,CAACuC,GAAP,CAAWE,KAAX,CAAkB1B,KAAD,KAAY;EAC/C2B,KAAK,EAAE;IACHC,KAAK,EAAEvC,qBAAqB,CAACW,KAAD,CADzB;IAEH6B,eAAe,EAAEzC,WAAW,CAACY,KAAK,CAACmB,gBAAP;EAFzB;AADwC,CAAZ,CAAjB,CAKlB;AACJ;AACA,CAPA;AASA;AACA;AACA;AACA;AACA;;MAbMM,a;;AAcN,MAAMK,QAAQ,GAAI9B,KAAD,IAAW;EACxB;EACA,MAAM+B,QAAQ,GAAG,EAAE,GAAG/B,KAAK,CAACgC,QAAX;IAAqB,GAAGhC,KAAK,CAACN;EAA9B,CAAjB;;EAEA,IAAIM,KAAK,CAACY,cAAN,KAAyB,GAA7B,EAAkC;IAAA;;IAC9BmB,QAAQ,CAACE,UAAT,GAAsB5C,qBAAqB,CAACW,KAAK,CAACgC,QAAP,CAA3C;IACAD,QAAQ,CAACZ,gBAAT,GAA4B/B,WAAW,oBAACY,KAAK,CAACgC,QAAP,oDAAC,gBAAgBb,gBAAjB,CAAvC;EACH,CAHD,MAGO,IAAInB,KAAK,CAACY,cAAN,KAAyB,GAA7B,EAAkC;IAAA;;IACrCmB,QAAQ,CAACE,UAAT,GAAsBb,oBAAoB,CAACpB,KAAD,CAA1C;IACA+B,QAAQ,CAACZ,gBAAT,GAA4B/B,WAAW,0BACnCY,KAAK,CAACN,aAD6B,0DACnC,sBAAqB2B,yBADc,CAAvC;EAGH;;EAED,IAAIrB,KAAK,CAACY,cAAN,KAAyB,GAA7B,EAAkC;IAAA;;IAC9B,oBACI;MAAK,SAAS,EAAG,GAAEZ,KAAK,CAACkC,SAAU,kBAAnC;MAAA,uBACI,QAAC,YAAD,OAAkBH,QAAlB;QAAA,uBACI,QAAC,IAAD;UAAM,YAAY,sBAAE/B,KAAK,CAACgC,QAAR,qDAAE,iBAAgBG,YAApC;UAAkD,SAAS,EAAC;QAA5D;UAAA;UAAA;UAAA;QAAA;MADJ;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QADJ;EAOH,CARD,MAQO;IAAA;;IACH,oBACI;MAAK,SAAS,EAAG,GAAEnC,KAAK,CAACkC,SAAU,kBAAnC;MAAA,uBACI,QAAC,aAAD,OAAmBH,QAAnB;QAAA,uBACI,QAAC,IAAD;UAAM,YAAY,sBAAE/B,KAAK,CAACgC,QAAR,qDAAE,iBAAgBG,YAApC;UAAkD,SAAS,EAAC;QAA5D;UAAA;UAAA;UAAA;QAAA;MADJ;QAAA;QAAA;QAAA;MAAA;IADJ;MAAA;MAAA;MAAA;IAAA,QADJ;EAOH;AACJ,CA/BD,C,CAiCA;;;MAjCML,Q;;AAkCN,SAASV,oBAAT,CAA8BpB,KAA9B,EAAqC;EACjC,MAAMN,aAAa,GAAG,CAAAM,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAEN,aAAP,MAAwBM,KAAxB,aAAwBA,KAAxB,uBAAwBA,KAAK,CAAEoC,gBAA/B,CAAtB,CADiC,CAGjC;EACA;EACA;;EACA,IAAI1C,aAAJ,EAAmB;IACf,IAAIA,aAAJ,aAAIA,aAAJ,eAAIA,aAAa,CAAE2C,mBAAnB,EAAwC;MACpC,OAAOjD,WAAW,CAACM,aAAD,aAACA,aAAD,uBAACA,aAAa,CAAE2C,mBAAhB,CAAlB;IACH,CAFD,MAEO,IAAI3C,aAAJ,aAAIA,aAAJ,eAAIA,aAAa,CAAE4C,sBAAnB,EAA2C;MAC9C,OAAOlD,WAAW,CAACM,aAAD,aAACA,aAAD,uBAACA,aAAa,CAAE4C,sBAAhB,CAAlB;IACH;EACJ,CAND,MAMO;IACH,IAAItC,KAAJ,aAAIA,KAAJ,eAAIA,KAAK,CAAEqC,mBAAX,EAAgC;MAC5B,OAAOjD,WAAW,CAACY,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEqC,mBAAR,CAAlB;IACH,CAFD,MAEO,IAAIrC,KAAJ,aAAIA,KAAJ,eAAIA,KAAK,CAAEsC,sBAAX,EAAmC;MACtC,OAAOlD,WAAW,CAACY,KAAD,aAACA,KAAD,uBAACA,KAAK,CAAEsC,sBAAR,CAAlB;IACH;;IAED,OAAO,EAAP;EACH;AACJ;;AAED,MAAMC,KAAK,GAAG,GAAd;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,cAAT,CAAwBC,SAAxB,EAAmCC,OAAnC,EAA4C;EAAA;;EAC/C,IAAI,CAACD,SAAL,EAAgB;IACZA,SAAS,GAAG,EAAZ;EACH;;EACD,IAAI,eAAAA,SAAS,UAAT,gDAAWN,YAAX,MAA4BI,KAAhC,EAAuC;IACnC,OAAOE,SAAP;EACH;;EAED,IAAI,iBAACA,SAAD,wCAAC,YAAWN,YAAZ,CAAJ,EAA8B;IAC1BM,SAAS,CAACN,YAAV,GAAyBI,KAAzB;IACA,OAAOE,SAAP;EACH;;EAED,IAAI,iBAACA,SAAD,wCAAC,YAAWR,UAAZ,KAA0BS,OAA9B,EAAuC;IACnCD,SAAS,CAACR,UAAV,GAAuBS,OAAO,CAACT,UAA/B;EACH;;EACD,IAAI,iBAACQ,SAAD,wCAAC,YAAWtB,gBAAZ,KAAgCuB,OAApC,EAA6C;IACzCD,SAAS,CAACtB,gBAAV,GAA6BuB,OAAO,CAACvB,gBAArC;EACH;;EAED,OAAOsB,SAAP;AACH;AAED1C,UAAU,CAACN,SAAX,GAAuBA,SAAvB;AACA,eAAeM,UAAf"}, "metadata": {}, "sourceType": "module"}