{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Title from'./elements/Title';import TextScroll from'./elements/TextScroll';import Cell from'./elements/Cell';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource}from'../utils/Util.js';/**\r\n * 当番医コンテンツ<br>\r\n * propsは、「3.14当番医コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module DoctorOnDuty\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";var DoctorOnDuty=function DoctorOnDuty(props){var MAX_ROW=6;return/*#__PURE__*/_jsxs(\"div\",{className:\"text-8xl\",children:[/*#__PURE__*/_jsx(Title,{title:'当番病院'}),isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-[repeat(2,8rem)_minmax(0.25rem,1fr)_repeat(5,8rem)_minmax(0.25rem,1fr)_repeat(7,7rem)] leading-[1]  gap-y-[2.3rem] mt-[1rem] items-end\",children:props.medical_subject.map(function(item,index){if(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(DoctorOnDutyRow,{item:item},index);})})]});};var DoctorOnDutyRow=function DoctorOnDutyRow(props){var _rowData$medical_inst,_rowData$medical_inst2;var rowData=props===null||props===void 0?void 0:props.item;if(!rowData)return;var cell1Props=getCellFace(rowData,'col-span-2');// 結合するかをチェック\nif(((_rowData$medical_inst=rowData.medical_institution_name)===null||_rowData$medical_inst===void 0?void 0:_rowData$medical_inst.length)>1||((_rowData$medical_inst2=rowData.medical_institution_telephone_no)===null||_rowData$medical_inst2===void 0?void 0:_rowData$medical_inst2.length)>1){var _rowData$medical_inst3,_rowData$medical_inst4,_rowData$medical_inst5;var showMsg='';// まず医療期間で表示文字列を作る\n(_rowData$medical_inst3=rowData.medical_institution_name)===null||_rowData$medical_inst3===void 0?void 0:_rowData$medical_inst3.forEach(function(item,index){if(rowData.medical_institution_telephone_no[index]&&rowData.medical_institution_telephone_no[index].display_text){showMsg=\"\".concat(showMsg,\" \").concat(item.display_text,\" \").concat(rowData.medical_institution_telephone_no[index].display_text);}else{showMsg=\"\".concat(showMsg,\" \").concat(item.display_text);}});// 残る電話番号で表示文字列を作る\nif(((_rowData$medical_inst4=rowData.medical_institution_name)===null||_rowData$medical_inst4===void 0?void 0:_rowData$medical_inst4.length)<((_rowData$medical_inst5=rowData.medical_institution_telephone_no)===null||_rowData$medical_inst5===void 0?void 0:_rowData$medical_inst5.length)){for(var index=((_rowData$medical_inst6=rowData.medical_institution_name)===null||_rowData$medical_inst6===void 0?void 0:_rowData$medical_inst6.length)||0;index<((_rowData$medical_inst7=rowData.medical_institution_telephone_no)===null||_rowData$medical_inst7===void 0?void 0:_rowData$medical_inst7.length);index++){var _rowData$medical_inst6,_rowData$medical_inst7;var teleNo=rowData.medical_institution_telephone_no[index];if(teleNo.display_text){showMsg=\"\".concat(showMsg,\" \").concat(teleNo.display_text);}}}// 結合した文字列の表示Styleは、1個目の電話番号のStyleを使う。なければ、1個目の医療機関のStyleを使う\nvar cell2Props;if(rowData.medical_institution_telephone_no&&rowData.medical_institution_telephone_no[0]){cell2Props=getCellFace(rowData.medical_institution_telephone_no[0],'col-span-13 col-start-4 text-7xl');}else{cell2Props=getCellFace(rowData.medical_institution_name[0],'col-span-13 col-start-4 text-7xl');}cell2Props.text=showMsg;var cell2PropFrame={className:cell2Props.className,text_color:cell2Props.text_color};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1Props)),/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},cell2PropFrame),{},{children:/*#__PURE__*/_jsx(TextScroll,{content:cell2Props.text,background_color:cell2Props.background_color})}))]});}else{var _rowData$medical_inst8,_rowData$medical_inst9;// 医療機関と電話番号をそれぞれ表示\nvar _cell2Props={className:'col-span-5 col-start-4'};var cell3Props={className:'col-span-7 col-start-10'};if(((_rowData$medical_inst8=rowData.medical_institution_name)===null||_rowData$medical_inst8===void 0?void 0:_rowData$medical_inst8.length)===1){_cell2Props=getCellFace(rowData.medical_institution_name[0],'col-span-5 col-start-4');}if(((_rowData$medical_inst9=rowData.medical_institution_telephone_no)===null||_rowData$medical_inst9===void 0?void 0:_rowData$medical_inst9.length)===1){cell3Props=getCellFace(rowData.medical_institution_telephone_no[0],'col-span-7 col-start-10 text-7xl w-fit');}var _cell2PropFrame={className:_cell2Props.className,text_color:_cell2Props.text_color};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1Props)),/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},_cell2PropFrame),{},{children:/*#__PURE__*/_jsx(TextScroll,{content:_cell2Props.text,background_color:_cell2Props.background_color})})),/*#__PURE__*/_jsx(Cell,_objectSpread({},cell3Props))]});}};export default DoctorOnDuty;", "map": {"version": 3, "names": ["React", "Title", "TextScroll", "Cell", "CellBox", "getCellFace", "isValidSource", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "MAX_ROW", "medical_subject", "map", "item", "index", "undefined", "DoctorOnDutyRow", "rowData", "cell1Props", "medical_institution_name", "length", "medical_institution_telephone_no", "showMsg", "for<PERSON>ach", "display_text", "teleNo", "cell2Props", "text", "cell2PropFrame", "className", "text_color", "background_color", "cell3Props"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/DoctorOnDuty.js"], "sourcesContent": ["import React from 'react';\r\nimport Title from './elements/Title';\r\nimport TextScroll from './elements/TextScroll';\r\nimport Cell from './elements/Cell';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\n\r\n/**\r\n * 当番医コンテンツ<br>\r\n * propsは、「3.14当番医コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module DoctorOnDuty\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst DoctorOnDuty = (props) => {\r\n  const MAX_ROW = 6;\r\n  return (\r\n    <div className=\"text-8xl\">\r\n      <Title title={'当番病院'} />\r\n      {isValidSource(props) && (\r\n        <div className=\"border-transparent border-x-[1rem] grid grid-cols-[repeat(2,8rem)_minmax(0.25rem,1fr)_repeat(5,8rem)_minmax(0.25rem,1fr)_repeat(7,7rem)] leading-[1]  gap-y-[2.3rem] mt-[1rem] items-end\">\r\n          {props.medical_subject.map((item, index) => {\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <DoctorOnDutyRow key={index} item={item} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DoctorOnDutyRow = (props) => {\r\n  const rowData = props?.item;\r\n  if (!rowData) return;\r\n\r\n  let cell1Props = getCellFace(rowData, 'col-span-2');\r\n\r\n  // 結合するかをチェック\r\n  if (\r\n    rowData.medical_institution_name?.length > 1 ||\r\n    rowData.medical_institution_telephone_no?.length > 1\r\n  ) {\r\n    let showMsg = '';\r\n\r\n    // まず医療期間で表示文字列を作る\r\n    rowData.medical_institution_name?.forEach((item, index) => {\r\n      if (\r\n        rowData.medical_institution_telephone_no[index] &&\r\n        rowData.medical_institution_telephone_no[index].display_text\r\n      ) {\r\n        showMsg = `${showMsg} ${item.display_text} ${rowData.medical_institution_telephone_no[index].display_text}`;\r\n      } else {\r\n        showMsg = `${showMsg} ${item.display_text}`;\r\n      }\r\n    });\r\n\r\n    // 残る電話番号で表示文字列を作る\r\n    if (\r\n      rowData.medical_institution_name?.length <\r\n      rowData.medical_institution_telephone_no?.length\r\n    ) {\r\n      for (\r\n        let index = rowData.medical_institution_name?.length || 0;\r\n        index < rowData.medical_institution_telephone_no?.length;\r\n        index++\r\n      ) {\r\n        const teleNo = rowData.medical_institution_telephone_no[index];\r\n        if (teleNo.display_text) {\r\n          showMsg = `${showMsg} ${teleNo.display_text}`;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 結合した文字列の表示Styleは、1個目の電話番号のStyleを使う。なければ、1個目の医療機関のStyleを使う\r\n    let cell2Props;\r\n    if (\r\n      rowData.medical_institution_telephone_no &&\r\n      rowData.medical_institution_telephone_no[0]\r\n    ) {\r\n      cell2Props = getCellFace(\r\n        rowData.medical_institution_telephone_no[0],\r\n        'col-span-13 col-start-4 text-7xl'\r\n      );\r\n    } else {\r\n      cell2Props = getCellFace(\r\n        rowData.medical_institution_name[0],\r\n        'col-span-13 col-start-4 text-7xl'\r\n      );\r\n    }\r\n\r\n    cell2Props.text = showMsg;\r\n\r\n    let cell2PropFrame = {\r\n      className: cell2Props.className,\r\n      text_color: cell2Props.text_color,\r\n    };\r\n\r\n    return (\r\n      <>\r\n        <Cell {...cell1Props} />\r\n        <CellBox {...cell2PropFrame}>\r\n          <TextScroll\r\n            content={cell2Props.text}\r\n            background_color={cell2Props.background_color}\r\n          />\r\n        </CellBox>\r\n      </>\r\n    );\r\n  } else {\r\n    // 医療機関と電話番号をそれぞれ表示\r\n    let cell2Props = { className: 'col-span-5 col-start-4' };\r\n    let cell3Props = { className: 'col-span-7 col-start-10' };\r\n\r\n    if (rowData.medical_institution_name?.length === 1) {\r\n      cell2Props = getCellFace(\r\n        rowData.medical_institution_name[0],\r\n        'col-span-5 col-start-4'\r\n      );\r\n    }\r\n\r\n    if (rowData.medical_institution_telephone_no?.length === 1) {\r\n      cell3Props = getCellFace(\r\n        rowData.medical_institution_telephone_no[0],\r\n        'col-span-7 col-start-10 text-7xl w-fit'\r\n      );\r\n    }\r\n\r\n    let cell2PropFrame = {\r\n      className: cell2Props.className,\r\n      text_color: cell2Props.text_color,\r\n    };\r\n\r\n    return (\r\n      <>\r\n        <Cell {...cell1Props} />\r\n        <CellBox {...cell2PropFrame}>\r\n          <TextScroll\r\n            content={cell2Props.text}\r\n            background_color={cell2Props.background_color}\r\n          />\r\n        </CellBox>\r\n        <Cell {...cell3Props} />\r\n      </>\r\n    );\r\n  }\r\n};\r\n\r\nexport default DoctorOnDuty;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,MAAP,KAAkB,kBAAlB,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,aAAY,CAAG,QAAfA,aAAe,CAACC,KAAD,CAAW,CAC9B,GAAMC,QAAO,CAAG,CAAhB,CACA,mBACE,aAAK,SAAS,CAAC,UAAf,wBACE,KAAC,KAAD,EAAO,KAAK,CAAE,MAAd,EADF,CAEGH,aAAa,CAACE,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,0LAAf,UACGA,KAAK,CAACE,eAAN,CAAsBC,GAAtB,CAA0B,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC1C,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,eAAD,EAA6B,IAAI,CAAEF,IAAnC,EAAsBC,KAAtB,CAAP,CACD,CAJA,CADH,EAHJ,GADF,CAcD,CAhBD,CAkBA,GAAME,gBAAe,CAAG,QAAlBA,gBAAkB,CAACP,KAAD,CAAW,kDACjC,GAAMQ,QAAO,CAAGR,KAAH,SAAGA,KAAH,iBAAGA,KAAK,CAAEI,IAAvB,CACA,GAAI,CAACI,OAAL,CAAc,OAEd,GAAIC,WAAU,CAAGZ,WAAW,CAACW,OAAD,CAAU,YAAV,CAA5B,CAEA;AACA,GACE,wBAAAA,OAAO,CAACE,wBAAR,sEAAkCC,MAAlC,EAA2C,CAA3C,EACA,yBAAAH,OAAO,CAACI,gCAAR,wEAA0CD,MAA1C,EAAmD,CAFrD,CAGE,0EACA,GAAIE,QAAO,CAAG,EAAd,CAEA;AACA,wBAAAL,OAAO,CAACE,wBAAR,wEAAkCI,OAAlC,CAA0C,SAACV,IAAD,CAAOC,KAAP,CAAiB,CACzD,GACEG,OAAO,CAACI,gCAAR,CAAyCP,KAAzC,GACAG,OAAO,CAACI,gCAAR,CAAyCP,KAAzC,EAAgDU,YAFlD,CAGE,CACAF,OAAO,WAAMA,OAAN,aAAiBT,IAAI,CAACW,YAAtB,aAAsCP,OAAO,CAACI,gCAAR,CAAyCP,KAAzC,EAAgDU,YAAtF,CAAP,CACD,CALD,IAKO,CACLF,OAAO,WAAMA,OAAN,aAAiBT,IAAI,CAACW,YAAtB,CAAP,CACD,CACF,CATD,EAWA;AACA,GACE,yBAAAP,OAAO,CAACE,wBAAR,wEAAkCC,MAAlC,2BACAH,OAAO,CAACI,gCADR,iDACA,uBAA0CD,MAD1C,CADF,CAGE,CACA,IACE,GAAIN,MAAK,CAAG,yBAAAG,OAAO,CAACE,wBAAR,wEAAkCC,MAAlC,GAA4C,CAD1D,CAEEN,KAAK,0BAAGG,OAAO,CAACI,gCAAX,iDAAG,uBAA0CD,MAA7C,CAFP,CAGEN,KAAK,EAHP,CAIE,mDACA,GAAMW,OAAM,CAAGR,OAAO,CAACI,gCAAR,CAAyCP,KAAzC,CAAf,CACA,GAAIW,MAAM,CAACD,YAAX,CAAyB,CACvBF,OAAO,WAAMA,OAAN,aAAiBG,MAAM,CAACD,YAAxB,CAAP,CACD,CACF,CACF,CAED;AACA,GAAIE,WAAJ,CACA,GACET,OAAO,CAACI,gCAAR,EACAJ,OAAO,CAACI,gCAAR,CAAyC,CAAzC,CAFF,CAGE,CACAK,UAAU,CAAGpB,WAAW,CACtBW,OAAO,CAACI,gCAAR,CAAyC,CAAzC,CADsB,CAEtB,kCAFsB,CAAxB,CAID,CARD,IAQO,CACLK,UAAU,CAAGpB,WAAW,CACtBW,OAAO,CAACE,wBAAR,CAAiC,CAAjC,CADsB,CAEtB,kCAFsB,CAAxB,CAID,CAEDO,UAAU,CAACC,IAAX,CAAkBL,OAAlB,CAEA,GAAIM,eAAc,CAAG,CACnBC,SAAS,CAAEH,UAAU,CAACG,SADH,CAEnBC,UAAU,CAAEJ,UAAU,CAACI,UAFJ,CAArB,CAKA,mBACE,wCACE,KAAC,IAAD,kBAAUZ,UAAV,EADF,cAEE,KAAC,OAAD,gCAAaU,cAAb,4BACE,KAAC,UAAD,EACE,OAAO,CAAEF,UAAU,CAACC,IADtB,CAEE,gBAAgB,CAAED,UAAU,CAACK,gBAF/B,EADF,GAFF,GADF,CAWD,CAtED,IAsEO,mDACL;AACA,GAAIL,YAAU,CAAG,CAAEG,SAAS,CAAE,wBAAb,CAAjB,CACA,GAAIG,WAAU,CAAG,CAAEH,SAAS,CAAE,yBAAb,CAAjB,CAEA,GAAI,yBAAAZ,OAAO,CAACE,wBAAR,wEAAkCC,MAAlC,IAA6C,CAAjD,CAAoD,CAClDM,WAAU,CAAGpB,WAAW,CACtBW,OAAO,CAACE,wBAAR,CAAiC,CAAjC,CADsB,CAEtB,wBAFsB,CAAxB,CAID,CAED,GAAI,yBAAAF,OAAO,CAACI,gCAAR,wEAA0CD,MAA1C,IAAqD,CAAzD,CAA4D,CAC1DY,UAAU,CAAG1B,WAAW,CACtBW,OAAO,CAACI,gCAAR,CAAyC,CAAzC,CADsB,CAEtB,wCAFsB,CAAxB,CAID,CAED,GAAIO,gBAAc,CAAG,CACnBC,SAAS,CAAEH,WAAU,CAACG,SADH,CAEnBC,UAAU,CAAEJ,WAAU,CAACI,UAFJ,CAArB,CAKA,mBACE,wCACE,KAAC,IAAD,kBAAUZ,UAAV,EADF,cAEE,KAAC,OAAD,gCAAaU,eAAb,4BACE,KAAC,UAAD,EACE,OAAO,CAAEF,WAAU,CAACC,IADtB,CAEE,gBAAgB,CAAED,WAAU,CAACK,gBAF/B,EADF,GAFF,cAQE,KAAC,IAAD,kBAAUC,UAAV,EARF,GADF,CAYD,CACF,CAlHD,CAoHA,cAAexB,aAAf"}, "metadata": {}, "sourceType": "module"}