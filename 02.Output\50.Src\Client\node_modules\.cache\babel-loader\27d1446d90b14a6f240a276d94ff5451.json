{"ast": null, "code": "'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\n\nif (Driver) {\n  module.exports = function WebSocketBrowserDriver(url) {\n    return new Driver(url);\n  };\n} else {\n  module.exports = undefined;\n}", "map": {"version": 3, "names": ["Driver", "global", "WebSocket", "MozWebSocket", "module", "exports", "WebSocketBrowserDriver", "url", "undefined"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/browser/websocket.js"], "sourcesContent": ["'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\nif (Driver) {\n\tmodule.exports = function WebSocketBrowserDriver(url) {\n\t\treturn new Driver(url);\n\t};\n} else {\n\tmodule.exports = undefined;\n}\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM,GAAGC,MAAM,CAACC,SAAP,IAAoBD,MAAM,CAACE,YAAxC;;AACA,IAAIH,MAAJ,EAAY;EACXI,MAAM,CAACC,OAAP,GAAiB,SAASC,sBAAT,CAAgCC,GAAhC,EAAqC;IACrD,OAAO,IAAIP,MAAJ,CAAWO,GAAX,CAAP;EACA,CAFD;AAGA,CAJD,MAIO;EACNH,MAAM,CAACC,OAAP,GAAiBG,SAAjB;AACA"}, "metadata": {}, "sourceType": "script"}