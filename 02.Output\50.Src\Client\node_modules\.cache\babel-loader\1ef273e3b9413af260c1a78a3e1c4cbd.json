{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useEffect, useMemo, useState, useRef } from 'react';\nimport useEventListener from '../useEventListener';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useSize from '../useSize';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isNumber } from '../utils';\n\nvar useVirtualList = function useVirtualList(list, options) {\n  var containerTarget = options.containerTarget,\n      wrapperTarget = options.wrapperTarget,\n      itemHeight = options.itemHeight,\n      _a = options.overscan,\n      overscan = _a === void 0 ? 5 : _a;\n  var itemHeightRef = useLatest(itemHeight);\n  var size = useSize(containerTarget);\n  var scrollTriggerByScrollToFunc = useRef(false);\n\n  var _b = __read(useState([]), 2),\n      targetList = _b[0],\n      setTargetList = _b[1];\n\n  var getVisibleCount = function getVisibleCount(containerHeight, fromIndex) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.ceil(containerHeight / itemHeightRef.current);\n    }\n\n    var sum = 0;\n    var endIndex = 0;\n\n    for (var i = fromIndex; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      endIndex = i;\n\n      if (sum >= containerHeight) {\n        break;\n      }\n    }\n\n    return endIndex - fromIndex;\n  };\n\n  var getOffset = function getOffset(scrollTop) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.floor(scrollTop / itemHeightRef.current) + 1;\n    }\n\n    var sum = 0;\n    var offset = 0;\n\n    for (var i = 0; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n\n    return offset + 1;\n  }; // 获取上部高度\n\n\n  var getDistanceTop = function getDistanceTop(index) {\n    if (isNumber(itemHeightRef.current)) {\n      var height_1 = index * itemHeightRef.current;\n      return height_1;\n    }\n\n    var height = list.slice(0, index).reduce(function (sum, _, i) {\n      return sum + itemHeightRef.current(i, list[i]);\n    }, 0);\n    return height;\n  };\n\n  var totalHeight = useMemo(function () {\n    if (isNumber(itemHeightRef.current)) {\n      return list.length * itemHeightRef.current;\n    }\n\n    return list.reduce(function (sum, _, index) {\n      return sum + itemHeightRef.current(index, list[index]);\n    }, 0);\n  }, [list]);\n\n  var calculateRange = function calculateRange() {\n    var container = getTargetElement(containerTarget);\n    var wrapper = getTargetElement(wrapperTarget);\n\n    if (container && wrapper) {\n      var scrollTop = container.scrollTop,\n          clientHeight = container.clientHeight;\n      var offset = getOffset(scrollTop);\n      var visibleCount = getVisibleCount(clientHeight, offset);\n      var start_1 = Math.max(0, offset - overscan);\n      var end = Math.min(list.length, offset + visibleCount + overscan);\n      var offsetTop = getDistanceTop(start_1);\n      wrapper.style.height = totalHeight - offsetTop + 'px';\n      wrapper.style.marginTop = offsetTop + 'px';\n      setTargetList(list.slice(start_1, end).map(function (ele, index) {\n        return {\n          data: ele,\n          index: index + start_1\n        };\n      }));\n    }\n  };\n\n  useEffect(function () {\n    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {\n      return;\n    }\n\n    calculateRange();\n  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);\n  useEventListener('scroll', function (e) {\n    if (scrollTriggerByScrollToFunc.current) {\n      scrollTriggerByScrollToFunc.current = false;\n      return;\n    }\n\n    e.preventDefault();\n    calculateRange();\n  }, {\n    target: containerTarget\n  });\n\n  var scrollTo = function scrollTo(index) {\n    var container = getTargetElement(containerTarget);\n\n    if (container) {\n      scrollTriggerByScrollToFunc.current = true;\n      container.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n\n  return [targetList, useMemoizedFn(scrollTo)];\n};\n\nexport default useVirtualList;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useEffect", "useMemo", "useState", "useRef", "useEventListener", "useLatest", "useMemoizedFn", "useSize", "getTargetElement", "isNumber", "useVirtualList", "list", "options", "containerTarget", "wrapperTarget", "itemHeight", "_a", "overscan", "itemHeightRef", "size", "scrollTriggerByScrollToFunc", "_b", "targetList", "setTargetList", "getVisibleCount", "containerHeight", "fromIndex", "current", "Math", "ceil", "sum", "endIndex", "length", "height", "getOffset", "scrollTop", "floor", "offset", "getDistanceTop", "index", "height_1", "slice", "reduce", "_", "totalHeight", "calculateRange", "container", "wrapper", "clientHeight", "visibleCount", "start_1", "max", "end", "min", "offsetTop", "style", "marginTop", "map", "ele", "data", "width", "preventDefault", "target", "scrollTo"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useVirtualList/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useEffect, useMemo, useState, useRef } from 'react';\nimport useEventListener from '../useEventListener';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useSize from '../useSize';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isNumber } from '../utils';\nvar useVirtualList = function useVirtualList(list, options) {\n  var containerTarget = options.containerTarget,\n    wrapperTarget = options.wrapperTarget,\n    itemHeight = options.itemHeight,\n    _a = options.overscan,\n    overscan = _a === void 0 ? 5 : _a;\n  var itemHeightRef = useLatest(itemHeight);\n  var size = useSize(containerTarget);\n  var scrollTriggerByScrollToFunc = useRef(false);\n  var _b = __read(useState([]), 2),\n    targetList = _b[0],\n    setTargetList = _b[1];\n  var getVisibleCount = function getVisibleCount(containerHeight, fromIndex) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.ceil(containerHeight / itemHeightRef.current);\n    }\n    var sum = 0;\n    var endIndex = 0;\n    for (var i = fromIndex; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      endIndex = i;\n      if (sum >= containerHeight) {\n        break;\n      }\n    }\n    return endIndex - fromIndex;\n  };\n  var getOffset = function getOffset(scrollTop) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.floor(scrollTop / itemHeightRef.current) + 1;\n    }\n    var sum = 0;\n    var offset = 0;\n    for (var i = 0; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n  // 获取上部高度\n  var getDistanceTop = function getDistanceTop(index) {\n    if (isNumber(itemHeightRef.current)) {\n      var height_1 = index * itemHeightRef.current;\n      return height_1;\n    }\n    var height = list.slice(0, index).reduce(function (sum, _, i) {\n      return sum + itemHeightRef.current(i, list[i]);\n    }, 0);\n    return height;\n  };\n  var totalHeight = useMemo(function () {\n    if (isNumber(itemHeightRef.current)) {\n      return list.length * itemHeightRef.current;\n    }\n    return list.reduce(function (sum, _, index) {\n      return sum + itemHeightRef.current(index, list[index]);\n    }, 0);\n  }, [list]);\n  var calculateRange = function calculateRange() {\n    var container = getTargetElement(containerTarget);\n    var wrapper = getTargetElement(wrapperTarget);\n    if (container && wrapper) {\n      var scrollTop = container.scrollTop,\n        clientHeight = container.clientHeight;\n      var offset = getOffset(scrollTop);\n      var visibleCount = getVisibleCount(clientHeight, offset);\n      var start_1 = Math.max(0, offset - overscan);\n      var end = Math.min(list.length, offset + visibleCount + overscan);\n      var offsetTop = getDistanceTop(start_1);\n      wrapper.style.height = totalHeight - offsetTop + 'px';\n      wrapper.style.marginTop = offsetTop + 'px';\n      setTargetList(list.slice(start_1, end).map(function (ele, index) {\n        return {\n          data: ele,\n          index: index + start_1\n        };\n      }));\n    }\n  };\n  useEffect(function () {\n    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {\n      return;\n    }\n    calculateRange();\n  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);\n  useEventListener('scroll', function (e) {\n    if (scrollTriggerByScrollToFunc.current) {\n      scrollTriggerByScrollToFunc.current = false;\n      return;\n    }\n    e.preventDefault();\n    calculateRange();\n  }, {\n    target: containerTarget\n  });\n  var scrollTo = function scrollTo(index) {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      scrollTriggerByScrollToFunc.current = true;\n      container.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n  return [targetList, useMemoizedFn(scrollTo)];\n};\nexport default useVirtualList;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,SAAT,EAAoBC,OAApB,EAA6BC,QAA7B,EAAuCC,MAAvC,QAAqD,OAArD;AACA,OAAOC,gBAAP,MAA6B,qBAA7B;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,OAAP,MAAoB,YAApB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,IAAxB,EAA8BC,OAA9B,EAAuC;EAC1D,IAAIC,eAAe,GAAGD,OAAO,CAACC,eAA9B;EAAA,IACEC,aAAa,GAAGF,OAAO,CAACE,aAD1B;EAAA,IAEEC,UAAU,GAAGH,OAAO,CAACG,UAFvB;EAAA,IAGEC,EAAE,GAAGJ,OAAO,CAACK,QAHf;EAAA,IAIEA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAhB,GAAoBA,EAJjC;EAKA,IAAIE,aAAa,GAAGb,SAAS,CAACU,UAAD,CAA7B;EACA,IAAII,IAAI,GAAGZ,OAAO,CAACM,eAAD,CAAlB;EACA,IAAIO,2BAA2B,GAAGjB,MAAM,CAAC,KAAD,CAAxC;;EACA,IAAIkB,EAAE,GAAGrC,MAAM,CAACkB,QAAQ,CAAC,EAAD,CAAT,EAAe,CAAf,CAAf;EAAA,IACEoB,UAAU,GAAGD,EAAE,CAAC,CAAD,CADjB;EAAA,IAEEE,aAAa,GAAGF,EAAE,CAAC,CAAD,CAFpB;;EAGA,IAAIG,eAAe,GAAG,SAASA,eAAT,CAAyBC,eAAzB,EAA0CC,SAA1C,EAAqD;IACzE,IAAIjB,QAAQ,CAACS,aAAa,CAACS,OAAf,CAAZ,EAAqC;MACnC,OAAOC,IAAI,CAACC,IAAL,CAAUJ,eAAe,GAAGP,aAAa,CAACS,OAA1C,CAAP;IACD;;IACD,IAAIG,GAAG,GAAG,CAAV;IACA,IAAIC,QAAQ,GAAG,CAAf;;IACA,KAAK,IAAIzC,CAAC,GAAGoC,SAAb,EAAwBpC,CAAC,GAAGqB,IAAI,CAACqB,MAAjC,EAAyC1C,CAAC,EAA1C,EAA8C;MAC5C,IAAI2C,MAAM,GAAGf,aAAa,CAACS,OAAd,CAAsBrC,CAAtB,EAAyBqB,IAAI,CAACrB,CAAD,CAA7B,CAAb;MACAwC,GAAG,IAAIG,MAAP;MACAF,QAAQ,GAAGzC,CAAX;;MACA,IAAIwC,GAAG,IAAIL,eAAX,EAA4B;QAC1B;MACD;IACF;;IACD,OAAOM,QAAQ,GAAGL,SAAlB;EACD,CAfD;;EAgBA,IAAIQ,SAAS,GAAG,SAASA,SAAT,CAAmBC,SAAnB,EAA8B;IAC5C,IAAI1B,QAAQ,CAACS,aAAa,CAACS,OAAf,CAAZ,EAAqC;MACnC,OAAOC,IAAI,CAACQ,KAAL,CAAWD,SAAS,GAAGjB,aAAa,CAACS,OAArC,IAAgD,CAAvD;IACD;;IACD,IAAIG,GAAG,GAAG,CAAV;IACA,IAAIO,MAAM,GAAG,CAAb;;IACA,KAAK,IAAI/C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqB,IAAI,CAACqB,MAAzB,EAAiC1C,CAAC,EAAlC,EAAsC;MACpC,IAAI2C,MAAM,GAAGf,aAAa,CAACS,OAAd,CAAsBrC,CAAtB,EAAyBqB,IAAI,CAACrB,CAAD,CAA7B,CAAb;MACAwC,GAAG,IAAIG,MAAP;;MACA,IAAIH,GAAG,IAAIK,SAAX,EAAsB;QACpBE,MAAM,GAAG/C,CAAT;QACA;MACD;IACF;;IACD,OAAO+C,MAAM,GAAG,CAAhB;EACD,CAfD,CA5B0D,CA4C1D;;;EACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,KAAxB,EAA+B;IAClD,IAAI9B,QAAQ,CAACS,aAAa,CAACS,OAAf,CAAZ,EAAqC;MACnC,IAAIa,QAAQ,GAAGD,KAAK,GAAGrB,aAAa,CAACS,OAArC;MACA,OAAOa,QAAP;IACD;;IACD,IAAIP,MAAM,GAAGtB,IAAI,CAAC8B,KAAL,CAAW,CAAX,EAAcF,KAAd,EAAqBG,MAArB,CAA4B,UAAUZ,GAAV,EAAea,CAAf,EAAkBrD,CAAlB,EAAqB;MAC5D,OAAOwC,GAAG,GAAGZ,aAAa,CAACS,OAAd,CAAsBrC,CAAtB,EAAyBqB,IAAI,CAACrB,CAAD,CAA7B,CAAb;IACD,CAFY,EAEV,CAFU,CAAb;IAGA,OAAO2C,MAAP;EACD,CATD;;EAUA,IAAIW,WAAW,GAAG3C,OAAO,CAAC,YAAY;IACpC,IAAIQ,QAAQ,CAACS,aAAa,CAACS,OAAf,CAAZ,EAAqC;MACnC,OAAOhB,IAAI,CAACqB,MAAL,GAAcd,aAAa,CAACS,OAAnC;IACD;;IACD,OAAOhB,IAAI,CAAC+B,MAAL,CAAY,UAAUZ,GAAV,EAAea,CAAf,EAAkBJ,KAAlB,EAAyB;MAC1C,OAAOT,GAAG,GAAGZ,aAAa,CAACS,OAAd,CAAsBY,KAAtB,EAA6B5B,IAAI,CAAC4B,KAAD,CAAjC,CAAb;IACD,CAFM,EAEJ,CAFI,CAAP;EAGD,CAPwB,EAOtB,CAAC5B,IAAD,CAPsB,CAAzB;;EAQA,IAAIkC,cAAc,GAAG,SAASA,cAAT,GAA0B;IAC7C,IAAIC,SAAS,GAAGtC,gBAAgB,CAACK,eAAD,CAAhC;IACA,IAAIkC,OAAO,GAAGvC,gBAAgB,CAACM,aAAD,CAA9B;;IACA,IAAIgC,SAAS,IAAIC,OAAjB,EAA0B;MACxB,IAAIZ,SAAS,GAAGW,SAAS,CAACX,SAA1B;MAAA,IACEa,YAAY,GAAGF,SAAS,CAACE,YAD3B;MAEA,IAAIX,MAAM,GAAGH,SAAS,CAACC,SAAD,CAAtB;MACA,IAAIc,YAAY,GAAGzB,eAAe,CAACwB,YAAD,EAAeX,MAAf,CAAlC;MACA,IAAIa,OAAO,GAAGtB,IAAI,CAACuB,GAAL,CAAS,CAAT,EAAYd,MAAM,GAAGpB,QAArB,CAAd;MACA,IAAImC,GAAG,GAAGxB,IAAI,CAACyB,GAAL,CAAS1C,IAAI,CAACqB,MAAd,EAAsBK,MAAM,GAAGY,YAAT,GAAwBhC,QAA9C,CAAV;MACA,IAAIqC,SAAS,GAAGhB,cAAc,CAACY,OAAD,CAA9B;MACAH,OAAO,CAACQ,KAAR,CAActB,MAAd,GAAuBW,WAAW,GAAGU,SAAd,GAA0B,IAAjD;MACAP,OAAO,CAACQ,KAAR,CAAcC,SAAd,GAA0BF,SAAS,GAAG,IAAtC;MACA/B,aAAa,CAACZ,IAAI,CAAC8B,KAAL,CAAWS,OAAX,EAAoBE,GAApB,EAAyBK,GAAzB,CAA6B,UAAUC,GAAV,EAAenB,KAAf,EAAsB;QAC/D,OAAO;UACLoB,IAAI,EAAED,GADD;UAELnB,KAAK,EAAEA,KAAK,GAAGW;QAFV,CAAP;MAID,CALa,CAAD,CAAb;IAMD;EACF,CApBD;;EAqBAlD,SAAS,CAAC,YAAY;IACpB,IAAI,EAAEmB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACyC,KAAnD,KAA6D,EAAEzC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACc,MAAnD,CAAjE,EAA6H;MAC3H;IACD;;IACDY,cAAc;EACf,CALQ,EAKN,CAAC1B,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACyC,KAAlD,EAAyDzC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACc,MAA1G,EAAkHtB,IAAlH,CALM,CAAT;EAMAP,gBAAgB,CAAC,QAAD,EAAW,UAAUV,CAAV,EAAa;IACtC,IAAI0B,2BAA2B,CAACO,OAAhC,EAAyC;MACvCP,2BAA2B,CAACO,OAA5B,GAAsC,KAAtC;MACA;IACD;;IACDjC,CAAC,CAACmE,cAAF;IACAhB,cAAc;EACf,CAPe,EAOb;IACDiB,MAAM,EAAEjD;EADP,CAPa,CAAhB;;EAUA,IAAIkD,QAAQ,GAAG,SAASA,QAAT,CAAkBxB,KAAlB,EAAyB;IACtC,IAAIO,SAAS,GAAGtC,gBAAgB,CAACK,eAAD,CAAhC;;IACA,IAAIiC,SAAJ,EAAe;MACb1B,2BAA2B,CAACO,OAA5B,GAAsC,IAAtC;MACAmB,SAAS,CAACX,SAAV,GAAsBG,cAAc,CAACC,KAAD,CAApC;MACAM,cAAc;IACf;EACF,CAPD;;EAQA,OAAO,CAACvB,UAAD,EAAahB,aAAa,CAACyD,QAAD,CAA1B,CAAP;AACD,CA7GD;;AA8GA,eAAerD,cAAf"}, "metadata": {}, "sourceType": "module"}