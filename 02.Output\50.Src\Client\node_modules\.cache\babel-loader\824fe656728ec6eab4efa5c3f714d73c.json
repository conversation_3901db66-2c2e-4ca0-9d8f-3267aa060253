{"ast": null, "code": "var overArg = require('./_overArg');\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\n\nvar nativeKeys = overArg(Object.keys, Object);\nmodule.exports = nativeKeys;", "map": {"version": 3, "names": ["overArg", "require", "nativeKeys", "Object", "keys", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_nativeKeys.js"], "sourcesContent": ["var overArg = require('./_overArg');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\n\nmodule.exports = nativeKeys;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAD,CAArB;AAEA;;;AACA,IAAIC,UAAU,GAAGF,OAAO,CAACG,MAAM,CAACC,IAAR,EAAcD,MAAd,CAAxB;AAEAE,MAAM,CAACC,OAAP,GAAiBJ,UAAjB"}, "metadata": {}, "sourceType": "script"}