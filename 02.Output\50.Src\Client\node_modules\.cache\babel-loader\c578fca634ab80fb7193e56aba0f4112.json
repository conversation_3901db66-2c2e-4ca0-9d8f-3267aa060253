{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import Cell from'./elements/Cell';import{getCellFace,isValidSource}from'../utils/Util.js';import BlinkBlock from'./elements/BlinkBlock';/**\r\n * 着信状況コンテンツB<br>\r\n * propsは、「3.9着信状況コンテンツB情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module IncomingCallB\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var IncomingCallB=function IncomingCallB(props){var MAX_ROW=32;return/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl leading-[1]\",children:isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 grid-rows-16 grid-flow-col gap-[0.1rem] gap-x-[1rem] auto-cols-fr mt-5\",children:props.line_name.map(function(item,index){//最大32個まで表示する\nif(index>=MAX_ROW)return undefined;return/*#__PURE__*/_jsx(IncomingCallBRow,_objectSpread({},item),index);})})});};var IncomingCallBRow=function IncomingCallBRow(props){var cell1Props=getCellFace(props,'col-span-4 w-fit');var spaceArray;var callTag='■';var MAX_CALL=6;if(!props.incoming_call){spaceArray=getSpaceArray(MAX_CALL);}else if(props.incoming_call.length<MAX_CALL){spaceArray=getSpaceArray(MAX_CALL-props.incoming_call.length);}return/*#__PURE__*/_jsxs(\"div\",{className:\"border-transparent border-x-[1rem] grid grid-cols-14 grid-rows-1 auto-cols-fr\",children:[/*#__PURE__*/_jsx(Cell,_objectSpread({},cell1Props)),/*#__PURE__*/_jsx(\"div\",{className:\"col-start-9 col-span-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row\",children:[props.incoming_call&&props.incoming_call.map(function(item,index){//最大6個まで表示する\nif(index>=MAX_CALL){return undefined;}var blinkItem=_objectSpread({},item);blinkItem.display_text=callTag;var showBlock=[{showInfo:blinkItem}];return/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,index:index,blink_setting:item.blink_setting},index);}),spaceArray&&spaceArray.map(function(item,index){return/*#__PURE__*/_jsx(\"div\",{className:\"text-[#404040]\",children:item},index);})]})})]});};function getSpaceArray(count){var callTag='■';var resultArray=[];for(var i=0;i<count;i++){resultArray.push(callTag);}return resultArray;}export default IncomingCallB;", "map": {"version": 3, "names": ["React", "Cell", "getCellFace", "isValidSource", "BlinkBlock", "IncomingCallB", "props", "MAX_ROW", "line_name", "map", "item", "index", "undefined", "IncomingCallBRow", "cell1Props", "spaceArray", "callTag", "MAX_CALL", "incoming_call", "getSpaceArray", "length", "blinkItem", "display_text", "showBlock", "showInfo", "blink_setting", "count", "resultArray", "i", "push"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/IncomingCallB.js"], "sourcesContent": ["import React from 'react';\r\nimport Cell from './elements/Cell';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport BlinkBlock from './elements/BlinkBlock';\r\n\r\n/**\r\n * 着信状況コンテンツB<br>\r\n * propsは、「3.9着信状況コンテンツB情報更新」のsource_data部分のAPI仕様に従う\r\n *\r\n * @module IncomingCallB\r\n * @component\r\n * @param {*} props\r\n * @returns 表示内容\r\n */\r\nconst IncomingCallB = (props) => {\r\n  const MAX_ROW = 32;\r\n  return (\r\n    <div className=\"text-4xl leading-[1]\">\r\n      {isValidSource(props) && (\r\n        <div className=\"grid grid-cols-2 grid-rows-16 grid-flow-col gap-[0.1rem] gap-x-[1rem] auto-cols-fr mt-5\">\r\n          {props.line_name.map((item, index) => {\r\n            //最大32個まで表示する\r\n            if (index >= MAX_ROW) return undefined;\r\n\r\n            return <IncomingCallBRow key={index} {...item} />;\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst IncomingCallBRow = (props) => {\r\n  let cell1Props = getCellFace(props, 'col-span-4 w-fit');\r\n  let spaceArray;\r\n  const callTag = '■';\r\n  const MAX_CALL = 6;\r\n\r\n  if (!props.incoming_call) {\r\n    spaceArray = getSpaceArray(MAX_CALL);\r\n  } else if (props.incoming_call.length < MAX_CALL) {\r\n    spaceArray = getSpaceArray(MAX_CALL - props.incoming_call.length);\r\n  }\r\n\r\n  return (\r\n    <div className=\"border-transparent border-x-[1rem] grid grid-cols-14 grid-rows-1 auto-cols-fr\">\r\n      <Cell {...cell1Props} />\r\n      <div className=\"col-start-9 col-span-6\">\r\n        <div className=\"flex flex-row\">\r\n          {props.incoming_call &&\r\n            props.incoming_call.map((item, index) => {\r\n              //最大6個まで表示する\r\n              if (index >= MAX_CALL) {\r\n                return undefined;\r\n              }\r\n\r\n              let blinkItem = { ...item };\r\n              blinkItem.display_text = callTag;\r\n\r\n              const showBlock = [{ showInfo: blinkItem }];\r\n\r\n              return (\r\n                <BlinkBlock\r\n                  key={index}\r\n                  block={showBlock}\r\n                  index={index}\r\n                  blink_setting={item.blink_setting}\r\n                />\r\n              );\r\n            })}\r\n          {spaceArray &&\r\n            spaceArray.map((item, index) => {\r\n              return (\r\n                <div key={index} className=\"text-[#404040]\">\r\n                  {item}\r\n                </div>\r\n              );\r\n            })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction getSpaceArray(count) {\r\n  const callTag = '■';\r\n\r\n  let resultArray = [];\r\n  for (let i = 0; i < count; i++) {\r\n    resultArray.push(callTag);\r\n  }\r\n  return resultArray;\r\n}\r\n\r\nexport default IncomingCallB;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,KAAP,KAAiB,iBAAjB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,WAAP,KAAuB,uBAAvB,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,wFACA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACC,KAAD,CAAW,CAC/B,GAAMC,QAAO,CAAG,EAAhB,CACA,mBACE,YAAK,SAAS,CAAC,sBAAf,UACGJ,aAAa,CAACG,KAAD,CAAb,eACC,YAAK,SAAS,CAAC,yFAAf,UACGA,KAAK,CAACE,SAAN,CAAgBC,GAAhB,CAAoB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACpC;AACA,GAAIA,KAAK,EAAIJ,OAAb,CAAsB,MAAOK,UAAP,CAEtB,mBAAO,KAAC,gBAAD,kBAAkCF,IAAlC,EAAuBC,KAAvB,CAAP,CACD,CALA,CADH,EAFJ,EADF,CAcD,CAhBD,CAkBA,GAAME,iBAAgB,CAAG,QAAnBA,iBAAmB,CAACP,KAAD,CAAW,CAClC,GAAIQ,WAAU,CAAGZ,WAAW,CAACI,KAAD,CAAQ,kBAAR,CAA5B,CACA,GAAIS,WAAJ,CACA,GAAMC,QAAO,CAAG,GAAhB,CACA,GAAMC,SAAQ,CAAG,CAAjB,CAEA,GAAI,CAACX,KAAK,CAACY,aAAX,CAA0B,CACxBH,UAAU,CAAGI,aAAa,CAACF,QAAD,CAA1B,CACD,CAFD,IAEO,IAAIX,KAAK,CAACY,aAAN,CAAoBE,MAApB,CAA6BH,QAAjC,CAA2C,CAChDF,UAAU,CAAGI,aAAa,CAACF,QAAQ,CAAGX,KAAK,CAACY,aAAN,CAAoBE,MAAhC,CAA1B,CACD,CAED,mBACE,aAAK,SAAS,CAAC,+EAAf,wBACE,KAAC,IAAD,kBAAUN,UAAV,EADF,cAEE,YAAK,SAAS,CAAC,wBAAf,uBACE,aAAK,SAAS,CAAC,eAAf,WACGR,KAAK,CAACY,aAAN,EACCZ,KAAK,CAACY,aAAN,CAAoBT,GAApB,CAAwB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CACvC;AACA,GAAIA,KAAK,EAAIM,QAAb,CAAuB,CACrB,MAAOL,UAAP,CACD,CAED,GAAIS,UAAS,kBAAQX,IAAR,CAAb,CACAW,SAAS,CAACC,YAAV,CAAyBN,OAAzB,CAEA,GAAMO,UAAS,CAAG,CAAC,CAAEC,QAAQ,CAAEH,SAAZ,CAAD,CAAlB,CAEA,mBACE,KAAC,UAAD,EAEE,KAAK,CAAEE,SAFT,CAGE,KAAK,CAAEZ,KAHT,CAIE,aAAa,CAAED,IAAI,CAACe,aAJtB,EACOd,KADP,CADF,CAQD,CAnBD,CAFJ,CAsBGI,UAAU,EACTA,UAAU,CAACN,GAAX,CAAe,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC9B,mBACE,YAAiB,SAAS,CAAC,gBAA3B,UACGD,IADH,EAAUC,KAAV,CADF,CAKD,CAND,CAvBJ,GADF,EAFF,GADF,CAsCD,CAlDD,CAoDA,QAASQ,cAAT,CAAuBO,KAAvB,CAA8B,CAC5B,GAAMV,QAAO,CAAG,GAAhB,CAEA,GAAIW,YAAW,CAAG,EAAlB,CACA,IAAK,GAAIC,EAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGF,KAApB,CAA2BE,CAAC,EAA5B,CAAgC,CAC9BD,WAAW,CAACE,IAAZ,CAAiBb,OAAjB,EACD,CACD,MAAOW,YAAP,CACD,CAED,cAAetB,cAAf"}, "metadata": {}, "sourceType": "module"}