{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\n\nvar usePagination = function usePagination(service, options) {\n  var _a;\n\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _b = options.defaultPageSize,\n      defaultPageSize = _b === void 0 ? 10 : _b,\n      _c = options.defaultCurrent,\n      defaultCurrent = _c === void 0 ? 1 : _c,\n      rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function refreshDepsAction() {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n\n  var _d = result.params[0] || {},\n      _e = _d.current,\n      current = _e === void 0 ? 1 : _e,\n      _f = _d.pageSize,\n      pageSize = _f === void 0 ? defaultPageSize : _f;\n\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n\n  var onChange = function onChange(c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n\n    var _a = __read(result.params || []),\n        _b = _a[0],\n        oldPaginationParams = _b === void 0 ? {} : _b,\n        restParams = _a.slice(1);\n\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n\n  var changeCurrent = function changeCurrent(c) {\n    onChange(c, pageSize);\n  };\n\n  var changePageSize = function changePageSize(p) {\n    onChange(current, p);\n  };\n\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\n\nexport default usePagination;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "useMemo", "useMemoizedFn", "useRequest", "usePagination", "service", "options", "_a", "_b", "defaultPageSize", "_c", "defaultCurrent", "rest", "result", "defaultParams", "current", "pageSize", "refreshDepsAction", "changeCurrent", "_d", "params", "_e", "_f", "total", "data", "totalPage", "Math", "ceil", "onChange", "c", "to<PERSON><PERSON><PERSON>", "toPageSize", "tempTotalPage", "max", "oldPaginationParams", "restParams", "run", "changePageSize", "pagination"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/usePagination/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nvar usePagination = function usePagination(service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var _b = options.defaultPageSize,\n    defaultPageSize = _b === void 0 ? 10 : _b,\n    _c = options.defaultCurrent,\n    defaultCurrent = _c === void 0 ? 1 : _c,\n    rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function refreshDepsAction() {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n  var _d = result.params[0] || {},\n    _e = _d.current,\n    current = _e === void 0 ? 1 : _e,\n    _f = _d.pageSize,\n    pageSize = _f === void 0 ? defaultPageSize : _f;\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n  var onChange = function onChange(c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n    var _a = __read(result.params || []),\n      _b = _a[0],\n      oldPaginationParams = _b === void 0 ? {} : _b,\n      restParams = _a.slice(1);\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n  var changeCurrent = function changeCurrent(c) {\n    onChange(c, pageSize);\n  };\n  var changePageSize = function changePageSize(p) {\n    onChange(current, p);\n  };\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\nexport default usePagination;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUV,CAAV,EAAaW,CAAb,EAAgB;EAClD,IAAIZ,CAAC,GAAG,EAAR;;EACA,KAAK,IAAIM,CAAT,IAAcL,CAAd,EAAiB;IACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,KAA8CM,CAAC,CAACC,OAAF,CAAUP,CAAV,IAAe,CAAjE,EAAoEN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;EACrE;;EACD,IAAIL,CAAC,IAAI,IAAL,IAAa,OAAOH,MAAM,CAACgB,qBAAd,KAAwC,UAAzD,EAAqE,KAAK,IAAIZ,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAGR,MAAM,CAACgB,qBAAP,CAA6Bb,CAA7B,CAApB,EAAqDC,CAAC,GAAGI,CAAC,CAACD,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IAC3I,IAAIU,CAAC,CAACC,OAAF,CAAUP,CAAC,CAACJ,CAAD,CAAX,IAAkB,CAAlB,IAAuBJ,MAAM,CAACS,SAAP,CAAiBQ,oBAAjB,CAAsCN,IAAtC,CAA2CR,CAA3C,EAA8CK,CAAC,CAACJ,CAAD,CAA/C,CAA3B,EAAgFF,CAAC,CAACM,CAAC,CAACJ,CAAD,CAAF,CAAD,GAAUD,CAAC,CAACK,CAAC,CAACJ,CAAD,CAAF,CAAX;EACjF;EACD,OAAOF,CAAP;AACD,CATD;;AAUA,IAAIgB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAad,CAAb,EAAgB;EAClD,IAAIe,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIf,CAAC,GAAGgB,CAAC,CAACT,IAAF,CAAOQ,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEV,CAHF;;EAIA,IAAI;IACF,OAAO,CAACT,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACkB,CAAC,GAAGnB,CAAC,CAACqB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDF,EAAE,CAACG,IAAH,CAAQJ,CAAC,CAACK,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdf,CAAC,GAAG;MACFe,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIN,CAAC,IAAI,CAACA,CAAC,CAACG,IAAR,KAAiBN,CAAC,GAAGhB,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCgB,CAAC,CAACT,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIU,CAAJ,EAAO,MAAMA,CAAC,CAACe,KAAR;IACR;EACF;;EACD,OAAOL,EAAP;AACD,CAvBD;;AAwBA,IAAIM,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAI3B,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIH,CAAC,GAAG,CAAR,EAAW8B,CAAC,GAAGF,IAAI,CAACzB,MAApB,EAA4BiB,EAAjC,EAAqCpB,CAAC,GAAG8B,CAAzC,EAA4C9B,CAAC,EAA7C,EAAiD;IACnF,IAAIoB,EAAE,IAAI,EAAEpB,CAAC,IAAI4B,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACR,EAAL,EAASA,EAAE,GAAGW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,EAAiC,CAAjC,EAAoC5B,CAApC,CAAL;MACToB,EAAE,CAACpB,CAAD,CAAF,GAAQ4B,IAAI,CAAC5B,CAAD,CAAZ;IACD;EACF;EACD,OAAO2B,EAAE,CAACM,MAAH,CAAUb,EAAE,IAAIW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASM,OAAT,QAAwB,OAAxB;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,UAAP,MAAuB,eAAvB;;AACA,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,OAAvB,EAAgCC,OAAhC,EAAyC;EAC3D,IAAIC,EAAJ;;EACA,IAAID,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIE,EAAE,GAAGF,OAAO,CAACG,eAAjB;EAAA,IACEA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EADzC;EAAA,IAEEE,EAAE,GAAGJ,OAAO,CAACK,cAFf;EAAA,IAGEA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAhB,GAAoBA,EAHvC;EAAA,IAIEE,IAAI,GAAGpC,MAAM,CAAC8B,OAAD,EAAU,CAAC,iBAAD,EAAoB,gBAApB,CAAV,CAJf;;EAKA,IAAIO,MAAM,GAAGV,UAAU,CAACE,OAAD,EAAU3C,QAAQ,CAAC;IACxCoD,aAAa,EAAE,CAAC;MACdC,OAAO,EAAEJ,cADK;MAEdK,QAAQ,EAAEP;IAFI,CAAD,CADyB;IAKxCQ,iBAAiB,EAAE,SAASA,iBAAT,GAA6B;MAC9C;MACAC,aAAa,CAAC,CAAD,CAAb;IACD;EARuC,CAAD,EAStCN,IATsC,CAAlB,CAAvB;;EAUA,IAAIO,EAAE,GAAGN,MAAM,CAACO,MAAP,CAAc,CAAd,KAAoB,EAA7B;EAAA,IACEC,EAAE,GAAGF,EAAE,CAACJ,OADV;EAAA,IAEEA,OAAO,GAAGM,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAhB,GAAoBA,EAFhC;EAAA,IAGEC,EAAE,GAAGH,EAAE,CAACH,QAHV;EAAA,IAIEA,QAAQ,GAAGM,EAAE,KAAK,KAAK,CAAZ,GAAgBb,eAAhB,GAAkCa,EAJ/C;;EAKA,IAAIC,KAAK,GAAG,CAAC,CAAChB,EAAE,GAAGM,MAAM,CAACW,IAAb,MAAuB,IAAvB,IAA+BjB,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACgB,KAA5D,KAAsE,CAAlF;EACA,IAAIE,SAAS,GAAGxB,OAAO,CAAC,YAAY;IAClC,OAAOyB,IAAI,CAACC,IAAL,CAAUJ,KAAK,GAAGP,QAAlB,CAAP;EACD,CAFsB,EAEpB,CAACA,QAAD,EAAWO,KAAX,CAFoB,CAAvB;;EAGA,IAAIK,QAAQ,GAAG,SAASA,QAAT,CAAkBC,CAAlB,EAAqB1D,CAArB,EAAwB;IACrC,IAAI2D,SAAS,GAAGD,CAAC,IAAI,CAAL,GAAS,CAAT,GAAaA,CAA7B;IACA,IAAIE,UAAU,GAAG5D,CAAC,IAAI,CAAL,GAAS,CAAT,GAAaA,CAA9B;IACA,IAAI6D,aAAa,GAAGN,IAAI,CAACC,IAAL,CAAUJ,KAAK,GAAGQ,UAAlB,CAApB;;IACA,IAAID,SAAS,GAAGE,aAAhB,EAA+B;MAC7BF,SAAS,GAAGJ,IAAI,CAACO,GAAL,CAAS,CAAT,EAAYD,aAAZ,CAAZ;IACD;;IACD,IAAIzB,EAAE,GAAG1B,MAAM,CAACgC,MAAM,CAACO,MAAP,IAAiB,EAAlB,CAAf;IAAA,IACEZ,EAAE,GAAGD,EAAE,CAAC,CAAD,CADT;IAAA,IAEE2B,mBAAmB,GAAG1B,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EAF7C;IAAA,IAGE2B,UAAU,GAAG5B,EAAE,CAACR,KAAH,CAAS,CAAT,CAHf;;IAIAc,MAAM,CAACuB,GAAP,CAAW7D,KAAX,CAAiBsC,MAAjB,EAAyBpB,aAAa,CAAC,CAAC/B,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKwE,mBAAL,CAAT,EAAoC;MAClFnB,OAAO,EAAEe,SADyE;MAElFd,QAAQ,EAAEe;IAFwE,CAApC,CAAT,CAAD,EAGjClD,MAAM,CAACsD,UAAD,CAH2B,EAGb,KAHa,CAAtC;EAID,CAfD;;EAgBA,IAAIjB,aAAa,GAAG,SAASA,aAAT,CAAuBW,CAAvB,EAA0B;IAC5CD,QAAQ,CAACC,CAAD,EAAIb,QAAJ,CAAR;EACD,CAFD;;EAGA,IAAIqB,cAAc,GAAG,SAASA,cAAT,CAAwBlE,CAAxB,EAA2B;IAC9CyD,QAAQ,CAACb,OAAD,EAAU5C,CAAV,CAAR;EACD,CAFD;;EAGA,OAAOT,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKmD,MAAL,CAAT,EAAuB;IACpCyB,UAAU,EAAE;MACVvB,OAAO,EAAEA,OADC;MAEVC,QAAQ,EAAEA,QAFA;MAGVO,KAAK,EAAEA,KAHG;MAIVE,SAAS,EAAEA,SAJD;MAKVG,QAAQ,EAAE1B,aAAa,CAAC0B,QAAD,CALb;MAMVV,aAAa,EAAEhB,aAAa,CAACgB,aAAD,CANlB;MAOVmB,cAAc,EAAEnC,aAAa,CAACmC,cAAD;IAPnB;EADwB,CAAvB,CAAf;AAWD,CA9DD;;AA+DA,eAAejC,aAAf"}, "metadata": {}, "sourceType": "module"}