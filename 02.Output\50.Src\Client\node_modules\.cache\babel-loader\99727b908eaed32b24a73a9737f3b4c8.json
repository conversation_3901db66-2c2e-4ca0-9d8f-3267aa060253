{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\n\nvar setRafTimeout = function setRafTimeout(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n\n  var handle = {\n    id: 0\n  };\n  var startTime = new Date().getTime();\n\n  var loop = function loop() {\n    var current = new Date().getTime();\n\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\n\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\n\nvar clearRafTimeout = function clearRafTimeout(handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n\n  cancelAnimationFrame(handle.id);\n};\n\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearRafTimeout(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\n\nexport default useRafTimeout;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "setRafTimeout", "callback", "delay", "requestAnimationFrame", "undefined", "id", "setTimeout", "handle", "startTime", "Date", "getTime", "loop", "current", "cancelAnimationFrameIsNotDefined", "t", "cancelAnimationFrame", "clearRafTimeout", "clearTimeout", "useRafTimeout", "fn", "fnRef", "timerRef", "clear"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRafTimeout/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafTimeout = function setRafTimeout(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n  var handle = {\n    id: 0\n  };\n  var startTime = new Date().getTime();\n  var loop = function loop() {\n    var current = new Date().getTime();\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafTimeout = function clearRafTimeout(handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return function () {\n      if (timerRef.current) {\n        clearRafTimeout(timerRef.current);\n      }\n    };\n  }, [delay]);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  return clear;\n}\nexport default useRafTimeout;"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,SAAtB,EAAiCC,MAAjC,QAA+C,OAA/C;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,QAAT,QAAyB,UAAzB;;AACA,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,QAAvB,EAAiCC,KAAjC,EAAwC;EAC1D,IAAIA,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,CAAR;EACD;;EACD,IAAI,OAAOC,qBAAP,KAAiC,OAAOC,SAA5C,EAAuD;IACrD,OAAO;MACLC,EAAE,EAAEC,UAAU,CAACL,QAAD,EAAWC,KAAX;IADT,CAAP;EAGD;;EACD,IAAIK,MAAM,GAAG;IACXF,EAAE,EAAE;EADO,CAAb;EAGA,IAAIG,SAAS,GAAG,IAAIC,IAAJ,GAAWC,OAAX,EAAhB;;EACA,IAAIC,IAAI,GAAG,SAASA,IAAT,GAAgB;IACzB,IAAIC,OAAO,GAAG,IAAIH,IAAJ,GAAWC,OAAX,EAAd;;IACA,IAAIE,OAAO,GAAGJ,SAAV,IAAuBN,KAA3B,EAAkC;MAChCD,QAAQ;IACT,CAFD,MAEO;MACLM,MAAM,CAACF,EAAP,GAAYF,qBAAqB,CAACQ,IAAD,CAAjC;IACD;EACF,CAPD;;EAQAJ,MAAM,CAACF,EAAP,GAAYF,qBAAqB,CAACQ,IAAD,CAAjC;EACA,OAAOJ,MAAP;AACD,CAvBD;;AAwBA,SAASM,gCAAT,CAA0CC,CAA1C,EAA6C;EAC3C,OAAO,OAAOC,oBAAP,KAAgC,OAAOX,SAA9C;AACD;;AACD,IAAIY,eAAe,GAAG,SAASA,eAAT,CAAyBT,MAAzB,EAAiC;EACrD,IAAIM,gCAAgC,CAACN,MAAM,CAACF,EAAR,CAApC,EAAiD;IAC/C,OAAOY,YAAY,CAACV,MAAM,CAACF,EAAR,CAAnB;EACD;;EACDU,oBAAoB,CAACR,MAAM,CAACF,EAAR,CAApB;AACD,CALD;;AAMA,SAASa,aAAT,CAAuBC,EAAvB,EAA2BjB,KAA3B,EAAkC;EAChC,IAAIkB,KAAK,GAAGtB,SAAS,CAACqB,EAAD,CAArB;EACA,IAAIE,QAAQ,GAAGxB,MAAM,EAArB;EACAD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAD,CAAT,IAAoBA,KAAK,GAAG,CAAhC,EAAmC;IACnCmB,QAAQ,CAACT,OAAT,GAAmBZ,aAAa,CAAC,YAAY;MAC3CoB,KAAK,CAACR,OAAN;IACD,CAF+B,EAE7BV,KAF6B,CAAhC;IAGA,OAAO,YAAY;MACjB,IAAImB,QAAQ,CAACT,OAAb,EAAsB;QACpBI,eAAe,CAACK,QAAQ,CAACT,OAAV,CAAf;MACD;IACF,CAJD;EAKD,CAVQ,EAUN,CAACV,KAAD,CAVM,CAAT;EAWA,IAAIoB,KAAK,GAAG3B,WAAW,CAAC,YAAY;IAClC,IAAI0B,QAAQ,CAACT,OAAb,EAAsB;MACpBI,eAAe,CAACK,QAAQ,CAACT,OAAV,CAAf;IACD;EACF,CAJsB,EAIpB,EAJoB,CAAvB;EAKA,OAAOU,KAAP;AACD;;AACD,eAAeJ,aAAf"}, "metadata": {}, "sourceType": "module"}