{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport utils from './../utils.js';\n\nvar InterceptorManager = /*#__PURE__*/function () {\n  function InterceptorManager() {\n    _classCallCheck(this, InterceptorManager);\n\n    this.handlers = [];\n  }\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n\n\n  _createClass(InterceptorManager, [{\n    key: \"use\",\n    value: function use(fulfilled, rejected, options) {\n      this.handlers.push({\n        fulfilled: fulfilled,\n        rejected: rejected,\n        synchronous: options ? options.synchronous : false,\n        runWhen: options ? options.runWhen : null\n      });\n      return this.handlers.length - 1;\n    }\n    /**\n     * Remove an interceptor from the stack\n     *\n     * @param {Number} id The ID that was returned by `use`\n     *\n     * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n     */\n\n  }, {\n    key: \"eject\",\n    value: function eject(id) {\n      if (this.handlers[id]) {\n        this.handlers[id] = null;\n      }\n    }\n    /**\n     * Clear all interceptors from the stack\n     *\n     * @returns {void}\n     */\n\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      if (this.handlers) {\n        this.handlers = [];\n      }\n    }\n    /**\n     * Iterate over all the registered interceptors\n     *\n     * This method is particularly useful for skipping over any\n     * interceptors that may have become `null` calling `eject`.\n     *\n     * @param {Function} fn The function to call for each interceptor\n     *\n     * @returns {void}\n     */\n\n  }, {\n    key: \"forEach\",\n    value: function forEach(fn) {\n      utils.forEach(this.handlers, function forEachHandler(h) {\n        if (h !== null) {\n          fn(h);\n        }\n      });\n    }\n  }]);\n\n  return InterceptorManager;\n}();\n\nexport default InterceptorManager;", "map": {"version": 3, "names": ["utils", "InterceptorManager", "handlers", "fulfilled", "rejected", "options", "push", "synchronous", "runWhen", "length", "id", "fn", "for<PERSON>ach", "forEachHandler", "h"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n"], "mappings": "AAAA;;;;AAEA,OAAOA,KAAP,MAAkB,eAAlB;;IAEMC,kB;EACJ,8BAAc;IAAA;;IACZ,KAAKC,QAAL,GAAgB,EAAhB;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;;;WACE,aAAIC,SAAJ,EAAeC,QAAf,EAAyBC,OAAzB,EAAkC;MAChC,KAAKH,QAAL,CAAcI,IAAd,CAAmB;QACjBH,SAAS,EAATA,SADiB;QAEjBC,QAAQ,EAARA,QAFiB;QAGjBG,WAAW,EAAEF,OAAO,GAAGA,OAAO,CAACE,WAAX,GAAyB,KAH5B;QAIjBC,OAAO,EAAEH,OAAO,GAAGA,OAAO,CAACG,OAAX,GAAqB;MAJpB,CAAnB;MAMA,OAAO,KAAKN,QAAL,CAAcO,MAAd,GAAuB,CAA9B;IACD;IAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;WACE,eAAMC,EAAN,EAAU;MACR,IAAI,KAAKR,QAAL,CAAcQ,EAAd,CAAJ,EAAuB;QACrB,KAAKR,QAAL,CAAcQ,EAAd,IAAoB,IAApB;MACD;IACF;IAED;AACF;AACA;AACA;AACA;;;;WACE,iBAAQ;MACN,IAAI,KAAKR,QAAT,EAAmB;QACjB,KAAKA,QAAL,GAAgB,EAAhB;MACD;IACF;IAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;WACE,iBAAQS,EAAR,EAAY;MACVX,KAAK,CAACY,OAAN,CAAc,KAAKV,QAAnB,EAA6B,SAASW,cAAT,CAAwBC,CAAxB,EAA2B;QACtD,IAAIA,CAAC,KAAK,IAAV,EAAgB;UACdH,EAAE,CAACG,CAAD,CAAF;QACD;MACF,CAJD;IAKD;;;;;;AAGH,eAAeb,kBAAf"}, "metadata": {}, "sourceType": "module"}