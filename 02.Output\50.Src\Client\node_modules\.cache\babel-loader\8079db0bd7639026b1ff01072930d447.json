{"ast": null, "code": "import _toConsumableArray from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _slicedToArray from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport React, { createContext, useState, useRef, useEffect, useContext } from 'react';\nimport SockJS from 'sockjs-client';\nimport { Client } from '@stomp/stompjs';\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar StompContext = createContext(undefined);\nvar _excluded = [\"url\", \"children\", \"stompClientOptions\"];\n/**\r\n * The StompSessionProvider manages the STOMP connection\r\n * All Hooks and HOCs in this library require an ancestor of this type.\r\n * The URL to connect to can be specified via the url prop.\r\n * Depending on the Schema of the URL either Sockjs or a raw Websocket is used.\r\n * You can override this behavior with the brokerURL or webSocketFactory props, which will then be forwarded to @stomp/stompjs\r\n * Custom @stomp/stompjs options can be used as props.\r\n * Please consult the @stomp/stompjs documentation for more information.\r\n */\n\nfunction StompSessionProvider(props) {\n  var url = props.url,\n      children = props.children,\n      stompClientOptions = props.stompClientOptions,\n      stompOptions = _objectWithoutPropertiesLoose(props, _excluded); // Support old API\n\n\n  if (stompClientOptions) stompOptions = stompClientOptions;\n\n  var _useState = useState(undefined),\n      _useState2 = _slicedToArray(_useState, 2),\n      client = _useState2[0],\n      setClient = _useState2[1];\n\n  var subscriptionRequests = useRef(new Map());\n  useEffect(function () {\n    var _client = new Client(stompOptions);\n\n    if (!stompOptions.brokerURL && !stompOptions.webSocketFactory) {\n      _client.webSocketFactory = function () {\n        var _window, _window$location;\n\n        var parsedUrl = new URL(url, (_window = window) == null ? void 0 : (_window$location = _window.location) == null ? void 0 : _window$location.href);\n\n        if (parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:') {\n          return new SockJS(url);\n        } else if (parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:') {\n          return new WebSocket(url);\n        } else throw new Error('Protocol not supported');\n      };\n    }\n\n    _client.onConnect = function (frame) {\n      if (stompOptions.onConnect) stompOptions.onConnect(frame);\n      subscriptionRequests.current.forEach(function (value) {\n        value.subscription = _client.subscribe(value.destination, value.callback, value.headers);\n      });\n      setClient(_client);\n    };\n\n    _client.onWebSocketClose = function (event) {\n      if (stompOptions.onWebSocketClose) stompOptions.onWebSocketClose(event);\n      setClient(undefined);\n    };\n\n    if (!stompOptions.onStompError) {\n      _client.onStompError = function (frame) {\n        throw frame;\n      };\n    }\n\n    _client.activate();\n\n    return function () {\n      _client.deactivate();\n    };\n  }, [url].concat(_toConsumableArray(Object.values(stompOptions))));\n\n  var subscribe = function subscribe(destination, callback) {\n    var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var subscriptionId = Math.random().toString(36).substr(2, 9);\n    var subscriptionRequest = {\n      destination: destination,\n      callback: callback,\n      headers: headers\n    };\n    subscriptionRequests.current.set(subscriptionId, subscriptionRequest);\n\n    if (client && client.connected) {\n      subscriptionRequest.subscription = client.subscribe(destination, callback, headers);\n    }\n\n    return function () {\n      var subscriptionData = subscriptionRequests.current.get(subscriptionId);\n\n      if (subscriptionData.subscription) {\n        subscriptionData.subscription.unsubscribe();\n      }\n\n      subscriptionRequests.current.delete(subscriptionId);\n    };\n  };\n\n  return React.createElement(StompContext.Provider, {\n    value: {\n      client: client,\n      subscribe: subscribe\n    }\n  }, children);\n}\n/**\r\n *\r\n * @param destinations The destinations to subscribe to. Can be a string for a single destination or an array of strings for multiple.\r\n * @param onMessage Callback called when a message arrives for this subscription\r\n * @param headers Additional Headers for this subscription, consult @stomp/stompjs docs.\r\n */\n\n\nfunction useSubscription(destinations, onMessage) {\n  var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var stompContext = useContext(StompContext);\n  if (stompContext === undefined) throw new Error('There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs');\n  var callbackRef = useRef(onMessage);\n\n  var _destinations = Array.isArray(destinations) ? destinations : [destinations];\n\n  callbackRef.current = onMessage;\n  useEffect(function () {\n    var cleanUpFunctions = [];\n\n    _destinations.forEach(function (_destination) {\n      return cleanUpFunctions.push(stompContext.subscribe(_destination, function (message) {\n        callbackRef.current(message);\n      }, headers));\n    });\n\n    return function () {\n      cleanUpFunctions.forEach(function (_cleanUpFunction) {\n        _cleanUpFunction();\n      });\n    };\n  }, [Object.values(_destinations).toString(), Object.values(headers).toString()]);\n}\n/**\r\n * Returns the Stomp Client from @stomp/stompjs\r\n * This will be undefined if the client is currently not connected\r\n */\n\n\nfunction useStompClient() {\n  var context = useContext(StompContext);\n  if (context === undefined) throw new Error('There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs');\n  return context.client;\n}\n\nfunction withStompClient(WrappedComponent) {\n  return function (props) {\n    var stompClient = useStompClient();\n    return React.createElement(WrappedComponent, Object.assign({\n      stompClient: stompClient\n    }, props));\n  };\n}\n\nfunction withSubscription(WrappedComponent, destinations) {\n  var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return function (props) {\n    var ref = useRef();\n    useSubscription(destinations, function (message) {\n      if (ref.current) ref.current.onMessage(message);\n    }, headers); // @ts-ignore\n\n    return React.createElement(WrappedComponent, Object.assign({\n      ref: ref\n    }, props));\n  };\n}\n\nvar subscriptions = new Map();\n\nfunction subscribeMock(destination, callback) {\n  var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var subscriptionId = Math.random().toString(36).substr(2, 9);\n\n  if (!subscriptions.has(destination)) {\n    subscriptions.set(destination, new Map());\n  } // @ts-ignore\n\n\n  subscriptions.get(destination).set(subscriptionId, callback);\n  return function () {\n    // @ts-ignore\n    subscriptions.get(destination).delete(subscriptionId);\n  };\n}\n/**\r\n * Simulates receiving a message from the server to the specified destination\r\n * @param destination The topic to send the message to\r\n * @param message The message to send\r\n */\n\n\nfunction mockReceiveMessage(destination, message) {\n  if (subscriptions.has(destination)) {\n    // @ts-ignore\n    subscriptions.get(destination).forEach(function (callback) {\n      callback(message);\n    });\n  }\n}\n/**\r\n * Gets the current subscriptions for the specified destination\r\n * @param destination The topic to get the subscriptions for, or undefined to get all subscriptions\r\n */\n\n\nfunction getMockSubscriptions(destination) {\n  if (destination) {\n    return subscriptions.get(destination);\n  }\n\n  return subscriptions;\n}\n\nvar messages = new Map();\n/**\r\n * A mock implementation of the publish function of the @stomp/stompjs client.\r\n * Will store the messages in a map, keyed by the destination.\r\n * @param params\r\n */\n\nfunction mockClientPublish(params) {\n  if (!messages.has(params.destination)) {\n    messages.set(params.destination, []);\n  } // @ts-ignore\n\n\n  messages.get(params.destination).push(params);\n}\n/**\r\n * Gets a default Mock of the @stomp/stompjs client.\r\n * If you require a custom client, you can use this as a base.\r\n */\n\n\nfunction getMockClient() {\n  return {\n    publish: mockClientPublish\n  };\n}\n/**\r\n * Gets all messages which have been sent via a mock client.\r\n * @param destination The destination to get messages for, or undefined to get all messages.\r\n */\n\n\nfunction getSentMockMessages(destination) {\n  if (destination) {\n    return messages.get(destination);\n  }\n\n  return messages;\n}\n/**\r\n * A mock StompSessionProvider.\r\n * Messages send via this mock implementation can be received via the getSentMockMessages method.\r\n * Subscriptions can be received via the getMockSubscriptions method.\r\n * The sendMockMessage method can be used, to simulate receiving a message from the server.\r\n *\r\n * @param props.client Optional. Can be used to provide a custom mock of the sompjs client,\r\n * in case you require additional properties/functions to be present. getMockClient can be used as a base.\r\n * @constructor\r\n */\n\n\nfunction StompSessionProviderMock(props) {\n  var _props$client;\n\n  return React.createElement(StompContext.Provider, {\n    value: {\n      subscribe: subscribeMock,\n      // @ts-ignore\n      client: (_props$client = props.client) != null ? _props$client : getMockClient()\n    }\n  }, props.children);\n}\n/**\r\n * Resets the state of the mock implementation, clearing all subscriptions and messages.\r\n */\n\n\nfunction reset() {\n  subscriptions.clear();\n  messages.clear();\n}\n\nvar index = {\n  __proto__: null,\n  StompSessionProviderMock: StompSessionProviderMock,\n  getMockClient: getMockClient,\n  mockClientPublish: mockClientPublish,\n  mockReceiveMessage: mockReceiveMessage,\n  getSentMockMessages: getSentMockMessages,\n  reset: reset,\n  getMockSubscriptions: getMockSubscriptions\n};\nexport { StompSessionProvider, index as mock, useStompClient, useSubscription, withStompClient, withSubscription };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGA,IAAMA,YAAY,GAAGC,aAAa,CAChCC,SADgC,CAAlC;;ACSA;;;;;;;;AAQG;;AACH,SAASC,oBAAT,CAA8BC,KAA9B,EAA8D;EACxD,IAAEC,GAAF,GAAyDD,KAAzD,CAAEC,GAAF;EAAA,IAAOC,QAAP,GAAyDF,KAAzD,CAAOE,QAAP;EAAA,IAAiBC,kBAAjB,GAAyDH,KAAzD,CAAiBG,kBAAjB;EAAA,IAAwCC,YAAxC,GAAJC,8BAA6DL,KAA7D,YAAI,CADwD;;;EAI5D,IAAIG,kBAAJ,EAAwBC,YAAY,GAAGD,kBAAfC;;EAElB,gBAAsBE,QAAQ,CAAqBR,SAArB,CAA9B;EAAA;EAAA,IAACS,MAAD;EAAA,IAASC,SAAT;;EACN,IAAMC,oBAAoB,GAAGC,MAAM,CAAC,IAAIC,GAAJ,EAAD,CAAnC;EAEAC,SAAS,CAAC,YAAK;IACb,IAAMC,OAAO,GAAG,IAAIC,MAAJ,CAAWV,YAAX,CAAhB;;IAEI,KAACA,YAAY,CAACW,SAAd,IAA2B,CAACX,YAAY,CAACY,gBAAzC,EAA2D;MAC7DH,OAAO,CAACG,gBAARH,GAA2B;QAAA;;QACzB,IAAMI,SAAS,GAAG,IAAIC,GAAJ,CAAQjB,GAAR,aAAakB,MAAb,yCAAaC,OAAQC,SAArB,KAAa,IAAb,GAAa,MAAb,GAAaC,iBAAkBC,IAA/B,CAAlB;;QACIN,aAAS,CAACO,QAAVP,KAAuB,OAAvBA,IAAkCA,SAAS,CAACO,QAAVP,KAAuB,QAAzDA,EAAmE;UACrE,OAAO,IAAIQ,MAAJ,CAAWxB,GAAX,CAAP;QADF,CAAIgB,MAEG,IACLA,SAAS,CAACO,QAAVP,KAAuB,KAAvBA,IACAA,SAAS,CAACO,QAAVP,KAAuB,MAFlB,EAGL;UACA,OAAO,IAAIS,SAAJ,CAAczB,GAAd,CAAP;QAJK,OAKA,MAAM,IAAI0B,KAAJ,CAAU,wBAAV,CAAN;MATT;IAWD;;IAEDd,OAAO,CAACe,SAARf,GAAoB,UAAUgB,KAAV,EAAe;MAC7BzB,gBAAY,CAACwB,SAAbxB,EAAwBA,YAAY,CAACwB,SAAbxB,CAAuByB,KAAvBzB;MAE5BK,oBAAoB,CAACqB,OAArBrB,CAA6BsB,OAA7BtB,CAAsCuB,eAAD,EAAU;QAC7CA,KAAK,CAACC,YAAND,GAAqBnB,OAAO,CAACqB,SAARrB,CACnBmB,KAAK,CAACG,WADatB,EAEnBmB,KAAK,CAACI,QAFavB,EAGnBmB,KAAK,CAACK,OAHaxB,CAArBmB;MADF;MAQAxB,SAAS,CAACK,OAAD,CAATL;IAXF;;IAcAK,OAAO,CAACyB,gBAARzB,GAA2B,UAAU0B,KAAV,EAAe;MACpCnC,gBAAY,CAACkC,gBAAblC,EAA+BA,YAAY,CAACkC,gBAAblC,CAA8BmC,KAA9BnC;MAEnCI,SAAS,CAACV,SAAD,CAATU;IAHF;;IAMA,IAAI,CAACJ,YAAY,CAACoC,YAAlB,EAAgC;MAC9B3B,OAAO,CAAC2B,YAAR3B,GAAuB,UAAUgB,KAAV,EAAe;QACpC,MAAMA,KAAN;MADF;IAGD;;IAEDhB,OAAO,CAAC4B,QAAR5B;;IAEA,OAAO,YAAK;MACVA,OAAO,CAAC6B,UAAR7B;IADF;EA7CO,IAgDLZ,GAhDK,4BAgDG0C,MAAM,CAACC,MAAPD,CAAcvC,YAAduC,CAhDH,GAAT/B;;EAkDMsB,aAAS,GAAG,SAAZA,SAAY,CAChBC,WADgB,EAEhBC,QAFgB,EAId;IAAA,IADFC,OACE,uEADsB,EACtB;IACF,IAAMQ,cAAc,GAAGC,IAAI,CAACC,MAALD,GAAcE,QAAdF,CAAuB,EAAvBA,EAA2BG,MAA3BH,CAAkC,CAAlCA,EAAqC,CAArCA,CAAvB;IACA,IAAMI,mBAAmB,GAA6B;MACpDf,WADoD,EACpDA,WADoD;MAEpDC,QAFoD,EAEpDA,QAFoD;MAGpDC;IAHoD,CAAtD;IAMA5B,oBAAoB,CAACqB,OAArBrB,CAA6B0C,GAA7B1C,CAAiCoC,cAAjCpC,EAAiDyC,mBAAjDzC;;IAEA,IAAIF,MAAM,IAAIA,MAAM,CAAC6C,SAArB,EAAgC;MAC9BF,mBAAmB,CAACjB,YAApBiB,GAAmC3C,MAAM,CAAC2B,SAAP3B,CACjC4B,WADiC5B,EAEjC6B,QAFiC7B,EAGjC8B,OAHiC9B,CAAnC2C;IAKD;;IAED,OAAO,YAAK;MACJG,oBAAgB,GAAG5C,oBAAoB,CAACqB,OAArBrB,CAA6B6C,GAA7B7C,CAAiCoC,cAAjCpC,CAAnB4C;;MAEFA,oBAAgB,CAACpB,YAAjBoB,EAA+B;QACjCA,gBAAgB,CAACpB,YAAjBoB,CAA8BE,WAA9BF;MACD;;MAED5C,oBAAoB,CAACqB,OAArBrB,CAA6B+C,MAA7B/C,CAAoCoC,cAApCpC;IAPF;EAtBF,CAAMyB;;EAiCN,OACEuB,KAAC,cAADA,CAAC7D,YAAY,CAAC8D,QAAdD,EAAsB;IACpBzB,KAAK,EAAE;MACLzB,MADK,EACLA,MADK;MAEL2B;IAFK;EADa,CAAtBuB,EAMGvD,QANHuD,CADF;AAUD;ACvHD;;;;;AAKG;;;AACH,SAASE,eAAT,CACEC,YADF,EAEEC,SAFF,EAG4B;EAAA,IAA1BxB,OAA0B,uEAAF,EAAE;EAE1B,IAAMyB,YAAY,GAAGC,UAAU,CAACnE,YAAD,CAA/B;EAEIkE,gBAAY,KAAKhE,SAAjBgE,EACF,MAAM,IAAInC,KAAJ,CACJ,8EADI,CAAN;EAIF,IAAMqC,WAAW,GAAGtD,MAAM,CAAsBmD,SAAtB,CAA1B;;EACA,IAAMI,aAAa,GAAGC,KAAK,CAACC,OAAND,CAAcN,YAAdM,IAClBN,YADkBM,GAElB,CAACN,YAAD,CAFJ;;EAIAI,WAAW,CAAClC,OAAZkC,GAAsBH,SAAtBG;EAEApD,SAAS,CAAC,YAAK;IACPwD,oBAAgB,GAAmB,EAAnCA;;IAENH,aAAa,CAAClC,OAAdkC,CAAuBI,sBAAD;MAAA,OACpBD,gBAAgB,CAACE,IAAjBF,CACEN,YAAY,CAAC5B,SAAb4B,CACEO,YADFP,EAEGS,iBAAD,EAAY;QACVP,WAAW,CAAClC,OAAZkC,CAAoBO,OAApBP;MAHJ,GAKE3B,OALFyB,CADFM,CADoB;IAAA,CAAtBH;;IAYA,OAAO,YAAK;MACVG,gBAAgB,CAACrC,OAAjBqC,CAA0BI,0BAAD,EAAqB;QAC5CA,gBAAgB;MADlB;IADF;EAfO,GAoBN,CACD7B,MAAM,CAACC,MAAPD,CAAcsB,aAAdtB,EAA6BK,QAA7BL,EADC,EAEDA,MAAM,CAACC,MAAPD,CAAcN,OAAdM,EAAuBK,QAAvBL,EAFC,CApBM,CAAT/B;AAwBD;ACjDD;;;AAGG;;;AACH,SAAS6D,cAAT,GAAuB;EACrB,IAAMC,OAAO,GAAGX,UAAU,CACxBnE,YADwB,CAA1B;EAII8E,WAAO,KAAK5E,SAAZ4E,EACF,MAAM,IAAI/C,KAAJ,CACJ,8EADI,CAAN;EAIK+C,cAAO,CAACnE,MAARmE;AACR;;AChBD,SAASC,eAAT,CAA4BC,gBAA5B,EAAoE;EAClE,OAAQ5E,eAAD,EAAa;IACZ6E,eAAW,GAAGJ,cAAc,EAA5BI;IACCpB,2BAACmB,gBAADnB,EAAkBd;MAAAkC,WAAW,EAAEA;IAAb,GAA8B7E,KAA9B2C,CAAlBc;EAFT;AAID;;ACCD,SAASqB,gBAAT,CACEF,gBADF,EAEEhB,YAFF,EAG4B;EAAA,IAA1BvB,OAA0B,uEAAF,EAAE;EAE1B,OAAQrC,eAAD,EAAa;IACZ+E,OAAG,GAAGrE,MAAM,EAAZqE;IACNpB,eAAe,CACbC,YADa,EAEZW,iBAAD,EAAsB;MAChBQ,OAAG,CAACjD,OAAJiD,EAAaA,GAAG,CAACjD,OAAJiD,CAAYlB,SAAZkB,CAAsBR,OAAtBQ;IAHN,GAKb1C,OALa,CAAfsB,CAFkB;;IAWXF,2BAACmB,gBAADnB,EAAkBd;MAAAoC,GAAG,EAAEA;IAAL,GAAc/E,KAAd2C,CAAlBc;EAXT;AAaD;;ACxBM,IAAMuB,aAAa,GAAG,IAAIrE,GAAJ,EAAtB;;AAES,uBACdwB,WADc,EAEdC,QAFc,EAKY;EAAA,IAA1BC,OAA0B,uEAAF,EAAE;EAE1B,IAAMQ,cAAc,GAAGC,IAAI,CAACC,MAALD,GAAcE,QAAdF,CAAuB,EAAvBA,EAA2BG,MAA3BH,CAAkC,CAAlCA,EAAqC,CAArCA,CAAvB;;EAEA,IAAI,CAACkC,aAAa,CAACC,GAAdD,CAAkB7C,WAAlB6C,CAAL,EAAqC;IACnCA,aAAa,CAAC7B,GAAd6B,CAAkB7C,WAAlB6C,EAA+B,IAAIrE,GAAJ,EAA/BqE;EALwB;;;EAS1BA,aAAa,CAAC1B,GAAd0B,CAAkB7C,WAAlB6C,EAA+B7B,GAA/B6B,CAAmCnC,cAAnCmC,EAAmD5C,QAAnD4C;EAEA,OAAO,YAAK;IACV;IACAA,aAAa,CAAC1B,GAAd0B,CAAkB7C,WAAlB6C,EAA+BxB,MAA/BwB,CAAsCnC,cAAtCmC;EAFF;AAID;AAED;;;;AAIG;;;AACa,4BACd7C,WADc,EAEdoC,OAFc,EAEG;EAEjB,IAAIS,aAAa,CAACC,GAAdD,CAAkB7C,WAAlB6C,CAAJ,EAAoC;IAClC;IACAA,aAAa,CAAC1B,GAAd0B,CAAkB7C,WAAlB6C,EAA+BjD,OAA/BiD,CAAwC5C,kBAAD,EAAuB;MAC5DA,QAAQ,CAACmC,OAAD,CAARnC;IADF;EAGD;AACF;AAED;;;AAGG;;;AACG,SAAU8C,oBAAV,CAA+B/C,WAA/B,EAAmD;EACvD,IAAIA,WAAJ,EAAiB;IACf,OAAO6C,aAAa,CAAC1B,GAAd0B,CAAkB7C,WAAlB6C,CAAP;EACD;;EACD,OAAOA,aAAP;AACD;;ACnDM,IAAMG,QAAQ,GAAG,IAAIxE,GAAJ,EAAjB;AAEP;;;;AAIG;;AACG,SAAUyE,iBAAV,CAA4BC,MAA5B,EAAkD;EAClD,KAACF,QAAQ,CAACF,GAATE,CAAaE,MAAM,CAAClD,WAApBgD,CAAD,EAAmC;IACrCA,QAAQ,CAAChC,GAATgC,CAAaE,MAAM,CAAClD,WAApBgD,EAAiC,EAAjCA;EAFoD;;;EAMtDA,QAAQ,CAAC7B,GAAT6B,CAAaE,MAAM,CAAClD,WAApBgD,EAAiCb,IAAjCa,CAAsCE,MAAtCF;AACD;AAED;;;AAGG;;;SACaG,gBAAa;EACpB;IACLC,OAAO,EAAEH;EADJ;AAGR;AAED;;;AAGG;;;AACG,SAAUI,mBAAV,CAA8BrD,WAA9B,EAAkD;EACtD,IAAIA,WAAJ,EAAiB;IACf,OAAOgD,QAAQ,CAAC7B,GAAT6B,CAAahD,WAAbgD,CAAP;EACD;;EACD,OAAOA,QAAP;AACD;AChCD;;;;;;;;;AASG;;;AACqB,kCAAyBnF,KAAzB,EAGvB;EAAA;;EACC,OACEyD,KAAC,cAADA,CAAC7D,YAAY,CAAC8D,QAAdD,EAAsB;IACpBzB,KAAK,EAAE;MACLE,SAAS,EAAEuD,aADN;MAEL;MACAlF,MAAM,EAAEP,sBAAK,CAACO,MAANP,KAAF,IAAEA,GAAF0F,aAAE1F,GAAgBsF,aAAa;IAHhC;EADa,CAAtB7B,EAOGzD,KAAK,CAACE,QAPTuD,CADF;AAWD;AC3BD;;AAEG;;;SACakC,QAAK;EACnBX,aAAa,CAACY,KAAdZ;EACAG,QAAQ,CAACS,KAATT;AACD", "names": ["StompContext", "createContext", "undefined", "StompSessionProvider", "props", "url", "children", "stompClientOptions", "stompOptions", "_objectWithoutPropertiesLoose", "useState", "client", "setClient", "subscriptionRequests", "useRef", "Map", "useEffect", "_client", "Client", "brokerURL", "webSocketFactory", "parsedUrl", "URL", "window", "_window", "location", "_window$location", "href", "protocol", "SockJS", "WebSocket", "Error", "onConnect", "frame", "current", "for<PERSON>ach", "value", "subscription", "subscribe", "destination", "callback", "headers", "onWebSocketClose", "event", "onStompError", "activate", "deactivate", "Object", "values", "subscriptionId", "Math", "random", "toString", "substr", "subscriptionRequest", "set", "connected", "subscriptionData", "get", "unsubscribe", "delete", "React", "Provider", "useSubscription", "destinations", "onMessage", "stompContext", "useContext", "callback<PERSON><PERSON>", "_destinations", "Array", "isArray", "cleanUpFunctions", "_destination", "push", "message", "_cleanUpFunction", "useStompClient", "context", "withStompClient", "WrappedComponent", "stompClient", "withSubscription", "ref", "subscriptions", "has", "getMockSubscriptions", "messages", "mockClientPublish", "params", "getMockClient", "publish", "getSentMockMessages", "subscribeMock", "_props$client", "reset", "clear"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\context\\StompContext.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\components\\StompSessionProvider.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\hooks\\useSubscription.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\hooks\\useStompClient.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\hoc\\withStompClient.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\hoc\\withSubscription.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\mock\\subscriptions.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\mock\\client.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\mock\\StompSessionProviderMock.tsx", "D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\react-stomp-hooks\\src\\mock\\reset.tsx"], "sourcesContent": ["import { createContext } from 'react';\nimport { StompSessionProviderContext } from '../interfaces/StompSessionProviderContext';\n\nconst StompContext = createContext<StompSessionProviderContext | undefined>(\n  undefined\n);\n\nexport default StompContext;\n", "import React, { useEffect, useRef, useState } from 'react';\nimport StompContext from '../context/StompContext';\nimport SockJS from 'sockjs-client';\nimport {\n  Client,\n  IStompSocket,\n  messageCallbackType,\n  StompHeaders\n} from '@stomp/stompjs';\nimport { StompSessionProviderProps } from '../interfaces/StompSessionProviderProps';\nimport { StompSessionSubscription } from '../interfaces/StompSessionSubscription';\n\n/**\n * The StompSessionProvider manages the STOMP connection\n * All Hooks and HOCs in this library require an ancestor of this type.\n * The URL to connect to can be specified via the url prop.\n * Depending on the Schema of the URL either Sockjs or a raw Websocket is used.\n * You can override this behavior with the brokerURL or webSocketFactory props, which will then be forwarded to @stomp/stompjs\n * Custom @stomp/stompjs options can be used as props.\n * Please consult the @stomp/stompjs documentation for more information.\n */\nfunction StompSessionProvider(props: StompSessionProviderProps) {\n  let { url, children, stompClientOptions, ...stompOptions } = props;\n\n  // Support old API\n  if (stompClientOptions) stompOptions = stompClientOptions;\n\n  const [client, setClient] = useState<Client | undefined>(undefined);\n  const subscriptionRequests = useRef(new Map());\n\n  useEffect(() => {\n    const _client = new Client(stompOptions);\n\n    if (!stompOptions.brokerURL && !stompOptions.webSocketFactory) {\n      _client.webSocketFactory = function () {\n        const parsedUrl = new URL(url, window?.location?.href);\n        if (parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:') {\n          return new SockJS(url) as IStompSocket;\n        } else if (\n          parsedUrl.protocol === 'ws:' ||\n          parsedUrl.protocol === 'wss:'\n        ) {\n          return new WebSocket(url) as IStompSocket;\n        } else throw new Error('Protocol not supported');\n      };\n    }\n\n    _client.onConnect = function (frame) {\n      if (stompOptions.onConnect) stompOptions.onConnect(frame);\n\n      subscriptionRequests.current.forEach((value) => {\n        value.subscription = _client.subscribe(\n          value.destination,\n          value.callback,\n          value.headers\n        );\n      });\n\n      setClient(_client);\n    };\n\n    _client.onWebSocketClose = function (event) {\n      if (stompOptions.onWebSocketClose) stompOptions.onWebSocketClose(event);\n\n      setClient(undefined);\n    };\n\n    if (!stompOptions.onStompError) {\n      _client.onStompError = function (frame) {\n        throw frame;\n      };\n    }\n\n    _client.activate();\n\n    return () => {\n      _client.deactivate();\n    };\n  }, [url, ...Object.values(stompOptions)]);\n\n  const subscribe = (\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {}\n  ) => {\n    const subscriptionId = Math.random().toString(36).substr(2, 9);\n    const subscriptionRequest: StompSessionSubscription = {\n      destination,\n      callback,\n      headers\n    };\n\n    subscriptionRequests.current.set(subscriptionId, subscriptionRequest);\n\n    if (client && client.connected) {\n      subscriptionRequest.subscription = client.subscribe(\n        destination,\n        callback,\n        headers\n      );\n    }\n\n    return () => {\n      const subscriptionData = subscriptionRequests.current.get(subscriptionId);\n\n      if (subscriptionData.subscription) {\n        subscriptionData.subscription.unsubscribe();\n      }\n\n      subscriptionRequests.current.delete(subscriptionId);\n    };\n  };\n\n  return (\n    <StompContext.Provider\n      value={{\n        client,\n        subscribe\n      }}\n    >\n      {children}\n    </StompContext.Provider>\n  );\n}\n\nexport default StompSessionProvider;\n", "import { useContext, useEffect, useRef } from 'react';\nimport StompContext from '../context/StompContext';\nimport { messageCallbackType, StompHeaders } from '@stomp/stompjs';\n\n/**\n *\n * @param destinations The destinations to subscribe to. Can be a string for a single destination or an array of strings for multiple.\n * @param onMessage Callback called when a message arrives for this subscription\n * @param headers Additional Headers for this subscription, consult @stomp/stompjs docs.\n */\nfunction useSubscription(\n  destinations: string | string[],\n  onMessage: messageCallbackType,\n  headers: StompHeaders = {}\n) {\n  const stompContext = useContext(StompContext);\n\n  if (stompContext === undefined)\n    throw new Error(\n      'There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs'\n    );\n\n  const callbackRef = useRef<messageCallbackType>(onMessage);\n  const _destinations = Array.isArray(destinations)\n    ? destinations\n    : [destinations];\n\n  callbackRef.current = onMessage;\n\n  useEffect(() => {\n    const cleanUpFunctions: (() => void)[] = [];\n\n    _destinations.forEach((_destination) =>\n      cleanUpFunctions.push(\n        stompContext.subscribe(\n          _destination,\n          (message) => {\n            callbackRef.current(message);\n          },\n          headers\n        )\n      )\n    );\n\n    return () => {\n      cleanUpFunctions.forEach((_cleanUpFunction) => {\n        _cleanUpFunction();\n      });\n    };\n  }, [\n    Object.values(_destinations).toString(),\n    Object.values(headers).toString()\n  ]);\n}\n\nexport default useSubscription;\n", "import { useContext } from 'react';\nimport StompContext from '../context/StompContext';\nimport { StompSessionProviderContext } from '../interfaces/StompSessionProviderContext';\n\n/**\n * Returns the Stomp Client from @stomp/stompjs\n * This will be undefined if the client is currently not connected\n */\nfunction useStompClient() {\n  const context = useContext<StompSessionProviderContext | undefined>(\n    StompContext\n  );\n\n  if (context === undefined)\n    throw new Error(\n      'There must be a StompSessionProvider as Ancestor of all Stomp Hooks and HOCs'\n    );\n\n  return context.client;\n}\n\nexport default useStompClient;\n", "import React from 'react';\nimport useStompClient from '../hooks/useStompClient';\n\nfunction withStompClient<P>(WrappedComponent: React.ComponentType<P>) {\n  return (props: P) => {\n    const stompClient = useStompClient();\n    return <WrappedComponent stompClient={stompClient} {...props} />;\n  };\n}\n\nexport default withStompClient;\n", "import React, { useRef } from 'react';\nimport useSubscription from '../hooks/useSubscription';\nimport { StompHeaders } from '@stomp/stompjs';\nimport {\n  MessageReceiverInterface,\n  StompMessageReceiver\n} from '../interfaces/StompMessageReceiver';\nimport { IMessage } from '@stomp/stompjs/esm6/i-message';\n\nfunction withSubscription<P>(\n  WrappedComponent: StompMessageReceiver<P>,\n  destinations: string | string[],\n  headers: StompHeaders = {}\n) {\n  return (props: P) => {\n    const ref = useRef<MessageReceiverInterface>();\n    useSubscription(\n      destinations,\n      (message: IMessage) => {\n        if (ref.current) ref.current.onMessage(message);\n      },\n      headers\n    );\n\n    // @ts-ignore\n    return <WrappedComponent ref={ref} {...props} />;\n  };\n}\n\nexport default withSubscription;\n", "import { IMessage } from '@stomp/stompjs/src/i-message';\nimport { messageCallbackType, StompHeaders } from '@stomp/stompjs';\n\nexport const subscriptions = new Map<string, Map<string, Function>>();\n\nexport function subscribeMock(\n  destination: string,\n  callback: messageCallbackType,\n  // @ts-ignore\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  headers: StompHeaders = {}\n) {\n  const subscriptionId = Math.random().toString(36).substr(2, 9);\n\n  if (!subscriptions.has(destination)) {\n    subscriptions.set(destination, new Map<string, Function>());\n  }\n\n  // @ts-ignore\n  subscriptions.get(destination).set(subscriptionId, callback);\n\n  return () => {\n    // @ts-ignore\n    subscriptions.get(destination).delete(subscriptionId);\n  };\n}\n\n/**\n * Simulates receiving a message from the server to the specified destination\n * @param destination The topic to send the message to\n * @param message The message to send\n */\nexport function mockReceiveMessage(\n  destination: string,\n  message: IMessage\n): void {\n  if (subscriptions.has(destination)) {\n    // @ts-ignore\n    subscriptions.get(destination).forEach((callback: Function) => {\n      callback(message);\n    });\n  }\n}\n\n/**\n * Gets the current subscriptions for the specified destination\n * @param destination The topic to get the subscriptions for, or undefined to get all subscriptions\n */\nexport function getMockSubscriptions(destination?: string) {\n  if (destination) {\n    return subscriptions.get(destination);\n  }\n  return subscriptions;\n}\n", "import { IPublishParams } from '@stomp/stompjs';\n\nexport const messages = new Map<string, Array<IPublishParams>>();\n\n/**\n * A mock implementation of the publish function of the @stomp/stompjs client.\n * Will store the messages in a map, keyed by the destination.\n * @param params\n */\nexport function mockClientPublish(params: IPublishParams) {\n  if (!messages.has(params.destination)) {\n    messages.set(params.destination, []);\n  }\n\n  // @ts-ignore\n  messages.get(params.destination).push(params);\n}\n\n/**\n * Gets a default Mock of the @stomp/stompjs client.\n * If you require a custom client, you can use this as a base.\n */\nexport function getMockClient() {\n  return {\n    publish: mockClientPublish\n  };\n}\n\n/**\n * Gets all messages which have been sent via a mock client.\n * @param destination The destination to get messages for, or undefined to get all messages.\n */\nexport function getSentMockMessages(destination?: string) {\n  if (destination) {\n    return messages.get(destination);\n  }\n  return messages;\n}\n", "import React from 'react';\nimport StompContext from '../context/StompContext';\nimport { subscribeMock } from './subscriptions';\nimport { getMockClient } from './client';\n\n/**\n * A mock StompSessionProvider.\n * Messages send via this mock implementation can be received via the getSentMockMessages method.\n * Subscriptions can be received via the getMockSubscriptions method.\n * The sendMockMessage method can be used, to simulate receiving a message from the server.\n *\n * @param props.client Optional. Can be used to provide a custom mock of the sompjs client,\n * in case you require additional properties/functions to be present. getMockClient can be used as a base.\n * @constructor\n */\nexport default function StompSessionProviderMock(props: {\n  children: React.ReactNode;\n  client?: any;\n}) {\n  return (\n    <StompContext.Provider\n      value={{\n        subscribe: subscribeMock,\n        // @ts-ignore\n        client: props.client ?? getMockClient()\n      }}\n    >\n      {props.children}\n    </StompContext.Provider>\n  );\n}\n", "import { subscriptions } from './subscriptions';\nimport { messages } from './client';\n\n/**\n * Resets the state of the mock implementation, clearing all subscriptions and messages.\n */\nexport function reset() {\n  subscriptions.clear();\n  messages.clear();\n}\n"]}, "metadata": {}, "sourceType": "module"}