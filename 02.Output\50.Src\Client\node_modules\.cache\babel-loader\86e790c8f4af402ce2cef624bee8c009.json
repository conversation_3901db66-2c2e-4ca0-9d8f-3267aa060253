{"ast": null, "code": "var arrayFilter = require('./_arrayFilter'),\n    stubArray = require('./stubArray');\n/** Used for built-in method references. */\n\n\nvar objectProto = Object.prototype;\n/** Built-in value references. */\n\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n/* Built-in method references for those with the same name as other `lodash` methods. */\n\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\n\nvar getSymbols = !nativeGetSymbols ? stubArray : function (object) {\n  if (object == null) {\n    return [];\n  }\n\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function (symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\nmodule.exports = getSymbols;", "map": {"version": 3, "names": ["arrayFilter", "require", "stubArray", "objectProto", "Object", "prototype", "propertyIsEnumerable", "nativeGetSymbols", "getOwnPropertySymbols", "getSymbols", "object", "symbol", "call", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_getSymbols.js"], "sourcesContent": ["var arrayFilter = require('./_arrayFilter'),\n    stubArray = require('./stubArray');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function(symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\n\nmodule.exports = getSymbols;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAD,CAAzB;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,aAAD,CADvB;AAGA;;;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACC,SAAzB;AAEA;;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACG,oBAAvC;AAEA;;AACA,IAAIC,gBAAgB,GAAGH,MAAM,CAACI,qBAA9B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,UAAU,GAAG,CAACF,gBAAD,GAAoBL,SAApB,GAAgC,UAASQ,MAAT,EAAiB;EAChE,IAAIA,MAAM,IAAI,IAAd,EAAoB;IAClB,OAAO,EAAP;EACD;;EACDA,MAAM,GAAGN,MAAM,CAACM,MAAD,CAAf;EACA,OAAOV,WAAW,CAACO,gBAAgB,CAACG,MAAD,CAAjB,EAA2B,UAASC,MAAT,EAAiB;IAC5D,OAAOL,oBAAoB,CAACM,IAArB,CAA0BF,MAA1B,EAAkCC,MAAlC,CAAP;EACD,CAFiB,CAAlB;AAGD,CARD;AAUAE,MAAM,CAACC,OAAP,GAAiBL,UAAjB"}, "metadata": {}, "sourceType": "script"}