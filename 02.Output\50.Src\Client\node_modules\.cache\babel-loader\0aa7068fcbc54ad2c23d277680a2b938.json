{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nexport var fieldAdapter = function fieldAdapter(field) {\n  return {\n    getFieldInstance: function getFieldInstance(name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function validateFields(fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function resultAdapter(result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function onSort(dataIndex, order) {\n      var _a;\n\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function onFilter(filterParams) {\n      var _a;\n\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "fieldAdapter", "field", "getFieldInstance", "name", "getNames", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON>", "getFieldsValue", "getV<PERSON>ues", "resetFields", "resetToDefault", "validateFields", "fields", "callback", "validate", "resultAdapter", "result", "tableProps", "dataSource", "loading", "onSort", "dataIndex", "order", "_a", "onChange", "current", "pagination", "pageSize", "params", "filters", "onFilter", "filterParams", "sorter", "paginationProps", "changeCurrent", "onPageSizeChange", "changePageSize", "total"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useFusionTable/fusionAdapter.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nexport var fieldAdapter = function fieldAdapter(field) {\n  return {\n    getFieldInstance: function getFieldInstance(name) {\n      return field.getNames().includes(name);\n    },\n    setFieldsValue: field.setValues,\n    getFieldsValue: field.getValues,\n    resetFields: field.resetToDefault,\n    validateFields: function validateFields(fields, callback) {\n      field.validate(fields, callback);\n    }\n  };\n};\nexport var resultAdapter = function resultAdapter(result) {\n  var tableProps = {\n    dataSource: result.tableProps.dataSource,\n    loading: result.tableProps.loading,\n    onSort: function onSort(dataIndex, order) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {\n        field: dataIndex,\n        order: order\n      });\n    },\n    onFilter: function onFilter(filterParams) {\n      var _a;\n      result.tableProps.onChange({\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize\n      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);\n    }\n  };\n  var paginationProps = {\n    onChange: result.pagination.changeCurrent,\n    onPageSizeChange: result.pagination.changePageSize,\n    current: result.pagination.current,\n    pageSize: result.pagination.pageSize,\n    total: result.pagination.total\n  };\n  return __assign(__assign({}, result), {\n    tableProps: tableProps,\n    paginationProps: paginationProps\n  });\n};"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,OAAO,IAAIO,YAAY,GAAG,SAASA,YAAT,CAAsBC,KAAtB,EAA6B;EACrD,OAAO;IACLC,gBAAgB,EAAE,SAASA,gBAAT,CAA0BC,IAA1B,EAAgC;MAChD,OAAOF,KAAK,CAACG,QAAN,GAAiBC,QAAjB,CAA0BF,IAA1B,CAAP;IACD,CAHI;IAILG,cAAc,EAAEL,KAAK,CAACM,SAJjB;IAKLC,cAAc,EAAEP,KAAK,CAACQ,SALjB;IAMLC,WAAW,EAAET,KAAK,CAACU,cANd;IAOLC,cAAc,EAAE,SAASA,cAAT,CAAwBC,MAAxB,EAAgCC,QAAhC,EAA0C;MACxDb,KAAK,CAACc,QAAN,CAAeF,MAAf,EAAuBC,QAAvB;IACD;EATI,CAAP;AAWD,CAZM;AAaP,OAAO,IAAIE,aAAa,GAAG,SAASA,aAAT,CAAuBC,MAAvB,EAA+B;EACxD,IAAIC,UAAU,GAAG;IACfC,UAAU,EAAEF,MAAM,CAACC,UAAP,CAAkBC,UADf;IAEfC,OAAO,EAAEH,MAAM,CAACC,UAAP,CAAkBE,OAFZ;IAGfC,MAAM,EAAE,SAASA,MAAT,CAAgBC,SAAhB,EAA2BC,KAA3B,EAAkC;MACxC,IAAIC,EAAJ;;MACAP,MAAM,CAACC,UAAP,CAAkBO,QAAlB,CAA2B;QACzBC,OAAO,EAAET,MAAM,CAACU,UAAP,CAAkBD,OADF;QAEzBE,QAAQ,EAAEX,MAAM,CAACU,UAAP,CAAkBC;MAFH,CAA3B,EAGG,CAACJ,EAAE,GAAGP,MAAM,CAACY,MAAP,CAAc,CAAd,CAAN,MAA4B,IAA5B,IAAoCL,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACM,OAHnE,EAG4E;QAC1E7B,KAAK,EAAEqB,SADmE;QAE1EC,KAAK,EAAEA;MAFmE,CAH5E;IAOD,CAZc;IAafQ,QAAQ,EAAE,SAASA,QAAT,CAAkBC,YAAlB,EAAgC;MACxC,IAAIR,EAAJ;;MACAP,MAAM,CAACC,UAAP,CAAkBO,QAAlB,CAA2B;QACzBC,OAAO,EAAET,MAAM,CAACU,UAAP,CAAkBD,OADF;QAEzBE,QAAQ,EAAEX,MAAM,CAACU,UAAP,CAAkBC;MAFH,CAA3B,EAGGI,YAHH,EAGiB,CAACR,EAAE,GAAGP,MAAM,CAACY,MAAP,CAAc,CAAd,CAAN,MAA4B,IAA5B,IAAoCL,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACS,MAHjF;IAID;EAnBc,CAAjB;EAqBA,IAAIC,eAAe,GAAG;IACpBT,QAAQ,EAAER,MAAM,CAACU,UAAP,CAAkBQ,aADR;IAEpBC,gBAAgB,EAAEnB,MAAM,CAACU,UAAP,CAAkBU,cAFhB;IAGpBX,OAAO,EAAET,MAAM,CAACU,UAAP,CAAkBD,OAHP;IAIpBE,QAAQ,EAAEX,MAAM,CAACU,UAAP,CAAkBC,QAJR;IAKpBU,KAAK,EAAErB,MAAM,CAACU,UAAP,CAAkBW;EALL,CAAtB;EAOA,OAAOpD,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAK+B,MAAL,CAAT,EAAuB;IACpCC,UAAU,EAAEA,UADwB;IAEpCgB,eAAe,EAAEA;EAFmB,CAAvB,CAAf;AAID,CAjCM"}, "metadata": {}, "sourceType": "module"}