{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\n\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function shouldUpdate() {\n      return true;\n    };\n  }\n\n  var _a = __read(useRafState(), 2),\n      position = _a[0],\n      setPosition = _a[1];\n\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n\n    if (!el) {\n      return;\n    }\n\n    var updatePosition = function updatePosition() {\n      var newPosition;\n\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\n\nexport default useScroll;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "useRafState", "useLatest", "getTargetElement", "useEffectWithTarget", "useScroll", "target", "shouldUpdate", "_a", "position", "setPosition", "shouldUpdateRef", "el", "document", "updatePosition", "newPosition", "scrollingElement", "left", "scrollLeft", "top", "scrollTop", "Math", "max", "window", "pageXOffset", "documentElement", "body", "pageYOffset", "current", "addEventListener", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useScroll/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function shouldUpdate() {\n      return true;\n    };\n  }\n  var _a = __read(useRafState(), 2),\n    position = _a[0],\n    setPosition = _a[1];\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var updatePosition = function updatePosition() {\n      var newPosition;\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\nexport default useScroll;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,OAAOO,WAAP,MAAwB,gBAAxB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;;AACA,SAASC,SAAT,CAAmBC,MAAnB,EAA2BC,YAA3B,EAAyC;EACvC,IAAIA,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAG,SAASA,YAAT,GAAwB;MACrC,OAAO,IAAP;IACD,CAFD;EAGD;;EACD,IAAIC,EAAE,GAAGvB,MAAM,CAACgB,WAAW,EAAZ,EAAgB,CAAhB,CAAf;EAAA,IACEQ,QAAQ,GAAGD,EAAE,CAAC,CAAD,CADf;EAAA,IAEEE,WAAW,GAAGF,EAAE,CAAC,CAAD,CAFlB;;EAGA,IAAIG,eAAe,GAAGT,SAAS,CAACK,YAAD,CAA/B;EACAH,mBAAmB,CAAC,YAAY;IAC9B,IAAIQ,EAAE,GAAGT,gBAAgB,CAACG,MAAD,EAASO,QAAT,CAAzB;;IACA,IAAI,CAACD,EAAL,EAAS;MACP;IACD;;IACD,IAAIE,cAAc,GAAG,SAASA,cAAT,GAA0B;MAC7C,IAAIC,WAAJ;;MACA,IAAIH,EAAE,KAAKC,QAAX,EAAqB;QACnB,IAAIA,QAAQ,CAACG,gBAAb,EAA+B;UAC7BD,WAAW,GAAG;YACZE,IAAI,EAAEJ,QAAQ,CAACG,gBAAT,CAA0BE,UADpB;YAEZC,GAAG,EAAEN,QAAQ,CAACG,gBAAT,CAA0BI;UAFnB,CAAd;QAID,CALD,MAKO;UACL;UACA;UACA;UACAL,WAAW,GAAG;YACZE,IAAI,EAAEI,IAAI,CAACC,GAAL,CAASC,MAAM,CAACC,WAAhB,EAA6BX,QAAQ,CAACY,eAAT,CAAyBP,UAAtD,EAAkEL,QAAQ,CAACa,IAAT,CAAcR,UAAhF,CADM;YAEZC,GAAG,EAAEE,IAAI,CAACC,GAAL,CAASC,MAAM,CAACI,WAAhB,EAA6Bd,QAAQ,CAACY,eAAT,CAAyBL,SAAtD,EAAiEP,QAAQ,CAACa,IAAT,CAAcN,SAA/E;UAFO,CAAd;QAID;MACF,CAfD,MAeO;QACLL,WAAW,GAAG;UACZE,IAAI,EAAEL,EAAE,CAACM,UADG;UAEZC,GAAG,EAAEP,EAAE,CAACQ;QAFI,CAAd;MAID;;MACD,IAAIT,eAAe,CAACiB,OAAhB,CAAwBb,WAAxB,CAAJ,EAA0C;QACxCL,WAAW,CAACK,WAAD,CAAX;MACD;IACF,CA1BD;;IA2BAD,cAAc;IACdF,EAAE,CAACiB,gBAAH,CAAoB,QAApB,EAA8Bf,cAA9B;IACA,OAAO,YAAY;MACjBF,EAAE,CAACkB,mBAAH,CAAuB,QAAvB,EAAiChB,cAAjC;IACD,CAFD;EAGD,CArCkB,EAqChB,EArCgB,EAqCZR,MArCY,CAAnB;EAsCA,OAAOG,QAAP;AACD;;AACD,eAAeJ,SAAf"}, "metadata": {}, "sourceType": "module"}