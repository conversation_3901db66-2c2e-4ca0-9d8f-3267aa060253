{"ast": null, "code": "'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\n\nEvent.prototype.initEvent = function (eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\n\nEvent.prototype.stopPropagation = function () {};\n\nEvent.prototype.preventDefault = function () {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\nmodule.exports = Event;", "map": {"version": 3, "names": ["Event", "eventType", "type", "prototype", "initEvent", "canBubble", "cancelable", "bubbles", "timeStamp", "Date", "stopPropagation", "preventDefault", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/event/event.js"], "sourcesContent": ["'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\n\nmodule.exports = Event;\n"], "mappings": "AAAA;;AAEA,SAASA,KAAT,CAAeC,SAAf,EAA0B;EACxB,KAAKC,IAAL,GAAYD,SAAZ;AACD;;AAEDD,KAAK,CAACG,SAAN,CAAgBC,SAAhB,GAA4B,UAASH,SAAT,EAAoBI,SAApB,EAA+BC,UAA/B,EAA2C;EACrE,KAAKJ,IAAL,GAAYD,SAAZ;EACA,KAAKM,OAAL,GAAeF,SAAf;EACA,KAAKC,UAAL,GAAkBA,UAAlB;EACA,KAAKE,SAAL,GAAiB,CAAC,IAAIC,IAAJ,EAAlB;EACA,OAAO,IAAP;AACD,CAND;;AAQAT,KAAK,CAACG,SAAN,CAAgBO,eAAhB,GAAkC,YAAW,CAAE,CAA/C;;AACAV,KAAK,CAACG,SAAN,CAAgBQ,cAAhB,GAAiC,YAAW,CAAE,CAA9C;;AAEAX,KAAK,CAACY,eAAN,GAAwB,CAAxB;AACAZ,KAAK,CAACa,SAAN,GAAkB,CAAlB;AACAb,KAAK,CAACc,cAAN,GAAuB,CAAvB;AAEAC,MAAM,CAACC,OAAP,GAAiBhB,KAAjB"}, "metadata": {}, "sourceType": "script"}