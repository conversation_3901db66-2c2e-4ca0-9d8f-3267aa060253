{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    Event = require('./event');\n\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\n\ninherits(CloseEvent, Event);\nmodule.exports = CloseEvent;", "map": {"version": 3, "names": ["inherits", "require", "Event", "CloseEvent", "call", "initEvent", "<PERSON><PERSON><PERSON>", "code", "reason", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/event/close.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\n\ninherits(CloseEvent, Event);\n\nmodule.exports = CloseEvent;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,KAAK,GAAGD,OAAO,CAAC,SAAD,CADnB;;AAIA,SAASE,UAAT,GAAsB;EACpBD,KAAK,CAACE,IAAN,CAAW,IAAX;EACA,KAAKC,SAAL,CAAe,OAAf,EAAwB,KAAxB,EAA+B,KAA/B;EACA,KAAKC,QAAL,GAAgB,KAAhB;EACA,KAAKC,IAAL,GAAY,CAAZ;EACA,KAAKC,MAAL,GAAc,EAAd;AACD;;AAEDR,QAAQ,CAACG,UAAD,EAAaD,KAAb,CAAR;AAEAO,MAAM,CAACC,OAAP,GAAiBP,UAAjB"}, "metadata": {}, "sourceType": "script"}