{"ast": null, "code": "/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n  'use strict';\n\n  var document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n  var isCommonjs = typeof module !== 'undefined' && module.exports;\n\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'], // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'], // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n\n    for (; i < l; i++) {\n      val = fnMap[i];\n\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n\n        return ret;\n      }\n    }\n\n    return false;\n  }();\n\n  var eventNameMap = {\n    change: fn.fullscreenchange,\n    error: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function request(element, options) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function () {\n          this.off('change', onFullScreenEntered);\n          resolve();\n        }.bind(this);\n\n        this.on('change', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen](options);\n\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      }.bind(this));\n    },\n    exit: function exit() {\n      return new Promise(function (resolve, reject) {\n        if (!this.isFullscreen) {\n          resolve();\n          return;\n        }\n\n        var onFullScreenExit = function () {\n          this.off('change', onFullScreenExit);\n          resolve();\n        }.bind(this);\n\n        this.on('change', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      }.bind(this));\n    },\n    toggle: function toggle(element, options) {\n      return this.isFullscreen ? this.exit() : this.request(element, options);\n    },\n    onchange: function onchange(callback) {\n      this.on('change', callback);\n    },\n    onerror: function onerror(callback) {\n      this.on('error', callback);\n    },\n    on: function on(event, callback) {\n      var eventName = eventNameMap[event];\n\n      if (eventName) {\n        document.addEventListener(eventName, callback, false);\n      }\n    },\n    off: function off(event, callback) {\n      var eventName = eventNameMap[event];\n\n      if (eventName) {\n        document.removeEventListener(eventName, callback, false);\n      }\n    },\n    raw: fn\n  };\n\n  if (!fn) {\n    if (isCommonjs) {\n      module.exports = {\n        isEnabled: false\n      };\n    } else {\n      window.screenfull = {\n        isEnabled: false\n      };\n    }\n\n    return;\n  }\n\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function get() {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function get() {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function get() {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n\n  if (isCommonjs) {\n    module.exports = screenfull;\n  } else {\n    window.screenfull = screenfull;\n  }\n})();", "map": {"version": 3, "names": ["document", "window", "is<PERSON><PERSON><PERSON>j<PERSON>", "module", "exports", "fn", "val", "fnMap", "i", "l", "length", "ret", "eventNameMap", "change", "fullscreenchange", "error", "fullscreenerror", "screenfull", "request", "element", "options", "Promise", "resolve", "reject", "onFullScreenEntered", "off", "bind", "on", "documentElement", "returnPromise", "requestFullscreen", "then", "catch", "exit", "isFullscreen", "onFullScreenExit", "exitFullscreen", "toggle", "onchange", "callback", "onerror", "event", "eventName", "addEventListener", "removeEventListener", "raw", "isEnabled", "Object", "defineProperties", "get", "Boolean", "fullscreenElement", "enumerable", "fullscreenEnabled"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/screenfull/dist/screenfull.js"], "sourcesContent": ["/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n\t'use strict';\n\n\tvar document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n\tvar isCommonjs = typeof module !== 'undefined' && module.exports;\n\n\tvar fn = (function () {\n\t\tvar val;\n\n\t\tvar fnMap = [\n\t\t\t[\n\t\t\t\t'requestFullscreen',\n\t\t\t\t'exitFullscreen',\n\t\t\t\t'fullscreenElement',\n\t\t\t\t'fullscreenEnabled',\n\t\t\t\t'fullscreenchange',\n\t\t\t\t'fullscreenerror'\n\t\t\t],\n\t\t\t// New WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullscreen',\n\t\t\t\t'webkitExitFullscreen',\n\t\t\t\t'webkitFullscreenElement',\n\t\t\t\t'webkitFullscreenEnabled',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t// Old WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullScreen',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitCurrentFullScreenElement',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t[\n\t\t\t\t'mozRequestFullScreen',\n\t\t\t\t'mozCancelFullScreen',\n\t\t\t\t'mozFullScreenElement',\n\t\t\t\t'mozFullScreenEnabled',\n\t\t\t\t'mozfullscreenchange',\n\t\t\t\t'mozfullscreenerror'\n\t\t\t],\n\t\t\t[\n\t\t\t\t'msRequestFullscreen',\n\t\t\t\t'msExitFullscreen',\n\t\t\t\t'msFullscreenElement',\n\t\t\t\t'msFullscreenEnabled',\n\t\t\t\t'MSFullscreenChange',\n\t\t\t\t'MSFullscreenError'\n\t\t\t]\n\t\t];\n\n\t\tvar i = 0;\n\t\tvar l = fnMap.length;\n\t\tvar ret = {};\n\n\t\tfor (; i < l; i++) {\n\t\t\tval = fnMap[i];\n\t\t\tif (val && val[1] in document) {\n\t\t\t\tfor (i = 0; i < val.length; i++) {\n\t\t\t\t\tret[fnMap[0][i]] = val[i];\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t})();\n\n\tvar eventNameMap = {\n\t\tchange: fn.fullscreenchange,\n\t\terror: fn.fullscreenerror\n\t};\n\n\tvar screenfull = {\n\t\trequest: function (element, options) {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tvar onFullScreenEntered = function () {\n\t\t\t\t\tthis.off('change', onFullScreenEntered);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenEntered);\n\n\t\t\t\telement = element || document.documentElement;\n\n\t\t\t\tvar returnPromise = element[fn.requestFullscreen](options);\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\texit: function () {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tif (!this.isFullscreen) {\n\t\t\t\t\tresolve();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar onFullScreenExit = function () {\n\t\t\t\t\tthis.off('change', onFullScreenExit);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenExit);\n\n\t\t\t\tvar returnPromise = document[fn.exitFullscreen]();\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\ttoggle: function (element, options) {\n\t\t\treturn this.isFullscreen ? this.exit() : this.request(element, options);\n\t\t},\n\t\tonchange: function (callback) {\n\t\t\tthis.on('change', callback);\n\t\t},\n\t\tonerror: function (callback) {\n\t\t\tthis.on('error', callback);\n\t\t},\n\t\ton: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\toff: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\traw: fn\n\t};\n\n\tif (!fn) {\n\t\tif (isCommonjs) {\n\t\t\tmodule.exports = {isEnabled: false};\n\t\t} else {\n\t\t\twindow.screenfull = {isEnabled: false};\n\t\t}\n\n\t\treturn;\n\t}\n\n\tObject.defineProperties(screenfull, {\n\t\tisFullscreen: {\n\t\t\tget: function () {\n\t\t\t\treturn Boolean(document[fn.fullscreenElement]);\n\t\t\t}\n\t\t},\n\t\telement: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\treturn document[fn.fullscreenElement];\n\t\t\t}\n\t\t},\n\t\tisEnabled: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\t// Coerce to boolean in case of old WebKit\n\t\t\t\treturn Boolean(document[fn.fullscreenEnabled]);\n\t\t\t}\n\t\t}\n\t});\n\n\tif (isCommonjs) {\n\t\tmodule.exports = screenfull;\n\t} else {\n\t\twindow.screenfull = screenfull;\n\t}\n})();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY;EACZ;;EAEA,IAAIA,QAAQ,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,MAAM,CAACD,QAAd,KAA2B,WAA5D,GAA0EC,MAAM,CAACD,QAAjF,GAA4F,EAA3G;EACA,IAAIE,UAAU,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,OAAzD;;EAEA,IAAIC,EAAE,GAAI,YAAY;IACrB,IAAIC,GAAJ;IAEA,IAAIC,KAAK,GAAG,CACX,CACC,mBADD,EAEC,gBAFD,EAGC,mBAHD,EAIC,mBAJD,EAKC,kBALD,EAMC,iBAND,CADW,EASX;IACA,CACC,yBADD,EAEC,sBAFD,EAGC,yBAHD,EAIC,yBAJD,EAKC,wBALD,EAMC,uBAND,CAVW,EAmBX;IACA,CACC,yBADD,EAEC,wBAFD,EAGC,gCAHD,EAIC,wBAJD,EAKC,wBALD,EAMC,uBAND,CApBW,EA6BX,CACC,sBADD,EAEC,qBAFD,EAGC,sBAHD,EAIC,sBAJD,EAKC,qBALD,EAMC,oBAND,CA7BW,EAqCX,CACC,qBADD,EAEC,kBAFD,EAGC,qBAHD,EAIC,qBAJD,EAKC,oBALD,EAMC,mBAND,CArCW,CAAZ;IA+CA,IAAIC,CAAC,GAAG,CAAR;IACA,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAd;IACA,IAAIC,GAAG,GAAG,EAAV;;IAEA,OAAOH,CAAC,GAAGC,CAAX,EAAcD,CAAC,EAAf,EAAmB;MAClBF,GAAG,GAAGC,KAAK,CAACC,CAAD,CAAX;;MACA,IAAIF,GAAG,IAAIA,GAAG,CAAC,CAAD,CAAH,IAAUN,QAArB,EAA+B;QAC9B,KAAKQ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGF,GAAG,CAACI,MAApB,EAA4BF,CAAC,EAA7B,EAAiC;UAChCG,GAAG,CAACJ,KAAK,CAAC,CAAD,CAAL,CAASC,CAAT,CAAD,CAAH,GAAmBF,GAAG,CAACE,CAAD,CAAtB;QACA;;QACD,OAAOG,GAAP;MACA;IACD;;IAED,OAAO,KAAP;EACA,CAjEQ,EAAT;;EAmEA,IAAIC,YAAY,GAAG;IAClBC,MAAM,EAAER,EAAE,CAACS,gBADO;IAElBC,KAAK,EAAEV,EAAE,CAACW;EAFQ,CAAnB;EAKA,IAAIC,UAAU,GAAG;IAChBC,OAAO,EAAE,iBAAUC,OAAV,EAAmBC,OAAnB,EAA4B;MACpC,OAAO,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;QAC7C,IAAIC,mBAAmB,GAAG,YAAY;UACrC,KAAKC,GAAL,CAAS,QAAT,EAAmBD,mBAAnB;UACAF,OAAO;QACP,CAHyB,CAGxBI,IAHwB,CAGnB,IAHmB,CAA1B;;QAKA,KAAKC,EAAL,CAAQ,QAAR,EAAkBH,mBAAlB;QAEAL,OAAO,GAAGA,OAAO,IAAInB,QAAQ,CAAC4B,eAA9B;QAEA,IAAIC,aAAa,GAAGV,OAAO,CAACd,EAAE,CAACyB,iBAAJ,CAAP,CAA8BV,OAA9B,CAApB;;QAEA,IAAIS,aAAa,YAAYR,OAA7B,EAAsC;UACrCQ,aAAa,CAACE,IAAd,CAAmBP,mBAAnB,EAAwCQ,KAAxC,CAA8CT,MAA9C;QACA;MACD,CAfkB,CAejBG,IAfiB,CAeZ,IAfY,CAAZ,CAAP;IAgBA,CAlBe;IAmBhBO,IAAI,EAAE,gBAAY;MACjB,OAAO,IAAIZ,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;QAC7C,IAAI,CAAC,KAAKW,YAAV,EAAwB;UACvBZ,OAAO;UACP;QACA;;QAED,IAAIa,gBAAgB,GAAG,YAAY;UAClC,KAAKV,GAAL,CAAS,QAAT,EAAmBU,gBAAnB;UACAb,OAAO;QACP,CAHsB,CAGrBI,IAHqB,CAGhB,IAHgB,CAAvB;;QAKA,KAAKC,EAAL,CAAQ,QAAR,EAAkBQ,gBAAlB;QAEA,IAAIN,aAAa,GAAG7B,QAAQ,CAACK,EAAE,CAAC+B,cAAJ,CAAR,EAApB;;QAEA,IAAIP,aAAa,YAAYR,OAA7B,EAAsC;UACrCQ,aAAa,CAACE,IAAd,CAAmBI,gBAAnB,EAAqCH,KAArC,CAA2CT,MAA3C;QACA;MACD,CAlBkB,CAkBjBG,IAlBiB,CAkBZ,IAlBY,CAAZ,CAAP;IAmBA,CAvCe;IAwChBW,MAAM,EAAE,gBAAUlB,OAAV,EAAmBC,OAAnB,EAA4B;MACnC,OAAO,KAAKc,YAAL,GAAoB,KAAKD,IAAL,EAApB,GAAkC,KAAKf,OAAL,CAAaC,OAAb,EAAsBC,OAAtB,CAAzC;IACA,CA1Ce;IA2ChBkB,QAAQ,EAAE,kBAAUC,QAAV,EAAoB;MAC7B,KAAKZ,EAAL,CAAQ,QAAR,EAAkBY,QAAlB;IACA,CA7Ce;IA8ChBC,OAAO,EAAE,iBAAUD,QAAV,EAAoB;MAC5B,KAAKZ,EAAL,CAAQ,OAAR,EAAiBY,QAAjB;IACA,CAhDe;IAiDhBZ,EAAE,EAAE,YAAUc,KAAV,EAAiBF,QAAjB,EAA2B;MAC9B,IAAIG,SAAS,GAAG9B,YAAY,CAAC6B,KAAD,CAA5B;;MACA,IAAIC,SAAJ,EAAe;QACd1C,QAAQ,CAAC2C,gBAAT,CAA0BD,SAA1B,EAAqCH,QAArC,EAA+C,KAA/C;MACA;IACD,CAtDe;IAuDhBd,GAAG,EAAE,aAAUgB,KAAV,EAAiBF,QAAjB,EAA2B;MAC/B,IAAIG,SAAS,GAAG9B,YAAY,CAAC6B,KAAD,CAA5B;;MACA,IAAIC,SAAJ,EAAe;QACd1C,QAAQ,CAAC4C,mBAAT,CAA6BF,SAA7B,EAAwCH,QAAxC,EAAkD,KAAlD;MACA;IACD,CA5De;IA6DhBM,GAAG,EAAExC;EA7DW,CAAjB;;EAgEA,IAAI,CAACA,EAAL,EAAS;IACR,IAAIH,UAAJ,EAAgB;MACfC,MAAM,CAACC,OAAP,GAAiB;QAAC0C,SAAS,EAAE;MAAZ,CAAjB;IACA,CAFD,MAEO;MACN7C,MAAM,CAACgB,UAAP,GAAoB;QAAC6B,SAAS,EAAE;MAAZ,CAApB;IACA;;IAED;EACA;;EAEDC,MAAM,CAACC,gBAAP,CAAwB/B,UAAxB,EAAoC;IACnCiB,YAAY,EAAE;MACbe,GAAG,EAAE,eAAY;QAChB,OAAOC,OAAO,CAAClD,QAAQ,CAACK,EAAE,CAAC8C,iBAAJ,CAAT,CAAd;MACA;IAHY,CADqB;IAMnChC,OAAO,EAAE;MACRiC,UAAU,EAAE,IADJ;MAERH,GAAG,EAAE,eAAY;QAChB,OAAOjD,QAAQ,CAACK,EAAE,CAAC8C,iBAAJ,CAAf;MACA;IAJO,CAN0B;IAYnCL,SAAS,EAAE;MACVM,UAAU,EAAE,IADF;MAEVH,GAAG,EAAE,eAAY;QAChB;QACA,OAAOC,OAAO,CAAClD,QAAQ,CAACK,EAAE,CAACgD,iBAAJ,CAAT,CAAd;MACA;IALS;EAZwB,CAApC;;EAqBA,IAAInD,UAAJ,EAAgB;IACfC,MAAM,CAACC,OAAP,GAAiBa,UAAjB;EACA,CAFD,MAEO;IACNhB,MAAM,CAACgB,UAAP,GAAoBA,UAApB;EACA;AACD,CAlLD"}, "metadata": {}, "sourceType": "script"}