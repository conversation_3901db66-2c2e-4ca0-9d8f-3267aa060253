{"ast": null, "code": "var _jsxFileName = \"D:\\\\11.Working\\\\20.AutoControl\\\\02.Output\\\\50.Src\\\\Client\\\\src\\\\components\\\\elements\\\\Cell.js\";\nimport React from 'react';\nimport { getHexColor, getTextOrDisplayColor, replaceWithHtmlSpace } from '../../utils/Util.js';\nimport PropTypes from 'prop-types';\nimport ScaleBlock from './ScaleBlock';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst propTypes = {\n  scale: PropTypes.bool,\n  unit: PropTypes.string,\n  unitStyle: PropTypes.object,\n  className: PropTypes.string,\n  display_text: PropTypes.string,\n  text: PropTypes.string,\n  text_color: PropTypes.string,\n  background_color: PropTypes.string\n};\n/**\r\n * 色/背景色/文字列の情報を従い、表示する\r\n * @module Cell\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\n\nconst Cell = props => {\n  const style = {};\n\n  if (props.text_color) {\n    style.color = getTextOrDisplayColor(props);\n  }\n\n  if (props.background_color) {\n    style.backgroundColor = getHexColor(props.background_color);\n  }\n\n  let className = props.className || '';\n  let refinedText = replaceWithHtmlSpace((props === null || props === void 0 ? void 0 : props.display_text) || (props === null || props === void 0 ? void 0 : props.text));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    style: style,\n    children: [props.scale && /*#__PURE__*/_jsxDEV(ScaleBlock, { ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 23\n    }, this), !props.scale && /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: refinedText\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 9\n    }, this), // Scaleの計算は、Unitの文字を含めてない。画面Layoutによって、このUnitを含めて、ロジックを見直す必要\n    props.unit && /*#__PURE__*/_jsxDEV(\"span\", {\n      style: props.unitStyle,\n      children: props.unit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 23\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n\n_c = Cell;\nCell.propTypes = propTypes;\nexport default Cell;\n\nvar _c;\n\n$RefreshReg$(_c, \"Cell\");", "map": {"version": 3, "names": ["React", "getHexColor", "getTextOrDisplayColor", "replaceWithHtmlSpace", "PropTypes", "ScaleBlock", "propTypes", "scale", "bool", "unit", "string", "unitStyle", "object", "className", "display_text", "text", "text_color", "background_color", "Cell", "props", "style", "color", "backgroundColor", "refinedText", "__html"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/elements/Cell.js"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n  getHexColor,\r\n  getTextOrDisplayColor,\r\n  replaceWithHtmlSpace,\r\n} from '../../utils/Util.js';\r\nimport PropTypes from 'prop-types';\r\nimport ScaleBlock from './ScaleBlock';\r\n\r\nconst propTypes = {\r\n  scale: PropTypes.bool,\r\n  unit: PropTypes.string,\r\n  unitStyle: PropTypes.object,\r\n  className: PropTypes.string,\r\n  display_text: PropTypes.string,\r\n  text: PropTypes.string,\r\n  text_color: PropTypes.string,\r\n  background_color: PropTypes.string,\r\n};\r\n\r\n/**\r\n * 色/背景色/文字列の情報を従い、表示する\r\n * @module Cell\r\n * @component\r\n * @param {propTypes} props\r\n * @returns 表示内容\r\n */\r\nconst Cell = (props) => {\r\n  const style = {};\r\n  if (props.text_color) {\r\n    style.color = getTextOrDisplayColor(props);\r\n  }\r\n\r\n  if (props.background_color) {\r\n    style.backgroundColor = getHexColor(props.background_color);\r\n  }\r\n\r\n  let className = props.className || '';\r\n  let refinedText = replaceWithHtmlSpace(props?.display_text || props?.text);\r\n\r\n  return (\r\n    <div className={className} style={style}>\r\n      {props.scale && <ScaleBlock {...props}></ScaleBlock>}\r\n      {!props.scale && (\r\n        <span dangerouslySetInnerHTML={{ __html: refinedText }}></span>\r\n      )}\r\n      {\r\n        // Scaleの計算は、Unitの文字を含めてない。画面Layoutによって、このUnitを含めて、ロジックを見直す必要\r\n        props.unit && <span style={props.unitStyle}>{props.unit}</span>\r\n      }\r\n    </div>\r\n  );\r\n};\r\n\r\nCell.propTypes = propTypes;\r\nexport default Cell;\r\n"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,SACEC,WADF,EAEEC,qBAFF,EAGEC,oBAHF,QAIO,qBAJP;AAKA,OAAOC,SAAP,MAAsB,YAAtB;AACA,OAAOC,UAAP,MAAuB,cAAvB;;AAEA,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAEH,SAAS,CAACI,IADD;EAEhBC,IAAI,EAAEL,SAAS,CAACM,MAFA;EAGhBC,SAAS,EAAEP,SAAS,CAACQ,MAHL;EAIhBC,SAAS,EAAET,SAAS,CAACM,MAJL;EAKhBI,YAAY,EAAEV,SAAS,CAACM,MALR;EAMhBK,IAAI,EAAEX,SAAS,CAACM,MANA;EAOhBM,UAAU,EAAEZ,SAAS,CAACM,MAPN;EAQhBO,gBAAgB,EAAEb,SAAS,CAACM;AARZ,CAAlB;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMQ,IAAI,GAAIC,KAAD,IAAW;EACtB,MAAMC,KAAK,GAAG,EAAd;;EACA,IAAID,KAAK,CAACH,UAAV,EAAsB;IACpBI,KAAK,CAACC,KAAN,GAAcnB,qBAAqB,CAACiB,KAAD,CAAnC;EACD;;EAED,IAAIA,KAAK,CAACF,gBAAV,EAA4B;IAC1BG,KAAK,CAACE,eAAN,GAAwBrB,WAAW,CAACkB,KAAK,CAACF,gBAAP,CAAnC;EACD;;EAED,IAAIJ,SAAS,GAAGM,KAAK,CAACN,SAAN,IAAmB,EAAnC;EACA,IAAIU,WAAW,GAAGpB,oBAAoB,CAAC,CAAAgB,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAEL,YAAP,MAAuBK,KAAvB,aAAuBA,KAAvB,uBAAuBA,KAAK,CAAEJ,IAA9B,CAAD,CAAtC;EAEA,oBACE;IAAK,SAAS,EAAEF,SAAhB;IAA2B,KAAK,EAAEO,KAAlC;IAAA,WACGD,KAAK,CAACZ,KAAN,iBAAe,QAAC,UAAD,OAAgBY;IAAhB;MAAA;MAAA;MAAA;IAAA,QADlB,EAEG,CAACA,KAAK,CAACZ,KAAP,iBACC;MAAM,uBAAuB,EAAE;QAAEiB,MAAM,EAAED;MAAV;IAA/B;MAAA;MAAA;MAAA;IAAA,QAHJ,EAMI;IACAJ,KAAK,CAACV,IAAN,iBAAc;MAAM,KAAK,EAAEU,KAAK,CAACR,SAAnB;MAAA,UAA+BQ,KAAK,CAACV;IAArC;MAAA;MAAA;MAAA;IAAA,QAPlB;EAAA;IAAA;IAAA;IAAA;EAAA,QADF;AAYD,CAzBD;;KAAMS,I;AA2BNA,IAAI,CAACZ,SAAL,GAAiBA,SAAjB;AACA,eAAeY,IAAf"}, "metadata": {}, "sourceType": "module"}