{"ast": null, "code": "var baseIsTypedArray = require('./_baseIsTypedArray'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n/* Node.js helper references. */\n\n\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\n\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\nmodule.exports = isTypedArray;", "map": {"version": 3, "names": ["baseIsTypedArray", "require", "baseUnary", "nodeUtil", "nodeIsTypedArray", "isTypedArray", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/isTypedArray.js"], "sourcesContent": ["var baseIsTypedArray = require('./_baseIsTypedArray'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n\nmodule.exports = isTypedArray;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAD,CAA9B;AAAA,IACIC,SAAS,GAAGD,OAAO,CAAC,cAAD,CADvB;AAAA,IAEIE,QAAQ,GAAGF,OAAO,CAAC,aAAD,CAFtB;AAIA;;;AACA,IAAIG,gBAAgB,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,YAA5C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIA,YAAY,GAAGD,gBAAgB,GAAGF,SAAS,CAACE,gBAAD,CAAZ,GAAiCJ,gBAApE;AAEAM,MAAM,CAACC,OAAP,GAAiBF,YAAjB"}, "metadata": {}, "sourceType": "script"}