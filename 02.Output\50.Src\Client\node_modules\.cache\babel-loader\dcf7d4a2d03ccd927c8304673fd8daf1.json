{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter,\n    XHRLocalObject = require('./transport/sender/xhr-local'),\n    InfoAjax = require('./info-ajax');\n\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function (info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\n\ninherits(InfoReceiverIframe, EventEmitter);\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\n\nInfoReceiverIframe.prototype.close = function () {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n\n  this.removeAllListeners();\n};\n\nmodule.exports = InfoReceiverIframe;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "XHRLocalObject", "InfoAjax", "InfoReceiverIframe", "transUrl", "self", "call", "ir", "once", "info", "rtt", "emit", "JSON", "stringify", "transportName", "prototype", "close", "removeAllListeners", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/info-iframe-receiver.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , XHRLocalObject = require('./transport/sender/xhr-local')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function(info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\n\ninherits(InfoReceiverIframe, EventEmitter);\n\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\n\nInfoReceiverIframe.prototype.close = function() {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\n\nmodule.exports = InfoReceiverIframe;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;AAAA,IAEIC,cAAc,GAAGF,OAAO,CAAC,8BAAD,CAF5B;AAAA,IAGIG,QAAQ,GAAGH,OAAO,CAAC,aAAD,CAHtB;;AAMA,SAASI,kBAAT,CAA4BC,QAA5B,EAAsC;EACpC,IAAIC,IAAI,GAAG,IAAX;EACAL,YAAY,CAACM,IAAb,CAAkB,IAAlB;EAEA,KAAKC,EAAL,GAAU,IAAIL,QAAJ,CAAaE,QAAb,EAAuBH,cAAvB,CAAV;EACA,KAAKM,EAAL,CAAQC,IAAR,CAAa,QAAb,EAAuB,UAASC,IAAT,EAAeC,GAAf,EAAoB;IACzCL,IAAI,CAACE,EAAL,GAAU,IAAV;IACAF,IAAI,CAACM,IAAL,CAAU,SAAV,EAAqBC,IAAI,CAACC,SAAL,CAAe,CAACJ,IAAD,EAAOC,GAAP,CAAf,CAArB;EACD,CAHD;AAID;;AAEDZ,QAAQ,CAACK,kBAAD,EAAqBH,YAArB,CAAR;AAEAG,kBAAkB,CAACW,aAAnB,GAAmC,sBAAnC;;AAEAX,kBAAkB,CAACY,SAAnB,CAA6BC,KAA7B,GAAqC,YAAW;EAC9C,IAAI,KAAKT,EAAT,EAAa;IACX,KAAKA,EAAL,CAAQS,KAAR;IACA,KAAKT,EAAL,GAAU,IAAV;EACD;;EACD,KAAKU,kBAAL;AACD,CAND;;AAQAC,MAAM,CAACC,OAAP,GAAiBhB,kBAAjB"}, "metadata": {}, "sourceType": "script"}