{"ast": null, "code": "import { useRef } from 'react';\n\nvar defaultShouldUpdate = function defaultShouldUpdate(a, b) {\n  return !Object.is(a, b);\n};\n\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n\n  var prevRef = useRef();\n  var curRef = useRef();\n\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n\n  return prevRef.current;\n}\n\nexport default usePrevious;", "map": {"version": 3, "names": ["useRef", "defaultShouldUpdate", "a", "b", "Object", "is", "usePrevious", "state", "shouldUpdate", "prevRef", "curRef", "current"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/usePrevious/index.js"], "sourcesContent": ["import { useRef } from 'react';\nvar defaultShouldUpdate = function defaultShouldUpdate(a, b) {\n  return !Object.is(a, b);\n};\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n  var prevRef = useRef();\n  var curRef = useRef();\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n  return prevRef.current;\n}\nexport default usePrevious;"], "mappings": "AAAA,SAASA,MAAT,QAAuB,OAAvB;;AACA,IAAIC,mBAAmB,GAAG,SAASA,mBAAT,CAA6BC,CAA7B,EAAgCC,CAAhC,EAAmC;EAC3D,OAAO,CAACC,MAAM,CAACC,EAAP,CAAUH,CAAV,EAAaC,CAAb,CAAR;AACD,CAFD;;AAGA,SAASG,WAAT,CAAqBC,KAArB,EAA4BC,YAA5B,EAA0C;EACxC,IAAIA,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAGP,mBAAf;EACD;;EACD,IAAIQ,OAAO,GAAGT,MAAM,EAApB;EACA,IAAIU,MAAM,GAAGV,MAAM,EAAnB;;EACA,IAAIQ,YAAY,CAACE,MAAM,CAACC,OAAR,EAAiBJ,KAAjB,CAAhB,EAAyC;IACvCE,OAAO,CAACE,OAAR,GAAkBD,MAAM,CAACC,OAAzB;IACAD,MAAM,CAACC,OAAP,GAAiBJ,KAAjB;EACD;;EACD,OAAOE,OAAO,CAACE,OAAf;AACD;;AACD,eAAeL,WAAf"}, "metadata": {}, "sourceType": "module"}