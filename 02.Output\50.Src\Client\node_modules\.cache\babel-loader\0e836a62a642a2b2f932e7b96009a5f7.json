{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    EventEmitter = require('events').EventEmitter;\n\nvar debug = function debug() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\n\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n\n  this._scheduleReceiver();\n}\n\ninherits(Polling, EventEmitter);\n\nPolling.prototype._scheduleReceiver = function () {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n  poll.on('message', function (msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n  poll.once('close', function (code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\n\nPolling.prototype.abort = function () {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\n\nmodule.exports = Polling;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "Polling", "Receiver", "receiveUrl", "AjaxObject", "call", "_scheduleReceiver", "prototype", "self", "poll", "on", "msg", "emit", "once", "code", "reason", "pollIsClosing", "removeAllListeners", "abort", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/lib/polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\n\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\n\ninherits(Polling, EventEmitter);\n\nPolling.prototype._scheduleReceiver = function() {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n\n  poll.on('message', function(msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n\n  poll.once('close', function(code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\n\nPolling.prototype.abort = function() {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\n\nmodule.exports = Polling;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,YAAY,GAAGD,OAAO,CAAC,QAAD,CAAP,CAAkBC,YADrC;;AAIA,IAAIC,KAAK,GAAG,iBAAW,CAAE,CAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAD,CAAP,CAAiB,uBAAjB,CAAR;AACD;;AAED,SAASM,OAAT,CAAiBC,QAAjB,EAA2BC,UAA3B,EAAuCC,UAAvC,EAAmD;EACjDP,KAAK,CAACM,UAAD,CAAL;EACAP,YAAY,CAACS,IAAb,CAAkB,IAAlB;EACA,KAAKH,QAAL,GAAgBA,QAAhB;EACA,KAAKC,UAAL,GAAkBA,UAAlB;EACA,KAAKC,UAAL,GAAkBA,UAAlB;;EACA,KAAKE,iBAAL;AACD;;AAEDZ,QAAQ,CAACO,OAAD,EAAUL,YAAV,CAAR;;AAEAK,OAAO,CAACM,SAAR,CAAkBD,iBAAlB,GAAsC,YAAW;EAC/CT,KAAK,CAAC,mBAAD,CAAL;EACA,IAAIW,IAAI,GAAG,IAAX;EACA,IAAIC,IAAI,GAAG,KAAKA,IAAL,GAAY,IAAI,KAAKP,QAAT,CAAkB,KAAKC,UAAvB,EAAmC,KAAKC,UAAxC,CAAvB;EAEAK,IAAI,CAACC,EAAL,CAAQ,SAAR,EAAmB,UAASC,GAAT,EAAc;IAC/Bd,KAAK,CAAC,SAAD,EAAYc,GAAZ,CAAL;IACAH,IAAI,CAACI,IAAL,CAAU,SAAV,EAAqBD,GAArB;EACD,CAHD;EAKAF,IAAI,CAACI,IAAL,CAAU,OAAV,EAAmB,UAASC,IAAT,EAAeC,MAAf,EAAuB;IACxClB,KAAK,CAAC,OAAD,EAAUiB,IAAV,EAAgBC,MAAhB,EAAwBP,IAAI,CAACQ,aAA7B,CAAL;IACAR,IAAI,CAACC,IAAL,GAAYA,IAAI,GAAG,IAAnB;;IAEA,IAAI,CAACD,IAAI,CAACQ,aAAV,EAAyB;MACvB,IAAID,MAAM,KAAK,SAAf,EAA0B;QACxBP,IAAI,CAACF,iBAAL;MACD,CAFD,MAEO;QACLE,IAAI,CAACI,IAAL,CAAU,OAAV,EAAmBE,IAAI,IAAI,IAA3B,EAAiCC,MAAjC;QACAP,IAAI,CAACS,kBAAL;MACD;IACF;EACF,CAZD;AAaD,CAvBD;;AAyBAhB,OAAO,CAACM,SAAR,CAAkBW,KAAlB,GAA0B,YAAW;EACnCrB,KAAK,CAAC,OAAD,CAAL;EACA,KAAKoB,kBAAL;EACA,KAAKD,aAAL,GAAqB,IAArB;;EACA,IAAI,KAAKP,IAAT,EAAe;IACb,KAAKA,IAAL,CAAUS,KAAV;EACD;AACF,CAPD;;AASAC,MAAM,CAACC,OAAP,GAAiBnB,OAAjB"}, "metadata": {}, "sourceType": "script"}