{"ast": null, "code": "var freeGlobal = require('./_freeGlobal');\n/** Detect free variable `self`. */\n\n\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n/** Used as a reference to the global object. */\n\nvar root = freeGlobal || freeSelf || Function('return this')();\nmodule.exports = root;", "map": {"version": 3, "names": ["freeGlobal", "require", "freeSelf", "self", "Object", "root", "Function", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_root.js"], "sourcesContent": ["var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;;;AACA,IAAIC,QAAQ,GAAG,OAAOC,IAAP,IAAe,QAAf,IAA2BA,IAA3B,IAAmCA,IAAI,CAACC,MAAL,KAAgBA,MAAnD,IAA6DD,IAA5E;AAEA;;AACA,IAAIE,IAAI,GAAGL,UAAU,IAAIE,QAAd,IAA0BI,QAAQ,CAAC,aAAD,CAAR,EAArC;AAEAC,MAAM,CAACC,OAAP,GAAiBH,IAAjB"}, "metadata": {}, "sourceType": "script"}