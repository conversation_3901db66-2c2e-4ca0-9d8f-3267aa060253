{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\n\nvar useAntdTable = function useAntdTable(service, options) {\n  var _a;\n\n  if (options === void 0) {\n    options = {};\n  }\n\n  var form = options.form,\n      _b = options.defaultType,\n      defaultType = _b === void 0 ? 'simple' : _b,\n      defaultParams = options.defaultParams,\n      _c = options.manual,\n      manual = _c === void 0 ? false : _c,\n      _d = options.refreshDeps,\n      refreshDeps = _d === void 0 ? [] : _d,\n      _e = options.ready,\n      ready = _e === void 0 ? true : _e,\n      rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n\n  var result = usePagination(service, __assign({\n    manual: true\n  }, rest));\n  var _f = result.params,\n      params = _f === void 0 ? [] : _f,\n      run = result.run;\n  var cacheFormTableData = params[2] || {};\n\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n      type = _g[0],\n      setType = _g[1];\n\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks); // get current active field values\n\n  var getActiveFieldValues = function getActiveFieldValues() {\n    if (!form) {\n      return {};\n    } // antd 4\n\n\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    } // antd 3\n\n\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n\n  var validateFields = function validateFields() {\n    if (!form) {\n      return Promise.resolve({});\n    }\n\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue); // antd 4\n\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    } // antd 3\n\n\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n\n  var restoreForm = function restoreForm() {\n    if (!form) {\n      return;\n    } // antd v4\n\n\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    } // antd v3\n\n\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n\n  var changeType = function changeType() {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n\n  var _submit = function _submit(initPagination) {\n    if (!ready) {\n      return;\n    }\n\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        } // record all form data\n\n\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values); // @ts-ignore\n\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      })[\"catch\"](function (err) {\n        return err;\n      });\n    });\n  };\n\n  var reset = function reset() {\n    if (form) {\n      form.resetFields();\n    }\n\n    _submit();\n  };\n\n  var submit = function submit(e) {\n    var _a;\n\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n\n    _submit();\n  };\n\n  var onTableChange = function onTableChange(pagination, filters, sorter) {\n    var _a = __read(params || []),\n        oldPaginationParams = _a[0],\n        restParams = _a.slice(1);\n\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter\n    })], __read(restParams), false));\n  }; // init\n\n\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm(); // @ts-ignore\n\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n\n    if (!manual && ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, []); // change search type, restore form data\n\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n\n    restoreForm();\n  }, [type]); // refresh & ready change on the same time\n\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n\n      if (form) {\n        form.resetFields();\n      }\n\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n\n    if (!ready) {\n      return;\n    }\n\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\n\nexport default useAntdTable;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "Array", "slice", "concat", "useEffect", "useRef", "useState", "useMemoizedFn", "usePagination", "useUpdateEffect", "useAntdTable", "service", "options", "_a", "form", "_b", "defaultType", "defaultParams", "_c", "manual", "_d", "refreshDeps", "_e", "ready", "rest", "result", "_f", "params", "run", "cacheFormTableData", "_g", "type", "setType", "allFormDataRef", "defaultDataSourceRef", "isAntdV4", "getInternalHooks", "getActiveFieldValues", "getFieldsValue", "allFieldsValue", "activeFieldsValue", "keys", "for<PERSON>ach", "key", "getFieldInstance", "validateFields", "Promise", "resolve", "fields", "reject", "errors", "values", "restoreForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "changeType", "_submit", "initPagination", "setTimeout", "then", "pagination", "pageSize", "defaultPageSize", "allFormData", "err", "reset", "resetFields", "submit", "preventDefault", "onTableChange", "filters", "sorter", "oldPaginationParams", "restParams", "hasAutoRun", "changeCurrent", "tableProps", "dataSource", "data", "list", "loading", "onChange", "total", "search"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useAntdTable/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) {\n    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  }\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useAntdTable = function useAntdTable(service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var form = options.form,\n    _b = options.defaultType,\n    defaultType = _b === void 0 ? 'simple' : _b,\n    defaultParams = options.defaultParams,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    _d = options.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    _e = options.ready,\n    ready = _e === void 0 ? true : _e,\n    rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n  var result = usePagination(service, __assign({\n    manual: true\n  }, rest));\n  var _f = result.params,\n    params = _f === void 0 ? [] : _f,\n    run = result.run;\n  var cacheFormTableData = params[2] || {};\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n    type = _g[0],\n    setType = _g[1];\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);\n  // get current active field values\n  var getActiveFieldValues = function getActiveFieldValues() {\n    if (!form) {\n      return {};\n    }\n    // antd 4\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    }\n    // antd 3\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n  var validateFields = function validateFields() {\n    if (!form) {\n      return Promise.resolve({});\n    }\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue);\n    // antd 4\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    }\n    // antd 3\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n  var restoreForm = function restoreForm() {\n    if (!form) {\n      return;\n    }\n    // antd v4\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    }\n    // antd v3\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n  var changeType = function changeType() {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n  var _submit = function _submit(initPagination) {\n    if (!ready) {\n      return;\n    }\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        }\n        // record all form data\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);\n        // @ts-ignore\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      })[\"catch\"](function (err) {\n        return err;\n      });\n    });\n  };\n  var reset = function reset() {\n    if (form) {\n      form.resetFields();\n    }\n    _submit();\n  };\n  var submit = function submit(e) {\n    var _a;\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n    _submit();\n  };\n  var onTableChange = function onTableChange(pagination, filters, sorter) {\n    var _a = __read(params || []),\n      oldPaginationParams = _a[0],\n      restParams = _a.slice(1);\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter\n    })], __read(restParams), false));\n  };\n  // init\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm();\n      // @ts-ignore\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n    if (!manual && ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, []);\n  // change search type, restore form data\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n    restoreForm();\n  }, [type]);\n  // refresh & ready change on the same time\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      if (form) {\n        form.resetFields();\n      }\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!ready) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\nexport default useAntdTable;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUV,CAAV,EAAaW,CAAb,EAAgB;EAClD,IAAIZ,CAAC,GAAG,EAAR;;EACA,KAAK,IAAIM,CAAT,IAAcL,CAAd,EAAiB;IACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,KAA8CM,CAAC,CAACC,OAAF,CAAUP,CAAV,IAAe,CAAjE,EAAoEN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;EACrE;;EACD,IAAIL,CAAC,IAAI,IAAL,IAAa,OAAOH,MAAM,CAACgB,qBAAd,KAAwC,UAAzD,EAAqE,KAAK,IAAIZ,CAAC,GAAG,CAAR,EAAWI,CAAC,GAAGR,MAAM,CAACgB,qBAAP,CAA6Bb,CAA7B,CAApB,EAAqDC,CAAC,GAAGI,CAAC,CAACD,MAA3D,EAAmEH,CAAC,EAApE,EAAwE;IAC3I,IAAIU,CAAC,CAACC,OAAF,CAAUP,CAAC,CAACJ,CAAD,CAAX,IAAkB,CAAlB,IAAuBJ,MAAM,CAACS,SAAP,CAAiBQ,oBAAjB,CAAsCN,IAAtC,CAA2CR,CAA3C,EAA8CK,CAAC,CAACJ,CAAD,CAA/C,CAA3B,EAAgFF,CAAC,CAACM,CAAC,CAACJ,CAAD,CAAF,CAAD,GAAUD,CAAC,CAACK,CAAC,CAACJ,CAAD,CAAF,CAAX;EACjF;EACD,OAAOF,CAAP;AACD,CATD;;AAUA,IAAIgB,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAad,CAAb,EAAgB;EAClD,IAAIe,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIf,CAAC,GAAGgB,CAAC,CAACT,IAAF,CAAOQ,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEV,CAHF;;EAIA,IAAI;IACF,OAAO,CAACT,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACkB,CAAC,GAAGnB,CAAC,CAACqB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDF,EAAE,CAACG,IAAH,CAAQJ,CAAC,CAACK,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdf,CAAC,GAAG;MACFe,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIN,CAAC,IAAI,CAACA,CAAC,CAACG,IAAR,KAAiBN,CAAC,GAAGhB,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCgB,CAAC,CAACT,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIU,CAAJ,EAAO,MAAMA,CAAC,CAACe,KAAR;IACR;EACF;;EACD,OAAOL,EAAP;AACD,CAvBD;;AAwBA,IAAIM,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAI3B,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIH,CAAC,GAAG,CAAR,EAAW8B,CAAC,GAAGF,IAAI,CAACzB,MAApB,EAA4BiB,EAAjC,EAAqCpB,CAAC,GAAG8B,CAAzC,EAA4C9B,CAAC,EAA7C,EAAiD;IACnF,IAAIoB,EAAE,IAAI,EAAEpB,CAAC,IAAI4B,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACR,EAAL,EAASA,EAAE,GAAGW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,EAAiC,CAAjC,EAAoC5B,CAApC,CAAL;MACToB,EAAE,CAACpB,CAAD,CAAF,GAAQ4B,IAAI,CAAC5B,CAAD,CAAZ;IACD;EACF;EACD,OAAO2B,EAAE,CAACM,MAAH,CAAUb,EAAE,IAAIW,KAAK,CAAC1B,SAAN,CAAgB2B,KAAhB,CAAsBzB,IAAtB,CAA2BqB,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASM,SAAT,EAAoBC,MAApB,EAA4BC,QAA5B,QAA4C,OAA5C;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,eAAP,MAA4B,oBAA5B;;AACA,IAAIC,YAAY,GAAG,SAASA,YAAT,CAAsBC,OAAtB,EAA+BC,OAA/B,EAAwC;EACzD,IAAIC,EAAJ;;EACA,IAAID,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIE,IAAI,GAAGF,OAAO,CAACE,IAAnB;EAAA,IACEC,EAAE,GAAGH,OAAO,CAACI,WADf;EAAA,IAEEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,QAAhB,GAA2BA,EAF3C;EAAA,IAGEE,aAAa,GAAGL,OAAO,CAACK,aAH1B;EAAA,IAIEC,EAAE,GAAGN,OAAO,CAACO,MAJf;EAAA,IAKEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EALnC;EAAA,IAMEE,EAAE,GAAGR,OAAO,CAACS,WANf;EAAA,IAOEA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EAPrC;EAAA,IAQEE,EAAE,GAAGV,OAAO,CAACW,KARf;EAAA,IASEA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EATjC;EAAA,IAUEE,IAAI,GAAG7C,MAAM,CAACiC,OAAD,EAAU,CAAC,MAAD,EAAS,aAAT,EAAwB,eAAxB,EAAyC,QAAzC,EAAmD,aAAnD,EAAkE,OAAlE,CAAV,CAVf;;EAWA,IAAIa,MAAM,GAAGjB,aAAa,CAACG,OAAD,EAAU9C,QAAQ,CAAC;IAC3CsD,MAAM,EAAE;EADmC,CAAD,EAEzCK,IAFyC,CAAlB,CAA1B;EAGA,IAAIE,EAAE,GAAGD,MAAM,CAACE,MAAhB;EAAA,IACEA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,EAAhB,GAAqBA,EADhC;EAAA,IAEEE,GAAG,GAAGH,MAAM,CAACG,GAFf;EAGA,IAAIC,kBAAkB,GAAGF,MAAM,CAAC,CAAD,CAAN,IAAa,EAAtC;;EACA,IAAIG,EAAE,GAAG9C,MAAM,CAACsB,QAAQ,CAAC,CAACuB,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACE,IAA5F,KAAqGf,WAAtG,CAAT,EAA6H,CAA7H,CAAf;EAAA,IACEe,IAAI,GAAGD,EAAE,CAAC,CAAD,CADX;EAAA,IAEEE,OAAO,GAAGF,EAAE,CAAC,CAAD,CAFd;;EAGA,IAAIG,cAAc,GAAG5B,MAAM,CAAC,EAAD,CAA3B;EACA,IAAI6B,oBAAoB,GAAG7B,MAAM,CAAC,EAAD,CAAjC;EACA,IAAI8B,QAAQ,GAAG,CAAC,EAAErB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACsB,gBAAnD,CAAhB,CA5ByD,CA6BzD;;EACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAT,GAAgC;IACzD,IAAI,CAACvB,IAAL,EAAW;MACT,OAAO,EAAP;IACD,CAHwD,CAIzD;;;IACA,IAAIqB,QAAJ,EAAc;MACZ,OAAOrB,IAAI,CAACwB,cAAL,CAAoB,IAApB,EAA0B,YAAY;QAC3C,OAAO,IAAP;MACD,CAFM,CAAP;IAGD,CATwD,CAUzD;;;IACA,IAAIC,cAAc,GAAGzB,IAAI,CAACwB,cAAL,EAArB;IACA,IAAIE,iBAAiB,GAAG,EAAxB;IACA1E,MAAM,CAAC2E,IAAP,CAAYF,cAAZ,EAA4BG,OAA5B,CAAoC,UAAUC,GAAV,EAAe;MACjD,IAAI7B,IAAI,CAAC8B,gBAAL,GAAwB9B,IAAI,CAAC8B,gBAAL,CAAsBD,GAAtB,CAAxB,GAAqD,IAAzD,EAA+D;QAC7DH,iBAAiB,CAACG,GAAD,CAAjB,GAAyBJ,cAAc,CAACI,GAAD,CAAvC;MACD;IACF,CAJD;IAKA,OAAOH,iBAAP;EACD,CAnBD;;EAoBA,IAAIK,cAAc,GAAG,SAASA,cAAT,GAA0B;IAC7C,IAAI,CAAC/B,IAAL,EAAW;MACT,OAAOgC,OAAO,CAACC,OAAR,CAAgB,EAAhB,CAAP;IACD;;IACD,IAAIP,iBAAiB,GAAGH,oBAAoB,EAA5C;IACA,IAAIW,MAAM,GAAGlF,MAAM,CAAC2E,IAAP,CAAYD,iBAAZ,CAAb,CAL6C,CAM7C;;IACA,IAAIL,QAAJ,EAAc;MACZ,OAAOrB,IAAI,CAAC+B,cAAL,CAAoBG,MAApB,CAAP;IACD,CAT4C,CAU7C;;;IACA,OAAO,IAAIF,OAAJ,CAAY,UAAUC,OAAV,EAAmBE,MAAnB,EAA2B;MAC5CnC,IAAI,CAAC+B,cAAL,CAAoBG,MAApB,EAA4B,UAAUE,MAAV,EAAkBC,MAAlB,EAA0B;QACpD,IAAID,MAAJ,EAAY;UACVD,MAAM,CAACC,MAAD,CAAN;QACD,CAFD,MAEO;UACLH,OAAO,CAACI,MAAD,CAAP;QACD;MACF,CAND;IAOD,CARM,CAAP;EASD,CApBD;;EAqBA,IAAIC,WAAW,GAAG,SAASA,WAAT,GAAuB;IACvC,IAAI,CAACtC,IAAL,EAAW;MACT;IACD,CAHsC,CAIvC;;;IACA,IAAIqB,QAAJ,EAAc;MACZ,OAAOrB,IAAI,CAACuC,cAAL,CAAoBpB,cAAc,CAACqB,OAAnC,CAAP;IACD,CAPsC,CAQvC;;;IACA,IAAId,iBAAiB,GAAG,EAAxB;IACA1E,MAAM,CAAC2E,IAAP,CAAYR,cAAc,CAACqB,OAA3B,EAAoCZ,OAApC,CAA4C,UAAUC,GAAV,EAAe;MACzD,IAAI7B,IAAI,CAAC8B,gBAAL,GAAwB9B,IAAI,CAAC8B,gBAAL,CAAsBD,GAAtB,CAAxB,GAAqD,IAAzD,EAA+D;QAC7DH,iBAAiB,CAACG,GAAD,CAAjB,GAAyBV,cAAc,CAACqB,OAAf,CAAuBX,GAAvB,CAAzB;MACD;IACF,CAJD;IAKA7B,IAAI,CAACuC,cAAL,CAAoBb,iBAApB;EACD,CAhBD;;EAiBA,IAAIe,UAAU,GAAG,SAASA,UAAT,GAAsB;IACrC,IAAIf,iBAAiB,GAAGH,oBAAoB,EAA5C;IACAJ,cAAc,CAACqB,OAAf,GAAyBzF,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKoE,cAAc,CAACqB,OAApB,CAAT,EAAuCd,iBAAvC,CAAjC;IACAR,OAAO,CAAC,UAAUhE,CAAV,EAAa;MACnB,OAAOA,CAAC,KAAK,QAAN,GAAiB,SAAjB,GAA6B,QAApC;IACD,CAFM,CAAP;EAGD,CAND;;EAOA,IAAIwF,OAAO,GAAG,SAASA,OAAT,CAAiBC,cAAjB,EAAiC;IAC7C,IAAI,CAAClC,KAAL,EAAY;MACV;IACD;;IACDmC,UAAU,CAAC,YAAY;MACrBb,cAAc,GAAGc,IAAjB,CAAsB,UAAUR,MAAV,EAAkB;QACtC,IAAIA,MAAM,KAAK,KAAK,CAApB,EAAuB;UACrBA,MAAM,GAAG,EAAT;QACD;;QACD,IAAIS,UAAU,GAAGH,cAAc,IAAI5F,QAAQ,CAACA,QAAQ,CAAC;UACnDgG,QAAQ,EAAEjD,OAAO,CAACkD,eAAR,IAA2B;QADc,CAAD,EAEjD,CAACnC,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAC,CAAD,CAAvD,KAA+D,EAFd,CAAT,EAE4B;UACrE2B,OAAO,EAAE;QAD4D,CAF5B,CAA3C;;QAKA,IAAI,CAACxC,IAAL,EAAW;UACT;UACAc,GAAG,CAACgC,UAAD,CAAH;UACA;QACD,CAbqC,CActC;;;QACA3B,cAAc,CAACqB,OAAf,GAAyBzF,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKoE,cAAc,CAACqB,OAApB,CAAT,EAAuCH,MAAvC,CAAjC,CAfsC,CAgBtC;;QACAvB,GAAG,CAACgC,UAAD,EAAaT,MAAb,EAAqB;UACtBY,WAAW,EAAE9B,cAAc,CAACqB,OADN;UAEtBvB,IAAI,EAAEA;QAFgB,CAArB,CAAH;MAID,CArBD,EAqBG,OArBH,EAqBY,UAAUiC,GAAV,EAAe;QACzB,OAAOA,GAAP;MACD,CAvBD;IAwBD,CAzBS,CAAV;EA0BD,CA9BD;;EA+BA,IAAIC,KAAK,GAAG,SAASA,KAAT,GAAiB;IAC3B,IAAInD,IAAJ,EAAU;MACRA,IAAI,CAACoD,WAAL;IACD;;IACDV,OAAO;EACR,CALD;;EAMA,IAAIW,MAAM,GAAG,SAASA,MAAT,CAAgBvF,CAAhB,EAAmB;IAC9B,IAAIiC,EAAJ;;IACA,CAACA,EAAE,GAAGjC,CAAC,KAAK,IAAN,IAAcA,CAAC,KAAK,KAAK,CAAzB,GAA6B,KAAK,CAAlC,GAAsCA,CAAC,CAACwF,cAA9C,MAAkE,IAAlE,IAA0EvD,EAAE,KAAK,KAAK,CAAtF,GAA0F,KAAK,CAA/F,GAAmGA,EAAE,CAACpC,IAAH,CAAQG,CAAR,CAAnG;;IACA4E,OAAO;EACR,CAJD;;EAKA,IAAIa,aAAa,GAAG,SAASA,aAAT,CAAuBT,UAAvB,EAAmCU,OAAnC,EAA4CC,MAA5C,EAAoD;IACtE,IAAI1D,EAAE,GAAG7B,MAAM,CAAC2C,MAAM,IAAI,EAAX,CAAf;IAAA,IACE6C,mBAAmB,GAAG3D,EAAE,CAAC,CAAD,CAD1B;IAAA,IAEE4D,UAAU,GAAG5D,EAAE,CAACX,KAAH,CAAS,CAAT,CAFf;;IAGA0B,GAAG,CAAClD,KAAJ,CAAU,KAAK,CAAf,EAAkBkB,aAAa,CAAC,CAAC/B,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAK2G,mBAAL,CAAT,EAAoC;MAC3ElB,OAAO,EAAEM,UAAU,CAACN,OADuD;MAE3EO,QAAQ,EAAED,UAAU,CAACC,QAFsD;MAG3ES,OAAO,EAAEA,OAHkE;MAI3EC,MAAM,EAAEA;IAJmE,CAApC,CAAT,CAAD,EAK1BvF,MAAM,CAACyF,UAAD,CALoB,EAKN,KALM,CAA/B;EAMD,CAVD,CAzIyD,CAoJzD;;;EACArE,SAAS,CAAC,YAAY;IACpB;IACA,IAAIuB,MAAM,CAACtD,MAAP,GAAgB,CAApB,EAAuB;MACrB4D,cAAc,CAACqB,OAAf,GAAyB,CAACzB,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACkC,WAA5F,KAA4G,EAArI;MACAX,WAAW,GAFU,CAGrB;;MACAxB,GAAG,CAAClD,KAAJ,CAAU,KAAK,CAAf,EAAkBkB,aAAa,CAAC,EAAD,EAAKZ,MAAM,CAAC2C,MAAD,CAAX,EAAqB,KAArB,CAA/B;MACA;IACD;;IACD,IAAI,CAACR,MAAD,IAAWI,KAAf,EAAsB;MACpBU,cAAc,CAACqB,OAAf,GAAyB,CAACrC,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC,CAAD,CAA5E,KAAoF,EAA7G;MACAmC,WAAW;;MACXI,OAAO,CAACvC,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC,CAAD,CAA5E,CAAP;IACD;EACF,CAdQ,EAcN,EAdM,CAAT,CArJyD,CAoKzD;;EACAR,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACc,KAAL,EAAY;MACV;IACD;;IACD6B,WAAW;EACZ,CALc,EAKZ,CAACrB,IAAD,CALY,CAAf,CArKyD,CA2KzD;;EACA,IAAI2C,UAAU,GAAGrE,MAAM,CAAC,KAAD,CAAvB;EACAqE,UAAU,CAACpB,OAAX,GAAqB,KAArB;EACA7C,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACU,MAAD,IAAWI,KAAf,EAAsB;MACpBmD,UAAU,CAACpB,OAAX,GAAqB,IAArB;;MACA,IAAIxC,IAAJ,EAAU;QACRA,IAAI,CAACoD,WAAL;MACD;;MACDjC,cAAc,CAACqB,OAAf,GAAyB,CAACrC,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC,CAAD,CAA5E,KAAoF,EAA7G;MACAmC,WAAW;;MACXI,OAAO,CAACvC,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC,CAAD,CAA5E,CAAP;IACD;EACF,CAVc,EAUZ,CAACM,KAAD,CAVY,CAAf;EAWAd,eAAe,CAAC,YAAY;IAC1B,IAAIiE,UAAU,CAACpB,OAAf,EAAwB;MACtB;IACD;;IACD,IAAI,CAAC/B,KAAL,EAAY;MACV;IACD;;IACD,IAAI,CAACJ,MAAL,EAAa;MACXuD,UAAU,CAACpB,OAAX,GAAqB,IAArB;MACA7B,MAAM,CAACmC,UAAP,CAAkBe,aAAlB,CAAgC,CAAhC;IACD;EACF,CAXc,EAWZ/E,aAAa,CAAC,EAAD,EAAKZ,MAAM,CAACqC,WAAD,CAAX,EAA0B,KAA1B,CAXD,CAAf;EAYA,OAAOxD,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAK4D,MAAL,CAAT,EAAuB;IACpCmD,UAAU,EAAE;MACVC,UAAU,EAAE,CAAC,CAAChE,EAAE,GAAGY,MAAM,CAACqD,IAAb,MAAuB,IAAvB,IAA+BjE,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACkE,IAA5D,KAAqE7C,oBAAoB,CAACoB,OAD5F;MAEV0B,OAAO,EAAEvD,MAAM,CAACuD,OAFN;MAGVC,QAAQ,EAAE1E,aAAa,CAAC8D,aAAD,CAHb;MAIVT,UAAU,EAAE;QACVN,OAAO,EAAE7B,MAAM,CAACmC,UAAP,CAAkBN,OADjB;QAEVO,QAAQ,EAAEpC,MAAM,CAACmC,UAAP,CAAkBC,QAFlB;QAGVqB,KAAK,EAAEzD,MAAM,CAACmC,UAAP,CAAkBsB;MAHf;IAJF,CADwB;IAWpCC,MAAM,EAAE;MACNhB,MAAM,EAAE5D,aAAa,CAAC4D,MAAD,CADf;MAENpC,IAAI,EAAEA,IAFA;MAGNwB,UAAU,EAAEhD,aAAa,CAACgD,UAAD,CAHnB;MAINU,KAAK,EAAE1D,aAAa,CAAC0D,KAAD;IAJd;EAX4B,CAAvB,CAAf;AAkBD,CAvND;;AAwNA,eAAevD,YAAf"}, "metadata": {}, "sourceType": "module"}