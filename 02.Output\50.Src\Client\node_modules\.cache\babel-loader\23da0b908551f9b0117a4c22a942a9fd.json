{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\n\nvar initState = __assign({\n  text: ''\n}, initRect);\n\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n\n  var range = selection.getRangeAt(0);\n\n  var _a = range.getBoundingClientRect(),\n      height = _a.height,\n      width = _a.width,\n      top = _a.top,\n      left = _a.left,\n      right = _a.right,\n      bottom = _a.bottom;\n\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\n\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n      state = _a[0],\n      setState = _a[1];\n\n  var stateRef = useRef(state);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n\n    if (!el) {\n      return;\n    }\n\n    var mouseupHandler = function mouseupHandler() {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) return;\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n\n      if (text) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    }; // 任意点击都需要清空之前的 range\n\n\n    var mousedownHandler = function mousedownHandler() {\n      if (!window.getSelection) return;\n\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n\n      var selObj = window.getSelection();\n      if (!selObj) return;\n      selObj.removeAllRanges();\n    };\n\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\n\nexport default useTextSelection;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__read", "o", "m", "Symbol", "iterator", "r", "ar", "e", "next", "done", "push", "value", "error", "useRef", "useState", "getTargetElement", "useEffectWithTarget", "initRect", "top", "NaN", "left", "bottom", "right", "height", "width", "initState", "text", "getRectFromSelection", "selection", "rangeCount", "range", "getRangeAt", "_a", "getBoundingClientRect", "useTextSelection", "target", "state", "setState", "stateRef", "current", "el", "document", "mouseup<PERSON><PERSON><PERSON>", "sel<PERSON>bj", "rect", "window", "getSelection", "toString", "mousedownHandler", "removeAllRanges", "addEventListener", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useTextSelection/index.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) {\n        if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\nvar initState = __assign({\n  text: ''\n}, initRect);\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n  var range = selection.getRangeAt(0);\n  var _a = range.getBoundingClientRect(),\n    height = _a.height,\n    width = _a.width,\n    top = _a.top,\n    left = _a.left,\n    right = _a.right,\n    bottom = _a.bottom;\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var mouseupHandler = function mouseupHandler() {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) return;\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n      if (text) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    };\n    // 任意点击都需要清空之前的 range\n    var mousedownHandler = function mousedownHandler() {\n      if (!window.getSelection) return;\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n      var selObj = window.getSelection();\n      if (!selObj) return;\n      selObj.removeAllRanges();\n    };\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\nexport default useTextSelection;"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,QAAQ,KAAKA,QAAb,IAAyB,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAAUC,CAAV,EAAa;IACvC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB;QACf,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EAAgDN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;MACjD;IACF;;IACD,OAAON,CAAP;EACD,CARD;;EASA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACD,CAXD;;AAYA,IAAIO,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaT,CAAb,EAAgB;EAClD,IAAIU,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCF,CAAC,CAACE,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOD,CAAP;EACR,IAAIV,CAAC,GAAGW,CAAC,CAACJ,IAAF,CAAOG,CAAP,CAAR;EAAA,IACEI,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACf,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACa,CAAC,GAAGd,CAAC,CAACiB,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBP,CAAC,GAAGX,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCW,CAAC,CAACJ,IAAF,CAAOP,CAAP;IACxC,CAFD,SAEU;MACR,IAAIgB,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,SAASO,MAAT,EAAiBC,QAAjB,QAAiC,OAAjC;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;AACA,IAAIC,QAAQ,GAAG;EACbC,GAAG,EAAEC,GADQ;EAEbC,IAAI,EAAED,GAFO;EAGbE,MAAM,EAAEF,GAHK;EAIbG,KAAK,EAAEH,GAJM;EAKbI,MAAM,EAAEJ,GALK;EAMbK,KAAK,EAAEL;AANM,CAAf;;AAQA,IAAIM,SAAS,GAAGvC,QAAQ,CAAC;EACvBwC,IAAI,EAAE;AADiB,CAAD,EAErBT,QAFqB,CAAxB;;AAGA,SAASU,oBAAT,CAA8BC,SAA9B,EAAyC;EACvC,IAAI,CAACA,SAAL,EAAgB;IACd,OAAOX,QAAP;EACD;;EACD,IAAIW,SAAS,CAACC,UAAV,GAAuB,CAA3B,EAA8B;IAC5B,OAAOZ,QAAP;EACD;;EACD,IAAIa,KAAK,GAAGF,SAAS,CAACG,UAAV,CAAqB,CAArB,CAAZ;;EACA,IAAIC,EAAE,GAAGF,KAAK,CAACG,qBAAN,EAAT;EAAA,IACEV,MAAM,GAAGS,EAAE,CAACT,MADd;EAAA,IAEEC,KAAK,GAAGQ,EAAE,CAACR,KAFb;EAAA,IAGEN,GAAG,GAAGc,EAAE,CAACd,GAHX;EAAA,IAIEE,IAAI,GAAGY,EAAE,CAACZ,IAJZ;EAAA,IAKEE,KAAK,GAAGU,EAAE,CAACV,KALb;EAAA,IAMED,MAAM,GAAGW,EAAE,CAACX,MANd;;EAOA,OAAO;IACLE,MAAM,EAAEA,MADH;IAELC,KAAK,EAAEA,KAFF;IAGLN,GAAG,EAAEA,GAHA;IAILE,IAAI,EAAEA,IAJD;IAKLE,KAAK,EAAEA,KALF;IAMLD,MAAM,EAAEA;EANH,CAAP;AAQD;;AACD,SAASa,gBAAT,CAA0BC,MAA1B,EAAkC;EAChC,IAAIH,EAAE,GAAGhC,MAAM,CAACc,QAAQ,CAACW,SAAD,CAAT,EAAsB,CAAtB,CAAf;EAAA,IACEW,KAAK,GAAGJ,EAAE,CAAC,CAAD,CADZ;EAAA,IAEEK,QAAQ,GAAGL,EAAE,CAAC,CAAD,CAFf;;EAGA,IAAIM,QAAQ,GAAGzB,MAAM,CAACuB,KAAD,CAArB;EACAE,QAAQ,CAACC,OAAT,GAAmBH,KAAnB;EACApB,mBAAmB,CAAC,YAAY;IAC9B,IAAIwB,EAAE,GAAGzB,gBAAgB,CAACoB,MAAD,EAASM,QAAT,CAAzB;;IACA,IAAI,CAACD,EAAL,EAAS;MACP;IACD;;IACD,IAAIE,cAAc,GAAG,SAASA,cAAT,GAA0B;MAC7C,IAAIC,MAAM,GAAG,IAAb;MACA,IAAIjB,IAAI,GAAG,EAAX;MACA,IAAIkB,IAAI,GAAG3B,QAAX;MACA,IAAI,CAAC4B,MAAM,CAACC,YAAZ,EAA0B;MAC1BH,MAAM,GAAGE,MAAM,CAACC,YAAP,EAAT;MACApB,IAAI,GAAGiB,MAAM,GAAGA,MAAM,CAACI,QAAP,EAAH,GAAuB,EAApC;;MACA,IAAIrB,IAAJ,EAAU;QACRkB,IAAI,GAAGjB,oBAAoB,CAACgB,MAAD,CAA3B;QACAN,QAAQ,CAACnD,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKkD,KAAL,CAAT,EAAsB;UAC9CV,IAAI,EAAEA;QADwC,CAAtB,CAAT,EAEbkB,IAFa,CAAT,CAAR;MAGD;IACF,CAbD,CAL8B,CAmB9B;;;IACA,IAAII,gBAAgB,GAAG,SAASA,gBAAT,GAA4B;MACjD,IAAI,CAACH,MAAM,CAACC,YAAZ,EAA0B;;MAC1B,IAAIR,QAAQ,CAACC,OAAT,CAAiBb,IAArB,EAA2B;QACzBW,QAAQ,CAACnD,QAAQ,CAAC,EAAD,EAAKuC,SAAL,CAAT,CAAR;MACD;;MACD,IAAIkB,MAAM,GAAGE,MAAM,CAACC,YAAP,EAAb;MACA,IAAI,CAACH,MAAL,EAAa;MACbA,MAAM,CAACM,eAAP;IACD,CARD;;IASAT,EAAE,CAACU,gBAAH,CAAoB,SAApB,EAA+BR,cAA/B;IACAD,QAAQ,CAACS,gBAAT,CAA0B,WAA1B,EAAuCF,gBAAvC;IACA,OAAO,YAAY;MACjBR,EAAE,CAACW,mBAAH,CAAuB,SAAvB,EAAkCT,cAAlC;MACAD,QAAQ,CAACU,mBAAT,CAA6B,WAA7B,EAA0CH,gBAA1C;IACD,CAHD;EAID,CAnCkB,EAmChB,EAnCgB,EAmCZb,MAnCY,CAAnB;EAoCA,OAAOC,KAAP;AACD;;AACD,eAAeF,gBAAf"}, "metadata": {}, "sourceType": "module"}