{"ast": null, "code": "var MapCache = require('./_MapCache'),\n    setCacheAdd = require('./_setCacheAdd'),\n    setCacheHas = require('./_setCacheHas');\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\n\n\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n  this.__data__ = new MapCache();\n\n  while (++index < length) {\n    this.add(values[index]);\n  }\n} // Add methods to `SetCache`.\n\n\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\nmodule.exports = SetCache;", "map": {"version": 3, "names": ["MapCache", "require", "setCacheAdd", "setCacheHas", "<PERSON><PERSON><PERSON>", "values", "index", "length", "__data__", "add", "prototype", "push", "has", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_SetCache.js"], "sourcesContent": ["var MapCache = require('./_MapCache'),\n    setCacheAdd = require('./_setCacheAdd'),\n    setCacheHas = require('./_setCacheHas');\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n\n  this.__data__ = new MapCache;\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n\nmodule.exports = SetCache;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAD,CAAtB;AAAA,IACIC,WAAW,GAAGD,OAAO,CAAC,gBAAD,CADzB;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,gBAAD,CAFzB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,QAAT,CAAkBC,MAAlB,EAA0B;EACxB,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGF,MAAM,IAAI,IAAV,GAAiB,CAAjB,GAAqBA,MAAM,CAACE,MADzC;EAGA,KAAKC,QAAL,GAAgB,IAAIR,QAAJ,EAAhB;;EACA,OAAO,EAAEM,KAAF,GAAUC,MAAjB,EAAyB;IACvB,KAAKE,GAAL,CAASJ,MAAM,CAACC,KAAD,CAAf;EACD;AACF,C,CAED;;;AACAF,QAAQ,CAACM,SAAT,CAAmBD,GAAnB,GAAyBL,QAAQ,CAACM,SAAT,CAAmBC,IAAnB,GAA0BT,WAAnD;AACAE,QAAQ,CAACM,SAAT,CAAmBE,GAAnB,GAAyBT,WAAzB;AAEAU,MAAM,CAACC,OAAP,GAAiBV,QAAjB"}, "metadata": {}, "sourceType": "script"}