{"ast": null, "code": "var getMapData = require('./_getMapData');\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\n\n\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nmodule.exports = mapCacheSet;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheSet", "key", "value", "data", "size", "set", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_mapCacheSet.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nmodule.exports = mapCacheSet;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAD,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBC,GAArB,EAA0BC,KAA1B,EAAiC;EAC/B,IAAIC,IAAI,GAAGL,UAAU,CAAC,IAAD,EAAOG,GAAP,CAArB;EAAA,IACIG,IAAI,GAAGD,IAAI,CAACC,IADhB;EAGAD,IAAI,CAACE,GAAL,CAASJ,GAAT,EAAcC,KAAd;EACA,KAAKE,IAAL,IAAaD,IAAI,CAACC,IAAL,IAAaA,IAAb,GAAoB,CAApB,GAAwB,CAArC;EACA,OAAO,IAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBP,WAAjB"}, "metadata": {}, "sourceType": "script"}