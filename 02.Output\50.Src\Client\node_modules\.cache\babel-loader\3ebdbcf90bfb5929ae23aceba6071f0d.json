{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n    IframeTransport = require('../iframe'),\n    objectUtils = require('../../utils/object');\n\nmodule.exports = function (transport) {\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n\n  inherits(IframeWrapTransport, IframeTransport);\n\n  IframeWrapTransport.enabled = function (url, info) {\n    if (!global.document) {\n      return false;\n    }\n\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n  return IframeWrapTransport;\n};", "map": {"version": 3, "names": ["inherits", "require", "IframeTransport", "objectUtils", "module", "exports", "transport", "IframeWrapTransport", "transUrl", "baseUrl", "call", "transportName", "enabled", "url", "info", "global", "document", "iframeInfo", "extend", "<PERSON><PERSON><PERSON><PERSON>", "needBody", "roundTrips", "facadeTransport"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/sockjs-client/lib/transport/lib/iframe-wrap.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , IframeTransport = require('../iframe')\n  , objectUtils = require('../../utils/object')\n  ;\n\nmodule.exports = function(transport) {\n\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n\n  inherits(IframeWrapTransport, IframeTransport);\n\n  IframeWrapTransport.enabled = function(url, info) {\n    if (!global.document) {\n      return false;\n    }\n\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n\n  return IframeWrapTransport;\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAD,CAAtB;AAAA,IACIC,eAAe,GAAGD,OAAO,CAAC,WAAD,CAD7B;AAAA,IAEIE,WAAW,GAAGF,OAAO,CAAC,oBAAD,CAFzB;;AAKAG,MAAM,CAACC,OAAP,GAAiB,UAASC,SAAT,EAAoB;EAEnC,SAASC,mBAAT,CAA6BC,QAA7B,EAAuCC,OAAvC,EAAgD;IAC9CP,eAAe,CAACQ,IAAhB,CAAqB,IAArB,EAA2BJ,SAAS,CAACK,aAArC,EAAoDH,QAApD,EAA8DC,OAA9D;EACD;;EAEDT,QAAQ,CAACO,mBAAD,EAAsBL,eAAtB,CAAR;;EAEAK,mBAAmB,CAACK,OAApB,GAA8B,UAASC,GAAT,EAAcC,IAAd,EAAoB;IAChD,IAAI,CAACC,MAAM,CAACC,QAAZ,EAAsB;MACpB,OAAO,KAAP;IACD;;IAED,IAAIC,UAAU,GAAGd,WAAW,CAACe,MAAZ,CAAmB,EAAnB,EAAuBJ,IAAvB,CAAjB;IACAG,UAAU,CAACE,UAAX,GAAwB,IAAxB;IACA,OAAOb,SAAS,CAACM,OAAV,CAAkBK,UAAlB,KAAiCf,eAAe,CAACU,OAAhB,EAAxC;EACD,CARD;;EAUAL,mBAAmB,CAACI,aAApB,GAAoC,YAAYL,SAAS,CAACK,aAA1D;EACAJ,mBAAmB,CAACa,QAApB,GAA+B,IAA/B;EACAb,mBAAmB,CAACc,UAApB,GAAiCnB,eAAe,CAACmB,UAAhB,GAA6Bf,SAAS,CAACe,UAAvC,GAAoD,CAArF,CApBmC,CAoBqD;;EAExFd,mBAAmB,CAACe,eAApB,GAAsChB,SAAtC;EAEA,OAAOC,mBAAP;AACD,CAzBD"}, "metadata": {}, "sourceType": "script"}