{"ast": null, "code": "var assocIndexOf = require('./_assocIndexOf');\n/** Used for built-in method references. */\n\n\nvar arrayProto = Array.prototype;\n/** Built-in value references. */\n\nvar splice = arrayProto.splice;\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\n\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n\n  var lastIndex = data.length - 1;\n\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n\n  --this.size;\n  return true;\n}\n\nmodule.exports = listCacheDelete;", "map": {"version": 3, "names": ["assocIndexOf", "require", "arrayProto", "Array", "prototype", "splice", "listCacheDelete", "key", "data", "__data__", "index", "lastIndex", "length", "pop", "call", "size", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_listCacheDelete.js"], "sourcesContent": ["var assocIndexOf = require('./_assocIndexOf');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\nmodule.exports = listCacheDelete;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAD,CAA1B;AAEA;;;AACA,IAAIC,UAAU,GAAGC,KAAK,CAACC,SAAvB;AAEA;;AACA,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,eAAT,CAAyBC,GAAzB,EAA8B;EAC5B,IAAIC,IAAI,GAAG,KAAKC,QAAhB;EAAA,IACIC,KAAK,GAAGV,YAAY,CAACQ,IAAD,EAAOD,GAAP,CADxB;;EAGA,IAAIG,KAAK,GAAG,CAAZ,EAAe;IACb,OAAO,KAAP;EACD;;EACD,IAAIC,SAAS,GAAGH,IAAI,CAACI,MAAL,GAAc,CAA9B;;EACA,IAAIF,KAAK,IAAIC,SAAb,EAAwB;IACtBH,IAAI,CAACK,GAAL;EACD,CAFD,MAEO;IACLR,MAAM,CAACS,IAAP,CAAYN,IAAZ,EAAkBE,KAAlB,EAAyB,CAAzB;EACD;;EACD,EAAE,KAAKK,IAAP;EACA,OAAO,IAAP;AACD;;AAEDC,MAAM,CAACC,OAAP,GAAiBX,eAAjB"}, "metadata": {}, "sourceType": "script"}