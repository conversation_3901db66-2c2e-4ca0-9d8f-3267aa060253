{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport * as cache from '../utils/cache';\nimport * as cachePromise from '../utils/cachePromise';\nimport * as cacheSubscribe from '../utils/cacheSubscribe';\n\nvar useCachePlugin = function useCachePlugin(fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n      _b = _a.cacheTime,\n      cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n      _c = _a.staleTime,\n      staleTime = _c === void 0 ? 0 : _c,\n      customSetCache = _a.setCache,\n      customGetCache = _a.getCache;\n  var unSubscribeRef = useRef();\n  var currentPromiseRef = useRef();\n\n  var _setCache = function _setCache(key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      cache.setCache(key, cacheTime, cachedData);\n    }\n\n    cacheSubscribe.trigger(key, cachedData.data);\n  };\n\n  var _getCache = function _getCache(key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n\n    return cache.getCache(key);\n  };\n\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    } // get data from cache when init\n\n\n    var cacheData = _getCache(cacheKey);\n\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    } // subscribe same cachekey update, trigger update\n\n\n    unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n\n  if (!cacheKey) {\n    return {};\n  }\n\n  return {\n    onBefore: function onBefore(params) {\n      var cacheData = _getCache(cacheKey, params);\n\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      } // If the data is fresh, stop request\n\n\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function onRequest(service, args) {\n      var servicePromise = cachePromise.getCachePromise(cacheKey); // If has servicePromise, and is not trigger by self, then use it\n\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      cachePromise.setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function onSuccess(data, params) {\n      var _a;\n\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: new Date().getTime()\n        }); // resubscribe\n\n\n        unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function onMutate(data) {\n      var _a;\n\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: new Date().getTime()\n        }); // resubscribe\n\n\n        unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\n\nexport default useCachePlugin;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useRef", "useCreation", "useUnmount", "cache", "cachePromise", "cacheSubscribe", "useCachePlugin", "fetchInstance", "_a", "cache<PERSON>ey", "_b", "cacheTime", "_c", "staleTime", "customSetCache", "setCache", "customGetCache", "getCache", "unSubscribeRef", "currentPromiseRef", "_setCache", "key", "cachedData", "trigger", "data", "_getCache", "params", "cacheData", "Object", "hasOwnProperty", "state", "Date", "getTime", "time", "loading", "current", "subscribe", "setState", "onBefore", "undefined", "returnNow", "onRequest", "service", "args", "servicePromise", "getCachePromise", "apply", "setCachePromise", "onSuccess", "d", "onMutate"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useRef } from 'react';\nimport useCreation from '../../../useCreation';\nimport useUnmount from '../../../useUnmount';\nimport * as cache from '../utils/cache';\nimport * as cachePromise from '../utils/cachePromise';\nimport * as cacheSubscribe from '../utils/cacheSubscribe';\nvar useCachePlugin = function useCachePlugin(fetchInstance, _a) {\n  var cacheKey = _a.cacheKey,\n    _b = _a.cacheTime,\n    cacheTime = _b === void 0 ? 5 * 60 * 1000 : _b,\n    _c = _a.staleTime,\n    staleTime = _c === void 0 ? 0 : _c,\n    customSetCache = _a.setCache,\n    customGetCache = _a.getCache;\n  var unSubscribeRef = useRef();\n  var currentPromiseRef = useRef();\n  var _setCache = function _setCache(key, cachedData) {\n    if (customSetCache) {\n      customSetCache(cachedData);\n    } else {\n      cache.setCache(key, cacheTime, cachedData);\n    }\n    cacheSubscribe.trigger(key, cachedData.data);\n  };\n  var _getCache = function _getCache(key, params) {\n    if (params === void 0) {\n      params = [];\n    }\n    if (customGetCache) {\n      return customGetCache(params);\n    }\n    return cache.getCache(key);\n  };\n  useCreation(function () {\n    if (!cacheKey) {\n      return;\n    }\n    // get data from cache when init\n    var cacheData = _getCache(cacheKey);\n    if (cacheData && Object.hasOwnProperty.call(cacheData, 'data')) {\n      fetchInstance.state.data = cacheData.data;\n      fetchInstance.state.params = cacheData.params;\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        fetchInstance.state.loading = false;\n      }\n    }\n    // subscribe same cachekey update, trigger update\n    unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (data) {\n      fetchInstance.setState({\n        data: data\n      });\n    });\n  }, []);\n  useUnmount(function () {\n    var _a;\n    (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n  });\n  if (!cacheKey) {\n    return {};\n  }\n  return {\n    onBefore: function onBefore(params) {\n      var cacheData = _getCache(cacheKey, params);\n      if (!cacheData || !Object.hasOwnProperty.call(cacheData, 'data')) {\n        return {};\n      }\n      // If the data is fresh, stop request\n      if (staleTime === -1 || new Date().getTime() - cacheData.time <= staleTime) {\n        return {\n          loading: false,\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined,\n          returnNow: true\n        };\n      } else {\n        // If the data is stale, return data, and request continue\n        return {\n          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,\n          error: undefined\n        };\n      }\n    },\n    onRequest: function onRequest(service, args) {\n      var servicePromise = cachePromise.getCachePromise(cacheKey);\n      // If has servicePromise, and is not trigger by self, then use it\n      if (servicePromise && servicePromise !== currentPromiseRef.current) {\n        return {\n          servicePromise: servicePromise\n        };\n      }\n      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));\n      currentPromiseRef.current = servicePromise;\n      cachePromise.setCachePromise(cacheKey, servicePromise);\n      return {\n        servicePromise: servicePromise\n      };\n    },\n    onSuccess: function onSuccess(data, params) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    },\n    onMutate: function onMutate(data) {\n      var _a;\n      if (cacheKey) {\n        // cancel subscribe, avoid trgger self\n        (_a = unSubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unSubscribeRef);\n        _setCache(cacheKey, {\n          data: data,\n          params: fetchInstance.state.params,\n          time: new Date().getTime()\n        });\n        // resubscribe\n        unSubscribeRef.current = cacheSubscribe.subscribe(cacheKey, function (d) {\n          fetchInstance.setState({\n            data: d\n          });\n        });\n      }\n    }\n  };\n};\nexport default useCachePlugin;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASS,MAAT,QAAuB,OAAvB;AACA,OAAOC,WAAP,MAAwB,sBAAxB;AACA,OAAOC,UAAP,MAAuB,qBAAvB;AACA,OAAO,KAAKC,KAAZ,MAAuB,gBAAvB;AACA,OAAO,KAAKC,YAAZ,MAA8B,uBAA9B;AACA,OAAO,KAAKC,cAAZ,MAAgC,yBAAhC;;AACA,IAAIC,cAAc,GAAG,SAASA,cAAT,CAAwBC,aAAxB,EAAuCC,EAAvC,EAA2C;EAC9D,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAlB;EAAA,IACEC,EAAE,GAAGF,EAAE,CAACG,SADV;EAAA,IAEEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAI,EAAJ,GAAS,IAAzB,GAAgCA,EAF9C;EAAA,IAGEE,EAAE,GAAGJ,EAAE,CAACK,SAHV;EAAA,IAIEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,CAAhB,GAAoBA,EAJlC;EAAA,IAKEE,cAAc,GAAGN,EAAE,CAACO,QALtB;EAAA,IAMEC,cAAc,GAAGR,EAAE,CAACS,QANtB;EAOA,IAAIC,cAAc,GAAGlB,MAAM,EAA3B;EACA,IAAImB,iBAAiB,GAAGnB,MAAM,EAA9B;;EACA,IAAIoB,SAAS,GAAG,SAASA,SAAT,CAAmBC,GAAnB,EAAwBC,UAAxB,EAAoC;IAClD,IAAIR,cAAJ,EAAoB;MAClBA,cAAc,CAACQ,UAAD,CAAd;IACD,CAFD,MAEO;MACLnB,KAAK,CAACY,QAAN,CAAeM,GAAf,EAAoBV,SAApB,EAA+BW,UAA/B;IACD;;IACDjB,cAAc,CAACkB,OAAf,CAAuBF,GAAvB,EAA4BC,UAAU,CAACE,IAAvC;EACD,CAPD;;EAQA,IAAIC,SAAS,GAAG,SAASA,SAAT,CAAmBJ,GAAnB,EAAwBK,MAAxB,EAAgC;IAC9C,IAAIA,MAAM,KAAK,KAAK,CAApB,EAAuB;MACrBA,MAAM,GAAG,EAAT;IACD;;IACD,IAAIV,cAAJ,EAAoB;MAClB,OAAOA,cAAc,CAACU,MAAD,CAArB;IACD;;IACD,OAAOvB,KAAK,CAACc,QAAN,CAAeI,GAAf,CAAP;EACD,CARD;;EASApB,WAAW,CAAC,YAAY;IACtB,IAAI,CAACQ,QAAL,EAAe;MACb;IACD,CAHqB,CAItB;;;IACA,IAAIkB,SAAS,GAAGF,SAAS,CAAChB,QAAD,CAAzB;;IACA,IAAIkB,SAAS,IAAIC,MAAM,CAACC,cAAP,CAAsBjD,IAAtB,CAA2B+C,SAA3B,EAAsC,MAAtC,CAAjB,EAAgE;MAC9DpB,aAAa,CAACuB,KAAd,CAAoBN,IAApB,GAA2BG,SAAS,CAACH,IAArC;MACAjB,aAAa,CAACuB,KAAd,CAAoBJ,MAApB,GAA6BC,SAAS,CAACD,MAAvC;;MACA,IAAIb,SAAS,KAAK,CAAC,CAAf,IAAoB,IAAIkB,IAAJ,GAAWC,OAAX,KAAuBL,SAAS,CAACM,IAAjC,IAAyCpB,SAAjE,EAA4E;QAC1EN,aAAa,CAACuB,KAAd,CAAoBI,OAApB,GAA8B,KAA9B;MACD;IACF,CAZqB,CAatB;;;IACAhB,cAAc,CAACiB,OAAf,GAAyB9B,cAAc,CAAC+B,SAAf,CAAyB3B,QAAzB,EAAmC,UAAUe,IAAV,EAAgB;MAC1EjB,aAAa,CAAC8B,QAAd,CAAuB;QACrBb,IAAI,EAAEA;MADe,CAAvB;IAGD,CAJwB,CAAzB;EAKD,CAnBU,EAmBR,EAnBQ,CAAX;EAoBAtB,UAAU,CAAC,YAAY;IACrB,IAAIM,EAAJ;;IACA,CAACA,EAAE,GAAGU,cAAc,CAACiB,OAArB,MAAkC,IAAlC,IAA0C3B,EAAE,KAAK,KAAK,CAAtD,GAA0D,KAAK,CAA/D,GAAmEA,EAAE,CAAC5B,IAAH,CAAQsC,cAAR,CAAnE;EACD,CAHS,CAAV;;EAIA,IAAI,CAACT,QAAL,EAAe;IACb,OAAO,EAAP;EACD;;EACD,OAAO;IACL6B,QAAQ,EAAE,SAASA,QAAT,CAAkBZ,MAAlB,EAA0B;MAClC,IAAIC,SAAS,GAAGF,SAAS,CAAChB,QAAD,EAAWiB,MAAX,CAAzB;;MACA,IAAI,CAACC,SAAD,IAAc,CAACC,MAAM,CAACC,cAAP,CAAsBjD,IAAtB,CAA2B+C,SAA3B,EAAsC,MAAtC,CAAnB,EAAkE;QAChE,OAAO,EAAP;MACD,CAJiC,CAKlC;;;MACA,IAAId,SAAS,KAAK,CAAC,CAAf,IAAoB,IAAIkB,IAAJ,GAAWC,OAAX,KAAuBL,SAAS,CAACM,IAAjC,IAAyCpB,SAAjE,EAA4E;QAC1E,OAAO;UACLqB,OAAO,EAAE,KADJ;UAELV,IAAI,EAAEG,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACH,IAFjE;UAGLpC,KAAK,EAAEmD,SAHF;UAILC,SAAS,EAAE;QAJN,CAAP;MAMD,CAPD,MAOO;QACL;QACA,OAAO;UACLhB,IAAI,EAAEG,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACH,IADjE;UAELpC,KAAK,EAAEmD;QAFF,CAAP;MAID;IACF,CArBI;IAsBLE,SAAS,EAAE,SAASA,SAAT,CAAmBC,OAAnB,EAA4BC,IAA5B,EAAkC;MAC3C,IAAIC,cAAc,GAAGxC,YAAY,CAACyC,eAAb,CAA6BpC,QAA7B,CAArB,CAD2C,CAE3C;;MACA,IAAImC,cAAc,IAAIA,cAAc,KAAKzB,iBAAiB,CAACgB,OAA3D,EAAoE;QAClE,OAAO;UACLS,cAAc,EAAEA;QADX,CAAP;MAGD;;MACDA,cAAc,GAAGF,OAAO,CAACI,KAAR,CAAc,KAAK,CAAnB,EAAsBzD,aAAa,CAAC,EAAD,EAAKhB,MAAM,CAACsE,IAAD,CAAX,EAAmB,KAAnB,CAAnC,CAAjB;MACAxB,iBAAiB,CAACgB,OAAlB,GAA4BS,cAA5B;MACAxC,YAAY,CAAC2C,eAAb,CAA6BtC,QAA7B,EAAuCmC,cAAvC;MACA,OAAO;QACLA,cAAc,EAAEA;MADX,CAAP;IAGD,CApCI;IAqCLI,SAAS,EAAE,SAASA,SAAT,CAAmBxB,IAAnB,EAAyBE,MAAzB,EAAiC;MAC1C,IAAIlB,EAAJ;;MACA,IAAIC,QAAJ,EAAc;QACZ;QACA,CAACD,EAAE,GAAGU,cAAc,CAACiB,OAArB,MAAkC,IAAlC,IAA0C3B,EAAE,KAAK,KAAK,CAAtD,GAA0D,KAAK,CAA/D,GAAmEA,EAAE,CAAC5B,IAAH,CAAQsC,cAAR,CAAnE;;QACAE,SAAS,CAACX,QAAD,EAAW;UAClBe,IAAI,EAAEA,IADY;UAElBE,MAAM,EAAEA,MAFU;UAGlBO,IAAI,EAAE,IAAIF,IAAJ,GAAWC,OAAX;QAHY,CAAX,CAAT,CAHY,CAQZ;;;QACAd,cAAc,CAACiB,OAAf,GAAyB9B,cAAc,CAAC+B,SAAf,CAAyB3B,QAAzB,EAAmC,UAAUwC,CAAV,EAAa;UACvE1C,aAAa,CAAC8B,QAAd,CAAuB;YACrBb,IAAI,EAAEyB;UADe,CAAvB;QAGD,CAJwB,CAAzB;MAKD;IACF,CAtDI;IAuDLC,QAAQ,EAAE,SAASA,QAAT,CAAkB1B,IAAlB,EAAwB;MAChC,IAAIhB,EAAJ;;MACA,IAAIC,QAAJ,EAAc;QACZ;QACA,CAACD,EAAE,GAAGU,cAAc,CAACiB,OAArB,MAAkC,IAAlC,IAA0C3B,EAAE,KAAK,KAAK,CAAtD,GAA0D,KAAK,CAA/D,GAAmEA,EAAE,CAAC5B,IAAH,CAAQsC,cAAR,CAAnE;;QACAE,SAAS,CAACX,QAAD,EAAW;UAClBe,IAAI,EAAEA,IADY;UAElBE,MAAM,EAAEnB,aAAa,CAACuB,KAAd,CAAoBJ,MAFV;UAGlBO,IAAI,EAAE,IAAIF,IAAJ,GAAWC,OAAX;QAHY,CAAX,CAAT,CAHY,CAQZ;;;QACAd,cAAc,CAACiB,OAAf,GAAyB9B,cAAc,CAAC+B,SAAf,CAAyB3B,QAAzB,EAAmC,UAAUwC,CAAV,EAAa;UACvE1C,aAAa,CAAC8B,QAAd,CAAuB;YACrBb,IAAI,EAAEyB;UADe,CAAvB;QAGD,CAJwB,CAAzB;MAKD;IACF;EAxEI,CAAP;AA0ED,CAhID;;AAiIA,eAAe3C,cAAf"}, "metadata": {}, "sourceType": "module"}