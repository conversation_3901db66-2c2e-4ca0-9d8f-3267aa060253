{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\n\nvar useDrag = function useDrag(data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n\n    var onDragStart = function onDragStart(event) {\n      var _a, _b;\n\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n    };\n\n    var onDragEnd = function onDragEnd(event) {\n      var _a, _b;\n\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\n\nexport default useDrag;", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "useEffectWithTarget", "useDrag", "data", "target", "options", "optionsRef", "dataRef", "targetElement", "addEventListener", "onDragStart", "event", "_a", "_b", "current", "call", "dataTransfer", "setData", "JSON", "stringify", "onDragEnd", "setAttribute", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useDrag/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar useDrag = function useDrag(data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onDragStart = function onDragStart(event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n    };\n    var onDragEnd = function onDragEnd(event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\nexport default useDrag;"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;;AACA,IAAIC,OAAO,GAAG,SAASA,OAAT,CAAiBC,IAAjB,EAAuBC,MAAvB,EAA+BC,OAA/B,EAAwC;EACpD,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,UAAU,GAAGP,SAAS,CAACM,OAAD,CAA1B;EACA,IAAIE,OAAO,GAAGR,SAAS,CAACI,IAAD,CAAvB;EACAF,mBAAmB,CAAC,YAAY;IAC9B,IAAIO,aAAa,GAAGR,gBAAgB,CAACI,MAAD,CAApC;;IACA,IAAI,EAAEI,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACC,gBAA9E,CAAJ,EAAqG;MACnG;IACD;;IACD,IAAIC,WAAW,GAAG,SAASA,WAAT,CAAqBC,KAArB,EAA4B;MAC5C,IAAIC,EAAJ,EAAQC,EAAR;;MACA,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGN,UAAU,CAACQ,OAAjB,EAA0BJ,WAAhC,MAAiD,IAAjD,IAAyDG,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAACE,IAAH,CAAQH,EAAR,EAAYD,KAAZ,CAAlF;MACAA,KAAK,CAACK,YAAN,CAAmBC,OAAnB,CAA2B,QAA3B,EAAqCC,IAAI,CAACC,SAAL,CAAeZ,OAAO,CAACO,OAAvB,CAArC;IACD,CAJD;;IAKA,IAAIM,SAAS,GAAG,SAASA,SAAT,CAAmBT,KAAnB,EAA0B;MACxC,IAAIC,EAAJ,EAAQC,EAAR;;MACA,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGN,UAAU,CAACQ,OAAjB,EAA0BM,SAAhC,MAA+C,IAA/C,IAAuDP,EAAE,KAAK,KAAK,CAAnE,GAAuE,KAAK,CAA5E,GAAgFA,EAAE,CAACE,IAAH,CAAQH,EAAR,EAAYD,KAAZ,CAAhF;IACD,CAHD;;IAIAH,aAAa,CAACa,YAAd,CAA2B,WAA3B,EAAwC,MAAxC;IACAb,aAAa,CAACC,gBAAd,CAA+B,WAA/B,EAA4CC,WAA5C;IACAF,aAAa,CAACC,gBAAd,CAA+B,SAA/B,EAA0CW,SAA1C;IACA,OAAO,YAAY;MACjBZ,aAAa,CAACc,mBAAd,CAAkC,WAAlC,EAA+CZ,WAA/C;MACAF,aAAa,CAACc,mBAAd,CAAkC,SAAlC,EAA6CF,SAA7C;IACD,CAHD;EAID,CArBkB,EAqBhB,EArBgB,EAqBZhB,MArBY,CAAnB;AAsBD,CA5BD;;AA6BA,eAAeF,OAAf"}, "metadata": {}, "sourceType": "module"}