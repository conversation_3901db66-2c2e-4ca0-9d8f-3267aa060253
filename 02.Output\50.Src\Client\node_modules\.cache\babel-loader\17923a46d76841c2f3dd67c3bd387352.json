{"ast": null, "code": "import _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\n\n/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport var Versions = /*#__PURE__*/function () {\n  /**\n   * Takes an array of string of versions, typical elements '1.0', '1.1', or '1.2'\n   *\n   * You will an instance if this class if you want to override supported versions to be declared during\n   * STOMP handshake.\n   */\n  function Versions(versions) {\n    _classCallCheck(this, Versions);\n\n    this.versions = versions;\n  }\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n\n\n  _createClass(Versions, [{\n    key: \"supportedVersions\",\n    value: function supportedVersions() {\n      return this.versions.join(',');\n    }\n    /**\n     * Used while creating a WebSocket\n     */\n\n  }, {\n    key: \"protocolVersions\",\n    value: function protocolVersions() {\n      return this.versions.map(function (x) {\n        return \"v\".concat(x.replace('.', ''), \".stomp\");\n      });\n    }\n  }]);\n\n  return Versions;\n}();\n/**\n * Indicates protocol version 1.0\n */\n\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\n\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\n\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\n\nVersions.default = new Versions([Versions.V1_0, Versions.V1_1, Versions.V1_2]);", "map": {"version": 3, "mappings": ";;;AAAA;;;;;AAKA,WAAaA,QAAb;EAuBE;;;;;;EAMA,kBAAmBC,QAAnB,EAAqC;IAAA;;IAAlB;EAAsB;EAEzC;;;;;EA/BF;IAAA;IAAA,OAkCS,6BAAiB;MACtB,OAAO,KAAKA,QAAL,CAAcC,IAAd,CAAmB,GAAnB,CAAP;IACD;IAED;;;;EAtCF;IAAA;IAAA,OAyCS,4BAAgB;MACrB,OAAO,KAAKD,QAAL,CAAcE,GAAd,CAAkB,WAAC;QAAA,kBAAQC,CAAC,CAACC,OAAF,CAAU,GAAV,EAAe,EAAf,CAAR;MAAA,CAAnB,CAAP;IACD;EA3CH;;EAAA;AAAA;AACE;;;;AAGcL,gBAAO,KAAP;AACd;;;;AAGcA,gBAAO,KAAP;AACd;;;;AAGcA,gBAAO,KAAP;AAEd;;;;AAGcA,mBAAU,IAAIA,QAAJ,CAAa,CACnCA,QAAQ,CAACM,IAD0B,EAEnCN,QAAQ,CAACO,IAF0B,EAGnCP,QAAQ,CAACQ,IAH0B,CAAb,CAAV", "names": ["Versions", "versions", "join", "map", "x", "replace", "V1_0", "V1_1", "V1_2"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\versions.ts"], "sourcesContent": ["/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Indicates protocol version 1.0\n   */\n  public static V1_0 = '1.0';\n  /**\n   * Indicates protocol version 1.1\n   */\n  public static V1_1 = '1.1';\n  /**\n   * Indicates protocol version 1.2\n   */\n  public static V1_2 = '1.2';\n\n  /**\n   * @internal\n   */\n  public static default = new Versions([\n    Versions.V1_0,\n    Versions.V1_1,\n    Versions.V1_2,\n  ]);\n\n  /**\n   * Takes an array of string of versions, typical elements '1.0', '1.1', or '1.2'\n   *\n   * You will an instance if this class if you want to override supported versions to be declared during\n   * STOMP handshake.\n   */\n  constructor(public versions: string[]) {}\n\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  public supportedVersions() {\n    return this.versions.join(',');\n  }\n\n  /**\n   * Used while creating a WebSocket\n   */\n  public protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}