{"ast": null, "code": "import _objectSpread from\"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import CellBox from'./elements/CellBox';import{getCellFace,isValidSource}from'../utils/Util.js';import BlinkBlock,{checkBlinkInfo}from'./elements/BlinkBlock';/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";var CustomVehicle=function CustomVehicle(props){if(!isValidSource(props))return;if(!props.items)return;var showDelopy=props.is_deployment===1;var columnPosition=props.column_position;var gridClass=getGridClass(props);return/*#__PURE__*/_jsx(_Fragment,{children:isValidSource(props)&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridClass),children:props.items.map(function(item,index){return/*#__PURE__*/_jsx(Station,_objectSpread(_objectSpread({},item),{},{showDelopy:showDelopy,columnPosition:columnPosition}));})})});};/**\r\n * ソース表示パターンによってグリッドのクラス定義を取得する\r\n * @param {*} props\r\n * @returns クラス定義\r\n */var getGridClass=function getGridClass(props){if(!(props.sourceDispPattern===1))return'grid-cols-2 grid-rows-16 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';if(props.column_position=='left')return'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pr-[16px]';if(props.column_position=='right')return'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pl-[16px]';return'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';};/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */var Station=function Station(entity){var gridCol;// gridCol = getGridRowClass(entity);\nvar subTitleSpan='col-span-full';if(entity.showDelopy){gridCol='grid-cols-quarter-vehicle-deploy';}else{gridCol='grid-cols-quarter-vehicle-nodeploy';}var subTitleProp=getCellFace(entity.title,\"\".concat(subTitleSpan,\" flex flex-col items-center\"));return/*#__PURE__*/_jsxs(_Fragment,{children:[entity.title&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(CellBox,_objectSpread(_objectSpread({},subTitleProp),{},{children:/*#__PURE__*/_jsx(\"span\",{children:entity.title.display_text})}))}),!entity.title&&/*#__PURE__*/_jsx(VehicleDetailRow,_objectSpread({},entity))]});};/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */var VehicleDetailRow=function VehicleDetailRow(entity){var showInfoDeployment;var showInfoCarName;var showInfoTownName;var showInfoDisasterType;var showInfoAvmDynamicState;if(entity.showDelopy&&entity.deployment)showInfoDeployment=entity.deployment;if(entity.car_name)showInfoCarName=entity.car_name;if(entity.town_name)showInfoTownName=entity.town_name;if(entity.disaster_type)showInfoDisasterType=entity.disaster_type;if(entity.avm_dynamic_state)showInfoAvmDynamicState=entity.avm_dynamic_state;var status=entity.lighting_setting?entity.lighting_setting.lighting_status:1;//該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\nvar baseObj=[entity.car_name,entity.town_name,entity.disaster_type,entity.avm_dynamic_state,entity.deployment].find(function(item){return item;});showInfoDeployment=checkBlinkInfo(showInfoDeployment,baseObj);showInfoCarName=checkBlinkInfo(showInfoCarName,baseObj);showInfoTownName=checkBlinkInfo(showInfoTownName,baseObj);showInfoDisasterType=checkBlinkInfo(showInfoDisasterType,baseObj);showInfoAvmDynamicState=checkBlinkInfo(showInfoAvmDynamicState,baseObj);var showInfoSeperator0=_objectSpread({},showInfoDeployment);var showInfoSeperator1=_objectSpread({},showInfoDeployment);var showInfoSeperator2=_objectSpread({},showInfoCarName);var showInfoSeperator3=_objectSpread({},showInfoTownName);var showInfoSeperator4=_objectSpread({},showInfoDisasterType);var showInfoSeperator5=_objectSpread({},showInfoAvmDynamicState);showInfoSeperator0.display_text=' ';showInfoSeperator1.display_text=' ';showInfoSeperator2.display_text=' ';showInfoSeperator3.display_text=' ';showInfoSeperator4.display_text=' ';showInfoSeperator5.display_text=' ';// Status=3 点滅以外、背景色を表示する必要がないので、クリアする\nif(status!==3){showInfoSeperator0.background_color=undefined;showInfoSeperator1.background_color=undefined;showInfoSeperator2.background_color=undefined;showInfoSeperator3.background_color=undefined;showInfoSeperator4.background_color=undefined;showInfoSeperator5.background_color=undefined;}var gridCol;//gridCol = getGridRowClass(entity);\nvar showBlock=[];if(entity.showDelopy){gridCol='grid-cols-quarter-vehicle-deploy';if(entity.columnPosition=='left'){showBlock.push({showInfo:showInfoSeperator0,className:'col-span-1'});}showBlock.push({showInfo:showInfoDeployment,className:'col-span-1 col-start-2'});showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-4'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-9'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-16'});showBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-19'});if(entity.columnPosition=='right'){showBlock.push({showInfo:showInfoSeperator5,className:'col-span-1'});}}else{gridCol='grid-cols-quarter-vehicle-nodeploy';showBlock.push({showInfo:showInfoSeperator1,className:'col-span-1'});showBlock.push({showInfo:showInfoCarName,className:'col-span-4 col-start-2'});showBlock.push({showInfo:showInfoSeperator2,className:'col-span-1'});showBlock.push({showInfo:showInfoTownName,className:'col-span-6 col-start-7'});showBlock.push({showInfo:showInfoSeperator3,className:'col-span-1'});showBlock.push({showInfo:showInfoDisasterType,className:'col-span-2 col-start-14'});showBlock.push({showInfo:showInfoSeperator4,className:'col-span-1'});showBlock.push({showInfo:showInfoAvmDynamicState,className:'col-span-2 col-start-17'});if(entity.columnPosition=='right'){showBlock.push({showInfo:showInfoSeperator5,className:'col-span-1'});}}return/*#__PURE__*/_jsxs(_Fragment,{children:[entity.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:entity.lighting_setting})}),!entity.showDelopy&&/*#__PURE__*/_jsx(\"div\",{className:\"grid \".concat(gridCol),children:/*#__PURE__*/_jsx(BlinkBlock,{block:showBlock,blink_setting:entity.lighting_setting})})]});};/**\r\n * ソース表示パターンによってグリッド１行のクラス定義を取得する\r\n * @param {*} entity\r\n * @returns クラス定義\r\n */var getGridRowClass=function getGridRowClass(entity){if(entity.showDelopy){if(entity.columnPosition=='left')return'14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';if(entity.columnPosition=='right')return'14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';}else{if(entity.columnPosition=='left')return'14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';if(entity.columnPosition=='right')return'14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';}return'16px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';};export default CustomVehicle;", "map": {"version": 3, "names": ["React", "CellBox", "getCellFace", "isValidSource", "BlinkBlock", "checkBlinkInfo", "CustomVehicle", "props", "items", "showDelopy", "is_deployment", "columnPosition", "column_position", "gridClass", "getGridClass", "map", "item", "index", "sourceDispPattern", "Station", "entity", "gridCol", "subTitleSpan", "subTitleProp", "title", "display_text", "VehicleDetailRow", "showInfoDeployment", "showInfoCarName", "showInfoTownName", "showInfoDisasterType", "showInfoAvmDynamicState", "deployment", "car_name", "town_name", "disaster_type", "avm_dynamic_state", "status", "lighting_setting", "lighting_status", "baseObj", "find", "showInfoSeperator0", "showInfoSeperator1", "showInfoSeperator2", "showInfoSeperator3", "showInfoSeperator4", "showInfoSeperator5", "background_color", "undefined", "showBlock", "push", "showInfo", "className", "getGridRowClass"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/src/components/CustomVehicle.js"], "sourcesContent": ["import React from 'react';\r\nimport CellBox from './elements/CellBox';\r\nimport { getCellFace, isValidSource } from '../utils/Util.js';\r\nimport BlinkBlock, { checkBlinkInfo } from './elements/BlinkBlock';\r\n\r\n\r\n/**\r\n * 車両コンテンツ<br>\r\n * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う\r\n * @module Vehicle\r\n * @component\r\n * @param {*} props\r\n * @return {*} 表示データ\r\n */\r\nconst CustomVehicle = (props) => {\r\n\r\n    if (!isValidSource(props))\r\n        return;\r\n\r\n    if (!props.items)\r\n        return;\r\n\r\n    const showDelopy = props.is_deployment === 1;\r\n    const columnPosition = props.column_position;\r\n    const gridClass = getGridClass(props);\r\n\r\n    return (\r\n        <>\r\n            {isValidSource(props) && (\r\n                <div className={`grid ${gridClass}`}>\r\n                    {props.items.map((item, index) => {\r\n                        return (\r\n                            <Station\r\n                                {...item}\r\n                                showDelopy={showDelopy}\r\n                                columnPosition={columnPosition}\r\n                            />\r\n                        );\r\n                    })}\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * ソース表示パターンによってグリッドのクラス定義を取得する\r\n * @param {*} props\r\n * @returns クラス定義\r\n */\r\nconst getGridClass = (props) => {\r\n\r\n    if (!(props.sourceDispPattern === 1))\r\n        return 'grid-cols-2 grid-rows-16 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';\r\n\r\n    if (props.column_position == 'left')\r\n        return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pr-[16px]';\r\n\r\n    if (props.column_position == 'right')\r\n        return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-y-[12px] pl-[16px]';\r\n\r\n    return 'grid-cols-1 grid-rows-8 grid-flow-col text-[56px] leading-[1] gap-x-[36px] gap-y-[12px]';\r\n};\r\n\r\n/**\r\n * 署所(部隊)名または車両種別単位のデータを表示\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */\r\nconst Station = (entity) => {\r\n    let gridCol;\r\n    // gridCol = getGridRowClass(entity);\r\n    let subTitleSpan = 'col-span-full';\r\n    if (entity.showDelopy) {\r\n        gridCol = 'grid-cols-quarter-vehicle-deploy';\r\n    } else {\r\n        gridCol = 'grid-cols-quarter-vehicle-nodeploy';\r\n    }\r\n    const subTitleProp = getCellFace(\r\n        entity.title,\r\n        `${subTitleSpan} flex flex-col items-center`\r\n    );\r\n\r\n    return (\r\n        <>\r\n            {entity.title && (\r\n                < div className={`grid ${gridCol}`}>\r\n                    <CellBox {...subTitleProp}>\r\n                        <span>{entity.title.display_text}</span>\r\n                    </CellBox>\r\n                </div >\r\n            )}\r\n            {!entity.title && (\r\n                <VehicleDetailRow  {...entity} />\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n *  車両コンテンツの一行データ\r\n * @param {*} entity\r\n * @returns 表示データ\r\n */\r\nconst VehicleDetailRow = (entity) => {\r\n\r\n    let showInfoDeployment;\r\n    let showInfoCarName;\r\n    let showInfoTownName;\r\n    let showInfoDisasterType;\r\n    let showInfoAvmDynamicState;\r\n\r\n    if (entity.showDelopy && entity.deployment)\r\n        showInfoDeployment = entity.deployment;\r\n\r\n    if (entity.car_name)\r\n        showInfoCarName = entity.car_name;\r\n\r\n    if (entity.town_name)\r\n        showInfoTownName = entity.town_name;\r\n\r\n    if (entity.disaster_type)\r\n        showInfoDisasterType = entity.disaster_type;\r\n\r\n    if (entity.avm_dynamic_state)\r\n        showInfoAvmDynamicState = entity.avm_dynamic_state;\r\n\r\n    let status = entity.lighting_setting ? entity.lighting_setting.lighting_status : 1;\r\n\r\n    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ\r\n    let baseObj = [entity.car_name, entity.town_name, entity.disaster_type, entity.avm_dynamic_state, entity.deployment].find(item => item);\r\n\r\n    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);\r\n    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);\r\n    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);\r\n    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);\r\n    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);\r\n\r\n    let showInfoSeperator0 = { ...showInfoDeployment };\r\n    let showInfoSeperator1 = { ...showInfoDeployment };\r\n    let showInfoSeperator2 = { ...showInfoCarName };\r\n    let showInfoSeperator3 = { ...showInfoTownName };\r\n    let showInfoSeperator4 = { ...showInfoDisasterType };\r\n    let showInfoSeperator5 = { ...showInfoAvmDynamicState };\r\n\r\n    showInfoSeperator0.display_text = ' ';\r\n    showInfoSeperator1.display_text = ' ';\r\n    showInfoSeperator2.display_text = ' ';\r\n    showInfoSeperator3.display_text = ' ';\r\n    showInfoSeperator4.display_text = ' ';\r\n    showInfoSeperator5.display_text = ' ';\r\n\r\n    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする\r\n    if (status !== 3) {\r\n        showInfoSeperator0.background_color = undefined;\r\n        showInfoSeperator1.background_color = undefined;\r\n        showInfoSeperator2.background_color = undefined;\r\n        showInfoSeperator3.background_color = undefined;\r\n        showInfoSeperator4.background_color = undefined;\r\n        showInfoSeperator5.background_color = undefined;\r\n    }\r\n\r\n    let gridCol;\r\n    //gridCol = getGridRowClass(entity);\r\n    let showBlock = [];\r\n    if (entity.showDelopy) {\r\n        gridCol = 'grid-cols-quarter-vehicle-deploy';\r\n\r\n        if (entity.columnPosition == 'left') {\r\n            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });\r\n        }\r\n\r\n        showBlock.push({\r\n            showInfo: showInfoDeployment,\r\n            className: 'col-span-1 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-4',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-9',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-16',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-19',\r\n        });\r\n\r\n        if (entity.columnPosition == 'right') {\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n\r\n    } else {\r\n        gridCol = 'grid-cols-quarter-vehicle-nodeploy';\r\n\r\n        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoCarName,\r\n            className: 'col-span-4 col-start-2',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoTownName,\r\n            className: 'col-span-6 col-start-7',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoDisasterType,\r\n            className: 'col-span-2 col-start-14',\r\n        });\r\n        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });\r\n        showBlock.push({\r\n            showInfo: showInfoAvmDynamicState,\r\n            className: 'col-span-2 col-start-17',\r\n        });\r\n        if (entity.columnPosition == 'right') {\r\n            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });\r\n        }\r\n\r\n    }\r\n\r\n    return (\r\n        <>\r\n            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}\r\n            {entity.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={entity.lighting_setting}\r\n                    />\r\n                </div>\r\n            )}\r\n            {!entity.showDelopy && (\r\n                <div className={`grid ${gridCol}`}>\r\n                    <BlinkBlock\r\n                        block={showBlock}\r\n                        blink_setting={entity.lighting_setting}\r\n                    />\r\n                </div>\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\n/**\r\n * ソース表示パターンによってグリッド１行のクラス定義を取得する\r\n * @param {*} entity\r\n * @returns クラス定義\r\n */\r\nconst getGridRowClass = (entity) => {\r\n\r\n    if (entity.showDelopy) {\r\n\r\n        if (entity.columnPosition == 'left')\r\n            return '14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n\r\n        if (entity.columnPosition == 'right')\r\n            return '14px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n    } else {\r\n\r\n        if (entity.columnPosition == 'left')\r\n            return '14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n\r\n        if (entity.columnPosition == 'right')\r\n            return '14px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n    }\r\n\r\n    return '16px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';\r\n};\r\n\r\n\r\n\r\nexport default CustomVehicle;\r\n"], "mappings": "yIAAA,MAAOA,MAAP,KAAkB,OAAlB,CACA,MAAOC,QAAP,KAAoB,oBAApB,CACA,OAASC,WAAT,CAAsBC,aAAtB,KAA2C,kBAA3C,CACA,MAAOC,WAAP,EAAqBC,cAArB,KAA2C,uBAA3C,CAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G,6IACA,GAAMC,cAAa,CAAG,QAAhBA,cAAgB,CAACC,KAAD,CAAW,CAE7B,GAAI,CAACJ,aAAa,CAACI,KAAD,CAAlB,CACI,OAEJ,GAAI,CAACA,KAAK,CAACC,KAAX,CACI,OAEJ,GAAMC,WAAU,CAAGF,KAAK,CAACG,aAAN,GAAwB,CAA3C,CACA,GAAMC,eAAc,CAAGJ,KAAK,CAACK,eAA7B,CACA,GAAMC,UAAS,CAAGC,YAAY,CAACP,KAAD,CAA9B,CAEA,mBACI,yBACKJ,aAAa,CAACI,KAAD,CAAb,eACG,YAAK,SAAS,gBAAUM,SAAV,CAAd,UACKN,KAAK,CAACC,KAAN,CAAYO,GAAZ,CAAgB,SAACC,IAAD,CAAOC,KAAP,CAAiB,CAC9B,mBACI,KAAC,OAAD,gCACQD,IADR,MAEI,UAAU,CAAEP,UAFhB,CAGI,cAAc,CAAEE,cAHpB,GADJ,CAOH,CARA,CADL,EAFR,EADJ,CAiBH,CA7BD,CA+BA;AACA;AACA;AACA;AACA,GACA,GAAMG,aAAY,CAAG,QAAfA,aAAe,CAACP,KAAD,CAAW,CAE5B,GAAI,EAAEA,KAAK,CAACW,iBAAN,GAA4B,CAA9B,CAAJ,CACI,MAAO,0FAAP,CAEJ,GAAIX,KAAK,CAACK,eAAN,EAAyB,MAA7B,CACI,MAAO,sFAAP,CAEJ,GAAIL,KAAK,CAACK,eAAN,EAAyB,OAA7B,CACI,MAAO,sFAAP,CAEJ,MAAO,yFAAP,CACH,CAZD,CAcA;AACA;AACA;AACA;AACA,GACA,GAAMO,QAAO,CAAG,QAAVA,QAAU,CAACC,MAAD,CAAY,CACxB,GAAIC,QAAJ,CACA;AACA,GAAIC,aAAY,CAAG,eAAnB,CACA,GAAIF,MAAM,CAACX,UAAX,CAAuB,CACnBY,OAAO,CAAG,kCAAV,CACH,CAFD,IAEO,CACHA,OAAO,CAAG,oCAAV,CACH,CACD,GAAME,aAAY,CAAGrB,WAAW,CAC5BkB,MAAM,CAACI,KADqB,WAEzBF,YAFyB,gCAAhC,CAKA,mBACI,2BACKF,MAAM,CAACI,KAAP,eACG,YAAM,SAAS,gBAAUH,OAAV,CAAf,uBACI,KAAC,OAAD,gCAAaE,YAAb,4BACI,sBAAOH,MAAM,CAACI,KAAP,CAAaC,YAApB,EADJ,GADJ,EAFR,CAQK,CAACL,MAAM,CAACI,KAAR,eACG,KAAC,gBAAD,kBAAuBJ,MAAvB,EATR,GADJ,CAcH,CA5BD,CA8BA;AACA;AACA;AACA;AACA,GACA,GAAMM,iBAAgB,CAAG,QAAnBA,iBAAmB,CAACN,MAAD,CAAY,CAEjC,GAAIO,mBAAJ,CACA,GAAIC,gBAAJ,CACA,GAAIC,iBAAJ,CACA,GAAIC,qBAAJ,CACA,GAAIC,wBAAJ,CAEA,GAAIX,MAAM,CAACX,UAAP,EAAqBW,MAAM,CAACY,UAAhC,CACIL,kBAAkB,CAAGP,MAAM,CAACY,UAA5B,CAEJ,GAAIZ,MAAM,CAACa,QAAX,CACIL,eAAe,CAAGR,MAAM,CAACa,QAAzB,CAEJ,GAAIb,MAAM,CAACc,SAAX,CACIL,gBAAgB,CAAGT,MAAM,CAACc,SAA1B,CAEJ,GAAId,MAAM,CAACe,aAAX,CACIL,oBAAoB,CAAGV,MAAM,CAACe,aAA9B,CAEJ,GAAIf,MAAM,CAACgB,iBAAX,CACIL,uBAAuB,CAAGX,MAAM,CAACgB,iBAAjC,CAEJ,GAAIC,OAAM,CAAGjB,MAAM,CAACkB,gBAAP,CAA0BlB,MAAM,CAACkB,gBAAP,CAAwBC,eAAlD,CAAoE,CAAjF,CAEA;AACA,GAAIC,QAAO,CAAG,CAACpB,MAAM,CAACa,QAAR,CAAkBb,MAAM,CAACc,SAAzB,CAAoCd,MAAM,CAACe,aAA3C,CAA0Df,MAAM,CAACgB,iBAAjE,CAAoFhB,MAAM,CAACY,UAA3F,EAAuGS,IAAvG,CAA4G,SAAAzB,IAAI,QAAIA,KAAJ,EAAhH,CAAd,CAEAW,kBAAkB,CAAGtB,cAAc,CAACsB,kBAAD,CAAqBa,OAArB,CAAnC,CACAZ,eAAe,CAAGvB,cAAc,CAACuB,eAAD,CAAkBY,OAAlB,CAAhC,CACAX,gBAAgB,CAAGxB,cAAc,CAACwB,gBAAD,CAAmBW,OAAnB,CAAjC,CACAV,oBAAoB,CAAGzB,cAAc,CAACyB,oBAAD,CAAuBU,OAAvB,CAArC,CACAT,uBAAuB,CAAG1B,cAAc,CAAC0B,uBAAD,CAA0BS,OAA1B,CAAxC,CAEA,GAAIE,mBAAkB,kBAAQf,kBAAR,CAAtB,CACA,GAAIgB,mBAAkB,kBAAQhB,kBAAR,CAAtB,CACA,GAAIiB,mBAAkB,kBAAQhB,eAAR,CAAtB,CACA,GAAIiB,mBAAkB,kBAAQhB,gBAAR,CAAtB,CACA,GAAIiB,mBAAkB,kBAAQhB,oBAAR,CAAtB,CACA,GAAIiB,mBAAkB,kBAAQhB,uBAAR,CAAtB,CAEAW,kBAAkB,CAACjB,YAAnB,CAAkC,GAAlC,CACAkB,kBAAkB,CAAClB,YAAnB,CAAkC,GAAlC,CACAmB,kBAAkB,CAACnB,YAAnB,CAAkC,GAAlC,CACAoB,kBAAkB,CAACpB,YAAnB,CAAkC,GAAlC,CACAqB,kBAAkB,CAACrB,YAAnB,CAAkC,GAAlC,CACAsB,kBAAkB,CAACtB,YAAnB,CAAkC,GAAlC,CAEA;AACA,GAAIY,MAAM,GAAK,CAAf,CAAkB,CACdK,kBAAkB,CAACM,gBAAnB,CAAsCC,SAAtC,CACAN,kBAAkB,CAACK,gBAAnB,CAAsCC,SAAtC,CACAL,kBAAkB,CAACI,gBAAnB,CAAsCC,SAAtC,CACAJ,kBAAkB,CAACG,gBAAnB,CAAsCC,SAAtC,CACAH,kBAAkB,CAACE,gBAAnB,CAAsCC,SAAtC,CACAF,kBAAkB,CAACC,gBAAnB,CAAsCC,SAAtC,CACH,CAED,GAAI5B,QAAJ,CACA;AACA,GAAI6B,UAAS,CAAG,EAAhB,CACA,GAAI9B,MAAM,CAACX,UAAX,CAAuB,CACnBY,OAAO,CAAG,kCAAV,CAEA,GAAID,MAAM,CAACT,cAAP,EAAyB,MAA7B,CAAqC,CACjCuC,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEV,kBAAZ,CAAgCW,SAAS,CAAE,YAA3C,CAAf,EACH,CAEDH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAEzB,kBADC,CAEX0B,SAAS,CAAE,wBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAExB,eADC,CAEXyB,SAAS,CAAE,wBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAER,kBAAZ,CAAgCS,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAEvB,gBADC,CAEXwB,SAAS,CAAE,wBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEP,kBAAZ,CAAgCQ,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAEtB,oBADC,CAEXuB,SAAS,CAAE,yBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEN,kBAAZ,CAAgCO,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAErB,uBADC,CAEXsB,SAAS,CAAE,yBAFA,CAAf,EAKA,GAAIjC,MAAM,CAACT,cAAP,EAAyB,OAA7B,CAAsC,CAClCuC,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEL,kBAAZ,CAAgCM,SAAS,CAAE,YAA3C,CAAf,EACH,CAEJ,CApCD,IAoCO,CACHhC,OAAO,CAAG,oCAAV,CAEA6B,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAET,kBAAZ,CAAgCU,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAExB,eADC,CAEXyB,SAAS,CAAE,wBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAER,kBAAZ,CAAgCS,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAEvB,gBADC,CAEXwB,SAAS,CAAE,wBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEP,kBAAZ,CAAgCQ,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAEtB,oBADC,CAEXuB,SAAS,CAAE,yBAFA,CAAf,EAIAH,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEN,kBAAZ,CAAgCO,SAAS,CAAE,YAA3C,CAAf,EACAH,SAAS,CAACC,IAAV,CAAe,CACXC,QAAQ,CAAErB,uBADC,CAEXsB,SAAS,CAAE,yBAFA,CAAf,EAIA,GAAIjC,MAAM,CAACT,cAAP,EAAyB,OAA7B,CAAsC,CAClCuC,SAAS,CAACC,IAAV,CAAe,CAAEC,QAAQ,CAAEL,kBAAZ,CAAgCM,SAAS,CAAE,YAA3C,CAAf,EACH,CAEJ,CAED,mBACI,2BAEKjC,MAAM,CAACX,UAAP,eACG,YAAK,SAAS,gBAAUY,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAE6B,SADX,CAEI,aAAa,CAAE9B,MAAM,CAACkB,gBAF1B,EADJ,EAHR,CAUK,CAAClB,MAAM,CAACX,UAAR,eACG,YAAK,SAAS,gBAAUY,OAAV,CAAd,uBACI,KAAC,UAAD,EACI,KAAK,CAAE6B,SADX,CAEI,aAAa,CAAE9B,MAAM,CAACkB,gBAF1B,EADJ,EAXR,GADJ,CAqBH,CAnJD,CAqJA;AACA;AACA;AACA;AACA,GACA,GAAMgB,gBAAe,CAAG,QAAlBA,gBAAkB,CAAClC,MAAD,CAAY,CAEhC,GAAIA,MAAM,CAACX,UAAX,CAAuB,CAEnB,GAAIW,MAAM,CAACT,cAAP,EAAyB,MAA7B,CACI,MAAO,gJAAP,CAEJ,GAAIS,MAAM,CAACT,cAAP,EAAyB,OAA7B,CACI,MAAO,gJAAP,CACP,CAPD,IAOO,CAEH,GAAIS,MAAM,CAACT,cAAP,EAAyB,MAA7B,CACI,MAAO,2HAAP,CAEJ,GAAIS,MAAM,CAACT,cAAP,EAAyB,OAA7B,CACI,MAAO,2HAAP,CACP,CAED,MAAO,gJAAP,CACH,CAnBD,CAuBA,cAAeL,cAAf"}, "metadata": {}, "sourceType": "module"}