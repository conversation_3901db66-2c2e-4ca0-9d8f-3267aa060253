{"ast": null, "code": "var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n      r,\n      ar = [],\n      e;\n\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n\n  return ar;\n};\n\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nimport { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdate from '../useUpdate';\n\nfunction useControllableValue(props, options) {\n  if (props === void 0) {\n    props = {};\n  }\n\n  if (options === void 0) {\n    options = {};\n  }\n\n  var defaultValue = options.defaultValue,\n      _a = options.defaultValuePropName,\n      defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,\n      _b = options.valuePropName,\n      valuePropName = _b === void 0 ? 'value' : _b,\n      _c = options.trigger,\n      trigger = _c === void 0 ? 'onChange' : _c;\n  var value = props[valuePropName];\n  var isControlled = props.hasOwnProperty(valuePropName);\n  var initialValue = useMemo(function () {\n    if (isControlled) {\n      return value;\n    }\n\n    if (props.hasOwnProperty(defaultValuePropName)) {\n      return props[defaultValuePropName];\n    }\n\n    return defaultValue;\n  }, []);\n  var stateRef = useRef(initialValue);\n\n  if (isControlled) {\n    stateRef.current = value;\n  }\n\n  var update = useUpdate();\n\n  function setState(v) {\n    var args = [];\n\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n\n    var r = isFunction(v) ? v(stateRef.current) : v;\n\n    if (!isControlled) {\n      stateRef.current = r;\n      update();\n    }\n\n    if (props[trigger]) {\n      props[trigger].apply(props, __spreadArray([r], __read(args), false));\n    }\n  }\n\n  return [stateRef.current, useMemoizedFn(setState)];\n}\n\nexport default useControllableValue;", "map": {"version": 3, "names": ["__read", "o", "n", "m", "Symbol", "iterator", "i", "call", "r", "ar", "e", "next", "done", "push", "value", "error", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "l", "Array", "prototype", "slice", "concat", "useMemo", "useRef", "isFunction", "useMemoizedFn", "useUpdate", "useControllableValue", "props", "options", "defaultValue", "_a", "defaultValuePropName", "_b", "valuePropName", "_c", "trigger", "isControlled", "hasOwnProperty", "initialValue", "stateRef", "current", "update", "setState", "v", "args", "_i", "apply"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useControllableValue/index.js"], "sourcesContent": ["var __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) {\n      ar.push(r.value);\n    }\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdate from '../useUpdate';\nfunction useControllableValue(props, options) {\n  if (props === void 0) {\n    props = {};\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var defaultValue = options.defaultValue,\n    _a = options.defaultValuePropName,\n    defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,\n    _b = options.valuePropName,\n    valuePropName = _b === void 0 ? 'value' : _b,\n    _c = options.trigger,\n    trigger = _c === void 0 ? 'onChange' : _c;\n  var value = props[valuePropName];\n  var isControlled = props.hasOwnProperty(valuePropName);\n  var initialValue = useMemo(function () {\n    if (isControlled) {\n      return value;\n    }\n    if (props.hasOwnProperty(defaultValuePropName)) {\n      return props[defaultValuePropName];\n    }\n    return defaultValue;\n  }, []);\n  var stateRef = useRef(initialValue);\n  if (isControlled) {\n    stateRef.current = value;\n  }\n  var update = useUpdate();\n  function setState(v) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    var r = isFunction(v) ? v(stateRef.current) : v;\n    if (!isControlled) {\n      stateRef.current = r;\n      update();\n    }\n    if (props[trigger]) {\n      props[trigger].apply(props, __spreadArray([r], __read(args), false));\n    }\n  }\n  return [stateRef.current, useMemoizedFn(setState)];\n}\nexport default useControllableValue;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,QAAQ,KAAKA,MAAb,IAAuB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCH,CAAC,CAACG,MAAM,CAACC,QAAR,CAAzC;EACA,IAAI,CAACF,CAAL,EAAQ,OAAOF,CAAP;EACR,IAAIK,CAAC,GAAGH,CAAC,CAACI,IAAF,CAAON,CAAP,CAAR;EAAA,IACEO,CADF;EAAA,IAEEC,EAAE,GAAG,EAFP;EAAA,IAGEC,CAHF;;EAIA,IAAI;IACF,OAAO,CAACR,CAAC,KAAK,KAAK,CAAX,IAAgBA,CAAC,KAAK,CAAvB,KAA6B,CAAC,CAACM,CAAC,GAAGF,CAAC,CAACK,IAAF,EAAL,EAAeC,IAApD,EAA0D;MACxDH,EAAE,CAACI,IAAH,CAAQL,CAAC,CAACM,KAAV;IACD;EACF,CAJD,CAIE,OAAOC,KAAP,EAAc;IACdL,CAAC,GAAG;MACFK,KAAK,EAAEA;IADL,CAAJ;EAGD,CARD,SAQU;IACR,IAAI;MACF,IAAIP,CAAC,IAAI,CAACA,CAAC,CAACI,IAAR,KAAiBT,CAAC,GAAGG,CAAC,CAAC,QAAD,CAAtB,CAAJ,EAAuCH,CAAC,CAACI,IAAF,CAAOD,CAAP;IACxC,CAFD,SAEU;MACR,IAAII,CAAJ,EAAO,MAAMA,CAAC,CAACK,KAAR;IACR;EACF;;EACD,OAAON,EAAP;AACD,CAvBD;;AAwBA,IAAIO,aAAa,GAAG,QAAQ,KAAKA,aAAb,IAA8B,UAAUC,EAAV,EAAcC,IAAd,EAAoBC,IAApB,EAA0B;EAC1E,IAAIA,IAAI,IAAIC,SAAS,CAACC,MAAV,KAAqB,CAAjC,EAAoC,KAAK,IAAIf,CAAC,GAAG,CAAR,EAAWgB,CAAC,GAAGJ,IAAI,CAACG,MAApB,EAA4BZ,EAAjC,EAAqCH,CAAC,GAAGgB,CAAzC,EAA4ChB,CAAC,EAA7C,EAAiD;IACnF,IAAIG,EAAE,IAAI,EAAEH,CAAC,IAAIY,IAAP,CAAV,EAAwB;MACtB,IAAI,CAACT,EAAL,EAASA,EAAE,GAAGc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,EAAiC,CAAjC,EAAoCZ,CAApC,CAAL;MACTG,EAAE,CAACH,CAAD,CAAF,GAAQY,IAAI,CAACZ,CAAD,CAAZ;IACD;EACF;EACD,OAAOW,EAAE,CAACS,MAAH,CAAUjB,EAAE,IAAIc,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBlB,IAAtB,CAA2BW,IAA3B,CAAhB,CAAP;AACD,CARD;;AASA,SAASS,OAAT,EAAkBC,MAAlB,QAAgC,OAAhC;AACA,SAASC,UAAT,QAA2B,UAA3B;AACA,OAAOC,aAAP,MAA0B,kBAA1B;AACA,OAAOC,SAAP,MAAsB,cAAtB;;AACA,SAASC,oBAAT,CAA8BC,KAA9B,EAAqCC,OAArC,EAA8C;EAC5C,IAAID,KAAK,KAAK,KAAK,CAAnB,EAAsB;IACpBA,KAAK,GAAG,EAAR;EACD;;EACD,IAAIC,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,YAAY,GAAGD,OAAO,CAACC,YAA3B;EAAA,IACEC,EAAE,GAAGF,OAAO,CAACG,oBADf;EAAA,IAEEA,oBAAoB,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,cAAhB,GAAiCA,EAF1D;EAAA,IAGEE,EAAE,GAAGJ,OAAO,CAACK,aAHf;EAAA,IAIEA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,OAAhB,GAA0BA,EAJ5C;EAAA,IAKEE,EAAE,GAAGN,OAAO,CAACO,OALf;EAAA,IAMEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,UAAhB,GAA6BA,EANzC;EAOA,IAAI1B,KAAK,GAAGmB,KAAK,CAACM,aAAD,CAAjB;EACA,IAAIG,YAAY,GAAGT,KAAK,CAACU,cAAN,CAAqBJ,aAArB,CAAnB;EACA,IAAIK,YAAY,GAAGjB,OAAO,CAAC,YAAY;IACrC,IAAIe,YAAJ,EAAkB;MAChB,OAAO5B,KAAP;IACD;;IACD,IAAImB,KAAK,CAACU,cAAN,CAAqBN,oBAArB,CAAJ,EAAgD;MAC9C,OAAOJ,KAAK,CAACI,oBAAD,CAAZ;IACD;;IACD,OAAOF,YAAP;EACD,CARyB,EAQvB,EARuB,CAA1B;EASA,IAAIU,QAAQ,GAAGjB,MAAM,CAACgB,YAAD,CAArB;;EACA,IAAIF,YAAJ,EAAkB;IAChBG,QAAQ,CAACC,OAAT,GAAmBhC,KAAnB;EACD;;EACD,IAAIiC,MAAM,GAAGhB,SAAS,EAAtB;;EACA,SAASiB,QAAT,CAAkBC,CAAlB,EAAqB;IACnB,IAAIC,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAG/B,SAAS,CAACC,MAAhC,EAAwC8B,EAAE,EAA1C,EAA8C;MAC5CD,IAAI,CAACC,EAAE,GAAG,CAAN,CAAJ,GAAe/B,SAAS,CAAC+B,EAAD,CAAxB;IACD;;IACD,IAAI3C,CAAC,GAAGqB,UAAU,CAACoB,CAAD,CAAV,GAAgBA,CAAC,CAACJ,QAAQ,CAACC,OAAV,CAAjB,GAAsCG,CAA9C;;IACA,IAAI,CAACP,YAAL,EAAmB;MACjBG,QAAQ,CAACC,OAAT,GAAmBtC,CAAnB;MACAuC,MAAM;IACP;;IACD,IAAId,KAAK,CAACQ,OAAD,CAAT,EAAoB;MAClBR,KAAK,CAACQ,OAAD,CAAL,CAAeW,KAAf,CAAqBnB,KAArB,EAA4BjB,aAAa,CAAC,CAACR,CAAD,CAAD,EAAMR,MAAM,CAACkD,IAAD,CAAZ,EAAoB,KAApB,CAAzC;IACD;EACF;;EACD,OAAO,CAACL,QAAQ,CAACC,OAAV,EAAmBhB,aAAa,CAACkB,QAAD,CAAhC,CAAP;AACD;;AACD,eAAehB,oBAAf"}, "metadata": {}, "sourceType": "module"}