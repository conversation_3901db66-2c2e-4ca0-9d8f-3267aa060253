{"ast": null, "code": "/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n\n  return result;\n}\n\nmodule.exports = arrayFilter;", "map": {"version": 3, "names": ["arrayFilter", "array", "predicate", "index", "length", "resIndex", "result", "value", "module", "exports"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/lodash/_arrayFilter.js"], "sourcesContent": ["/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = arrayFilter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAT,CAAqBC,KAArB,EAA4BC,SAA5B,EAAuC;EACrC,IAAIC,KAAK,GAAG,CAAC,CAAb;EAAA,IACIC,MAAM,GAAGH,KAAK,IAAI,IAAT,GAAgB,CAAhB,GAAoBA,KAAK,CAACG,MADvC;EAAA,IAEIC,QAAQ,GAAG,CAFf;EAAA,IAGIC,MAAM,GAAG,EAHb;;EAKA,OAAO,EAAEH,KAAF,GAAUC,MAAjB,EAAyB;IACvB,IAAIG,KAAK,GAAGN,KAAK,CAACE,KAAD,CAAjB;;IACA,IAAID,SAAS,CAACK,KAAD,EAAQJ,KAAR,EAAeF,KAAf,CAAb,EAAoC;MAClCK,MAAM,CAACD,QAAQ,EAAT,CAAN,GAAqBE,KAArB;IACD;EACF;;EACD,OAAOD,MAAP;AACD;;AAEDE,MAAM,CAACC,OAAP,GAAiBT,WAAjB"}, "metadata": {}, "sourceType": "script"}