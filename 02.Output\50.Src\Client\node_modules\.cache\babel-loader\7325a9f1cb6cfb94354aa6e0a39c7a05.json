{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "<PERSON><PERSON><PERSON><PERSON>", "useIsomorphicLayoutEffect"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,eAApB,QAA2C,OAA3C;AACA,OAAOC,SAAP,MAAsB,oBAAtB;AACA,IAAIC,yBAAyB,GAAGD,SAAS,GAAGD,eAAH,GAAqBD,SAA9D;AACA,eAAeG,yBAAf"}, "metadata": {}, "sourceType": "module"}