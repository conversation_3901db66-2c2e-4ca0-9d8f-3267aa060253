{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\n\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(options.target, window);\n\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n\n    var eventListener = function eventListener(event) {\n      return handlerRef.current(event);\n    };\n\n    targetElement.addEventListener(eventName, eventListener, {\n      capture: options.capture,\n      once: options.once,\n      passive: options.passive\n    });\n    return function () {\n      targetElement.removeEventListener(eventName, eventListener, {\n        capture: options.capture\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive], options.target);\n}\n\nexport default useEventListener;", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "useEffectWithTarget", "useEventListener", "eventName", "handler", "options", "handler<PERSON>ef", "targetElement", "target", "window", "addEventListener", "eventListener", "event", "current", "capture", "once", "passive", "removeEventListener"], "sources": ["D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/ahooks/es/useEventListener/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(options.target, window);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var eventListener = function eventListener(event) {\n      return handlerRef.current(event);\n    };\n    targetElement.addEventListener(eventName, eventListener, {\n      capture: options.capture,\n      once: options.once,\n      passive: options.passive\n    });\n    return function () {\n      targetElement.removeEventListener(eventName, eventListener, {\n        capture: options.capture\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive], options.target);\n}\nexport default useEventListener;"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAOC,mBAAP,MAAgC,8BAAhC;;AACA,SAASC,gBAAT,CAA0BC,SAA1B,EAAqCC,OAArC,EAA8CC,OAA9C,EAAuD;EACrD,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EACD,IAAIC,UAAU,GAAGP,SAAS,CAACK,OAAD,CAA1B;EACAH,mBAAmB,CAAC,YAAY;IAC9B,IAAIM,aAAa,GAAGP,gBAAgB,CAACK,OAAO,CAACG,MAAT,EAAiBC,MAAjB,CAApC;;IACA,IAAI,EAAEF,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACG,gBAA9E,CAAJ,EAAqG;MACnG;IACD;;IACD,IAAIC,aAAa,GAAG,SAASA,aAAT,CAAuBC,KAAvB,EAA8B;MAChD,OAAON,UAAU,CAACO,OAAX,CAAmBD,KAAnB,CAAP;IACD,CAFD;;IAGAL,aAAa,CAACG,gBAAd,CAA+BP,SAA/B,EAA0CQ,aAA1C,EAAyD;MACvDG,OAAO,EAAET,OAAO,CAACS,OADsC;MAEvDC,IAAI,EAAEV,OAAO,CAACU,IAFyC;MAGvDC,OAAO,EAAEX,OAAO,CAACW;IAHsC,CAAzD;IAKA,OAAO,YAAY;MACjBT,aAAa,CAACU,mBAAd,CAAkCd,SAAlC,EAA6CQ,aAA7C,EAA4D;QAC1DG,OAAO,EAAET,OAAO,CAACS;MADyC,CAA5D;IAGD,CAJD;EAKD,CAlBkB,EAkBhB,CAACX,SAAD,EAAYE,OAAO,CAACS,OAApB,EAA6BT,OAAO,CAACU,IAArC,EAA2CV,OAAO,CAACW,OAAnD,CAlBgB,EAkB6CX,OAAO,CAACG,MAlBrD,CAAnB;AAmBD;;AACD,eAAeN,gBAAf"}, "metadata": {}, "sourceType": "module"}