{"ast": null, "code": "import _slicedToArray from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport _classCallCheck from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/classCallCheck.js\";\nimport _createClass from \"D:/11.Working/20.AutoControl/02.Output/50.Src/Client/node_modules/@babel/runtime/helpers/esm/createClass.js\";\nimport { BYTE } from './byte';\nimport { FrameImpl } from './frame-impl';\nimport { Parser } from './parser';\nimport { StompSocketState } from './types';\nimport { Versions } from './versions';\nimport { augmentWebsocket } from './augment-websocket';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\n\nexport var StompHandler = /*#__PURE__*/function () {\n  function StompHandler(_client, _webSocket) {\n    var _this = this;\n\n    var config = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n    _classCallCheck(this, StompHandler);\n\n    this._client = _client;\n    this._webSocket = _webSocket;\n    this._serverFrameHandlers = {\n      // [CONNECTED Frame](http://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n      CONNECTED: function CONNECTED(frame) {\n        _this.debug(\"connected to server \".concat(frame.headers.server));\n\n        _this._connected = true;\n        _this._connectedVersion = frame.headers.version; // STOMP version 1.2 needs header values to be escaped\n\n        if (_this._connectedVersion === Versions.V1_2) {\n          _this._escapeHeaderValues = true;\n        }\n\n        _this._setupHeartbeat(frame.headers);\n\n        _this.onConnect(frame);\n      },\n      // [MESSAGE Frame](http://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n      MESSAGE: function MESSAGE(frame) {\n        // the callback is registered when the client calls\n        // `subscribe()`.\n        // If there is no registered subscription for the received message,\n        // the default `onUnhandledMessage` callback is used that the client can set.\n        // This is useful for subscriptions that are automatically created\n        // on the browser side (e.g. [RabbitMQ's temporary\n        // queues](http://www.rabbitmq.com/stomp.html)).\n        var subscription = frame.headers.subscription;\n        var onReceive = _this._subscriptions[subscription] || _this.onUnhandledMessage; // bless the frame to be a Message\n\n        var message = frame;\n        var client = _this;\n        var messageId = _this._connectedVersion === Versions.V1_2 ? message.headers.ack : message.headers['message-id']; // add `ack()` and `nack()` methods directly to the returned frame\n        // so that a simple call to `message.ack()` can acknowledge the message.\n\n        message.ack = function () {\n          var headers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n          return client.ack(messageId, subscription, headers);\n        };\n\n        message.nack = function () {\n          var headers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n          return client.nack(messageId, subscription, headers);\n        };\n\n        onReceive(message);\n      },\n      // [RECEIPT Frame](http://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n      RECEIPT: function RECEIPT(frame) {\n        var callback = _this._receiptWatchers[frame.headers['receipt-id']];\n\n        if (callback) {\n          callback(frame); // Server will acknowledge only once, remove the callback\n\n          delete _this._receiptWatchers[frame.headers['receipt-id']];\n        } else {\n          _this.onUnhandledReceipt(frame);\n        }\n      },\n      // [ERROR Frame](http://stomp.github.com/stomp-specification-1.2.html#ERROR)\n      ERROR: function ERROR(frame) {\n        _this.onStompError(frame);\n      }\n    }; // used to index subscribers\n\n    this._counter = 0; // subscription callbacks indexed by subscriber's ID\n\n    this._subscriptions = {}; // receipt-watchers indexed by receipts-ids\n\n    this._receiptWatchers = {};\n    this._partialData = '';\n    this._escapeHeaderValues = false;\n    this._lastServerActivityTS = Date.now();\n    this.configure(config);\n  }\n\n  _createClass(StompHandler, [{\n    key: \"connectedVersion\",\n    get: function get() {\n      return this._connectedVersion;\n    }\n  }, {\n    key: \"connected\",\n    get: function get() {\n      return this._connected;\n    }\n  }, {\n    key: \"configure\",\n    value: function configure(conf) {\n      // bulk assign all properties to this\n      Object.assign(this, conf);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n\n      var parser = new Parser( // On Frame\n      function (rawFrame) {\n        var frame = FrameImpl.fromRawFrame(rawFrame, _this2._escapeHeaderValues); // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n\n        if (!_this2.logRawCommunication) {\n          _this2.debug(\"<<< \".concat(frame));\n        }\n\n        var serverFrameHandler = _this2._serverFrameHandlers[frame.command] || _this2.onUnhandledFrame;\n        serverFrameHandler(frame);\n      }, // On Incoming Ping\n      function () {\n        _this2.debug('<<< PONG');\n      });\n\n      this._webSocket.onmessage = function (evt) {\n        _this2.debug('Received data');\n\n        _this2._lastServerActivityTS = Date.now();\n\n        if (_this2.logRawCommunication) {\n          var rawChunkAsString = evt.data instanceof ArrayBuffer ? new TextDecoder().decode(evt.data) : evt.data;\n\n          _this2.debug(\"<<< \".concat(rawChunkAsString));\n        }\n\n        parser.parseChunk(evt.data, _this2.appendMissingNULLonIncoming);\n      };\n\n      this._onclose = function (closeEvent) {\n        _this2.debug(\"Connection closed to \".concat(_this2._client.brokerURL));\n\n        _this2._cleanUp();\n\n        _this2.onWebSocketClose(closeEvent);\n      };\n\n      this._webSocket.onclose = this._onclose;\n\n      this._webSocket.onerror = function (errorEvent) {\n        _this2.onWebSocketError(errorEvent);\n      };\n\n      this._webSocket.onopen = function () {\n        // Clone before updating\n        var connectHeaders = Object.assign({}, _this2.connectHeaders);\n\n        _this2.debug('Web Socket Opened...');\n\n        connectHeaders['accept-version'] = _this2.stompVersions.supportedVersions();\n        connectHeaders['heart-beat'] = [_this2.heartbeatOutgoing, _this2.heartbeatIncoming].join(',');\n\n        _this2._transmit({\n          command: 'CONNECT',\n          headers: connectHeaders\n        });\n      };\n    }\n  }, {\n    key: \"_setupHeartbeat\",\n    value: function _setupHeartbeat(headers) {\n      var _this3 = this;\n\n      if (headers.version !== Versions.V1_1 && headers.version !== Versions.V1_2) {\n        return;\n      } // It is valid for the server to not send this header\n      // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n\n\n      if (!headers['heart-beat']) {\n        return;\n      } // heart-beat header received from the server looks like:\n      //\n      //     heart-beat: sx, sy\n\n\n      var _headers$heartBeat$s = headers['heart-beat'].split(',').map(function (v) {\n        return parseInt(v, 10);\n      }),\n          _headers$heartBeat$s2 = _slicedToArray(_headers$heartBeat$s, 2),\n          serverOutgoing = _headers$heartBeat$s2[0],\n          serverIncoming = _headers$heartBeat$s2[1];\n\n      if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n        var ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n        this.debug(\"send PING every \".concat(ttl, \"ms\"));\n        this._pinger = setInterval(function () {\n          if (_this3._webSocket.readyState === StompSocketState.OPEN) {\n            _this3._webSocket.send(BYTE.LF);\n\n            _this3.debug('>>> PING');\n          }\n        }, ttl);\n      }\n\n      if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n        var _ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n\n        this.debug(\"check PONG every \".concat(_ttl, \"ms\"));\n        this._ponger = setInterval(function () {\n          var delta = Date.now() - _this3._lastServerActivityTS; // We wait twice the TTL to be flexible on window's setInterval calls\n\n\n          if (delta > _ttl * 2) {\n            _this3.debug(\"did not receive server activity for the last \".concat(delta, \"ms\"));\n\n            _this3._closeOrDiscardWebsocket();\n          }\n        }, _ttl);\n      }\n    }\n  }, {\n    key: \"_closeOrDiscardWebsocket\",\n    value: function _closeOrDiscardWebsocket() {\n      if (this.discardWebsocketOnCommFailure) {\n        this.debug('Discarding websocket, the underlying socket may linger for a while');\n\n        this._discardWebsocket();\n      } else {\n        this.debug('Issuing close on the websocket');\n\n        this._closeWebsocket();\n      }\n    }\n  }, {\n    key: \"forceDisconnect\",\n    value: function forceDisconnect() {\n      if (this._webSocket) {\n        if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n          this._closeOrDiscardWebsocket();\n        }\n      }\n    }\n  }, {\n    key: \"_closeWebsocket\",\n    value: function _closeWebsocket() {\n      this._webSocket.onmessage = function () {}; // ignore messages\n\n\n      this._webSocket.close();\n    }\n  }, {\n    key: \"_discardWebsocket\",\n    value: function _discardWebsocket() {\n      var _this4 = this;\n\n      if (!this._webSocket.terminate) {\n        augmentWebsocket(this._webSocket, function (msg) {\n          return _this4.debug(msg);\n        });\n      }\n\n      this._webSocket.terminate();\n    }\n  }, {\n    key: \"_transmit\",\n    value: function _transmit(params) {\n      var command = params.command,\n          headers = params.headers,\n          body = params.body,\n          binaryBody = params.binaryBody,\n          skipContentLengthHeader = params.skipContentLengthHeader;\n      var frame = new FrameImpl({\n        command: command,\n        headers: headers,\n        body: body,\n        binaryBody: binaryBody,\n        escapeHeaderValues: this._escapeHeaderValues,\n        skipContentLengthHeader: skipContentLengthHeader\n      });\n      var rawChunk = frame.serialize();\n\n      if (this.logRawCommunication) {\n        this.debug(\">>> \".concat(rawChunk));\n      } else {\n        this.debug(\">>> \".concat(frame));\n      }\n\n      if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n        rawChunk = new TextEncoder().encode(rawChunk);\n      }\n\n      if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n        this._webSocket.send(rawChunk);\n      } else {\n        var out = rawChunk;\n\n        while (out.length > 0) {\n          var chunk = out.substring(0, this.maxWebSocketChunkSize);\n          out = out.substring(this.maxWebSocketChunkSize);\n\n          this._webSocket.send(chunk);\n\n          this.debug(\"chunk sent = \".concat(chunk.length, \", remaining = \").concat(out.length));\n        }\n      }\n    }\n  }, {\n    key: \"dispose\",\n    value: function dispose() {\n      var _this5 = this;\n\n      if (this.connected) {\n        try {\n          // clone before updating\n          var disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n\n          if (!disconnectHeaders.receipt) {\n            disconnectHeaders.receipt = \"close-\".concat(this._counter++);\n          }\n\n          this.watchForReceipt(disconnectHeaders.receipt, function (frame) {\n            _this5._closeWebsocket();\n\n            _this5._cleanUp();\n\n            _this5.onDisconnect(frame);\n          });\n\n          this._transmit({\n            command: 'DISCONNECT',\n            headers: disconnectHeaders\n          });\n        } catch (error) {\n          this.debug(\"Ignoring error during disconnect \".concat(error));\n        }\n      } else {\n        if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n          this._closeWebsocket();\n        }\n      }\n    }\n  }, {\n    key: \"_cleanUp\",\n    value: function _cleanUp() {\n      this._connected = false;\n\n      if (this._pinger) {\n        clearInterval(this._pinger);\n      }\n\n      if (this._ponger) {\n        clearInterval(this._ponger);\n      }\n    }\n  }, {\n    key: \"publish\",\n    value: function publish(params) {\n      var destination = params.destination,\n          headers = params.headers,\n          body = params.body,\n          binaryBody = params.binaryBody,\n          skipContentLengthHeader = params.skipContentLengthHeader;\n      var hdrs = Object.assign({\n        destination: destination\n      }, headers);\n\n      this._transmit({\n        command: 'SEND',\n        headers: hdrs,\n        body: body,\n        binaryBody: binaryBody,\n        skipContentLengthHeader: skipContentLengthHeader\n      });\n    }\n  }, {\n    key: \"watchForReceipt\",\n    value: function watchForReceipt(receiptId, callback) {\n      this._receiptWatchers[receiptId] = callback;\n    }\n  }, {\n    key: \"subscribe\",\n    value: function subscribe(destination, callback) {\n      var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      headers = Object.assign({}, headers);\n\n      if (!headers.id) {\n        headers.id = \"sub-\".concat(this._counter++);\n      }\n\n      headers.destination = destination;\n      this._subscriptions[headers.id] = callback;\n\n      this._transmit({\n        command: 'SUBSCRIBE',\n        headers: headers\n      });\n\n      var client = this;\n      return {\n        id: headers.id,\n        unsubscribe: function unsubscribe(hdrs) {\n          return client.unsubscribe(headers.id, hdrs);\n        }\n      };\n    }\n  }, {\n    key: \"unsubscribe\",\n    value: function unsubscribe(id) {\n      var headers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      headers = Object.assign({}, headers);\n      delete this._subscriptions[id];\n      headers.id = id;\n\n      this._transmit({\n        command: 'UNSUBSCRIBE',\n        headers: headers\n      });\n    }\n  }, {\n    key: \"begin\",\n    value: function begin(transactionId) {\n      var txId = transactionId || \"tx-\".concat(this._counter++);\n\n      this._transmit({\n        command: 'BEGIN',\n        headers: {\n          transaction: txId\n        }\n      });\n\n      var client = this;\n      return {\n        id: txId,\n        commit: function commit() {\n          client.commit(txId);\n        },\n        abort: function abort() {\n          client.abort(txId);\n        }\n      };\n    }\n  }, {\n    key: \"commit\",\n    value: function commit(transactionId) {\n      this._transmit({\n        command: 'COMMIT',\n        headers: {\n          transaction: transactionId\n        }\n      });\n    }\n  }, {\n    key: \"abort\",\n    value: function abort(transactionId) {\n      this._transmit({\n        command: 'ABORT',\n        headers: {\n          transaction: transactionId\n        }\n      });\n    }\n  }, {\n    key: \"ack\",\n    value: function ack(messageId, subscriptionId) {\n      var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      headers = Object.assign({}, headers);\n\n      if (this._connectedVersion === Versions.V1_2) {\n        headers.id = messageId;\n      } else {\n        headers['message-id'] = messageId;\n      }\n\n      headers.subscription = subscriptionId;\n\n      this._transmit({\n        command: 'ACK',\n        headers: headers\n      });\n    }\n  }, {\n    key: \"nack\",\n    value: function nack(messageId, subscriptionId) {\n      var headers = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      headers = Object.assign({}, headers);\n\n      if (this._connectedVersion === Versions.V1_2) {\n        headers.id = messageId;\n      } else {\n        headers['message-id'] = messageId;\n      }\n\n      headers.subscription = subscriptionId;\n      return this._transmit({\n        command: 'NACK',\n        headers: headers\n      });\n    }\n  }]);\n\n  return StompHandler;\n}();", "map": {"version": 3, "mappings": ";;;AAAA,SAASA,IAAT,QAAqB,QAArB;AAEA,SAASC,SAAT,QAA0B,cAA1B;AAGA,SAASC,MAAT,QAAuB,UAAvB;AAIA,SAQEC,gBARF,QAUO,SAVP;AAWA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,gBAAT,QAAiC,qBAAjC;AAEA;;;;;;;;AAOA,WAAaC,YAAb;EA+DE,sBACUC,OADV,EAESC,UAFT,EAG0B;IAAA;;IAAA,IAAxBC,MAAwB,uEAAF,EAAE;;IAAA;;IAFhB;IACD;IA2FQ,4BAEb;MACF;MACAC,SAAS,EAAE,wBAAK,EAAG;QACjB,KAAI,CAACC,KAAL,+BAAkCC,KAAK,CAACC,OAAN,CAAcC,MAAhD;;QACA,KAAI,CAACC,UAAL,GAAkB,IAAlB;QACA,KAAI,CAACC,iBAAL,GAAyBJ,KAAK,CAACC,OAAN,CAAcI,OAAvC,CAHiB,CAIjB;;QACA,IAAI,KAAI,CAACD,iBAAL,KAA2BZ,QAAQ,CAACc,IAAxC,EAA8C;UAC5C,KAAI,CAACC,mBAAL,GAA2B,IAA3B;QACD;;QAED,KAAI,CAACC,eAAL,CAAqBR,KAAK,CAACC,OAA3B;;QACA,KAAI,CAACQ,SAAL,CAAeT,KAAf;MACD,CAbC;MAeF;MACAU,OAAO,EAAE,sBAAK,EAAG;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAMC,YAAY,GAAGX,KAAK,CAACC,OAAN,CAAcU,YAAnC;QACA,IAAMC,SAAS,GACb,KAAI,CAACC,cAAL,CAAoBF,YAApB,KAAqC,KAAI,CAACG,kBAD5C,CATe,CAYf;;QACA,IAAMC,OAAO,GAAGf,KAAhB;QAEA,IAAMgB,MAAM,GAAG,KAAf;QACA,IAAMC,SAAS,GACb,KAAI,CAACb,iBAAL,KAA2BZ,QAAQ,CAACc,IAApC,GACIS,OAAO,CAACd,OAAR,CAAgBiB,GADpB,GAEIH,OAAO,CAACd,OAAR,CAAgB,YAAhB,CAHN,CAhBe,CAqBf;QACA;;QACAc,OAAO,CAACG,GAAR,GAAc,YAAqC;UAAA,IAApCjB,OAAoC,uEAAZ,EAAY;UACjD,OAAOe,MAAM,CAACE,GAAP,CAAWD,SAAX,EAAsBN,YAAtB,EAAoCV,OAApC,CAAP;QACD,CAFD;;QAGAc,OAAO,CAACI,IAAR,GAAe,YAAqC;UAAA,IAApClB,OAAoC,uEAAZ,EAAY;UAClD,OAAOe,MAAM,CAACG,IAAP,CAAYF,SAAZ,EAAuBN,YAAvB,EAAqCV,OAArC,CAAP;QACD,CAFD;;QAGAW,SAAS,CAACG,OAAD,CAAT;MACD,CA9CC;MAgDF;MACAK,OAAO,EAAE,sBAAK,EAAG;QACf,IAAMC,QAAQ,GAAG,KAAI,CAACC,gBAAL,CAAsBtB,KAAK,CAACC,OAAN,CAAc,YAAd,CAAtB,CAAjB;;QACA,IAAIoB,QAAJ,EAAc;UACZA,QAAQ,CAACrB,KAAD,CAAR,CADY,CAEZ;;UACA,OAAO,KAAI,CAACsB,gBAAL,CAAsBtB,KAAK,CAACC,OAAN,CAAc,YAAd,CAAtB,CAAP;QACD,CAJD,MAIO;UACL,KAAI,CAACsB,kBAAL,CAAwBvB,KAAxB;QACD;MACF,CA1DC;MA4DF;MACAwB,KAAK,EAAE,oBAAK,EAAG;QACb,KAAI,CAACC,YAAL,CAAkBzB,KAAlB;MACD;IA/DC,CAFa,CA1FS,CAExB;;IACA,KAAK0B,QAAL,GAAgB,CAAhB,CAHwB,CAKxB;;IACA,KAAKb,cAAL,GAAsB,EAAtB,CANwB,CAQxB;;IACA,KAAKS,gBAAL,GAAwB,EAAxB;IAEA,KAAKK,YAAL,GAAoB,EAApB;IAEA,KAAKpB,mBAAL,GAA2B,KAA3B;IAEA,KAAKqB,qBAAL,GAA6BC,IAAI,CAACC,GAAL,EAA7B;IAEA,KAAKC,SAAL,CAAelC,MAAf;EACD;;EApFH;IAAA;IAAA,KAyCE,eAAoB;MAClB,OAAO,KAAKO,iBAAZ;IACD;EA3CH;IAAA;IAAA,KA8CE,eAAa;MACX,OAAO,KAAKD,UAAZ;IACD;EAhDH;IAAA;IAAA,OAsFS,mBAAU6B,IAAV,EAA2B;MAChC;MACCC,MAAc,CAACC,MAAf,CAAsB,IAAtB,EAA4BF,IAA5B;IACF;EAzFH;IAAA;IAAA,OA2FS,iBAAK;MAAA;;MACV,IAAMG,MAAM,GAAG,IAAI7C,MAAJ,EACb;MACA,kBAAQ,EAAG;QACT,IAAMU,KAAK,GAAGX,SAAS,CAAC+C,YAAV,CACZC,QADY,EAEZ,MAAI,CAAC9B,mBAFO,CAAd,CADS,CAMT;;QACA,IAAI,CAAC,MAAI,CAAC+B,mBAAV,EAA+B;UAC7B,MAAI,CAACvC,KAAL,eAAkBC,KAAlB;QACD;;QAED,IAAMuC,kBAAkB,GACtB,MAAI,CAACC,oBAAL,CAA0BxC,KAAK,CAACyC,OAAhC,KAA4C,MAAI,CAACC,gBADnD;QAEAH,kBAAkB,CAACvC,KAAD,CAAlB;MACD,CAhBY,EAiBb;MACA,YAAK;QACH,MAAI,CAACD,KAAL,CAAW,UAAX;MACD,CApBY,CAAf;;MAuBA,KAAKH,UAAL,CAAgB+C,SAAhB,GAA4B,UAACC,GAAD,EAAkC;QAC5D,MAAI,CAAC7C,KAAL,CAAW,eAAX;;QACA,MAAI,CAAC6B,qBAAL,GAA6BC,IAAI,CAACC,GAAL,EAA7B;;QAEA,IAAI,MAAI,CAACQ,mBAAT,EAA8B;UAC5B,IAAMO,gBAAgB,GACpBD,GAAG,CAACE,IAAJ,YAAoBC,WAApB,GACI,IAAIC,WAAJ,GAAkBC,MAAlB,CAAyBL,GAAG,CAACE,IAA7B,CADJ,GAEIF,GAAG,CAACE,IAHV;;UAIA,MAAI,CAAC/C,KAAL,eAAkB8C,gBAAlB;QACD;;QAEDV,MAAM,CAACe,UAAP,CAAkBN,GAAG,CAACE,IAAtB,EAA4B,MAAI,CAACK,2BAAjC;MACD,CAbD;;MAeA,KAAKC,QAAL,GAAgB,UAACC,UAAD,EAAqB;QACnC,MAAI,CAACtD,KAAL,gCAAmC,MAAI,CAACJ,OAAL,CAAa2D,SAAhD;;QACA,MAAI,CAACC,QAAL;;QACA,MAAI,CAACC,gBAAL,CAAsBH,UAAtB;MACD,CAJD;;MAMA,KAAKzD,UAAL,CAAgB6D,OAAhB,GAA0B,KAAKL,QAA/B;;MAEA,KAAKxD,UAAL,CAAgB8D,OAAhB,GAA0B,UAACC,UAAD,EAAqB;QAC7C,MAAI,CAACC,gBAAL,CAAsBD,UAAtB;MACD,CAFD;;MAIA,KAAK/D,UAAL,CAAgBiE,MAAhB,GAAyB,YAAK;QAC5B;QACA,IAAMC,cAAc,GAAI7B,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0B,MAAI,CAAC4B,cAA/B,CAAxB;;QAEA,MAAI,CAAC/D,KAAL,CAAW,sBAAX;;QACA+D,cAAc,CAAC,gBAAD,CAAd,GAAmC,MAAI,CAACC,aAAL,CAAmBC,iBAAnB,EAAnC;QACAF,cAAc,CAAC,YAAD,CAAd,GAA+B,CAC7B,MAAI,CAACG,iBADwB,EAE7B,MAAI,CAACC,iBAFwB,EAG7BC,IAH6B,CAGxB,GAHwB,CAA/B;;QAIA,MAAI,CAACC,SAAL,CAAe;UAAE3B,OAAO,EAAE,SAAX;UAAsBxC,OAAO,EAAE6D;QAA/B,CAAf;MACD,CAXD;IAYD;EA1JH;IAAA;IAAA,OAgOU,yBAAgB7D,OAAhB,EAAqC;MAAA;;MAC3C,IACEA,OAAO,CAACI,OAAR,KAAoBb,QAAQ,CAAC6E,IAA7B,IACApE,OAAO,CAACI,OAAR,KAAoBb,QAAQ,CAACc,IAF/B,EAGE;QACA;MACD,CAN0C,CAQ3C;MACA;;;MACA,IAAI,CAACL,OAAO,CAAC,YAAD,CAAZ,EAA4B;QAC1B;MACD,CAZ0C,CAc3C;MACA;MACA;;;MACA,2BAAyCA,OAAO,CAAC,YAAD,CAAP,CACtCqE,KADsC,CAChC,GADgC,EAEtCC,GAFsC,CAElC,UAACC,CAAD;QAAA,OAAeC,QAAQ,CAACD,CAAD,EAAI,EAAJ,CAAvB;MAAA,CAFkC,CAAzC;MAAA;MAAA,IAAOE,cAAP;MAAA,IAAuBC,cAAvB;;MAIA,IAAI,KAAKV,iBAAL,KAA2B,CAA3B,IAAgCU,cAAc,KAAK,CAAvD,EAA0D;QACxD,IAAMC,GAAG,GAAWC,IAAI,CAACC,GAAL,CAAS,KAAKb,iBAAd,EAAiCU,cAAjC,CAApB;QACA,KAAK5E,KAAL,2BAA8B6E,GAA9B;QACA,KAAKG,OAAL,GAAeC,WAAW,CAAC,YAAK;UAC9B,IAAI,MAAI,CAACpF,UAAL,CAAgBqF,UAAhB,KAA+B1F,gBAAgB,CAAC2F,IAApD,EAA0D;YACxD,MAAI,CAACtF,UAAL,CAAgBuF,IAAhB,CAAqB/F,IAAI,CAACgG,EAA1B;;YACA,MAAI,CAACrF,KAAL,CAAW,UAAX;UACD;QACF,CALyB,EAKvB6E,GALuB,CAA1B;MAMD;;MAED,IAAI,KAAKV,iBAAL,KAA2B,CAA3B,IAAgCQ,cAAc,KAAK,CAAvD,EAA0D;QACxD,IAAME,IAAG,GAAWC,IAAI,CAACC,GAAL,CAAS,KAAKZ,iBAAd,EAAiCQ,cAAjC,CAApB;;QACA,KAAK3E,KAAL,4BAA+B6E,IAA/B;QACA,KAAKS,OAAL,GAAeL,WAAW,CAAC,YAAK;UAC9B,IAAMM,KAAK,GAAGzD,IAAI,CAACC,GAAL,KAAa,MAAI,CAACF,qBAAhC,CAD8B,CAE9B;;;UACA,IAAI0D,KAAK,GAAGV,IAAG,GAAG,CAAlB,EAAqB;YACnB,MAAI,CAAC7E,KAAL,wDAA2DuF,KAA3D;;YACA,MAAI,CAACC,wBAAL;UACD;QACF,CAPyB,EAOvBX,IAPuB,CAA1B;MAQD;IACF;EA5QH;IAAA;IAAA,OA8QU,oCAAwB;MAC9B,IAAI,KAAKY,6BAAT,EAAwC;QACtC,KAAKzF,KAAL,CACE,oEADF;;QAGA,KAAK0F,iBAAL;MACD,CALD,MAKO;QACL,KAAK1F,KAAL,CAAW,gCAAX;;QACA,KAAK2F,eAAL;MACD;IACF;EAxRH;IAAA;IAAA,OA0RS,2BAAe;MACpB,IAAI,KAAK9F,UAAT,EAAqB;QACnB,IACE,KAAKA,UAAL,CAAgBqF,UAAhB,KAA+B1F,gBAAgB,CAACoG,UAAhD,IACA,KAAK/F,UAAL,CAAgBqF,UAAhB,KAA+B1F,gBAAgB,CAAC2F,IAFlD,EAGE;UACA,KAAKK,wBAAL;QACD;MACF;IACF;EAnSH;IAAA;IAAA,OAqSS,2BAAe;MACpB,KAAK3F,UAAL,CAAgB+C,SAAhB,GAA4B,YAAK,CAAG,CAApC,CADoB,CACkB;;;MACtC,KAAK/C,UAAL,CAAgBgG,KAAhB;IACD;EAxSH;IAAA;IAAA,OA0SU,6BAAiB;MAAA;;MACvB,IAAI,CAAC,KAAKhG,UAAL,CAAgBiG,SAArB,EAAgC;QAC9BpG,gBAAgB,CAAC,KAAKG,UAAN,EAAkB,UAACkG,GAAD;UAAA,OAAiB,MAAI,CAAC/F,KAAL,CAAW+F,GAAX,CAAjB;QAAA,CAAlB,CAAhB;MACD;;MAED,KAAKlG,UAAL,CAAgBiG,SAAhB;IACD;EAhTH;IAAA;IAAA,OAkTU,mBAAUE,MAAV,EAMP;MACC,IAAQtD,OAAR,GACEsD,MADF,CAAQtD,OAAR;MAAA,IAAiBxC,OAAjB,GACE8F,MADF,CAAiB9F,OAAjB;MAAA,IAA0B+F,IAA1B,GACED,MADF,CAA0BC,IAA1B;MAAA,IAAgCC,UAAhC,GACEF,MADF,CAAgCE,UAAhC;MAAA,IAA4CC,uBAA5C,GACEH,MADF,CAA4CG,uBAA5C;MAEA,IAAMlG,KAAK,GAAG,IAAIX,SAAJ,CAAc;QAC1BoD,OAAO,EAAPA,OAD0B;QAE1BxC,OAAO,EAAPA,OAF0B;QAG1B+F,IAAI,EAAJA,IAH0B;QAI1BC,UAAU,EAAVA,UAJ0B;QAK1BE,kBAAkB,EAAE,KAAK5F,mBALC;QAM1B2F,uBAAuB,EAAvBA;MAN0B,CAAd,CAAd;MASA,IAAIE,QAAQ,GAAGpG,KAAK,CAACqG,SAAN,EAAf;;MAEA,IAAI,KAAK/D,mBAAT,EAA8B;QAC5B,KAAKvC,KAAL,eAAkBqG,QAAlB;MACD,CAFD,MAEO;QACL,KAAKrG,KAAL,eAAkBC,KAAlB;MACD;;MAED,IAAI,KAAKsG,mBAAL,IAA4B,OAAOF,QAAP,KAAoB,QAApD,EAA8D;QAC5DA,QAAQ,GAAG,IAAIG,WAAJ,GAAkBC,MAAlB,CAAyBJ,QAAzB,CAAX;MACD;;MAED,IAAI,OAAOA,QAAP,KAAoB,QAApB,IAAgC,CAAC,KAAKK,gBAA1C,EAA4D;QAC1D,KAAK7G,UAAL,CAAgBuF,IAAhB,CAAqBiB,QAArB;MACD,CAFD,MAEO;QACL,IAAIM,GAAG,GAAGN,QAAV;;QACA,OAAOM,GAAG,CAACC,MAAJ,GAAa,CAApB,EAAuB;UACrB,IAAMC,KAAK,GAAGF,GAAG,CAACG,SAAJ,CAAc,CAAd,EAAiB,KAAKC,qBAAtB,CAAd;UACAJ,GAAG,GAAGA,GAAG,CAACG,SAAJ,CAAc,KAAKC,qBAAnB,CAAN;;UACA,KAAKlH,UAAL,CAAgBuF,IAAhB,CAAqByB,KAArB;;UACA,KAAK7G,KAAL,wBAA2B6G,KAAK,CAACD,MAAjC,2BAAwDD,GAAG,CAACC,MAA5D;QACD;MACF;IACF;EA3VH;IAAA;IAAA,OA6VS,mBAAO;MAAA;;MACZ,IAAI,KAAKI,SAAT,EAAoB;QAClB,IAAI;UACF;UACA,IAAMC,iBAAiB,GAAI/E,MAAc,CAACC,MAAf,CACzB,EADyB,EAEzB,KAAK8E,iBAFoB,CAA3B;;UAKA,IAAI,CAACA,iBAAiB,CAACC,OAAvB,EAAgC;YAC9BD,iBAAiB,CAACC,OAAlB,mBAAqC,KAAKvF,QAAL,EAArC;UACD;;UACD,KAAKwF,eAAL,CAAqBF,iBAAiB,CAACC,OAAvC,EAAgD,eAAK,EAAG;YACtD,MAAI,CAACvB,eAAL;;YACA,MAAI,CAACnC,QAAL;;YACA,MAAI,CAAC4D,YAAL,CAAkBnH,KAAlB;UACD,CAJD;;UAKA,KAAKoE,SAAL,CAAe;YAAE3B,OAAO,EAAE,YAAX;YAAyBxC,OAAO,EAAE+G;UAAlC,CAAf;QACD,CAhBD,CAgBE,OAAOI,KAAP,EAAc;UACd,KAAKrH,KAAL,4CAA+CqH,KAA/C;QACD;MACF,CApBD,MAoBO;QACL,IACE,KAAKxH,UAAL,CAAgBqF,UAAhB,KAA+B1F,gBAAgB,CAACoG,UAAhD,IACA,KAAK/F,UAAL,CAAgBqF,UAAhB,KAA+B1F,gBAAgB,CAAC2F,IAFlD,EAGE;UACA,KAAKQ,eAAL;QACD;MACF;IACF;EA1XH;IAAA;IAAA,OA4XU,oBAAQ;MACd,KAAKvF,UAAL,GAAkB,KAAlB;;MAEA,IAAI,KAAK4E,OAAT,EAAkB;QAChBsC,aAAa,CAAC,KAAKtC,OAAN,CAAb;MACD;;MACD,IAAI,KAAKM,OAAT,EAAkB;QAChBgC,aAAa,CAAC,KAAKhC,OAAN,CAAb;MACD;IACF;EArYH;IAAA;IAAA,OAuYS,iBAAQU,MAAR,EAA8B;MACnC,IAAQuB,WAAR,GACEvB,MADF,CAAQuB,WAAR;MAAA,IAAqBrH,OAArB,GACE8F,MADF,CAAqB9F,OAArB;MAAA,IAA8B+F,IAA9B,GACED,MADF,CAA8BC,IAA9B;MAAA,IAAoCC,UAApC,GACEF,MADF,CAAoCE,UAApC;MAAA,IAAgDC,uBAAhD,GACEH,MADF,CAAgDG,uBAAhD;MAEA,IAAMqB,IAAI,GAAkBtF,MAAc,CAACC,MAAf,CAAsB;QAAEoF,WAAW,EAAXA;MAAF,CAAtB,EAAuCrH,OAAvC,CAA5B;;MACA,KAAKmE,SAAL,CAAe;QACb3B,OAAO,EAAE,MADI;QAEbxC,OAAO,EAAEsH,IAFI;QAGbvB,IAAI,EAAJA,IAHa;QAIbC,UAAU,EAAVA,UAJa;QAKbC,uBAAuB,EAAvBA;MALa,CAAf;IAOD;EAlZH;IAAA;IAAA,OAoZS,yBAAgBsB,SAAhB,EAAmCnG,QAAnC,EAA8D;MACnE,KAAKC,gBAAL,CAAsBkG,SAAtB,IAAmCnG,QAAnC;IACD;EAtZH;IAAA;IAAA,OAwZS,mBACLiG,WADK,EAELjG,QAFK,EAGqB;MAAA,IAA1BpB,OAA0B,uEAAF,EAAE;MAE1BA,OAAO,GAAIgC,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BjC,OAA1B,CAAX;;MAEA,IAAI,CAACA,OAAO,CAACwH,EAAb,EAAiB;QACfxH,OAAO,CAACwH,EAAR,iBAAoB,KAAK/F,QAAL,EAApB;MACD;;MACDzB,OAAO,CAACqH,WAAR,GAAsBA,WAAtB;MACA,KAAKzG,cAAL,CAAoBZ,OAAO,CAACwH,EAA5B,IAAkCpG,QAAlC;;MACA,KAAK+C,SAAL,CAAe;QAAE3B,OAAO,EAAE,WAAX;QAAwBxC,OAAO,EAAPA;MAAxB,CAAf;;MACA,IAAMe,MAAM,GAAG,IAAf;MACA,OAAO;QACLyG,EAAE,EAAExH,OAAO,CAACwH,EADP;QAGLC,WAHK,uBAGOH,IAHP,EAGW;UACd,OAAOvG,MAAM,CAAC0G,WAAP,CAAmBzH,OAAO,CAACwH,EAA3B,EAA+BF,IAA/B,CAAP;QACD;MALI,CAAP;IAOD;EA7aH;IAAA;IAAA,OA+aS,qBAAYE,EAAZ,EAAkD;MAAA,IAA1BxH,OAA0B,uEAAF,EAAE;MACvDA,OAAO,GAAIgC,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BjC,OAA1B,CAAX;MAEA,OAAO,KAAKY,cAAL,CAAoB4G,EAApB,CAAP;MACAxH,OAAO,CAACwH,EAAR,GAAaA,EAAb;;MACA,KAAKrD,SAAL,CAAe;QAAE3B,OAAO,EAAE,aAAX;QAA0BxC,OAAO,EAAPA;MAA1B,CAAf;IACD;EArbH;IAAA;IAAA,OAubS,eAAM0H,aAAN,EAA2B;MAChC,IAAMC,IAAI,GAAGD,aAAa,iBAAU,KAAKjG,QAAL,EAAV,CAA1B;;MACA,KAAK0C,SAAL,CAAe;QACb3B,OAAO,EAAE,OADI;QAEbxC,OAAO,EAAE;UACP4H,WAAW,EAAED;QADN;MAFI,CAAf;;MAMA,IAAM5G,MAAM,GAAG,IAAf;MACA,OAAO;QACLyG,EAAE,EAAEG,IADC;QAELE,MAFK,oBAEC;UACJ9G,MAAM,CAAC8G,MAAP,CAAcF,IAAd;QACD,CAJI;QAKLG,KALK,mBAKA;UACH/G,MAAM,CAAC+G,KAAP,CAAaH,IAAb;QACD;MAPI,CAAP;IASD;EAzcH;IAAA;IAAA,OA2cS,gBAAOD,aAAP,EAA4B;MACjC,KAAKvD,SAAL,CAAe;QACb3B,OAAO,EAAE,QADI;QAEbxC,OAAO,EAAE;UACP4H,WAAW,EAAEF;QADN;MAFI,CAAf;IAMD;EAldH;IAAA;IAAA,OAodS,eAAMA,aAAN,EAA2B;MAChC,KAAKvD,SAAL,CAAe;QACb3B,OAAO,EAAE,OADI;QAEbxC,OAAO,EAAE;UACP4H,WAAW,EAAEF;QADN;MAFI,CAAf;IAMD;EA3dH;IAAA;IAAA,OA6dS,aACL1G,SADK,EAEL+G,cAFK,EAGqB;MAAA,IAA1B/H,OAA0B,uEAAF,EAAE;MAE1BA,OAAO,GAAIgC,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BjC,OAA1B,CAAX;;MAEA,IAAI,KAAKG,iBAAL,KAA2BZ,QAAQ,CAACc,IAAxC,EAA8C;QAC5CL,OAAO,CAACwH,EAAR,GAAaxG,SAAb;MACD,CAFD,MAEO;QACLhB,OAAO,CAAC,YAAD,CAAP,GAAwBgB,SAAxB;MACD;;MACDhB,OAAO,CAACU,YAAR,GAAuBqH,cAAvB;;MACA,KAAK5D,SAAL,CAAe;QAAE3B,OAAO,EAAE,KAAX;QAAkBxC,OAAO,EAAPA;MAAlB,CAAf;IACD;EA3eH;IAAA;IAAA,OA6eS,cACLgB,SADK,EAEL+G,cAFK,EAGqB;MAAA,IAA1B/H,OAA0B,uEAAF,EAAE;MAE1BA,OAAO,GAAIgC,MAAc,CAACC,MAAf,CAAsB,EAAtB,EAA0BjC,OAA1B,CAAX;;MAEA,IAAI,KAAKG,iBAAL,KAA2BZ,QAAQ,CAACc,IAAxC,EAA8C;QAC5CL,OAAO,CAACwH,EAAR,GAAaxG,SAAb;MACD,CAFD,MAEO;QACLhB,OAAO,CAAC,YAAD,CAAP,GAAwBgB,SAAxB;MACD;;MACDhB,OAAO,CAACU,YAAR,GAAuBqH,cAAvB;MACA,OAAO,KAAK5D,SAAL,CAAe;QAAE3B,OAAO,EAAE,MAAX;QAAmBxC,OAAO,EAAPA;MAAnB,CAAf,CAAP;IACD;EA3fH;;EAAA;AAAA", "names": ["BYTE", "FrameImpl", "<PERSON><PERSON><PERSON>", "StompSocketState", "Versions", "augmentWebsocket", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_client", "_webSocket", "config", "CONNECTED", "debug", "frame", "headers", "server", "_connected", "_connectedVersion", "version", "V1_2", "_escapeHeaderV<PERSON>ues", "_setupHeartbeat", "onConnect", "MESSAGE", "subscription", "onReceive", "_subscriptions", "onUnhandledMessage", "message", "client", "messageId", "ack", "nack", "RECEIPT", "callback", "_receiptWatchers", "onUnhandledReceipt", "ERROR", "onStompError", "_counter", "_partialData", "_lastServerActivityTS", "Date", "now", "configure", "conf", "Object", "assign", "parser", "fromRawFrame", "rawFrame", "logRawCommunication", "serverFrameHandler", "_serverFrameHandlers", "command", "onUnhandledFrame", "onmessage", "evt", "rawChunkAsString", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "decode", "parseChunk", "appendMissingNULLonIncoming", "_onclose", "closeEvent", "brokerURL", "_cleanUp", "onWebSocketClose", "onclose", "onerror", "errorEvent", "onWebSocketError", "onopen", "connectHeaders", "stompV<PERSON><PERSON>", "supportedVersions", "heartbeatOutgoing", "heartbeatIncoming", "join", "_transmit", "V1_1", "split", "map", "v", "parseInt", "serverOutgoing", "serverIncoming", "ttl", "Math", "max", "_pinger", "setInterval", "readyState", "OPEN", "send", "LF", "_ponger", "delta", "_closeOrDiscardWebsocket", "discardWebsocketOnCommFailure", "_discardWebsocket", "_closeWebsocket", "CONNECTING", "close", "terminate", "msg", "params", "body", "binaryBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapeHeader<PERSON><PERSON>ues", "rawChunk", "serialize", "forceBinaryWSFrames", "TextEncoder", "encode", "splitLargeFrames", "out", "length", "chunk", "substring", "maxWebSocketChunkSize", "connected", "disconnectHeaders", "receipt", "watchForReceipt", "onDisconnect", "error", "clearInterval", "destination", "hdrs", "receiptId", "id", "unsubscribe", "transactionId", "txId", "transaction", "commit", "abort", "subscriptionId"], "sources": ["D:\\11.Working\\20.AutoControl\\02.Output\\50.Src\\Client\\node_modules\\@stomp\\stompjs\\src\\stomp-handler.ts"], "sourcesContent": ["import { BYTE } from './byte';\nimport { Client } from './client';\nimport { FrameImpl } from './frame-impl';\nimport { IMessage } from './i-message';\nimport { ITransaction } from './i-transaction';\nimport { Parser } from './parser';\nimport { StompConfig } from './stomp-config';\nimport { StompHeaders } from './stomp-headers';\nimport { StompSubscription } from './stomp-subscription';\nimport {\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  IStompSocketMessageEvent,\n  messageCallbackType,\n  StompSocketState,\n  wsErrorCallbackType,\n} from './types';\nimport { Versions } from './versions';\nimport { augmentWebsocket } from './augment-websocket';\n\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  public debug: debugFnType;\n\n  public stompVersions: Versions;\n\n  public connectHeaders: StompHeaders;\n\n  public disconnectHeaders: StompHeaders;\n\n  public heartbeatIncoming: number;\n\n  public heartbeatOutgoing: number;\n\n  public onUnhandledMessage: messageCallbackType;\n\n  public onUnhandledReceipt: frameCallbackType;\n\n  public onUnhandledFrame: frameCallbackType;\n\n  public onConnect: frameCallbackType;\n\n  public onDisconnect: frameCallbackType;\n\n  public onStompError: frameCallbackType;\n\n  public onWebSocketClose: closeEventCallbackType;\n\n  public onWebSocketError: wsErrorCallbackType;\n\n  public logRawCommunication: boolean;\n\n  public splitLargeFrames: boolean;\n\n  public maxWebSocketChunkSize: number;\n\n  public forceBinaryWSFrames: boolean;\n\n  public appendMissingNULLonIncoming: boolean;\n\n  public discardWebsocketOnCommFailure: boolean;\n\n  get connectedVersion(): string {\n    return this._connectedVersion;\n  }\n  private _connectedVersion: string;\n\n  get connected(): boolean {\n    return this._connected;\n  }\n\n  private _connected: boolean;\n\n  private readonly _subscriptions: { [key: string]: messageCallbackType };\n  private readonly _receiptWatchers: { [key: string]: frameCallbackType };\n  private _partialData: string;\n  private _escapeHeaderValues: boolean;\n  private _counter: number;\n  private _pinger: any;\n  private _ponger: any;\n  private _lastServerActivityTS: number;\n\n  private _onclose: (closeEvent: any) => void;\n\n  constructor(\n    private _client: Client,\n    public _webSocket: IStompSocket,\n    config: StompConfig = {}\n  ) {\n    // used to index subscribers\n    this._counter = 0;\n\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n\n    this._partialData = '';\n\n    this._escapeHeaderValues = false;\n\n    this._lastServerActivityTS = Date.now();\n\n    this.configure(config);\n  }\n\n  public configure(conf: StompConfig): void {\n    // bulk assign all properties to this\n    (Object as any).assign(this, conf);\n  }\n\n  public start(): void {\n    const parser = new Parser(\n      // On Frame\n      rawFrame => {\n        const frame = FrameImpl.fromRawFrame(\n          rawFrame,\n          this._escapeHeaderValues\n        );\n\n        // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n        if (!this.logRawCommunication) {\n          this.debug(`<<< ${frame}`);\n        }\n\n        const serverFrameHandler =\n          this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n        serverFrameHandler(frame);\n      },\n      // On Incoming Ping\n      () => {\n        this.debug('<<< PONG');\n      }\n    );\n\n    this._webSocket.onmessage = (evt: IStompSocketMessageEvent) => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n\n      if (this.logRawCommunication) {\n        const rawChunkAsString =\n          evt.data instanceof ArrayBuffer\n            ? new TextDecoder().decode(evt.data)\n            : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n\n      parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n    };\n\n    this._onclose = (closeEvent): void => {\n      this.debug(`Connection closed to ${this._client.brokerURL}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n\n    this._webSocket.onclose = this._onclose;\n\n    this._webSocket.onerror = (errorEvent): void => {\n      this.onWebSocketError(errorEvent);\n    };\n\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = (Object as any).assign({}, this.connectHeaders);\n\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [\n        this.heartbeatOutgoing,\n        this.heartbeatIncoming,\n      ].join(',');\n      this._transmit({ command: 'CONNECT', headers: connectHeaders });\n    };\n  }\n\n  private readonly _serverFrameHandlers: {\n    [key: string]: frameCallbackType;\n  } = {\n    // [CONNECTED Frame](http://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n    CONNECTED: frame => {\n      this.debug(`connected to server ${frame.headers.server}`);\n      this._connected = true;\n      this._connectedVersion = frame.headers.version;\n      // STOMP version 1.2 needs header values to be escaped\n      if (this._connectedVersion === Versions.V1_2) {\n        this._escapeHeaderValues = true;\n      }\n\n      this._setupHeartbeat(frame.headers);\n      this.onConnect(frame);\n    },\n\n    // [MESSAGE Frame](http://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n    MESSAGE: frame => {\n      // the callback is registered when the client calls\n      // `subscribe()`.\n      // If there is no registered subscription for the received message,\n      // the default `onUnhandledMessage` callback is used that the client can set.\n      // This is useful for subscriptions that are automatically created\n      // on the browser side (e.g. [RabbitMQ's temporary\n      // queues](http://www.rabbitmq.com/stomp.html)).\n      const subscription = frame.headers.subscription;\n      const onReceive =\n        this._subscriptions[subscription] || this.onUnhandledMessage;\n\n      // bless the frame to be a Message\n      const message = frame as IMessage;\n\n      const client = this;\n      const messageId =\n        this._connectedVersion === Versions.V1_2\n          ? message.headers.ack\n          : message.headers['message-id'];\n\n      // add `ack()` and `nack()` methods directly to the returned frame\n      // so that a simple call to `message.ack()` can acknowledge the message.\n      message.ack = (headers: StompHeaders = {}): void => {\n        return client.ack(messageId, subscription, headers);\n      };\n      message.nack = (headers: StompHeaders = {}): void => {\n        return client.nack(messageId, subscription, headers);\n      };\n      onReceive(message);\n    },\n\n    // [RECEIPT Frame](http://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n    RECEIPT: frame => {\n      const callback = this._receiptWatchers[frame.headers['receipt-id']];\n      if (callback) {\n        callback(frame);\n        // Server will acknowledge only once, remove the callback\n        delete this._receiptWatchers[frame.headers['receipt-id']];\n      } else {\n        this.onUnhandledReceipt(frame);\n      }\n    },\n\n    // [ERROR Frame](http://stomp.github.com/stomp-specification-1.2.html#ERROR)\n    ERROR: frame => {\n      this.onStompError(frame);\n    },\n  };\n\n  private _setupHeartbeat(headers: StompHeaders): void {\n    if (\n      headers.version !== Versions.V1_1 &&\n      headers.version !== Versions.V1_2\n    ) {\n      return;\n    }\n\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat']\n      .split(',')\n      .map((v: string) => parseInt(v, 10));\n\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl: number = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n      this._pinger = setInterval(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      }, ttl);\n    }\n\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl: number = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n\n  private _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug(\n        'Discarding websocket, the underlying socket may linger for a while'\n      );\n      this._discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n\n  public forceDisconnect() {\n    if (this._webSocket) {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n\n  public _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n\n  private _discardWebsocket() {\n    if (!this._webSocket.terminate) {\n      augmentWebsocket(this._webSocket, (msg: string) => this.debug(msg));\n    }\n\n    this._webSocket.terminate();\n  }\n\n  private _transmit(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    skipContentLengthHeader?: boolean;\n  }): void {\n    const { command, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader,\n    });\n\n    let rawChunk = frame.serialize();\n\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk as string;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n\n  public dispose(): void {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = (Object as any).assign(\n          {},\n          this.disconnectHeaders\n        );\n\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeWebsocket();\n      }\n    }\n  }\n\n  private _cleanUp() {\n    this._connected = false;\n\n    if (this._pinger) {\n      clearInterval(this._pinger);\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n    }\n  }\n\n  public publish(params: IPublishParams): void {\n    const { destination, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const hdrs: StompHeaders = (Object as any).assign({ destination }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader,\n    });\n  }\n\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._receiptWatchers[receiptId] = callback;\n  }\n\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {}\n  ): StompSubscription {\n    headers = (Object as any).assign({}, headers);\n\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({ command: 'SUBSCRIBE', headers });\n    const client = this;\n    return {\n      id: headers.id,\n\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      },\n    };\n  }\n\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    headers = (Object as any).assign({}, headers);\n\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({ command: 'UNSUBSCRIBE', headers });\n  }\n\n  public begin(transactionId: string): ITransaction {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId,\n      },\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit(): void {\n        client.commit(txId);\n      },\n      abort(): void {\n        client.abort(txId);\n      },\n    };\n  }\n\n  public commit(transactionId: string): void {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public abort(transactionId: string): void {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({ command: 'ACK', headers });\n  }\n\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({ command: 'NACK', headers });\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}